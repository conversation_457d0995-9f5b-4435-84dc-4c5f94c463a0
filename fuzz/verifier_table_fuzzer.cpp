#include <cstddef>
#include <cstdint>
#include <fstream>
#include <filesystem>
#include "core/security/hotpatch_verifier_table.hh"

// Very dumb harness: write arbitrary bytes into a temp file and attempt to
// load it via HotpatchVerifierTable::load().  Any crashes / UBSan findings are
// bugs.

extern "C" int LLVMFuzzerTestOneInput(const uint8_t* data, size_t size) {
#if !KAI_ENABLE_VPATCH
    // Nothing to fuzz when the feature is off.
    return 0;
#else
    using namespace launcher::core::security;
    // Write to /tmp rather than /dev/shm to stay within CI sandbox.
    const std::filesystem::path tmp_path = std::filesystem::temp_directory_path() / "fuzz_vpatch_snapshot.kfsn";

    {
        std::ofstream ofs(tmp_path, std::ios::binary | std::ios::trunc);
        ofs.write(reinterpret_cast<const char*>(data), static_cast<std::streamsize>(size));
    }

    auto res = HotpatchVerifierTable::load(tmp_path);
    (void)res; // We're only interested in crashes / sanitiser issues.

    std::filesystem::remove(tmp_path);
    return 0;
#endif
} 