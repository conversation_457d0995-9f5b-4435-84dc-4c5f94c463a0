// SPDX-License-Identifier: MIT
//
// adaptive_cache_fuzzer.cpp – libFuzzer harness stressing AdaptiveCache hot path.
// It interprets the input corpus as a stream of 9-byte commands:
//   [op:1 byte] [key:8 bytes]
//      op & 0x3 == 0 → lookup (getOrInsert)
//      op & 0x3 == 1 → mutate (getOrInsert then XOR value)
//      op & 0x3 == 2 → erase(key)
// Any remaining tail <9 bytes is ignored.
//
// The cache instance is kept static so mutations across iterations accumulate
// and exercise shard growth heuristics.  Thread-safety is provided internally
// by AdaptiveCache; no extra concurrency is introduced here to keep the
// harness lightweight and fast (≥5 k exec/s).

#include "core/container/adaptive_cache.hh"
#include <cstdint>
#include <cstddef>
#include <cstring>

using CacheT = launcher::core::container::AdaptiveCache<uint64_t, uint64_t>;

static CacheT g_cache{1024}; // 1024 sets → 16 KiB per shard max

extern "C" int LLVMFuzzerTestOneInput(const uint8_t *data, size_t size) {
    constexpr std::size_t kCmdSize = 9; // 1-byte op + 8-byte key
    std::size_t offset = 0;
    while (offset + kCmdSize <= size) {
        uint8_t op = data[offset];
        uint64_t key;
        std::memcpy(&key, data + offset + 1, sizeof(uint64_t));
        offset += kCmdSize;

        switch (op & 0x3) {
            case 0: {
                (void)g_cache.getOrInsert(key, [] { return 0ULL; });
                break;
            }
            case 1: {
                auto &val = g_cache.getOrInsert(key, [] { return 0ULL; });
                val ^= key; // mutate value to keep things interesting
                break;
            }
            case 2: {
                g_cache.erase(key);
                break;
            }
            default:
                break;
        }
    }
    return 0; // fuzzers must always return 0
} 