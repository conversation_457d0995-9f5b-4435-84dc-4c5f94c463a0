// SPDX-License-Identifier: MIT
// manifest_json_fuzzer.cpp – exercises capability_schema::extractCapabilities
// with arbitrary JSON inputs. Ensures malformed data is rejected gracefully
// without out-of-bounds reads or crashes.

#include <cstdint>
#include <cstddef>
#include <string_view>

#include <nlohmann/json.hpp>

#include "tools/kai-capability-gen/capability_schema.hh"

extern "C" int LLVMFuzzerTestOneInput(const uint8_t* data, size_t size) {
    using namespace kai::capability_schema;

    if (size == 0 || size > 1024 * 1024) { // 1 MiB guard for CI throughput
        return 0;
    }

    // Wrap data in string_view – no copy.
    std::string_view sv(reinterpret_cast<const char*>(data), size);

    try {
        // Let nlohmann::json validate UTF-8 & structure – exceptions expected.
        auto j = nlohmann::json::parse(sv, /*callback*/ nullptr, /*exceptions*/ true);
        (void)extractCapabilities(j);
    } catch (const std::exception&) {
        // All parse / validation failures are fine – only UB is a bug.
    }

    return 0;
} 