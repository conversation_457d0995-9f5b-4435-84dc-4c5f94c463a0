# Exit early when neither ASAN nor TSAN is enabled – libFuzzer main would be missing.
if(NOT (ENABLE_TSAN OR ENABLE_ASAN))
    message(STATUS "Skipping ring_queue_fuzzer target (requires ENABLE_TSAN or ENABLE_ASAN)")
    return()
endif()

add_executable(ring_queue_fuzzer ring_queue_fuzzer.cpp)

# Enable libFuzzer + sanitiser flags.  We piggy-back on the top-level ENABLE_*
# toggles so the same build matrix covers both plain and TSAN variants.
if(ENABLE_TSAN)
    set(FUZZ_SAN_FLAGS "-fsanitize=fuzzer,thread")
elseif(ENABLE_ASAN)
    set(FUZZ_SAN_FLAGS "-fsanitize=fuzzer,address")
else()
    set(FUZZ_SAN_FLAGS "")
endif()

if(FUZZ_SAN_FLAGS)
    target_compile_options(ring_queue_fuzzer PRIVATE ${FUZZ_SAN_FLAGS} -fno-omit-frame-pointer)
    target_link_options(   ring_queue_fuzzer PRIVATE ${FUZZ_SAN_FLAGS})
endif()

# Access headers in src/ hierarchy without awkward relative paths.
target_include_directories(ring_queue_fuzzer PRIVATE ${PROJECT_SOURCE_DIR}/src)

target_link_libraries(ring_queue_fuzzer PRIVATE pthread) 
# Create a small helper interface with the common sanitiser + libFuzzer
# flags so we do not repeat the same boiler-plate for every fuzzer target.

add_library(kai_fuzzer_common INTERFACE)
# Pull in core for headers / logging.  Linking to the static core lib also
# ensures the fuzzer sees the same code paths as the production binary.

target_link_libraries(kai_fuzzer_common INTERFACE core)

# Clang/LLVM style flags – on non-Clang compilers these options may be
# unsupported, but Kai CI uses Apple Clang so we keep the flags simple.
# LibFuzzer requires `-fsanitize=fuzzer` which implicitly links the runtime.
set(_FUZZ_SAN_FLAGS
    -fsanitize=fuzzer,address
    -fno-omit-frame-pointer)

target_compile_options(kai_fuzzer_common INTERFACE ${_FUZZ_SAN_FLAGS})
target_link_options   (kai_fuzzer_common INTERFACE ${_FUZZ_SAN_FLAGS})

# ---------------------------------------------------------------------------
# Helper macro – two lines per new fuzzer target instead of boiler-plate block
# ---------------------------------------------------------------------------
function(add_kai_fuzzer target src)
    add_executable(${target} ${src})
    target_link_libraries(${target} PRIVATE kai_fuzzer_common)
    # Ensure headers under src/ are reachable (e.g., core/storage/flat_snapshot.hh,
    # tools/kai-capability-gen/capability_schema.hh).
    target_include_directories(${target} PRIVATE ${PROJECT_SOURCE_DIR}/src)
    # Propagate include directories from core; INTERFACE on common already does.
endfunction()

# ---------------------------------------------------------------------------
# Individual fuzzers
# ---------------------------------------------------------------------------
add_kai_fuzzer(flat_snapshot_fuzzer flat_snapshot_fuzzer.cpp)
add_kai_fuzzer(verifier_table_fuzzer verifier_table_fuzzer.cpp)
add_kai_fuzzer(manifest_json_fuzzer manifest_json_fuzzer.cpp)
add_kai_fuzzer(adaptive_cache_fuzzer adaptive_cache_fuzzer.cpp)

# Link header-only nlohmann_json include dirs so the fuzzer sees json.hpp.
if(TARGET nlohmann_json::nlohmann_json)
    target_link_libraries(manifest_json_fuzzer PRIVATE nlohmann_json::nlohmann_json)
endif()

# ---------------------------------------------------------------------------
# Meta-target : all_fuzz
# Building this target compiles every individual libFuzzer harness so
# developers can simply run `ninja all_fuzz` (or `make all_fuzz`).  This is
# defined only when sanitiser flags are active (same early-exit guard).
# ---------------------------------------------------------------------------

add_custom_target(all_fuzz DEPENDS
    ring_queue_fuzzer
    flat_snapshot_fuzzer
    verifier_table_fuzzer
    manifest_json_fuzzer
    adaptive_cache_fuzzer) 
