#include <cstdint>
#include <cstddef>
#include <fstream>
#include <filesystem>

#include "core/storage/flat_snapshot.hh"

// Harness that writes arbitrary bytes to a temporary file and exercises the
// FlatSnapshot mmap + verify + payload access pipeline.  The temp file lives
// inside the system temp directory so the CI sandbox has permissions, and is
// deleted on every iteration.  The size guard keeps disk I/O bounded to
// maintain ≥5 k exec/s throughput.

extern "C" int LLVMFuzzerTestOneInput(const uint8_t* data, size_t size) {
    using namespace launcher::core::storage;

    // Abort early for empty or excessively large inputs (1 MiB cap protects
    // CI runner disks and keeps the fuzzer fast).
    if (size == 0 || size > 1024 * 1024) {
        return 0;
    }

    const std::filesystem::path tmp_path =
        std::filesystem::temp_directory_path() / "fuzz_flat_snapshot.kfsn";

    {
        std::ofstream ofs(tmp_path, std::ios::binary | std::ios::trunc);
        if (!ofs) {
            return 0; // I/O error – skip.
        }
        ofs.write(reinterpret_cast<const char*>(data),
                  static_cast<std::streamsize>(size));
    }

    auto snap_exp = FlatSnapshot::mmapReadOnly(tmp_path);
    if (snap_exp) {
        // Even if verify() fails we still call it – bugs can surface in both
        // success and failure paths.
        (void)snap_exp.value().verify();

        // Touch first byte of payload to ensure the span is valid and mapped.
        auto payload_span = snap_exp.value().payload();
        if (!payload_span.empty()) {
            volatile std::byte b = payload_span.front();
            (void)b;
        }
    }

    std::filesystem::remove(tmp_path);
    return 0;
} 