// SPDX-License-Identifier: MIT
//
// ring_queue_fuzzer.cpp – libFuzzer harness that exercises RingQueueBackend
// with multiple producer threads and a single consumer thread while Thread
// Sanitizer is active.  Inspired by the existing hazard-pointer stress test
// but optimised for ≥5 k exec/s by keeping worker threads persistent across
// iterations.
//
// Build flags are supplied by fuzz/CMakeLists.txt (‐fsanitize=fuzzer,…).

#include <atomic>
#include <array>
#include <thread>
#include <vector>
#include <cassert>
#include <cstddef>
#include <cstdint>
#include <cstring>
#include "core/runtime/ring_queue_backend.hh"

using launcher::core::runtime::RingQueueBackend;

// ------------------------------------------------------------
// Global configuration
// ------------------------------------------------------------
static constexpr std::size_t kCap        = 128;   // small – wrap quickly
static constexpr int          kProducers = 4;     // fixed producer count
static constexpr std::size_t kMaxSeen    = 65'536; // ABA duplicate guard

// ------------------------------------------------------------
// Shared state between the harness and worker threads
// ------------------------------------------------------------
struct IterContext {
    const uint8_t* data{nullptr};
    std::size_t    size{0};
    std::size_t    expected{0}; // total pushes for this iteration
};

static RingQueueBackend<int, kCap> g_queue;
static std::array<std::atomic<uint8_t>, kMaxSeen> g_seen{}; // zero-init
static std::atomic<uint64_t> g_epoch{0};                    // iteration token
static std::atomic<int>      g_remaining_producers{0};
static std::atomic<std::size_t> g_consumed{0};
static IterContext            g_ctx;
static std::atomic<bool>      g_stop{false};

// ------------------------------------------------------------
// Producer worker – one per thread, lives forever.
// ------------------------------------------------------------
static void producerWorker(int id) {
    uint64_t local_epoch = 0;
    while (!g_stop.load(std::memory_order_acquire)) {
        // Wait for next iteration.
        uint64_t cur = g_epoch.load(std::memory_order_acquire);
        if (cur == local_epoch) {
            std::this_thread::yield();
            continue;
        }
        local_epoch = cur;

        // Snapshot iteration context.
        const auto  ctx      = g_ctx; // copy – safe: ctx fields updated before epoch bump
        const auto  per_step = kProducers;

        // Each producer pushes unique integer IDs in round-robin fashion.
        for (std::size_t i = static_cast<std::size_t>(id); i < ctx.expected; i += per_step) {
            // Back-pressure: spin until slot available.
            int value = static_cast<int>(i);
            while (!g_queue.try_push(value) && !g_stop.load(std::memory_order_relaxed)) {
                std::this_thread::yield();
            }
        }

        // Signal completion.
        g_remaining_producers.fetch_sub(1, std::memory_order_acq_rel);
    }
}

// ------------------------------------------------------------
// Consumer worker – single thread, lives forever.
// ------------------------------------------------------------
static void consumerWorker() {
    uint64_t local_epoch = 0;
    while (!g_stop.load(std::memory_order_acquire)) {
        // Drain queue if not in the middle of an iteration.
        int val;
        if (g_queue.try_pop(val)) {
            // Access seen vector only within bounds guarded by producer logic.
            if (static_cast<std::size_t>(val) < kMaxSeen) {
                assert(g_seen[static_cast<std::size_t>(val)].exchange(1, std::memory_order_relaxed) == 0);
            }
            g_consumed.fetch_add(1, std::memory_order_relaxed);
            continue;
        }

        uint64_t cur = g_epoch.load(std::memory_order_acquire);
        if (cur != local_epoch) {
            // New iteration started – reset local state.
            local_epoch = cur;
        } else {
            std::this_thread::yield();
        }
    }
}

// ------------------------------------------------------------
// Thread initialisation – done once.
// ------------------------------------------------------------
static void startWorkers() {
    static bool started = false;
    if (started) return;
    started = true;

    for (int i = 0; i < kProducers; ++i) {
        std::thread(producerWorker, i).detach();
    }
    std::thread(consumerWorker).detach();
}

// ------------------------------------------------------------
// Harness entry – called by libFuzzer for every test case.
// ------------------------------------------------------------
extern "C" int LLVMFuzzerTestOneInput(const uint8_t* data, size_t size) {
    // Initialise workers lazily to avoid TLS in child processes.
    startWorkers();

    if (size == 0) return 0;

    // Determine how many pushes we will perform this round – cap to kMaxSeen.
    const std::size_t expected = (size < kMaxSeen) ? size : kMaxSeen;
    if (expected == 0) return 0;

    // Prepare iteration context.
    g_ctx.data     = data;
    g_ctx.size     = size;
    g_ctx.expected = expected;

    // Reset bookkeeping.
    for (std::size_t i = 0; i < expected; ++i) {
        g_seen[i].store(0, std::memory_order_relaxed);
    }
    g_consumed.store(0, std::memory_order_release);
    g_remaining_producers.store(kProducers, std::memory_order_release);

    // Advance epoch => wake workers.
    g_epoch.fetch_add(1, std::memory_order_acq_rel);

    // Wait until producers done and consumer drained everything.
    while (g_remaining_producers.load(std::memory_order_acquire) != 0) {
        std::this_thread::yield();
    }
    // Allow consumer to finish popping.
    while (g_consumed.load(std::memory_order_acquire) < expected) {
        std::this_thread::yield();
    }

    // Final sanity: queue must be empty.
    int dummy;
    assert(!g_queue.try_pop(dummy));

    return 0;
}

// ------------------------------------------------------------
// Cleanup – never called, but present for completeness.
// ------------------------------------------------------------
extern "C" int LLVMFuzzerInitialize(int*, char***) {
    return 0;
} 