# MicroLauncher Directory Structure

| Path                       | Purpose                                               |
| -------------------------- | ----------------------------------------------------- |
| `src/core/`                | Core logic: search, persistence, index, agent, config |
| `src/ui/macos/`            | AppKit UI glue written in Objective-C++               |
| `src/ranking/`             | Bandit + heuristic re-ranking models                  |
| `src/tests/`               | GoogleTest unit & integration tests                   |
| `config/`                  | YAML / JSON runtime configuration, action manifests   |
| `resources/`               | Icons, prompts, embeddings, static data               |
| `docs/`                    | Design docs, reports, security profiles               |
| `docs/security/`           | Seatbelt profiles, entitlements templates             |
| `scripts/`                 | Build / CI helper shells (`build.sh`, `run.sh`, etc.) |
| `build/`, `build-release/` | Generated CMake build trees (ignored)                 |
| `lib/`                     | Vendored third-party libraries                        |

Update this file whenever new top-level modules are introduced.
