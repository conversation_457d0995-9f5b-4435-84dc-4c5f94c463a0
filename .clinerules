# Cline Rules for MicroLauncher Project

This file captures important patterns, preferences, and project intelligence to help <PERSON><PERSON> work more effectively with the MicroLauncher project.

## Project Structure

-   The project follows a modular architecture with clear component boundaries
-   Core functionality is in `src/core/`
-   UI components are in `src/ui/`
-   Platform-specific implementations are in `src/platform/`
-   Tests are in `src/tests/`
-   Configuration files are in `config/`
-   Build scripts and tools are in `scripts/`

## Coding Patterns

-   The project uses C++23 features extensively
-   CamelCase is used for class names
-   snake_case is used for variables and functions
-   4-space indentation is standard (no tabs)
-   Maximum line length is 100 characters
-   Doxygen-style comments are used for public API documentation
-   Platform-specific code is isolated through abstraction layers
-   Factory pattern is used for creating platform-specific implementations
-   Command pattern is used for implementing actions
-   Observer pattern is used for event handling

## Build System

-   CMake is used as the build system
-   Multiple build configurations are supported:
    -   Debug: `build/`
    -   Release: `build-release/`
-   The project uses GitHub Actions for CI/CD
-   Code quality is enforced through automated checks
-   Git hooks are available for automatic code formatting

## Project Priorities

-   Performance and minimal resource usage are top priorities
-   Cross-platform compatibility (macOS and Windows) is essential
-   Code quality and maintainability are highly valued
-   User experience and responsiveness are critical

## Current Focus

-   Implementing the Actions system (both static and dynamic)
-   Developing the Agent UI for AI interaction
-   Maintaining the core performance characteristics while adding AI capabilities

## Memory Bank Usage

-   The memory bank should be updated when:
    -   Discovering new project patterns
    -   After implementing significant changes
    -   When context needs clarification
-   Focus particularly on updating `activeContext.md` and `progress.md` as they track current state

## File Locations

-   Default workspace: `~/Documents/MicroLauncher/Workspace`
-   Configuration files: `config/`
-   Action definitions:
    -   Static actions (Tools): `config/actions/tools/`
    -   Dynamic actions (Requests): `config/actions/requests/`

## Terminology

-   **LauncherBar**: The main search/command input interface (renamed from "search bar")
-   **Actions**: Functionality that can be triggered via the LauncherBar
    -   **Static Actions (Tools)**: Predefined, directly executable actions
    -   **Dynamic Actions (Requests)**: Actions processed by AI models
-   **Agent**: Encapsulation of AI models, instructions, requests, context, and tools
    -   **General Agent**: Default agent used when no specific agent is selected
-   **Mission**: A task or conversation assigned to one or more agents
-   **Workspace**: A directory containing files and resources for agent missions
-   **Context**: Information provided to actions or agents to improve relevance
    -   Can be manual (user input) or automatic (system context)

## Implementation Notes

-   The project aims to minimize external dependencies
-   Native UI frameworks are preferred over cross-platform alternatives
-   AI model integration is designed to be modular and extensible
-   Context awareness is a key differentiator for the launcher
-   The application uses a multi-threaded architecture

## User Preferences

-   As this is the initial setup of the memory bank, specific user preferences have not yet been documented
-   This section will be updated as we learn more about user preferences through development and feedback

## Known Challenges

-   Balancing AI capabilities with performance goals
-   Managing different AI model capabilities and limitations
-   Ensuring consistent behavior across platforms
-   Optimizing resource usage while adding features

## Evolution Path

-   The project is evolving from a basic application launcher to a comprehensive productivity tool with AI capabilities
-   Future versions may include more advanced agent collaboration features
-   Additional AI models and providers will be supported over time
