# Deep Mode – Hybrid Architecture (Option B + C)

Goal  
Blend the **Modular Plugin Architecture** (Option B) with the **Header-only / `constexpr` / module-based performance focus** (Option C) so that:

• We keep _runtime_ extensibility via drop-in `.dylib` plugins.  
• We obtain _compile-time_ speed and _runtime_ performance inside each component by embracing header-only algorithms, C++23 modules, arena allocators, and reduced virtual dispatch.

---

1. Conceptual Model

---

```
             +-----------------------+
             |  kai-core  (kernel)   |  <-- very small, stable ABI
             +-----------+-----------+
                         ^  C-API façade
          dlopen()       |
+---------+--------------+--------------+---------+
|   scanner_macos.dylib  |   openai.dylib     | ... other plugins
|   (plugin boundary)    |   (plugin boundary)    |
|   +-----------------+  |   +-----------------+  |
|   | header-only     |  |   | header-only     |  |  <-- Option C goodness
|   | algorithms,     |  |   | algorithms,     |  |      (modules, CRTP,…)
|   | PMR pools, etc. |  |   | PMR pools, etc. |  |
+---+-----------------+--+---+-----------------+--+
```

1. `kai-core` exposes a **pure C interface** (`service_registry.h` + opaque handles).
2. Each plugin is a **single dynamic library**.  
   • Its boundary layer is thin C wrappers respecting the ABI.  
   • Internally it is free to use any compile-time tricks (`export module scanner.macos;`).
3. The host loads plugins, they register factories into a **ServiceRegistry** (lightweight DI container).
4. Core & plugins share _header-only_ common facilities (`fuzzy_match.h`, `expected.h`, `arena.h`) distributed as C++23 modules → compile fast & inline aggressively.

---

2. Technical Design Details

---

A. Stable ABI Layer  
• All types crossing the plugin boundary are **POD / C structs** or opaque `void*`.  
• Version struct (`KaiAbiVersion{ major, minor, patch }`).  
• One entry point per plugin:

```cpp
extern "C" KaiPluginInfo kaiGetPluginInfo();
extern "C" void kaiRegisterPlugin(KaiServiceRegistry* registry);
```

B. Header-only / Module Strategy  
• Files in `core/include/` become `export module kai.<topic>;`.  
 – Example: `export module kai.fuzzy;` holding constexpr fuzzy-match.  
• Heavier algorithms (ranking, linear-UCB) can adopt **CRTP** or `std::variant` to remove vtables.  
• `expected.h`, `result.h`, `debug.h`, `arena.h` moved under `kai.util` module for single compile pass.

C. Memory Management  
• Provide `core/util/arena_allocator.h` (PMR based).  
• `SearchResult`, `AppEntry` vectors switch to `std::pmr::vector` with arena scoped in `Executor` task.  
• Plugins can opt-in; allocator passed via `KaiTaskContext`.

D. Concurrency  
• Introduce `core/async/executor.[h|cpp]` (simple thread pool).  
• Task submission is part of `ServiceRegistry` so plugins remain agnostic.

E. Build System  
• Root `CMakeLists.txt` sets `CMAKE_INTERPROCEDURAL_OPTIMIZATION ON` (LTO).  
• `kai-core` built as a **shared library** (`libkai_core.dylib`) exposing only the C façade.  
• Plugins link privately to `kai-core` headers (importing modules), but not to its binary symbols except the C API.  
• Use `add_library(plugin_xxx MODULE …)` for `.dylib` output.

---

3. Advantages & Trade-offs

---

Pros

1. Drop-in extensibility (B) without sacrificing hot-loop performance (C).
2. Core binary remains tiny; crash isolation per plugin.
3. Compile times ↓ due to C++23 modules’ BMI caching; runtime perf ↑ by eliminating vtables inside plugins and hot algorithms.
4. Memory locality improved with arenas; ref-count churn reduced by favouring `unique_ptr` internally.

Cons / Mitigations

1. **ABI Drift** – Any change in C façade requires plugin rebuild.  
   → Keep façade minimal and versioned; support multiple minor versions.
2. **Mac Hardened Runtime** – `dlopen` demands signed plugins.  
   → Supply signing script; or embed plugins in app bundle with same Team ID.
3. **Binary Size** – Header-only code replicated per plugin.  
   → Use LTO/ICF; ensure inlined code actually deduped by link-time optimiser.
4. **Tooling Complexity** – Need CMake template & CI for modules + dylib.  
   → Provide `kai_add_plugin(NAME …)` macro packaging all flags.

---

4. Migration Roadmap

---

Phase 0 (Preparatory)  
• Enable modules in build (`-fmodules-ts` Clang) behind option flag.  
• Move utility headers into `core/include/` without functional change.

Phase 1 (Kernel & Registry)

1. Create `core/di/service_registry.h/cpp`.
2. Define `extern "C"` ABI structs in `core/api/abi.h`.
3. Wrap existing singleton-like services (AppIndex, Executor) as registry services.

Phase 2 (Executor & Arena)  
• Implement `core/async/executor.{h,cpp}`, return `Expected<void,Error>` on submit.  
• Add `arena_allocator.h`; integrate into search algorithm.

Phase 3 (First Plugin Extraction)  
• Move macOS `AppScanner` implementation into `plugins/scanner_macos/`.  
• Provide `kaiRegisterPlugin()` to register `IAppScanner` factory with registry.

Phase 4 (Header-only Modules)  
• Convert `fuzzy_match`, `ranking/heuristic_scorer` to modules.  
• Replace virtual `HeuristicScorer` with CRTP/variant.  
• Measure performance & binary size.

Phase 5 (LLM Providers)  
• Extract `openai_model`, `anthropic_model` plugins; adopt header-only helper algorithms (token counting, JSON parsing using `simdjson` header-only).

Phase 6 (Tooling & Docs)  
• Add `kai add-plugin my_plugin` script scaffold.  
• Update developer docs; CI builds and signs each plugin.

---

5. Next Action

---

If you’re happy with this blended architecture, answer **“go”** and we’ll switch to Go mode.  
Initial concrete tasks we can implement in that session:

1. Add `core/di/service_registry.h` (interface + minimal implementation).
2. Add `core/async/executor.{h,cpp}` with a simple thread-pool.
3. Refactor `AppContext` usages in one file (e.g. `app_index.cpp`) to use the ServiceRegistry to showcase the pattern.

Otherwise, stay in Deep mode (`deep`) and we can refine any aspect (modules, ABI, memory pools, build system, etc.).
