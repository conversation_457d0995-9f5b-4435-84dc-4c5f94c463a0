#!/bin/bash

# Script to build and run the application with optimizations enabled

# Set up colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${YELLOW}Building optimized application...${NC}"

# Create build directory if it doesn't exist
mkdir -p build-release

# Navigate to build directory
cd build-release

# Configure with Release build type using Ninja
echo -e "${YELLOW}Configuring CMake with Ninja and Release build type...${NC}"
cmake -G Ninja -DCMAKE_BUILD_TYPE=Release ..

if [ $? -ne 0 ]; then
    echo -e "${RED}CMake configuration failed!${NC}"
    exit 1
fi

# Determine number of CPU cores for parallel build
if [[ "$OSTYPE" == "darwin"* ]]; then
    # macOS
    CORES=$(sysctl -n hw.ncpu)
else
    # Linux and others
    CORES=$(nproc)
fi

# Build the application using CMake (which will use Ninja) in parallel
echo -e "${YELLOW}Building with ${CORES} cores...${NC}"
cmake --build . --parallel ${CORES}

if [ $? -ne 0 ]; then
    echo -e "${RED}Build failed!${NC}"
    exit 1
fi

echo -e "${GREEN}Build successful!${NC}"

# Run the application
echo -e "${GREEN}Running application...${NC}"
echo -e "${GREEN}Note: The application will show the UI immediately while scanning for applications in the background.${NC}"
echo -e "${GREEN}Performance metrics will be displayed in the console.${NC}"
echo ""

# Check if binary exists and is executable
if [ -x "./bin/launcher" ]; then
    ./bin/launcher
elif [ -x "./launcher" ]; then
    ./launcher
else
    echo -e "${RED}Could not find executable. Please check the build output.${NC}"
    exit 1
fi 