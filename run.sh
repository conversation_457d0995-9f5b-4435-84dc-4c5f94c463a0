#!/bin/bash

# Development run script for the project
# Builds the project then launches it in development mode

# Parse command line arguments
RUN_TESTS=""
BUILD_TYPE="Debug"  # default for development run
# Out-of-source build directory mapping
BUILD_DIR="build-debug"
while [[ $# -gt 0 ]]; do
    case $1 in
        --fast-tests)
            RUN_TESTS="fast"
            shift
            ;;
        --all-tests)
            RUN_TESTS="all"
            shift
            ;;
        --debug)
            BUILD_TYPE="Debug"
            BUILD_DIR="build-debug"
            shift
            ;;
        --release)
            BUILD_TYPE="Release"
            BUILD_DIR="build"
            shift
            ;;
        --legacy-tests)
            RUN_TESTS="legacy"
            shift
            ;;
        --help)
            echo "Usage: $0 [OPTIONS]"
            echo "Options:"
            echo "  --fast-tests    Run fast test subset (1.68s, 89% faster)"
            echo "  --all-tests     Run all tests in parallel (5.02s, 67% faster)"
            echo "  --legacy-tests  Run tests sequentially (15.35s, for debugging)"
            echo "  --help          Show this help message"
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            echo "Use --help for usage information"
            exit 1
            ;;
    esac
done

# Print header with timestamp
echo "====================================================="
echo "Development run started: $(date)"
echo "====================================================="

# Print system information
echo "System information:"
echo "  OS: $(uname -s)"
echo "  Hostname: $(hostname)"
echo "  Kernel: $(uname -r)"
echo "  Architecture: $(uname -m)"

# Record start time for overall duration calculation
start_time=$(date +%s)

# Build the project first
echo "====================================================="
echo "Building the project (${BUILD_TYPE} → ${BUILD_DIR})..."
echo "====================================================="

# Create build directory if it doesn't exist
mkdir -p "$BUILD_DIR"

# Navigate to the build directory
cd "$BUILD_DIR"

# Configure the project
echo "Configuring with CMake..."

# ------------------- Compiler cache & linker detection ----------------------
cmake_common_flags=(
    -G Ninja ..
    -DCMAKE_EXPORT_COMPILE_COMMANDS=ON
    -DCMAKE_BUILD_TYPE=${BUILD_TYPE}
    $([[ "${BUILD_TYPE}" == "Debug" ]] && echo "-DKAI_DISABLE_CODESIGN_VERIFY=ON")
)

if [ -f CMakeCache.txt ] && grep -q "CMAKE_BUILD_TYPE:STRING=${BUILD_TYPE}" CMakeCache.txt; then
    echo "CMake already configured for ${BUILD_TYPE} – skipping configure step"
else
    cmake "${cmake_common_flags[@]}"
fi
cmake_status=$?
if [ $cmake_status -ne 0 ]; then
    echo "Error: CMake configuration failed with exit code $cmake_status"
    exit $cmake_status
fi

# Build the project
echo "Building..."
# Get the number of CPU cores, default to 4 if detection fails
num_cores=$(sysctl -n hw.ncpu 2>/dev/null || echo 4)
cmake --build . --parallel $num_cores
build_status=$?
if [ $build_status -ne 0 ]; then
    echo "Error: Build failed with exit code $build_status"
    exit $build_status
fi

# Calculate and print build duration
build_end_time=$(date +%s)
build_duration=$((build_end_time - start_time))
build_minutes=$((build_duration / 60))
build_seconds=$((build_duration % 60))

echo "Build completed in ${build_minutes}m ${build_seconds}s"

# Run tests if requested
if [ -n "$RUN_TESTS" ]; then
    echo "====================================================="
    echo "Running tests..."
    echo "====================================================="
    
    case $RUN_TESTS in
        "fast")
            echo "Running fast test subset (parallel, ~1.68s)..."
            ../scripts/run_tests_fast.sh
            ;;
        "all")
            echo "Running all tests (parallel, ~5.02s)..."
            ../scripts/run_tests_parallel.sh
            ;;
        "legacy")
            echo "Running tests (sequential, ~15.35s)..."
            ctest --output-on-failure
            ;;
    esac
    
    test_status=$?
    if [ $test_status -ne 0 ]; then
        echo "Error: Tests failed with exit code $test_status"
        exit $test_status
    fi
    
    echo "====================================================="
    echo "Tests passed successfully"
fi

echo "====================================================="

# Run the application
echo "====================================================="
echo "Starting application in development mode..."
echo "====================================================="

# Check if the binary exists
if [ ! -f "./bin/launcher" ]; then
    echo "Error: Application binary not found at ./bin/launcher"
    exit 1
fi

# Run the application
./bin/launcher
run_status=$?

# Calculate total duration
end_time=$(date +%s)
duration=$((end_time - start_time))
minutes=$((duration / 60))
seconds=$((duration % 60))

echo "====================================================="
if [ $run_status -eq 0 ]; then
    echo "Application exited successfully"
else
    echo "Application exited with code $run_status"
fi
echo "Total runtime: ${minutes}m ${seconds}s"
echo "=====================================================" 