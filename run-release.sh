#!/bin/bash

# Script to build and run the application with optimizations enabled
# Minimal output mode - only errors are displayed

# Set up colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Create build directory if it doesn't exist
mkdir -p build-release > /dev/null 2>&1

# Navigate to build directory
cd build-release

# Configure with Release build type using Ninja
cmake -G Ninja -DCMAKE_BUILD_TYPE=Release -Wno-dev .. > /dev/null 2>&1

if [ $? -ne 0 ]; then
    echo -e "${RED}CMake configuration failed!${NC}"
    exit 1
fi

# Determine number of CPU cores for parallel build
if [[ "$OSTYPE" == "darwin"* ]]; then
    # macOS
    CORES=$(sysctl -n hw.ncpu)
else
    # Linux and others
    CORES=$(nproc)
fi

# Build the application using CMake (which will use Ninja) - Verbose
cmake --build . --parallel ${CORES} > /dev/null 2>&1

if [ $? -ne 0 ]; then
    echo -e "${RED}Build failed!${NC}"
    exit 1
fi

echo -e "${GREEN}Build successful!${NC}"

# Run the application
echo -e "${GREEN}Running...${NC}"
echo ""

# Check if binary exists and is executable
if [ -x "./bin/launcher" ]; then
    ./bin/launcher
elif [ -x "./launcher" ]; then
    ./launcher
else
    echo -e "${RED}Could not find executable. Please check the build output.${NC}"
    exit 1
fi 