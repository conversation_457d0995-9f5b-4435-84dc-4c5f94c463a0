#!/bin/bash

# Exit on error
set -e

# Configuration
APP_NAME="MicroLauncher"
APP_IDENTIFIER="com.launcher.app"
APP_VERSION="0.1.36"
ICON_NAME="AppIcon"
SIGN_APP=false
DEVELOPER_ID=""
CERT_TYPE="Apple Development"  # Default certificate type

# Parse command line arguments
while [[ $# -gt 0 ]]; do
  case $1 in
    --sign)
      SIGN_APP=true
      shift
      ;;
    --developer-id)
      DEVELOPER_ID="$2"
      shift 2
      ;;
    --cert-type)
      CERT_TYPE="$2"
      shift 2
      ;;
    --help)
      echo "Usage: $0 [options]"
      echo "Options:"
      echo "  --sign                Sign the application bundle"
      echo "  --developer-id ID     Developer ID to use for signing"
      echo "  --cert-type TYPE      Certificate type (default: 'Apple Development')"
      echo "                        Options: 'Apple Development', 'Developer ID Application', 'Apple Distribution'"
      echo "  --help                Show this help message"
      exit 0
      ;;
    *)
      echo "Unknown option: $1"
      exit 1
      ;;
  esac
done

# Determine script directory and project root
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
PROJECT_ROOT="$SCRIPT_DIR"

# Create build directory if it doesn't exist
mkdir -p build-release
cd build-release

# Build the application in Release mode
echo "Building application in Release mode..."
cmake -DCMAKE_BUILD_TYPE=Release ..
cmake --build . --target launcher || true

# Create app bundle structure
echo "Creating app bundle structure..."
APP_BUNDLE="${APP_NAME}.app"
mkdir -p "${APP_BUNDLE}/Contents/MacOS"
mkdir -p "${APP_BUNDLE}/Contents/Resources"

# Copy executable
echo "Copying executable..."
cp bin/launcher "${APP_BUNDLE}/Contents/MacOS/${APP_NAME}"

# Copy plugins into app bundle
echo "Copying plugins into app bundle..."
if [ -d "bin/Plugins" ]; then
    mkdir -p "${APP_BUNDLE}/Contents/PlugIns"
    cp -R bin/Plugins/* "${APP_BUNDLE}/Contents/PlugIns/" || true
fi

# Create Info.plist
echo "Creating Info.plist..."
cat > "${APP_BUNDLE}/Contents/Info.plist" << EOF
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>CFBundleDevelopmentRegion</key>
    <string>en</string>
    <key>CFBundleExecutable</key>
    <string>${APP_NAME}</string>
    <key>CFBundleIconFile</key>
    <string>${ICON_NAME}</string>
    <key>CFBundleIdentifier</key>
    <string>${APP_IDENTIFIER}</string>
    <key>CFBundleInfoDictionaryVersion</key>
    <string>6.0</string>
    <key>CFBundleName</key>
    <string>${APP_NAME}</string>
    <key>CFBundlePackageType</key>
    <string>APPL</string>
    <key>CFBundleShortVersionString</key>
    <string>${APP_VERSION}</string>
    <key>CFBundleVersion</key>
    <string>${APP_VERSION}</string>
    <key>LSMinimumSystemVersion</key>
    <string>10.15</string>
    <key>NSHighResolutionCapable</key>
    <true/>
    <key>NSPrincipalClass</key>
    <string>NSApplication</string>
    <key>LSUIElement</key>
    <true/>
    <key>NSAppleEventsUsageDescription</key>
    <string>This app needs to access your browser history to provide website search functionality.</string>
    <key>NSDesktopFolderUsageDescription</key>
    <string>This app needs access to your files to provide search functionality.</string>
    <key>NSDocumentsFolderUsageDescription</key>
    <string>This app needs access to your files to provide search functionality.</string>
    <key>NSDownloadsFolderUsageDescription</key>
    <string>This app needs access to your files to provide search functionality.</string>
    <key>NSRemovableVolumesUsageDescription</key>
    <string>This app needs access to your files to provide search functionality.</string>
    <key>NSFileProviderDomainUsageDescription</key>
    <string>This app needs access to your files to provide search functionality.</string>
    <key>NSSystemAdministrationUsageDescription</key>
    <string>This app needs access to your system to provide full search functionality.</string>
</dict>
</plist>
EOF

# Generate and copy the app icon
echo "Generating and copying app icon..."
RESOURCES_DIR="${PROJECT_ROOT}/src/ui/macos/resources"
ICON_PATH="${APP_BUNDLE}/Contents/Resources/${ICON_NAME}.icns"

# Check if the icon already exists in the resources directory
if [ -f "${RESOURCES_DIR}/${ICON_NAME}.icns" ]; then
    echo "Using existing icon from resources directory"
    cp "${RESOURCES_DIR}/${ICON_NAME}.icns" "${ICON_PATH}"
else
    # Generate the icon using our script
    echo "Generating icon using generate_app_icon.sh script"
    "${PROJECT_ROOT}/scripts/generate_app_icon.sh"
    
    # Copy the generated icon
    if [ -f "${RESOURCES_DIR}/${ICON_NAME}.icns" ]; then
        cp "${RESOURCES_DIR}/${ICON_NAME}.icns" "${ICON_PATH}"
    else
        echo "Warning: Could not find or generate icon. Using system default."
        cp /System/Library/CoreServices/CoreTypes.bundle/Contents/Resources/GenericApplicationIcon.icns "${ICON_PATH}"
    fi
fi

# Copy JS syntax-highlighting engine (highlight.js)
echo "Copying highlight.min.js..."
cp "${PROJECT_ROOT}/src/resources/highlight.min.js" "${APP_BUNDLE}/Contents/Resources/highlight.min.js"

# Sign the application if requested
if [ "$SIGN_APP" = true ]; then
    if [ -z "$DEVELOPER_ID" ]; then
        echo "Error: Developer ID is required for signing. Use --developer-id option."
        exit 1
    fi
    
    echo "Signing application with certificate: ${CERT_TYPE}: ${DEVELOPER_ID}"
    
    # First sign individual plug-ins (if any)
    if [ -d "${APP_BUNDLE}/Contents/PlugIns" ]; then
        for dylib in "${APP_BUNDLE}/Contents/PlugIns"/*; do
            codesign --force --options runtime --sign "${CERT_TYPE}: ${DEVELOPER_ID}" "$dylib"
        done
    fi

    codesign --force --options runtime --sign "${CERT_TYPE}: ${DEVELOPER_ID}" "${APP_BUNDLE}"
    
    # Verify signature
    echo "Verifying signature..."
    codesign -vvv --deep --strict "${APP_BUNDLE}"
    
    # Create a DMG for distribution
    echo "Creating DMG for distribution..."
    DMG_NAME="${APP_NAME}-${APP_VERSION}.dmg"
    hdiutil create -volname "${APP_NAME}" -srcfolder "${APP_BUNDLE}" -ov -format UDZO "${DMG_NAME}"
    
    # Sign the DMG
    echo "Signing DMG..."
    codesign --force --sign "${CERT_TYPE}: ${DEVELOPER_ID}" "${DMG_NAME}"
    
    echo "DMG created and signed: $(pwd)/${DMG_NAME}"
fi

echo "App bundle created at: $(pwd)/${APP_BUNDLE}"
echo "You can now copy it to your Applications folder."

# Provide instructions for notarization (if signed)
if [ "$SIGN_APP" = true ]; then
    echo ""
    if [ "$CERT_TYPE" = "Developer ID Application" ]; then
        echo "To notarize the application for distribution, run:"
        echo "xcrun notarytool submit \"${DMG_NAME}\" --apple-id \"your-apple-id\" --password \"app-specific-password\" --team-id \"your-team-id\" --wait"
        echo ""
        echo "After notarization is complete, staple the notarization ticket:"
        echo "xcrun stapler staple \"${APP_BUNDLE}\""
        echo "xcrun stapler staple \"${DMG_NAME}\""
    elif [ "$CERT_TYPE" = "Apple Distribution" ]; then
        echo "Note: Apps signed with 'Apple Distribution' certificates are for App Store distribution."
        echo "To prepare for App Store submission, you should create an archive using Xcode"
        echo "or use the 'xcrun altool' command to upload the app to App Store Connect."
    else
        echo "Note: Apps signed with '${CERT_TYPE}' certificates are for development only."
        echo "For distribution outside the App Store, you need a 'Developer ID Application' certificate."
        echo "For distribution via the App Store, you need an 'Apple Distribution' certificate."
    fi
fi 