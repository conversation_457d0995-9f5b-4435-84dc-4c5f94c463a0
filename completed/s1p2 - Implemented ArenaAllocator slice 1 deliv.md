Implemented ArenaAllocator slice 1 deliverables 2 (Go mode):

1. Added header-only façade  
   • `src/core/memory/arena_allocator.h` – inline `alloc`, `free`, `getStats`, `ArenaStats` with compile-time switch between rpmalloc (default) and mimalloc (`KAI_USE_MIMALLOC`).

2. Service implementation  
   • `arena_allocator_service.{h,cpp}` under `src/core/memory/` implementing `IService`, handling backend `initialize/finalize`, exposing `stats()`.

3. Tests  
   • `src/tests/core/memory/arena_allocator_test.cpp` with Catch2 smoke & stats tests.  
   • Registered in `src/tests/core/CMakeLists.txt`.

4. Build system  
   • Root `CMakeLists.txt`: added `KAI_ALLOCATOR` cache variable, FetchContent for rpmalloc v1.4.4 (default) and mimalloc v2.1.2, compile-time definitions, and exported `${KAI_ALLOCATOR_LIB}`.  
   • Linked allocator lib and new service source in `src/core/CMakeLists.txt`.

5. Core sources updated  
   • Added `memory/arena_allocator_service.cpp` to build.

Everything compiles with either backend (pass `-DKAI_ALLOCATOR=mimalloc` to switch). Run `ctest` to execute new `ArenaAllocatorTest`.
