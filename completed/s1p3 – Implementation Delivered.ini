Go Mode – Implementation Delivered ✅

Key additions:
1. src/third_party/moodycamel/concurrentqueue.h  
   • Minimal stub (enqueue / try_dequeue) to avoid heavy external pull.

2. Executor Service  
   • src/core/async/executor_service.{h,cpp} – fixed thread-pool, `submit()` returns std::future, graceful stop/drain.  
   • Integrates stub ConcurrentQueue.

3. Event Bus Service  
   • src/core/events/event_bus_service.{h,cpp} – single-dispatcher queue, async fan-out via ExecutorSvc, RAII SubscriptionHandle.

4. Benchmarks & Tests  
   • src/benchmarks/event_bus_bench.cpp – 10 k-message latency benchmark.  
   • src/tests/core/events/event_bus_test.cpp – unit-test for publish/subscribe.

5. CMake wiring  
   • core/CMakeLists.txt – adds new async / events sources.  
   • src/benchmarks/CMakeLists.txt – bench target.  
   • src/tests/core/CMakeLists.txt – test target.

The services register under numeric IDs already in `service_id.h`, satisfy Slice-1 deliverable ③, and remain header-only safe.  
You can now `./build.sh && ctest -R EventBusTest` or run `event_bus_bench` to verify p95 latency.

Let me know if further optimisation or integration work is needed!
