Implemented ServiceRegistry foundation (Go mode)

Added new Slice-1 deliverables:

1. src/core/foundation/service_id.h  
   • Numeric enum ServiceId with stable indices and kMaxServices helper.

2. src/core/foundation/service_traits.h  
   • Lightweight TypeList meta, Size/ToArray helpers.  
   • service_traits specialisations for the initial 10 core services with declared Dependencies DAG.

3. src/core/foundation/iservice.h  
   • Abstract IService interface with id(), start(), stop() using util::VoidResult.

4. src/core/foundation/registry.{h,cpp}  
   • Fixed-size std::array slots, O(1) lookup, compile-time → runtime dependency resolver, Kahn-style topo-sort, deterministic start/stop, cycle detection (KaiError enum).  
   • API: registerService, get<T>(), startAll(), stopAll().

5. CMake integration  
   • foundation/CMakeLists.txt registers new sources.  
   • Added `add_subdirectory(foundation)` to core/CMakeLists.

6. Unit tests (Catch2)  
   • src/tests/core/foundation/service_registry_test.cpp with dummy services verifying successful start/stop and duplicate-registration error.  
   • Updated tests/core/CMakeLists to build & register new test.

Build passes (`./build.sh`).  
ServiceRegistry ready for further service implementations and satisfies ≤ 5 ns lookup via direct array index.

Let me know if further tweaks or additional tests are desired!
