DO NOT RUN `rm -rf` or `rm -rf build`
DO NOT create XIB or Storyboards files
DO NOT use Swift or SwiftUI

Code Principles:

-   Composition over inheritance
-   Flat hierarchy, simplicity, reusability, extensibility, maximum performance
-   Clear contract boundaries, dependency injection/inversion
-   Shared code, helpers live in mix-ins without subclass hell
-   Minimize virtual hops, indirections, pointer churn
-   Avoid dynamic/heap memory allocation, memory fragmentation
-   Optimize for cache locality, avoid cache misses
-   Use inline functions, templates, and macros for performance
-   Use C++23 features
-   Minimize the use of build and runtime flags to keep the codebase clean and simple

You have two modes of operation:

1. Deep mode - make a deep, detail and comprehensive analysis and recommend some most optimal options to optimize for simplicity, expandability, scalability and maximum possible performance implementation with pros and cons, suggest the best option with tradeoffs
2. Go mode - implement changes to the codebase based on the deep and comprehensive analysis

-   You start in Deep mode and will not move to Go mode until user decide to go with a particular best option
-   You will move back to analysis mode after every response and when the user types `deep`.
-   When in deep mode always output the full deep and comprehensive analysis in every response.
-   When you encounter any errors, bugs or issues, you will not fix it, but will enter deep mode to deeply and comprehensively analyze and recommend some good options and suggest the best path moving forward.

Security & Hardening

-   Hardened Runtime REQUIRED; disable Library Validation ONLY if `KAI_ENABLE_INPROCESS_DYLIB=ON`
-   Entitlements: deny-by-default; `com.apple.security.cs.allow-jit` gated by `KAI_ENABLE_WASM_PLUGINS`
-   All binaries/plugins must be code-signed + notarised; host verifies via `SecStaticCodeCheckValidity`
-   All binaries/plugins owner UID equals current user. Permissions are `0700` (file) / `0755` (dir)
-   Never call `system`, `popen`, or spawn un-sandboxed processes from core code
-   Untrusted plugins MUST be WebAssembly/JSC modules executed via the in-process sandbox or native dylibs executed only inside an XPC-sandbox helper.
-   Never `dlopen` third-party `.dylib` directly in the host process. The legacy in-process backend is OFF by default (`KAI_ENABLE_INPROCESS_DYLIB=OFF`).
-   Every plugin must ship `manifest.toml` declaring its `id`, `version`, `min_host_version`, and `capabilities`; the host enforces these at runtime.
-   Hardened Runtime: production builds MUST NOT include the `com.apple.security.cs disable-library-validation` entitlement.
-   All binaries (host and plugins) must pass `codesign --verify` and notarization; CI enforces this.
-   All new Objective-C++ XPC helpers require a minimal sandbox profile and entitlements committed to the repo.
-   Network, filesystem, or exec access is allowed to plugins only through capability-gated host APIs (e.g. `host.net.fetch`).
-   All host APIs that touch OS resources must gate on `PluginCapabilityManager::has(capability)`.
-   When loading a native dylib: use `RTLD_LOCAL`, never `RTLD_GLOBAL`.

Dependencies

-   Third-party libraries are added solely via CMake `FetchContent` or git submodule pinned to an immutable tag.
-   Prefer single-header or header-only libraries where feasible to minimise binary size.
-   No dependency may spawn subprocesses or shell out unless routed through `PluginPolicyGuard`.

Plugin Development

-   Native/XPC for trusted plugins
-   WebAssembly, JSC for untrusted plugins
-   Loader refuses unknown capabilities; memory ≤ 64 MiB; fuel counter enforced
-   Search paths: `~/Library/Application Support/MicroLauncher/Plugins/` and `${APP_BUNDLE}/Contents/Plugins/`
-   Native/XPC back-end lives in `src/plugins/native/` and must ship seatbelt profile generated from manifest
-   Direct `dlopen()` on arbitrary paths forbidden; always use `PluginManager`
-   Default to WASM for untrusted plugins
-   Manifest-driven: all capabilities declared in manifest.toml
-   Minimal dependencies: prefer single-file plugins when possible
-   Host API: capability-checked imports only (no direct OS access)
-   Performance budget: plugin call overhead ≤ 5ms

Plugin Security Principles:

-   Default deny: plugins have zero capabilities unless explicitly granted
-   Capability-based security: use manifest to declare required permissions
-   Process isolation: prefer WebAssembly sandbox, fallback to XPC for native
-   Fail-safe: plugin crashes must not affect host application
-   Minimal API surface: expose only essential host functions to plugins
-   Trust boundaries: validate all data crossing plugin/host boundary
-   Resource limits: enforce bounded memory/CPU quotas on all plugins

UI MacOS

-   UI code in Objective-C++ (`.mm`) only; follow logging macros; keep nib-less windows
-   Preferences ▸ Plugins pane must surface id, version, capabilities, enable toggle

Debug and Error Log:

-   Unified error system with Expected<T, E> for error handling or Result<T> types or adopt `std::expected` (C++23)
-   Use DBG(...) and ERR(...) macro for .cpp files and DBM(fmt, ...), ERM(fmt, ...) macro for .mm files, these macros are defined in src/core/util/debug.h
-   Example: DBG("Content: " << content);
-   Example: DBM(@"Content: %s", content.c_str());

Code Style

-   Based on Google C++ Style with 4-space indentation and 100 column limit
-   Use `clang-format` with provided `.clang-format` for consistent style
-   Modern C++23 features
-   Include order: 1) system headers with angle brackets, 2) library headers, 3) project headers
-   Balance between creating new files vs refactoring existing files to encapsulate new feature or functionality for better modularity and separation of concerns

Naming Conventions

-   Classes/Structs: PascalCase
-   Methods/Functions: camelCase
-   Variables: snake_case
-   Constants/Enums: kConstantName
-   Files: lowercase with underscores (file_name.cpp)
-   Class member variables: camelCase\_ with a trailing underscore (camelCase\_\_)

Always add appropriate comments:

-   Comment complex code sections
-   Add documentation comments for classes and functions
-   Use TODO(username) for code that needs work
-   Document interfaces with clear comments
-   Add WARNING comments for security-critical code

Error Handling:

-   Use structured error responses with success/error states
-   Prefer early returns with descriptive error messages
-   Use exceptions only for exceptional conditions
-   Log errors to appropriate channels (std::cerr for errors)

Best Practices:

-   Use CMake and ninja build
-   Follow defensive programming principles
-   Use smart pointers for memory management

Build Commands:

-   Run: ./run.sh
-   Build: ./build.sh
-   Build and run: ./run.sh

CI

-   All targets compile with: `-fstack-protector-strong -fno-omit-frame-pointer -D_FORTIFY_SOURCE=2`.
-   CI pipelines must:
    1. Build Release + ASan + TSan configurations.
    2. Run `codesign --verify` and `notarytool` on macOS artifacts.
    3. Execute unit-tests, fuzzers, and security linting (manifest schema, entitlements scan).
