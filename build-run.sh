#!/bin/bash

# Build and run script for the project
# First builds the project using build.sh and then runs the executable if build was successful

echo "====================================================="
echo "Build and run started: $(date)"
echo "====================================================="

# Execute build script
echo "Building the project..."
./build.sh
build_status=$?

# Check if build was successful
if [ $build_status -ne 0 ]; then
    echo "Error: Build failed with exit code $build_status"
    exit $build_status
fi

# Run the executable
echo "====================================================="
echo "Running the application..."
echo "====================================================="
./run-only.sh
run_status=$?

# Check run status
if [ $run_status -ne 0 ]; then
    echo "Error: Application exited with code $run_status"
    exit $run_status
fi

echo "====================================================="
echo "Application execution completed"
echo "=====================================================" 