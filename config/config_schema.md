# Micro Launcher Configuration Schema Guide

This document provides a comprehensive guide to the Micro Launcher configuration schema defined in `config-schema.json`. The schema defines the structure, validation rules, and constraints for all configuration files used by the Micro Launcher application.

## Table of Contents

1. [Introduction](#introduction)
2. [Schema Overview](#schema-overview)
3. [Main Configuration Structure](#main-configuration-structure)
    - [Version](#version)
    - [General Settings](#general-settings)
    - [LauncherBar Settings](#launcherbar-settings)
    - [Actions Settings](#actions-settings)
    - [Context Settings](#context-settings)
    - [Performance Settings](#performance-settings)
    - [Security Settings](#security-settings)
4. [Definitions](#definitions)
    - [Tool Parameters](#tool-parameters)
    - [Execution Steps](#execution-steps)
    - [Static Actions (Tools)](#static-actions-tools)
    - [Dynamic Actions (Requests)](#dynamic-actions-requests)
    - [AI Providers](#ai-providers)
    - [Context Providers](#context-providers)
5. [Using the Schema](#using-the-schema)
    - [Validation](#validation)
    - [IDE Integration](#ide-integration)
    - [Generating Documentation](#generating-documentation)
    - [Generating UI Forms](#generating-ui-forms)
6. [Examples](#examples)
    - [Complete Configuration Example](#complete-configuration-example)
    - [Static Action Example](#static-action-example)
    - [Dynamic Action Example](#dynamic-action-example)
    - [AI Provider Example](#ai-provider-example)
7. [Best Practices](#best-practices)
8. [Troubleshooting](#troubleshooting)

## Introduction

The Micro Launcher configuration schema is a JSON Schema document that defines the structure and validation rules for all configuration files used by the Micro Launcher application. It serves as both a specification for developers and a validation tool to ensure configuration files are correctly formatted.

The schema follows the [JSON Schema Draft-07](https://json-schema.org/draft-07/json-schema-release-notes.html) specification and can be used with any JSON Schema validator that supports this version.

## Schema Overview

The schema defines the following main components:

1. **Main Configuration**: The top-level structure of the `config.json` file
2. **Definitions**: Reusable components that define the structure of various configuration elements
3. **Validation Rules**: Constraints on values, required properties, and data types

The schema uses JSON Schema features such as:

-   Type validation
-   Required properties
-   Enumerated values
-   Minimum and maximum constraints
-   Pattern matching
-   Default values
-   References to definitions

## Main Configuration Structure

The main configuration file (`config.json`) must contain the following top-level sections:

```json
{
  "version": "1.0.0",
  "general": { ... },
  "launcher_bar": { ... },
  "actions": { ... },
  "context": { ... },
  "performance": { ... },
  "security": { ... }
}
```

### Version

The `version` field specifies the version of the configuration format. It must follow semantic versioning (e.g., "1.0.0").

```json
"version": {
  "type": "string",
  "description": "Version of the configuration format",
  "pattern": "^\\d+\\.\\d+\\.\\d+$"
}
```

### General Settings

The `general` section contains application-wide settings:

```json
"general": {
  "launcher_hotkey": "Alt+Space",
  "theme": "system",
  "language": "en",
  "startup_on_login": true,
  "check_updates": true,
  "telemetry_enabled": false
}
```

| Property            | Type    | Description                                | Default  | Required |
| ------------------- | ------- | ------------------------------------------ | -------- | -------- |
| `launcher_hotkey`   | string  | Keyboard shortcut to activate the launcher | -        | Yes      |
| `theme`             | string  | UI theme setting (system, light, dark)     | "system" | Yes      |
| `language`          | string  | Application language (ISO code)            | "en"     | Yes      |
| `startup_on_login`  | boolean | Whether to launch at login                 | true     | Yes      |
| `check_updates`     | boolean | Whether to check for updates               | true     | No       |
| `telemetry_enabled` | boolean | Whether to send anonymous usage data       | false    | No       |

### LauncherBar Settings

The `launcher_bar` section controls the appearance and behavior of the launcher interface:

```json
"launcher_bar": {
  "width": 600,
  "height": 600,
  "max_results": 10,
  "animation_speed": "normal",
  "font_size": 14,
  "opacity": 0.95,
  "position": "center",
  "blur_background": true
}
```

| Property          | Type    | Description                       | Default  | Required |
| ----------------- | ------- | --------------------------------- | -------- | -------- |
| `width`           | integer | Width in pixels (300-2000)        | 600      | Yes      |
| `height`          | integer | Height in pixels (200-1500)       | 600      | No       |
| `max_results`     | integer | Maximum results to display (5-50) | 10       | Yes      |
| `animation_speed` | string  | Speed of UI animations            | "normal" | No       |
| `font_size`       | integer | Font size for the UI (8-32)       | 14       | No       |
| `opacity`         | number  | Window opacity (0.1-1.0)          | 0.95     | No       |
| `position`        | string  | Window position on screen         | "center" | No       |
| `blur_background` | boolean | Whether to use blur effect        | true     | No       |

### Actions Settings

The `actions` section configures action management and preferences:

```json
"actions": {
  "default_ai_provider": "openai",
  "default_ai_model": "gpt-4",
  "suggestion_algorithm": {
    "recency_weight": 0.3,
    "frequency_weight": 0.3,
    "relevance_weight": 0.4
  },
  "require_approval_for_dangerous_actions": true,
  "actions_directory": "~/.config/microlauncher/actions"
}
```

| Property                                 | Type    | Description                      | Default                           | Required |
| ---------------------------------------- | ------- | -------------------------------- | --------------------------------- | -------- |
| `default_ai_provider`                    | string  | Default AI provider              | -                                 | Yes      |
| `default_ai_model`                       | string  | Default AI model                 | -                                 | Yes      |
| `suggestion_algorithm`                   | object  | Weights for suggestions          | -                                 | Yes      |
| `require_approval_for_dangerous_actions` | boolean | Whether to require approval      | true                              | Yes      |
| `actions_directory`                      | string  | Directory for action definitions | "~/.config/microlauncher/actions" | Yes      |

The `suggestion_algorithm` object contains weights that must sum to 1.0:

| Property           | Type   | Description                | Default | Required |
| ------------------ | ------ | -------------------------- | ------- | -------- |
| `recency_weight`   | number | Weight for recency (0-1)   | 0.3     | Yes      |
| `frequency_weight` | number | Weight for frequency (0-1) | 0.3     | Yes      |
| `relevance_weight` | number | Weight for relevance (0-1) | 0.4     | Yes      |

### Context Settings

The `context` section configures context providers:

```json
"context": {
  "enabled_providers": [
    "clipboard",
    "active_window",
    "selected_text",
    "current_directory"
  ],
  "max_context_size": 4096
}
```

| Property            | Type    | Description                                | Default                                                              | Required |
| ------------------- | ------- | ------------------------------------------ | -------------------------------------------------------------------- | -------- |
| `enabled_providers` | array   | List of enabled context providers          | ["clipboard", "active_window", "selected_text", "current_directory"] | Yes      |
| `max_context_size`  | integer | Maximum context size in bytes (1024-16384) | 4096                                                                 | Yes      |

### Performance Settings

The `performance` section controls resource usage and caching:

```json
"performance": {
  "memory_limit_mb": 200,
  "background_process_priority": "low",
  "cache_size_mb": 50,
  "search_cache": {
    "enabled": true,
    "max_size": 100,
    "ttl_seconds": 300
  }
}
```

| Property                      | Type    | Description                          | Default | Required |
| ----------------------------- | ------- | ------------------------------------ | ------- | -------- |
| `memory_limit_mb`             | integer | Maximum memory usage in MB (50-1000) | 200     | Yes      |
| `background_process_priority` | string  | Priority for background processes    | "low"   | Yes      |
| `cache_size_mb`               | integer | Maximum cache size in MB (10-500)    | 50      | Yes      |
| `search_cache`                | object  | Search cache settings                | -       | Yes      |

The `search_cache` object contains:

| Property      | Type    | Description                       | Default | Required |
| ------------- | ------- | --------------------------------- | ------- | -------- |
| `enabled`     | boolean | Whether to enable search cache    | true    | Yes      |
| `max_size`    | integer | Maximum cache entries (10-1000)   | 100     | Yes      |
| `ttl_seconds` | integer | Time-to-live in seconds (30-3600) | 300     | Yes      |

### Security Settings

The `security` section controls security-related settings:

```json
"security": {
  "encryption_enabled": true,
  "api_key_storage": "system_keychain"
}
```

| Property             | Type    | Description                       | Default           | Required |
| -------------------- | ------- | --------------------------------- | ----------------- | -------- |
| `encryption_enabled` | boolean | Whether to encrypt sensitive data | true              | Yes      |
| `api_key_storage`    | string  | Storage method for API keys       | "system_keychain" | Yes      |

## Definitions

The schema includes several reusable definitions that define the structure of various configuration components.

### Tool Parameters

The `tool_parameter` definition describes parameters for actions:

```json
{
    "name": "query",
    "type": "string",
    "required": true,
    "description": "The search query",
    "default": ""
}
```

| Property      | Type    | Description                   | Default | Required |
| ------------- | ------- | ----------------------------- | ------- | -------- |
| `name`        | string  | Parameter name                | -       | Yes      |
| `type`        | string  | Parameter data type           | -       | Yes      |
| `required`    | boolean | Whether parameter is required | true    | No       |
| `description` | string  | Description of the parameter  | -       | Yes      |
| `default`     | any     | Default value                 | -       | No       |

The `type` field must be one of: "string", "number", "boolean", "file", "directory", "object", "array".

### Execution Steps

The `execution_step` definition describes steps in a tool's execution sequence:

```json
{
    "type": "command",
    "data": "find ${directory} -name \"*${pattern}*\" -type f -o -type d | head -n ${max_results}",
    "condition": ""
}
```

| Property      | Type          | Description                       | Default | Required |
| ------------- | ------------- | --------------------------------- | ------- | -------- |
| `type`        | string        | Type of execution step            | -       | Yes      |
| `data`        | string/object | Data for the execution step       | -       | Yes      |
| `script_type` | string        | Type of script (for script steps) | -       | No       |
| `condition`   | string        | Condition for executing this step | ""      | No       |

The `type` field must be one of: "command", "script", "api", "ui".
The `script_type` field (when present) must be one of: "shell", "applescript", "powershell", "python".

### Static Actions (Tools)

The `tool` definition describes a static action:

```json
{
  "id": "file_search",
  "name": "Find Files",
  "description": "Searches for files and directories on your system",
  "type": "tool",
  "triggers": ["find", "search", "locate"],
  "parameters": [...],
  "steps": [...],
  "metadata": {...},
  "requires_user_approval": false,
  "icon": "icons/icon.svg"
}
```

| Property                 | Type    | Description                         | Default          | Required |
| ------------------------ | ------- | ----------------------------------- | ---------------- | -------- |
| `id`                     | string  | Unique identifier                   | -                | Yes      |
| `name`                   | string  | Human-readable name                 | -                | Yes      |
| `description`            | string  | Detailed description                | -                | Yes      |
| `type`                   | string  | Type of action (must be "tool")     | -                | Yes      |
| `triggers`               | array   | Keywords that trigger this action   | -                | Yes      |
| `parameters`             | array   | Parameters required by this tool    | -                | Yes      |
| `steps`                  | array   | Sequence of steps to execute        | -                | Yes      |
| `metadata`               | object  | Usage statistics and other metadata | -                | Yes      |
| `requires_user_approval` | boolean | Whether approval is required        | false            | No       |
| `icon`                   | string  | Path to the icon file               | "icons/icon.svg" | No       |

### Dynamic Actions (Requests)

The `dynamic_action` definition describes a dynamic action (request):

```json
{
    "id": "summarize_text",
    "name": "Summarize Text",
    "description": "Create a concise summary of the selected text",
    "type": "request",
    "triggers": ["summarize", "summary", "tldr"],
    "prompt_template": "Summarize the following text in a concise manner while preserving the key points and main message:\n\n{selected_text}",
    "preferred_provider": "openai",
    "preferred_model": "gpt-4",
    "required_tools": [],
    "required_context_types": ["selected_text"],
    "requires_selected_text": true,
    "request_options": {
        "temperature": 0.5,
        "max_tokens": 500,
        "tool_use_enabled": false,
        "streaming_enabled": true
    },
    "parameters": [],
    "metadata": {
        "usage_count": 0,
        "last_used": "",
        "success_rate": 1.0,
        "category": "writing",
        "tags": ["text", "summarization"]
    },
    "icon": "icons/icon.svg"
}
```

| Property                 | Type    | Description                               | Default          | Required |
| ------------------------ | ------- | ----------------------------------------- | ---------------- | -------- |
| `id`                     | string  | Unique identifier                         | -                | Yes      |
| `name`                   | string  | Human-readable name                       | -                | Yes      |
| `description`            | string  | Detailed description                      | -                | Yes      |
| `type`                   | string  | Type of action (must be "request")        | -                | Yes      |
| `triggers`               | array   | Keywords that trigger this action         | -                | Yes      |
| `prompt_template`        | string  | Template for AI request with placeholders | -                | Yes      |
| `preferred_provider`     | string  | Preferred AI provider                     | -                | Yes      |
| `preferred_model`        | string  | Preferred AI model                        | -                | Yes      |
| `required_tools`         | array   | Tools that this request might need        | -                | No       |
| `required_context_types` | array   | Context types required by this request    | -                | No       |
| `requires_selected_text` | boolean | Whether this request needs selected text  | false            | No       |
| `request_options`        | object  | Default options for AI request            | -                | No       |
| `parameters`             | array   | Parameters required by this request       | -                | Yes      |
| `metadata`               | object  | Usage statistics and other metadata       | -                | Yes      |
| `icon`                   | string  | Path to the icon file                     | "icons/icon.svg" | No       |

The `request_options` object contains configuration for the AI request:

| Property            | Type    | Description                         | Default | Required |
| ------------------- | ------- | ----------------------------------- | ------- | -------- |
| `temperature`       | number  | Temperature for AI generation (0-2) | 0.7     | No       |
| `max_tokens`        | integer | Maximum tokens to generate          | 1000    | No       |
| `tool_use_enabled`  | boolean | Whether to allow AI to use tools    | false   | No       |
| `streaming_enabled` | boolean | Whether to stream the response      | true    | No       |

The `metadata` object for requests includes additional fields:

| Property       | Type    | Description                          | Default   | Required |
| -------------- | ------- | ------------------------------------ | --------- | -------- |
| `usage_count`  | integer | Number of times this action was used | 0         | No       |
| `last_used`    | string  | When this action was last used       | ""        | No       |
| `success_rate` | number  | Ratio of successful executions (0-1) | 1.0       | No       |
| `category`     | string  | Category of the request              | "general" | No       |
| `tags`         | array   | Tags for categorizing requests       | []        | No       |

The system supports the following context variables in prompt templates:

-   `{selected_text}` - Currently selected text
-   `{clipboard}` - Content of the clipboard
-   `{current_app}` - Name of the current application
-   `{current_url}` - URL of the current website (if in a browser)
-   `{current_file}` - Path of the current file (if in a file editor)
-   `{current_directory}` - Path of the current directory
-   `{current_time}` - Current time in the user's timezone
-   `{user_input}` - Additional input provided by the user

Request categories include:

1. **Writing Improvement** - Improve, expand, summarize, translate text
2. **Tone and Style** - Rewrite content in different tones (friendly, technical, formal, etc.)
3. **Content Generation** - Create new content like emails, blog posts, titles, etc.

### AI Providers

The `ai_provider` definition describes an AI provider configuration:

```json
{
  "provider_id": "openai",
  "name": "OpenAI",
  "enabled": true,
  "api_key_variable": "OPENAI_API_KEY",
  "base_url": "https://api.openai.com/v1",
  "models": [...],
  "default_model": "gpt-3.5-turbo",
  "request_timeout_seconds": 30,
  "rate_limit_requests_per_minute": 60
}
```

| Property                         | Type    | Description                         | Default | Required |
| -------------------------------- | ------- | ----------------------------------- | ------- | -------- |
| `provider_id`                    | string  | Unique identifier                   | -       | Yes      |
| `name`                           | string  | Human-readable name                 | -       | Yes      |
| `enabled`                        | boolean | Whether provider is enabled         | true    | Yes      |
| `api_key_variable`               | string  | Environment variable for API key    | -       | Yes      |
| `base_url`                       | string  | Base URL for API requests           | -       | Yes      |
| `models`                         | array   | Available models from this provider | -       | Yes      |
| `default_model`                  | string  | Default model to use                | -       | Yes      |
| `request_timeout_seconds`        | integer | Timeout for API requests            | 30      | No       |
| `rate_limit_requests_per_minute` | integer | Maximum requests per minute         | 60      | No       |

Each model in the `models` array has the following structure:

```json
{
    "id": "gpt-4",
    "name": "GPT-4",
    "display_name": "GPT-4",
    "supports_tool_calling": true,
    "supports_image_input": true,
    "supports_streaming": true,
    "max_tokens": 8192,
    "max_tool_calls": 15,
    "input_token_cost": 0.03,
    "output_token_cost": 0.06
}
```

| Property                | Type    | Description                              | Default | Required |
| ----------------------- | ------- | ---------------------------------------- | ------- | -------- |
| `id`                    | string  | Unique identifier for the model          | -       | Yes      |
| `name`                  | string  | Technical name of the model              | -       | Yes      |
| `display_name`          | string  | User-friendly name for display           | -       | Yes      |
| `supports_tool_calling` | boolean | Whether model can use tools              | false   | No       |
| `supports_image_input`  | boolean | Whether model can process images         | false   | No       |
| `supports_streaming`    | boolean | Whether model supports streaming         | false   | No       |
| `max_tokens`            | integer | Maximum tokens this model can process    | -       | No       |
| `max_tool_calls`        | integer | Maximum number of tool calls per request | -       | No       |
| `input_token_cost`      | number  | Cost per 1K input tokens                 | -       | No       |
| `output_token_cost`     | number  | Cost per 1K output tokens                | -       | No       |

### Context Providers

The `context_provider` definition describes a context provider configuration:

```json
{
    "provider_id": "clipboard",
    "name": "Clipboard",
    "enabled": true,
    "max_content_size": 1024,
    "refresh_interval_ms": 500,
    "include_images": false,
    "privacy": {
        "mask_sensitive_data": true,
        "sensitive_patterns": ["\\b(?:\\d[ -]*?){13,16}\\b", "\\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}\\b"]
    }
}
```

| Property              | Type    | Description                      | Default | Required |
| --------------------- | ------- | -------------------------------- | ------- | -------- |
| `provider_id`         | string  | Unique identifier                | -       | Yes      |
| `name`                | string  | Human-readable name              | -       | Yes      |
| `enabled`             | boolean | Whether provider is enabled      | true    | Yes      |
| `max_content_size`    | integer | Maximum content size in bytes    | 1024    | No       |
| `refresh_interval_ms` | integer | Refresh interval in milliseconds | 500     | No       |
| `include_images`      | boolean | Whether to include images        | false   | No       |
| `privacy`             | object  | Privacy settings                 | -       | No       |

The `privacy` object contains:

| Property              | Type    | Description                       | Default | Required |
| --------------------- | ------- | --------------------------------- | ------- | -------- |
| `mask_sensitive_data` | boolean | Whether to mask sensitive data    | true    | No       |
| `sensitive_patterns`  | array   | Regex patterns for sensitive data | -       | No       |

## Using the Schema

### Validation

You can use the schema to validate configuration files using various JSON Schema validators:

#### Command Line Validation with `ajv`

```bash
# Install ajv-cli
npm install -g ajv-cli

# Validate a configuration file
ajv validate -s config-schema.json -d config.json
```

#### Programmatic Validation with JavaScript

```javascript
const Ajv = require("ajv")
const ajv = new Ajv()
const schema = require("./config-schema.json")
const config = require("./config.json")

const validate = ajv.compile(schema)
const valid = validate(config)

if (!valid) {
    console.log(validate.errors)
}
```

#### Programmatic Validation with Python

```python
import json
import jsonschema

with open('config-schema.json') as f:
    schema = json.load(f)

with open('config.json') as f:
    config = json.load(f)

try:
    jsonschema.validate(config, schema)
    print("Validation successful")
except jsonschema.exceptions.ValidationError as e:
    print(f"Validation error: {e}")
```

### IDE Integration

Many IDEs and text editors support JSON Schema validation and autocompletion:

#### Visual Studio Code

1. Install the "JSON Schema" extension
2. Add a reference to the schema in your JSON file:

```json
{
  "$schema": "file:///path/to/config-schema.json",
  "version": "1.0.0",
  ...
}
```

Or configure workspace settings:

```json
{
    "json.schemas": [
        {
            "fileMatch": ["config.json", "**/actions/**/*.json"],
            "url": "./config-schema.json"
        }
    ]
}
```

#### JetBrains IDEs (IntelliJ, WebStorm, etc.)

1. Go to Settings > Languages & Frameworks > JSON Schema Mappings
2. Add a new schema mapping with the schema file and file patterns

### Generating Documentation

You can generate documentation from the schema using tools like:

-   [JSON Schema to Markdown](https://github.com/adobe/jsonschema2md)
-   [JSON Schema Viewer](https://github.com/networknt/json-schema-viewer)

Example:

```bash
# Install jsonschema2md
npm install -g @adobe/jsonschema2md

# Generate documentation
jsonschema2md -d config-schema.json -o docs/
```

### Generating UI Forms

You can generate UI forms from the schema using libraries like:

-   [React JSON Schema Form](https://github.com/rjsf-team/react-jsonschema-form)
-   [Angular JSON Schema Form](https://github.com/json-schema-form/angular-schema-form)
-   [Vue JSON Schema Form](https://github.com/koumoul-dev/vuetify-jsonschema-form)

## Examples

### Complete Configuration Example

```json
{
    "version": "1.0.0",
    "general": {
        "launcher_hotkey": "⌘ Space",
        "theme": "system",
        "language": "en",
        "startup_on_login": true,
        "check_updates": true,
        "telemetry_enabled": false
    },
    "launcher_bar": {
        "width": 800,
        "height": 600,
        "max_results": 15,
        "animation_speed": "normal",
        "font_size": 16,
        "opacity": 0.9,
        "position": "center",
        "blur_background": true
    },
    "actions": {
        "default_ai_provider": "openai",
        "default_ai_model": "gpt-4",
        "suggestion_algorithm": {
            "recency_weight": 0.3,
            "frequency_weight": 0.3,
            "relevance_weight": 0.4
        },
        "require_approval_for_dangerous_actions": true,
        "actions_directory": "~/.config/microlauncher/actions"
    },
    "context": {
        "enabled_providers": [
            "clipboard",
            "active_window",
            "selected_text",
            "current_directory",
            "current_application"
        ],
        "max_context_size": 8192
    },
    "performance": {
        "memory_limit_mb": 300,
        "background_process_priority": "low",
        "cache_size_mb": 100,
        "search_cache": {
            "enabled": true,
            "max_size": 200,
            "ttl_seconds": 600
        }
    },
    "security": {
        "encryption_enabled": true,
        "api_key_storage": "system_keychain"
    }
}
```

### Static Action Example

```json
{
    "id": "file_search",
    "name": "Find Files",
    "description": "Searches for files and directories on your system",
    "type": "tool",
    "triggers": ["find", "search", "locate"],
    "parameters": [
        {
            "name": "pattern",
            "type": "string",
            "required": true,
            "description": "The search term"
        },
        {
            "name": "directory",
            "type": "directory",
            "required": false,
            "default": "~",
            "description": "Directory to search in"
        },
        {
            "name": "max_results",
            "type": "number",
            "required": false,
            "default": 20,
            "description": "Maximum number of results to return"
        }
    ],
    "steps": [
        {
            "type": "command",
            "data": "find ${directory} -name \"*${pattern}*\" -type f -o -type d | head -n ${max_results}",
            "condition": ""
        }
    ],
    "metadata": {
        "usage_count": 0,
        "last_used": "",
        "success_rate": 1.0
    },
    "requires_user_approval": false,
    "icon": "icons/icon.svg"
}
```

### Dynamic Action Example

```json
{
    "id": "summarize_text",
    "name": "Summarize Text",
    "description": "Create a concise summary of the selected text",
    "type": "request",
    "triggers": ["summarize", "summary", "tldr"],
    "prompt_template": "Summarize the following text in a concise manner while preserving the key points and main message:\n\n{selected_text}",
    "preferred_provider": "openai",
    "preferred_model": "gpt-4",
    "required_tools": [],
    "required_context_types": ["selected_text"],
    "requires_selected_text": true,
    "request_options": {
        "temperature": 0.5,
        "max_tokens": 500,
        "tool_use_enabled": false,
        "streaming_enabled": true
    },
    "parameters": [],
    "metadata": {
        "usage_count": 0,
        "last_used": "",
        "success_rate": 1.0,
        "category": "writing",
        "tags": ["text", "summarization"]
    },
    "icon": "icons/icon.svg"
}
```

### AI Provider Example

```json
{
    "provider_id": "openai",
    "name": "OpenAI",
    "enabled": true,
    "api_key_variable": "OPENAI_API_KEY",
    "base_url": "https://api.openai.com/v1",
    "models": [
        {
            "id": "gpt-4",
            "name": "GPT-4",
            "display_name": "GPT-4",
            "supports_tool_calling": true,
            "supports_image_input": true,
            "supports_streaming": true,
            "max_tokens": 8192,
            "max_tool_calls": 15,
            "input_token_cost": 0.03,
            "output_token_cost": 0.06
        },
        {
            "id": "gpt-3.5-turbo",
            "name": "GPT-3.5 Turbo",
            "display_name": "GPT-3.5",
            "supports_tool_calling": true,
            "supports_image_input": false,
            "supports_streaming": true,
            "max_tokens": 4096,
            "max_tool_calls": 10,
            "input_token_cost": 0.0015,
            "output_token_cost": 0.002
        }
    ],
    "default_model": "gpt-3.5-turbo",
    "request_timeout_seconds": 30,
    "rate_limit_requests_per_minute": 60
}
```

## Best Practices

1. **Use Version Control**: Keep your configuration files in version control to track changes.

2. **Validate Before Deployment**: Always validate configuration files before deploying them.

3. **Use Default Values**: Rely on schema default values for optional properties when possible.

4. **Document Custom Actions**: Include detailed descriptions for custom actions to help users understand their purpose.

5. **Organize Actions by Category**: Group related actions in subdirectories for better organization.

6. **Limit Resource Usage**: Be mindful of performance settings, especially on resource-constrained devices.

7. **Secure API Keys**: Use the system keychain for storing API keys rather than embedding them in configuration files.

8. **Regular Backups**: Regularly back up your configuration files to prevent data loss.

9. **Test Configuration Changes**: Test configuration changes in a development environment before applying them to production.

10. **Keep Schema Updated**: Update the schema when adding new configuration options to maintain validation accuracy.

## Troubleshooting

### Common Validation Errors

1. **Missing Required Property**:

    ```
    Error: must have required property 'version'
    ```

    Solution: Add the missing property to your configuration file.

2. **Invalid Type**:

    ```
    Error: must be integer
    ```

    Solution: Ensure the property has the correct data type.

3. **Value Out of Range**:

    ```
    Error: must be <= 50
    ```

    Solution: Adjust the value to be within the specified range.

4. **Invalid Enum Value**:

    ```
    Error: must be equal to one of the allowed values
    ```

    Solution: Use one of the allowed values for the property.

5. **Invalid Pattern**:
    ```
    Error: must match pattern "^\\d+\\.\\d+\\.\\d+$"
    ```
    Solution: Ensure the string matches the required pattern.

### Fixing Configuration Issues

1. **Schema Validation**: Use a JSON Schema validator to identify and fix issues.

2. **Default Configuration**: Reset to the default configuration if you encounter persistent issues.

3. **Configuration Backup**: Restore from a backup if a configuration change causes problems.

4. **Incremental Changes**: Make small, incremental changes to configuration files to isolate issues.

5. **Check Logs**: Review application logs for error messages related to configuration loading.

If you encounter persistent issues with configuration validation, please refer to the application documentation or contact support for assistance.
