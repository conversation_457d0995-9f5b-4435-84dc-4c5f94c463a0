{"version": "1.0.0", "general": {"launcher_hotkey": "Cmd+Space", "theme": "system", "language": "en", "startup_on_login": true, "check_updates": true, "telemetry_enabled": false}, "launcher_bar": {"width": 700, "height": 550, "max_results": 12, "animation_speed": "normal", "font_size": 14, "opacity": 0.95, "position": "center", "blur_background": true}, "models": {"default_ai_provider": "openai", "default_ai_model": "gpt-4"}, "context": {"enabled_providers": ["clipboard", "active_window", "selected_text", "current_directory", "current_application"], "max_context_size": 4096}, "performance": {"memory_limit_mb": 200, "background_process_priority": "low", "cache_size_mb": 50, "search_cache": {"enabled": true, "max_size": 100, "ttl_seconds": 300}}, "security": {"encryption_enabled": true, "api_key_storage": "system_keychain"}, "ai_providers": {"openai": {"provider_id": "openai", "name": "OpenAI", "enabled": true, "api_key": "test-api-key", "api_key_variable": "OPENAI_API_KEY", "base_url": "https://api.openai.com/v1", "models": [{"id": "gpt-4", "name": "GPT-4", "display_name": "GPT-4", "supports_tool_calling": true, "supports_image_input": true, "supports_streaming": true, "max_tokens": 8192, "max_tool_calls": 15, "input_token_cost": 0.03, "output_token_cost": 0.06}, {"id": "gpt-3.5-turbo", "name": "GPT-3.5 Turbo", "display_name": "GPT-3.5", "supports_tool_calling": true, "supports_image_input": false, "supports_streaming": true, "max_tokens": 4096, "max_tool_calls": 10, "input_token_cost": 0.0015, "output_token_cost": 0.002}], "default_model": "gpt-3.5-turbo", "request_timeout_seconds": 30, "rate_limit_requests_per_minute": 60}, "anthropic": {"provider_id": "anthropic", "name": "Anthropic", "enabled": true, "api_key": "test-api-key", "api_key_variable": "ANTHROPIC_API_KEY", "base_url": "https://api.anthropic.com/v1", "models": [{"id": "claude-3-opus", "name": "Claude 3 Opus", "display_name": "Claude 3 Opus", "supports_tool_calling": true, "supports_image_input": true, "supports_streaming": true, "max_tokens": 32768, "max_tool_calls": 20, "input_token_cost": 0.015, "output_token_cost": 0.075}, {"id": "claude-3-sonnet", "name": "Claude 3 Sonnet", "display_name": "Claude 3 Sonnet", "supports_tool_calling": true, "supports_image_input": true, "supports_streaming": true, "max_tokens": 24576, "max_tool_calls": 15, "input_token_cost": 0.003, "output_token_cost": 0.015}], "default_model": "claude-3-sonnet", "request_timeout_seconds": 45, "rate_limit_requests_per_minute": 50}}, "context_providers": [{"provider_id": "clipboard", "name": "Clipboard", "enabled": true, "max_content_size": 2048, "refresh_interval_ms": 500, "include_images": false, "privacy": {"mask_sensitive_data": true, "sensitive_patterns": ["\\b(?:\\d[ -]*?){13,16}\\b", "\\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}\\b"]}}, {"provider_id": "active_window", "name": "Active Window", "enabled": true, "max_content_size": 1024, "refresh_interval_ms": 1000, "privacy": {"mask_sensitive_data": true, "sensitive_patterns": ["password", "secret", "token", "key"]}}, {"provider_id": "selected_text", "name": "Selected Text", "enabled": true, "max_content_size": 4096, "refresh_interval_ms": 300}, {"provider_id": "current_directory", "name": "Current Directory", "enabled": true, "max_content_size": 8192, "refresh_interval_ms": 2000, "include_hidden_files": false}, {"provider_id": "current_application", "name": "Current Application", "enabled": true, "refresh_interval_ms": 1000}], "platform_specific": {"macos": {"use_native_notifications": true, "status_bar_icon": true, "use_touchbar": true, "scan_applications": true}, "windows": {"use_native_notifications": true, "tray_icon": true, "scan_start_menu": true}, "linux": {"use_native_notifications": true, "tray_icon": true, "desktop_environment_integration": true}}}