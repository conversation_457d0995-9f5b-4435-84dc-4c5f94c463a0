#include <iostream>
#include <string>

#include "src/core/config/config_manager.h"

using namespace launcher::core;

void printConfigValue(const std::string& key, const std::string& value) {
    std::cout << key << " = " << value << std::endl;
}

void printConfigValue(const std::string& key, int value) {
    std::cout << key << " = " << value << std::endl;
}

void printConfigValue(const std::string& key, double value) {
    std::cout << key << " = " << value << std::endl;
}

void printConfigValue(const std::string& key, bool value) {
    std::cout << key << " = " << (value ? "true" : "false") << std::endl;
}

void printConfigArray(const std::string& key, const std::vector<std::string>& values) {
    std::cout << key << " = [";
    for (size_t i = 0; i < values.size(); ++i) {
        if (i > 0) {
            std::cout << ", ";
        }
        std::cout << "\"" << values[i] << "\"";
    }
    std::cout << "]" << std::endl;
}

int main() {
    // Initialize the configuration manager via direct construction
    ConfigManager config;

    // Try to load the configuration
    if (!config.initialize()) {
        std::cerr << "Failed to initialize configuration manager" << std::endl;
        return 1;
    }

    std::cout << "Configuration loaded from: " << config.getConfigPath() << std::endl;
    std::cout << std::endl;

    // Print some configuration values using the new dot notation
    std::cout << "=== General Settings ===" << std::endl;
    printConfigValue("general.launcher_hotkey", config.getLauncherHotkey());
    printConfigValue("general.theme", config.getTheme());
    printConfigValue("general.language", config.getLanguage());
    printConfigValue("general.startup_on_login", config.getStartupOnLogin());
    printConfigValue("general.check_updates", config.getBool("general.check_updates"));
    printConfigValue("general.telemetry_enabled", config.getBool("general.telemetry_enabled"));
    std::cout << std::endl;

    std::cout << "=== Launcher Bar Settings ===" << std::endl;
    printConfigValue("launcher_bar.width", config.getLauncherBarWidth());
    printConfigValue("launcher_bar.height", config.getInt("launcher_bar.height"));
    printConfigValue("launcher_bar.max_results", config.getMaxResults());
    printConfigValue("launcher_bar.animation_speed",
                     config.getString("launcher_bar.animation_speed"));
    printConfigValue("launcher_bar.font_size", config.getInt("launcher_bar.font_size"));
    printConfigValue("launcher_bar.opacity", config.getDouble("launcher_bar.opacity"));
    printConfigValue("launcher_bar.position", config.getString("launcher_bar.position"));
    printConfigValue("launcher_bar.blur_background",
                     config.getBool("launcher_bar.blur_background"));
    std::cout << std::endl;

    std::cout << "=== Models Settings ===" << std::endl;
    printConfigValue("models.default_ai_provider", config.getDefaultAIProvider());
    printConfigValue("models.default_ai_model", config.getDefaultAIModel());
    std::cout << std::endl;

    std::cout << "=== Context Settings ===" << std::endl;
    printConfigArray("context.enabled_providers", config.getEnabledContextProviders());
    printConfigValue("context.max_context_size", config.getInt("context.max_context_size"));
    std::cout << std::endl;

    std::cout << "=== Performance Settings ===" << std::endl;
    printConfigValue("performance.memory_limit_mb", config.getInt("performance.memory_limit_mb"));
    printConfigValue("performance.background_process_priority",
                     config.getString("performance.background_process_priority"));
    printConfigValue("performance.cache_size_mb", config.getInt("performance.cache_size_mb"));
    printConfigValue("performance.search_cache.enabled",
                     config.getBool("performance.search_cache.enabled"));
    printConfigValue("performance.search_cache.max_size",
                     config.getInt("performance.search_cache.max_size"));
    printConfigValue("performance.search_cache.ttl_seconds",
                     config.getInt("performance.search_cache.ttl_seconds"));
    std::cout << std::endl;

    std::cout << "=== Security Settings ===" << std::endl;
    printConfigValue("security.encryption_enabled", config.getBool("security.encryption_enabled"));
    printConfigValue("security.api_key_storage", config.getString("security.api_key_storage"));
    std::cout << std::endl;

    // Test modifying a value
    std::cout << "=== Modifying a Value ===" << std::endl;
    std::string originalTheme = config.getTheme();
    std::string newTheme = (originalTheme == "light") ? "dark" : "light";

    std::cout << "Changing theme from '" << originalTheme << "' to '" << newTheme << "'"
              << std::endl;
    config.setString("general.theme", newTheme);
    std::cout << "New theme: " << config.getTheme() << std::endl;

    // Save the configuration
    if (config.save()) {
        std::cout << "Configuration saved successfully" << std::endl;
    } else {
        std::cerr << "Failed to save configuration" << std::endl;
    }

    // Restore the original theme
    config.setString("general.theme", originalTheme);
    config.save();

    return 0;
}