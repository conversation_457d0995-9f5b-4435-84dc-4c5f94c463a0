{"$schema": "http://json-schema.org/draft-07/schema#", "title": "Micro Launcher Configuration Schema", "description": "Schema for Micro Launcher configuration files", "type": "object", "required": ["version", "general", "launcher_bar", "models", "context", "performance", "security"], "properties": {"version": {"type": "string", "description": "Version of the configuration format", "pattern": "^\\d+\\.\\d+\\.\\d+$"}, "general": {"type": "object", "description": "General application settings", "required": ["launcher_hotkey", "theme", "language", "startup_on_login"], "properties": {"launcher_hotkey": {"type": "string", "description": "Keyboard shortcut to activate the launcher", "examples": ["Alt+Space", "⌘ Space", "Ctrl+Space"]}, "theme": {"type": "string", "description": "UI theme setting", "enum": ["system", "light", "dark"], "default": "system"}, "language": {"type": "string", "description": "Application language", "pattern": "^[a-z]{2}(-[A-Z]{2})?$", "default": "en"}, "startup_on_login": {"type": "boolean", "description": "Whether to launch the application at login", "default": true}, "check_updates": {"type": "boolean", "description": "Whether to check for updates automatically", "default": true}, "telemetry_enabled": {"type": "boolean", "description": "Whether to send anonymous usage data", "default": false}}}, "launcher_bar": {"type": "object", "description": "LauncherBar appearance and behavior settings", "required": ["width", "max_results"], "properties": {"width": {"type": "integer", "description": "Width of the launcher window in pixels", "minimum": 300, "maximum": 2000, "default": 600}, "height": {"type": "integer", "description": "Height of the launcher window in pixels", "minimum": 200, "maximum": 1500, "default": 600}, "max_results": {"type": "integer", "description": "Maximum number of results to display", "minimum": 5, "maximum": 50, "default": 10}, "animation_speed": {"type": "string", "description": "Speed of UI animations", "enum": ["none", "slow", "normal", "fast"], "default": "normal"}, "font_size": {"type": "integer", "description": "Font size for the launcher UI", "minimum": 8, "maximum": 32, "default": 14}, "opacity": {"type": "number", "description": "Opacity of the launcher window", "minimum": 0.1, "maximum": 1.0, "default": 0.95}, "position": {"type": "string", "description": "Position of the launcher window on the screen", "enum": ["center", "top", "bottom"], "default": "center"}, "blur_background": {"type": "boolean", "description": "Whether to use a blurred background effect", "default": true}}}, "models": {"type": "object", "description": "LLM management and preferences", "required": ["default_ai_provider", "default_ai_model"], "properties": {"default_ai_provider": {"type": "string", "description": "Default AI provider", "examples": ["openai", "anthropic", "google"]}, "default_ai_model": {"type": "string", "description": "Default AI model", "examples": ["gpt-4", "gpt-3.5-turbo", "claude-2"]}}}, "context": {"type": "object", "description": "Context provider settings", "required": ["enabled_providers", "max_context_size"], "properties": {"enabled_providers": {"type": "array", "description": "List of enabled context providers", "items": {"type": "string", "enum": ["clipboard", "active_window", "selected_text", "open_file", "current_directory", "current_application", "time_location"]}, "default": ["clipboard", "active_window", "selected_text", "current_directory"]}, "max_context_size": {"type": "integer", "description": "Maximum size of context data in bytes", "minimum": 1024, "maximum": 16384, "default": 4096}}}, "performance": {"type": "object", "description": "Performance-related settings", "required": ["memory_limit_mb", "background_process_priority", "cache_size_mb", "search_cache"], "properties": {"memory_limit_mb": {"type": "integer", "description": "Maximum memory usage in megabytes", "minimum": 50, "maximum": 1000, "default": 200}, "background_process_priority": {"type": "string", "description": "Priority for background processes", "enum": ["low", "normal", "high"], "default": "low"}, "cache_size_mb": {"type": "integer", "description": "Maximum cache size in megabytes", "minimum": 10, "maximum": 500, "default": 50}, "search_cache": {"type": "object", "description": "Search cache settings", "required": ["enabled", "max_size", "ttl_seconds"], "properties": {"enabled": {"type": "boolean", "description": "Whether to enable the search cache", "default": true}, "max_size": {"type": "integer", "description": "Maximum number of search queries to cache", "minimum": 10, "maximum": 1000, "default": 100}, "ttl_seconds": {"type": "integer", "description": "Time-to-live for cached results in seconds", "minimum": 30, "maximum": 3600, "default": 300}}}}}, "security": {"type": "object", "description": "Security-related settings", "required": ["encryption_enabled", "api_key_storage"], "properties": {"encryption_enabled": {"type": "boolean", "description": "Whether to encrypt sensitive data", "default": true}, "api_key_storage": {"type": "string", "description": "Storage method for API keys", "enum": ["system_keychain", "environment_variables", "config_file"], "default": "system_keychain"}}}}, "definitions": {"tool_parameter": {"type": "object", "required": ["name", "type", "description"], "properties": {"name": {"type": "string", "description": "Parameter name"}, "type": {"type": "string", "description": "Parameter data type", "enum": ["string", "number", "boolean", "file", "directory", "object", "array"]}, "required": {"type": "boolean", "description": "Whether the parameter is required", "default": true}, "description": {"type": "string", "description": "Description of the parameter"}, "default": {"description": "Default value for the parameter"}}}, "execution_step": {"type": "object", "required": ["type", "data"], "properties": {"type": {"type": "string", "description": "Type of execution step", "enum": ["command", "script", "api", "ui"]}, "data": {"description": "Data for the execution step", "oneOf": [{"type": "string"}, {"type": "object"}]}, "script_type": {"type": "string", "description": "Type of script (for script steps)", "enum": ["shell", "applescript", "powershell", "python"]}, "condition": {"type": "string", "description": "Condition for executing this step", "default": ""}}}, "static_action": {"type": "object", "required": ["id", "name", "description", "type", "triggers", "parameters", "steps", "metadata"], "properties": {"id": {"type": "string", "description": "Unique identifier for the action"}, "name": {"type": "string", "description": "Human-readable name for display in UI"}, "description": {"type": "string", "description": "Detailed description of what the action does"}, "type": {"type": "string", "description": "Type of action", "enum": ["tool"]}, "triggers": {"type": "array", "description": "Keywords that can trigger this action", "items": {"type": "string"}, "minItems": 1}, "parameters": {"type": "array", "description": "Parameters required by this tool", "items": {"$ref": "#/definitions/tool_parameter"}}, "steps": {"type": "array", "description": "Sequence of steps to execute", "items": {"$ref": "#/definitions/execution_step"}, "minItems": 1}, "metadata": {"type": "object", "description": "Usage statistics and other metadata", "properties": {"usage_count": {"type": "integer", "description": "Number of times this action has been used", "default": 0}, "last_used": {"type": "string", "description": "When this action was last used (ISO 8601 format)", "default": ""}, "success_rate": {"type": "number", "description": "Ratio of successful executions", "minimum": 0, "maximum": 1, "default": 1.0}}}, "requires_user_approval": {"type": "boolean", "description": "Whether this tool requires explicit user approval", "default": false}, "icon": {"type": "string", "description": "Path to the icon file relative to the action directory", "default": "icons/icon.svg"}}}, "dynamic_action": {"type": "object", "required": ["id", "name", "description", "type", "triggers", "prompt_template", "preferred_provider", "preferred_model", "parameters", "metadata"], "properties": {"id": {"type": "string", "description": "Unique identifier for the action"}, "name": {"type": "string", "description": "Human-readable name for display in UI"}, "description": {"type": "string", "description": "Detailed description of what the action does"}, "type": {"type": "string", "description": "Type of action", "enum": ["request"]}, "triggers": {"type": "array", "description": "Keywords that can trigger this action", "items": {"type": "string"}, "minItems": 1}, "prompt_template": {"type": "string", "description": "Template for formatting the request to AI"}, "preferred_provider": {"type": "string", "description": "Preferred AI provider for this request"}, "preferred_model": {"type": "string", "description": "Preferred AI model for this request"}, "required_tools": {"type": "array", "description": "Tools that this request might need to use", "items": {"type": "string"}}, "required_context_types": {"type": "array", "description": "Context types required by this request", "items": {"type": "string", "enum": ["selected_text", "clipboard", "current_app", "current_url", "current_file", "current_directory", "current_time", "user_input"]}}, "requires_selected_text": {"type": "boolean", "description": "Whether this request operates on selected text", "default": false}, "request_options": {"type": "object", "description": "Default options for AI request", "properties": {"temperature": {"type": "number", "description": "Temperature for AI generation (0.0-2.0)", "minimum": 0.0, "maximum": 2.0, "default": 0.7}, "max_tokens": {"type": "integer", "description": "Maximum tokens to generate", "minimum": 1, "maximum": 16384, "default": 1000}, "tool_use_enabled": {"type": "boolean", "description": "Whether to allow the AI to use tools", "default": false}, "streaming_enabled": {"type": "boolean", "description": "Whether to stream the response", "default": true}}}, "parameters": {"type": "array", "description": "Parameters required by this request", "items": {"$ref": "#/definitions/tool_parameter"}}, "metadata": {"type": "object", "description": "Usage statistics and other metadata", "properties": {"usage_count": {"type": "integer", "description": "Number of times this action has been used", "default": 0}, "last_used": {"type": "string", "description": "When this action was last used (ISO 8601 format)", "default": ""}, "success_rate": {"type": "number", "description": "Ratio of successful executions", "minimum": 0, "maximum": 1, "default": 1.0}, "category": {"type": "string", "description": "Category of the request", "enum": ["writing", "tone_style", "content_generation", "code", "data", "general"], "default": "general"}, "tags": {"type": "array", "description": "Tags for categorizing and searching requests", "items": {"type": "string"}, "default": []}}}, "icon": {"type": "string", "description": "Path to the icon file relative to the action directory", "default": "icons/icon.svg"}}}, "ai_provider": {"type": "object", "required": ["provider_id", "name", "enabled", "api_key_variable", "base_url", "models", "default_model"], "properties": {"provider_id": {"type": "string", "description": "Unique identifier for the provider"}, "name": {"type": "string", "description": "Human-readable name for the provider"}, "enabled": {"type": "boolean", "description": "Whether this provider is enabled", "default": true}, "api_key_variable": {"type": "string", "description": "Environment variable name for the API key"}, "base_url": {"type": "string", "description": "Base URL for API requests", "format": "uri"}, "models": {"type": "array", "description": "Available models from this provider", "items": {"type": "object", "required": ["id", "name", "display_name"], "properties": {"id": {"type": "string", "description": "Unique identifier for the model"}, "name": {"type": "string", "description": "Technical name of the model"}, "display_name": {"type": "string", "description": "User-friendly name for display"}, "supports_tool_calling": {"type": "boolean", "description": "Whether this model can use tools", "default": false}, "supports_image_input": {"type": "boolean", "description": "Whether this model can process images", "default": false}, "supports_streaming": {"type": "boolean", "description": "Whether this model supports streaming responses", "default": false}, "max_tokens": {"type": "integer", "description": "Maximum tokens this model can process", "minimum": 1}, "max_tool_calls": {"type": "integer", "description": "Maximum number of tool calls per request", "minimum": 0}, "input_token_cost": {"type": "number", "description": "Cost per 1K input tokens", "minimum": 0}, "output_token_cost": {"type": "number", "description": "Cost per 1K output tokens", "minimum": 0}}}, "minItems": 1}, "default_model": {"type": "string", "description": "Default model to use for this provider"}, "request_timeout_seconds": {"type": "integer", "description": "Timeout for API requests in seconds", "minimum": 1, "maximum": 300, "default": 30}, "rate_limit_requests_per_minute": {"type": "integer", "description": "Maximum number of requests per minute", "minimum": 1, "default": 60}}}, "context_provider": {"type": "object", "required": ["provider_id", "name", "enabled"], "properties": {"provider_id": {"type": "string", "description": "Unique identifier for the provider"}, "name": {"type": "string", "description": "Human-readable name for the provider"}, "enabled": {"type": "boolean", "description": "Whether this provider is enabled", "default": true}, "max_content_size": {"type": "integer", "description": "Maximum size of content in bytes", "minimum": 1, "default": 1024}, "refresh_interval_ms": {"type": "integer", "description": "Interval between context refreshes in milliseconds", "minimum": 100, "default": 500}, "include_images": {"type": "boolean", "description": "Whether to include images in the context", "default": false}, "privacy": {"type": "object", "description": "Privacy settings for this provider", "properties": {"mask_sensitive_data": {"type": "boolean", "description": "Whether to mask sensitive data", "default": true}, "sensitive_patterns": {"type": "array", "description": "Regular expressions for sensitive data patterns", "items": {"type": "string", "format": "regex"}}}}}}}}