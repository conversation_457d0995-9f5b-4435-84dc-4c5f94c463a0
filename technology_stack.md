# MicroLauncher Technology Stack

-   **Languages**: C++20/23 (core), Objective-C++ (UI). No Swift/SwiftUI.
-   **Build**: CMake 3.25+, Ninja, clang++ ≥ 17.
-   **Testing**: GoogleTest + Catch2 for behavioural specs.
-   **Benchmarks**: Google Benchmark suite (`make perf` or shortcut `make bench`).
-   **Logging**: Custom macros in `src/core/util/debug.h` (DBG, ERR, DBM, ERM).
-   **Memory Management**: Custom pool/arena (see `src/core/utils`). Smart pointers elsewhere.
-   **Concurrency**: `std::jthread`, Grand Central Dispatch for UI bridging.
-   **Docs**: Markdown in `docs/`, security profiles in `docs/security/`.
-   **Packaging**: `.app` via CMake `MACOSX_BUNDLE` + `scripts/package_app.sh`.
-   **AI models**: Local embedding; OpenAI, Anthropic via `src/core/llm/`.

Keep this list current when adopting new libs or compilers.
