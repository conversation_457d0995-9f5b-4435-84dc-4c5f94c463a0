cmake_minimum_required(VERSION 3.25)
project(launcher VERSION 1.0.0 LANGUAGES CXX C OBJCXX)

# -----------------------------------------------------------------------------
# Embed current Git commit (first 20 chars) into build via KAI_BUILD_SHA macro
# so FlatSnapshot can record provenance in its header.  Fallback to "unknown"
# when not building from a git repository (e.g. release tarball).
# -----------------------------------------------------------------------------
execute_process(
    COMMAND git rev-parse --short=20 HEAD
    WORKING_DIRECTORY ${CMAKE_SOURCE_DIR}
    RESULT_VARIABLE _git_status
    OUTPUT_VARIABLE _git_sha
    OUTPUT_STRIP_TRAILING_WHITESPACE)

if(_git_status EQUAL 0 AND NOT _git_sha STREQUAL "")
    add_compile_definitions(KAI_BUILD_SHA="${_git_sha}")
    message(STATUS "KAI_BUILD_SHA set to ${_git_sha}")
else()
    add_compile_definitions(KAI_BUILD_SHA="unknown")
    message(WARNING "git not available – KAI_BUILD_SHA set to 'unknown'")
endif()

# -----------------------------------------------------------------------------
# Enable CTest testing framework at the very beginning so that any subprojects
# adding tests via add_test() are properly registered regardless of call order.
enable_testing()

# Set C++ standard
set(CMAKE_CXX_STANDARD 23)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS OFF)

# Set Objective-C++ flags
set(CMAKE_OBJCXX_STANDARD 23)
set(CMAKE_OBJCXX_STANDARD_REQUIRED ON)

# Enable ARC (Automatic Reference Counting) and completely suppress warnings
set(CMAKE_OBJCXX_FLAGS "${CMAKE_OBJCXX_FLAGS} -fobjc-arc -w")
set(CMAKE_OBJC_FLAGS "${CMAKE_OBJC_FLAGS} -fobjc-arc -w")

# Selectively disable ARC for specific files
if(APPLE)
  set_source_files_properties(
    src/core/http/http_client.mm
    src/core/http/http_streaming_delegate.mm
    PROPERTIES COMPILE_FLAGS "-fno-objc-arc -w"
  )
endif()

# Suppress all warnings for C/C++ code
set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -w")
set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -w")

# Set optimization flags for Release build
set(CMAKE_CXX_FLAGS_RELEASE "${CMAKE_CXX_FLAGS_RELEASE} -O3")
set(CMAKE_OBJCXX_FLAGS_RELEASE "${CMAKE_OBJCXX_FLAGS_RELEASE} -O3")

# Add CMAKE_MODULE_PATH to find custom modules
list(APPEND CMAKE_MODULE_PATH "${CMAKE_CURRENT_SOURCE_DIR}/cmake")

# -----------------------------------------------------------------------------
# CMake modules & service header generation must be available before any
# subdirectory that includes <service_id.h> is processed.  Inject
# CodeGenServices here immediately after we extend CMAKE_MODULE_PATH.
# -----------------------------------------------------------------------------

include(CodeGenServices)

# Set CMake policies
cmake_policy(SET CMP0076 NEW) # target_sources() command converts relative paths to absolute
cmake_policy(SET CMP0079 NEW) # target_link_libraries allows to link targets which were not yet defined

# Disable all warnings instead of enabling them
if(MSVC)
    # Use /w to disable all warnings on MSVC
    add_compile_options(/w)
else()
    # Use -w to disable all warnings on GCC/Clang
    add_compile_options(-w)
endif()

# Set output directories
set(CMAKE_ARCHIVE_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib)
set(CMAKE_LIBRARY_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib)
set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin)

# Include FetchContent for dependency management
include(FetchContent)

# -----------------------------------------------------------------------------
# All third-party sources will be downloaded/cloned into ${PROJECT_ROOT}/third_party
# to keep the build tree deterministic and IDE-friendly.
# -----------------------------------------------------------------------------
set(FETCHCONTENT_BASE_DIR "${CMAKE_SOURCE_DIR}/third_party" CACHE PATH "Base directory for FetchContent")
file(MAKE_DIRECTORY ${FETCHCONTENT_BASE_DIR})

# --- Sanitizer toggles -------------------------------------------------------
option(ENABLE_ASAN "Build with AddressSanitizer" OFF)
option(ENABLE_TSAN "Build with ThreadSanitizer" OFF)

if(ENABLE_ASAN AND ENABLE_TSAN)
    message(FATAL_ERROR "ENABLE_ASAN and ENABLE_TSAN are mutually exclusive")
endif()

if(ENABLE_ASAN)
    message(STATUS "AddressSanitizer enabled")
    add_compile_options(-fsanitize=address -fno-omit-frame-pointer)
    add_compile_options($<$<COMPILE_LANGUAGE:OBJCXX>:-fsanitize=address;-fno-omit-frame-pointer>)
    add_link_options(-fsanitize=address)
endif()

if(ENABLE_TSAN)
    message(STATUS "ThreadSanitizer enabled")
    add_compile_options(-fsanitize=thread -fno-omit-frame-pointer)
    add_compile_options($<$<COMPILE_LANGUAGE:OBJCXX>:-fsanitize=thread;-fno-omit-frame-pointer>)
    add_link_options(-fsanitize=thread)
endif()

# END Sanitizer toggles -------------------------------------------------------

# BEGIN KaiOptions inclusion ---------------------------------------------------
include(KaiOptions)

# When FlatSnapshot signing is enabled, pull in libsodium for Ed25519.
if(KAI_ENABLE_FLATSNAPSHOT_SIGN)
    FetchContent_Declare(
        sodium
        GIT_REPOSITORY https://github.com/jedisct1/libsodium.git
        GIT_TAG 1.0.19
    )
    set(ENABLE_MINIMAL OFF CACHE BOOL "Build minimal libsodium" FORCE)
    FetchContent_MakeAvailable(sodium)
    add_compile_definitions(KAI_USE_LIBSODIUM)
    include_directories(${sodium_SOURCE_DIR}/src/libsodium/include)
    # Generated headers like version.h live in the binary tree; expose that too so they are visible during compilation.
    include_directories(${sodium_BINARY_DIR}/include)

    # Create interface target so consumers can depend without repeating.
    add_library(kai_crypto INTERFACE)
    # Libsodium exports target `libsodium`; provide alias `sodium` for legacy
    # but link directly to libsodium to ensure availability.
    if(TARGET libsodium)
        add_library(sodium ALIAS libsodium)
        target_link_libraries(kai_crypto INTERFACE libsodium)
        target_link_directories(kai_crypto INTERFACE $<TARGET_FILE_DIR:libsodium>)

        # Discover system include directory for libsodium headers (sodium.h, version.h)
        find_path(SODIUM_INCLUDE_DIR sodium.h
            HINTS /opt/homebrew/include /usr/local/include /usr/include
        )
        if(NOT SODIUM_INCLUDE_DIR)
            message(FATAL_ERROR "libsodium headers not found. Install via Homebrew (brew install libsodium) or ensure headers are discoverable.")
        endif()
        target_include_directories(kai_crypto INTERFACE ${SODIUM_INCLUDE_DIR})
    else()
        # Fallback: search system libsodium
        find_library(SODIUM_LIB sodium HINTS /opt/homebrew/lib /usr/local/lib)
        if(NOT SODIUM_LIB)
            message(FATAL_ERROR "libsodium library not found (neither built nor system). Install via Homebrew or ensure FetchContent build succeeds.")
        endif()
        target_link_libraries(kai_crypto INTERFACE ${SODIUM_LIB})

        # Discover system include directory for libsodium headers (sodium.h, version.h)
        find_path(SODIUM_INCLUDE_DIR sodium.h
            HINTS /opt/homebrew/include /usr/local/include /usr/include
        )
        if(NOT SODIUM_INCLUDE_DIR)
            message(FATAL_ERROR "libsodium headers not found. Install via Homebrew (brew install libsodium) or ensure headers are discoverable.")
        endif()
        target_include_directories(kai_crypto INTERFACE ${SODIUM_INCLUDE_DIR})
    endif()
    # When libsodium was built from source, also propagate its generated headers.
    if(EXISTS "${sodium_BINARY_DIR}/include")
        target_include_directories(kai_crypto INTERFACE ${sodium_BINARY_DIR}/include)
    endif()
    target_include_directories(kai_crypto INTERFACE ${sodium_SOURCE_DIR}/src/libsodium/include)

    # Ensure the linker can locate the freshly built libsodium static library
    # by adding its binary directory to the search paths for all dependents.
    target_link_directories(kai_crypto INTERFACE ${sodium_BINARY_DIR})
endif()

# ----------------------------------------------------------------------------
# Thin-LTO build toggle – activates Clang Thin Link-Time Optimisation when
# `-DKAI_ENABLE_THINLTO=ON` is passed at configure time.  Sanitiser builds are
# explicitly disallowed because the resulting binary size and link time
# explode and provide little value inside CI.
# ----------------------------------------------------------------------------
if(KAI_ENABLE_THINLTO)
    if(ENABLE_ASAN OR ENABLE_TSAN)
        message(FATAL_ERROR "KAI_ENABLE_THINLTO cannot be combined with sanitizers (ENABLE_ASAN or ENABLE_TSAN).")
    endif()

    if(CMAKE_CXX_COMPILER_ID MATCHES "Clang" OR CMAKE_CXX_COMPILER_ID MATCHES "AppleClang")
        add_compile_options(-flto=thin)
        add_link_options(-flto=thin)
        message(STATUS "Thin-LTO enabled (-flto=thin)")
    else()
        message(WARNING "KAI_ENABLE_THINLTO is ON but the compiler is not Clang; Thin-LTO flag not applied.")
    endif()
endif()

# -----------------------------------------------------------------------------
# ProbeCache backend: LMDB optional toggle
# -----------------------------------------------------------------------------
option(KAI_USE_LMDB_PROBECACHE "Enable LMDB backend for ProbeCache" OFF)

if(KAI_USE_LMDB_PROBECACHE)
    add_compile_definitions(KAI_USE_LMDB_PROBECACHE)

    # Fetch lightning-fast LMDB key-value store (single C source, ~80 KB)
    FetchContent_Declare(
        lmdb
        GIT_REPOSITORY https://github.com/LMDB/lmdb.git
        GIT_TAG LMDB_0.9.31
    )
    FetchContent_MakeAvailable(lmdb)

    # Build static LMDB library (two C files)
    add_library(lmdb STATIC
        ${lmdb_SOURCE_DIR}/libraries/liblmdb/mdb.c
        ${lmdb_SOURCE_DIR}/libraries/liblmdb/midl.c
    )
    target_include_directories(lmdb PUBLIC ${lmdb_SOURCE_DIR}/libraries/liblmdb)
    set_target_properties(lmdb PROPERTIES POSITION_INDEPENDENT_CODE ON)
endif()

# Fetch nlohmann/json
FetchContent_Declare(
  json
  GIT_REPOSITORY https://github.com/nlohmann/json.git
  GIT_TAG v3.11.2
)
FetchContent_MakeAvailable(json)

# Fetch fmt (header-only) for ultra-low-latency logging
FetchContent_Declare(
  fmt
  GIT_REPOSITORY https://github.com/fmtlib/fmt.git
  GIT_TAG 10.2.1
)
FetchContent_MakeAvailable(fmt)
include_directories(${fmt_SOURCE_DIR}/include)

# Disable RapidJSON upstream tests/perf to avoid CTest stray entries
set(RAPIDJSON_BUILD_TESTS OFF CACHE BOOL "Disable RapidJSON tests" FORCE)
set(RAPIDJSON_BUILD_PERF OFF CACHE BOOL "Disable RapidJSON performance benchmarks" FORCE)
set(RAPIDJSON_BUILD_DOC OFF CACHE BOOL "Disable RapidJSON documentation" FORCE)

# -----------------------------------------------------------------------------
# JSON parser backend toggle (RapidJSON writer is always used)
#   KAI_JSON_PARSER = simdjson | rapidjson  (default simdjson)
# -----------------------------------------------------------------------------

set(KAI_JSON_PARSER "simdjson" CACHE STRING "JSON parser backend (simdjson|rapidjson)")

if(NOT KAI_JSON_PARSER STREQUAL "simdjson" AND NOT KAI_JSON_PARSER STREQUAL "rapidjson")
    message(FATAL_ERROR "Invalid value for KAI_JSON_PARSER: ${KAI_JSON_PARSER}. Use 'simdjson' or 'rapidjson'.")
endif()

if(KAI_JSON_PARSER STREQUAL "simdjson")
    add_compile_definitions(KAI_JSON_PARSER_SIMDJSON)

    # Fetch simdjson for high-performance on-demand parsing
    FetchContent_Declare(
      simdjson
      GIT_REPOSITORY https://github.com/simdjson/simdjson.git
      GIT_TAG v3.13.0
      GIT_SHALLOW TRUE
    )
    FetchContent_MakeAvailable(simdjson)
    include_directories(${simdjson_SOURCE_DIR}/include)

else()
    add_compile_definitions(KAI_JSON_PARSER_RAPIDJSON)
endif()

# Fetch RapidJSON early so include dirs are available to subprojects
FetchContent_Declare(
  rapidjson
  GIT_REPOSITORY https://github.com/Tencent/rapidjson.git
  GIT_TAG master
)
FetchContent_MakeAvailable(rapidjson)

# -----------------------------------------------------------------------------
# Fetch yyjson – ultra-fast C JSON parser/serializer (used in micro-benchmarks)
# Pinned to an immutable release tag for deterministic builds.
# -----------------------------------------------------------------------------
FetchContent_Declare(
  yyjson
  GIT_REPOSITORY https://github.com/ibireme/yyjson.git
  GIT_TAG 0.9.0
  GIT_SHALLOW TRUE
)
FetchContent_MakeAvailable(yyjson)
# Expose include directory so consumers can `#include <yyjson.h>`
include_directories(${yyjson_SOURCE_DIR}/src)

# Fetch concurrentqueue
FetchContent_Declare(
  concurrentqueue
  GIT_REPOSITORY https://github.com/cameron314/concurrentqueue.git
  GIT_TAG master
)
FetchContent_MakeAvailable(concurrentqueue)
include_directories(${concurrentqueue_SOURCE_DIR})

# -----------------------------------------------------------------------------
# Abseil (header-only) – must be fetched early so its include directory is
# available when core targets set PUBLIC include paths that rely on
# ${absl_SOURCE_DIR}.  Placing this block here guarantees the variable is
# defined before any subdirectories are processed.
# Silence Abseil CMake warning about ABSL_PROPAGATE_CXX_STD by explicitly opting-in
set(ABSL_PROPAGATE_CXX_STD ON CACHE BOOL "Propagate C++ standard to Abseil" FORCE)
# Temporarily disable clang-tidy while configuring Abseil (3rd-party code)
# to avoid upstream warnings promoted to errors.
if(ENABLE_CLANG_TIDY AND CMAKE_CXX_CLANG_TIDY)
  set(_OLD_CLANG_TIDY ${CMAKE_CXX_CLANG_TIDY})
  set(CMAKE_CXX_CLANG_TIDY "")
endif()

FetchContent_Declare(
  abseil
  GIT_REPOSITORY https://github.com/abseil/abseil-cpp.git
  GIT_TAG 20240116.0
)
FetchContent_MakeAvailable(abseil)

if(ENABLE_CLANG_TIDY AND _OLD_CLANG_TIDY)
  set(CMAKE_CXX_CLANG_TIDY ${_OLD_CLANG_TIDY})
endif()

include_directories(${absl_SOURCE_DIR})

# -----------------------------------------------------------------------------
# EventBus capacity option (0 = unbounded)
# -----------------------------------------------------------------------------
set(KAI_EVENTBUS_CAPACITY "0" CACHE STRING "Maximum number of pending EventBus tasks (0=unbounded)")

if("${KAI_EVENTBUS_CAPACITY}" STREQUAL "OFF")
  set(KAI_EVENTBUS_CAPACITY 0)
endif()

add_compile_definitions(KAI_EVENTBUS_CAPACITY=${KAI_EVENTBUS_CAPACITY})

# -----------------------------------------------------------------------------
# Executor capacity option (0 = unbounded)
# -----------------------------------------------------------------------------
set(KAI_EXECUTOR_CAPACITY "0" CACHE STRING "Maximum number of pending Executor tasks (0=unbounded)")

if("${KAI_EXECUTOR_CAPACITY}" STREQUAL "OFF")
  set(KAI_EXECUTOR_CAPACITY 0)
endif()

add_compile_definitions(KAI_EXECUTOR_CAPACITY=${KAI_EXECUTOR_CAPACITY})

# -----------------------------------------------------------------------------
# Memory allocator backend – choose rpmalloc (default) or mimalloc via option
# -----------------------------------------------------------------------------

# Allow caller to override allocator via -DKAI_ALLOCATOR=… (rpmalloc|mimalloc)
set(KAI_ALLOCATOR "rpmalloc" CACHE STRING "Allocator backend (rpmalloc|mimalloc)")

# Convert to string cache variable to allow custom values

if(KAI_ALLOCATOR STREQUAL "mimalloc")
    add_compile_definitions(KAI_USE_MIMALLOC)
    FetchContent_Declare(
        mimalloc
        GIT_REPOSITORY https://github.com/microsoft/mimalloc.git
        GIT_TAG v2.1.2
    )
    FetchContent_MakeAvailable(mimalloc)
    # Upstream CMake exports target mimalloc::mimalloc (>=v2.1). Use that if present
    if(TARGET mimalloc::mimalloc)
        set(KAI_ALLOCATOR_LIB mimalloc::mimalloc)
    else()
        set(KAI_ALLOCATOR_LIB mimalloc)
    endif()
else()
    # default to rpmalloc
    add_compile_definitions(KAI_USE_RPMALLOC)

    # -------------------------------------------------------------------
    # Per-plugin heap toggle (rpmalloc first-class heaps)
    # -------------------------------------------------------------------
    option(KAI_PER_PLUGIN_HEAP "Enable separate rpmalloc heap per plugin" ON)

    if(KAI_PER_PLUGIN_HEAP)
        set(RPMALLOC_FIRST_CLASS_HEAPS ON CACHE BOOL "Enable rpmalloc per-heap API" FORCE)
        add_compile_definitions(RPMALLOC_FIRST_CLASS_HEAPS=1)
    else()
        set(RPMALLOC_FIRST_CLASS_HEAPS OFF CACHE BOOL "Disable rpmalloc per-heap API" FORCE)
    endif()

    # third_party directory already ensured by global FetchContent base dir

    FetchContent_Declare(
        rpmalloc
        GIT_REPOSITORY https://github.com/mjansson/rpmalloc.git
        GIT_TAG 1.4.5
        SOURCE_DIR ${FETCHCONTENT_BASE_DIR}/rpmalloc
    )
    # Build as static lib only
    set(RPMALLOC_BUILD_STATIC ON CACHE BOOL "" FORCE)
    FetchContent_MakeAvailable(rpmalloc)

    # rpmalloc does not ship a CMake target; build a minimal static lib from source
    add_library(rpmalloc STATIC
        ${rpmalloc_SOURCE_DIR}/rpmalloc/rpmalloc.c
    )
    target_include_directories(rpmalloc PUBLIC ${rpmalloc_SOURCE_DIR}/rpmalloc)
    # Ensure correct export semantics when building as static library
    target_compile_definitions(rpmalloc PUBLIC RPMALLOC_STATIC_DEFINE)
    # Ensure per-heap symbols are built into rpmalloc when enabled
    target_compile_definitions(rpmalloc PUBLIC RPMALLOC_FIRST_CLASS_HEAPS=1)
    # Build rpmalloc with statistics enabled so ZeroAllocGuard can inspect
    # per-thread allocation counters without relying on global fallbacks.
    target_compile_definitions(rpmalloc PUBLIC ENABLE_STATISTICS=1)
    set_target_properties(rpmalloc PROPERTIES POSITION_INDEPENDENT_CODE ON)

    # Expose as allocator link dependency
    set(KAI_ALLOCATOR_LIB rpmalloc)
endif()

include_directories(${rapidjson_SOURCE_DIR}/include)

# Configure cmark options before fetching
set(CMARK_SHARED OFF CACHE BOOL "Build shared libcmark library" FORCE)
set(CMARK_STATIC ON CACHE BOOL "Build static libcmark library" FORCE)
set(CMARK_TESTS OFF CACHE BOOL "Build cmark tests and enable testing" FORCE)

# Fetch cmark-gfm (GitHub Flavored Markdown) library
FetchContent_Declare(
  cmark
  GIT_REPOSITORY https://github.com/github/cmark-gfm.git
  GIT_TAG 0.29.0.gfm.13
)

# Configure cmark-gfm extensions
set(CMARK_GFM_EXTENSIONS ON CACHE BOOL "Build GitHub Flavored Markdown extensions" FORCE)
set(CMARK_GFM_STATIC ON CACHE BOOL "Build static libcmark-gfm-extensions library" FORCE)

FetchContent_MakeAvailable(cmark)

# Add src directory to include path for easier header access
include_directories(src)

# Find dependencies
find_package(Catch2 QUIET)

# Add platform-specific settings
if(APPLE)
    # macOS-specific settings
    find_library(FOUNDATION_LIBRARY Foundation REQUIRED)
    find_library(APPKIT_LIBRARY AppKit REQUIRED)
    find_library(COCOA_LIBRARY Cocoa REQUIRED)
    find_library(JAVASCRIPTCORE_LIBRARY JavaScriptCore REQUIRED)
    
    set(PLATFORM_LIBS ${FOUNDATION_LIBRARY} ${APPKIT_LIBRARY} ${COCOA_LIBRARY} ${JAVASCRIPTCORE_LIBRARY})
endif()

# -----------------------------------------------------------------------------
# Plugin allocator shim – generated header + interface target so every plugin
# automatically sees operator new/delete overrides bound to Kai allocator.
# -----------------------------------------------------------------------------
configure_file(${CMAKE_CURRENT_SOURCE_DIR}/cmake/kai_plugin_alloc_shim.h.in
               ${CMAKE_BINARY_DIR}/generated/kai_plugin_alloc_shim.h @ONLY)

# Expose shim via an INTERFACE target; link to allocator lib so symbols resolve.
add_library(kai_plugin_alloc_shim INTERFACE)
# Generated directory contains the header; -include guarantees it is injected
# into every translation unit for targets that link to this interface.
# This avoids manual #include within plugin sources and prevents ODR issues.

# Ensure the generated directory exists early (configure_file already does),
# then propagate include + compile options.
set(GENERATED_INCLUDE_DIR "${CMAKE_BINARY_DIR}/generated")

target_include_directories(kai_plugin_alloc_shim INTERFACE ${GENERATED_INCLUDE_DIR})
# Force-include the shim for all C++ sources of dependents
if(CMAKE_CXX_COMPILER_ID MATCHES "Clang" OR CMAKE_CXX_COMPILER_ID MATCHES "GNU" OR CMAKE_CXX_COMPILER_ID MATCHES "AppleClang")
    target_compile_options(kai_plugin_alloc_shim INTERFACE $<$<COMPILE_LANGUAGE:CXX>:-include;kai_plugin_alloc_shim.h>)
endif()
# Link rpmalloc/mimalloc as required
target_link_libraries(kai_plugin_alloc_shim INTERFACE ${KAI_ALLOCATOR_LIB})

# Load helper for plugin targets
include(KaiPlugin)

# Add subdirectories - plugin libs first so tests can reference their TARGET_FILE
add_subdirectory(src/plugins)
add_subdirectory(src)
add_subdirectory(fuzz)

# Diagnostics tests target
add_executable(diagnostics_service_test
    src/tests/diagnostics_service_test.cpp
)
target_link_libraries(diagnostics_service_test PRIVATE core ${PLATFORM_LIBS})
add_test(NAME DiagnosticsServiceTest COMMAND diagnostics_service_test)
target_compile_definitions(diagnostics_service_test PRIVATE TESTING)

# Gauge presence test ------------------------------------------------------
add_executable(diagnostics_gauges_test
    src/tests/diagnostics_gauges_test.cpp
)
target_link_libraries(diagnostics_gauges_test PRIVATE core ${PLATFORM_LIBS})
add_test(NAME DiagnosticsGaugesTest COMMAND diagnostics_gauges_test)
target_compile_definitions(diagnostics_gauges_test PRIVATE TESTING)

# Summary
message(STATUS "Configuration Summary:")
message(STATUS "  C++ Standard: ${CMAKE_CXX_STANDARD}")
message(STATUS "  Build Type: ${CMAKE_BUILD_TYPE}")
message(STATUS "  Install Prefix: ${CMAKE_INSTALL_PREFIX}")

# Set Catch2 include directory if found
if(Catch2_FOUND)
    message(STATUS "  Catch2: Found")
    include(Catch)

    # Add test targets for http_client
    add_executable(http_client_test 
        src/core/http/http_client_test.mm
    )
    
    if(APPLE)
        # Set Objective-C++ flags
        set_source_files_properties(
            src/core/http/http_client_test.mm
            PROPERTIES COMPILE_FLAGS "-x objective-c++"
        )
    endif()
    
    target_link_libraries(http_client_test
        PRIVATE
        http_client
        ${PLATFORM_LIBS}
    )
else()
    message(STATUS "  Catch2: Not found - tests will not be built")
endif()

# Copy resources
set(RESOURCE_FILES
    src/resources/highlight.min.js
)

# Copy resources next to the built binaries so that development runs (run.sh, unit-tests)
# can load them via [[NSBundle mainBundle] bundlePath]/Resources.
# The final packaging script will also copy the same files into .app.
set(DEST_RESOURCE_DIR "${CMAKE_RUNTIME_OUTPUT_DIRECTORY}/Resources")
file(MAKE_DIRECTORY "${DEST_RESOURCE_DIR}")

foreach(_res ${RESOURCE_FILES})
    get_filename_component(_res_filename "${_res}" NAME)
    configure_file("${_res}" "${DEST_RESOURCE_DIR}/${_res_filename}" COPYONLY)
endforeach()

add_custom_target(copy_resources ALL
    DEPENDS "${DEST_RESOURCE_DIR}/highlight.min.js"
    COMMENT "Staging resource files into ${DEST_RESOURCE_DIR}"
)

# Ensure the main executable depends on the staged resources so they are always
# up-to-date for local development launches.
add_dependencies(launcher copy_resources)

# Optional static analysis with clang-tidy. Enable automatically if the tool is found.
option(ENABLE_CLANG_TIDY "Run clang-tidy static analysis" OFF)
if(ENABLE_CLANG_TIDY)
    find_program(CLANG_TIDY_EXE NAMES clang-tidy)
    if(CLANG_TIDY_EXE)
        message(STATUS "clang-tidy found: ${CLANG_TIDY_EXE} – static analysis enabled")
        set(CMAKE_CXX_CLANG_TIDY ${CLANG_TIDY_EXE};-quiet)
    else()
        message(STATUS "clang-tidy not found – static analysis disabled")
    endif()
endif()

# Optional include-what-you-use analysis for header hygiene.
option(ENABLE_IWYU "Run include-what-you-use analysis" OFF)
if(ENABLE_IWYU)
    find_program(IWYU_PATH NAMES include-what-you-use iwyu)
    if(IWYU_PATH)
        message(STATUS "IWYU found: ${IWYU_PATH} – header analysis enabled")
        set(CMAKE_CXX_INCLUDE_WHAT_YOU_USE ${IWYU_PATH})
    else()
        message(WARNING "ENABLE_IWYU is ON but include-what-you-use executable not found")
    endif()
endif()

# Optional toggle to skip codesign verify for faster debug. Defaults ON in Debug.
option(KAI_DISABLE_CODESIGN_VERIFY "Disable codesign signature validation" OFF)

# If build type is Debug and user didn't explicitly force OFF, enable.
if((CMAKE_BUILD_TYPE STREQUAL "Debug") AND NOT KAI_DISABLE_CODESIGN_VERIFY)
  set(KAI_DISABLE_CODESIGN_VERIFY ON CACHE BOOL "Disable codesign signature validation" FORCE)
endif()

if(KAI_DISABLE_CODESIGN_VERIFY)
  add_compile_definitions(KAI_DISABLE_CODESIGN_VERIFY)
  message(STATUS "Codesign verification disabled (KAI_DISABLE_CODESIGN_VERIFY=ON)")
endif()

# The main executable target is defined in src/CMakeLists.txt

# -----------------------------------------------------------------------------
# Service headers are now *checked into version control*.
# -----------------------------------------------------------------------------
# The compile-time dependency DAG (`kSorted` in `service_topology.h`) is
# maintained manually whenever a new service is added or dependencies change.
# A `static_assert` in `service_static_checks.h` validates at build time that
# the order matches every service's declared `kDeps`, so mistakes are caught
# immediately without any code-generation step.

# Abseil already configured earlier — removed duplicate block to avoid
# re-declaration conflicts.

# ---------------------------------------------------------------------------
# Optional: skip building UI targets (Objective-C++) – handy for CI matrix
# variants that exercise backend-only flags such as LMDB without requiring
# macOS frameworks.  Default OFF so normal developer builds include UI.
# ---------------------------------------------------------------------------
option(SKIP_UI "Skip building UI code (macOS AppKit)" OFF)

# --- Linker selection: use lld when available --------------------------------
option(KAI_USE_LLD "Use LLVM lld linker if available" ON)

if(KAI_USE_LLD)
    # Look for ld64.lld first on macOS, then generic lld.
    find_program(LLD_BIN NAMES ld64.lld lld llvm-ld PATHS ENV PATH)
    if(LLD_BIN)
        message(STATUS "lld found: ${LLD_BIN} – using it as the project linker")
        # Override global linker variables so every generator path (Ninja, Xcode, Make)
        # leverages lld.  CACHE FORCE ensures subsequent re-configures keep the value.
        set(CMAKE_LINKER            ${LLD_BIN} CACHE STRING "Project linker" FORCE)
        set(CMAKE_C_LINKER          ${LLD_BIN} CACHE STRING "C linker" FORCE)
        set(CMAKE_CXX_LINKER        ${LLD_BIN} CACHE STRING "C++ linker" FORCE)

        # Clang requires the -fuse-ld flag even when CMAKE_LINKER is set for some generators.
        if(CMAKE_CXX_COMPILER_ID MATCHES "Clang" OR CMAKE_CXX_COMPILER_ID MATCHES "AppleClang")
            add_link_options(-fuse-ld=lld)
        endif()
    else()
        message(STATUS "lld not found – falling back to system linker")
    endif()
endif()

# Extra lld optimisation flags ------------------------------------------------
#   • --threads    : multithreaded linking (ELF, enabled non-Apple)
#   • -incremental : cache link graph (both Mach-O and ELF) for fast relink
# These are added only when Thin-LTO is disabled to avoid conflicts.
if(LLD_BIN AND NOT KAI_ENABLE_THINLTO)
    if(NOT APPLE)
        add_link_options(-Wl,--threads)
    endif()
    # Release incremental link: safe when no LTO
    if(CMAKE_BUILD_TYPE STREQUAL "Release")
        add_link_options(-Wl,-incremental)
    endif()
endif()

# -----------------------------------------------------------------------------
# Compiler cache: prefer sccache, fallback to ccache
# -----------------------------------------------------------------------------
option(KAI_USE_SCCACHE "Use sccache compiler cache if available" ON)
option(KAI_USE_CCACHE "Fallback to ccache when sccache not found" ON)

if(KAI_USE_SCCACHE)
    find_program(SCCACHE_BIN sccache PATHS ENV PATH)
    if(SCCACHE_BIN)
        message(STATUS "sccache found: ${SCCACHE_BIN} – compiler cache enabled")
        foreach(lang C CXX OBJC OBJCXX)
            set(CMAKE_${lang}_COMPILER_LAUNCHER ${SCCACHE_BIN} CACHE STRING "${lang} launcher" FORCE)
        endforeach()
    elseif(KAI_USE_CCACHE)
        find_program(CCACHE_BIN ccache PATHS ENV PATH)
        if(CCACHE_BIN)
            message(STATUS "ccache found: ${CCACHE_BIN} – fallback compiler cache enabled")
            foreach(lang C CXX OBJC OBJCXX)
                set(CMAKE_${lang}_COMPILER_LAUNCHER ${CCACHE_BIN} CACHE STRING "${lang} launcher" FORCE)
            endforeach()
        else()
            message(STATUS "No compiler cache found – proceeding without sccache/ccache")
        endif()
    endif()
endif()
# -----------------------------------------------------------------------------
