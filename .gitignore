# Build directories
build/
build-release/
build-dbg/
out/
cmake-build-*/
bin/
_deps/
build-*/

# IDE files
.idea/
.vscode/
*.swp
*.swo
.DS_Store
src/**/.DS_Store

# Compiled files
*.o
*.obj
*.exe
*.dll
*.so
*.dylib
*.a
*.lib

# CMake generated files
CMakeCache.txt
CMakeFiles/
cmake_install.cmake
Makefile
!CMakeLists.txt

# Generated files
*.log
compile_commands.json
progress_data.json

# Test output
Testing/
test_index.json
build-test/
.specstory/
.cache
build-*/
build_*/

# Third party sources
third_party/
