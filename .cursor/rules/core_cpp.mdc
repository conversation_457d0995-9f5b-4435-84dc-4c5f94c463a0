---
description: 
globs: *.cpp,*.h
alwaysApply: false
---
Apply these rules to all **C++ source/header files** in core and ranking modules.

1. **Architecture & Boundaries**
   - Do NOT include any UI (`src/ui/*`) headers; depend on abstract interfaces only.
   - Prefer composition, free functions, and RAII over deep class hierarchies.
   - Inject dependencies via constructor or factory; avoid singletons.

2. **Performance Targets**
   - Avoid dynamic allocations; prefer stack or custom memory pool (`src/core/utils/memory_pool.h`).
   - Watch cache locality; favour contiguous containers (`std::vector`, `absl::InlinedVector`).
   - Use `std::span`, `std::string_view`, and `std::move` semantics.
   - Minimize virtual hops, indirections, pointer churn
   - Optimize for cache locality, avoid cache misses
   - Use inline functions, templates, and macros for performance
   - Zero-copy, zero-allocation, zero-overhead
   - Use C++23 features

3. **Logging & Diagnostics**
   - Use `DBG/ERR/INF/WRN` macros for diagnostics.
   - Remove stray `std::cout` before commit.

4. **Error Handling**
   - Return structured `Result<T, Error>` or use early-return; only throw exceptions for truly exceptional cases.

5. **Style Quick-ref** (see @style_cheatsheet.md)
   - 4-space indent, 100-col limit, PascalCase types, snake_case funcs/vars.

6. **Testing**
   - For any new public API, add Catch2 in `src/tests/core/` mirroring path.

7. **C++23/23 Best Practices** (also see @style_cheatsheet.md#modern-c++20-best-practices)
   - Prefer `std::span`, `std::string_view` for non-owning refs.
   - Use `std::ranges` algorithms; avoid `std::begin/end` loops when possible.
   - Express template requirements via `concepts` & `requires` clauses over `enable_if`.
   - Mark value-returning factory functions `[[nodiscard]]`.
   - Annotate APIs `noexcept` when it can be guaranteed; propagate otherwise.
   - Use coroutine-based pipelines (`co_await`) if it clarifies async flows.
   - Leverage compile-time facilities (`constexpr`, `consteval`, `static_assert`).
   - Favour `std::format` for building user strings; keep logging via macros.
   - Flat hierarchy, simplicity, reusability, extensibility, maximum performance

When an edit would violate any rule, surface an alternative instead of applying it.
