---
description: 
globs: *.mm
alwaysApply: false
---
Apply to **Objective-C++** UI files only.

1. **Platform Tech**
   - Use AppKit (`NSWindow`, `NSViewController`) and Objective-C++ bridging; no Swift, no SwiftUI.
   - Keep `.mm` files thin; heavy logic resides in C++ `core/`.

2. **Threading & Performance**
   - UI work on main thread; offload heavy CPU to background via `dispatch_async` or C++ thread pool.

3. **Communication with Core**
   - Interact through `core/ui/` interfaces or observer events; avoid direct includes of internal utils.

4. **Logging**
   - Use `DBM/ERM` macros; avoid NSLog.

5. **Memory Management**
   - Prefer ARC objects; when bridging to C++ ensure lifetime via smart pointers or `std::shared_ptr` wrapped in ObjC++.

6. **Style**
   - Follow same naming conventions; Objective-C selectors in camelCase.

Ask for clarification before generating large UI refactors.
