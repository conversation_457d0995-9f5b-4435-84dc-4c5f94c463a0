---
description: 
globs: 
alwaysApply: false
---
Apply to Catch2-based unit/integration tests.

1. Mirror production paths; e.g., `src/core/foo.cpp` → `src/tests/core/foo_test.cpp`.
2. Keep tests deterministic/offline; stub network & filesystem.
3. Use `ASSERT_*` for fatal expectations, `EXPECT_*` otherwise.
4. Prefer small, focused tests over monolithic ones.
5. Ensure new features land with tests or explicit TODO(username) if deferred.

When creating scaffolding, auto-add to corresponding `CMakeLists.txt`.
