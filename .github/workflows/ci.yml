name: Kai CI

on:
    push:
        branches: [main]
    pull_request:
        branches: [main]

jobs:
    build:
        runs-on: macos-14
        strategy:
            matrix:
                config:
                    - { name: "release", cmake_flags: "-DCMAKE_BUILD_TYPE=Release" }
                    - { name: "asan", cmake_flags: "-DCMAKE_BUILD_TYPE=Debug -DENABLE_ASAN=ON" }
                    - { name: "tsan", cmake_flags: "-DCMAKE_BUILD_TYPE=Debug -DENABLE_TSAN=ON" }
                    - { name: "sign", cmake_flags: "-DCMAKE_BUILD_TYPE=Release -DKAI_ENABLE_FLATSNAPSHOT_SIGN=ON" }
                    - { name: "thinlto", cmake_flags: "-DCMAKE_BUILD_TYPE=Release -DKAI_ENABLE_THINLTO=ON" }
                    - { name: "release-mimalloc", cmake_flags: "-DCMAKE_BUILD_TYPE=Release -DKAI_ALLOCATOR=mimalloc" }
                    - {
                          name: "asan-mimalloc",
                          cmake_flags: "-DCMAKE_BUILD_TYPE=Debug -DENABLE_ASAN=ON -DKAI_ALLOCATOR=mimalloc",
                      }
                    # Feature-flag coverage (both VPATCH & MUX_QUEUE enabled)
                    - {
                          name: "flags",
                          cmake_flags: "-DCMAKE_BUILD_TYPE=Release -DKAI_ENABLE_VPATCH=ON -DKAI_ENABLE_MUX_QUEUE=ON",
                      }
        env:
            HOMEBREW_NO_INSTALL_CLEANUP: 1
            HOMEBREW_NO_AUTO_UPDATE: 1
        steps:
            - name: Checkout
              uses: actions/checkout@v4

            - name: Install dependencies
              run: brew install cmake ninja coreutils

            - name: Configure (${{ matrix.config.name }})
              run: |
                  mkdir -p build
                  cd build
                  cmake .. -G Ninja ${{ matrix.config.cmake_flags }} -Wno-dev -DKAI_DISABLE_CODESIGN_VERIFY=ON

            - name: Build (${{ matrix.config.name }})
              run: |
                  cd build
                  ninja -j$(sysctl -n hw.ncpu)

            - name: Run fast tests (${{ matrix.config.name }})
              run: scripts/run_tests_fast.sh

            - name: Codesign verify (${{ matrix.config.name }})
              run: ci/scripts/codesign_verify.sh

            - name: RingQueue fuzzer (30 s)
              if: matrix.config.name == 'tsan'
              run: |
                  build/bin/ring_queue_fuzzer -max_total_time=30 -print_final_stats=1 2>&1 | tee fz.log
                  RATE=$(grep -Eo "[0-9]+ exec/s" fz.log | awk '{print $1}' | tail -n1)
                  if [ "${RATE:-0}" -lt 5000 ]; then
                      echo "Throughput ${RATE} exec/s below 5k – failing CI."; exit 1;
                  fi

            - name: AdaptiveCache fuzzer (30 s)
              if: matrix.config.name == 'tsan'
              run: |
                  build/bin/adaptive_cache_fuzzer -max_total_time=30 -print_final_stats=1 2>&1 | tee acfz.log
                  RATE=$(grep -Eo "[0-9]+ exec/s" acfz.log | awk '{print $1}' | tail -n1)
                  if [ "${RATE:-0}" -lt 5000 ]; then
                      echo "Throughput ${RATE} exec/s below 5k – failing CI."; exit 1;
                  fi

            - name: Upload binary artifact (release only)
              if: matrix.config.name == 'release'
              uses: actions/upload-artifact@v4
              with:
                  name: kai-bin-release
                  path: build/bin

    perf_gate:
        needs: build
        if: success()
        runs-on: macos-14
        steps:
            - uses: actions/checkout@v4
            - name: Build benchmark
              run: |
                  mkdir -p build && cd build
                  cmake .. -G Ninja -DCMAKE_BUILD_TYPE=Release -Wno-dev
                  # Build all benches that are part of the perf gate
                  ninja adaptive_cache_hit_bench verifier_pipeline_bench runtime_scan_200_bench
            - name: Run bench_adaptive_cache_hit
              run: |
                  # Produce a single JSON file that accumulates results from all benches.
                  build/bin/adaptive_cache_hit_bench \
                    --benchmark_format=json \
                    --benchmark_out=perf_current.json
            - name: Run verifier_pipeline_bench
              run: |
                  build/bin/verifier_pipeline_bench \
                    --benchmark_format=json \
                    --benchmark_out=perf_current.json \
                    --benchmark_out_append=yes
            - name: Run runtime_scan_200_bench
              run: |
                  build/bin/runtime_scan_200_bench \
                    --benchmark_format=json \
                    --benchmark_out=perf_current.json \
                    --benchmark_out_append=yes
            - name: Perf regression gate
              run: |
                  ci/scripts/perf_regression_gate.py --baseline ci/perf_baseline.json --current perf_current.json --threshold 0.05

    notarisation_sim:
        needs: build
        runs-on: macos-14
        # Run only for Release artefacts; fail early if artefact missing
        steps:
            - uses: actions/checkout@v4

            - name: Download Release artefact
              uses: actions/download-artifact@v4
              with:
                  name: kai-bin-release
                  path: build/bin

            - name: Simulate notarisation
              run: bash ci/scripts/notarisation_sim.sh build/bin

    fuzz_flat_snapshot:
        needs: build
        runs-on: macos-14
        steps:
            - uses: actions/checkout@v4

            - name: Install dependencies
              run: brew install cmake ninja coreutils

            - name: Configure (ASan + fuzzers)
              run: |
                  mkdir -p build
                  cd build
                  cmake .. -G Ninja -DCMAKE_BUILD_TYPE=Debug -DENABLE_ASAN=ON -Wno-dev -DKAI_DISABLE_CODESIGN_VERIFY=ON

            - name: Build flat_snapshot_fuzzer
              run: |
                  cd build
                  ninja flat_snapshot_fuzzer manifest_json_fuzzer adaptive_cache_fuzzer

            - name: Run flat_snapshot_fuzzer (30 s, ≥5 k exec/s)
              run: bash ci/scripts/run_all_fuzzers.sh flat_snapshot_fuzzer 30 5000

            - name: Run manifest_json_fuzzer (30 s, ≥5 k exec/s)
              run: bash ci/scripts/run_all_fuzzers.sh manifest_json_fuzzer 30 5000

            - name: Run adaptive_cache_fuzzer (30 s, ≥5 k exec/s)
              run: bash ci/scripts/run_all_fuzzers.sh adaptive_cache_fuzzer 30 5000
