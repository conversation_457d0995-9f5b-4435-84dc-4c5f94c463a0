// -----------------------------------------------------------------------------
// @file probe_cache_api.h
// @brief Thin abstract interface exposing ProbeCache capabilities without
//        importing snapshot headers.  Allows core components to depend only on
//        this header while the concrete implementation lives in snapshot layer.
// -----------------------------------------------------------------------------

#pragma once

#include <array>
#include <filesystem>
#include "core/security/hash_types.hh"

namespace launcher::core::interfaces {

class IProbeCache {
public:
    virtual ~IProbeCache() = default;

    virtual launcher::core::security::Verdict lookup(const std::filesystem::path& path,
                                                     const std::array<uint8_t,32>& sha) noexcept = 0;

    virtual void update(const std::filesystem::path& path,
                        const std::array<uint8_t,32>& sha,
                        launcher::core::security::Verdict verdict) noexcept = 0;
};

} // namespace launcher::core::interfaces 