#pragma once

#include <cstdint>
#include <memory>
#include <optional>
#include <string>
#include <string_view>
#include <unordered_map>
#include <vector>
#include "../config/ai_provider_config.h"
#include "../util/result.h"

namespace launcher {
namespace core {

/**
 * Pure virtual interface exposing the subset of ConfigManager functionality
 * needed by consumers.  This allows dependency injection / mocking without
 * committing callers to the concrete singleton implementation.
 */
class IConfigManager {
 public:
    virtual ~IConfigManager() = default;

    // Lifecycle ------------------------------------------------------------
    virtual util::Result<void> initialize(const std::string& configPath = "") = 0;
    virtual util::Result<void> save() = 0;
    virtual void requestSave(uint32_t debounce_ms = 1000) = 0;

    // Basic getters --------------------------------------------------------
    virtual std::string getString(std::string_view key,
                                  const std::string& defaultValue = "") const = 0;
    virtual int         getInt(std::string_view key, int defaultValue = 0) const = 0;
    virtual bool        getBool(std::string_view key, bool defaultValue = false) const = 0;
    virtual double      getDouble(std::string_view key, double defaultValue = 0.0) const = 0;
    virtual std::vector<std::string> getStringArray(std::string_view key) const = 0;

    // Basic setters --------------------------------------------------------
    virtual void setString(std::string_view key, const std::string& value) = 0;
    virtual void setInt(std::string_view key, int value) = 0;
    virtual void setBool(std::string_view key, bool value) = 0;
    virtual void setDouble(std::string_view key, double value) = 0;
    virtual void setStringArray(std::string_view key,
                            const std::vector<std::string>& value) = 0;

    // Higher-level helpers -------------------------------------------------
    virtual std::string getConfigPath() const = 0;
    virtual std::string getApiKeyFor(std::string_view provider_id) const = 0;

    virtual std::vector<AIProviderConfig> getAIProviders() const = 0;
    virtual util::Result<void> replaceAIProviders(const std::vector<AIProviderConfig>& providers) = 0;
    virtual void setGlobalDefaultModel(std::string_view provider_id,
                                   std::string_view model_id) = 0;

    // Per-provider default model helpers ----------------------------------
    virtual std::string getProviderDefaultModel(std::string_view provider_id) const = 0;
    virtual void setProviderDefaultModel(std::string_view provider_id,
                                     std::string_view model_id) = 0;

    // Convenience helpers --------------------------------------------------
    virtual int getMaxResults() const { return getInt("launcher_bar.max_results", 50); }
    virtual std::string getLauncherHotkey() const { return getString("general.launcher_hotkey", "Alt+Space"); }
};

using IConfigManagerPtr = std::shared_ptr<IConfigManager>;

}  // namespace core
}  // namespace launcher 