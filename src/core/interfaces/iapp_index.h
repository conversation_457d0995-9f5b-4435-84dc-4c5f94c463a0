#pragma once

#include <chrono>
#include <memory>
#include <string>
#include <string_view>
#include <vector>
#include "../util/result.h"

namespace launcher {
namespace core {

struct AppResult;
class SearchHistory;
class AppScannerInterface;

/**
 * Abstract interface for an application index capable of adding, searching,
 * and enumerating application entries.  Allows swapping out the concrete
 * fuzzy-search implementation in tests.
 */
class IAppIndex {
 public:
    virtual ~IAppIndex() = default;

    // Index management -----------------------------------------------------
    virtual void addApp(const AppResult& app) = 0;
    virtual util::Result<void> updateApplications(const std::shared_ptr<AppScannerInterface>& scanner) = 0;

    // Search ---------------------------------------------------------------
    virtual std::vector<AppResult> search(const std::string_view& query,
                                          const SearchHistory* searchHistory = nullptr) const = 0;
    virtual std::vector<AppResult> getAllApplications() const = 0;

    // Stats ----------------------------------------------------------------
    virtual size_t size() const = 0;

    // Caching --------------------------------------------------------------
    virtual void setCaching(bool enabled,
                            size_t maxCacheSize = 50,
                            std::chrono::seconds ttl = std::chrono::seconds(60)) = 0;

    // Incremental updates ---------------------------------------------------
    virtual util::Result<void> enableIncrementalUpdates(const std::shared_ptr<AppScannerInterface>& scanner) = 0;
    virtual util::Result<void> disableIncrementalUpdates() = 0;
    virtual bool isIncrementalUpdatesEnabled() const = 0;
};

using IAppIndexPtr = std::shared_ptr<IAppIndex>;

}  // namespace core
}  // namespace launcher 