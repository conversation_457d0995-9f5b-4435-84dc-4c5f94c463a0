#pragma once

#include <string>
#include <vector>
#include <memory>

namespace launcher {
namespace ui {

struct WebsiteEntry; // forward decl of entry struct used by importer

class IBrowserHistoryImporter {
 public:
    virtual ~IBrowserHistoryImporter() = default;

    virtual int importAllBrowserHistory() = 0;
    virtual int continueImportAllBrowserHistory() = 0;
    virtual int importSafariHistory() = 0;
    virtual int importChromeHistory() = 0;
    virtual int importFirefoxHistory() = 0;

    // search
    virtual std::vector<WebsiteEntry> searchWebsites(const std::string &query,
                                                     int maxResults = 20) = 0;

    virtual size_t getTotalEntries() const = 0;
    virtual std::vector<WebsiteEntry> getTopEntries(size_t maxCount = 20) const = 0;

    // static utilities can't be in interface; but we can add virtual default.
    virtual void resetPermissionRequestFlag() = 0;
};

using IBrowserHistoryImporterPtr = std::shared_ptr<IBrowserHistoryImporter>;

} // namespace ui
} // namespace launcher 