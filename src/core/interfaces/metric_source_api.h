// -----------------------------------------------------------------------------
// @file metric_source_api.h
// @brief Interface that allows any service to push metrics into DiagnosticsService
//        periodically without Diagnostics knowing concrete types.
// -----------------------------------------------------------------------------
#pragma once

namespace launcher::core::diagnostics { class DiagnosticsService; }

namespace launcher::core::interfaces {

class IMetricSource {
public:
    virtual ~IMetricSource() = default;
    virtual void emitMetrics(launcher::core::diagnostics::DiagnosticsService&) noexcept = 0;
};

} // namespace launcher::core::interfaces 