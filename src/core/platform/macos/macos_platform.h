#pragma once

#include <filesystem>
#include <memory>
#include <string>
#include <vector>

#include "../../interfaces/iconfig_manager.h"
#include "../../util/result.h"

namespace launcher {
namespace core {

class AppScannerInterface;

/**
 * @brief macOS implementation of the platform interface
 */
class MacOSPlatform {
 public:
    explicit MacOSPlatform(IConfigManager& cfg);
    ~MacOSPlatform();

    // PlatformInterface implementation
    std::filesystem::path getAppDataDirectory() const;
    std::filesystem::path getUserHomeDirectory() const;
    std::filesystem::path getSystemApplicationsDirectory() const;
    std::filesystem::path getUserApplicationsDirectory() const;
    util::Result<void> launchApplication(const std::string& path,
                           const std::vector<std::string>& args = {}) const;
    util::Result<void> registerHotkey(int key, int modifiers, int id) const;
    util::Result<void> unregisterHotkey(int id) const;
    util::Result<void> setLaunchAtLogin(bool enable) const;
    bool isLaunchAtLoginEnabled() const;
    std::string getPlatformName() const;
    std::string getPlatformVersion() const;
    std::shared_ptr<AppScannerInterface> createAppScanner() const;

 private:
    // Private implementation details
    class Impl;
    std::unique_ptr<Impl> pImpl;

    IConfigManager& cfg_;
};

}  // namespace core
}  // namespace launcher