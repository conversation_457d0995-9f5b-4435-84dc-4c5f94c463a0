# macOS platform code CMakeLists.txt

# This directory contains macOS-specific code

# Find required macOS frameworks
find_library(APPKIT_LIBRARY AppKit REQUIRED)
find_library(FOUNDATION_LIBRARY Foundation REQUIRED)

# Set Objective-C++ properties for .mm files
set_source_files_properties(
    ${CMAKE_CURRENT_SOURCE_DIR}/macos_platform.mm
    PROPERTIES
    COMPILE_FLAGS "-x objective-c++"
)

# Add macOS frameworks to parent scope
set(MACOS_LIBRARIES
    ${APPKIT_LIBRARY}
    ${FOUNDATION_LIBRARY}
    PARENT_SCOPE
) 