#include "macos_platform.h"
#include "../common/platform_common.h"
#include "../../scanner/macos/macos_app_scanner.h"
#include "../../util/debug.h"
#include "../../util/result.h"

#import <Foundation/Foundation.h>
#import <AppKit/AppKit.h>
#import <Carbon/Carbon.h>  // For hotkey registration
#import <ServiceManagement/ServiceManagement.h> // For login item management

namespace fs = std::filesystem;

namespace launcher {
namespace core {

// Private implementation class (PIMPL idiom)
class MacOSPlatform::Impl {
public:
    Impl() = default;
    ~Impl() = default;
};

MacOSPlatform::MacOSPlatform(IConfigManager& cfg) : pImpl(new Impl()), cfg_(cfg) {
}

MacOSPlatform::~MacOSPlatform() = default;

fs::path MacOSPlatform::getAppDataDirectory() const {
    NSArray<NSString *> *paths = NSSearchPathForDirectoriesInDomains(NSApplicationSupportDirectory, NSUserDomainMask, YES);
    NSString *applicationSupportDirectory = [paths firstObject];
    
    // Create app-specific directory
    NSString *bundleID = [[NSBundle mainBundle] bundleIdentifier];
    if (!bundleID) {
        bundleID = @"com.launcher.app";
    }
    
    NSString *appDirectory = [applicationSupportDirectory stringByAppendingPathComponent:bundleID];
    
    // Create the directory if it doesn't exist
    NSFileManager *fileManager = [NSFileManager defaultManager];
    if (![fileManager fileExistsAtPath:appDirectory]) {
        [fileManager createDirectoryAtPath:appDirectory withIntermediateDirectories:YES attributes:nil error:nil];
    }
    
    return fs::path(appDirectory.UTF8String);
}

fs::path MacOSPlatform::getUserHomeDirectory() const {
    NSString *homeDirectory = NSHomeDirectory();
    return fs::path(homeDirectory.UTF8String);
}

fs::path MacOSPlatform::getSystemApplicationsDirectory() const {
    return fs::path("/Applications");
}

fs::path MacOSPlatform::getUserApplicationsDirectory() const {
    NSString *homeDirectory = NSHomeDirectory();
    NSString *userApplicationsDirectory = [homeDirectory stringByAppendingPathComponent:@"Applications"];
    return fs::path(userApplicationsDirectory.UTF8String);
}

util::Result<void> MacOSPlatform::launchApplication(const std::string& appPath, const std::vector<std::string>& args) const {
    if (appPath.empty()) {
        ERM(@"Empty application path");
        return util::Result<void>::failure("Empty application path");
    }
    
    NSString* appPath_ns_log = [NSString stringWithUTF8String:appPath.c_str()]; // For logging only
    DBM(@"Called with path: %@", appPath_ns_log);

    @try {
        NSString *path = [NSString stringWithUTF8String:appPath.c_str()]; // Keep original path string
        DBM(@"Application path as NSString: %@", path);

        NSFileManager *fileManager = [NSFileManager defaultManager];

        // Check if the file exists
        if (![fileManager fileExistsAtPath:path]) {
            ERM(@"File does not exist at path: %@", path);
            return util::Result<void>::failure("File does not exist");
        }

        // Check if the path is a directory (app bundle) or executable
        BOOL isDirectory = NO;
        [fileManager fileExistsAtPath:path isDirectory:&isDirectory];
        DBM(@"Path is %@ directory", isDirectory ? @"a" : @"not a");

        NSString *executablePath = path;

        // If it's a directory (app bundle), try to find the executable
        if (isDirectory) {
            NSString *infoPlistPath = [path stringByAppendingPathComponent:@"Contents/Info.plist"];
            DBM(@"Looking for Info.plist at: %@", infoPlistPath);

            if ([fileManager fileExistsAtPath:infoPlistPath]) {
                NSDictionary *infoPlist = [NSDictionary dictionaryWithContentsOfFile:infoPlistPath];
                DBM(@"Info.plist contents found");

                NSString *executableName = infoPlist[@"CFBundleExecutable"];
                DBM(@"Executable name from Info.plist: %@",
                         executableName ? executableName : @"null");

                if (executableName) {
                    executablePath = [path stringByAppendingPathComponent:
                                     [NSString stringWithFormat:@"Contents/MacOS/%@", executableName]];
                    DBM(@"Full executable path: %@", executablePath);

                    if ([fileManager fileExistsAtPath:executablePath]) {
                        DBM(@"Executable exists at path: %@", executablePath);
                    } else {
                        ERM(@"Executable does not exist at path: %@", executablePath);
                        return util::Result<void>::failure("Executable does not exist");
                    }
                } else {
                    ERM(@"No executable name found in Info.plist");
                    return util::Result<void>::failure("No executable name found in Info.plist");
                }
            } else {
                ERM(@"Info.plist not found at: %@", infoPlistPath);
                return util::Result<void>::failure("Info.plist not found");
            }
        }

        NSURL *appURL = [NSURL fileURLWithPath:path]; // Use original 'path'
        DBM(@"App URL created");

        NSWorkspace *workspace = [NSWorkspace sharedWorkspace];

        if (args.empty()) {
            // Launch without arguments
            DBM(@"Launching without arguments");
            NSError *error = nil;

            @try {
                // Deprecated way:
                // NSRunningApplication *app = [workspace launchApplicationAtURL:appURL
                //                                                             options:NSWorkspaceLaunchDefault
                //                                                       configuration:@{}
                //                                                               error:&error];

                // Modern way (macOS 11+)
                if (@available(macOS 11.0, *)) {
                    NSWorkspaceOpenConfiguration *config = [NSWorkspaceOpenConfiguration configuration];
                    [workspace openApplicationAtURL:appURL
                                        configuration:config
                                    completionHandler:^(NSRunningApplication * _Nullable app, NSError * _Nullable error) {
                                        if (!app) {
                                            ERM(@"Failed to launch application: %@", error.localizedDescription);
                                            // How to return false from here?
                                            // Consider using a completion handler or synchronous approach if return value is critical.
                                        }
                                    }];
                } else {
                    // Fallback for older macOS versions (still uses deprecated API)
                    #pragma clang diagnostic push
                    #pragma clang diagnostic ignored "-Wdeprecated-declarations"
                    NSRunningApplication *app = [workspace launchApplicationAtURL:appURL
                                                                          options:NSWorkspaceLaunchDefault
                                                                    configuration:@{}
                                                                            error:&error];
                    #pragma clang diagnostic pop
                    if (!app) {
                        ERM(@"Failed to launch application (fallback): %@", error.localizedDescription);
                        return util::Result<void>::failure("Failed to launch application (fallback)");
                    }
                }
                
                DBM(@"Application launch initiated successfully");
                return util::Result<void>::success();
            } @catch (NSException *exception) {
                ERM(@"EXCEPTION while launching application: %@", exception.description);
                return util::Result<void>::failure("Exception launching application");
            }
        } else {
            // Launch with arguments
            DBM(@"Launching with arguments: %lu", (unsigned long)args.size());
            NSMutableArray<NSString *> *nsArgs = [NSMutableArray arrayWithCapacity:args.size()];
            for (const auto& arg : args) {
                NSString *nsArg = [NSString stringWithUTF8String:arg.c_str()];
                [nsArgs addObject:nsArg];
                DBM(@"Argument: %@", nsArg);
            }

            // Use modern API if available
            if (@available(macOS 11.0, *)) {
                NSWorkspaceOpenConfiguration *config = [NSWorkspaceOpenConfiguration configuration];
                config.arguments = nsArgs;
                
                [workspace openApplicationAtURL:appURL 
                                    configuration:config 
                                completionHandler:^(NSRunningApplication * _Nullable app, NSError * _Nullable error) {
                    if (!app) {
                        ERM(@"Failed to launch application with arguments: %@", error.localizedDescription);
                        // Handle error appropriately (e.g., log, notify user)
                    }
                }];
                DBM(@"Application launch with arguments initiated successfully (async)");
                return util::Result<void>::success();
            } else {
                 // Fallback using deprecated API
                NSMutableDictionary *configuration = [NSMutableDictionary dictionary];
                // configuration[NSWorkspaceLaunchConfigurationArguments] = nsArgs; // Deprecated key
                // Use the string directly as it's defined in older SDKs
                configuration[@"NSWorkspaceLaunchConfigurationArguments"] = nsArgs;
                
                DBM(@"Launch configuration prepared (fallback)");
                NSError *error = nil;
                @try {
                    #pragma clang diagnostic push
                    #pragma clang diagnostic ignored "-Wdeprecated-declarations"
                    NSRunningApplication *app = [workspace launchApplicationAtURL:appURL
                                                                          options:NSWorkspaceLaunchDefault
                                                                    configuration:configuration
                                                                            error:&error];
                    #pragma clang diagnostic pop
                    if (!app) {
                        ERM(@"Failed to launch application with arguments (fallback): %@", error.localizedDescription);
                        return util::Result<void>::failure("Failed to launch application with arguments (fallback)");
                    }
                    DBM(@"Application launched successfully with arguments (fallback)");
                    return util::Result<void>::success();
                } @catch (NSException *exception) {
                    ERM(@"EXCEPTION while launching application with arguments (fallback): %@", exception.description);
                    return util::Result<void>::failure("Exception launching application with arguments (fallback)");
                }
            }
        }
    } @catch (NSException *exception) {
        ERM(@"EXCEPTION in launchApplication: %@", exception.description);
        return util::Result<void>::failure("Exception launching application");
    } @catch (...) {
        ERM(@"UNKNOWN EXCEPTION in launchApplication");
        return util::Result<void>::failure("Unknown exception launching application");
    }
}

util::Result<void> MacOSPlatform::registerHotkey(int key, int modifiers, int id) const {
    @try {
        DBM(@"Registering hotkey with key: %d, modifiers: %d, id: %d",
                 key, modifiers, id);

        // Convert our modifiers to Carbon modifiers
        UInt32 carbonModifiers = 0;
        if (modifiers & 1) carbonModifiers |= cmdKey;      // Command
        if (modifiers & 2) carbonModifiers |= optionKey;   // Option/Alt
        if (modifiers & 4) carbonModifiers |= controlKey;  // Control
        if (modifiers & 8) carbonModifiers |= shiftKey;    // Shift
        
        // Register the hotkey
        EventHotKeyRef hotKeyRef;
        EventHotKeyID hotKeyID;
        hotKeyID.signature = 'LWAP';  // MicroLauncher
        hotKeyID.id = id;
        
        OSStatus status = RegisterEventHotKey(key, carbonModifiers, hotKeyID,
                                             GetApplicationEventTarget(),
                                             0, &hotKeyRef);

        if (status != noErr) {
            ERM(@"Failed to register hotkey. Status: %d", (int)status);
            return util::Result<void>::failure("Failed to register hotkey");
        }

        DBM(@"Hotkey registered successfully");
        return util::Result<void>::success();
    } @catch (NSException *exception) {
        ERM(@"EXCEPTION in registerHotkey: %@", exception.description);
        return util::Result<void>::failure("Exception registerHotkey");
    } @catch (...) {
        ERM(@"UNKNOWN EXCEPTION in registerHotkey");
        return util::Result<void>::failure("Unknown exception registerHotkey");
    }
}

util::Result<void> MacOSPlatform::unregisterHotkey(int id) const {
    @try {
        DBM(@"Unregistering hotkey with id: %d", id);

        // Find the hotkey reference with the given ID
        // This is a simplified implementation - in a real app, you would store
        EventHotKeyRef hotKeyRef = NULL;
        
        // Since we don't have the actual reference, we can't unregister it
        // In a real implementation, you would store the references in a map
        WRM(@"Hotkey unregistration not fully implemented");
        
        // If we had the reference, we would do:
        // OSStatus status = UnregisterEventHotKey(hotKeyRef);
        // if (status != noErr) {
        //     ERM("unregisterHotkey", "Failed to unregister hotkey. Status: " + std::to_string((int)status));
        //     return false;
        // }
        
        return util::Result<void>::failure("Hotkey unregistration not implemented");
    } @catch (NSException *exception) {
        ERM(@"EXCEPTION in unregisterHotkey: %@", exception.description);
        return util::Result<void>::failure("Exception unregisterHotkey");
    } @catch (...) {
        ERM(@"UNKNOWN EXCEPTION in unregisterHotkey");
        return util::Result<void>::failure("Unknown exception unregisterHotkey");
    }
}

util::Result<void> MacOSPlatform::setLaunchAtLogin(bool enable) const {
    @try {
        DBM(@"Setting launch at login to: %@", enable ? @"YES" : @"NO");

        // Get the app URL
        NSURL *appURL = [[NSBundle mainBundle] bundleURL];
        if (!appURL) {
            ERM(@"Failed to get app URL");
            return util::Result<void>::failure("Failed to get app URL");
        }

        // Get the bundle identifier
        NSString *bundleID = [[NSBundle mainBundle] bundleIdentifier];
        if (!bundleID) {
            bundleID = @"com.launcher.app";
            WRM(@"Using default bundle ID: com.launcher.app");
        }

        // Use the SMLoginItemSetEnabled API (available in macOS 10.10+)
        // OSStatus status = SMLoginItemSetEnabled((__bridge CFStringRef)bundleID, enable); // Deprecated

        if (@available(macOS 13.0, *)) {
            SMAppService *service = [SMAppService mainAppService];
            NSError *error = nil;
            if (enable) {
                [service registerAndReturnError:&error];
            } else {
                [service unregisterAndReturnError:&error];
            }

            if (error) {
                ERM(@"Failed to %@ login item using SMAppService: %@", enable ? @"register" : @"unregister", error.localizedDescription);
                return util::Result<void>::failure("SMAppService error");
            } else {
                DBM(@"Successfully %@ed login item for %@ using SMAppService",
                        enable ? @"register" : @"unregister", bundleID);
                return util::Result<void>::success();
            }
        } else {
             // Fallback for older macOS versions (still uses deprecated API)
             #pragma clang diagnostic push
             #pragma clang diagnostic ignored "-Wdeprecated-declarations"
             OSStatus status = SMLoginItemSetEnabled((__bridge CFStringRef)bundleID, enable);
             #pragma clang diagnostic pop

             if (status != noErr) {
                 ERM(@"Failed to set login item (fallback). Status: %d", (int)status);
                 return util::Result<void>::failure("SMLoginItemSetEnabled failed");
             }
             DBM(@"Successfully set login item (fallback) for %@ to %@",
                      bundleID, enable ? @"enabled" : @"disabled");
             return util::Result<void>::success();
        }

    } @catch (NSException *exception) {
        ERM(@"EXCEPTION in setLaunchAtLogin: %@", exception.description);
        return util::Result<void>::failure("Exception setLaunchAtLogin");
    } @catch (...) {
        ERM(@"UNKNOWN EXCEPTION in setLaunchAtLogin");
        return util::Result<void>::failure("Unknown exception setLaunchAtLogin");
    }
}

bool MacOSPlatform::isLaunchAtLoginEnabled() const {
    @try {
        // Get the bundle identifier
        NSString *bundleID = [[NSBundle mainBundle] bundleIdentifier];
        if (!bundleID) {
            bundleID = @"com.launcher.app";
            WRM(@"Using default bundle ID for checking login item: com.launcher.app");
        }

        // Check if the app is in the login items
        // Modern way (macOS 13+)
        if (@available(macOS 13.0, *)) {
            SMAppService *service = [SMAppService mainAppService];
            bool isEnabled = (service.status == SMAppServiceStatusEnabled);
            DBM(@"Launch at login is %@ (SMAppService)", isEnabled ? @"enabled" : @"disabled");
            return isEnabled;
        } else {
            // Fallback for older macOS versions
            // Unfortunately, there's no reliable public API before macOS 13.
            // We'll rely on the ConfigManager as a fallback, acknowledging it might be inaccurate
            // if the user changed the setting outside the app.
            WRM(@"Unable to reliably check login item status on this macOS version. Relying on config.");
            bool isEnabled = cfg_.getBool("general.startup_on_login", false);
            DBM(@"Launch at login is %@ (Config Fallback)", isEnabled ? @"enabled" : @"disabled");
            return isEnabled;
        }

    } @catch (NSException *exception) {
        ERM(@"EXCEPTION in isLaunchAtLoginEnabled: %@", exception.description);
        return false;
    } @catch (...) {
        ERM(@"UNKNOWN EXCEPTION in isLaunchAtLoginEnabled");
        return false;
    }
}

std::string MacOSPlatform::getPlatformName() const {
    return "macOS";
}

std::string MacOSPlatform::getPlatformVersion() const {
    NSOperatingSystemVersion version = [[NSProcessInfo processInfo] operatingSystemVersion];
    std::string versionStr = std::to_string(version.majorVersion) + "." +
                             std::to_string(version.minorVersion) + "." +
                             std::to_string(version.patchVersion);
    return versionStr;
}

std::shared_ptr<AppScannerInterface> MacOSPlatform::createAppScanner() const {
    return std::make_shared<MacOSAppScanner>();
}

} // namespace core
} // namespace launcher 