# Platform abstraction layer CMakeLists.txt

# Determine platform-specific source files
if(APPLE)
    set(PLATFORM_SOURCES
        ${CMAKE_CURRENT_SOURCE_DIR}/macos/macos_platform.mm
        ${CMAKE_CURRENT_SOURCE_DIR}/common/platform_common.cpp
    )
else()
    message(FATAL_ERROR "Unsupported platform")
endif()

# Add platform sources to parent scope
set(PLATFORM_SOURCES ${PLATFORM_SOURCES} PARENT_SCOPE)

# Add subdirectories
add_subdirectory(common)
if(APPLE)
    add_subdirectory(macos)
endif() 