#include "platform_common.h"

#include <filesystem>

namespace fs = std::filesystem;

namespace launcher {
namespace core {

fs::path PlatformCommon::normalizePath(const fs::path& path) {
    try {
        return fs::canonical(path);
    } catch (const fs::filesystem_error&) {
        // If canonical fails (e.g., path doesn't exist), return the absolute path
        return fs::absolute(path);
    }
}

bool PlatformCommon::fileExists(const fs::path& path) {
    std::error_code ec;
    return fs::exists(path, ec) && fs::is_regular_file(path, ec);
}

bool PlatformCommon::directoryExists(const fs::path& path) {
    std::error_code ec;
    return fs::exists(path, ec) && fs::is_directory(path, ec);
}

bool PlatformCommon::createDirectory(const fs::path& path) {
    std::error_code ec;
    return fs::create_directories(path, ec);
}

fs::path PlatformCommon::getExecutablePath() {
    // This is platform-dependent and will be implemented in the platform-specific code
    return fs::path();
}

}  // namespace core
}  // namespace launcher