#pragma once

#include <filesystem>
#include <string>

namespace launcher {
namespace core {

/**
 * @brief Common platform utilities
 *
 * This class provides platform-independent utility functions
 * that can be used by all platform implementations.
 */
class PlatformCommon {
 public:
    /**
     * @brief Normalize a file path
     *
     * Converts a path to a canonical form, resolving symlinks,
     * relative paths, and removing redundant separators.
     *
     * @param path The path to normalize
     * @return std::filesystem::path The normalized path
     */
    static std::filesystem::path normalizePath(const std::filesystem::path& path);

    /**
     * @brief Check if a file exists
     *
     * @param path The path to check
     * @return bool True if the file exists
     */
    static bool fileExists(const std::filesystem::path& path);

    /**
     * @brief Check if a directory exists
     *
     * @param path The path to check
     * @return bool True if the directory exists
     */
    static bool directoryExists(const std::filesystem::path& path);

    /**
     * @brief Create a directory
     *
     * Creates a directory and all parent directories if they don't exist.
     *
     * @param path The directory path to create
     * @return bool True if the directory was created or already exists
     */
    static bool createDirectory(const std::filesystem::path& path);

    /**
     * @brief Get the executable path
     *
     * @return std::filesystem::path Path to the current executable
     */
    static std::filesystem::path getExecutablePath();
};

}  // namespace core
}  // namespace launcher