#pragma once

#include <array>
#include <atomic>
#include <chrono>
#include <filesystem>
#include <memory>
#include <string>
#include <thread>
#include <string_view>
#include <vector>
#include <unordered_set>
#include <mutex>

#include "../util/file_watcher.h"
#include "../util/debug.h"
#include "../util/result.h"
#include "../runtime/ring_queue_backend.hh"
#include "../runtime/queue_traits.hh"
#if KAI_ENABLE_MUX_QUEUE
#include "../runtime/mux_queue.hh"
#endif
#include "../async/executor_service.h"
#include "../events/event_bus_service.h"
#include "../security/mask128.hh"

namespace launcher::core::runtime {

// ---------------------------------------------------------------------------
// RuntimeType – coarse classification of runtime files we recognise.
// In Slice-2 we only differentiate on file extension; extend later.
// ---------------------------------------------------------------------------

enum class RuntimeType : uint8_t {
    kNative       = 0, // native executable / dylib / bundle
    kJavaScript   = 1,
    kWebAssembly  = 2,
    kMcpServer    = 3,
    kUnknown      = 255
};

// ---------------------------------------------------------------------------
// Capability mask – using real Mask128 from core/security/mask128.hh.
// ---------------------------------------------------------------------------
using CapabilityMask = launcher::core::security::Mask128;

// ---------------------------------------------------------------------------
// RuntimeDescriptor – produced by RuntimeScanner when a runtime file is fully
// hashed and verified.
// ---------------------------------------------------------------------------
struct RuntimeDescriptor {
    RuntimeType                        type{RuntimeType::kUnknown};
    std::filesystem::path              path;         // canonical path
    CapabilityMask                     caps;         // declared capabilities (future)
    std::array<uint8_t, 32>            sha256;       // SHA-256 of file contents
    std::chrono::system_clock::time_point mtime{};   // filesystem mtime
};

// ---------------------------------------------------------------------------
// Event published on EventBus once a runtime has been discovered + verified.
// ---------------------------------------------------------------------------
struct RuntimeDiscoveredEvent {
    RuntimeDescriptor descriptor;
};

using RuntimeDiscoveredEventPtr = std::shared_ptr<RuntimeDiscoveredEvent>;

// ---------------------------------------------------------------------------
// RuntimeScanner – watches directories, debounces FsEvents, dispatches hashing
// & verification jobs to ExecutorService, publishes RuntimeDiscoveredEvent.
// ---------------------------------------------------------------------------
class RuntimeScanner {
 public:
    // capacity of internal ring queue (power-of-two).
    static constexpr std::size_t kQueueCap = 4096;

    RuntimeScanner(async::ExecutorService& executor,
                   events::EventBusService& bus,
                   std::vector<std::filesystem::path> roots,
                   std::chrono::milliseconds debounce_window = std::chrono::milliseconds(30));

    ~RuntimeScanner();

    util::Result<void> start();
    void             stop() noexcept;

    [[nodiscard]] QueueStats stats() const noexcept { return queue_.stats(); }
    static constexpr bool is_metric_source = true;

 private:
    // --------------------------- helpers -----------------------------------
    // Deduplication – paths that have already produced a RuntimeDiscoveredEvent.
    // We guard the unordered_set with a mutex because processFile() can be
    // executed concurrently on the ExecutorService worker threads.  Expected
    // insertion rate is low (<= few thousand), so the contention window is
    // negligible and memory overhead acceptable.
    std::unordered_set<std::string> processed_paths_;
    mutable std::mutex processed_mtx_;

    bool allowEvent(const std::string& path) noexcept;
    void consumerLoop();
    void scheduleProcess(const std::filesystem::path& p);
    void processFile(const std::filesystem::path& p);

    // root directories to scan & watch
    const std::vector<std::filesystem::path> roots_;

    async::ExecutorService&  executor_;
    events::EventBusService& bus_;

    std::shared_ptr<::launcher::core::FileWatcher> watcher_;

    // lock-free producer queue (FileWatcher thread → consumer thread)
#if KAI_ENABLE_MUX_QUEUE
    // Use MuxQueue backend when feature flag is enabled.  We dedicate 4
    // priority levels but all FileEvent currently map to default priority 0
    // because FileEvent lacks a .priority field.  This preserves identical
    // behaviour while enabling strict-priority delivery for future high/low
    // watermark events without further changes.
    static constexpr std::size_t kMuxLevels  = 4;
    static constexpr std::size_t kSubCap     = kQueueCap / kMuxLevels;
    static_assert((kQueueCap % kMuxLevels) == 0, "kQueueCap must be divisible by kMuxLevels");

    ::launcher::core::runtime::MuxQueueBackend<::launcher::core::FileEvent,
                                               kSubCap,
                                               kMuxLevels> queue_;
#else
    ::launcher::core::runtime::RingQueueBackend<::launcher::core::FileEvent, kQueueCap> queue_;
#endif

    // consumer thread
    std::thread consumer_;
    std::atomic<bool> stop_flag_{false};

    // debounce ring – fixed size, zero-alloc.
    struct DebounceEntry { uint64_t hash{0}; uint64_t ts_ns{0}; };
    static constexpr std::size_t kDebounceSlots = 1024;
    std::array<DebounceEntry, kDebounceSlots> debounce_{};
    const uint64_t window_ns_;

    // hash helper (FNV-1a 64-bit)
    static uint64_t fnv1a64(std::string_view s) noexcept;
};

} // namespace launcher::core::runtime 