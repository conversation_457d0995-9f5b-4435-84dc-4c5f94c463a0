/*
 * @file mux_queue.hh
 * @brief Lock-free multi-producer single-consumer tagged-priority queue.
 *
 * A thin composition of multiple RingQueueBackend<T,kSubCap> shards – one per
 * priority level – plus a single 32-bit atomic bitmap (`nonempty_mask_`) that
 * indicates which shards contain data.  Lower numerical value means **higher**
 * priority (0 – top priority).
 *
 * Design goals:
 *   • Strict priority ordering: consumer always pops from the highest available
 *     priority level; FIFO order is preserved *within* a level.
 *   • Zero steady-state heap allocations.  All storage is pre-allocated in the
 *     underlying ring buffers.
 *   • Header-only hot path to enable inlining.
 *   • API matches EventQueue concept (`try_push`, `try_pop`, `wait_pop`, …).
 *   • Enabled at compile-time via `KAI_ENABLE_MUX_QUEUE`.  Header can still be
 *     included when the flag is OFF – no dead code warnings.
 */
#pragma once

#include <array>
#include <atomic>
#include <bit>
#include <concepts>
#include <cassert>
#include <cstddef>
#include <cstdint>
#include <optional>
#include <thread>
#include <type_traits>

#include "ring_queue_backend.hh"
#include "queue_traits.hh"

namespace launcher::core::runtime {

namespace detail {
// Detection idiom – check whether T has a public data member named `priority`.
template <typename T>
concept HasPriorityField = requires(const T& v) {
    { v.priority } -> std::convertible_to<std::uint8_t>;
};
}  // namespace detail

// ---------------------------------------------------------------------------
// MuxQueueBackend – Fixed number of priority levels (0 == highest).
// ---------------------------------------------------------------------------

template <typename T, std::size_t kSubCap, std::size_t kLevels = 3>
class MuxQueueBackend {
    static_assert(kLevels > 0 && kLevels <= 32, "MuxQueueBackend supports 1…32 levels");
    static_assert(::launcher::core::runtime::detail::isPowerOfTwo(kSubCap), "kSubCap must be power-of-two");

 public:
    using value_type = T;

    // ---------------------------- capacity ---------------------------------
    [[nodiscard]] constexpr std::size_t capacity() const noexcept {
        return kSubCap * kLevels;
    }

    // ----------------------------- size ------------------------------------
    [[nodiscard]] std::size_t size() const noexcept {
        std::size_t total = 0;
        for (const auto& q : queues_) total += q.size();
        return total;
    }

    // --------------------------- try_push ----------------------------------
    bool try_push(T&& item) noexcept {
        const std::uint8_t lvl = clampPriority(priorityOf(item));
        if (!queues_[lvl].try_push(std::move(item))) {
            // Underlying queue full → drop statistics already counted inside.
            return false;
        }

        // Mark level as non-empty and wake (single) waiter.
        nonempty_mask_.fetch_or(1u << lvl, std::memory_order_acq_rel);
        nonempty_mask_.notify_one();
        return true;
    }

    // Convenience overload for copyable types.
    bool try_push(const T& item) noexcept requires(std::is_copy_constructible_v<T>) {
        T copy = item;
        return try_push(std::move(copy));
    }

    // --------------------------- try_pop -----------------------------------
    bool try_pop(T& out) noexcept {
        const uint32_t mask = nonempty_mask_.load(std::memory_order_acquire);
        if (mask == 0) {
            return false;  // empty – fast path
        }

        // ---------------- Fairness chooser ------------------------------
        std::uint32_t lvl;
        if (high_prio_budget_ == 0) {
            // Force switch to next available lower-priority shard if any.
            const uint32_t lower_mask = mask & ~1u;  // clear bit 0 (level-0)
            if (lower_mask != 0) {
                lvl = static_cast<std::uint32_t>(std::countr_zero(lower_mask));
            } else {
                lvl = 0; // fallback – only high-priority available
            }
        } else {
            lvl = static_cast<std::uint32_t>(std::countr_zero(mask));
        }

        if (lvl >= kLevels) {
            // Should not happen; mask invariant violated.  Treat as empty.
            return false;
        }

        if (!queues_[lvl].try_pop(out)) {
            // Rare race: producer set the bit but slot was not yet ready OR
            // we lost a concurrency window. Re-examine shard size; if it is
            // currently empty we must clear the bit, otherwise the consumer
            // would spin forever on this shard while producers stall.

            if (queues_[lvl].size() == 0) {
                const uint32_t bit = 1u << lvl;
                // Opportunistically clear; if a producer pushes between size()
                // and the AND it will re-set the bit via fetch_or.
                nonempty_mask_.fetch_and(~bit, std::memory_order_acq_rel);
            }
            return false;
        }

        // Successfully popped – clear bit if queue became empty.
        if (queues_[lvl].size() == 0) {
            // Two-step "test-and-clear": clear the bit optimistically, then
            // re-check the underlying shard. If a producer inserted after our
            // pop we restore the bit. This guarantees the bit remains set
            // whenever the queue is *actually* non-empty without the ABA loop
            // overhead.
            const uint32_t bit = 1u << lvl;
            nonempty_mask_.fetch_and(~bit, std::memory_order_acq_rel);

            // If another producer raced and queue is **not** empty anymore
            // we must set the bit back.
            if (queues_[lvl].size() != 0) {
                nonempty_mask_.fetch_or(bit, std::memory_order_acq_rel);
            }
        }

        // Update fairness budget ----------------------------------------
        if (lvl == 0) {
            if (high_prio_budget_ > 0) {
                --high_prio_budget_;
            }
        } else {
            high_prio_budget_ = kFairPopBudget; // reset after serving lower-prio
        }
        return true;
    }

    // -------------------------- wait_pop -----------------------------------
    void wait_pop(T& out) noexcept {
        uint32_t expected = 0;
        while (true) {
            expected = nonempty_mask_.load(std::memory_order_acquire);
            if (expected != 0 && try_pop(out)) {
                return;  // got element
            }
            // Wait until mask changes; atomic::wait blocks efficiently.
            nonempty_mask_.wait(expected, std::memory_order_relaxed);
        }
    }

    // --------------------------- stats -------------------------------------
    [[nodiscard]] QueueStats stats() const noexcept {
        QueueStats agg{};
        for (const auto& q : queues_) {
            const auto s = q.stats();
            agg.hit += s.hit;
            agg.miss += s.miss;
            agg.drop += s.drop;
            if (s.high_water_pct > agg.high_water_pct) agg.high_water_pct = s.high_water_pct;
        }
        return agg;
    }

    // Expose as MetricSource provider.
    static constexpr bool is_metric_source = true;

 private:
    // ------------------------ helpers --------------------------------------
    static constexpr std::uint8_t clampPriority(std::uint8_t p) noexcept {
        return (p < kLevels) ? p : static_cast<std::uint8_t>(kLevels - 1);
    }

    static constexpr std::uint8_t priorityOf(const T& v) noexcept {
        if constexpr (detail::HasPriorityField<T>) {
            return static_cast<std::uint8_t>(v.priority);
        } else {
            return 0;  // default priority when field absent
        }
    }

    // Fairness: after serving this many consecutive high-priority (level 0)
    // items the consumer is forced to pop from a lower priority level if
    // one is available.  This guarantees that low-priority tasks are not
    // starved for more than a bounded window (≈ kFairPopBudget / pop_rate).
    static constexpr std::size_t kFairPopBudget = 8;
    mutable std::size_t high_prio_budget_ = kFairPopBudget;

    // Sharded ring queues per level (0 == highest priority).
    std::array<RingQueueBackend<T, kSubCap>, kLevels> queues_{};

    // Bitmap; bit i set ⇒ queues_[i] non-empty.
    std::atomic<uint32_t> nonempty_mask_{0};
};

}  // namespace launcher::core::runtime
