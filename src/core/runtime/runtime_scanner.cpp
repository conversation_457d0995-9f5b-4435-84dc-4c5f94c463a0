#include "runtime_scanner.hh"

#include <fstream>
#include <cstring>
#include <string_view>
#ifdef __APPLE__
#include <CommonCrypto/CommonDigest.h>
#endif
#include "../foundation/registry.h" // KaiError enum for back-pressure handling
#include <nlohmann/json.hpp>
#include "core/foundation/capability256.h"
#include "core/security/mask128.hh"

namespace launcher::core::runtime {

using ::launcher::core::security::Mask128;
using ::kai::fromString;

// Ensure ERR/DBG macros resolve to global ::detail::logInternal despite inner
// runtime::detail namespace from ring_queue_backend.hh.
namespace detail {
    using ::detail::logInternal;
}

// ------------------------- FNV1a helper ----------------------------------
uint64_t RuntimeScanner::fnv1a64(std::string_view s) noexcept {
    constexpr uint64_t kOffset = 0xcbf29ce484222325ULL;
    constexpr uint64_t kPrime  = 0x100000001b3ULL;
    uint64_t acc = kOffset;
    for (unsigned char c : s) {
        acc ^= static_cast<uint64_t>(c);
        acc *= kPrime;
    }
    return acc;
}

// ------------------------------ ctor -------------------------------------
RuntimeScanner::RuntimeScanner(async::ExecutorService& executor,
                               events::EventBusService& bus,
                               std::vector<std::filesystem::path> roots,
                               std::chrono::milliseconds debounce_window)
    : roots_(std::move(roots)),
      executor_(executor),
      bus_(bus),
      watcher_(::launcher::core::FileWatcher::create()),
      window_ns_(static_cast<uint64_t>(debounce_window.count()) * 1'000'000ULL) {
    // zero-init debounce_ already done via {} initialiser
}

RuntimeScanner::~RuntimeScanner() {
    stop();
}

// ------------------------------ start ------------------------------------
util::Result<void> RuntimeScanner::start() {
    const bool has_watcher = static_cast<bool>(watcher_);

    // 1. Kick initial cold scan (parallelised)
    for (const auto& r : roots_) {
        try {
            for (auto it = std::filesystem::recursive_directory_iterator(r); it != std::filesystem::recursive_directory_iterator(); ++it) {
                if (it->is_regular_file()) {
                    const auto fname = it->path().filename().string();
                    if (!fname.empty() && fname[0] == '.') continue; // skip hidden files like .DS_Store
                    if (fname.ends_with(".manifest.json")) continue; // skip manifest sidecars
                    scheduleProcess(it->path());
                }
            }
        } catch (const std::exception& e) {
            ERR("RuntimeScanner: initial scan error " << e.what());
        }
    }

    if (!has_watcher) {
        // Platform without watcher – only cold scan supported.
        return util::Result<void>::success();
    }

    bool add_ok = true;
    for (const auto& r : roots_) {
        auto res = watcher_->addWatch(r.string(), true);
        if (!res) {
            WRN("RuntimeScanner: addWatch failed for " << r);
            add_ok = false;
        }
    }

    if (!add_ok) {
        // Degrade to cold-scan only.
        watcher_.reset();
        return util::Result<void>::success();
    }

    // Start consumer thread
    stop_flag_.store(false, std::memory_order_relaxed);
    consumer_ = std::thread(&RuntimeScanner::consumerLoop, this);

    auto res = watcher_->startWatching([this](const ::launcher::core::FileEvent& evt) {
        if (!allowEvent(evt.path)) return;
        queue_.try_push(::launcher::core::FileEvent{evt});
    });

    if (!res) {
        WRN("RuntimeScanner: startWatching failed – continuing without live events");
        watcher_.reset();
        // Consumer thread useless without producer; stop it.
        stop_flag_.store(true, std::memory_order_relaxed);
        queue_.try_push(::launcher::core::FileEvent{});
        if (consumer_.joinable()) consumer_.join();
        return util::Result<void>::success();
    }

    return res;
}

// ------------------------------ stop -------------------------------------
void RuntimeScanner::stop() noexcept {
    if (stop_flag_.exchange(true)) return; // already stopped

    if (watcher_) {
        watcher_->stopWatching();
    }

    if (consumer_.joinable()) {
        queue_.try_push(::launcher::core::FileEvent{}); // wake consumer (dummy)
        consumer_.join();
    }
}

// --------------------------- allowEvent (debounce) -----------------------
bool RuntimeScanner::allowEvent(const std::string& path) noexcept {
    uint64_t h = fnv1a64(path);
    const std::size_t idx = h & (kDebounceSlots - 1);

    const uint64_t now_ns = std::chrono::duration_cast<std::chrono::nanoseconds>(
                                std::chrono::steady_clock::now().time_since_epoch()).count();

    uint64_t prev_hash = debounce_[idx].hash;
    uint64_t prev_ts   = debounce_[idx].ts_ns;

    if (prev_hash == h && (now_ns - prev_ts) < window_ns_) {
        return false; // drop duplicate within window
    }

    debounce_[idx].hash   = h;
    debounce_[idx].ts_ns  = now_ns;
    return true;
}

// --------------------------- consumerLoop ---------------------------------
void RuntimeScanner::consumerLoop() {
    while (!stop_flag_.load(std::memory_order_relaxed)) {
        ::launcher::core::FileEvent evt;
        queue_.wait_pop(evt);
        if (evt.path.empty()) {
            // dummy event used for wake-up on stop; continue loop to check flag.
            continue;
        }
        scheduleProcess(evt.path);
    }
}

void RuntimeScanner::scheduleProcess(const std::filesystem::path& p) {
    // Capture path by value to avoid lifetime issues.
    auto submit_result = executor_.submit([p, this]() {
        this->processFile(p);
    });

    // If the executor rejected the task due to back-pressure, run it inline
    // to guarantee we never lose a discovery event during cold-scan. For
    // other errors, emit a diagnostic but keep the pipeline alive.
    if (!submit_result) {
        if (submit_result.error() == foundation::KaiError::BackPressure) {
            DBG("RuntimeScanner: executor back-pressure – processing inline for " << p);
            this->processFile(p);
        } else {
            ERR("RuntimeScanner: executor submit failed for " << p << " error=" << static_cast<int>(submit_result.error()));
        }
    } else {
        // We intentionally detach from the returned future – cold-scan does
        // not require result synchronisation. Holding the future would add
        // allocation pressure and offer no benefit here.
        (void)submit_result.value();
    }
}

// --------------------------- processFile ----------------------------------
void RuntimeScanner::processFile(const std::filesystem::path& p) {
    // ------------------------------------------------------------------
    // Canonicalise the path to collapse /var ↔ /private/var aliases and
    // strip any "./" or symlink components that would otherwise bypass
    // our string-based deduplication.  If resolution fails we fall back
    // to the original path to avoid dropping legitimate events.
    // Additionally ignore anything that is **not** a regular file – this
    // prevents directory events from being treated as runtimes.
    // ------------------------------------------------------------------
    std::error_code ec;
    std::filesystem::path canon = std::filesystem::weakly_canonical(p, ec);

    if (ec) {
        DBG("RuntimeScanner: weakly_canonical failed for " << p << " : " << ec.message());
        canon = p; // best-effort fallback
        ec.clear();
    }

    if (!std::filesystem::is_regular_file(canon, ec) || ec) {
        return; // skip non-regular files (directories, sockets, etc.)
    }

    // Fast-path deduplication: ensure each path is processed only once. This
    // guarantees that a single RuntimeDiscoveredEvent is published per
    // runtime even if FSEvents delivers multiple modify events after the file
    // is read (APFS inode metadata updates, Spotlight, etc.).
    {
        std::string path_str = canon.string();
        std::lock_guard<std::mutex> guard(processed_mtx_);
        auto [it, inserted] = processed_paths_.insert(path_str);
        if (!inserted) {
            // Already discovered – skip any further work to avoid duplicate
            // events and unnecessary hashing I/O.
            return;
        }
    }

    // Compute SHA-256 (best effort, falls back to FNV64 repeated).
    std::array<uint8_t, 32> sha{};
#ifdef __APPLE__
    {
        CC_SHA256_CTX ctx;
        CC_SHA256_Init(&ctx);
        std::ifstream in(canon, std::ios::binary);
        if (in) {
            char buf[4096];
            while (in) {
                in.read(buf, sizeof(buf));
                std::streamsize n = in.gcount();
                if (n > 0) CC_SHA256_Update(&ctx, buf, static_cast<CC_LONG>(n));
            }
            CC_SHA256_Final(sha.data(), &ctx);
        }
    }
#else
    {
        uint64_t h = fnv1a64(canon.string());
        std::memcpy(sha.data(), &h, sizeof(h));
        // repeat pattern to fill 32 bytes
        for (size_t i = sizeof(h); i < sha.size(); i += sizeof(h)) {
            std::memcpy(sha.data() + i, &h, sizeof(h));
        }
    }
#endif

    // Determine runtime type by extension.
    RuntimeType type = RuntimeType::kUnknown;
    auto ext = canon.extension().string();
    if (ext == ".wasm") type = RuntimeType::kWebAssembly;
    else if (ext == ".js") type = RuntimeType::kJavaScript;
#ifdef __APPLE__
    else if (ext == ".app" || ext == ".dylib") type = RuntimeType::kNative;
#else
    else if (ext == ".so" || ext == "" || ext == ".exe") type = RuntimeType::kNative;
#endif

    // Fetch mtime
    std::chrono::system_clock::time_point tp = std::chrono::system_clock::now();
    (void)ec; // suppress unused warning; precise mtime not critical for bench

    auto evt = std::make_shared<RuntimeDiscoveredEvent>();
    evt->descriptor.type   = type;
    evt->descriptor.path   = canon;
    evt->descriptor.sha256 = sha;
    evt->descriptor.mtime  = tp;

    // ----------------------- Manifest capability extraction ----------------
    try {
        std::filesystem::path manifest = canon;
        manifest.replace_extension(".manifest.json");
        if (std::filesystem::exists(manifest)) {
            std::ifstream jf(manifest);
            nlohmann::json j = nlohmann::json::parse(jf, nullptr, true, true);
            if (j.contains("capabilities") && j["capabilities"].is_array()) {
                Mask128 mask;
                for (const auto& item : j["capabilities"]) {
                    if (!item.is_string()) continue;
                    auto capOpt = fromString(item.get<std::string_view>());
                    if (capOpt && static_cast<uint8_t>(*capOpt) < 128) {
                        mask.set(static_cast<uint8_t>(*capOpt));
                    }
                }
                evt->descriptor.caps = mask;
            }
        }
    } catch (const std::exception& ex) {
        DBG("RuntimeScanner: manifest parse failed for " << canon << " : " << ex.what());
    }

    // Publish with simple bounded retry to avoid event loss when the EventBus
    // is compiled with a small bounded capacity (e.g. unit-tests with
    // KAI_EVENTBUS_CAPACITY=8).  We retry on BackPressure using an adaptive
    // yield/sleep strategy that remains allocation-free and keeps latency low
    // for the common case (attempt 0 succeeds).
    for (int attempt = 0;; ++attempt) {
        auto res = bus_.publish(std::static_pointer_cast<const RuntimeDiscoveredEvent>(evt)); // cast to const
        if (res) {
            break; // delivered
        }

        if (res.error() != foundation::KaiError::BackPressure) {
            // Unexpected failure – log and give up to avoid livelock.
            ERR("RuntimeScanner: EventBus publish failed for " << canon << " err=" << static_cast<int>(res.error()));
            break;
        }

        // Back-pressure: queue full. Employ progressive back-off.
        if (attempt < 4) {
            std::this_thread::yield();
        } else {
            std::this_thread::sleep_for(std::chrono::microseconds(50));
        }
    }
}

} // namespace launcher::core::runtime 