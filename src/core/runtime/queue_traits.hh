/*
 * @file queue_traits.hh
 * @brief Generic queue concept and shared statistics struct used across Kai runtime.
 */
#pragma once

#include <cstddef>
#include <cstdint>
#include <concepts>
#include <type_traits>

namespace launcher::core::runtime {

// ---------------------------------------------------------------------------
// QueueStats – common statistics every queue backend should provide.
//     hit               – successful push operations (producer side).
//     miss              – failed pop attempts when queue was empty.
//     drop              – push operations rejected because the queue was full.
//     high_water_pct    – highest utilisation percentage ever observed (0-100).
// ---------------------------------------------------------------------------
struct QueueStats {
    std::uint64_t hit{0};
    std::uint64_t miss{0};
    std::uint64_t drop{0};
    std::uint32_t high_water_pct{0};
};

// ---------------------------------------------------------------------------
// EventQueue concept – compile-time contract a queue backend must satisfy.
// ---------------------------------------------------------------------------

template <typename Q>
concept EventQueue = requires(Q q, typename Q::value_type v) {
    // Non-blocking try-push returns false when the queue is full.
    { q.try_push(std::move(v)) } -> std::same_as<bool>;

    // Blocking pop that waits until an element is available.
    { q.wait_pop(v) } -> std::same_as<void>;

    // Fast non-blocking pop; returns false when queue empty.
    { q.try_pop(v) } -> std::same_as<bool>;

    { q.size() }     -> std::same_as<std::size_t>;
    { q.capacity() } -> std::same_as<std::size_t>;
    { q.stats() }    -> std::convertible_to<QueueStats>;
};

} // namespace launcher::core::runtime 