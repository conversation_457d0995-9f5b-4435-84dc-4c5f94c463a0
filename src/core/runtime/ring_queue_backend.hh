/*
 * @file ring_queue_backend.hh
 * @brief Lock-free fixed-size multi-producer single-consumer ring buffer.
 *
 * Design notes:
 *   • Capacity is a compile-time power-of-two constant `kCap` – ensures
 *     cheap masking instead of expensive modulo.
 *   • Producers reserve a slot by CAS-incrementing `tail_` atomically.
 *   • Consumer owns `head_` and drains in order, using `std::atomic_flag`
 *     array (`slot_ready_`) for per-slot availability signalling.
 *   • Blocking wait_pop() leverages C++23 futex (`atomic::wait`/`notify_one`)
 *     avoiding heavyweight condition variables or dynamic allocation.
 *   • Zero steady-state heap allocations – storage is `std::optional<T>` per
 *     slot, constructed once.
 */
#pragma once

#include <array>
#include <atomic>
#include <cassert>
#include <cstddef>
#include <cstdint>
#include <optional>
#include <type_traits>
#include <utility>
#include <chrono>
#include <thread>

#include "queue_traits.hh"

namespace launcher::core::runtime {

namespace detail {
constexpr bool isPowerOfTwo(std::size_t n) { return (n != 0) && ((n & (n - 1)) == 0); }
} // namespace detail

// ---------------------------------------------------------------------------
// RingQueueBackend – template parameters:
//   T       – value_type stored in the queue.
//   kCap    – capacity (must be power-of-two and >0).
// ---------------------------------------------------------------------------

template <typename T, std::size_t kCap>
class RingQueueBackend {
    static_assert(detail::isPowerOfTwo(kCap), "RingQueueBackend capacity must be power-of-two");

 public:
    using value_type = T;

    [[nodiscard]] constexpr std::size_t capacity() const noexcept { return kCap; }

    // ----------------------------- try_push --------------------------------
    // Non-blocking push. Returns false when the buffer is full.
    // Lock-free (producers only touch tail_ and slot_ready_ flag).
    bool try_push(T&& item) noexcept {
        std::size_t head_snapshot = head_.load(std::memory_order_acquire);
        std::size_t tail = tail_.load(std::memory_order_relaxed);
        if ((tail - head_snapshot) >= kCap) {
            stats_.drop.fetch_add(1, std::memory_order_relaxed);
            return false;  // full
        }

        // Reserve index by CAS.
        while (!tail_.compare_exchange_weak(tail, tail + 1, std::memory_order_acq_rel, std::memory_order_relaxed)) {
            // Refresh head snapshot on each failed attempt to avoid stale view
            head_snapshot = head_.load(std::memory_order_acquire);
            if ((tail - head_snapshot) >= kCap) {
                stats_.drop.fetch_add(1, std::memory_order_relaxed);
                return false; // full after refresh
            }
        }

        const std::size_t idx = tail & kMask;
        buffer_[idx].emplace(std::move(item));
        slot_ready_[idx].store(true, std::memory_order_release);
        slot_ready_[idx].notify_one();

        // Update stats.
        const std::size_t sz = tail - head_snapshot + 1; // size after push
        const std::uint32_t pct = static_cast<std::uint32_t>((sz * 100) / kCap);
        // Update high-water mark without risk of infinite CAS retry: if the
        // new percentage exceeds the previously recorded value perform a
        // single relaxed store.  Occasional lost updates are acceptable for
        // this best-effort statistic.
        const auto prev_hw = stats_.high_water_pct.load(std::memory_order_relaxed);
        if (pct > prev_hw) {
            stats_.high_water_pct.store(pct, std::memory_order_relaxed);
        }
        stats_.hit.fetch_add(1, std::memory_order_relaxed);
        return true;
    }

    // Convenience overload for copyable types – forwards to rvalue version.
    bool try_push(const T& item) noexcept requires(std::is_copy_constructible_v<T>) {
        T copy = item;
        return try_push(std::move(copy));
    }

    // ----------------------------- try_pop ---------------------------------
    // Non-blocking pop for the consumer thread. Returns false when empty.
    bool try_pop(T& out) noexcept {
        const std::size_t head = head_.load(std::memory_order_relaxed);
        const std::size_t idx  = head & kMask;
        if (!slot_ready_[idx].load(std::memory_order_acquire)) {
            stats_.miss.fetch_add(1, std::memory_order_relaxed);
            return false; // empty
        }

        // Element ready – move & clear.
        out = std::move(*buffer_[idx]);
        buffer_[idx].reset();
        slot_ready_[idx].store(false, std::memory_order_release);

        head_.store(head + 1, std::memory_order_release);
        return true;
    }

    // ----------------------------- wait_pop --------------------------------
    // Blocking pop for the consumer thread. Utilises futex-like atomic wait.
    void wait_pop(T& out) noexcept {
        std::size_t head_local = head_.load(std::memory_order_relaxed);
        std::size_t idx        = head_local & kMask;
        // Wait until slot becomes ready.
        while (!slot_ready_[idx].load(std::memory_order_acquire)) {
            slot_ready_[idx].wait(false, std::memory_order_relaxed);
        }

        // Element is ready.
        out = std::move(*buffer_[idx]);
        buffer_[idx].reset();
        slot_ready_[idx].store(false, std::memory_order_release);

        head_.store(head_local + 1, std::memory_order_release);
    }

    // ----------------------------- size ------------------------------------
    [[nodiscard]] std::size_t size() const noexcept {
        const std::size_t head_snapshot = head_.load(std::memory_order_acquire);
        const std::size_t tail_snapshot = tail_.load(std::memory_order_acquire);
        return tail_snapshot - head_snapshot;
    }

    // ----------------------------- stats -----------------------------------
    [[nodiscard]] QueueStats stats() const noexcept {
        QueueStats s{};
        s.hit             = stats_.hit.load(std::memory_order_relaxed);
        s.miss            = stats_.miss.load(std::memory_order_relaxed);
        s.drop            = stats_.drop.load(std::memory_order_relaxed);
        s.high_water_pct  = stats_.high_water_pct.load(std::memory_order_relaxed);
        return s;
    }

    // Expose as MetricSource concept provider (see util/metrics.hh).
    static constexpr bool is_metric_source = true;

 private:
    static constexpr std::size_t kMask = kCap - 1;

    // Array for element presence flags. Using atomic<bool> which has well-defined
    // specialisation allowing wait/notify.
    std::array<std::atomic<bool>, kCap> slot_ready_{}; // value-initialised to false

    // Storage for actual elements.
    std::array<std::optional<T>, kCap> buffer_{};

    // Indexes.
    alignas(64) std::atomic<std::size_t> head_{0}; // consumer only
    alignas(64) std::atomic<std::size_t> tail_{0}; // producers

    // Statistics (atomic to avoid false sharing with data path indices).
    struct AtomicStats {
        std::atomic<std::uint64_t> hit{0};
        std::atomic<std::uint64_t> miss{0};
        std::atomic<std::uint64_t> drop{0};
        std::atomic<std::uint32_t> high_water_pct{0};
    } stats_;
};

} // namespace launcher::core::runtime
 