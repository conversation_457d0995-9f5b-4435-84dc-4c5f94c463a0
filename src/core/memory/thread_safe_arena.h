#pragma once

#include "slab_arena.h"
#include <mutex>

namespace launcher::core::memory {

// ThreadSafeSlabArena – thin wrapper adding a mutex around SlabArena to
// support concurrent writers.  Use when multiple producer threads write to
// the same sink.  Holds internal SlabArena and guards all mutating calls.
class ThreadSafeSlabArena : public MemorySink {
 public:
    ThreadSafeSlabArena() {
#ifndef NDEBUG
        arena_.disableThreadCheck();
#endif
    }

    // Append bytes thread-safely; returns view stable for lifetime of arena.
    std::string_view append(const char* data, std::size_t len) override {
        std::lock_guard<std::mutex> lk(mtx_);
        return arena_.append(data, len);
    }

    std::string_view append(std::string_view sv) {
        std::lock_guard<std::mutex> lk(mtx_);
        return arena_.append(sv);
    }

    void copyTo(std::string& out) const {
        std::lock_guard<std::mutex> lk(mtx_);
        arena_.copyTo(out);
    }

    void clear() {
        std::lock_guard<std::mutex> lk(mtx_);
        arena_.clear();
    }

    std::size_t size() const noexcept {
        std::lock_guard<std::mutex> lk(mtx_);
        return arena_.size();
    }

 private:
    mutable std::mutex mtx_;
    SlabArena arena_;
};

} // namespace launcher::core::memory 