/*
 * @file global_new_delete.cpp
 * @brief Overrides global new/delete to route through kai arena allocator (rpmalloc/mimalloc).
 *        Can be disabled by defining KAI_DISABLE_GLOBAL_NEW.
 */

#ifndef KAI_DISABLE_GLOBAL_NEW

#include "arena_allocator.h"
#include <new> // std::bad_alloc, std::nothrow_t

namespace kai_mem = launcher::core::memory;

// ---------------------------------------------------------------------------
// operator new
// ---------------------------------------------------------------------------

void* operator new(std::size_t size) {
    if (void* p = kai_mem::alloc(size)) {
        return p;
    }
    throw std::bad_alloc();
}

void* operator new[](std::size_t size) {
    if (void* p = kai_mem::alloc(size)) {
        return p;
    }
    throw std::bad_alloc();
}

void* operator new(std::size_t size, const std::nothrow_t&) noexcept {
    return kai_mem::alloc(size);
}

void* operator new[](std::size_t size, const std::nothrow_t&) noexcept {
    return kai_mem::alloc(size);
}

// ---------------------------------------------------------------------------
// operator delete
// ---------------------------------------------------------------------------

void operator delete(void* ptr) noexcept {
    kai_mem::free(ptr);
}

void operator delete[](void* ptr) noexcept {
    kai_mem::free(ptr);
}

void operator delete(void* ptr, const std::nothrow_t&) noexcept {
    kai_mem::free(ptr);
}

void operator delete[](void* ptr, const std::nothrow_t&) noexcept {
    kai_mem::free(ptr);
}

// Sized delete (C++14+). GCC/Clang require both sized & unsized forms.
void operator delete(void* ptr, std::size_t) noexcept {
    kai_mem::free(ptr);
}

void operator delete[](void* ptr, std::size_t) noexcept {
    kai_mem::free(ptr);
}

// ---------------------------------------------------------------------------
// operator new/delete with alignment (C++17)
// ---------------------------------------------------------------------------

void* operator new(std::size_t size, std::align_val_t align) {
    if (void* p = kai_mem::allocAligned(size, static_cast<std::size_t>(align))) {
        return p;
    }
    throw std::bad_alloc();
}

void* operator new[](std::size_t size, std::align_val_t align) {
    if (void* p = kai_mem::allocAligned(size, static_cast<std::size_t>(align))) {
        return p;
    }
    throw std::bad_alloc();
}

void* operator new(std::size_t size, std::align_val_t align, const std::nothrow_t&) noexcept {
    return kai_mem::allocAligned(size, static_cast<std::size_t>(align));
}

void* operator new[](std::size_t size, std::align_val_t align, const std::nothrow_t&) noexcept {
    return kai_mem::allocAligned(size, static_cast<std::size_t>(align));
}

void operator delete(void* ptr, std::align_val_t) noexcept {
    kai_mem::free(ptr);
}

void operator delete[](void* ptr, std::align_val_t) noexcept {
    kai_mem::free(ptr);
}

void operator delete(void* ptr, std::size_t, std::align_val_t) noexcept {
    kai_mem::free(ptr);
}

void operator delete[](void* ptr, std::size_t, std::align_val_t) noexcept {
    kai_mem::free(ptr);
}

#endif // !KAI_DISABLE_GLOBAL_NEW 