# Memory module split into its own static library – isolates allocator related
# code so that edits rebuild only this archive.

set(MEMORY_SOURCES
    arena_allocator_service.cpp
    global_new_delete.cpp
)

# OBJECT library holds the compilation units (can be reused by other static
# libs to avoid object duplication).
add_library(core_memory_obj OBJECT ${MEMORY_SOURCES})
add_dependencies(core_memory_obj generate_service_headers)

# Inherit allocator header paths (rpmalloc or mimalloc)
target_include_directories(core_memory_obj
    PUBLIC ${CMAKE_CURRENT_SOURCE_DIR}
)

# Propagate allocator include directories to compilation of this object lib.
if(KAI_ALLOCATOR_LIB)
    target_link_libraries(core_memory_obj PUBLIC ${KAI_ALLOCATOR_LIB})
endif()

# Thin STATIC façade for downstream users (preserves original semantics of a
# separate .a archive on the link line).
add_library(core_memory STATIC)

# Link object files and allocator backend into the archive.
# `KAI_ALLOCATOR_LIB` is provided by the top-level build or preset.
if(KAI_ALLOCATOR_LIB)
    target_link_libraries(core_memory PUBLIC core_memory_obj ${KAI_ALLOCATOR_LIB})
else()
    target_link_libraries(core_memory PUBLIC core_memory_obj)
endif() 