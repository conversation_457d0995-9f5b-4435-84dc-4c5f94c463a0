// memory_sink.h – abstract sink for append-only byte storage.
#pragma once
#include <cstddef>
#include <string_view>

namespace launcher::core::memory {

struct MemorySink {
    virtual ~MemorySink() = default;
    // Append |len| bytes from |data| returning stable view valid until sink destroyed/cleared.
    virtual std::string_view append(const char* data, std::size_t len) = 0;
};

} // namespace launcher::core::memory 