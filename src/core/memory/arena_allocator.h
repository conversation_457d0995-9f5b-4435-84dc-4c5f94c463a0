/*
 * @file arena_allocator.h
 * @brief Header-only façade over rpmalloc/mimalloc providing kai::memory alloc/free API.
 */
#pragma once

#include <cstddef>
#include <cstdint>
#include <atomic>
#include <mutex>

// Choose backend -------------------------------------------------------------
// Default to rpmalloc unless build system defines KAI_USE_MIMALLOC (see CMake).
#if defined(KAI_USE_MIMALLOC)
// mimalloc headers are C, ensure extern "C" safety
extern "C" {
#include <mimalloc.h>
}
#else // rpmalloc (default)
extern "C" {
#include <rpmalloc.h>
}
#endif

namespace launcher::core::memory {

struct ArenaStats {
    std::size_t reserved{0};   // Bytes reserved from the OS (mapped)
    std::size_t committed{0};  // Bytes committed/active
    std::size_t allocated{0};  // Bytes currently allocated (in use)
    std::size_t frees{0};      // Total number of frees since init
    std::size_t allocs{0};     // Total number of allocs since init
};

// ---------------------------------------------------------------------------
// Internal globals
// ---------------------------------------------------------------------------

#if !defined(KAI_USE_MIMALLOC)
// Flag indicating whether rpmalloc global init has happened through
// ArenaAllocatorSvc or through lazy path. Header-inline variable.
inline std::atomic<bool> g_rpmalloc_initialized{false};
#endif

// Counters that are always updated, even if the underlying allocator does not
// expose per-thread/global count APIs. We use relaxed atomics because ordering
// semantics are not critical for coarse statistics.
inline std::atomic<std::size_t> _g_allocs{0};
inline std::atomic<std::size_t> _g_frees{0};

// ---------------------------------------------------------------------------
// Thread-local current heap pointer (rpmalloc backend only). When null, global
// heap is used.  Updated by PluginHeapScope RAII helper.
// ---------------------------------------------------------------------------
#if !defined(KAI_USE_MIMALLOC) && defined(RPMALLOC_FIRST_CLASS_HEAPS) && (RPMALLOC_FIRST_CLASS_HEAPS)
extern thread_local rpmalloc_heap_t* tls_current_heap;
#else
// When per-heap support is unavailable, fallback to nullptr typed as void*
extern thread_local void* tls_current_heap;
#endif

// ---------------------------------------------------------------------------
// Allocation helpers (header-only, always inline for zero overhead)
// ---------------------------------------------------------------------------
#if !defined(KAI_USE_MIMALLOC)
// Helper RAII object ensuring rpmalloc_thread_finalize() is invoked when a thread
// exits.  This avoids false-positive leak reports in tools such as ASan/LSan
// while adding zero runtime overhead in the fast path.
struct RpmallocThreadDestructor {
    ~RpmallocThreadDestructor() noexcept { rpmalloc_thread_finalize(/*release_caches=*/0); }
};

inline void _touchThreadDestructor() noexcept {
    // The mere act of referencing this thread_local guarantees construction
    // on first use and destruction at thread exit.
    thread_local RpmallocThreadDestructor _rpmalloc_tls_dtor;
    (void)_rpmalloc_tls_dtor;
}
#endif

#if !defined(KAI_USE_MIMALLOC)
inline void ensureInit() noexcept {
    // Fast path: already globally initialised.
    if (g_rpmalloc_initialized.load(std::memory_order_acquire)) {
        rpmalloc_thread_initialize();
        _touchThreadDestructor();
        return;
    }

    // Slow path: perform global initialisation exactly once.
    static std::once_flag init_flag;
    std::call_once(init_flag, [] {
        if (rpmalloc_initialize() == 0) {
            g_rpmalloc_initialized.store(true, std::memory_order_release);
        }
    });

    rpmalloc_thread_initialize();
    _touchThreadDestructor();
}
#endif  // ensureInit() function

#if !defined(KAI_USE_MIMALLOC) && defined(RPMALLOC_FIRST_CLASS_HEAPS) && (RPMALLOC_FIRST_CLASS_HEAPS)
// Forward declarations (implemented in arena_allocator_service.cpp)
void _updateHeapMetrics(rpmalloc_heap_t* heap, intptr_t delta) noexcept;
void _incHeapAllocs(rpmalloc_heap_t* heap) noexcept;
#define KAI_HEAP_UPDATE_BYTES(h, d) ::launcher::core::memory::_updateHeapMetrics((h), (d))
#define KAI_HEAP_INC_ALLOCS(h)     ::launcher::core::memory::_incHeapAllocs((h))
#else
#define KAI_HEAP_UPDATE_BYTES(h, d) ((void)0)
#define KAI_HEAP_INC_ALLOCS(h)     ((void)0)
#endif

[[nodiscard]] inline void* alloc(std::size_t bytes) noexcept {
#if defined(KAI_USE_MIMALLOC)
    _g_allocs.fetch_add(1, std::memory_order_relaxed);
    return mi_malloc(bytes);
#else
    ensureInit();
    _g_allocs.fetch_add(1, std::memory_order_relaxed);
    #if defined(RPMALLOC_FIRST_CLASS_HEAPS) && (RPMALLOC_FIRST_CLASS_HEAPS)
    if (tls_current_heap) {
        void* p = rpmalloc_heap_alloc(static_cast<rpmalloc_heap_t*>(tls_current_heap), bytes);
        if (p) {
            size_t u = rpmalloc_usable_size(p);
            KAI_HEAP_UPDATE_BYTES(static_cast<rpmalloc_heap_t*>(tls_current_heap), static_cast<intptr_t>(u));
            KAI_HEAP_INC_ALLOCS(static_cast<rpmalloc_heap_t*>(tls_current_heap));
        }
        return p;
    }
    #endif
    void* p = rpmalloc(bytes);
    return p;
#endif
}

inline void free(void* p) noexcept {
#if defined(KAI_USE_MIMALLOC)
    if (!p) return;
    _g_frees.fetch_add(1, std::memory_order_relaxed);
    mi_free(p);
#else
    if (!p) return;
    ensureInit();
    _g_frees.fetch_add(1, std::memory_order_relaxed);
    rpmalloc_heap_t* heap_for_ptr = nullptr;
    #if defined(RPMALLOC_FIRST_CLASS_HEAPS) && (RPMALLOC_FIRST_CLASS_HEAPS)
    heap_for_ptr = rpmalloc_get_heap_for_ptr(p);
    #endif
    size_t u = rpmalloc_usable_size(p);
    rpfree(p);
    #if defined(RPMALLOC_FIRST_CLASS_HEAPS) && (RPMALLOC_FIRST_CLASS_HEAPS)
    if (heap_for_ptr) {
        KAI_HEAP_UPDATE_BYTES(heap_for_ptr, -static_cast<intptr_t>(u));
    }
    #endif
#endif
}

// Collect global statistics and map to uniform ArenaStats structure.
[[nodiscard]] inline ArenaStats getStats() noexcept {
    ArenaStats s{};
#if defined(KAI_USE_MIMALLOC)
    mi_stats_t mst{};
    mi_stats_merge(&mst);
    s.reserved  = mst.reserved;
    s.committed = mst.committed;
    s.allocated = mst.active;
    s.allocs    = mst.total.allocations;
    s.frees     = mst.total.frees;
#else
    rpmalloc_global_statistics_t gst{};
    rpmalloc_global_statistics(&gst);
    s.reserved  = gst.mapped;
    s.committed = gst.mapped;  // rpmalloc doesn't track commit vs reserve separately
    s.allocated = (gst.mapped > gst.cached) ? (gst.mapped - gst.cached) : 0; // rough approximation

    // Prefer our own counters which are guaranteed to be monotonic regardless
    // of backend internals.
    s.allocs = _g_allocs.load(std::memory_order_relaxed);
    s.frees  = _g_frees.load(std::memory_order_relaxed);
#endif
    return s;
}

// ---------------------------------------------------------------------------
// Aligned allocation
// ---------------------------------------------------------------------------
[[nodiscard]] inline void* allocAligned(std::size_t bytes, std::size_t alignment) noexcept {
#if defined(KAI_USE_MIMALLOC)
    _g_allocs.fetch_add(1, std::memory_order_relaxed);
    return mi_malloc_aligned(bytes, alignment);
#else
    ensureInit();
    _g_allocs.fetch_add(1, std::memory_order_relaxed);
    #if defined(RPMALLOC_FIRST_CLASS_HEAPS) && (RPMALLOC_FIRST_CLASS_HEAPS)
    if (tls_current_heap) {
        void* p = rpmalloc_heap_aligned_alloc(static_cast<rpmalloc_heap_t*>(tls_current_heap), alignment, bytes);
        if (p) {
            size_t u = rpmalloc_usable_size(p);
            KAI_HEAP_UPDATE_BYTES(static_cast<rpmalloc_heap_t*>(tls_current_heap), static_cast<intptr_t>(u));
            KAI_HEAP_INC_ALLOCS(static_cast<rpmalloc_heap_t*>(tls_current_heap));
        }
        return p;
    }
    #endif
    void* p = rpaligned_alloc(alignment, bytes);
    return p;
#endif
}

} // namespace launcher::core::memory 