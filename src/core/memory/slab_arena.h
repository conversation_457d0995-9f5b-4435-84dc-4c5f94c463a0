// slab_arena.h – fixed-size block arena for zero-realloc appends
#pragma once

#include <deque>
#include <memory>
#include <string_view>
#include <cstring>
#include <cassert>
#include <algorithm>
#include "core/util/debug.h"
#include "core/memory/memory_sink.h"
#include <thread>

namespace launcher::core::memory {

// Fixed-size slab allocator optimised for append-only byte storage.
// Blocks are never freed until clear()/~SlabArena().  All pointers
// returned by append() remain valid for the lifetime of the arena
// (or until clear()).  Suitable for SSE decoders where small strings
// must outlive the producing function without incurring realloc-moves.
class SlabArena : public MemorySink {
 public:
    static constexpr std::size_t kBlockSize = 32 * 1024; // 32 KiB

    SlabArena() = default;
    SlabArena(const SlabArena&) = delete;
    SlabArena& operator=(const SlabArena&) = delete;

    // Append |data| of length |len| and return stable view.
    [[nodiscard]] std::string_view append(const char* data, std::size_t len) override {
        if (len == 0) return {};
        ensureCapacity(len);
        char* dest = currentBlockPtr_ + currentOffset_;
        std::memcpy(dest, data, len);
        currentOffset_ += len;
        totalSize_ += len;
        used_sizes_.back() = currentOffset_;
        return std::string_view(dest, len);
    }

    [[nodiscard]] std::string_view append(std::string_view sv) {
        return append(sv.data(), sv.size());
    }

    // Allocate a raw buffer of 'len' bytes. Returns a pointer to the allocated
    // buffer. The memory is NOT initialized. The caller must not write past 'len' bytes.
    // The returned pointer is stable until the arena is cleared or destroyed.
    [[nodiscard]] char* alloc_buffer(std::size_t len) {
        if (len == 0) return nullptr;
        ensureCapacity(len);
        char* dest = currentBlockPtr_ + currentOffset_;
        currentOffset_ += len;
        totalSize_ += len;
        used_sizes_.back() = currentOffset_; // Update used size of the current block
        return dest;
    }

    // Total bytes stored.
    [[nodiscard]] std::size_t size() const noexcept { return totalSize_; }

    // Copy all stored bytes into |out| (appends).
    void copyTo(std::string& out) const {
        out.clear();
        out.reserve(totalSize_);
        for (std::size_t idx = 0; idx < blocks_.size(); ++idx) {
            std::size_t used = used_sizes_[idx];
            if (used == 0) {
                // Skip completely unused blocks, but do **not** break – later blocks
                // might contain data when reserveBlocks() or custom allocation
                // strategies are introduced.
                continue;
            }
            out.append(blocks_[idx].get(), used);
        }
    }

    // Discard all data and release blocks.
    void clear() noexcept {
        blocks_.clear();
        used_sizes_.clear();
        blocks_.shrink_to_fit();
        used_sizes_.shrink_to_fit();
        currentBlockPtr_ = nullptr;
        currentOffset_ = 0;
        totalSize_ = 0;
    }

    #ifndef NDEBUG
    // Allow an explicit opt-out of the single-writer thread assertion – used by
    // ThreadSafeSlabArena which guards all access with a mutex.
    void disableThreadCheck() noexcept { thread_safe_override_ = true; }
    #endif

 private:
    #ifndef NDEBUG
    std::thread::id owner_{};
    bool thread_safe_override_ = false;
    #endif

    struct AlignedDeleter {
        void operator()(char* p) const noexcept {
#if defined(__cpp_aligned_new)
            ::operator delete[](p, std::align_val_t{32});
#else
            free(p);
#endif
        }
    };

    void ensureCapacity(std::size_t len) {
        // Need new block if none yet or not enough free space in current.
        if (!currentBlockPtr_ || currentOffset_ + len > kBlockSize) {
            allocateBlock(std::max(len, kBlockSize));
        }
#ifndef NDEBUG
        if (!thread_safe_override_) {
            if (owner_ == std::thread::id()) owner_ = std::this_thread::get_id();
            assert(owner_ == std::this_thread::get_id() && "SlabArena used from multiple threads – not thread-safe");
        }
#endif
    }

    void allocateBlock(std::size_t block_size) {
        block_size = (block_size + 31) & ~std::size_t(31); // align up to 32 bytes
#if defined(__cpp_aligned_new)
        char* mem = reinterpret_cast<char*>(::operator new[](block_size, std::align_val_t{32}));
#else
        void* mem = nullptr;
        if (posix_memalign(&mem, 32, block_size) != 0) {
            throw std::bad_alloc();
        }
#endif
        blocks_.emplace_back(static_cast<char*>(mem), AlignedDeleter{});
        used_sizes_.push_back(0);
        currentBlockPtr_ = blocks_.back().get();
        currentOffset_ = 0;
    }

    // Storage ----------------------------------------------------------------
    std::deque<std::unique_ptr<char, AlignedDeleter>> blocks_;
    std::deque<std::size_t> used_sizes_;
    char*   currentBlockPtr_ {nullptr};
    std::size_t currentOffset_ {0};
    std::size_t totalSize_ {0};
};

} // namespace launcher::core::memory 