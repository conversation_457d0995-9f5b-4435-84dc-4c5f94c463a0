#include "arena_allocator_service.h"
#include "arena_allocator.h"
#include "core/util/result.h"

#if defined(KAI_USE_MIMALLOC)
// mimalloc initialisation functions are C, include header here as well
extern "C" {
#include <mimalloc.h>
}
#else
extern "C" {
#include <rpmalloc.h>
}
#endif

#include "../util/debug.h"

#include <unordered_map>
#include <mutex>
#include <vector>
#include <string>
#include <atomic>

namespace launcher::core::memory {

using ::launcher::core::util::Result;

#if !defined(KAI_USE_MIMALLOC) && defined(RPMALLOC_FIRST_CLASS_HEAPS) && (RPMALLOC_FIRST_CLASS_HEAPS)
// Define thread_local pointer declared in arena_allocator.h
thread_local rpmalloc_heap_t* tls_current_heap = nullptr;
#else
thread_local void* tls_current_heap = nullptr;
#endif

#if !defined(KAI_USE_MIMALLOC) && defined(RPMALLOC_FIRST_CLASS_HEAPS) && (RPMALLOC_FIRST_CLASS_HEAPS)
using HeapPtr = rpmalloc_heap_t*;

// Forward declaration
static void cleanupHeapTracking() noexcept;
#endif

#if !defined(KAI_USE_MIMALLOC) && defined(RPMALLOC_FIRST_CLASS_HEAPS) && (RPMALLOC_FIRST_CLASS_HEAPS)
namespace {
struct HeapEntry {
    std::string plugin_id;
    HeapPtr     heap;
};
}
#endif

//--------------------------------------------------------------------
// IService life-cycle
//--------------------------------------------------------------------

Result<void> ArenaAllocatorSvc::start() {
#if defined(KAI_USE_MIMALLOC)
    // Mimalloc auto-initialises on first call, but we reset stats to start fresh.
    mi_stats_reset();
    DBG("ArenaAllocatorSvc: mimalloc initialised");
    return Result<void>::success();
#else
    int rc = rpmalloc_initialize();
    if (rc != 0) {
        ERR("ArenaAllocatorSvc: rpmalloc_initialize failed with code " << rc);
        return Result<void>::failure("rpmalloc_initialize failed");
    }
    g_rpmalloc_initialized.store(true, std::memory_order_release);
    DBG("ArenaAllocatorSvc: rpmalloc initialised");
    return Result<void>::success();
#endif
}

void ArenaAllocatorSvc::stop() noexcept {
#if defined(KAI_USE_MIMALLOC)
    // Collect pending frees and release OS pages where possible
    mi_collect(true);
    DBG("ArenaAllocatorSvc: mimalloc finalised");
#else
#if defined(RPMALLOC_FIRST_CLASS_HEAPS) && (RPMALLOC_FIRST_CLASS_HEAPS)
    cleanupHeapTracking();
#endif
    rpmalloc_finalize();
    g_rpmalloc_initialized.store(false, std::memory_order_release);
    DBG("ArenaAllocatorSvc: rpmalloc finalised");
#endif
}

ArenaStats ArenaAllocatorSvc::stats() const noexcept {
    return getStats();
}

#if !defined(KAI_USE_MIMALLOC) && defined(RPMALLOC_FIRST_CLASS_HEAPS) && (RPMALLOC_FIRST_CLASS_HEAPS)

// --------------------------------------------------------------------
// Per-plugin heap tracking
// --------------------------------------------------------------------

// Recursive mutex is required because these metric helpers can be invoked
// re-entrantly from inside allocation/free paths that themselves allocate
// (e.g. when `std::mutex::lock` throws and the runtime allocates the
// `system_error` object). A plain `std::mutex` would throw again, causing
// infinite recursion and eventual stack-overflow. The additional cost is
// negligible on this slow-path.
static std::recursive_mutex g_heap_mutex;
// Runtime flag to disable heap tracking when RPMALLOC_FIRST_CLASS_HEAPS=0
static std::atomic<bool> g_heap_tracking_enabled{true};

struct HeapMeta {
    std::string plugin_id;
    std::size_t allocated{0};
    std::size_t alloc_count{0};
};

// Intentionally leak – avoids use-after-destroy during shutdown when rpmalloc
// may still invoke our hooks after global destructors have run.
static auto* g_heap_map = new std::unordered_map<HeapPtr, HeapMeta>(); // heap → metadata

static bool isHeapTrackingEnabled() {
    return g_heap_tracking_enabled.load(std::memory_order_acquire);
}

HeapPtr ArenaAllocatorSvc::acquireHeap(const std::string& plugin_id) {
    if (!isHeapTrackingEnabled()) {
        // Fall back to default heap when tracking is disabled
        ensureInit();
        return nullptr;
    }
    ensureInit();
    HeapPtr h = rpmalloc_heap_acquire();
    {
        std::unique_lock<std::recursive_mutex> lk(g_heap_mutex, std::try_to_lock);
        if (!lk.owns_lock()) return nullptr;
        if (g_heap_map) g_heap_map->emplace(h, HeapMeta{plugin_id});
    }
    DBG("ArenaAllocatorSvc: heap acquired for plugin " << plugin_id << ", ptr=" << h);
    return h;
}

void ArenaAllocatorSvc::releaseHeap(HeapPtr heap) {
    if (!heap || !g_heap_map || !isHeapTrackingEnabled()) return;
    {
        std::unique_lock<std::recursive_mutex> lk(g_heap_mutex, std::try_to_lock);
        if (!lk.owns_lock()) return;
        if (g_heap_map) g_heap_map->erase(heap);
    }
    rpmalloc_heap_release(heap);
    DBG("ArenaAllocatorSvc: heap released ptr=" << heap);
}

// Per-heap statistics disabled pending rpmalloc public API support

std::size_t ArenaAllocatorSvc::heapCount() const noexcept {
    if (!isHeapTrackingEnabled() || !g_heap_map) return 0;
    std::unique_lock<std::recursive_mutex> lk(g_heap_mutex, std::try_to_lock);
    if (!lk.owns_lock()) return 0;
    return g_heap_map->size();
}

std::vector<ArenaAllocatorSvc::HeapStats> ArenaAllocatorSvc::allHeapStats() const {
    std::vector<HeapStats> out;
    if (!isHeapTrackingEnabled() || !g_heap_map) return out;
    std::unique_lock<std::recursive_mutex> lk(g_heap_mutex, std::try_to_lock);
    if (!lk.owns_lock()) return out;
    out.reserve(g_heap_map->size());
    for (const auto& [heap, meta] : *g_heap_map) {
        // rpmalloc lacks per-heap stats pre-1.5; populate zeros for now.
        out.push_back(HeapStats{meta.plugin_id, meta.allocated, meta.alloc_count});
    }
    return out;
}

// ---------------- Metric update helpers -----------------------------

void _updateHeapMetrics(rpmalloc_heap_t* heap, intptr_t bytes_delta) noexcept {
    if (!heap || !g_heap_map || !isHeapTrackingEnabled()) return;
    std::unique_lock<std::recursive_mutex> lk(g_heap_mutex, std::try_to_lock);
    if (!lk.owns_lock()) return;
    auto it = g_heap_map->find(heap);
    if (it != g_heap_map->end()) {
        auto& meta = it->second;
        meta.allocated = static_cast<std::size_t>(static_cast<intptr_t>(meta.allocated) + bytes_delta);
    }
}

void _incHeapAllocs(rpmalloc_heap_t* heap) noexcept {
    if (!heap || !g_heap_map || !isHeapTrackingEnabled()) return;
    std::unique_lock<std::recursive_mutex> lk(g_heap_mutex, std::try_to_lock);
    if (!lk.owns_lock()) return;
    auto it = g_heap_map->find(heap);
    if (it != g_heap_map->end()) {
        ++it->second.alloc_count;
    }
}

// Disable heap tracking at runtime
void ArenaAllocatorSvc::disableHeapTracking() noexcept {
    g_heap_tracking_enabled.store(false, std::memory_order_release);
}

// Clean up heap tracking data before rpmalloc finalization
static void cleanupHeapTracking() noexcept {
    // Clear per-plugin heap map before calling rpmalloc_finalize to avoid
    // unordered_map accesses during allocator teardown.
    {
        std::unique_lock<std::recursive_mutex> lk(g_heap_mutex, std::try_to_lock);
        if (!lk.owns_lock()) return;
        if (g_heap_map) g_heap_map->clear();
    }
    // After finalisation the allocator internals are gone; disable further metric updates.
    g_heap_map = nullptr;
}

#endif // !KAI_USE_MIMALLOC

} // namespace launcher::core::memory 