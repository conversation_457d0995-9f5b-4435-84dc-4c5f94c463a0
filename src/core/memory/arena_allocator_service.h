/*
 * @file arena_allocator_service.h
 * @brief IService implementation managing global allocator backend lifecycle.
 */
#pragma once

#include "../foundation/iservice.h"
#include "arena_allocator.h"
#include "../util/debug.h"
#if !defined(KAI_USE_MIMALLOC)
#include <rpmalloc.h>
#endif
#include <string>
#include <vector>
#include <unordered_map>
#include <mutex>
#include <cstdint>
#include "../foundation/service_base.h"

namespace launcher::core::memory {

class ArenaAllocatorSvc final : public foundation::ServiceBase<ArenaAllocatorSvc> {
 public:
    static constexpr foundation::ServiceId kId = foundation::ServiceId::kArenaAllocatorSvc;

    // Constructor ---------------------------------------------------------------
    explicit ArenaAllocatorSvc(foundation::ServiceRegistry& registry)
        : ServiceBase<ArenaAllocatorSvc>(registry) {}

    // IService -----------------------------------------------------------------
    ::launcher::core::util::Result<void> start() override;
    void                             stop() noexcept override;

    // Additional API -----------------------------------------------------------
    [[nodiscard]] ArenaStats stats() const noexcept;

    // Acquire/Release API --------------------------------------------------------
#if defined(RPMALLOC_FIRST_CLASS_HEAPS) && (RPMALLOC_FIRST_CLASS_HEAPS)
    rpmalloc_heap_t* acquireHeap(const std::string& plugin_id);
    void             releaseHeap(rpmalloc_heap_t* heap);
    void             disableHeapTracking() noexcept;
#endif

#if defined(RPMALLOC_FIRST_CLASS_HEAPS) && (RPMALLOC_FIRST_CLASS_HEAPS)
    struct HeapStats {
        std::string plugin_id;
        std::size_t allocated_bytes{0};
        std::size_t alloc_count{0};
    };

    std::vector<HeapStats> allHeapStats() const;
    std::size_t heapCount() const noexcept;
#endif
};

} // namespace launcher::core::memory

#if defined(RPMALLOC_FIRST_CLASS_HEAPS) && (RPMALLOC_FIRST_CLASS_HEAPS)
namespace launcher::core::memory {
    void _updateHeapMetrics(rpmalloc_heap_t* heap, intptr_t bytes_delta) noexcept;
    void _incHeapAllocs(rpmalloc_heap_t* heap) noexcept;
}
#endif 