// zero_alloc_guard.h - Header providing KAI_VERIFY_ZERO_ALLOC macro and guard implementation
// NOTE: placed under src/core/memory/ per architecture guidelines.
// This header is header-only and incurs zero overhead when the macro is unused.
//
// Usage:
//     KAI_VERIFY_ZERO_ALLOC({
//         // code that must not allocate
//     });
// If any heap allocation routed through arena_allocator occurs within the
// lambda/body, the guard will throw std::runtime_error which can be captured by
// unit-tests. In production builds this can be disabled via
// `-DKAI_DISABLE_ZERO_ALLOC_GUARD`.
//
// Caveats:
//  • Guard observes allocations executed by the *current* thread only.
//  • If the enclosed code spawns threads and allocates from them, the guard
//    will not detect those.
#pragma once

#ifndef KAI_DISABLE_ZERO_ALLOC_GUARD

#include "core/memory/arena_allocator.h"
#include <stdexcept>
#include <thread>

namespace launcher::core::memory {

class ZeroAllocGuard {
 public:
  ZeroAllocGuard() noexcept {
#if defined(KAI_USE_MIMALLOC)
    allocs_before_ = _g_allocs.load(std::memory_order_relaxed);
    frees_before_  = _g_frees.load(std::memory_order_relaxed);
    thread_id_     = std::this_thread::get_id();
#else
    rpmalloc_thread_statistics_t ts{};
    rpmalloc_thread_statistics(&ts);
    sumCounters(ts, allocs_before_, frees_before_);
#endif
  }

  ZeroAllocGuard(const ZeroAllocGuard&)            = delete;
  ZeroAllocGuard& operator=(const ZeroAllocGuard&) = delete;
  ZeroAllocGuard(ZeroAllocGuard&&)                 = delete;
  ZeroAllocGuard& operator=(ZeroAllocGuard&&)      = delete;

  ~ZeroAllocGuard() noexcept(false) {
#if defined(KAI_USE_MIMALLOC)
    if (std::this_thread::get_id() != thread_id_) {
      throw std::runtime_error("KAI_VERIFY_ZERO_ALLOC must exit on same thread it starts");
    }
    const std::size_t allocs_after = _g_allocs.load(std::memory_order_relaxed);
    const std::size_t frees_after  = _g_frees.load(std::memory_order_relaxed);
    if ((allocs_after != allocs_before_) || (frees_after != frees_before_)) {
      throw std::runtime_error("KAI_VERIFY_ZERO_ALLOC: heap activity detected");
    }
#else
    rpmalloc_thread_statistics_t ts{};
    rpmalloc_thread_statistics(&ts);
    std::size_t allocs_after=0, frees_after=0;
    sumCounters(ts, allocs_after, frees_after);
    if ((allocs_after != allocs_before_) || (frees_after != frees_before_)) {
      throw std::runtime_error("KAI_VERIFY_ZERO_ALLOC: heap activity detected");
    }
#endif
  }

 private:
#if !defined(KAI_USE_MIMALLOC)
  // Helper to accumulate per-size-class allocation totals in rpmalloc stats
  static inline void sumCounters(const rpmalloc_thread_statistics_t& ts,
                                 std::size_t& allocs, std::size_t& frees) {
    allocs = 0;
    frees  = 0;
    for (int i = 0; i < 128; ++i) {
      allocs += ts.size_use[i].alloc_total;
      frees  += ts.size_use[i].free_total;
    }
  }
#endif
#if defined(KAI_USE_MIMALLOC)
  std::size_t allocs_before_{};
  std::size_t frees_before_{};
  std::thread::id thread_id_{};
#else
  std::size_t allocs_before_{};
  std::size_t frees_before_{};
#endif
};

}  // namespace launcher::core::memory

#define KAI_VERIFY_ZERO_ALLOC(CodeBlock)                         \
  do {                                                          \
    ::launcher::core::memory::ZeroAllocGuard _kai_zero_guard;   \
    CodeBlock;                                                  \
  } while (false)

#else
// Guard disabled – macro reduces to plain code execution.
#define KAI_VERIFY_ZERO_ALLOC(CodeBlock) CodeBlock
#endif // KAI_DISABLE_ZERO_ALLOC_GUARD 