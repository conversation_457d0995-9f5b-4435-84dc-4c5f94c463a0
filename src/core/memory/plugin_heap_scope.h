#pragma once

// -----------------------------------------------------------------------------
// @file plugin_heap_scope.h
// @brief RAII helper that switches thread-local current heap for plugin code.
// -----------------------------------------------------------------------------

#include "arena_allocator.h"

namespace launcher::core::memory {

class PluginHeapScope {
 public:
#if !defined(KAI_USE_MIMALLOC) && defined(RPMALLOC_FIRST_CLASS_HEAPS) && (RPMALLOC_FIRST_CLASS_HEAPS)
    explicit PluginHeapScope(rpmalloc_heap_t* new_heap) noexcept
        : prev_(tls_current_heap) {
        tls_current_heap = new_heap;
    }
#else
    explicit PluginHeapScope(void*) noexcept {}
#endif

    PluginHeapScope(const PluginHeapScope&)            = delete;
    PluginHeapScope& operator=(const PluginHeapScope&) = delete;

    ~PluginHeapScope() noexcept {
#if !defined(KAI_USE_MIMALLOC) && defined(RPMALLOC_FIRST_CLASS_HEAPS) && (RPMALLOC_FIRST_CLASS_HEAPS)
        tls_current_heap = prev_;
#endif
    }

 private:
#if !defined(KAI_USE_MIMALLOC) && defined(RPMALLOC_FIRST_CLASS_HEAPS) && (RPMALLOC_FIRST_CLASS_HEAPS)
    rpmalloc_heap_t* prev_;
#else
    void* prev_{};
#endif
};

} // namespace launcher::core::memory 