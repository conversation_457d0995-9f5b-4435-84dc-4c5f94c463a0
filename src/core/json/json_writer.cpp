#include "core/json/json_writer.h"

#include <yyjson.h>
#include <optional>
#include <cstdlib>

namespace launcher::core::json {

static std::optional<std::string> makeErr() { return std::nullopt; }

std::optional<std::string> toJson(const yyjson_doc* doc) noexcept {
    if (!doc) return std::nullopt;
    char* json = yyjson_write(doc, 0, nullptr);
    if (!json) return makeErr();
    std::string res(json);
    std::free(json);
    return res;
}

std::optional<std::string> toJson(const yyjson_mut_doc* doc) noexcept {
    if (!doc) return std::nullopt;
    char* json = yyjson_mut_write(doc, 0, nullptr);
    if (!json) return makeErr();
    std::string res(json);
    std::free(json);
    return res;
}

} // namespace launcher::core::json 