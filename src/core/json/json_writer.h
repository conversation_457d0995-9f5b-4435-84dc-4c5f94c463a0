#pragma once

#include <string>
#include <optional>

// Forward declare yyjson types to avoid heavy include for consumers
struct yyjson_doc;
struct yyjson_mut_doc;

namespace launcher::core::json {

// Serialize immutable yyjson document to compact JSON string.
[[nodiscard]] std::optional<std::string> to<PERSON><PERSON>(const yyjson_doc* doc) noexcept;

// Serialize mutable yyjson document.
[[nodiscard]] std::optional<std::string> to<PERSON><PERSON>(const yyjson_mut_doc* doc) noexcept;

} // namespace launcher::core::json 