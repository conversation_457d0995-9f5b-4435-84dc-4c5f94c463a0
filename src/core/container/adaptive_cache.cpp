// -----------------------------------------------------------------------------
// @file adaptive_cache.cpp
// @brief Explicit template instantiation for AdaptiveCache specialisations.
// -----------------------------------------------------------------------------

#include "adaptive_cache.hh"
#include "../security/hash_types.hh"

// -----------------------------------------------------------------------------
// Forward / placeholder definitions for CDHash, PathHash, Verdict, ProbeResult
// These minimal POD structs satisfy compilation until full definitions land.
// They MUST remain trivially copyable and match final size requirements.
// -----------------------------------------------------------------------------

#if 0
namespace launcher::core::security {
struct CDHash {
    uint8_t bytes[20]{}; // SHA-1 digest length (Apple Code Directory)
    bool operator==(const CDHash&) const noexcept = default;
};
}

namespace std {
template<>
struct hash<launcher::core::security::CDHash> {
    size_t operator()(const launcher::core::security::CDHash& h) const noexcept {
        // FNV-1a 64-bit over 20 bytes
        constexpr uint64_t fnv_offset = 0xcbf29ce484222325ULL;
        constexpr uint64_t fnv_prime  = 0x100000001b3ULL;
        uint64_t acc = fnv_offset;
        for (auto b : h.bytes) acc = (acc ^ b) * fnv_prime;
        return static_cast<size_t>(acc);
    }
};
}

namespace launcher::core::security {
struct Verdict {
    enum class Code : uint8_t { kUnknown, kAllowed, kDenied };
    Code code{Code::kUnknown};
};
}

namespace launcher::core::security {
struct PathHash {
    uint64_t value{0};
    bool operator==(const PathHash&) const noexcept = default;
};
}

namespace std {
template<>
struct hash<launcher::core::security::PathHash> {
    size_t operator()(const launcher::core::security::PathHash& h) const noexcept {
        return static_cast<size_t>(h.value);
    }
};
}

namespace launcher::core::security {
struct ProbeResult {
    bool valid{false};
};
}
#endif // 0

// -----------------------------------------------------------------------------
// Instantiate cache specialisations (4-way, 64 shards as defaults)
// -----------------------------------------------------------------------------

namespace launcher::core::container {

using CDHash_t = launcher::core::security::CDHash;
using Verdict_t = launcher::core::security::Verdict;
using PathHash_t = launcher::core::security::PathHash;
using ProbeResult_t = launcher::core::security::ProbeResult;

template class AdaptiveCache<CDHash_t, Verdict_t, NullPolicy, 64, 16>;
template class AdaptiveCache<PathHash_t, ProbeResult_t, NullPolicy, 64, 16>;

} // namespace launcher::core::container 