// -----------------------------------------------------------------------------
// @file adaptive_cache.hh (rewritten)
// @brief Adaptive striped N-way set-associative cache with optional grow & PLRU.
//        Header-only hot paths; zero steady-state heap allocations after grow.
//        Part of Slice-2 security substrate.
// -----------------------------------------------------------------------------

#pragma once

#include <atomic>
#include <array>
#include <cassert>
#include <cstddef>
#include <cstdint>
#include <functional>
#include <memory>
#include <thread>
#include <type_traits>
#include <utility>
#include <algorithm>
#include <bit>

namespace launcher::core::container {

// -----------------------------------------------------------------------------
// NullPolicy – default policy that does nothing.
// -----------------------------------------------------------------------------

template <class K, class V>
struct NullPolicy {
    static inline void onMiss(const K&) noexcept {}
    static inline void onEvict(const K&, const V&) noexcept {}
};

// -----------------------------------------------------------------------------
// Helper – PLRU bit-tree utilities. Supports associativity 2…16 (power-of-two).
// We encode the internal tree in a uint16_t – 15 bits is enough for 16-way.
// -----------------------------------------------------------------------------
namespace detail {

inline std::size_t selectVictim(uint16_t &tree, std::size_t ways_pow2) noexcept {
    // Walking the binary tree; bits order: pre-order. 0 = choose left, 1 = right
    std::size_t idx = 0;     // leaf index we will return
    std::size_t node = 0;    // current bit position in tree
    std::size_t step = ways_pow2 >> 1; // half size of subtree
    while (step != 0) {
        bool dir = static_cast<bool>((tree >> node) & 0x1);
        // flip bit to point to *other* direction next time (PLRU property)
        tree ^= static_cast<uint16_t>(1u << node);
        if (dir) {
            idx += step;
            node = 2 * node + 2; // right child in array representation
        } else {
            node = 2 * node + 1; // left child
        }
        step >>= 1;
    }
    return idx;
}

inline void updatePlruOnAccess(uint16_t &tree, std::size_t ways_pow2, std::size_t idx) noexcept {
    // Update bits along path to leaf so that next victim avoids recently used line.
    std::size_t node = 0;            // root bit
    std::size_t step = ways_pow2 >> 1;
    std::size_t local_idx = idx;
    while (step != 0) {
        bool dir = local_idx >= step; // 0 = left, 1 = right
        if (dir) {
            // we accessed right – set bit = 1 (point to left for victim)
            tree |= static_cast<uint16_t>(1u << node);
            local_idx -= step;
            node = 2 * node + 2; // right child
        } else {
            // accessed left – set bit = 0 (point to right for victim)
            tree &= static_cast<uint16_t>(~(1u << node));
            node = 2 * node + 1; // left child
        }
        step >>= 1;
    }
}

constexpr bool isPowerOfTwo(std::size_t v) { return v && !(v & (v - 1)); }

} // namespace detail

// -----------------------------------------------------------------------------
// AdaptiveCache – template definition
// -----------------------------------------------------------------------------
// Key/Value: trivially-copyable PODs (so we can memcpy, place in arrays).
// Policy   : provides static onMiss/onEvict hooks.
// Shards   : compile-time number of stripes (use power-of-two for fast mask).
// MaxWays  : compile-time hard cap of associativity (2…16).
// -----------------------------------------------------------------------------

#ifndef KAI_CACHE_PREALLOC_MAX_WAYS
#define KAI_CACHE_PREALLOC_MAX_WAYS 0
#endif

/**
 * \brief AdaptiveCache heuristic constants.
 *
 * These govern the dynamic associativity growth algorithm:
 *   • kMissGrowThreshold – Fractional miss-ratio threshold that, once exceeded
 *     within a shard's sample window, triggers a doubling of the current
 *     associativity (up to kMaxWays).
 *     0.15 corresponds to the empirically determined 15 % miss-rate where the
 *     benefit of additional ways outweighs the cost of a wider probe.
 *
 *   • kMaxWays – Hard upper bound for associativity. 16 ways were chosen
 *     because a 16-way set of 8-byte <Key,Value> pointers fits into two 64-byte
 *     cache-lines on modern x86_64/Apple Silicon CPUs, keeping lookup latency
 *     low while still providing ample space for the PLRU replacement policy.
 */
inline constexpr double      kMissGrowThreshold = 0.15; // 15 %
inline constexpr std::size_t kMaxWays           = 16;

// NOTE: Default Shards changed from 64 → 16 (≈ CPU×2 cap) per Slice-2 decision.
template <class Key,
          class Value,
          template <class, class> class Policy = NullPolicy,
          std::size_t Shards = 16,
          std::size_t MaxWays = kMaxWays>
class AdaptiveCache {
    static_assert(Shards >= 1 && detail::isPowerOfTwo(Shards), "Shards must be power-of-two >=1");
    static_assert(MaxWays >= 2 && MaxWays <= kMaxWays && detail::isPowerOfTwo(MaxWays), "MaxWays must be power-of-two and ≤ kMaxWays");
    static_assert(std::is_trivially_copyable_v<Key>, "Key must be trivially copyable");
    static_assert(std::is_trivially_copyable_v<Value>, "Value must be trivially copyable");

    struct Entry {
        Key   key;
        Value value;
        bool  valid{false};
    };

    struct alignas(64) Shard {
        // spin-lock guard
        alignas(64) std::atomic_flag lock = ATOMIC_FLAG_INIT;

        // counters (own cache-lines in practise because of 64-B align)
        alignas(64) std::atomic<uint64_t> hit{0};
        alignas(64) std::atomic<uint64_t> miss{0};
        alignas(64) std::atomic<uint64_t> evict{0};

        // current associativity (power-of-two ≤ MaxWays)
        std::size_t ways_curr{0};     // logical associativity currently in use
        std::size_t set_count{0};
        std::size_t stride_ways{0};   // physical stride (alloc_ways)

        // storage
        std::unique_ptr<Entry[]>    table;
        std::unique_ptr<uint16_t[]> plru; // one uint16 per set (enough for 16-way)

        [[nodiscard]] Entry* setAt(std::size_t idx) const noexcept {
            return table.get() + idx * stride_ways;
        }
    };

public:
    struct Stats { uint64_t hit{0}, miss{0}, evict{0}; };

    // MetricSource concept hook
    static constexpr bool is_metric_source = true;

    // Constructor
    AdaptiveCache(std::size_t capacity_pow2_sets = 1024,
                  std::size_t initial_ways = 4) {
        assert(detail::isPowerOfTwo(capacity_pow2_sets));
        assert(initial_ways >= 2 && initial_ways <= MaxWays && detail::isPowerOfTwo(initial_ways));

        const std::size_t sets_per_shard = capacity_pow2_sets / Shards;
        const std::size_t alloc_ways = KAI_CACHE_PREALLOC_MAX_WAYS ? MaxWays : initial_ways;

        for (auto &s : shards_) {
            s.ways_curr = initial_ways;
            s.set_count = sets_per_shard;
            s.stride_ways = alloc_ways;
            s.table = std::make_unique<Entry[]>(sets_per_shard * alloc_ways);
            s.plru  = std::make_unique<uint16_t[]>(sets_per_shard);
            std::fill_n(s.plru.get(), sets_per_shard, 0);

            if constexpr (KAI_CACHE_PREALLOC_MAX_WAYS) {
                // Ensure newly allocated extra ways are invalid
                for (std::size_t set = 0; set < sets_per_shard; ++set) {
                    Entry *base = s.table.get() + set * alloc_ways;
                    for (std::size_t i = initial_ways; i < alloc_ways; ++i) {
                        base[i].valid = false;
                    }
                }
            }
        }
    }

    AdaptiveCache(const AdaptiveCache&)            = delete;
    AdaptiveCache& operator=(const AdaptiveCache&) = delete;
    AdaptiveCache(AdaptiveCache&&)                 = delete;
    AdaptiveCache& operator=(AdaptiveCache&&)      = delete;

    [[nodiscard]] std::size_t ways() const noexcept { return shards_[0].ways_curr; }

    template <class F>
    Value& getOrInsert(const Key &key, F &&ctor) noexcept(noexcept(std::declval<F>()())) {
        const std::size_t h = hasher_(key);
        Shard &shard = shards_[h & (Shards - 1)];
        lock(shard);

        const std::size_t set_idx = (h >> shard_bits_) & (shard.set_count - 1);
        Entry  *set_base = shard.setAt(set_idx);
        uint16_t *plru_state_ptr = &shard.plru[set_idx];

        // 1) HIT probe
        for (std::size_t i = 0; i < shard.ways_curr; ++i) {
#if defined(__has_builtin)
#if __has_builtin(__builtin_prefetch)
            __builtin_prefetch(set_base + i + 1, 0, 1);
#endif
#endif
            Entry &e = set_base[i];
            if (e.valid && e.key == key) {
                ++shard.hit;
                detail::updatePlruOnAccess(*plru_state_ptr, shard.ways_curr, i);
                unlock(shard);
                return e.value;
            }
        }

        // 2) MISS path
        ++shard.miss;
        Policy<Key, Value>::onMiss(key);

        // 3) Maybe grow before selecting victim (pointer safety)
        const std::size_t prev_ways = shard.ways_curr;
        maybeGrow(shard);
        if (shard.ways_curr != prev_ways) {
            set_base = shard.setAt(set_idx);
            plru_state_ptr = &shard.plru[set_idx];
        }

        // 4) Find slot or victim
        Entry *victim = nullptr;
        std::size_t victim_idx = 0;
        for (std::size_t i = 0; i < shard.ways_curr; ++i) {
            Entry &e = set_base[i];
            if (!e.valid) { victim = &e; victim_idx = i; break; }
        }
        if (!victim) {
            victim_idx = detail::selectVictim(*plru_state_ptr, shard.ways_curr);
            victim = &set_base[victim_idx];
            Policy<Key, Value>::onEvict(victim->key, victim->value);
            ++shard.evict;
        }

        // 5) Overwrite victim
        victim->key   = key;
        victim->value = std::forward<F>(ctor)();
        victim->valid = true;
        detail::updatePlruOnAccess(*plru_state_ptr, shard.ways_curr, victim_idx);

        unlock(shard);
        return victim->value;
    }

    bool erase(const Key &key) noexcept {
        const std::size_t h = hasher_(key);
        Shard &shard = shards_[h & (Shards - 1)];
        lock(shard);

        const std::size_t set_idx = (h >> shard_bits_) & (shard.set_count - 1);
        Entry  *set_base = shard.setAt(set_idx);
        uint16_t *plru_state_ptr = &shard.plru[set_idx];

        for (std::size_t i = 0; i < shard.ways_curr; ++i) {
            Entry &e = set_base[i];
            if (e.valid && e.key == key) {
                e.valid = false;
                Policy<Key, Value>::onEvict(e.key, e.value);
                // mark slot as recently used so PLRU chooses other next time
                detail::updatePlruOnAccess(*plru_state_ptr, shard.ways_curr, i);
                unlock(shard);
                return true;
            }
        }
        unlock(shard);
        return false;
    }

    [[nodiscard]] Stats stats() const noexcept {
        Stats s{};
        for (const auto &sh : shards_) {
            s.hit   += sh.hit.load(std::memory_order_relaxed);
            s.miss  += sh.miss.load(std::memory_order_relaxed);
            s.evict += sh.evict.load(std::memory_order_relaxed);
        }
        return s;
    }

private:
    static constexpr std::size_t shard_bits_ = std::countr_zero(Shards);
    static constexpr uint64_t    grow_threshold = 256; // per-shard sample window

    static inline void lock(Shard &s) noexcept {
        while (s.lock.test_and_set(std::memory_order_acquire)) {
#if defined(__x86_64__)
            __builtin_ia32_pause();
#else
            std::this_thread::yield();
#endif
        }
    }
    static inline void unlock(Shard &s) noexcept { s.lock.clear(std::memory_order_release); }

    void maybeGrow(Shard &s) {
        if (s.ways_curr >= MaxWays) return;
        const uint64_t total = s.miss.load(std::memory_order_relaxed) + s.hit.load(std::memory_order_relaxed);
        if (total < grow_threshold) return; // not enough samples yet
        const double miss_ratio = static_cast<double>(s.miss.load(std::memory_order_relaxed)) / total;
        if (miss_ratio <= kMissGrowThreshold) return; // healthy hit-rate

#if KAI_CACHE_PREALLOC_MAX_WAYS
        // In-place growth – table already allocated for MaxWays
        const std::size_t new_ways = std::min(s.ways_curr * 2, MaxWays);
        if (new_ways == s.ways_curr) return;
        for (std::size_t set = 0; set < s.set_count; ++set) {
            Entry *set_base = s.setAt(set);
            for (std::size_t i = s.ways_curr; i < new_ways; ++i) {
                set_base[i].valid = false;
            }
        }
        std::fill_n(s.plru.get(), s.set_count, 0);
        s.ways_curr = new_ways;
#else
        // Grow  ×2   — allocate new arrays
        const std::size_t new_ways = s.ways_curr * 2;
        const std::size_t new_cap  = new_ways * s.set_count;

        auto new_table = std::make_unique<Entry[]>(new_cap);
        auto new_plru  = std::make_unique<uint16_t[]>(s.set_count);
        std::fill_n(new_plru.get(), s.set_count, 0);

        for (std::size_t set = 0; set < s.set_count; ++set) {
            Entry *old_set = s.setAt(set);
            Entry *new_set = new_table.get() + set * new_ways;
            // copy existing entries keeping order
            for (std::size_t i = 0; i < s.ways_curr; ++i) new_set[i] = old_set[i];
            // mark rest invalid
            for (std::size_t i = s.ways_curr; i < new_ways; ++i) new_set[i].valid = false;
        }
        s.table      = std::move(new_table);
        s.plru       = std::move(new_plru);
        s.ways_curr  = new_ways;
        s.stride_ways = new_ways;
#endif
    }

    std::array<Shard, Shards> shards_;
    std::hash<Key>            hasher_;
};

} // namespace launcher::core::container 