#pragma once

// -----------------------------------------------------------------------------
// @file shared_library.h
// @brief Small RAII helper over dlopen/dlsym/dlclose.
// -----------------------------------------------------------------------------

#include <string>
#include <filesystem>
#include <dlfcn.h>
#include "../util/debug.h"

namespace launcher::core::plugins {

class SharedLibrary {
 public:
    SharedLibrary() = default;

    SharedLibrary(const std::filesystem::path& path, int flags) { open(path, flags); }

    SharedLibrary(const SharedLibrary&) = delete;
    SharedLibrary& operator=(const SharedLibrary&) = delete;

    SharedLibrary(SharedLibrary&& other) noexcept : handle_(other.handle_) {
        other.handle_ = nullptr;
    }
    SharedLibrary& operator=(SharedLibrary&& other) noexcept {
        if (this != &other) {
            close();
            handle_ = other.handle_;
            other.handle_ = nullptr;
        }
        return *this;
    }

    ~SharedLibrary() { close(); }

    bool open(const std::filesystem::path& path, int flags) {
        close();
        handle_ = ::dlopen(path.c_str(), flags);
        if (!handle_) {
            ERR("dlopen failed: " << ::dlerror());
            return false;
        }
        return true;
    }

    void* symbol(const char* name) const {
        if (!handle_) return nullptr;
        return ::dlsym(handle_, name);
    }

    void close() {
        if (handle_) {
            ::dlclose(handle_);
            handle_ = nullptr;
        }
    }

    explicit operator bool() const noexcept { return handle_ != nullptr; }

 private:
    void* handle_{nullptr};
};

} // namespace launcher::core::plugins 