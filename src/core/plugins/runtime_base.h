#pragma once

// -----------------------------------------------------------------------------
// @file runtime_base.h
// @brief Common interface implemented by all plugin runtimes (null, J<PERSON>, Wasm…).
// -----------------------------------------------------------------------------

#include <cstdint>
#include "abi.h"  // provides ::KaiPluginInfo definition
#include "../util/expected.h"

namespace launcher::core::plugins {

// Bring the fully-defined C ABI struct from the global namespace into this
// namespace for convenience before it is referenced below.
using KaiPluginInfo = ::KaiPluginInfo;

// ABI-stable discriminant for the runtime used by a plugin. Kept as a 32-bit
// integral so that the C ABI struct (KaiPluginInfo) does not depend on C++
// enum layout which is implementation-defined prior to C++23.
// Values MUST remain stable once released.
enum class RuntimeKind : std::uint32_t {
    kNull        = 0,
    kJavaScript  = 1,
    kWasmNative  = 2,
    kNativeXpc   = 3,
};

// Lightweight base class.  Only a handful of concrete runtimes will ever be
// instantiated per process, so a single virtual table indirection is
// acceptable and dramatically simplifies the host code.
class RuntimeBase {
 public:
    virtual ~RuntimeBase() = default;

    [[nodiscard]] virtual RuntimeKind kind() const noexcept = 0;

    // Initialise runtime with plugin metadata.  For Slice-1 NullRuntime this is
    // a no-op.  Must be idempotent and return Expected error on failure.
    virtual launcher::core::util::Expected<void, int /*placeholder*/> initialise(
        const KaiPluginInfo&) = 0;

    // Shutdown the runtime; guaranteed to be called exactly once.  Must be
    // noexcept and tolerate partially-initialised state.
    virtual void shutdown() noexcept = 0;
};

} // namespace launcher::core::plugins
