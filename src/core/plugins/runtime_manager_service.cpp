// -----------------------------------------------------------------------------
// @file runtime_manager_service.cpp
// -----------------------------------------------------------------------------

#include "runtime_manager_service.h"
#include <regex>

#include "../diagnostics/diagnostics_service.h"
#include "../security/codesign_verify.h"
#include "../events/event_bus_service.h"
#include "../memory/arena_allocator_service.h"
#include "../memory/plugin_heap_scope.h"
#include "../security/sandbox.h"
#include "../foundation/service_base.h"
#include "../foundation/registry.h"
#include "../security/verification_store.hh"
#include "plugin_events.h"
#include "../interfaces/iconfig_manager.h"

#include <sys/stat.h>
#include <unistd.h>
#include <dlfcn.h>
#include <fcntl.h>
#include <cstring>
#include <cstdlib>
#include <string>
#include <string_view>
#include <memory>
#include <array>
#include <algorithm>
#include <filesystem>
#include <limits.h>
#include <fstream>
#include <cctype>
#include <absl/container/inlined_vector.h>
#include <chrono>

#ifndef RTLD_FIRST
#define RTLD_FIRST 0
#endif

#if defined(__APPLE__) && defined(__has_include)
#  if __has_include(<sandbox.h>)
#    include <sandbox.h>
#    define KAI_HAS_LIBSANDBOX 1
#  endif
#endif

#if defined(__APPLE__)
#include <mach-o/dyld.h>
#endif

#ifndef KAI_FAST_MANIFEST_PARSE
#define KAI_FAST_MANIFEST_PARSE 1
#endif

#if !KAI_FAST_MANIFEST_PARSE
#include <cpptoml.h>
#endif

namespace {
constexpr const char* kPluginSymbolName = "kai_plugin_get_info";

bool hasDylibSuffix(const std::filesystem::path& p) {
#ifdef __APPLE__
    return p.extension() == ".dylib";
#else
    return p.extension() == ".so";
#endif
}

// small helper to bump counter if diag available
inline void incCounter(launcher::core::diagnostics::DiagnosticsService* d,
                       std::string_view name) {
    if (d) { d->incrementCounter(name); }
}

// Map PluginError → KaiError so callers get meaningful diagnostics.
[[maybe_unused]] inline launcher::core::foundation::KaiError toKaiError(
    launcher::core::plugins::PluginError pe) noexcept {
    using launcher::core::foundation::KaiError;
    using launcher::core::plugins::PluginError;
    switch (pe) {
        case PluginError::kStatFailed:           return KaiError::StatFailed;
        case PluginError::kUidMismatch:          return KaiError::UidMismatch;
        case PluginError::kCodesignInvalid:      return KaiError::InvalidSignature;
        case PluginError::kDlopenFailed:         return KaiError::DlopenFailed;
        case PluginError::kMissingSymbol:        return KaiError::MissingSymbol;
        case PluginError::kAbiIncompatible:      return KaiError::AbiIncompatible;
        case PluginError::kSeatbeltInvalid:      return KaiError::SeatbeltInvalid;
        case PluginError::kCapabilitiesMissing:  return KaiError::CapabilitiesMissing;
        case PluginError::kPermInsecure:        return KaiError::PermInsecure;
        default:                                 return KaiError::NotRegistered;
    }
}

} // namespace

// ---------------------------------------------------------------------------
// Helper – returns the list of plugin search roots (may include non-existent
// paths).  Duplicates and empty paths are filtered out.
// ---------------------------------------------------------------------------
static absl::InlinedVector<std::filesystem::path, 5> enumeratePluginRoots() {
    absl::InlinedVector<std::filesystem::path, 5> roots;

#if defined(__APPLE__)
    {
        char exePathBuf[PATH_MAX];
        uint32_t sz = sizeof(exePathBuf);
        if (_NSGetExecutablePath(exePathBuf, &sz) == 0) {
            std::error_code ec;
            auto canon = std::filesystem::canonical(exePathBuf, ec);
            if (!ec) {
                roots.push_back(canon.parent_path() / "Plugins");
                // Also consider *.app/Contents/PlugIns when inside bundle.
                auto bundlePlugIns = canon.parent_path().parent_path() / "PlugIns";
                roots.push_back(bundlePlugIns);
            }
        }
    }
#else
    {
        char exePathBuf[PATH_MAX];
        ssize_t len = ::readlink("/proc/self/exe", exePathBuf, sizeof(exePathBuf) - 1);
        if (len > 0) {
            exePathBuf[len] = '\0';
            std::error_code ec;
            auto canon = std::filesystem::canonical(exePathBuf, ec);
            if (!ec) {
                roots.push_back(canon.parent_path() / "Plugins");
            }
        }
    }
#endif

    // Project-relative default (useful for unit-tests when cwd is project root)
    roots.push_back(std::filesystem::path("./Plugins"));

#ifdef __APPLE__
    if (const char* home = std::getenv("HOME")) {
        roots.push_back(std::filesystem::path(home) / "Library/Application Support/MicroLauncher/Plugins");
    }
#else
    if (const char* home = std::getenv("HOME")) {
        roots.push_back(std::filesystem::path(home) / ".kai/Plugins");
    }
#endif

    // Deduplicate while preserving order – simple O(n^2) is fine for ≤5 items.
    absl::InlinedVector<std::filesystem::path, 5> unique;
    for (const auto& p : roots) {
        bool dup = false;
        for (const auto& u : unique) {
            std::error_code ec;
            if (std::filesystem::equivalent(p, u, ec)) { dup = true; break; }
        }
        if (!dup) unique.push_back(p);
    }
    return unique;
}

using namespace launcher::core;

namespace launcher::core::plugins {

// ----------------------------- start ----------------------------------------
util::Result<void> RuntimeManagerSvc::start() {
    // Ensure ConfigManager dependency resolved
    if (!config_) {
        INF("RuntimeManagerSvc start(): ConfigManager not yet available; disabled-plugin settings will be ignored for initial scan");
    }
    descriptors_.clear();

    // Enumerate candidate roots and perform full probe scan (dlopen etc.).
    for (const auto& dir : enumeratePluginRoots()) {
        (void)scanDirectory(dir);
    }

    if (eventBus_) {
        auto evt = std::make_shared<PluginScanCompleteEvent>();
        evt->descriptors.assign(descriptors_.begin(), descriptors_.end());
        eventBus_->publish(std::static_pointer_cast<const PluginScanCompleteEvent>(evt));

        // Subscribe to UI-driven plugin enable / disable toggles ----------------
        plugin_toggle_handle_ = eventBus_->subscribe<launcher::core::plugins::PluginStateChangeEvent>(
            [this](const launcher::core::plugins::PluginStateChangeEvent* evt_ptr) {
                if (!evt_ptr) return;
                const auto& evt = *evt_ptr;
                std::unique_lock guard(mtx_);
                if (!evt.enabled) {
                    // Disable → find runtime, shutdown, erase
                    auto it = std::find_if(runtimes_.begin(), runtimes_.end(), [&](const PluginRuntime& pr){
                        return std::string(pr.descriptor.info.id) == evt.id;
                    });
                    if (it != runtimes_.end()) {
                        if (it->runtime) {
                            it->runtime->shutdown();
                        }
                        // Call kai_plugin_stop if symbol available and runtime kind is Null
                        if (it->descriptor.info.runtime == KAI_RUNTIME_NULL) {
                            void* stopSym = it->descriptor.lib ? it->descriptor.lib->symbol("kai_plugin_stop") : nullptr;
                            if (stopSym) {
                                using StopFn = void (*)();
                                reinterpret_cast<StopFn>(stopSym)();
                            }
                        }
                        runtimes_.erase(it);
                        loaded_ = false; // Force subsequent loadPlugins() to skip reload until enabled
                    }
                } else {
                    // Enable → ensure descriptor exists then instantiate runtime (if needed)
                    auto desc_it = std::find_if(descriptors_.begin(), descriptors_.end(), [&](const PluginDescriptor& d){
                        return std::string(d.info.id) == evt.id;
                    });
                    if (desc_it == descriptors_.end()) return; // unknown id

                    (void)instantiateRuntime(*desc_it);
                }
            });
    }

    return util::Result<void>::success();
}

// ----------------------------- stop -----------------------------------------
void RuntimeManagerSvc::stop() noexcept {
    plugin_toggle_handle_.unsubscribe();
    // Shutdown runtimes first
    for (auto& pr : runtimes_) {
        if (pr.runtime) {
            pr.runtime->shutdown();
        }
#if !defined(KAI_USE_MIMALLOC) && defined(RPMALLOC_FIRST_CLASS_HEAPS) && (RPMALLOC_FIRST_CLASS_HEAPS)
        if (arena_ && pr.descriptor.heap) {
            arena_->releaseHeap(pr.descriptor.heap);
            pr.descriptor.heap = nullptr;
        }
#endif
    }
    runtimes_.clear();

    // Optional dlclose for development profiling – set KAI_PLUGIN_UNLOAD=1
    if (const char* env = std::getenv("KAI_PLUGIN_UNLOAD")) {
        if (std::string_view(env) == "1") {
            for (auto& d : descriptors_) {
                d.lib.reset(); // releases dlopen handle
            }
        }
    }

    // Intentionally keep descriptors_ (and therefore SharedLibrary handles)
    // alive until process exit (unless env override). Negligible impact on
    // production host where stop() called at shutdown.
    loaded_ = false;
}

// ----------------------------- loadPlugins ----------------------------------
foundation::KaiExpected<void> RuntimeManagerSvc::loadPlugins() {
    std::unique_lock lock(mtx_);
    if (loaded_) return foundation::KaiExpected<void>::success();

    for (const auto& desc : descriptors_) {
        // Skip plugins that are disabled in configuration
        if (config_) {
            std::string key = std::string("plugins.disabled.") + desc.info.id;
            if (config_->getBool(key, false)) {
                continue;
            }
        }
        switch (static_cast<RuntimeKind>(desc.info.runtime)) {
            case RuntimeKind::kNull:
                // Plugins that self-register services (e.g. openai) require no additional runtime.
                incCounter(diag_, "plugins.loaded_total");
                continue;
            default:
                if (!instantiateRuntime(desc)) {
                    return foundation::KaiExpected<void>::failure(foundation::KaiError::RuntimeInitFailed);
                }
                continue;
        }
    }

    loaded_ = true;
    return foundation::KaiExpected<void>::success();
}

// ----------------------------- scanDirectory --------------------------------
util::Result<void> RuntimeManagerSvc::scanDirectory(const std::filesystem::path& dir) {
    if (!std::filesystem::exists(dir)) return util::Result<void>::success();

    for (const auto& entry : std::filesystem::directory_iterator(dir)) {
        if (!entry.is_regular_file()) continue;
        const auto& path = entry.path();
        if (!hasDylibSuffix(path)) continue;

        auto descExp = probeLibrary(path);
        if (descExp) {
            const PluginDescriptor& newDesc = descExp.value();
            PluginDescriptor descCopy = newDesc; // own copy
            bool already = std::any_of(descriptors_.begin(), descriptors_.end(), [&](const PluginDescriptor& d){
                std::error_code ec;
                return std::filesystem::equivalent(d.path, descCopy.path, ec);
            });
            if (!already) {
                bool enabled = true;
                if (config_) {
                    std::string key = std::string("plugins.disabled.") + descCopy.info.id;
                    enabled = !config_->getBool(key, false);
                }
                pregistry_.add(descCopy, enabled);
                // No version info at this stage
                descriptors_.push_back(std::move(descCopy));
            }
        }
    }
    return util::Result<void>::success();
}

// --------------------------- scanManifestDirectory --------------------------
// Fast-path directory scan that looks for `*.toml` manifest files and builds a
// lightweight PluginDescriptor without touching the actual shared library.
launcher::core::util::Result<void>
RuntimeManagerSvc::scanManifestDirectory(const std::filesystem::path& dir) {
    if (!std::filesystem::exists(dir)) return util::Result<void>::success();

    for (const auto& entry : std::filesystem::directory_iterator(dir)) {
        if (!entry.is_regular_file()) continue;
        const auto& path = entry.path();
        if (path.extension() != ".toml") continue;

        std::string id;
        std::string runtime_str;
        std::string version;
        std::string override_path_str;
        std::array<uint8_t, 16> caps_bytes{};
        bool caps_parsed = false;

#if KAI_FAST_MANIFEST_PARSE
        // ultra-fast hand-rolled parsing -------------------------------------------------
        std::ifstream in(path);
        if (!in) continue;
        std::string line;
        while (std::getline(in, line)) {
            auto trim_start = [](std::string_view sv) {
                std::size_t pos = 0;
                while (pos < sv.size() && std::isspace(static_cast<unsigned char>(sv[pos]))) ++pos;
                return sv.substr(pos);
            };
            std::string_view sv = trim_start(line);
            auto parse_key = [&](std::string_view key, std::string& out) {
                if (sv.rfind(key, 0) == 0) {
                    auto q1 = sv.find('"');
                    auto q2 = sv.find('"', q1 + 1);
                    if (q1 != std::string_view::npos && q2 != std::string_view::npos && q2 > q1 + 1) {
                        out = std::string(sv.substr(q1 + 1, q2 - q1 - 1));
                    }
                }
            };
            parse_key("id", id);
            parse_key("runtime", runtime_str);
            parse_key("version", version);
            parse_key("path", override_path_str);

            // capabilities = [1,2,4]
            if (!caps_parsed && sv.rfind("capabilities",0)==0) {
                auto br1 = sv.find('[');
                auto br2 = sv.find(']');
                if (br1!=std::string_view::npos && br2!=std::string_view::npos && br2>br1) {
                    std::string inner = std::string(sv.substr(br1+1, br2-br1-1));
                    std::size_t idx = 0;
                    std::size_t pos = 0;
                    while (idx < caps_bytes.size() && pos < inner.size()) {
                        auto comma = inner.find(',', pos);
                        std::string slice = inner.substr(pos, comma == std::string::npos ? std::string::npos : comma - pos);
                        std::erase_if(slice, [](char c){ return std::isspace(static_cast<unsigned char>(c)); });
                        if (!slice.empty()) {
                            try {
                                int v = std::stoi(slice);
                                caps_bytes[idx++] = static_cast<uint8_t>(v & 0xFF);
                            } catch(...) {}
                        }
                        if (comma == std::string::npos) break;
                        pos = comma + 1;
                    }
                    caps_parsed = true;
                }
            }
            if (!id.empty() && !runtime_str.empty()) {
                break;
            }
        }
#else
        // full cpptoml parse --------------------------------------------------------------
        std::filesystem::path overridePath;
        try {
            auto root = cpptoml::parse_file(path.string());
            if (auto v = root->get_as<std::string>("id")) id = *v;
            if (auto v = root->get_as<std::string>("runtime")) runtime_str = *v;
            if (auto v = root->get_as<std::string>("version")) version = *v;
            if (auto v = root->get_as<std::string>("path")) {
                overridePath = *v;
            }
        } catch (const std::exception&) {
            continue; // malformed
        }
#endif

        if (id.empty()) continue; // invalid manifest

        KaiPluginInfo info{};
        std::strncpy(info.id, id.c_str(), sizeof(info.id));
        info.abi_major = kKaiAbiMajor;
        info.abi_minor = kKaiAbiMinor;
        info.runtime   = KAI_RUNTIME_NULL; // default
        if (runtime_str == "javascript") {
            info.runtime = KAI_RUNTIME_JAVASCRIPT;
        } else if (runtime_str == "wasm_native") {
            info.runtime = KAI_RUNTIME_WASM_NATIVE;
        } else if (runtime_str == "native_xpc") {
            info.runtime = KAI_RUNTIME_NATIVE_XPC;
        }
        std::memset(info.capabilities, 0, sizeof(info.capabilities));
        std::memcpy(info.capabilities, caps_bytes.data(), std::min(sizeof(info.capabilities), caps_bytes.size()));

        // semver validation
        if (!version.empty()) {
            static const std::regex kSemver{R"(^\d+\.\d+\.\d+(?:[-+][0-9A-Za-z-.]+)?$)"};
            if (!std::regex_match(version, kSemver)) {
                ERR("Invalid semver format for plugin " << id);
                incCounter(diag_, "plugins.invalid_semver");
                continue; // skip invalid manifest
            }
        }

        PluginDescriptor desc{};
#if KAI_FAST_MANIFEST_PARSE
        if (!override_path_str.empty()) {
            desc.path = override_path_str;
        } else {
            desc.path = path.parent_path() / (id + ".dylib");
        }
#else
        desc.path = path.parent_path() / (id + ".dylib");
        if (!overridePath.empty()) {
            desc.path = overridePath;
        }
#endif
        desc.info = info;
        desc.owner_uid = ::geteuid();

        bool enabled = true;
        if (config_) {
            std::string key = std::string("plugins.disabled.") + id;
            enabled = !config_->getBool(key, false);
        }

        // Deduplicate by id
        bool already = std::any_of(descriptors_.begin(), descriptors_.end(), [&](const PluginDescriptor& d){
            return std::string(d.info.id) == id;
        });
        if (already) continue;

        pregistry_.add(desc, enabled);
        if (!version.empty()) {
            if (auto* m = pregistry_.find(id); m) {
                m->version = version;
            }
        }
        descriptors_.push_back(std::move(desc));

        incCounter(diag_, "plugins.discovered_fast");
    }

    return util::Result<void>::success();
}

// ----------------------------- probeLibrary ---------------------------------
PluginResult<PluginDescriptor> RuntimeManagerSvc::probeLibrary(const std::filesystem::path& path) {
    // Fast-path: if the plugin at this path was already probed, return the
    // cached descriptor and avoid an extra dlopen/dlclose cycle.
    for (const auto& d : descriptors_) {
        std::error_code ec;
        if (std::filesystem::equivalent(d.path, path, ec)) {
            return PluginResult<PluginDescriptor>::success(d);
        }
    }

    // -------------------------------------------------------------------
    // VerificationStore – deny fast-path
    // -------------------------------------------------------------------
    using namespace launcher::core::security;
    const CDHash cdhash = computeCdHash(path);

    // Light-weight filesystem stat to ensure existence and owner UID
    struct stat st;
    if (::stat(path.c_str(), &st) != 0) {
        ERR("stat failed on " << path);
        incCounter(diag_, "plugins.stat_failed");
        return PluginResult<PluginDescriptor>::failure(PluginError::kStatFailed);
    }
    const uid_t current_uid = ::geteuid();
    if (st.st_uid != current_uid) {
        ERR("Owner UID mismatch for plugin " << path);
        incCounter(diag_, "plugins.uid_mismatch");
        return PluginResult<PluginDescriptor>::failure(PluginError::kUidMismatch);
    }

    // Ensure parent directory ownership and permissions are secure (0700)
    struct stat dir_st;
    if (::stat(path.parent_path().c_str(), &dir_st) != 0 ||
        dir_st.st_uid != current_uid || (dir_st.st_mode & 0777) != 0700) {
        ERR("Insecure permissions or ownership on plugin directory " << path.parent_path());
        incCounter(diag_, "plugins.perm_insecure");
        return PluginResult<PluginDescriptor>::failure(PluginError::kPermInsecure);
    }

    bool need_verify = true;
    if (auto cached = vstore_->find(cdhash); cached) {
        if (cached.value().code == Verdict::Code::kDenied) {
            incCounter(diag_, "plugins.verification_cached_deny");
            return PluginResult<PluginDescriptor>::failure(PluginError::kCodesignInvalid);
        }
        if (cached.value().code == Verdict::Code::kAllowed) {
            incCounter(diag_, "plugins.verification_cached_allow");
            need_verify = false;
        }
    }

    if (need_verify) {
        // codesign --------------------------------------------------------------
        auto code_res = launcher::core::security::verifyCodeSignature(path);
        if (!code_res) {
            ERR("Codesign verification failed for plugin " << path);
            incCounter(diag_, "plugins.codesign_invalid");
            (void)vstore_->insert(cdhash, Verdict{ Verdict::Code::kDenied });
            return PluginResult<PluginDescriptor>::failure(PluginError::kCodesignInvalid);
        }

        // Store successful verification
        (void)vstore_->insert(cdhash, Verdict{ Verdict::Code::kAllowed });
    }

    // ------------------------ dlopen -------------------------------------
    auto lib_ptr = std::make_shared<SharedLibrary>(path, RTLD_LOCAL | RTLD_FIRST | RTLD_NOW);
    if (!(*lib_ptr)) {
        incCounter(diag_, "plugins.dlopen_failed");
        return PluginResult<PluginDescriptor>::failure(PluginError::kDlopenFailed);
    }

    using GetInfoFn = ::KaiPluginInfo (*)();
    auto* sym = reinterpret_cast<GetInfoFn>(lib_ptr->symbol(kPluginSymbolName));
    if (!sym) {
        ERR("Symbol kai_plugin_get_info missing in " << path);
        incCounter(diag_, "plugins.missing_symbol");
        return PluginResult<PluginDescriptor>::failure(PluginError::kMissingSymbol);
    }

    ::KaiPluginInfo info = sym();

    // seatbelt blob -----------------------------------
#ifdef __APPLE__
    const std::string prefix = std::string(info.id);
    std::string blobSym = prefix + "_seatbelt_blob";
    std::string lenSym  = prefix + "_seatbelt_blob_len";
    const unsigned char* sb_blob = reinterpret_cast<const unsigned char*>(lib_ptr->symbol(blobSym.c_str()));
    const unsigned int*  sb_len  = reinterpret_cast<const unsigned int*>(lib_ptr->symbol(lenSym.c_str()));
    const bool has_embedded_sb = sb_blob && sb_len && *sb_len > 0;
    if (has_embedded_sb) {
        char* err_msg = nullptr;
        int rc = sandbox_init(reinterpret_cast<const char*>(sb_blob), 0, &err_msg);
        if (rc != 0) {
            if (err_msg) {
                ERR("sandbox_init failed: " << err_msg);
                free(err_msg);
            }
            return PluginResult<PluginDescriptor>::failure(PluginError::kSeatbeltInvalid);
        }
    } else {
        // Fall back to external .sb file in same directory
        std::filesystem::path sbPath = path;
        sbPath.replace_extension(".sb");
        if (std::filesystem::exists(sbPath)) {
            char* compileErr = nullptr;
#ifdef KAI_HAS_LIBSANDBOX
            void* compiled = nullptr;
            void* libSandbox = dlopen("/usr/lib/system/libsystem_sandbox.dylib", RTLD_LAZY);
            using CompileFn = int (*)(const char*, uint64_t, char**, char**);
            auto compile_fn = reinterpret_cast<CompileFn>(dlsym(libSandbox, "sandbox_compile_file"));
            int rc = -1;
            if (compile_fn) {
                rc = compile_fn(sbPath.c_str(), /*flags=*/0, reinterpret_cast<char**>(&compiled), &compileErr);
            } else {
                DBG("Seatbelt stub: sandbox_compile_file symbol unavailable; skipping compile");
                rc = 0;
            }
            if (rc != 0) {
                ERR("Seatbelt compile failed for plugin " << path);
                if (compileErr) free(compileErr);
                incCounter(diag_, "plugins.seatbelt_invalid");
                return PluginResult<PluginDescriptor>::failure(PluginError::kSeatbeltInvalid);
            }
            if (compiled) free(compiled);
            if (libSandbox) dlclose(libSandbox);
#else
            DBG("Seatbelt stub: presence of profile " << sbPath << " verified");
#endif
        }
    }
#endif

    // ABI check ------------------------------------------------------------
    if (info.abi_major != kKaiAbiMajor || info.abi_minor > kKaiAbiMinor) {
        ERR("ABI incompatible plugin " << path);
        incCounter(diag_, "plugins.abi_incompatible");
        return PluginResult<PluginDescriptor>::failure(PluginError::kAbiIncompatible);
    }

    // Capabilities presence ------------------------------------------------
    bool has_cap = false;
    for (uint8_t byte : info.capabilities) {
        if (byte != 0) { has_cap = true; break; }
    }
    if (!has_cap && info.runtime != KAI_RUNTIME_NULL) {
        ERR("Plugin " << path << " declares no capabilities – rejecting");
        incCounter(diag_, "plugins.capabilities_missing");
        return PluginResult<PluginDescriptor>::failure(PluginError::kCapabilitiesMissing);
    }

#ifdef __APPLE__
    // Seatbelt profile (stub) ---------------------------------------------
    std::filesystem::path sbPath = path;
    if (!has_embedded_sb) {
        sbPath.replace_extension(".sb");
    }
    if (std::filesystem::exists(sbPath)) {
        char* compileErr = nullptr;
#ifdef KAI_HAS_LIBSANDBOX
        void* compiled = nullptr;
        void* libSandbox = dlopen("/usr/lib/system/libsystem_sandbox.dylib", RTLD_LAZY);
        using CompileFn = int (*)(const char*, uint64_t, char**, char**);
        auto compile_fn = reinterpret_cast<CompileFn>(dlsym(libSandbox, "sandbox_compile_file"));
        int rc = -1;
        if (compile_fn) {
            rc = compile_fn(sbPath.c_str(), /*flags=*/0, reinterpret_cast<char**>(&compiled), &compileErr);
        } else {
            DBG("Seatbelt stub: sandbox_compile_file symbol unavailable; skipping compile");
            rc = 0;
        }
        if (rc != 0) {
            ERR("Seatbelt compile failed for plugin " << path);
            if (compileErr) free(compileErr);
            incCounter(diag_, "plugins.seatbelt_invalid");
            return PluginResult<PluginDescriptor>::failure(PluginError::kSeatbeltInvalid);
        }
        if (compiled) free(compiled);
        if (libSandbox) dlclose(libSandbox);
#else
        DBG("Seatbelt stub: presence of profile " << sbPath << " verified");
#endif
    }
#endif // __APPLE__

#if !defined(KAI_USE_MIMALLOC) && defined(RPMALLOC_FIRST_CLASS_HEAPS) && (RPMALLOC_FIRST_CLASS_HEAPS)
    rpmalloc_heap_t* heap_ptr = nullptr;
    if (arena_) {
        heap_ptr = arena_->acquireHeap(std::string(info.id));
    }
#endif

    incCounter(diag_, "plugins.discovered");
    PluginDescriptor desc{path, info, st.st_uid
#if !defined(KAI_USE_MIMALLOC) && defined(RPMALLOC_FIRST_CLASS_HEAPS) && (RPMALLOC_FIRST_CLASS_HEAPS)
        ,heap_ptr
#endif
    };
    desc.lib = std::move(lib_ptr);

    // Note: insertion into descriptors_ happens in scanDirectory to avoid
    // duplicate entries when probeLibrary() is called multiple times for the
    // same path.

    // initialise plugin via exported function if present
    using InitFn = int (*)(launcher::core::foundation::ServiceRegistry*);
    auto* initFn = reinterpret_cast<InitFn>(desc.lib->symbol("kai_plugin_initialize"));
    if (initFn) {
        int rc = initFn(&getRegistry()); // need registry accessor
        if (rc != 0) {
            ERR("kai_plugin_initialize failed for " << info.id);
            return PluginResult<PluginDescriptor>::failure(PluginError::kRuntimeInitFailed);
        }
    }

    return PluginResult<PluginDescriptor>::success(std::move(desc));
}

RuntimeManagerSvc::RuntimeManagerSvc(foundation::ServiceRegistry& registry,
                                     security::VerificationStore& store)
    : foundation::ServiceBase<RuntimeManagerSvc,
                              launcher::core::diagnostics::DiagnosticsService,
                              launcher::core::events::EventBusService,
                              launcher::core::memory::ArenaAllocatorSvc>(registry),
      vstore_{&store}, registry_{registry} {
    diag_    = &this->template get<launcher::core::diagnostics::DiagnosticsService>();
    eventBus_= &this->template get<launcher::core::events::EventBusService>();
    arena_   = &this->template get<launcher::core::memory::ArenaAllocatorSvc>();

    registry.forEachService([this](foundation::IService& svc){
        if(auto* pc = dynamic_cast<interfaces::IProbeCache*>(&svc)) {
            probe_cache_ = pc;
        }
        if(auto* cfg = dynamic_cast<::launcher::core::IConfigManager*>(&svc)) {
            config_ = cfg;
        }
    });
}

// ----------------------------- instantiateRuntime ---------------------------------
bool RuntimeManagerSvc::instantiateRuntime(const PluginDescriptor& desc) {
    std::unique_lock lock(mtx_);
    // Skip if already loaded
    bool already_loaded = std::any_of(runtimes_.begin(), runtimes_.end(), [&](const PluginRuntime& pr){
        return std::string(pr.descriptor.info.id) == desc.info.id;
    });
    if (already_loaded) return true;

    std::unique_ptr<RuntimeBase> rt;
    switch (static_cast<RuntimeKind>(desc.info.runtime)) {
        case RuntimeKind::kNull:
            rt = std::make_unique<NullRuntime>();
            break;
        default:
            ERR("Unsupported runtime kind=" << static_cast<int>(desc.info.runtime));
            if (eventBus_) {
                auto evt = std::make_shared<launcher::core::plugins::PluginRuntimeErrorEvent>();
                evt->id = desc.info.id;
                evt->error = static_cast<int>(foundation::KaiError::UnsupportedRuntime);
                eventBus_->publish(std::static_pointer_cast<const launcher::core::plugins::PluginRuntimeErrorEvent>(evt));
            }
            return false;
    }

    // Apply seatbelt profile if present ---------------------------------------------------
#ifdef __APPLE__
    {
        std::filesystem::path sbPath = desc.path;
        sbPath.replace_extension(".sb");
        if (std::filesystem::exists(sbPath)) {
            auto sbRes = security::applySandboxProfile(sbPath);
            if (!sbRes) {
                incCounter(diag_, "plugins.capability_denied");
                // Publish error event
                if (eventBus_) {
                    auto evt = std::make_shared<launcher::core::plugins::PluginRuntimeErrorEvent>();
                    evt->id = desc.info.id;
                    evt->error = static_cast<int>(foundation::KaiError::CapabilityDenied);
                    eventBus_->publish(std::static_pointer_cast<const launcher::core::plugins::PluginRuntimeErrorEvent>(evt));
                }
                return false;
            }
        }
    }
#endif

    // Optional per-plugin heap switch – ensures runtime init allocates in plugin heap.
#if !defined(KAI_USE_MIMALLOC) && defined(RPMALLOC_FIRST_CLASS_HEAPS) && (RPMALLOC_FIRST_CLASS_HEAPS)
    memory::PluginHeapScope heapScope(desc.heap);
#endif

    auto initRes = rt->initialise(desc.info);
    if (!initRes) {
        ERR("Runtime initialise failed for plugin " << desc.info.id);
        if (eventBus_) {
            auto evt = std::make_shared<launcher::core::plugins::PluginRuntimeErrorEvent>();
            evt->id = desc.info.id;
            evt->error = static_cast<int>(foundation::KaiError::RuntimeInitFailed);
            eventBus_->publish(std::static_pointer_cast<const launcher::core::plugins::PluginRuntimeErrorEvent>(evt));
        }
        return false;
    }

    runtimes_.push_back(PluginRuntime{desc, std::move(rt)});
    incCounter(diag_, "plugins.loaded_total");
    // mark registry loaded if present
    if (auto* m = pregistry_.find(desc.info.id)) { m->loaded = true; }
    return true;
}

// ---------------------------- loadManifests ------------------------------
foundation::KaiExpected<void> RuntimeManagerSvc::loadManifests() {
    using clock = std::chrono::high_resolution_clock;
    std::unique_lock lock(mtx_);
    const auto t0 = clock::now();

    descriptors_.clear();
    pregistry_.clear();

    // Heuristic: count manifest files across plugin roots to pre-reserve registry
    std::size_t estimate = 0;
    for (const auto& root : enumeratePluginRoots()) {
        if (!std::filesystem::exists(root)) continue;
        for (const auto& entry : std::filesystem::directory_iterator(root)) {
            if (!entry.is_regular_file()) continue;
            const auto& p = entry.path();
            if (p.extension() == ".toml" || hasDylibSuffix(p)) {
                ++estimate;
            }
        }
    }
    if (estimate) {
        pregistry_.reserve(estimate + 16); // headroom
    }

    for (const auto& dir : enumeratePluginRoots()) {
        (void)scanManifestDirectory(dir);
    }

    const auto ms = std::chrono::duration_cast<std::chrono::milliseconds>(clock::now() - t0).count();

    if (diag_) {
        diag_->setGauge("plugins.manifest_scan_ms", ms);
    }

    if (eventBus_) {
        auto evt = std::make_shared<PluginScanCompleteEvent>();
        evt->descriptors.assign(descriptors_.begin(), descriptors_.end());
        eventBus_->publish(std::static_pointer_cast<const PluginScanCompleteEvent>(evt));
    }

    return foundation::KaiExpected<void>::success();
}

// ---------------------------- activatePlugin -----------------------------
foundation::KaiExpected<void> RuntimeManagerSvc::activatePlugin(std::string_view id) {
    std::unique_lock lock(mtx_);
    if (isLoaded(id)) return foundation::KaiExpected<void>::success();
    auto* meta = pregistry_.find(id);
    if (!meta) return foundation::KaiExpected<void>::failure(foundation::KaiError::NotRegistered);

    if (!instantiateRuntime(meta->descriptor)) {
        return foundation::KaiExpected<void>::failure(foundation::KaiError::RuntimeInitFailed);
    }
    meta->loaded = true;
    return foundation::KaiExpected<void>::success();
}

bool RuntimeManagerSvc::isLoaded(std::string_view id) const noexcept {
    std::shared_lock lock(mtx_);
    auto* meta = const_cast<PluginRegistry&>(pregistry_).find(id);
    return meta ? meta->loaded : false;
}

} // namespace launcher::core::plugins 