#pragma once

#include <vector>
#include <string>
#include <string_view>
#include <optional>
#include <span>
#include <absl/container/flat_hash_map.h>
#include "plugin_descriptor.h"
#include <shared_mutex>

namespace launcher::core::plugins {

struct PluginMeta {
    PluginDescriptor descriptor;
    bool             enabled{true}; // config toggle
    bool             loaded{false}; // runtime instantiated
    std::string      version;       // semantic version
};

class PluginRegistry {
 public:
    void add(const PluginDescriptor& d, bool enabled);
    std::span<const PluginMeta> list() const noexcept { return metas_; }
    PluginMeta* find(std::string_view id) noexcept;
    void reserve(std::size_t n) {
        metas_.reserve(n);
        index_.reserve(n);
    }
    void clear() {
        std::unique_lock lock(mtx_);
        metas_.clear();
        index_.clear();
    }
    // Thread-safe snapshot for read-only consumers
    std::vector<PluginMeta> snapshot() const {
        std::shared_lock lock(mtx_);
        return std::vector<PluginMeta>(metas_.begin(), metas_.end());
    }
 private:
    mutable std::shared_mutex               mtx_;
    std::vector<PluginMeta>             metas_;
    absl::flat_hash_map<std::string, std::size_t> index_;
};

} // namespace launcher::core::plugins 