#pragma once

// -----------------------------------------------------------------------------
// @file abi.h
// @brief Plain-C ABI definitions shared between host and plugins.
//        Must stay header-only and C-compatible.
// -----------------------------------------------------------------------------

#include <cstdint>
#include <cstddef>

#ifdef __cplusplus
extern "C" {
#endif

// Forward enum values as uint32_t so that plugins can be written in C.
enum KaiRuntimeKind : uint32_t {
    KAI_RUNTIME_NULL        = 0,
    KAI_RUNTIME_JAVASCRIPT  = 1,
    KAI_RUNTIME_WASM_NATIVE = 2,
    KAI_RUNTIME_NATIVE_XPC  = 3,
};

// ABI version negotiated between host and plugin loader.  Increment MAJOR on
// breaking changes, bump MINOR on additive changes.  Slice-1 hard-codes 1.0.
static const uint32_t kKaiAbiMajor = 1;
static const uint32_t kKaiAbiMinor = 0;

// 256-bit capability bitmap – future-proof.  Layout identical to host side.
typedef struct KaiPluginInfo {
    char            id[32];            // NUL-terminated plugin identifier
    uint32_t        abi_major;        // must equal kKaiAbiMajor
    uint32_t        abi_minor;        // min required host minor ≤ ours
    uint8_t         capabilities[32]; // 256-bit mask (little-endian)
    KaiRuntimeKind  runtime;          // enum discriminant
} KaiPluginInfo;

#ifdef __cplusplus
} // extern "C"
#endif 