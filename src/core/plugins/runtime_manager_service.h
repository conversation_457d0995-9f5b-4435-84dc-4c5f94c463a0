#pragma once

// -----------------------------------------------------------------------------
// @file runtime_manager_service.h
// @brief Discovers plugins and instantiates a Runtime for each (Slice-1: NullRuntime)
// -----------------------------------------------------------------------------

#include <absl/container/inlined_vector.h>
#include <filesystem>
#include <memory>
#include <vector>
#include <shared_mutex>

#include "abi.h"
#include "runtime_base.h"
#include "null_runtime.h"
#include "shared_library.h"
#include "plugin_registry.h"
#include "plugin_descriptor.h"

#include "../foundation/iservice.h"
#include "../foundation/service_base.h"
#include "../foundation/registry.h"               // KaiExpected alias
#include "../util/expected.h"
#include "../memory/arena_allocator_service.h"
#include "../memory/plugin_heap_scope.h"
#include "../util/debug.h"
#include "core/security/verification_store.hh"  // VerificationStore dependency
#include "core/interfaces/probe_cache_api.h"
#include "core/interfaces/iconfig_manager.h"
#include "../events/event_bus_service.h"

namespace launcher::core {
namespace diagnostics { class DiagnosticsService; }
namespace events { class EventBusService; }
class IConfigManager;  // forward decl
}

namespace launcher::core::plugins {

// ---------------------------------------------------------------------------
// Error codes reused from legacy PluginManagerSvc probe path
// ---------------------------------------------------------------------------

enum class PluginError {
    kStatFailed,
    kUidMismatch,
    kCodesignInvalid,
    kDlopenFailed,
    kMissingSymbol,
    kAbiIncompatible,
    kSeatbeltInvalid,
    kCapabilitiesMissing,
    kPermInsecure,
    kRuntimeInitFailed,
};

template <typename T>
using PluginResult = launcher::core::util::Expected<T, PluginError>;

// ---------------------------------------------------------------------------
// RuntimeManagerSvc – replaces PluginManagerSvc
// ---------------------------------------------------------------------------
class RuntimeManagerSvc final
    : public launcher::core::foundation::ServiceBase<RuntimeManagerSvc,
                                                    launcher::core::diagnostics::DiagnosticsService,
                                                    launcher::core::events::EventBusService,
                                                    launcher::core::memory::ArenaAllocatorSvc> {
 public:
    static constexpr launcher::core::foundation::ServiceId kId =
        launcher::core::foundation::ServiceId::kRuntimeManagerSvc;

    // IService id() via ServiceBase
    launcher::core::util::Result<void> start() override;  // performs discovery
    void                             stop() noexcept override;

    // Public API ---------------------------------------------------------
    launcher::core::foundation::KaiExpected<void> loadPlugins();

    // New two-phase APIs -------------------------------------------------
    foundation::KaiExpected<void> loadManifests(); // fast, manifest-only
    foundation::KaiExpected<void> activatePlugin(std::string_view id);
    bool                          isLoaded(std::string_view id) const noexcept;

    // NEW: Inject configuration after construction (for cases where
    // ConfigManager isn't yet registered at ctor time).
    void setConfig(launcher::core::IConfigManager* cfg) noexcept { config_ = cfg; }

    // Unit-test helper
    PluginResult<PluginDescriptor> probeLibrary(const std::filesystem::path&);

    // Dependency pointers retrieved via ServiceBase
    launcher::core::diagnostics::DiagnosticsService* diag_{nullptr};
    launcher::core::events::EventBusService*         eventBus_{nullptr};
    launcher::core::memory::ArenaAllocatorSvc*       arena_{nullptr};
    launcher::core::security::VerificationStore*     vstore_{nullptr};
    launcher::core::interfaces::IProbeCache*         probe_cache_{nullptr};
    launcher::core::IConfigManager*             config_{nullptr};

    launcher::core::events::SubscriptionHandle plugin_toggle_handle_{};

    foundation::ServiceRegistry& registry_;

    [[nodiscard]] foundation::ServiceRegistry& getRegistry() noexcept { return registry_; }

    // Legacy snapshot access (deprecated). Prefer registry().list().
    const std::vector<PluginDescriptor>& descriptors() const noexcept { return descriptors_; }

    // New accessor
    const PluginRegistry& registry() const noexcept { return pregistry_; }

    explicit RuntimeManagerSvc(launcher::core::foundation::ServiceRegistry& registry,
                               launcher::core::security::VerificationStore& store);

    ~RuntimeManagerSvc() override { stop(); }

 private:
    // Internal helpers
    launcher::core::util::Result<void> scanManifestDirectory(const std::filesystem::path& dir);
    launcher::core::util::Result<void> scanDirectory(const std::filesystem::path& dir);

    struct PluginRuntime {
        PluginDescriptor                    descriptor;
        std::unique_ptr<RuntimeBase>        runtime;
    };

    std::vector<PluginRuntime> runtimes_;

    // Caches during scanning before loadPlugins() is called
    std::vector<PluginDescriptor> descriptors_;

    bool loaded_{false};

    PluginRegistry pregistry_;

    mutable std::shared_mutex mtx_;

    // Instantiate the runtime described by |desc|. Publishes PluginRuntimeErrorEvent on failure.
    [[nodiscard]] bool instantiateRuntime(const PluginDescriptor& desc);
};

} // namespace launcher::core::plugins 