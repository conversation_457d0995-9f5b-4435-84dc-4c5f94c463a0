/*
 * @file plugin_events.h
 * @brief Lightweight event structs shared between core and UI for plugin state changes.
 */

#pragma once

#include <string>

namespace launcher::core::plugins {

//------------------------------------------------------------------------------
// Published by UI when user toggles a plugin enable-checkbox in Preferences ▸
// Plugins pane.  RuntimeManagerSvc subscribes to unload/reload the runtime.
//------------------------------------------------------------------------------
struct PluginStateChangeEvent {
    std::string id;      // Plugin identifier (ASCII, ≤32 chars)
    bool        enabled; // true when user enabled, false when disabled
};

struct PluginRuntimeErrorEvent {
    std::string id;   // plugin identifier
    int         error; // numeric error code (KaiError or PluginError enum cast)
};

} // namespace launcher::core::plugins 