#include "plugin_registry.h"
#include "runtime_manager_service.h"
#include <string>
#include <shared_mutex>

namespace launcher::core::plugins {

void PluginRegistry::add(const PluginDescriptor& d, bool enabled) {
    std::unique_lock lock(mtx_);
    std::string id_str(d.info.id);
    if (index_.contains(id_str)) return; // skip duplicate
    std::size_t pos = metas_.size();
    metas_.push_back(PluginMeta{d, enabled, false});
    index_.emplace(std::move(id_str), pos);
}

PluginMeta* PluginRegistry::find(std::string_view id) noexcept {
    std::shared_lock lock(mtx_);
    auto it = index_.find(std::string{id});
    if (it == index_.end()) return nullptr;
    return &metas_[it->second];
}

} // namespace launcher::core::plugins 