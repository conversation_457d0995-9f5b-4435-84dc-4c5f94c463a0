#pragma once

// -----------------------------------------------------------------------------
// @file null_runtime.h
// @brief Trivial runtime used for Slice-1 validation.  Performs no work.
// -----------------------------------------------------------------------------

#include "runtime_base.h"
#include "abi.h"

namespace launcher::core::plugins {

class NullRuntime : public RuntimeBase {
 public:
    [[nodiscard]] RuntimeKind kind() const noexcept override { return RuntimeKind::kNull; }

    launcher::core::util::Expected<void, int> initialise(const KaiPluginInfo&) override {
        // Nothing to do.  Always succeed.
        return launcher::core::util::Expected<void, int>::success();
    }

    void shutdown() noexcept override {}
};

} // namespace launcher::core::plugins 