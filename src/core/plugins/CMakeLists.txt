# Plugin subsystem – compiled into existing `core` static library

set(PLUGIN_SOURCES
    runtime_base.h
    null_runtime.h
    shared_library.h
    runtime_manager_service.h
    runtime_manager_service.cpp
    plugin_registry.h
    plugin_registry.cpp
)

# codesign_check.* deprecated – replaced by core/security/codesign_verify.*

# Header-only files do not need compilation but include for IDEs
source_group("plugins" FILES ${PLUGIN_SOURCES})

target_sources(core PRIVATE ${PLUGIN_SOURCES}) 