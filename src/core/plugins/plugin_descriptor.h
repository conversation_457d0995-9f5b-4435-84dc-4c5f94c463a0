#pragma once

// -----------------------------------------------------------------------------
// @file plugin_descriptor.h
// @brief Lightweight description of a plugin discovered during probing or
//        manifest scan.  Lives in a standalone header so core structures such
//        as PluginRegistry and RuntimeManagerSvc can depend on it without
//        forming cyclic includes.
// -----------------------------------------------------------------------------

#include <filesystem>
#include <memory>
#include <absl/container/inlined_vector.h>
#include "abi.h"
#include <sys/types.h>

#if !defined(KAI_USE_MIMALLOC) && defined(RPMALLOC_FIRST_CLASS_HEAPS) && (RPMALLOC_FIRST_CLASS_HEAPS)
#  include <rpmalloc.h>
#endif

namespace launcher::core::plugins {

struct SharedLibrary; // forward declaration – defined in shared_library.h

// Descriptor captured during probe/scan.  Kept trivial & POD-like so it can be
// stored by value in vectors without heavy construction cost.
struct PluginDescriptor {
    std::filesystem::path path;      // Path to dylib (or manifest-derived)
    ::KaiPluginInfo       info{};    // ABI-level metadata exposed by plugin
    uid_t                 owner_uid{0};
#if !defined(KAI_USE_MIMALLOC) && defined(RPMALLOC_FIRST_CLASS_HEAPS) && (RPMALLOC_FIRST_CLASS_HEAPS)
    rpmalloc_heap_t*      heap{nullptr};
#endif
    // Retain dlopen handle to keep image resident – avoids costly unloads
    std::shared_ptr<SharedLibrary> lib;
};

// Event payload published once a directory/manifest scan completes.
struct PluginScanCompleteEvent {
    absl::InlinedVector<PluginDescriptor, 16> descriptors;
};

} // namespace launcher::core::plugins 