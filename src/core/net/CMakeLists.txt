# Core networking components (HTTP, MCP, etc.)
add_library(core_net STATIC
    ../mcp/mcp_client_service.cpp
)

# Public headers for MCP client
target_include_directories(core_net
    PUBLIC
        ${CMAKE_CURRENT_SOURCE_DIR}/../mcp
        ${CMAKE_SOURCE_DIR}/include
        ${CMAKE_BINARY_DIR}/generated/services
)

# Dependencies
# - http_client library defined in src/core/http/CMakeLists
# - nlohmann_json for serialization

add_dependencies(core_net http_client generate_service_headers)

target_link_libraries(core_net
    PUBLIC
        http_client
        ${KAI_ALLOCATOR_LIB}
        nlohmann_json::nlohmann_json
) 