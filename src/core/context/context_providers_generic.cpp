#include <unistd.h>

#include <chrono>
#include <ctime>
#include <filesystem>

#include "context_providers.h"

namespace launcher {
namespace core {

// ClipboardContextProvider implementation
void ClipboardContextProvider::populateContext(Context& context) {
    // Generic implementation doesn't have clipboard access
    context.addValue("clipboard.available", ContextValue::fromBoolean(false), getName());
    context.addValue(
        "clipboard.reason",
        ContextValue::fromString("Clipboard access is not implemented for this platform"),
        getName());

    // Add information about supported platforms
    nlohmann::json supportedPlatforms = nlohmann::json::array();
    supportedPlatforms.push_back("macOS");
    supportedPlatforms.push_back("Windows");
    context.addValue("clipboard.supported_platforms", ContextValue::fromObject(supportedPlatforms),
                     getName());
}

bool ClipboardContextProvider::isAvailable() const {
    return false;  // Not available in generic implementation
}

// ActiveWindowContextProvider implementation
void ActiveWindowContextProvider::populateContext(Context& context) {
    // Generic implementation doesn't have window information
    context.addValue("active_window.available", ContextValue::fromBoolean(false), getName());
}

bool ActiveWindowContextProvider::isAvailable() const {
    return false;  // Not available in generic implementation
}

// SelectedTextContextProvider implementation
void SelectedTextContextProvider::populateContext(Context& context) {
    // Generic implementation doesn't have text selection capabilities
    context.addValue("selected_text.available", ContextValue::fromBoolean(false), getName());
    context.addValue(
        "selected_text.reason",
        ContextValue::fromString("Text selection access is not implemented for this platform"),
        getName());

    // Add information about supported platforms
    nlohmann::json supportedPlatforms = nlohmann::json::array();
    supportedPlatforms.push_back("macOS");
    supportedPlatforms.push_back("Windows");
    context.addValue("selected_text.supported_platforms",
                     ContextValue::fromObject(supportedPlatforms), getName());

    // Add information about required permissions
    nlohmann::json requiredPermissions = nlohmann::json::array();
    requiredPermissions.push_back("Accessibility (macOS)");
    requiredPermissions.push_back("UI Automation (Windows)");
    context.addValue("selected_text.required_permissions",
                     ContextValue::fromObject(requiredPermissions), getName());

    // Add information about supported applications
    nlohmann::json supportedApps = nlohmann::json::object();

    nlohmann::json macOSApps = nlohmann::json::array();
    macOSApps.push_back("Safari");
    macOSApps.push_back("Chrome");
    macOSApps.push_back("Brave");
    macOSApps.push_back("Microsoft Edge");
    macOSApps.push_back("Finder");
    macOSApps.push_back("Xcode");
    macOSApps.push_back("Microsoft Word");
    macOSApps.push_back("Microsoft Excel");
    macOSApps.push_back("Microsoft PowerPoint");
    macOSApps.push_back("TextEdit");

    nlohmann::json windowsApps = nlohmann::json::array();
    windowsApps.push_back("Chrome");
    windowsApps.push_back("Microsoft Edge");
    windowsApps.push_back("Firefox");
    windowsApps.push_back("Brave");
    windowsApps.push_back("File Explorer");
    windowsApps.push_back("Visual Studio");
    windowsApps.push_back("Microsoft Word");
    windowsApps.push_back("Microsoft Excel");
    windowsApps.push_back("Microsoft PowerPoint");
    windowsApps.push_back("Notepad");
    windowsApps.push_back("WordPad");

    supportedApps["macOS"] = macOSApps;
    supportedApps["Windows"] = windowsApps;

    context.addValue("selected_text.supported_applications",
                     ContextValue::fromObject(supportedApps), getName());
}

bool SelectedTextContextProvider::isAvailable() const {
    return false;  // Not available in generic implementation
}

// OpenFileContextProvider implementation
void OpenFileContextProvider::populateContext(Context& context) {
    // Generic implementation doesn't have open file information
    context.addValue("open_file.available", ContextValue::fromBoolean(false), getName());
}

bool OpenFileContextProvider::isAvailable() const {
    return false;  // Not available in generic implementation
}

// CurrentDirectoryContextProvider implementation
void CurrentDirectoryContextProvider::populateContext(Context& context) {
    // Get current working directory
    char cwd[PATH_MAX];
    if (getcwd(cwd, sizeof(cwd)) != nullptr) {
        context.addValue("current_directory.path", ContextValue::fromString(cwd), getName());

        // Get parent directory
        std::filesystem::path path(cwd);
        if (path.has_parent_path()) {
            context.addValue("current_directory.parent",
                             ContextValue::fromString(path.parent_path().string()), getName());
        }

        // Get directory name
        context.addValue("current_directory.name",
                         ContextValue::fromString(path.filename().string()), getName());
    }
}

bool CurrentDirectoryContextProvider::isAvailable() const {
    return true;  // Always available
}

// CurrentApplicationContextProvider implementation
void CurrentApplicationContextProvider::populateContext(Context& context) {
    // Generic implementation doesn't have application information
    context.addValue("current_application.available", ContextValue::fromBoolean(false), getName());
}

bool CurrentApplicationContextProvider::isAvailable() const {
    return false;  // Not available in generic implementation
}

// TimeLocationContextProvider implementation
void TimeLocationContextProvider::populateContext(Context& context) {
    // Get current time
    auto now = std::chrono::system_clock::now();
    auto time_t_now = std::chrono::system_clock::to_time_t(now);
    std::tm* local_time = std::localtime(&time_t_now);

    if (local_time) {
        // Add time components
        context.addValue("time.hour", ContextValue::fromNumber(local_time->tm_hour), getName());
        context.addValue("time.minute", ContextValue::fromNumber(local_time->tm_min), getName());
        context.addValue("time.second", ContextValue::fromNumber(local_time->tm_sec), getName());
        context.addValue("time.day", ContextValue::fromNumber(local_time->tm_mday), getName());
        context.addValue("time.month", ContextValue::fromNumber(local_time->tm_mon + 1),
                         getName());  // tm_mon is 0-based
        context.addValue("time.year", ContextValue::fromNumber(local_time->tm_year + 1900),
                         getName());  // tm_year is years since 1900
        context.addValue("time.weekday", ContextValue::fromNumber(local_time->tm_wday),
                         getName());  // 0 = Sunday

        // Format time as string
        char timeStr[100];
        std::strftime(timeStr, sizeof(timeStr), "%Y-%m-%d %H:%M:%S", local_time);
        context.addValue("time.iso_string", ContextValue::fromString(timeStr), getName());
    }

    // Location is not available in generic implementation
    context.addValue("location.available", ContextValue::fromBoolean(false), getName());
}

bool TimeLocationContextProvider::isAvailable() const {
    return true;  // Time is always available, location is not
}

// ContextProviderFactory implementation for generic platforms
#if !defined(__APPLE__) && !defined(_WIN32)
std::vector<std::unique_ptr<ContextProvider>> ContextProviderFactory::createAllProviders() {
    std::vector<std::unique_ptr<ContextProvider>> providers;

    // Create all providers
    providers.push_back(std::make_unique<ClipboardContextProvider>());
    providers.push_back(std::make_unique<ActiveWindowContextProvider>());
    providers.push_back(std::make_unique<SelectedTextContextProvider>());
    providers.push_back(std::make_unique<OpenFileContextProvider>());
    providers.push_back(std::make_unique<CurrentDirectoryContextProvider>());
    providers.push_back(std::make_unique<CurrentApplicationContextProvider>());
    providers.push_back(std::make_unique<TimeLocationContextProvider>());

    return providers;
}

std::unique_ptr<ContextProvider> ContextProviderFactory::createProvider(const std::string& name) {
    if (name == "clipboard") {
        return std::make_unique<ClipboardContextProvider>();
    } else if (name == "active_window") {
        return std::make_unique<ActiveWindowContextProvider>();
    } else if (name == "selected_text") {
        return std::make_unique<SelectedTextContextProvider>();
    } else if (name == "open_file") {
        return std::make_unique<OpenFileContextProvider>();
    } else if (name == "current_directory") {
        return std::make_unique<CurrentDirectoryContextProvider>();
    } else if (name == "current_application") {
        return std::make_unique<CurrentApplicationContextProvider>();
    } else if (name == "time_location") {
        return std::make_unique<TimeLocationContextProvider>();
    }

    return nullptr;
}
#endif

}  // namespace core
}  // namespace launcher