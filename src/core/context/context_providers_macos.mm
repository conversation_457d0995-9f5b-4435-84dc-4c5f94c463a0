#include "context_providers.h"
#include <chrono>
#include <ctime>
#include <filesystem>
#include <iostream>
#include <unistd.h>

// macOS specific includes
#import <AppKit/AppKit.h>
#import <Foundation/Foundation.h>
#import <CoreLocation/CoreLocation.h>

namespace launcher {
namespace core {

// Helper function to convert NSString to std::string
std::string nsStringToStdString(NSString* nsString) {
    if (!nsString) {
        return "";
    }
    return std::string([nsString UTF8String]);
}

// Helper function to get the frontmost application
NSRunningApplication* getFrontmostApplication() {
    return [[NSWorkspace sharedWorkspace] frontmostApplication];
}

// ClipboardContextProvider implementation
void ClipboardContextProvider::populateContext(Context& context) {
    NSPasteboard* pasteboard = [NSPasteboard generalPasteboard];
    
    // Get text from clipboard
    NSString* text = [pasteboard stringForType:NSPasteboardTypeString];
    if (text) {
        std::string clipboardText = nsStringToStdString(text);
        context.addValue("clipboard.text", ContextValue::fromString(clipboardText), getName());
        
        // Add text length as a separate value
        context.addValue("clipboard.text_length", ContextValue::fromNumber(clipboardText.length()), getName());
        
        // Add a preview (first 100 characters) for quick access
        if (clipboardText.length() > 100) {
            context.addValue("clipboard.text_preview", 
                            ContextValue::fromString(clipboardText.substr(0, 100) + "..."), 
                            getName());
        } else {
            context.addValue("clipboard.text_preview", ContextValue::fromString(clipboardText), getName());
        }
    }
    
    // Get HTML content from clipboard
    NSString* htmlContent = [pasteboard stringForType:NSPasteboardTypeHTML];
    if (htmlContent) {
        context.addValue("clipboard.html", ContextValue::fromString(nsStringToStdString(htmlContent)), getName());
    }
    
    // Get RTF content from clipboard
    NSData* rtfData = [pasteboard dataForType:NSPasteboardTypeRTF];
    if (rtfData) {
        NSString* rtfString = [[NSString alloc] initWithData:rtfData encoding:NSUTF8StringEncoding];
        if (rtfString) {
            context.addValue("clipboard.rtf_available", ContextValue::fromBoolean(true), getName());
            // We don't store the actual RTF data as it can be large and binary
        }
    }
    
    // Get file URLs from clipboard
    NSArray* urls = [pasteboard readObjectsForClasses:@[[NSURL class]] options:nil];
    if (urls && [urls count] > 0) {
        nlohmann::json fileUrls = nlohmann::json::array();
        nlohmann::json fileNames = nlohmann::json::array();
        nlohmann::json fileExtensions = nlohmann::json::array();
        
        for (NSURL* url in urls) {
            if ([url isFileURL]) {
                NSString* path = [url path];
                std::string pathStr = nsStringToStdString(path);
                fileUrls.push_back(pathStr);
                
                // Extract filename and extension
                NSString* fileName = [path lastPathComponent];
                fileNames.push_back(nsStringToStdString(fileName));
                
                NSString* fileExtension = [fileName pathExtension];
                if (fileExtension && [fileExtension length] > 0) {
                    fileExtensions.push_back(nsStringToStdString(fileExtension));
                }
            }
        }
        
        if (!fileUrls.empty()) {
            context.addValue("clipboard.files", ContextValue::fromObject(fileUrls), getName());
            context.addValue("clipboard.file_count", ContextValue::fromNumber(fileUrls.size()), getName());
            context.addValue("clipboard.file_names", ContextValue::fromObject(fileNames), getName());
            context.addValue("clipboard.file_extensions", ContextValue::fromObject(fileExtensions), getName());
        }
    }
    
    // Check for image data
    NSArray* imageTypes = @[NSPasteboardTypePNG, NSPasteboardTypeTIFF, NSPasteboardTypePDF];
    for (NSString* type in imageTypes) {
        NSData* imageData = [pasteboard dataForType:type];
        if (imageData) {
            NSString* typeStr = [type lastPathComponent]; // Get the type name without the UTI prefix
            context.addValue("clipboard.has_image", ContextValue::fromBoolean(true), getName());
            context.addValue("clipboard.image_type", ContextValue::fromString(nsStringToStdString(typeStr)), getName());
            context.addValue("clipboard.image_size", ContextValue::fromNumber([imageData length]), getName());
            break; // We only need to detect one image type
        }
    }
    
    // Add available types for debugging
    NSArray* types = [pasteboard types];
    if (types && [types count] > 0) {
        nlohmann::json availableTypes = nlohmann::json::array();
        for (NSString* type in types) {
            availableTypes.push_back(nsStringToStdString(type));
        }
        context.addValue("clipboard.available_types", ContextValue::fromObject(availableTypes), getName());
    }
}

bool ClipboardContextProvider::isAvailable() const {
    return true; // Always available on macOS
}

// ActiveWindowContextProvider implementation
void ActiveWindowContextProvider::populateContext(Context& context) {
    NSRunningApplication* app = getFrontmostApplication();
    if (!app) {
        return;
    }
    
    // Get window information
    NSString* appName = [app localizedName];
    NSString* bundleId = [app bundleIdentifier];
    
    if (appName) {
        context.addValue("active_window.app_name", ContextValue::fromString(nsStringToStdString(appName)), getName());
    }
    
    if (bundleId) {
        context.addValue("active_window.bundle_id", ContextValue::fromString(nsStringToStdString(bundleId)), getName());
    }
    
    // Get window title (this is more complex and may require Accessibility permissions)
    // For now, we'll just use the app name as a fallback
    context.addValue("active_window.title", ContextValue::fromString(nsStringToStdString(appName)), getName());
}

bool ActiveWindowContextProvider::isAvailable() const {
    return true; // Basic window info is always available on macOS
}

// SelectedTextContextProvider implementation
void SelectedTextContextProvider::populateContext(Context& context) {
    // We'll use multiple techniques to try to get selected text
    bool textFound = false;
    
    // 1. Try using Accessibility API (most reliable but requires permissions)
    NSString* selectedText = getSelectedTextViaAccessibility();
    if (selectedText && [selectedText length] > 0) {
        std::string text = nsStringToStdString(selectedText);
        
        context.addValue("selected_text", ContextValue::fromString(text), getName());
        context.addValue("selected_text.length", ContextValue::fromNumber(text.length()), getName());
        context.addValue("selected_text.source", ContextValue::fromString("accessibility_api"), getName());
        
        // Add a preview if the text is long
        if (text.length() > 100) {
            context.addValue("selected_text.preview", 
                            ContextValue::fromString(text.substr(0, 100) + "..."), 
                            getName());
        }
        
        textFound = true;
    }
    
    // 2. If Accessibility API failed, try application-specific methods
    if (!textFound) {
        // Get the frontmost application
        NSRunningApplication* app = getFrontmostApplication();
        if (app) {
            NSString* bundleId = [app bundleIdentifier];
            NSString* appName = [app localizedName];
            
            if (bundleId) {
                std::string bundleIdStr = nsStringToStdString(bundleId);
                
                // Try application-specific methods
                if ([bundleId isEqualToString:@"com.apple.Safari"] || 
                    [bundleId isEqualToString:@"com.google.Chrome"] ||
                    [bundleId isEqualToString:@"com.brave.Browser"] ||
                    [bundleId isEqualToString:@"com.microsoft.edgemac"]) {
                    // Browser-specific method using JavaScript
                    selectedText = getSelectedTextFromBrowser(bundleIdStr);
                } else if ([bundleId isEqualToString:@"com.apple.finder"]) {
                    // Finder-specific method
                    selectedText = getSelectedTextFromFinder();
                } else if ([bundleId hasPrefix:@"com.apple.dt.Xcode"]) {
                    // Xcode-specific method
                    selectedText = getSelectedTextFromXcode();
                } else if ([bundleId isEqualToString:@"com.microsoft.Word"] ||
                           [bundleId isEqualToString:@"com.microsoft.Excel"] ||
                           [bundleId isEqualToString:@"com.microsoft.Powerpoint"]) {
                    // Microsoft Office-specific method
                    selectedText = getSelectedTextFromOffice(bundleIdStr);
                }
                
                if (selectedText && [selectedText length] > 0) {
                    std::string text = nsStringToStdString(selectedText);
                    
                    context.addValue("selected_text", ContextValue::fromString(text), getName());
                    context.addValue("selected_text.length", ContextValue::fromNumber(text.length()), getName());
                    context.addValue("selected_text.source", ContextValue::fromString("app_specific"), getName());
                    context.addValue("selected_text.app", ContextValue::fromString(bundleIdStr), getName());
                    
                    // Add a preview if the text is long
                    if (text.length() > 100) {
                        context.addValue("selected_text.preview", 
                                        ContextValue::fromString(text.substr(0, 100) + "..."), 
                                        getName());
                    }
                    
                    textFound = true;
                }
            }
        }
    }
    
    // 3. If app-specific methods failed, try AppleScript (works for many apps but not all)
    if (!textFound) {
        selectedText = getSelectedTextViaAppleScript();
        if (selectedText && [selectedText length] > 0) {
            std::string text = nsStringToStdString(selectedText);
            
            context.addValue("selected_text", ContextValue::fromString(text), getName());
            context.addValue("selected_text.length", ContextValue::fromNumber(text.length()), getName());
            context.addValue("selected_text.source", ContextValue::fromString("applescript"), getName());
            
            // Add a preview if the text is long
            if (text.length() > 100) {
                context.addValue("selected_text.preview", 
                                ContextValue::fromString(text.substr(0, 100) + "..."), 
                                getName());
            }
            
            textFound = true;
        }
    }
    
    // 4. Last resort: Try clipboard method (least reliable, may interfere with user's clipboard)
    if (!textFound) {
        // Save current clipboard
        NSPasteboard* pasteboard = [NSPasteboard generalPasteboard];
        NSString* oldText = [pasteboard stringForType:NSPasteboardTypeString];
        
        // Simulate Cmd+C to copy selected text
        CGEventSourceRef source = CGEventSourceCreate(kCGEventSourceStateHIDSystemState);
        
        // Key down for Command
        CGEventRef cmdDown = CGEventCreateKeyboardEvent(source, 0x37, true);
        CGEventPost(kCGHIDEventTap, cmdDown);
        CFRelease(cmdDown);
        
        // Key down for C
        CGEventRef cDown = CGEventCreateKeyboardEvent(source, 0x08, true);
        CGEventSetFlags(cDown, kCGEventFlagMaskCommand);
        CGEventPost(kCGHIDEventTap, cDown);
        CFRelease(cDown);
        
        // Key up for C
        CGEventRef cUp = CGEventCreateKeyboardEvent(source, 0x08, false);
        CGEventSetFlags(cUp, kCGEventFlagMaskCommand);
        CGEventPost(kCGHIDEventTap, cUp);
        CFRelease(cUp);
        
        // Key up for Command
        CGEventRef cmdUp = CGEventCreateKeyboardEvent(source, 0x37, false);
        CGEventPost(kCGHIDEventTap, cmdUp);
        CFRelease(cmdUp);
        
        CFRelease(source);
        
        // Wait a bit for the clipboard to update
        [NSThread sleepForTimeInterval:0.1];
        
        // Check if clipboard has new text
        NSString* newText = [pasteboard stringForType:NSPasteboardTypeString];
        if (newText && ![newText isEqualToString:oldText] && [newText length] > 0) {
            std::string text = nsStringToStdString(newText);
            
            context.addValue("selected_text", ContextValue::fromString(text), getName());
            context.addValue("selected_text.length", ContextValue::fromNumber(text.length()), getName());
            context.addValue("selected_text.source", ContextValue::fromString("clipboard"), getName());
            
            // Add a preview if the text is long
            if (text.length() > 100) {
                context.addValue("selected_text.preview", 
                                ContextValue::fromString(text.substr(0, 100) + "..."), 
                                getName());
            }
            
            textFound = true;
        }
        
        // Restore original clipboard if needed
        if (oldText && ![oldText isEqualToString:newText]) {
            [pasteboard clearContents];
            [pasteboard writeObjects:@[oldText]];
        }
    }
    
    // If no text was found with any method
    if (!textFound) {
        context.addValue("selected_text.available", ContextValue::fromBoolean(false), getName());
    } else {
        context.addValue("selected_text.available", ContextValue::fromBoolean(true), getName());
    }
}

// Helper method to get selected text via Accessibility API
NSString* SelectedTextContextProvider::getSelectedTextViaAccessibility() {
    // Get the frontmost application
    AXUIElementRef appRef = NULL;
    pid_t pid = -1;
    NSRunningApplication* app = getFrontmostApplication();
    
    if (app) {
        pid = [app processIdentifier];
        appRef = AXUIElementCreateApplication(pid);
        if (!appRef) {
            return nil;
        }
    } else {
        return nil;
    }
    
    // Get the focused element
    AXUIElementRef focusedElement = NULL;
    AXError error = AXUIElementCopyAttributeValue(appRef, kAXFocusedUIElementAttribute, (CFTypeRef*)&focusedElement);
    
    if (error != kAXErrorSuccess || !focusedElement) {
        if (appRef) {
            CFRelease(appRef);
        }
        return nil;
    }
    
    // Try to get selected text from the focused element
    CFTypeRef selectedTextValue = NULL;
    error = AXUIElementCopyAttributeValue(focusedElement, kAXSelectedTextAttribute, &selectedTextValue);
    
    NSString* result = nil;
    if (error == kAXErrorSuccess && selectedTextValue) {
        if (CFGetTypeID(selectedTextValue) == CFStringGetTypeID()) {
            result = (__bridge_transfer NSString*)selectedTextValue;
        } else {
            CFRelease(selectedTextValue);
        }
    }
    
    // If no selected text, try to get the value (for text fields)
    if (!result) {
        CFTypeRef valueRef = NULL;
        error = AXUIElementCopyAttributeValue(focusedElement, kAXValueAttribute, &valueRef);
        
        if (error == kAXErrorSuccess && valueRef) {
            if (CFGetTypeID(valueRef) == CFStringGetTypeID()) {
                result = (__bridge_transfer NSString*)valueRef;
            } else {
                CFRelease(valueRef);
            }
        }
    }
    
    // Clean up
    if (focusedElement) {
        CFRelease(focusedElement);
    }
    if (appRef) {
        CFRelease(appRef);
    }
    
    return result;
}

// Helper method to get selected text via AppleScript
NSString* SelectedTextContextProvider::getSelectedTextViaAppleScript() {
    // Generic AppleScript to get selected text from the frontmost application
    NSString* script = @"tell application \"System Events\"\n"
                       @"    set frontApp to name of first application process whose frontmost is true\n"
                       @"    tell application frontApp\n"
                       @"        try\n"
                       @"            set selectedText to selection as text\n"
                       @"            return selectedText\n"
                       @"        on error\n"
                       @"            try\n"
                       @"                set selectedText to selected text\n"
                       @"                return selectedText\n"
                       @"            on error\n"
                       @"                return \"\"\n"
                       @"            end try\n"
                       @"        end try\n"
                       @"    end tell\n"
                       @"end tell";
    
    NSAppleScript* appleScript = [[NSAppleScript alloc] initWithSource:script];
    NSDictionary* errorDict = nil;
    NSAppleEventDescriptor* result = [appleScript executeAndReturnError:&errorDict];
    
    if (result && [result stringValue]) {
        return [result stringValue];
    }
    
    return nil;
}

// Helper method to get selected text from browsers using JavaScript
NSString* SelectedTextContextProvider::getSelectedTextFromBrowser(const std::string& bundleId) {
    NSString* script;
    
    if (bundleId == "com.apple.Safari") {
        script = @"tell application \"Safari\"\n"
                 @"    try\n"
                 @"        set selectedText to do JavaScript \"window.getSelection().toString()\" in current tab of front window\n"
                 @"        return selectedText\n"
                 @"    on error\n"
                 @"        return \"\"\n"
                 @"    end try\n"
                 @"end tell";
    } else if (bundleId == "com.google.Chrome" || bundleId == "com.brave.Browser" || bundleId == "com.microsoft.edgemac") {
        script = @"tell application \"Google Chrome\"\n"
                 @"    try\n"
                 @"        set selectedText to execute front window's active tab javascript \"window.getSelection().toString()\"\n"
                 @"        return selectedText\n"
                 @"    on error\n"
                 @"        return \"\"\n"
                 @"    end try\n"
                 @"end tell";
        
        // Replace application name based on bundle ID
        if (bundleId == "com.brave.Browser") {
            script = [script stringByReplacingOccurrencesOfString:@"Google Chrome" withString:@"Brave Browser"];
        } else if (bundleId == "com.microsoft.edgemac") {
            script = [script stringByReplacingOccurrencesOfString:@"Google Chrome" withString:@"Microsoft Edge"];
        }
    } else {
        return nil;
    }
    
    NSAppleScript* appleScript = [[NSAppleScript alloc] initWithSource:script];
    NSDictionary* errorDict = nil;
    NSAppleEventDescriptor* result = [appleScript executeAndReturnError:&errorDict];
    
    if (result && [result stringValue]) {
        return [result stringValue];
    }
    
    return nil;
}

// Helper method to get selected text from Finder
NSString* SelectedTextContextProvider::getSelectedTextFromFinder() {
    NSString* script = @"tell application \"Finder\"\n"
                       @"    try\n"
                       @"        set selectedItems to selection as alias list\n"
                       @"        set itemNames to {}\n"
                       @"        repeat with i in selectedItems\n"
                       @"            set end of itemNames to name of i\n"
                       @"        end repeat\n"
                       @"        return itemNames as text\n"
                       @"    on error\n"
                       @"        return \"\"\n"
                       @"    end try\n"
                       @"end tell";
    
    NSAppleScript* appleScript = [[NSAppleScript alloc] initWithSource:script];
    NSDictionary* errorDict = nil;
    NSAppleEventDescriptor* result = [appleScript executeAndReturnError:&errorDict];
    
    if (result && [result stringValue]) {
        return [result stringValue];
    }
    
    return nil;
}

// Helper method to get selected text from Xcode
NSString* SelectedTextContextProvider::getSelectedTextFromXcode() {
    NSString* script = @"tell application \"Xcode\"\n"
                       @"    try\n"
                       @"        tell front window\n"
                       @"            set selectedText to selected text of text view of front window\n"
                       @"            return selectedText\n"
                       @"        end tell\n"
                       @"    on error\n"
                       @"        return \"\"\n"
                       @"    end try\n"
                       @"end tell";
    
    NSAppleScript* appleScript = [[NSAppleScript alloc] initWithSource:script];
    NSDictionary* errorDict = nil;
    NSAppleEventDescriptor* result = [appleScript executeAndReturnError:&errorDict];
    
    if (result && [result stringValue]) {
        return [result stringValue];
    }
    
    return nil;
}

// Helper method to get selected text from Microsoft Office apps
NSString* SelectedTextContextProvider::getSelectedTextFromOffice(const std::string& bundleId) {
    NSString* appName;
    
    if (bundleId == "com.microsoft.Word") {
        appName = @"Microsoft Word";
    } else if (bundleId == "com.microsoft.Excel") {
        appName = @"Microsoft Excel";
    } else if (bundleId == "com.microsoft.Powerpoint") {
        appName = @"Microsoft PowerPoint";
    } else {
        return nil;
    }
    
    NSString* script = [NSString stringWithFormat:@"tell application \"%@\"\n"
                        @"    try\n"
                        @"        set selectedText to content of selection\n"
                        @"        return selectedText\n"
                        @"    on error\n"
                        @"        return \"\"\n"
                        @"    end try\n"
                        @"end tell", appName];
    
    NSAppleScript* appleScript = [[NSAppleScript alloc] initWithSource:script];
    NSDictionary* errorDict = nil;
    NSAppleEventDescriptor* result = [appleScript executeAndReturnError:&errorDict];
    
    if (result && [result stringValue]) {
        return [result stringValue];
    }
    
    return nil;
}

bool SelectedTextContextProvider::isAvailable() const {
    // Check if we have accessibility permissions
    // This is a simplified check that doesn't verify all permissions
    NSDictionary* options = @{(__bridge NSString*)kAXTrustedCheckOptionPrompt: @NO};
    BOOL accessibilityEnabled = AXIsProcessTrustedWithOptions((__bridge CFDictionaryRef)options);
    
    return accessibilityEnabled;
}

// OpenFileContextProvider implementation
void OpenFileContextProvider::populateContext(Context& context) {
    // Getting open files requires app-specific APIs or Accessibility permissions
    // This is a simplified implementation that may not work for all applications
    
    NSRunningApplication* app = getFrontmostApplication();
    if (!app) {
        return;
    }
    
    // For Finder, we can try to get the selected items
    NSString* bundleId = [app bundleIdentifier];
    if (bundleId && [bundleId isEqualToString:@"com.apple.finder"]) {
        // AppleScript to get selected items in Finder
        NSString* script = @"tell application \"Finder\" to set selectedItems to selection as alias list\n"
                           @"set itemPaths to {}\n"
                           @"repeat with i in selectedItems\n"
                           @"    set end of itemPaths to POSIX path of (i as text)\n"
                           @"end repeat\n"
                           @"return itemPaths";
        
        NSAppleScript* appleScript = [[NSAppleScript alloc] initWithSource:script];
        NSDictionary* errorDict = nil;
        NSAppleEventDescriptor* result = [appleScript executeAndReturnError:&errorDict];
        
        if (result && [result numberOfItems] > 0) {
            nlohmann::json files = nlohmann::json::array();
            for (NSInteger i = 1; i <= [result numberOfItems]; i++) {
                NSString* path = [[result descriptorAtIndex:i] stringValue];
                if (path) {
                    files.push_back(nsStringToStdString(path));
                }
            }
            if (!files.empty()) {
                context.addValue("open_file.paths", ContextValue::fromObject(files), getName());
            }
        }
    }
}

bool OpenFileContextProvider::isAvailable() const {
    return true; // Basic functionality is available, but may not work for all apps
}

// CurrentDirectoryContextProvider implementation
void CurrentDirectoryContextProvider::populateContext(Context& context) {
    // Get current working directory
    char cwd[PATH_MAX];
    if (getcwd(cwd, sizeof(cwd)) != nullptr) {
        context.addValue("current_directory.path", ContextValue::fromString(cwd), getName());
        
        // Get parent directory
        std::filesystem::path path(cwd);
        if (path.has_parent_path()) {
            context.addValue("current_directory.parent", 
                            ContextValue::fromString(path.parent_path().string()), 
                            getName());
        }
        
        // Get directory name
        context.addValue("current_directory.name", 
                        ContextValue::fromString(path.filename().string()), 
                        getName());
    }
}

bool CurrentDirectoryContextProvider::isAvailable() const {
    return true; // Always available
}

// CurrentApplicationContextProvider implementation
void CurrentApplicationContextProvider::populateContext(Context& context) {
    NSRunningApplication* app = getFrontmostApplication();
    if (!app) {
        return;
    }
    
    // Get application information
    NSString* appName = [app localizedName];
    NSString* bundleId = [app bundleIdentifier];
    NSURL* bundleURL = [app bundleURL];
    
    if (appName) {
        context.addValue("current_application.name", ContextValue::fromString(nsStringToStdString(appName)), getName());
    }
    
    if (bundleId) {
        context.addValue("current_application.bundle_id", ContextValue::fromString(nsStringToStdString(bundleId)), getName());
    }
    
    if (bundleURL) {
        context.addValue("current_application.path", ContextValue::fromString(nsStringToStdString([bundleURL path])), getName());
    }
    
    // Get application icon
    NSImage* icon = [app icon];
    if (icon) {
        context.addValue("current_application.has_icon", ContextValue::fromBoolean(true), getName());
    } else {
        context.addValue("current_application.has_icon", ContextValue::fromBoolean(false), getName());
    }
}

bool CurrentApplicationContextProvider::isAvailable() const {
    return true; // Always available on macOS
}

// TimeLocationContextProvider implementation
void TimeLocationContextProvider::populateContext(Context& context) {
    // Get current time
    auto now = std::chrono::system_clock::now();
    auto time_t_now = std::chrono::system_clock::to_time_t(now);
    std::tm* local_time = std::localtime(&time_t_now);
    
    if (local_time) {
        // Add time components
        context.addValue("time.hour", ContextValue::fromNumber(local_time->tm_hour), getName());
        context.addValue("time.minute", ContextValue::fromNumber(local_time->tm_min), getName());
        context.addValue("time.second", ContextValue::fromNumber(local_time->tm_sec), getName());
        context.addValue("time.day", ContextValue::fromNumber(local_time->tm_mday), getName());
        context.addValue("time.month", ContextValue::fromNumber(local_time->tm_mon + 1), getName()); // tm_mon is 0-based
        context.addValue("time.year", ContextValue::fromNumber(local_time->tm_year + 1900), getName()); // tm_year is years since 1900
        context.addValue("time.weekday", ContextValue::fromNumber(local_time->tm_wday), getName()); // 0 = Sunday
        
        // Format time as string
        char timeStr[100];
        std::strftime(timeStr, sizeof(timeStr), "%Y-%m-%d %H:%M:%S", local_time);
        context.addValue("time.iso_string", ContextValue::fromString(timeStr), getName());
    }
    
    // Location would require CoreLocation and user permission
    // This is a simplified implementation that doesn't actually get the location
    context.addValue("location.available", ContextValue::fromBoolean(false), getName());
}

bool TimeLocationContextProvider::isAvailable() const {
    return true; // Time is always available, location may require permissions
}

// ContextProviderFactory implementation
std::vector<std::unique_ptr<ContextProvider>> ContextProviderFactory::createAllProviders() {
    std::vector<std::unique_ptr<ContextProvider>> providers;
    
    // Create all providers
    providers.push_back(std::make_unique<ClipboardContextProvider>());
    providers.push_back(std::make_unique<ActiveWindowContextProvider>());
    providers.push_back(std::make_unique<SelectedTextContextProvider>());
    providers.push_back(std::make_unique<OpenFileContextProvider>());
    providers.push_back(std::make_unique<CurrentDirectoryContextProvider>());
    providers.push_back(std::make_unique<CurrentApplicationContextProvider>());
    providers.push_back(std::make_unique<TimeLocationContextProvider>());
    
    return providers;
}

std::unique_ptr<ContextProvider> ContextProviderFactory::createProvider(const std::string& name) {
    if (name == "clipboard") {
        return std::make_unique<ClipboardContextProvider>();
    } else if (name == "active_window") {
        return std::make_unique<ActiveWindowContextProvider>();
    } else if (name == "selected_text") {
        return std::make_unique<SelectedTextContextProvider>();
    } else if (name == "open_file") {
        return std::make_unique<OpenFileContextProvider>();
    } else if (name == "current_directory") {
        return std::make_unique<CurrentDirectoryContextProvider>();
    } else if (name == "current_application") {
        return std::make_unique<CurrentApplicationContextProvider>();
    } else if (name == "time_location") {
        return std::make_unique<TimeLocationContextProvider>();
    }
    
    return nullptr;
}

} // namespace core
} // namespace launcher 