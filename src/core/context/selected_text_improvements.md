# Selected Text Context Provider Improvements

This document outlines proposed improvements to enhance the Selected Text Context Provider, making it more powerful, reliable, and user-friendly.

## 1. Implement Caching Mechanism

**Why**: Retrieving selected text can be resource-intensive, especially when using Accessibility APIs or simulating keyboard shortcuts.

**Implementation**:

-   Add a time-based cache to store recently retrieved text
-   Only refresh the cache when necessary (e.g., after a certain time interval or when the active window changes)
-   Include a cache invalidation strategy based on window focus changes

```cpp
// Add to SelectedTextContextProvider class
private:
    struct CachedText {
        std::string text;
        std::string source;
        std::chrono::steady_clock::time_point timestamp;
    };

    std::optional<CachedText> cachedText;
    std::chrono::seconds cacheLifetime{2}; // 2 seconds cache lifetime

    bool isCacheValid() const {
        if (!cachedText) return false;
        auto now = std::chrono::steady_clock::now();
        return (now - cachedText->timestamp) < cacheLifetime;
    }
```

## 2. Add Asynchronous Text Retrieval

**Why**: Some text retrieval methods (especially UI Automation) can block the main thread, causing UI lag.

**Implementation**:

-   Create a background worker thread for text retrieval
-   Implement a callback mechanism to update the context when text is available
-   Add timeout handling to prevent hanging on unresponsive applications

```cpp
// Add to header file
#include <future>
#include <thread>

// Add to class implementation
private:
    std::future<std::string> asyncTextFuture;

    void startAsyncTextRetrieval() {
        asyncTextFuture = std::async(std::launch::async, [this]() {
            return getSelectedTextViaAccessibility(); // Or other methods
        });
    }

    std::string getAsyncTextResult(int timeoutMs = 500) {
        if (asyncTextFuture.valid()) {
            auto status = asyncTextFuture.wait_for(std::chrono::milliseconds(timeoutMs));
            if (status == std::future_status::ready) {
                return asyncTextFuture.get();
            }
        }
        return "";
    }
```

## 3. Implement Text Format Preservation

**Why**: Currently, the provider only captures plain text, losing formatting information that might be valuable.

**Implementation**:

-   Add support for rich text formats (RTF, HTML)
-   Preserve formatting attributes like bold, italic, font size, etc.
-   Provide both formatted and plain text versions

```cpp
// Add to context population
if (textFound) {
    // Add plain text as before

    // Add formatted versions if available
    if (hasRichTextFormat) {
        context.addValue("selected_text.rtf", ContextValue::fromString(rtfText), getName());
    }

    if (hasHtmlFormat) {
        context.addValue("selected_text.html", ContextValue::fromString(htmlText), getName());
    }

    // Add formatting metadata
    nlohmann::json formatting = nlohmann::json::object();
    // Add detected formatting properties
    context.addValue("selected_text.formatting", ContextValue::fromObject(formatting), getName());
}
```

## 4. Add Text Selection Metadata

**Why**: Additional metadata about the selection can provide valuable context for actions.

**Implementation**:

-   Add information about the selection's position in the document
-   Include surrounding text for context
-   Add language detection for the selected text

```cpp
// Add to context population
if (textFound) {
    // Add basic text as before

    // Add position information if available
    if (hasPositionInfo) {
        context.addValue("selected_text.position.start",
                         ContextValue::fromNumber(selectionStart), getName());
        context.addValue("selected_text.position.end",
                         ContextValue::fromNumber(selectionEnd), getName());
    }

    // Add surrounding context if available
    if (hasSurroundingText) {
        context.addValue("selected_text.context.before",
                         ContextValue::fromString(textBefore), getName());
        context.addValue("selected_text.context.after",
                         ContextValue::fromString(textAfter), getName());
    }

    // Detect language (using a lightweight language detection library)
    std::string detectedLanguage = detectLanguage(text);
    if (!detectedLanguage.empty()) {
        context.addValue("selected_text.language",
                         ContextValue::fromString(detectedLanguage), getName());
    }
}
```

## 5. Implement Application-Specific Plugins

**Why**: Different applications have unique ways of accessing selected text that may be more reliable than generic methods.

**Implementation**:

-   Create a plugin architecture for application-specific text retrieval
-   Allow third-party plugins to be registered for specific applications
-   Implement priority-based fallback between plugins

```cpp
// Add to header file
class TextSelectionPlugin {
public:
    virtual ~TextSelectionPlugin() = default;
    virtual std::string getName() const = 0;
    virtual bool canHandleApplication(const std::string& bundleId) const = 0;
    virtual std::string getSelectedText(const std::string& bundleId) = 0;
    virtual int getPriority() const = 0; // Higher priority plugins are tried first
};

// Add to class implementation
private:
    std::vector<std::unique_ptr<TextSelectionPlugin>> plugins;

    void registerBuiltinPlugins() {
        plugins.push_back(std::make_unique<BrowserTextPlugin>());
        plugins.push_back(std::make_unique<OfficeTextPlugin>());
        plugins.push_back(std::make_unique<IDETextPlugin>());
        // Add more built-in plugins
    }

    void registerPlugin(std::unique_ptr<TextSelectionPlugin> plugin) {
        plugins.push_back(std::move(plugin));
        // Sort plugins by priority
        std::sort(plugins.begin(), plugins.end(),
                 [](const auto& a, const auto& b) {
                     return a->getPriority() > b->getPriority();
                 });
    }
```

## 6. Add Accessibility Permissions Detection and Guidance

**Why**: Users may not understand why text selection isn't working if they haven't granted necessary permissions.

**Implementation**:

-   Add detailed permission checking
-   Provide user-friendly error messages explaining required permissions
-   Include instructions on how to grant permissions

```cpp
// Add to isAvailable method
bool SelectedTextContextProvider::isAvailable() const {
#ifdef __APPLE__
    // Check if we have accessibility permissions
    NSDictionary* options = @{(__bridge NSString*)kAXTrustedCheckOptionPrompt: @NO};
    BOOL accessibilityEnabled = AXIsProcessTrustedWithOptions((__bridge CFDictionaryRef)options);

    if (!accessibilityEnabled) {
        // Store detailed error message that can be retrieved by the UI
        lastErrorMessage = "Accessibility permissions are required for text selection. "
                          "Please go to System Preferences > Security & Privacy > Privacy > "
                          "Accessibility and enable permissions for this application.";
    }

    return accessibilityEnabled;
#elif defined(_WIN32)
    // Similar implementation for Windows
#else
    return false;
#endif
}

// Add getter for error message
std::string SelectedTextContextProvider::getLastErrorMessage() const {
    return lastErrorMessage;
}
```

## 7. Add Text Selection History

**Why**: Keeping a history of recently selected text can be valuable for users who need to reference previous selections.

**Implementation**:

-   Maintain a queue of recently selected text items
-   Add timestamp and source application for each history item
-   Provide access to the history through the context

```cpp
// Add to header file
private:
    struct HistoryItem {
        std::string text;
        std::string source;
        std::string application;
        std::chrono::system_clock::time_point timestamp;
    };

    std::deque<HistoryItem> selectionHistory;
    size_t maxHistorySize = 10;

    void addToHistory(const std::string& text, const std::string& source,
                     const std::string& application) {
        // Don't add empty text or duplicatesq of the most recent item
        if (text.empty() || (!selectionHistory.empty() &&
                            selectionHistory.front().text == text)) {
            return;
        }

        HistoryItem item{
            text,
            source,
            application,
            std::chrono::system_clock::now()
        };

        selectionHistory.push_front(item);

        // Trim history if needed
        if (selectionHistory.size() > maxHistorySize) {
            selectionHistory.pop_back();
        }
    }

// Add to context population
void SelectedTextContextProvider::populateContext(Context& context) {
    // Existing implementation

    // Add history to context
    if (!selectionHistory.empty()) {
        nlohmann::json history = nlohmann::json::array();

        for (const auto& item : selectionHistory) {
            nlohmann::json historyItem = nlohmann::json::object();
            historyItem["text"] = item.text;
            historyItem["source"] = item.source;
            historyItem["application"] = item.application;

            // Format timestamp
            auto timeT = std::chrono::system_clock::to_time_t(item.timestamp);
            std::tm* localTime = std::localtime(&timeT);
            char timeStr[100];
            std::strftime(timeStr, sizeof(timeStr), "%Y-%m-%d %H:%M:%S", localTime);

            historyItem["timestamp"] = timeStr;

            history.push_back(historyItem);
        }

        context.addValue("selected_text.history", ContextValue::fromObject(history), getName());
    }
}
```

## 8. Implement Text Selection Analytics

**Why**: Understanding how users interact with text selection can help improve the provider.

**Implementation**:

-   Track success rates of different text retrieval methods
-   Collect anonymous usage statistics
-   Use this data to automatically prioritize the most reliable methods

```cpp
// Add to header file
private:
    struct MethodStats {
        size_t attempts = 0;
        size_t successes = 0;
        double averageTime = 0.0; // in milliseconds
    };

    std::unordered_map<std::string, MethodStats> methodStats;

    void recordMethodAttempt(const std::string& method, bool success, double timeMs) {
        auto& stats = methodStats[method];
        stats.attempts++;

        if (success) {
            stats.successes++;
        }

        // Update average time (rolling average)
        stats.averageTime = (stats.averageTime * (stats.attempts - 1) + timeMs) / stats.attempts;
    }

    // Use stats to determine the best method order
    std::vector<std::string> getBestMethodOrder() {
        std::vector<std::pair<std::string, double>> methods;

        for (const auto& [method, stats] : methodStats) {
            // Calculate a score based on success rate and speed
            double successRate = stats.attempts > 0 ?
                                (double)stats.successes / stats.attempts : 0.0;

            // Lower time is better, so invert it for scoring
            double timeScore = stats.averageTime > 0 ?
                              1000.0 / stats.averageTime : 0.0;

            // Combined score (weight success rate more heavily)
            double score = (successRate * 0.7) + (timeScore * 0.3);

            methods.push_back({method, score});
        }

        // Sort by score (descending)
        std::sort(methods.begin(), methods.end(),
                 [](const auto& a, const auto& b) {
                     return a.second > b.second;
                 });

        // Extract just the method names
        std::vector<std::string> result;
        for (const auto& [method, _] : methods) {
            result.push_back(method);
        }

        return result;
    }
```

## 9. Add Support for Multi-Selection

**Why**: Many applications support selecting multiple text regions simultaneously.

**Implementation**:

-   Detect and handle multiple text selections
-   Provide both individual selections and combined text
-   Include position information for each selection

```cpp
// Add to context population
if (hasMultipleSelections) {
    nlohmann::json selections = nlohmann::json::array();

    for (size_t i = 0; i < multiSelections.size(); i++) {
        const auto& selection = multiSelections[i];

        nlohmann::json selectionObj = nlohmann::json::object();
        selectionObj["text"] = selection.text;
        selectionObj["start"] = selection.start;
        selectionObj["end"] = selection.end;

        selections.push_back(selectionObj);
    }

    context.addValue("selected_text.multi_selections",
                    ContextValue::fromObject(selections), getName());
    context.addValue("selected_text.multi_selection_count",
                    ContextValue::fromNumber(multiSelections.size()), getName());
}
```

## 10. Implement Text Selection Actions

**Why**: Allow actions to not just read but also modify selected text.

**Implementation**:

-   Add methods to replace selected text
-   Support inserting text at the selection point
-   Provide text transformation capabilities

```cpp
// Add to header file
public:
    // Replace the currently selected text
    bool replaceSelectedText(const std::string& newText);

    // Insert text at the current selection point
    bool insertTextAtSelection(const std::string& textToInsert);

    // Apply a transformation to the selected text
    bool transformSelectedText(const std::function<std::string(const std::string&)>& transformer);

// Implementation example for macOS
bool SelectedTextContextProvider::replaceSelectedText(const std::string& newText) {
#ifdef __APPLE__
    // Get the frontmost application
    NSRunningApplication* app = getFrontmostApplication();
    if (!app) return false;

    NSString* bundleId = [app bundleIdentifier];
    if (!bundleId) return false;

    // Different approaches based on application
    if ([bundleId isEqualToString:@"com.apple.Safari"] ||
        [bundleId isEqualToString:@"com.google.Chrome"]) {
        // For browsers, use JavaScript
        NSString* script = [NSString stringWithFormat:
            @"tell application \"%@\"\n"
            @"    set selectedText to execute front window's active tab javascript \"(function() { "
            @"        var selection = window.getSelection(); "
            @"        if (selection.rangeCount > 0) { "
            @"            var range = selection.getRangeAt(0); "
            @"            range.deleteContents(); "
            @"            range.insertNode(document.createTextNode('%@')); "
            @"            return true; "
            @"        } "
            @"        return false; "
            @"    })()\"\n"
            @"    return selectedText\n"
            @"end tell",
            bundleId == "com.apple.Safari" ? @"Safari" : @"Google Chrome",
            [NSString stringWithUTF8String:newText.c_str()]];

        NSAppleScript* appleScript = [[NSAppleScript alloc] initWithSource:script];
        NSDictionary* errorDict = nil;
        NSAppleEventDescriptor* result = [appleScript executeAndReturnError:&errorDict];

        return result && [[result stringValue] isEqualToString:@"true"];
    } else {
        // For other applications, use clipboard method
        // 1. Save current clipboard
        // 2. Copy selected text to clipboard
        // 3. Replace with new text
        // 4. Restore clipboard
        // Implementation details...
    }
#endif
    return false;
}
```

## 11. Add Support for Code-Aware Selection

**Why**: When selecting code, additional context about the code structure can be valuable.

**Implementation**:

-   Detect when selected text is code
-   Identify the programming language
-   Provide syntax highlighting information
-   Extract structural information (function names, class names, etc.)

```cpp
// Add to context population
if (textFound && isCodeSelection) {
    // Detect programming language
    std::string language = detectProgrammingLanguage(text);
    context.addValue("selected_text.code.language",
                    ContextValue::fromString(language), getName());

    // Extract structural information
    if (!language.empty()) {
        auto codeStructure = parseCodeStructure(text, language);
        if (!codeStructure.empty()) {
            context.addValue("selected_text.code.structure",
                            ContextValue::fromObject(codeStructure), getName());
        }
    }

    // Add syntax highlighting information
    auto syntaxHighlighting = generateSyntaxHighlighting(text, language);
    if (!syntaxHighlighting.empty()) {
        context.addValue("selected_text.code.syntax_highlighting",
                        ContextValue::fromObject(syntaxHighlighting), getName());
    }
}
```

## 12. Implement Optical Character Recognition (OCR) for Images

**Why**: Sometimes text is embedded in images and not directly selectable.

**Implementation**:

-   Detect when the selection contains an image
-   Use OCR to extract text from the image
-   Provide both the extracted text and confidence scores

```cpp
// Add to context population
if (!textFound && hasImageSelection) {
    // Use OCR to extract text from the image
    OCRResult ocrResult = performOCR(selectedImage);

    if (!ocrResult.text.empty()) {
        context.addValue("selected_text",
                        ContextValue::fromString(ocrResult.text), getName());
        context.addValue("selected_text.source",
                        ContextValue::fromString("ocr"), getName());
        context.addValue("selected_text.ocr_confidence",
                        ContextValue::fromNumber(ocrResult.confidence), getName());

        // Add bounding boxes for each recognized text element
        if (!ocrResult.textElements.empty()) {
            context.addValue("selected_text.ocr_elements",
                            ContextValue::fromObject(ocrResult.textElements), getName());
        }

        textFound = true;
    }
}
```

## 13. Add Support for Text Selection in Remote Desktop Sessions

**Why**: Text selection can be challenging in remote desktop environments.

**Implementation**:

-   Detect when running in a remote desktop session
-   Use specialized techniques for remote text selection
-   Provide fallback mechanisms for remote environments

```cpp
// Add to header file
private:
    bool isRemoteSession() const;
    std::string getSelectedTextInRemoteSession();

// Implementation
bool SelectedTextContextProvider::isRemoteSession() const {
#ifdef _WIN32
    // Check for Windows Remote Desktop
    return GetSystemMetrics(SM_REMOTESESSION) != 0;
#elif defined(__APPLE__)
    // Check for macOS Screen Sharing or Remote Management
    // Implementation details...
#else
    return false;
#endif
}

// Add to populateContext method
if (isRemoteSession() && !textFound) {
    std::string remoteText = getSelectedTextInRemoteSession();
    if (!remoteText.empty()) {
        context.addValue("selected_text",
                        ContextValue::fromString(remoteText), getName());
        context.addValue("selected_text.source",
                        ContextValue::fromString("remote_session"), getName());
        textFound = true;
    }
}
```

## 14. Implement Configurable Text Selection Behavior

**Why**: Users may have different preferences for how text selection works.

**Implementation**:

-   Add user-configurable settings for text selection behavior
-   Allow users to prioritize different selection methods
-   Provide options for handling special cases (e.g., password fields)

```cpp
// Add to header file
struct SelectionConfig {
    bool preferAccessibilityAPI = true;
    bool useClipboardMethod = true;
    bool includePasswordFields = false;
    int maxSelectionLength = 100000;
    std::vector<std::string> prioritizedMethods;
    // More configuration options...
};

private:
    SelectionConfig config;

    void loadConfig() {
        // Load configuration from settings
        // Implementation details...
    }

    void saveConfig() {
        // Save configuration to settings
        // Implementation details...
    }

// Add public methods to modify configuration
public:
    void setConfig(const SelectionConfig& newConfig) {
        config = newConfig;
        saveConfig();
    }

    SelectionConfig getConfig() const {
        return config;
    }
```

## 15. Add Support for Internationalization and Non-Latin Scripts

**Why**: Text selection should work reliably with all languages and scripts.

**Implementation**:

-   Ensure proper handling of Unicode text
-   Add special handling for right-to-left languages
-   Support complex scripts and character compositions

```cpp
// Add to header file
private:
    std::string normalizeText(const std::string& text) const;
    bool isRightToLeftText(const std::string& text) const;
    std::string getTextDirection(const std::string& text) const;

// Implementation
std::string SelectedTextContextProvider::normalizeText(const std::string& text) const {
    // Normalize Unicode text (NFC or NFD as appropriate)
    // Implementation details...
    return normalizedText;
}

// Add to context population
if (textFound) {
    // Normalize text before adding to context
    std::string normalizedText = normalizeText(text);
    context.addValue("selected_text",
                    ContextValue::fromString(normalizedText), getName());

    // Add text direction information
    std::string textDirection = getTextDirection(normalizedText);
    context.addValue("selected_text.direction",
                    ContextValue::fromString(textDirection), getName());
}
```

## Summary of Improvements

These improvements would significantly enhance the Selected Text Context Provider by:

1. **Improving Performance**: Through caching and asynchronous retrieval
2. **Enhancing Reliability**: With better fallback mechanisms and application-specific plugins
3. **Providing Richer Context**: By including formatting, position, and language information
4. **Supporting More Use Cases**: With history tracking, multi-selection support, and code-aware selection
5. **Adding New Capabilities**: Like text replacement, transformation, and OCR
6. **Improving User Experience**: With better error handling and permissions guidance
7. **Optimizing Over Time**: Through usage analytics and adaptive method selection
8. **Supporting Diverse Environments**: Including remote sessions and international text

Implementing these improvements would make the Selected Text Context Provider more powerful, reliable, and user-friendly, enabling more sophisticated actions that work with text selection.
