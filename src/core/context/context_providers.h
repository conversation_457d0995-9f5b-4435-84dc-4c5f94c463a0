#pragma once

#include <memory>
#include <string>

#include "context.h"

// Include Windows-specific headers for UI Automation
#ifdef _WIN32
#include <UIAutomation.h>
#include <exdisp.h>
#include <shlguid.h>
#include <shlobj.h>
#include <windows.h>
#endif

// Forward declarations for Objective-C types
#ifdef __APPLE__
#ifdef __OBJC__
@class NSString;
#else
typedef struct objc_object NSString;
#endif
#endif

namespace launcher {
namespace core {

/**
 * @brief Provides clipboard content to the context
 *
 * This provider adds the current clipboard content to the context.
 */
class ClipboardContextProvider : public ContextProvider {
 public:
    std::string getName() const override { return "clipboard"; }
    void populateContext(Context& context) override;
    bool isAvailable() const override;
};

/**
 * @brief Provides information about the active window
 *
 * This provider adds information about the currently active window to the context.
 */
class ActiveWindowContextProvider : public ContextProvider {
 public:
    std::string getName() const override { return "active_window"; }
    void populateContext(Context& context) override;
    bool isAvailable() const override;
};

/**
 * @brief Provides currently selected text
 *
 * This provider adds the currently selected text to the context.
 * It uses multiple techniques to capture selected text from various applications.
 */
class SelectedTextContextProvider : public ContextProvider {
 public:
    std::string getName() const override { return "selected_text"; }
    void populateContext(Context& context) override;
    bool isAvailable() const override;

 private:
#ifdef __APPLE__
    // macOS-specific helper methods
    NSString* getSelectedTextViaAccessibility();
    NSString* getSelectedTextViaAppleScript();
    NSString* getSelectedTextFromBrowser(const std::string& bundleId);
    NSString* getSelectedTextFromFinder();
    NSString* getSelectedTextFromXcode();
    NSString* getSelectedTextFromOffice(const std::string& bundleId);
#elif defined(_WIN32)
    // Windows-specific helper methods
    std::string getSelectedTextViaUIAutomation();
    std::string getSelectedTextFromBrowser(HWND hwnd, const std::string& processName);
    std::string getSelectedTextFromExplorer(HWND hwnd);
    std::string getSelectedTextFromVisualStudio(HWND hwnd);
    std::string getSelectedTextFromOffice(HWND hwnd, const std::string& processName);
    std::string getSelectedTextFromTextEditor(HWND hwnd);
#endif
};

/**
 * @brief Provides information about the open file/document
 *
 * This provider adds information about the currently open file or document to the context.
 */
class OpenFileContextProvider : public ContextProvider {
 public:
    std::string getName() const override { return "open_file"; }
    void populateContext(Context& context) override;
    bool isAvailable() const override;
};

/**
 * @brief Provides information about the current directory
 *
 * This provider adds information about the current working directory to the context.
 */
class CurrentDirectoryContextProvider : public ContextProvider {
 public:
    std::string getName() const override { return "current_directory"; }
    void populateContext(Context& context) override;
    bool isAvailable() const override;
};

/**
 * @brief Provides information about the current application
 *
 * This provider adds information about the currently active application to the context.
 */
class CurrentApplicationContextProvider : public ContextProvider {
 public:
    std::string getName() const override { return "current_application"; }
    void populateContext(Context& context) override;
    bool isAvailable() const override;
};

/**
 * @brief Provides time and location information
 *
 * This provider adds current time and location information to the context.
 */
class TimeLocationContextProvider : public ContextProvider {
 public:
    std::string getName() const override { return "time_location"; }
    void populateContext(Context& context) override;
    bool isAvailable() const override;
};

/**
 * @brief Factory for creating platform-specific context providers
 *
 * This factory creates the appropriate context providers for the current platform.
 */
class ContextProviderFactory {
 public:
    /**
     * @brief Create all available context providers for the current platform
     *
     * @return Vector of unique pointers to context providers
     */
    static std::vector<std::unique_ptr<ContextProvider>> createAllProviders();

    /**
     * @brief Create a specific context provider by name
     *
     * @param name Name of the provider to create
     * @return Unique pointer to the created provider, or nullptr if not available
     */
    static std::unique_ptr<ContextProvider> createProvider(const std::string& name);
};

}  // namespace core
}  // namespace launcher