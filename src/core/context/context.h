#pragma once

#include <functional>
#include <memory>
#include <optional>
#include <string>
#include <unordered_map>
#include <variant>
#include <vector>
#include <cstddef> // For size_t

// Include JSON library
#include <nlohmann/json.hpp>

// Include http client
#include "../http/http_client.h"

namespace launcher {
namespace core {

// --- Added ChatMessage struct ---
/**
 * @brief Represents a single message formatted for API requests.
 */
struct ApiChatMessage {
    std::string role;
    std::shared_ptr<std::string> content;

    // NEW: Provenance fields -------------------------------------------------
    // Canonical provider id ("openai", "anthropic", etc.).  May be empty for
    // legacy transcripts or user messages.
    std::string provider;
    // Concrete model identifier ("gpt-4o", "claude-3-sonnet-20240229", ...)
    std::string model;
    // Optional arbitrary metadata – token counts, cost, latency, etc.
    nlohmann::json extra_meta = nlohmann::json::object();
    // -----------------------------------------------------------------------

    ApiChatMessage() = default;

    // Back-compat ctor (role + content only)
    ApiChatMessage(const std::string &r, const std::shared_ptr<std::string>& c)
        : role(r), content(c) {}

    // Full ctor with provenance & metadata
    ApiChatMessage(const std::string &r,
                   const std::shared_ptr<std::string>& c,
                   const std::string &provider_id,
                   const std::string &model_id,
                   const nlohmann::json &meta = nlohmann::json::object())
        : role(r), content(c), provider(provider_id), model(model_id), extra_meta(meta) {}

    // Convenience accessor
    const std::string &contentRef() const { return *content; }

    // -------------------------------------------------------------------
    // Helper: Return a human-friendly display name for UI given role and
    // provenance metadata.  This centralises the mapping logic so that all
    // callers (Objective-C or pure C++) show consistent labels.
    // -------------------------------------------------------------------
    [[nodiscard]] std::string displayName() const {
        // Assistant messages → concrete model when available, else generic.
        if (role == "assistant") {
            return !model.empty() ? model : std::string("Assistant");
        }

        // System messages keep "System" regardless of provider/model.
        if (role == "system") {
            return "System";
        }

        // Fallback – treat anything else as a user.
        return "User";
    }
};

// Forward declaration of StreamingResponseCallback
using StreamingResponseCallback = http::DataCallback;

/**
 * @brief Polymorphic value container that can hold different types of data
 *
 * This class provides a type-safe container for various data types used in the context system.
 */
class ContextValue {
 public:
    // Default constructor
    ContextValue() : type_(Type::String), value_(std::string("")) {}

    // Factory methods for creating values of different types
    static ContextValue fromString(const std::string& value);
    static ContextValue fromNumber(double value);
    static ContextValue fromBoolean(bool value);
    static ContextValue fromObject(const nlohmann::json& value);
    static ContextValue fromArray(const std::vector<ContextValue>& values);
    static ContextValue fromCallback(StreamingResponseCallback callback);

    // Type checking methods to determine the contained type
    bool isString() const;
    bool isNumber() const;
    bool isBoolean() const;
    bool isObject() const;
    bool isArray() const;
    bool isCallback() const;

    // Conversion methods to access the contained value
    std::string asString() const;
    double asNumber() const;
    bool asBoolean() const;
    nlohmann::json asObject() const;
    std::vector<ContextValue> asArray() const;
    StreamingResponseCallback asCallback() const;

    // Serialization
    nlohmann::json toJson() const;
    static ContextValue fromJson(const nlohmann::json& json);

 private:
    enum class Type { String, Number, Boolean, Object, Array, Callback };
    Type type_;
    std::variant<std::string, double, bool, nlohmann::json, std::vector<ContextValue>,
                 StreamingResponseCallback>
        value_;

    // Private constructor used by factory methods
    ContextValue(Type type,
                 const std::variant<std::string, double, bool, nlohmann::json,
                                    std::vector<ContextValue>, StreamingResponseCallback>& value)
        : type_(type), value_(value) {}
};

/**
 * @brief Context data structure
 *
 * This class provides a central repository of contextual data that agents can use
 * to make decisions and perform their tasks effectively.
 */
class Context {
 public:
    // Core methods
    void addValue(const std::string& key, const ContextValue& value,
                  const std::string& source = "");
    ContextValue getValue(const std::string& key) const;
    bool hasValue(const std::string& key) const;
    void removeValue(const std::string& key);

    // Get all values
    std::unordered_map<std::string, ContextValue> getValues() const;

    // --- Added Chat History Methods ---
    /**
     * @brief Sets the chat history in the context.
     *
     * Stores the history under a predefined key, serializing it appropriately.
     * @param history The vector of chat messages representing the conversation history.
     */
    void setChatHistory(const std::vector<ApiChatMessage>& history);

    /**
     * @brief Retrieves the chat history from the context.
     *
     * Deserializes the history stored under a predefined key.
     * @return An optional containing the vector of chat messages if found, otherwise std::nullopt.
     */
    std::optional<std::vector<ApiChatMessage>> getChatHistory() const;
    // --- End Added Chat History Methods ---

    // Check if context is empty
    bool empty() const { return values_.empty(); }

    // Get size of context
    size_t size() const { return values_.size(); }

    // Serialization
    std::string toJson() const;
    static Context fromJson(const std::string& json);

    // Context sources
    std::vector<std::string> getSources() const;
    std::string getSourceForKey(const std::string& key) const;

 private:
    std::unordered_map<std::string, ContextValue> values_;
    std::unordered_map<std::string, std::string> sources_;
};

/**
 * @brief Base class for all context providers
 *
 * Context providers are responsible for collecting contextual information from various sources.
 */
class ContextProvider {
 public:
    virtual ~ContextProvider() = default;
    virtual std::string getName() const = 0;
    virtual void populateContext(Context& context) = 0;
    virtual bool isAvailable() const = 0;
};

/**
 * @brief Central manager for all context providers
 *
 * This class manages the registration and use of context providers.
 */
class ContextManager {
 public:
    ContextManager() = default;
    ~ContextManager() = default;

    void registerProvider(std::unique_ptr<ContextProvider> provider);
    void unregisterProvider(const std::string& providerName);

    Context collectContext();
    Context collectContextFromProviders(const std::vector<std::string>& providerNames);

 private:
    ContextManager(const ContextManager&) = delete;
    ContextManager& operator=(const ContextManager&) = delete;

    std::unordered_map<std::string, std::unique_ptr<ContextProvider>> providers_;
};

}  // namespace core
}  // namespace launcher