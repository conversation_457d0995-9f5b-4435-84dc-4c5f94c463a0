add_library(context STATIC
    context.cpp
)

# Platform-specific provider sources and settings
if(APPLE)
    target_sources(context PRIVATE
        context_providers_macos.mm
    )

    # Treat Objective-C++ sources appropriately
    set_source_files_properties(context_providers_macos.mm PROPERTIES
        COMPILE_FLAGS "-x objective-c++"
    )

    # Apple frameworks required by provider implementation
    find_library(APPKIT_LIBRARY AppKit REQUIRED)
    find_library(FOUNDATION_LIBRARY Foundation REQUIRED)
    find_library(CORELOCATION_LIBRARY CoreLocation REQUIRED)
    target_link_libraries(context PUBLIC
        ${APPKIT_LIBRARY}
        ${FOUNDATION_LIBRARY}
        ${CORELOCATION_LIBRARY}
    )
else()
    target_sources(context PRIVATE
        context_providers_generic.cpp
    )
    # Example: link against CURL on Linux if required
    find_package(CURL QUIET)
    if(CURL_FOUND)
        target_include_directories(context PRIVATE ${CURL_INCLUDE_DIRS})
        target_link_libraries(context PUBLIC ${CURL_LIBRARIES})
    endif()
endif()

# Public include path so other targets can `#include "../context/context.h"`
target_include_directories(context PUBLIC
    ${CMAKE_CURRENT_SOURCE_DIR}
    ${CMAKE_SOURCE_DIR}/_deps/json-src/include
    ${CMAKE_SOURCE_DIR}/src
)

# Third-party dependencies
target_link_libraries(context
    PUBLIC
        nlohmann_json::nlohmann_json
) 