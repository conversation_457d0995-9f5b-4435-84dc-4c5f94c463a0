# Context Providers Documentation

## Overview

Context providers are components that collect contextual information from various sources in the system and make it available to actions. This contextual information enables actions to be more intelligent, adaptive, and aware of the user's current environment.

The context system is designed to be:

-   **Extensible**: New context providers can be added to support additional data sources
-   **Platform-aware**: Providers adapt to the capabilities of the current platform
-   **Privacy-focused**: Context is collected only when needed and with appropriate permissions
-   **Structured**: Context data is organized in a hierarchical structure for easy access

## Available Context Providers

The following context providers are available in the system:

| Provider              | Description                                        | Platform Support          |
| --------------------- | -------------------------------------------------- | ------------------------- |
| `clipboard`           | Provides access to clipboard content               | macOS, Windows            |
| `active_window`       | Information about the currently active window      | macOS, Windows            |
| `selected_text`       | Currently selected text in the active application  | macOS, Windows            |
| `open_file`           | Information about currently open files             | macOS, Windows            |
| `current_directory`   | Information about the current working directory    | All platforms             |
| `current_application` | Information about the currently active application | macOS, Windows            |
| `time_location`       | Current time and location information              | All platforms (time only) |

## Context Data Structure

Context data is organized in a hierarchical structure using dot notation. For example, `clipboard.text` refers to the text content of the clipboard. Each provider adds its data under its own namespace.

## Detailed Provider Documentation

### Clipboard Context Provider

The clipboard provider collects information from the system clipboard.

#### Available Context Keys

| Key                             | Type    | Description                                      | Platform          |
| ------------------------------- | ------- | ------------------------------------------------ | ----------------- |
| `clipboard.text`                | String  | Text content of the clipboard                    | macOS, Windows    |
| `clipboard.text_length`         | Number  | Length of the clipboard text                     | macOS, Windows    |
| `clipboard.text_preview`        | String  | Preview of clipboard text (first 100 chars)      | macOS, Windows    |
| `clipboard.html`                | String  | HTML content of the clipboard if available       | macOS, Windows    |
| `clipboard.rtf_available`       | Boolean | Whether RTF content is available                 | macOS, Windows    |
| `clipboard.has_image`           | Boolean | Whether the clipboard contains an image          | macOS, Windows    |
| `clipboard.image_type`          | String  | Type of image in clipboard (e.g., "png", "tiff") | macOS, Windows    |
| `clipboard.image_size`          | Number  | Size of image data in bytes                      | macOS             |
| `clipboard.files`               | Array   | List of file paths in clipboard                  | macOS, Windows    |
| `clipboard.file_count`          | Number  | Number of files in clipboard                     | macOS, Windows    |
| `clipboard.file_names`          | Array   | List of file names without paths                 | macOS, Windows    |
| `clipboard.file_extensions`     | Array   | List of file extensions                          | macOS, Windows    |
| `clipboard.available_types`     | Array   | List of available data types in clipboard        | macOS             |
| `clipboard.available_formats`   | Array   | List of available clipboard formats              | Windows           |
| `clipboard.available`           | Boolean | Whether clipboard access is available            | Generic platforms |
| `clipboard.reason`              | String  | Reason for clipboard unavailability              | Generic platforms |
| `clipboard.supported_platforms` | Array   | List of platforms that support clipboard access  | Generic platforms |

#### Usage Example

```cpp
// Check if clipboard has text
if (context.hasValue("clipboard.text")) {
    std::string text = context.getValue("clipboard.text").asString();
    // Use the clipboard text
}

// Check if clipboard has files
if (context.hasValue("clipboard.files")) {
    nlohmann::json files = context.getValue("clipboard.files").asObject();
    // Process the files
}
```

#### Permissions

-   No special permissions required on Windows
-   No special permissions required on macOS

### Active Window Context Provider

The active window provider collects information about the currently active window.

#### Available Context Keys

| Key                          | Type    | Description                               | Platform          |
| ---------------------------- | ------- | ----------------------------------------- | ----------------- |
| `active_window.title`        | String  | Title of the active window                | macOS, Windows    |
| `active_window.app_name`     | String  | Name of the application owning the window | macOS, Windows    |
| `active_window.bundle_id`    | String  | Bundle identifier of the application      | macOS             |
| `active_window.process_path` | String  | Full path to the application executable   | Windows           |
| `active_window.class_name`   | String  | Window class name                         | Windows           |
| `active_window.available`    | Boolean | Whether window information is available   | Generic platforms |

#### Usage Example

```cpp
// Get the active application name
if (context.hasValue("active_window.app_name")) {
    std::string appName = context.getValue("active_window.app_name").asString();
    // Use the application name
}
```

#### Permissions

-   No special permissions required on Windows
-   No special permissions required on macOS

### Selected Text Context Provider

The selected text provider collects the currently selected text from the active application using multiple techniques.

#### Available Context Keys

| Key                                    | Type    | Description                                                                                                          | Platform          |
| -------------------------------------- | ------- | -------------------------------------------------------------------------------------------------------------------- | ----------------- |
| `selected_text`                        | String  | Currently selected text                                                                                              | macOS, Windows    |
| `selected_text.length`                 | Number  | Length of the selected text                                                                                          | macOS, Windows    |
| `selected_text.preview`                | String  | Preview of selected text (first 100 chars)                                                                           | macOS, Windows    |
| `selected_text.source`                 | String  | Source of the selected text (e.g., "accessibility_api", "app_specific", "applescript", "clipboard", "ui_automation") | macOS, Windows    |
| `selected_text.app`                    | String  | Application from which the text was selected                                                                         | macOS, Windows    |
| `selected_text.available`              | Boolean | Whether selected text is available                                                                                   | All platforms     |
| `selected_text.reason`                 | String  | Reason for text selection unavailability                                                                             | Generic platforms |
| `selected_text.supported_platforms`    | Array   | List of platforms that support text selection                                                                        | Generic platforms |
| `selected_text.required_permissions`   | Array   | List of required permissions                                                                                         | Generic platforms |
| `selected_text.supported_applications` | Object  | List of supported applications by platform                                                                           | Generic platforms |

#### Usage Example

```cpp
// Get selected text if available
if (context.hasValue("selected_text")) {
    std::string text = context.getValue("selected_text").asString();
    // Use the selected text
}

// Check the source of the selected text
if (context.hasValue("selected_text.source")) {
    std::string source = context.getValue("selected_text.source").asString();
    // Different handling based on source
}
```

#### Permissions

-   **macOS**: Requires Accessibility permissions
-   **Windows**: Requires UI Automation permissions

#### Supported Applications

The selected text provider uses different techniques based on the active application:

**macOS**:

-   Safari, Chrome, Brave, Microsoft Edge: Uses JavaScript injection
-   Finder: Gets selected file names
-   Xcode: Uses AppleScript
-   Microsoft Office (Word, Excel, PowerPoint): Uses AppleScript
-   Other applications: Uses Accessibility API or clipboard method

**Windows**:

-   Chrome, Edge, Firefox, Brave: Uses clipboard method
-   File Explorer: Uses Shell API to get selected files
-   Visual Studio: Uses clipboard method
-   Microsoft Office (Word, Excel, PowerPoint): Uses clipboard method
-   Text editors (Notepad, WordPad): Uses direct Windows messages
-   Other applications: Uses UI Automation API or clipboard method

### Open File Context Provider

The open file provider collects information about currently open files in the active application.

#### Available Context Keys

| Key                        | Type    | Description                                    | Platform          |
| -------------------------- | ------- | ---------------------------------------------- | ----------------- |
| `open_file.paths`          | Array   | List of open file paths                        | macOS (Finder)    |
| `open_file.explorer_title` | String  | Title of Explorer window (often contains path) | Windows           |
| `open_file.available`      | Boolean | Whether open file information is available     | Generic platforms |

#### Usage Example

```cpp
// Get open files in Finder
if (context.hasValue("open_file.paths")) {
    nlohmann::json paths = context.getValue("open_file.paths").asObject();
    // Process the open files
}
```

#### Permissions

-   **macOS**: May require Accessibility permissions for some applications
-   **Windows**: No special permissions required

### Current Directory Context Provider

The current directory provider collects information about the current working directory.

#### Available Context Keys

| Key                        | Type   | Description                        | Platform      |
| -------------------------- | ------ | ---------------------------------- | ------------- |
| `current_directory.path`   | String | Full path to the current directory | All platforms |
| `current_directory.parent` | String | Path to the parent directory       | All platforms |
| `current_directory.name`   | String | Name of the current directory      | All platforms |

#### Usage Example

```cpp
// Get current directory path
if (context.hasValue("current_directory.path")) {
    std::string path = context.getValue("current_directory.path").asString();
    // Use the current directory path
}
```

#### Permissions

-   No special permissions required on any platform

### Current Application Context Provider

The current application provider collects information about the currently active application.

#### Available Context Keys

| Key                             | Type    | Description                                  | Platform          |
| ------------------------------- | ------- | -------------------------------------------- | ----------------- |
| `current_application.name`      | String  | Name of the current application              | macOS, Windows    |
| `current_application.bundle_id` | String  | Bundle identifier of the application         | macOS             |
| `current_application.path`      | String  | Full path to the application executable      | macOS, Windows    |
| `current_application.has_icon`  | Boolean | Whether the application has an icon          | macOS             |
| `current_application.version`   | String  | Version of the application                   | Windows           |
| `current_application.available` | Boolean | Whether application information is available | Generic platforms |

#### Usage Example

```cpp
// Get current application name
if (context.hasValue("current_application.name")) {
    std::string appName = context.getValue("current_application.name").asString();
    // Use the application name
}
```

#### Permissions

-   No special permissions required on Windows
-   No special permissions required on macOS

### Time Location Context Provider

The time location provider collects information about the current time and location.

#### Available Context Keys

| Key                  | Type    | Description                                | Platform      |
| -------------------- | ------- | ------------------------------------------ | ------------- |
| `time.hour`          | Number  | Current hour (0-23)                        | All platforms |
| `time.minute`        | Number  | Current minute (0-59)                      | All platforms |
| `time.second`        | Number  | Current second (0-59)                      | All platforms |
| `time.day`           | Number  | Current day of month (1-31)                | All platforms |
| `time.month`         | Number  | Current month (1-12)                       | All platforms |
| `time.year`          | Number  | Current year                               | All platforms |
| `time.weekday`       | Number  | Current day of week (0=Sunday, 6=Saturday) | All platforms |
| `time.iso_string`    | String  | ISO formatted date-time string             | All platforms |
| `location.available` | Boolean | Whether location information is available  | All platforms |

#### Usage Example

```cpp
// Get current hour
if (context.hasValue("time.hour")) {
    int hour = context.getValue("time.hour").asNumber();
    // Use the current hour
}

// Get formatted time
if (context.hasValue("time.iso_string")) {
    std::string timeStr = context.getValue("time.iso_string").asString();
    // Use the formatted time
}
```

#### Permissions

-   No special permissions required for time information
-   Location information is not currently implemented

## Using Context in Actions

Actions can access context data through the `Context` object passed to their `execute` method:

```cpp
ActionResult MyAction::execute(const Context& context) {
    // Check if a specific context value exists
    if (context.hasValue("clipboard.text")) {
        // Get the value
        std::string clipboardText = context.getValue("clipboard.text").asString();

        // Use the value in the action
        // ...
    }

    // Return result
    return ActionResult::createSuccess(getId(), ContextValue::fromString("Success"));
}
```

## Extending with Custom Context Providers

To create a custom context provider:

1. Create a new class that inherits from `ContextProvider`
2. Implement the required methods:
    - `getName()`: Return a unique identifier for the provider
    - `populateContext(Context& context)`: Add values to the context
    - `isAvailable()`: Check if the provider is available on the current platform
3. Register the provider with the `ContextManager`

Example:

```cpp
class MyCustomProvider : public ContextProvider {
public:
    std::string getName() const override { return "my_custom"; }

    void populateContext(Context& context) override {
        // Add values to the context
        context.addValue("my_custom.value", ContextValue::fromString("Hello, world!"), getName());
    }

    bool isAvailable() const override {
        return true;  // Always available
    }
};

// Register the provider
ContextManager::getInstance().registerProvider(std::make_unique<MyCustomProvider>());
```

## Platform-Specific Considerations

### macOS

-   Some context providers require Accessibility permissions
-   AppleScript is used for application-specific functionality
-   Clipboard access uses NSPasteboard

### Windows

-   UI Automation is used for advanced text selection
-   Shell API is used for Explorer integration
-   Clipboard access uses Windows Clipboard API

### Generic Platforms

-   Limited context information is available
-   Current directory and time information are always available
-   Other providers indicate their unavailability with appropriate messages
