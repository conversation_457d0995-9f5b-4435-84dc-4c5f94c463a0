#include "context.h"
#include <memory>

#include <set>
#include <stdexcept>

namespace launcher {
namespace core {

// Define a constant key for storing chat history within the Context
const std::string kChatHistoryKey = "chat_history";

// ContextValue implementation

ContextValue ContextValue::fromString(const std::string& value) {
    return ContextValue(Type::String, value);
}

ContextValue ContextValue::fromNumber(double value) {
    return ContextValue(Type::Number, value);
}

ContextValue ContextValue::fromBoolean(bool value) {
    return ContextValue(Type::Boolean, value);
}

ContextValue ContextValue::fromObject(const nlohmann::json& value) {
    return ContextValue(Type::Object, value);
}

ContextValue ContextValue::fromArray(const std::vector<ContextValue>& values) {
    return ContextValue(Type::Array, values);
}

ContextValue ContextValue::fromCallback(StreamingResponseCallback callback) {
    return ContextValue(Type::Callback, callback);
}

bool ContextValue::isString() const {
    return type_ == Type::String;
}

bool ContextValue::isNumber() const {
    return type_ == Type::Number;
}

bool ContextValue::isBoolean() const {
    return type_ == Type::Boolean;
}

bool ContextValue::isObject() const {
    return type_ == Type::Object;
}

bool ContextValue::isArray() const {
    return type_ == Type::Array;
}

bool ContextValue::isCallback() const {
    return type_ == Type::Callback;
}

std::string ContextValue::asString() const {
    if (type_ == Type::String) {
        return std::get<std::string>(value_);
    } else if (type_ == Type::Number) {
        return std::to_string(std::get<double>(value_));
    } else if (type_ == Type::Boolean) {
        return std::get<bool>(value_) ? "true" : "false";
    } else if (type_ == Type::Object) {
        return std::get<nlohmann::json>(value_).dump();
    } else {
        return "[array]";
    }
}

double ContextValue::asNumber() const {
    if (type_ == Type::Number) {
        return std::get<double>(value_);
    } else if (type_ == Type::String) {
        try {
            return std::stod(std::get<std::string>(value_));
        } catch (...) {
            throw std::runtime_error("Cannot convert string to number: " +
                                     std::get<std::string>(value_));
        }
    } else if (type_ == Type::Boolean) {
        return std::get<bool>(value_) ? 1.0 : 0.0;
    } else {
        throw std::runtime_error("Cannot convert to number");
    }
}

bool ContextValue::asBoolean() const {
    if (type_ == Type::Boolean) {
        return std::get<bool>(value_);
    } else if (type_ == Type::Number) {
        return std::get<double>(value_) != 0.0;
    } else if (type_ == Type::String) {
        std::string str = std::get<std::string>(value_);
        return !str.empty() && str != "0" && str != "false";
    } else {
        return false;
    }
}

nlohmann::json ContextValue::asObject() const {
    if (type_ == Type::Object) {
        return std::get<nlohmann::json>(value_);
    } else if (type_ == Type::String) {
        try {
            // Parse the JSON string into an object
            return nlohmann::json::parse(std::get<std::string>(value_));
        } catch (...) {
            // Return empty object if parsing fails
            return nlohmann::json::object();
        }
    } else {
        return nlohmann::json::object();
    }
}

std::vector<ContextValue> ContextValue::asArray() const {
    if (type_ == Type::Array) {
        return std::get<std::vector<ContextValue>>(value_);
    } else {
        throw std::runtime_error("Cannot convert to array");
    }
}

StreamingResponseCallback ContextValue::asCallback() const {
    if (type_ == Type::Callback) {
        return std::get<StreamingResponseCallback>(value_);
    } else {
        throw std::runtime_error("Cannot convert to callback");
    }
}

nlohmann::json ContextValue::toJson() const {
    switch (type_) {
        case Type::String:
            return std::get<std::string>(value_);
        case Type::Number:
            return std::get<double>(value_);
        case Type::Boolean:
            return std::get<bool>(value_);
        case Type::Object:
            return std::get<nlohmann::json>(value_);
        case Type::Array: {
            nlohmann::json array = nlohmann::json::array();
            for (const auto& item : std::get<std::vector<ContextValue>>(value_)) {
                array.push_back(item.toJson());
            }
            return array;
        }
        case Type::Callback:
            return "[callback]";  // Callbacks cannot be serialized to JSON
        default:
            return nullptr;
    }
}

ContextValue ContextValue::fromJson(const nlohmann::json& json) {
    if (json.is_string()) {
        return fromString(json.get<std::string>());
    } else if (json.is_number()) {
        return fromNumber(json.get<double>());
    } else if (json.is_boolean()) {
        return fromBoolean(json.get<bool>());
    } else if (json.is_object()) {
        return fromObject(json);
    } else if (json.is_array()) {
        std::vector<ContextValue> values;
        for (const auto& item : json) {
            values.push_back(fromJson(item));
        }
        return fromArray(values);
    } else {
        return ContextValue();  // Default to empty string
    }
}

// Context implementation

void Context::addValue(const std::string& key, const ContextValue& value,
                       const std::string& source) {
    values_[key] = value;
    if (!source.empty()) {
        sources_[key] = source;
    }
}

ContextValue Context::getValue(const std::string& key) const {
    auto it = values_.find(key);
    if (it != values_.end()) {
        return it->second;
    }

    return ContextValue();  // Default to empty string
}

bool Context::hasValue(const std::string& key) const {
    return values_.find(key) != values_.end();
}

void Context::removeValue(const std::string& key) {
    values_.erase(key);
    sources_.erase(key);
}

std::unordered_map<std::string, ContextValue> Context::getValues() const {
    return values_;
}

std::string Context::toJson() const {
    nlohmann::json json;
    nlohmann::json sourcesJson = nlohmann::json::object();

    for (const auto& pair : values_) {
        json["values"][pair.first] = pair.second.toJson();
    }

    for (const auto& pair : sources_) {
        sourcesJson[pair.first] = pair.second;
    }

    json["sources"] = sourcesJson;

    return json.dump();
}

Context Context::fromJson(const std::string& json) {
    Context context;

    try {
        nlohmann::json jsonObj = nlohmann::json::parse(json);

        // Parse values
        if (jsonObj.contains("values") && jsonObj["values"].is_object()) {
            for (auto it = jsonObj["values"].begin(); it != jsonObj["values"].end(); ++it) {
                context.addValue(it.key(), ContextValue::fromJson(it.value()));
            }
        } else {
            // Legacy format - direct key-value pairs
            for (auto it = jsonObj.begin(); it != jsonObj.end(); ++it) {
                if (it.key() != "sources") {
                    context.addValue(it.key(), ContextValue::fromJson(it.value()));
                }
            }
        }

        // Parse sources
        if (jsonObj.contains("sources") && jsonObj["sources"].is_object()) {
            for (auto it = jsonObj["sources"].begin(); it != jsonObj["sources"].end(); ++it) {
                context.sources_[it.key()] = it.value().get<std::string>();
            }
        }
    } catch (...) {
        // Ignore parsing errors
    }

    return context;
}

std::vector<std::string> Context::getSources() const {
    std::vector<std::string> result;

    for (const auto& pair : sources_) {
        if (std::find(result.begin(), result.end(), pair.second) == result.end()) {
            result.push_back(pair.second);
        }
    }

    return result;
}

std::string Context::getSourceForKey(const std::string& key) const {
    auto it = sources_.find(key);
    if (it != sources_.end()) {
        return it->second;
    }

    return "";
}

// --- Implementation for Chat History Methods ---
void Context::setChatHistory(const std::vector<ApiChatMessage>& history) {
    // Convert the vector of ChatMessage to a JSON array
    nlohmann::json historyJson = nlohmann::json::array();
    for (const auto& msg : history) {
        historyJson.push_back({{"role", msg.role}, {"content", msg.contentRef()}});
    }
    // Store the JSON object using ContextValue::fromObject
    addValue(kChatHistoryKey, ContextValue::fromObject(historyJson));
}

std::optional<std::vector<ApiChatMessage>> Context::getChatHistory() const {
    if (!hasValue(kChatHistoryKey)) {
        return std::nullopt;
    }

    ContextValue historyValue = getValue(kChatHistoryKey);
    if (!historyValue.isObject()) { // Should be stored as an object (JSON array)
        // TODO(hole): Log an error or warning here? Type mismatch.
        return std::nullopt;
    }

    nlohmann::json historyJson = historyValue.asObject();
    if (!historyJson.is_array()) {
        // TODO(hole): Log an error or warning here? Expected JSON array.
        return std::nullopt;
    }

    std::vector<ApiChatMessage> history;
    try {
        for (const auto& item : historyJson) {
            if (item.is_object() && item.contains("role") && item["role"].is_string() &&
                item.contains("content") && item["content"].is_string()) {
                auto contentPtr = std::make_shared<std::string>(item["content"].get<std::string>());
                history.emplace_back(item["role"].get<std::string>(), contentPtr);
            } else {
                // TODO(hole): Log an error or warning for malformed message object?
            }
        }
        return history;
    } catch (const nlohmann::json::exception& e) {
        // TODO(hole): Log the JSON parsing error (e.what())
        return std::nullopt;
    }
}
// --- End Implementation for Chat History Methods ---

// ContextManager implementation

void ContextManager::registerProvider(std::unique_ptr<ContextProvider> provider) {
    if (!provider) {
        return;
    }

    const std::string& name = provider->getName();
    providers_[name] = std::move(provider);
}

void ContextManager::unregisterProvider(const std::string& providerName) {
    providers_.erase(providerName);
}

Context ContextManager::collectContext() {
    Context context;

    for (const auto& pair : providers_) {
        if (pair.second->isAvailable()) {
            pair.second->populateContext(context);
        }
    }

    return context;
}

Context ContextManager::collectContextFromProviders(const std::vector<std::string>& providerNames) {
    Context context;

    for (const auto& name : providerNames) {
        auto it = providers_.find(name);
        if (it != providers_.end() && it->second->isAvailable()) {
            it->second->populateContext(context);
        }
    }

    return context;
}

}  // namespace core
}  // namespace launcher