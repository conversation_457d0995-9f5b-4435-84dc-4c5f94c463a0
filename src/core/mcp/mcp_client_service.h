#pragma once

// -----------------------------------------------------------------------------
// @file mcp_client_service.h
// @brief Skeleton MCP client responsible for managing connections to MCP servers.
//        Slice-1 scope: placeholder start/stop and dependency wiring.
// -----------------------------------------------------------------------------

#include "../foundation/iservice.h"
#include "../foundation/service_base.h"
#include "../diagnostics/diagnostics_service.h"
#include "../util/result.h"

namespace launcher::core::mcp {

class McpClientService final : public launcher::core::foundation::ServiceBase<McpClientService, launcher::core::diagnostics::DiagnosticsService> {
 public:
    static constexpr launcher::core::foundation::ServiceId kId =
        launcher::core::foundation::ServiceId::kMcpClientService;

    using Base = launcher::core::foundation::ServiceBase<McpClientService, launcher::core::diagnostics::DiagnosticsService>;

    explicit McpClientService(launcher::core::foundation::ServiceRegistry& registry)
        : Base(registry) {}

    // IService -------------------------------------------------------------
    [[nodiscard]] launcher::core::util::Result<void> start() override;
    void                                           stop() noexcept override;

    // TODO: attach dependencies once full implementation lands (diagnostics, event bus, etc.)

 private:
    bool started_{false};
};

} // namespace launcher::core::mcp 