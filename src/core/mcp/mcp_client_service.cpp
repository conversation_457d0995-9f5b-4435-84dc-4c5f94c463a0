// -----------------------------------------------------------------------------
// @file mcp_client_service.cpp
// -----------------------------------------------------------------------------

#include "mcp_client_service.h"

#include "../util/debug.h"
#include "../diagnostics/diagnostics_service.h"

namespace launcher::core::mcp {

using launcher::core::util::Result;

// ----------------------------- start ----------------------------------------
Result<void> McpClientService::start() {
    if (started_) return Result<void>::success();
    DBG("McpClientService::start – placeholder implementation");
    // Record metric via DiagnosticsService (compile-time fetched dependency).
    auto& diag = this->get<launcher::core::diagnostics::DiagnosticsService>();
    (void)diag.incrementCounter("mcpclient.start_called");
    started_ = true;
    return Result<void>::success();
}

// ----------------------------- stop -----------------------------------------
void McpClientService::stop() noexcept {
    if (!started_) return;
    DBG("McpClientService::stop – placeholder implementation");
    started_ = false;
}

} // namespace launcher::core::mcp 