#include "core/util/json_writer.h"
#include <cassert>
#include <cstring>
#include <cstdlib>
#include <utility>

namespace launcher::core::util {

JsonWriter::JsonWriter() {
    doc_ = yyjson_mut_doc_new(nullptr);
    stack_.reserve(8);
}

JsonWriter::~JsonWriter() {
    if (doc_) {
        yyjson_mut_doc_free(doc_);
        doc_ = nullptr;
    }
}

yyjson_mut_val* JsonWriter::current() {
    return stack_.empty() ? nullptr : stack_.back();
}

void JsonWriter::push(yyjson_mut_val* v) { stack_.push_back(v); }
void JsonWriter::pop() { if (!stack_.empty()) stack_.pop_back(); }

// ---------------- Container helpers -----------------------------
void JsonWriter::startObject() {
    yyjson_mut_val* obj = yyjson_mut_obj(doc_);
    if (!current()) {
        yyjson_mut_doc_set_root(doc_, obj);
    } else if (!pending_key_.empty()) {
        yyjson_mut_obj_add_val(doc_, current(), pending_key_.c_str(), obj);
        pending_key_.clear();
    } else {
        yyjson_mut_arr_add_val(current(), obj);
    }
    push(obj);
}

void JsonWriter::startArray() {
    yyjson_mut_val* arr = yyjson_mut_arr(doc_);
    if (!current()) {
        yyjson_mut_doc_set_root(doc_, arr);
    } else if (!pending_key_.empty()) {
        yyjson_mut_obj_add_val(doc_, current(), pending_key_.c_str(), arr);
        pending_key_.clear();
    } else {
        yyjson_mut_arr_add_val(current(), arr);
    }
    push(arr);
}

void JsonWriter::end() {
    assert(!stack_.empty());
    pop();
}

// ---------------- Key/value helpers -----------------------------
void JsonWriter::key(std::string_view k) { pending_key_.assign(k.data(), k.size()); }

// Generic value inserter used by overloads
void JsonWriter::null() {
    yyjson_mut_val* val = yyjson_mut_null(doc_);
    if (!pending_key_.empty()) {
        yyjson_mut_obj_add_val(doc_, current(), pending_key_.c_str(), val);
        pending_key_.clear();
    } else {
        yyjson_mut_arr_add_val(current(), val);
    }
}

void JsonWriter::value(std::string_view v) {
    yyjson_mut_val* val = yyjson_mut_strn(doc_, v.data(), v.size());
    if (!pending_key_.empty()) {
        yyjson_mut_obj_add_val(doc_, current(), pending_key_.c_str(), val);
        pending_key_.clear();
    } else {
        yyjson_mut_arr_add_val(current(), val);
    }
}
void JsonWriter::value(const char* v) { value(std::string_view(v)); }

void JsonWriter::value(bool b) {
    yyjson_mut_val* val = yyjson_mut_bool(doc_, b);
    if (!pending_key_.empty()) {
        yyjson_mut_obj_add_val(doc_, current(), pending_key_.c_str(), val);
        pending_key_.clear();
    } else {
        yyjson_mut_arr_add_val(current(), val);
    }
}

void JsonWriter::value(uint64_t n) {
    yyjson_mut_val* val = yyjson_mut_uint(doc_, n);
    if (!pending_key_.empty()) {
        yyjson_mut_obj_add_val(doc_, current(), pending_key_.c_str(), val);
        pending_key_.clear();
    } else {
        yyjson_mut_arr_add_val(current(), val);
    }
}

void JsonWriter::value(int64_t n) {
    yyjson_mut_val* val = yyjson_mut_sint(doc_, n);
    if (!pending_key_.empty()) {
        yyjson_mut_obj_add_val(doc_, current(), pending_key_.c_str(), val);
        pending_key_.clear();
    } else {
        yyjson_mut_arr_add_val(current(), val);
    }
}

void JsonWriter::value(double d) {
    yyjson_mut_val* val = yyjson_mut_real(doc_, d);
    if (!pending_key_.empty()) {
        yyjson_mut_obj_add_val(doc_, current(), pending_key_.c_str(), val);
        pending_key_.clear();
    } else {
        yyjson_mut_arr_add_val(current(), val);
    }
}

void JsonWriter::value(int n) { value(static_cast<int64_t>(n)); }

void JsonWriter::value(unsigned int n) { value(static_cast<uint64_t>(n)); }

std::string JsonWriter::finish() {
    size_t len;
    char* raw = yyjson_mut_write(doc_, YYJSON_WRITE_NOFLAG, &len);
    std::string out(raw, len);
    free(raw);
    return out;
}

} // namespace launcher::core::util 