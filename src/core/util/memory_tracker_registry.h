#pragma once

#include <atomic>
#include <cassert>

namespace launcher::core {

class MemoryTracker;

// Internal helper to hold the global pointer atomically.
inline std::atomic<MemoryTracker*>& _trackerSlot() {
    static std::atomic<MemoryTracker*> ptr{nullptr};
    return ptr;
}

// Register an application-level tracker instance. Pass nullptr to unregister.
inline void registerMemoryTracker(MemoryTracker* tracker) {
    _trackerSlot().store(tracker, std::memory_order_release);
}

// currentMemoryTracker and TRACKER declared later after MemoryTracker definition.

} // namespace launcher::core

#include "memory_tracker.h"  // brings in full definition for fallback

namespace launcher::core {

// Re-open namespace to provide inline definitions that require MemoryTracker size

inline MemoryTracker& currentMemoryTracker() {
    MemoryTracker* p = _trackerSlot().load(std::memory_order_acquire);
    if (p) { return *p; }
    static MemoryTracker fallback;
    return fallback;
}

inline MemoryTracker& TRACKER() { return currentMemoryTracker(); }

} // namespace launcher::core 