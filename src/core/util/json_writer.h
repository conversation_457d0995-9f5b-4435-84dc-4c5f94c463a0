/*
 * Minimal JSON writer built on top of yyj<PERSON> to replace RapidJSON Writer.
 */
#pragma once

#include <string>
#include <string_view>
#include <vector>
#include <cstdint>
#include <utility>
#include <yyjson.h>

namespace launcher::core::util {

class JsonWriter {
 public:
  JsonWriter();
  ~JsonWriter();

  void startObject();
  void startArray();
  void end();  // closes current container

  void key(std::string_view k);

  void value(std::string_view v);
  void value(const char* v);
  void value(bool b);
  void value(uint64_t n);
  void value(int64_t n);
  void value(int n);
  void value(unsigned int n);
  void value(double d);
  void null();

  template<typename T>
  void kv(std::string_view k, T&& val) { key(k); value(std::forward<T>(val)); }

  std::string finish();

 private:
  yyjson_mut_doc* doc_;
  std::vector<yyjson_mut_val*> stack_;
  std::string pending_key_;

  yyjson_mut_val* current();
  void push(yyjson_mut_val* v);
  void pop();
};

} // namespace launcher::core::util 