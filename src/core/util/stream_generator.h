#pragma once

#include <coroutine>
#include <optional>
#include <memory>
#include "stream_channel.h"

namespace launcher::core::util {

template <typename T>
class AsyncStream {
 public:
    explicit AsyncStream(std::shared_ptr<StreamChannel<T>> ch)
        : channel_(std::move(ch)) {}

    // Blocking retrieval (for non-coroutine environments)
    std::optional<T> waitNext(std::shared_ptr<::launcher::core::utilities::CancellationToken> tok = nullptr) {
        return channel_->waitPop(std::move(tok));
    }

    std::optional<http::HttpError> error() const { return channel_->error(); }

    auto next(std::shared_ptr<::launcher::core::utilities::CancellationToken> tok = nullptr) { return channel_->coPop(std::move(tok)); }

 private:
    std::shared_ptr<StreamChannel<T>> channel_;
};

}  // namespace launcher::core::util 