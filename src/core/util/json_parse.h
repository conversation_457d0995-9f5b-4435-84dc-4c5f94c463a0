// json_parse.h - JSON parsing helpers with selectable backend
#pragma once

#include <string_view>
#include <string>

// -----------------------------------------------------------------------------
// Compile-time selection between RapidJSON and simdjson.
//   • Define KAI_JSON_PARSER_SIMDJSON  – use simdjson ondemand parser
//   • Define KAI_JSON_PARSER_RAPIDJSON – use RapidJSON DOM parser
// The root CMakeLists.txt sets one of these based on KAI_JSON_PARSER option.
// -----------------------------------------------------------------------------

#if defined(KA<PERSON>_JSON_PARSER_SIMDJSON)
#include <simdjson.h>
#elif defined(KAI_JSON_PARSER_RAPIDJSON)
#include <rapidjson/document.h>
#else
#error "KAI_JSON_PARSER_SIMDJSON or KAI_JSON_PARSER_RAPIDJSON must be defined"
#endif

namespace launcher::core::util {

#if defined(KAI_JSON_PARSER_SIMDJSON)
// Lightweight wrapper returning an on-demand document.  The underlying parser is
// thread-local to avoid contention and heap churn.
inline simdjson::ondemand::document parseJson(std::string_view json) {
    thread_local simdjson::ondemand::parser parser;
    return parser.iterate(json);
}
#endif

#if defined(KAI_JSON_PARSER_RAPIDJSON)
inline rapidjson::Document parseJson(std::string_view json) {
    rapidjson::Document doc;
    doc.Parse(json.data(), json.size());
    return doc;
}
#endif

} // namespace launcher::core::util 