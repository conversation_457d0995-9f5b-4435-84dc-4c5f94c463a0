// -----------------------------------------------------------------------------
// @file metrics.hh
// @brief MetricSource concept and compile-time enumeration helpers.
//        Any type that exposes `.stats()` and `static constexpr bool is_metric_source = true`
//        satisfies the concept.  This lightweight header avoids introducing
//        a full registry; higher-level services (diagnostics exporter, prom
//        endpoint) can call `for_each_type` with a tuple of metric sources.
// -----------------------------------------------------------------------------

#pragma once

#include <concepts>
#include <tuple>
#include <utility>
#include <type_traits>

namespace launcher::core::util {

// -------------------------------------------------------------------------
// MetricSource concept – requires `.stats()` method returning any type and
// a static boolean tag.  The latter prevents accidental inclusion of random
// classes that happen to have a stats() free function.
// -------------------------------------------------------------------------

template <class T>
concept MetricSource = requires(T t) {
    { t.stats() };  // must be callable
    requires std::is_class_v<decltype(t.stats())>;
    { T::is_metric_source } -> std::convertible_to<const bool&>;
};

// -------------------------------------------------------------------------
// Helper: compile-time iteration over a std::tuple of types.
// -------------------------------------------------------------------------

template <typename Tuple, typename F>
constexpr void for_each_type(F&& fn) {
    constexpr std::size_t N = std::tuple_size_v<Tuple>;
    [&]<std::size_t... Is>(std::index_sequence<Is...>) {
        (fn.template operator()<std::tuple_element_t<Is, Tuple>>(), ...);
    }(std::make_index_sequence<N>{});
}

// overload for tuple instances (runtime values) – useful for iterating over
// concrete objects composed at runtime but known types at compile time.

template <typename... Ts, typename F>
constexpr void for_each_type(const std::tuple<Ts...>&, F&& fn) {
    (fn.template operator()<Ts>(), ...);
}

} // namespace launcher::core::util 