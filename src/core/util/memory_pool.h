#pragma once

#include <cstddef>
#include <cstdint>
#include <memory>
#include <mutex>
#include <vector>

namespace launcher {
namespace core {

/**
 * @brief A memory pool allocator for efficient allocation of small objects
 *
 * This class provides a memory pool that pre-allocates memory in chunks and
 * allows for efficient allocation and deallocation of small objects of a fixed size.
 * It reduces memory fragmentation and improves performance by avoiding frequent
 * calls to the system allocator.
 *
 * @tparam T The type of objects to allocate
 * @tparam ChunkSize The number of objects per chunk
 */
template <typename T, size_t ChunkSize = 64>
class MemoryPool {
 public:
    /**
     * @brief Construct a new Memory Pool
     */
    MemoryPool() : currentChunk_(nullptr), freeList_(nullptr) {
        // Allocate the first chunk
        allocateChunk();
    }

    /**
     * @brief Destroy the Memory Pool
     */
    ~MemoryPool() {
        // Free all chunks
        for (auto& chunk : chunks_) {
            ::operator delete(chunk);
        }
    }

    /**
     * @brief Allocate memory for an object
     *
     * @return void* Pointer to the allocated memory
     */
    void* allocate() {
        std::lock_guard<std::mutex> lock(mutex_);

        // If there are free blocks, use one
        if (freeList_ != nullptr) {
            void* result = freeList_;
            freeList_ = *reinterpret_cast<void**>(freeList_);
            return result;
        }

        // If the current chunk is full, allocate a new one
        if (currentIndex_ >= ChunkSize) {
            allocateChunk();
        }

        // Return the next block in the current chunk
        void* result = reinterpret_cast<char*>(currentChunk_) + currentIndex_ * blockSize_;
        currentIndex_++;
        return result;
    }

    /**
     * @brief Deallocate memory
     *
     * @param p Pointer to the memory to deallocate
     */
    void deallocate(void* p) {
        if (p == nullptr) {
            return;
        }

        std::lock_guard<std::mutex> lock(mutex_);

        // Add the block to the free list
        *reinterpret_cast<void**>(p) = freeList_;
        freeList_ = p;
    }

    /**
     * @brief Get the number of chunks allocated
     *
     * @return size_t The number of chunks
     */
    size_t getChunkCount() const {
        std::lock_guard<std::mutex> lock(mutex_);
        return chunks_.size();
    }

    /**
     * @brief Get the total memory allocated in bytes
     *
     * @return size_t The total memory in bytes
     */
    size_t getTotalMemory() const {
        std::lock_guard<std::mutex> lock(mutex_);
        return chunks_.size() * ChunkSize * blockSize_;
    }

 private:
    /**
     * @brief Allocate a new chunk of memory
     */
    void allocateChunk() {
        // Calculate the block size (max of sizeof(T) and sizeof(void*))
        blockSize_ = sizeof(T) < sizeof(void*) ? sizeof(void*) : sizeof(T);

        // Allocate a new chunk
        currentChunk_ = ::operator new(ChunkSize * blockSize_);
        chunks_.push_back(currentChunk_);
        currentIndex_ = 0;
    }

 private:
    std::vector<void*> chunks_;  ///< List of allocated chunks
    void* currentChunk_;         ///< Current chunk being used
    size_t currentIndex_ = 0;    ///< Current index in the current chunk
    size_t blockSize_ = 0;       ///< Size of each block
    void* freeList_ = nullptr;   ///< List of free blocks
    mutable std::mutex mutex_;   ///< Mutex for thread safety
};

/**
 * @brief Custom allocator that uses a memory pool
 *
 * This class provides a standard-compliant allocator that uses a memory pool
 * for efficient allocation and deallocation of objects.
 *
 * @tparam T The type of objects to allocate
 */
template <typename T>
class PoolAllocator {
 public:
    using value_type = T;
    using pointer = T*;
    using const_pointer = const T*;
    using reference = T&;
    using const_reference = const T&;
    using size_type = std::size_t;
    using difference_type = std::ptrdiff_t;

    template <typename U>
    struct rebind {
        using other = PoolAllocator<U>;
    };

    /**
     * @brief Construct a new Pool Allocator
     */
    PoolAllocator() : pool_(std::make_shared<MemoryPool<T>>()) {}

    /**
     * @brief Construct a new Pool Allocator from another allocator
     *
     * @tparam U The type of the other allocator
     * @param other The other allocator
     */
    template <typename U>
    PoolAllocator(const PoolAllocator<U>& other) : pool_(other.pool_) {}

    /**
     * @brief Allocate memory for n objects
     *
     * @param n The number of objects
     * @return pointer Pointer to the allocated memory
     */
    pointer allocate(size_type n) {
        if (n == 1) {
            return static_cast<pointer>(pool_->allocate());
        } else {
            // For larger allocations, fall back to the standard allocator
            return static_cast<pointer>(::operator new(n * sizeof(T)));
        }
    }

    /**
     * @brief Deallocate memory
     *
     * @param p Pointer to the memory to deallocate
     * @param n The number of objects
     */
    void deallocate(pointer p, size_type n) {
        if (n == 1) {
            pool_->deallocate(p);
        } else {
            // For larger allocations, use the standard deallocator
            ::operator delete(p);
        }
    }

    /**
     * @brief Construct an object at the given address
     *
     * @tparam Args The types of the constructor arguments
     * @param p The address to construct the object at
     * @param args The constructor arguments
     */
    template <typename... Args>
    void construct(pointer p, Args&&... args) {
        new (p) T(std::forward<Args>(args)...);
    }

    /**
     * @brief Destroy an object at the given address
     *
     * @param p The address of the object to destroy
     */
    void destroy(pointer p) { p->~T(); }

    /**
     * @brief Check if two allocators are equal
     *
     * @tparam U The type of the other allocator
     * @param other The other allocator
     * @return true If the allocators are equal
     * @return false If the allocators are not equal
     */
    template <typename U>
    bool operator==(const PoolAllocator<U>& other) const {
        return pool_ == other.pool_;
    }

    /**
     * @brief Check if two allocators are not equal
     *
     * @tparam U The type of the other allocator
     * @param other The other allocator
     * @return true If the allocators are not equal
     * @return false If the allocators are equal
     */
    template <typename U>
    bool operator!=(const PoolAllocator<U>& other) const {
        return pool_ != other.pool_;
    }

 private:
    std::shared_ptr<MemoryPool<T>> pool_;

    template <typename U>
    friend class PoolAllocator;
};

}  // namespace core
}  // namespace launcher