#pragma once

#include <chrono>
#include <functional>
#include <memory>
#include <string>
#include <vector>
#include "../util/result.h"

namespace launcher {
namespace core {

/**
 * @brief Type of file system event
 */
enum class FileEventType {
    Created,   // File or directory was created
    Modified,  // File or directory was modified
    Deleted,   // File or directory was deleted
    Renamed,   // File or directory was renamed
    Unknown    // Unknown event type
};

/**
 * @brief File system event information
 */
struct FileEvent {
    std::string path;                            // Path to the file or directory
    std::string old_path;                        // Old path (for rename events)
    FileEventType type;                          // Type of event
    std::chrono::system_clock::time_point time;  // Time of the event

    FileEvent() : type(FileEventType::Unknown) { time = std::chrono::system_clock::now(); }

    FileEvent(const std::string& path, FileEventType type) : path(path), type(type) {
        time = std::chrono::system_clock::now();
    }

    FileEvent(const std::string& path, const std::string& old_path, FileEventType type)
        : path(path), old_path(old_path), type(type) {
        time = std::chrono::system_clock::now();
    }
};

/**
 * @brief Callback function type for file events
 */
using FileEventCallback = std::function<void(const FileEvent&)>;

/**
 * @brief Interface for file system watchers
 *
 * This interface defines the methods that platform-specific
 * file watchers must implement to monitor file system changes.
 */
class FileWatcher {
 public:
    virtual ~FileWatcher() = default;

    /**
     * @brief Add a directory to watch
     *
     * @param path Path to the directory to watch
     * @param recursive Whether to watch subdirectories recursively
     * @return true if the directory was added successfully
     */
    virtual util::Result<void> addWatch(const std::string& path, bool recursive = true) = 0;

    /**
     * @brief Remove a directory from the watch list
     *
     * @param path Path to the directory to stop watching
     * @return true if the directory was removed successfully
     */
    virtual util::Result<void> removeWatch(const std::string& path) = 0;

    /**
     * @brief Start watching for file system events
     *
     * @param callback Function to call when an event occurs
     * @return true if watching started successfully
     */
    virtual util::Result<void> startWatching(FileEventCallback callback) = 0;

    /**
     * @brief Stop watching for file system events
     *
     * @return true if watching stopped successfully
     */
    virtual util::Result<void> stopWatching() = 0;

    /**
     * @brief Check if the watcher is currently active
     *
     * @return true if the watcher is active
     */
    virtual bool isWatching() const = 0;

    /**
     * @brief Get the list of directories being watched
     *
     * @return Vector of watched directory paths
     */
    virtual std::vector<std::string> getWatchedDirectories() const = 0;

    /**
     * @brief Create a platform-specific file watcher instance
     *
     * @return std::shared_ptr<FileWatcher> A shared pointer to the file watcher
     */
    static std::shared_ptr<FileWatcher> create();
};

}  // namespace core
}  // namespace launcher