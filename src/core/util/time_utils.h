/**
 * @file time_utils.h
 * @brief Time utilities (header-only)
 *
 * Provides helper functions for obtaining the current wall-clock time with
 * millisecond precision in a human-readable format. Kept header-only to avoid
 * linkage overhead and simplify inclusion in logging macros.
 */

#ifndef LAUNCHER_TIME_UTILS_H
#define LAUNCHER_TIME_UTILS_H

#include <chrono>
#include <iomanip>
#include <sstream>
#include <string>

namespace time_utils {

/**
 * @brief Get current local time formatted as HH:MM:SS.mmm.
 *
 * The function is implemented header-only and is inexpensive (≈ 1–5 µs on
 * desktop platforms). It is primarily intended for debugging / logging, so
 * performance impact in hot paths should be assessed before usage elsewhere.
 *
 * Thread-safe since it uses thread-local broken-down time routines.
 *
 * @return std::string Human-readable timestamp in local time zone.
 */
[[nodiscard]] inline std::string currentTimeMsString() {
    using namespace std::chrono;

    const auto now = system_clock::now();
    const auto ms_part = duration_cast<milliseconds>(now.time_since_epoch()) % 1000;

    const std::time_t now_c = system_clock::to_time_t(now);

    std::tm tm_buf;
#if defined(_WIN32)
    localtime_s(&tm_buf, &now_c);
#else
    localtime_r(&now_c, &tm_buf);
#endif

    std::ostringstream oss;
    oss << std::put_time(&tm_buf, "%H:%M:%S") << '.' << std::setfill('0') << std::setw(3)
        << ms_part.count();

    return oss.str();
}

}  // namespace time_utils

// Additional time utilities from the utils/time_utils.h file
namespace launcher {
namespace core {
namespace utils {

/**
 * @brief Convert a time_point to a string
 *
 * @param time The time point to convert
 * @return std::string The formatted time string
 */
inline std::string timePointToString(const std::chrono::system_clock::time_point& time) {
    const std::time_t time_c = std::chrono::system_clock::to_time_t(time);

    std::tm tm_buf{};
#if defined(_WIN32)
    localtime_s(&tm_buf, &time_c);
#else
    localtime_r(&time_c, &tm_buf);
#endif

    std::stringstream ss;
    ss << std::put_time(&tm_buf, "%Y-%m-%d %H:%M:%S");
    return ss.str();
}

/**
 * @brief Convert a string to a time_point
 *
 * @param str The string to convert
 * @return std::chrono::system_clock::time_point The parsed time point
 */
inline std::chrono::system_clock::time_point stringToTimePoint(const std::string& str) {
    std::tm tm = {};
    std::stringstream ss(str);
    ss >> std::get_time(&tm, "%Y-%m-%d %H:%M:%S");
    return std::chrono::system_clock::from_time_t(std::mktime(&tm));
}

}  // namespace utils
}  // namespace core
}  // namespace launcher

#endif  // LAUNCHER_TIME_UTILS_H 