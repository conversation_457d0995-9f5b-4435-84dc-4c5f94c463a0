#pragma once

#include <condition_variable>
#include <mutex>
#include <queue>
#include <string>
#include <thread>
#include <atomic>
#include <iostream>
#include <chrono>
#include <cstdlib>

namespace launcher {
namespace core {
namespace util {

#ifndef LAUNCHER_LOG_MAX_QUEUE
#define LAUNCHER_LOG_MAX_QUEUE 8192
#endif
#ifndef LAUNCHER_LOG_FLUSH_BATCH
#define LAUNCHER_LOG_FLUSH_BATCH 64
#endif

class AsyncLogger {
 public:
    static AsyncLogger& instance() {
        static AsyncLogger inst; return inst;
    }

    // Non-copyable
    AsyncLogger(const AsyncLogger&) = delete;
    AsyncLogger& operator=(const AsyncLogger&) = delete;

    inline void enqueue(std::string_view line_sv) {
        std::string line(line_sv); // Convert to std::string for the queue
        // If shutdown already happened, fall back to synchronous output.
        if (!running_.load(std::memory_order_acquire)) {
            std::cout << line << std::endl;
            return;
        }

        {
            std::lock_guard<std::mutex> lock(mutex_);
            if (queue_.size() >= LAUNCHER_LOG_MAX_QUEUE) {
                // Drop oldest to keep memory bounded.
                queue_.pop();
            }
            queue_.push(std::move(line));
        }
        cv_.notify_one();
    }

    // Gracefully stop background thread and flush remaining messages.
    void shutdown() {
        bool expected = true;
        if (!running_.compare_exchange_strong(expected, false)) {
            return; // already shut down
        }
        stop_.store(true, std::memory_order_release);
        cv_.notify_all();
        if (worker_.joinable()) worker_.join();
    }

 private:
    AsyncLogger() {
        worker_ = std::thread([this]{ run(); });
        std::atexit([](){ AsyncLogger::instance().shutdown(); });
    }
    ~AsyncLogger() {
        shutdown();
    }

    void run() {
        while (!stop_.load(std::memory_order_acquire)) {
            std::unique_lock<std::mutex> lock(mutex_);
            cv_.wait_for(lock, std::chrono::milliseconds(100), [this]{
                return stop_.load(std::memory_order_relaxed) || !queue_.empty();
            });

            size_t batchCount = 0;
            while (!queue_.empty() && batchCount < LAUNCHER_LOG_FLUSH_BATCH) {
                std::string msg = std::move(queue_.front());
                queue_.pop();
                lock.unlock();
                std::cout << msg << '\n';
                ++batchCount;
                lock.lock();
            }
            if (batchCount) {
                std::cout.flush();
            }
        }
        // Flush leftovers
        for (;;) {
            std::lock_guard<std::mutex> lock(mutex_);
            if (queue_.empty()) break;
            std::string msg = std::move(queue_.front());
            queue_.pop();
            std::cout << msg << '\n';
        }
        std::cout.flush();
    }

    std::mutex mutex_;
    std::condition_variable cv_;
    std::queue<std::string> queue_;
    std::thread worker_;
    std::atomic<bool> stop_{false};
    std::atomic<bool> running_{true};
};

} // namespace util
} // namespace core
} // namespace launcher 