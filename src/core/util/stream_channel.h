#pragma once

#include <condition_variable>
#include <mutex>
#include <optional>
#include <queue>
#include <thread>
#include <functional>
#include <coroutine>
#include <memory>
#include "core/http/http_error.h"
#include "utilities/cancellation_token.h"
#include <algorithm>

namespace launcher::core::util {

template <typename T>
class StreamChannel {
 public:
    void push(T value) {
        std::unique_lock<std::mutex> lk(mtx_);
        if (closed_) return;
        if (capacity_ > 0) {
            cv_.wait(lk, [&] { return closed_ || q_.size() < capacity_; });
            if (closed_) return;
        }
        q_.push(std::move(value));

        // Resume coroutine if waiting
        if (!waiters_.empty()) {
            auto local = std::move(waiters_);
            waiters_.clear();
            for (auto h : local) h.resume();
            lk.unlock();
        } else {
            lk.unlock();
            cv_.notify_one();
        }
    }

    void close() {
        std::vector<std::coroutine_handle<>> local;
        {
            std::unique_lock<std::mutex> lk(mtx_);
            closed_ = true;
            local = std::move(waiters_);
            waiters_.clear();
        }
        for (auto h : local) h.resume();
        cv_.notify_all();
    }

    void abort(const http::HttpError& err) {
        std::vector<std::coroutine_handle<>> local;
        {
            std::unique_lock<std::mutex> lk(mtx_);
            closed_ = true;
            error_ = err;
            local = std::move(waiters_);
            waiters_.clear();
        }
        for (auto h : local) h.resume();
        cv_.notify_all();
    }

    // Blocking pop; returns std::optional. Empty optional indicates closed and no more data.
    std::optional<T> waitPop(std::shared_ptr<::launcher::core::utilities::CancellationToken> token = nullptr) {
        std::unique_lock<std::mutex> lock(mtx_);
        std::shared_ptr<::launcher::core::utilities::CancellationToken> regTok = token;
        if (regTok) {
            regTok->registerCallback([this]() {
                std::lock_guard<std::mutex> lk(mtx_);
                cv_.notify_all();
            });
        }
        cv_.wait(lock, [this, &token] {
            return closed_ || !q_.empty() || (token && token->isCancelled());
        });
        if (token && token->isCancelled()) {
            return std::nullopt;
        }
        if (!q_.empty()) {
            T val = std::move(q_.front());
            q_.pop();
            lock.unlock();
            cv_.notify_one(); // notify potential producers waiting
            return val;
        }
        return std::nullopt;
    }

    // Legacy asyncPop removed; use coPop() for coroutine or waitPop() for blocking.

    std::optional<http::HttpError> error() const { return error_; }

    void setCapacity(size_t cap) { capacity_ = cap; }

    // Allow external owner to extend the lifetime of referenced buffers
    void setKeepAlive(std::shared_ptr<void> p) { keep_alive_ = std::move(p); }

    // Coroutine-friendly awaitable pop ----------------------------------
    struct PopAwaiter {
        StreamChannel* ch_;
        std::optional<T> result_;
        std::shared_ptr<::launcher::core::utilities::CancellationToken> token_;

        bool await_ready() {
            if (token_ && token_->isCancelled()) {
                return true;
            }
            std::lock_guard<std::mutex> lock(ch_->mtx_);
            if (!ch_->q_.empty()) {
                result_ = std::move(ch_->q_.front());
                ch_->q_.pop();
                return true;
            }
            if (ch_->closed_) {
                return true; // will resume, result_ empty
            }
            return false;
        }

        bool await_suspend(std::coroutine_handle<> h) {
            if (token_ && token_->isCancelled()) {
                return false; // don't suspend, resume immediately
            }
            {
                std::lock_guard<std::mutex> lock(ch_->mtx_);
                ch_->waiters_.push_back(h);
            }
            if (token_) {
                // Register callback to resume this coroutine upon cancellation. Capture handle by value.
                auto weak_ch = ch_;
                std::coroutine_handle<> handle_copy = h;
                token_->registerCallback([weak_ch, handle_copy]() {
                    // Attempt to remove from waiters list to avoid duplicate resume.
                    std::lock_guard<std::mutex> lk(weak_ch->mtx_);
                    auto& vec = weak_ch->waiters_;
                    auto it = std::find(vec.begin(), vec.end(), handle_copy);
                    if (it != vec.end()) {
                        vec.erase(it);
                    }
                    if (handle_copy && !handle_copy.done()) {
                        handle_copy.resume();
                    }
                });
            }
            return true; // suspend
        }

        std::optional<T> await_resume() {
            if (result_.has_value()) return std::move(result_);
            std::lock_guard<std::mutex> lock(ch_->mtx_);
            if (!ch_->q_.empty()) {
                auto v = std::move(ch_->q_.front());
                ch_->q_.pop();
                return v;
            }
            return std::nullopt;
        }
    };

    PopAwaiter coPop(std::shared_ptr<::launcher::core::utilities::CancellationToken> tok = nullptr) {
        return PopAwaiter{this, std::nullopt, std::move(tok)};
    }

 private:
    std::queue<T> q_;
    mutable std::mutex mtx_;
    std::condition_variable cv_;
    bool closed_ = false;
    std::optional<http::HttpError> error_;
    size_t capacity_ = 0; // 0 = unbounded

    std::vector<std::coroutine_handle<>> waiters_;
    std::shared_ptr<void> keep_alive_{}; // extra storage kept alive with channel
};

}  // namespace launcher::core::util 