#pragma once

#include <cstddef>
#include <type_traits>
#include <utility>
#include <new>
#include <cstring>

namespace launcher::core::util {

// ---------------------------------------------------------------------------
// move_only_function<void()> – minimal, allocator-free function wrapper.
//   • Stores callable inline in fixed buffer (default 64 bytes).
//   • Move-only; no heap allocation; no RTTI.
//   • Intended for high-perf task queues (ExecutorService / EventBus).
// ---------------------------------------------------------------------------

template <std::size_t InlineSize = 64>
class MoveOnlyFunction {
 public:
    static constexpr std::size_t kInlineSize = InlineSize;

    MoveOnlyFunction() noexcept = default;

    template <typename F>
    MoveOnlyFunction(F&& fn) {
        using Fn = std::decay_t<F>;
        static_assert(std::is_invocable_r_v<void, Fn>, "Callable must be void()");
        static_assert(sizeof(Fn) <= InlineSize, "Callable too large for inline buffer");
        static_assert(alignof(Fn) <= alignof(std::max_align_t), "Alignment too strict");

        new (&storage_) Fn(std::forward<F>(fn));
        call_    = [](void* ptr) { (*static_cast<Fn*>(ptr))(); };
        destroy_ = [](void* ptr) { static_cast<Fn*>(ptr)->~Fn(); };
    }

    MoveOnlyFunction(MoveOnlyFunction&& other) noexcept { moveFrom(std::move(other)); }
    MoveOnlyFunction& operator=(MoveOnlyFunction&& other) noexcept {
        if (this != &other) {
            reset();
            moveFrom(std::move(other));
        }
        return *this;
    }

    MoveOnlyFunction(const MoveOnlyFunction&) = delete;
    MoveOnlyFunction& operator=(const MoveOnlyFunction&) = delete;

    ~MoveOnlyFunction() { reset(); }

    explicit operator bool() const noexcept { return call_ != nullptr; }

    void operator()() {
        call_(std::addressof(storage_));
    }

 private:
    void moveFrom(MoveOnlyFunction&& other) noexcept {
        call_    = other.call_;
        destroy_ = other.destroy_;
        std::memcpy(&storage_, &other.storage_, InlineSize);
        other.call_ = other.destroy_ = nullptr;
    }

    void reset() noexcept {
        if (destroy_) destroy_(std::addressof(storage_));
        call_ = destroy_ = nullptr;
    }

    using Storage = std::aligned_storage_t<InlineSize, alignof(std::max_align_t)>;
    Storage storage_{};
    void (*call_)(void*){nullptr};
    void (*destroy_)(void*){nullptr};
};

} // namespace launcher::core::util 