/*
 * @file expected.h
 * @brief Minimal std::expected substitute (since C++23 lacks it).
 *        Provides Expected<T,E> similar API for success/error propagation.
 */
#pragma once

#include <variant>
#include <utility>

namespace launcher::core::util {

// Simple expected type – either holds T or error E.
// Not fully standards-compliant but sufficient for internal use.
template <typename T, typename E>
class Expected {
 public:
    // Success factory
    static Expected success(T value) { return Expected(std::in_place_index<0>, std::move(value)); }
    // Error factory
    static Expected failure(E error) { return Expected(std::in_place_index<1>, std::move(error)); }

    bool has_value() const { return idx_ == 0; }
    explicit operator bool() const { return has_value(); }

    const T& value() const { return std::get<0>(data_); }
    T&       value()       { return std::get<0>(data_); }

    const E& error() const { return std::get<1>(data_); }
    E&       error()       { return std::get<1>(data_); }

 private:
    template <typename... Args>
    Expected(std::in_place_index_t<0>, Args&&... args) : data_(std::in_place_index<0>, std::forward<Args>(args)...), idx_(0) {}
    template <typename... Args>
    Expected(std::in_place_index_t<1>, Args&&... args) : data_(std::in_place_index<1>, std::forward<Args>(args)...), idx_(1) {}

    std::variant<T,E> data_;
    int idx_;
};

// -------------------- void specialization --------------------

template <typename E>
class Expected<void, E> {
 public:
    using Mono = std::monostate;

    static Expected success() { return Expected(std::in_place_index<0>, Mono{}); }
    static Expected failure(E error) { return Expected(std::in_place_index<1>, std::move(error)); }

    bool has_value() const { return idx_ == 0; }
    explicit operator bool() const { return has_value(); }

    void value() const {}

    const E& error() const { return std::get<1>(data_); }
    E&       error()       { return std::get<1>(data_); }

 private:
    template <typename... Args>
    Expected(std::in_place_index_t<0>, Args&&... args) : data_(std::in_place_index<0>, std::forward<Args>(args)...), idx_(0) {}
    template <typename... Args>
    Expected(std::in_place_index_t<1>, Args&&... args) : data_(std::in_place_index<1>, std::forward<Args>(args)...), idx_(1) {}

    std::variant<Mono,E> data_;
    int idx_;
};

} // namespace launcher::core::util 