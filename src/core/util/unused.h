#pragma once

// -----------------------------------------------------------------------------
// KAI_UNUSED(…)
// -----------------------------------------------------------------------------
// Helper macro to explicitly mark a variable or parameter as intentionally unused
// to silence -Wunused-* diagnostics while still documenting intent.
//
// Usage:
//   void foo(int unused_param) {
//       KAI_UNUSED(unused_param);
//   }
//
// The macro expands to a portable cast-to-void expression, works for both C++ and
// Objective-C/Objective-C++ sources, and is compatible with [[maybe_unused]].
// -----------------------------------------------------------------------------

#ifdef __cplusplus
#define KAI_UNUSED(x) (static_cast<void>(x))
#else
#define KAI_UNUSED(x) ((void)(x))
#endif 