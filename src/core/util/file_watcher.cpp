#include "file_watcher.h"
#include <memory>

#ifdef __APPLE__
#include "macos/file_watcher_macos.h"
#elif defined(_WIN32)
#include "windows/file_watcher_windows.h"
#endif

namespace launcher {
namespace core {

std::shared_ptr<FileWatcher> FileWatcher::create() {
#ifdef __APPLE__
    return std::make_shared<MacOSFileWatcher>();
#elif defined(_WIN32)
    return std::make_shared<WindowsFileWatcher>();
#else
    // Placeholder for other platforms
    // You might return a different implementation or nullptr/throw an exception
    // For now, return nullptr to indicate no implementation for non-Apple platforms
    return nullptr;
#endif
}

}  // namespace core
}  // namespace launcher