#pragma once

/**
 * @file span_guard.h
 * @brief RAII helper that measures wall-clock time for a scope and records the
 *        elapsed micro-seconds into DiagnosticsService counters.
 *
 * The helper is designed for minimal overhead: two steady_clock::now() calls
 * plus a single atomic add in DiagnosticsService. The destructor is marked
 * noexcept so it can be used around critical code without risking exceptions
 * during stack unwinding.
 */

#include <chrono>
#include <string_view>

#include "../diagnostics/diagnostics_service.h"

namespace launcher::core::util {

class SpanGuard {
 public:
    SpanGuard(launcher::core::diagnostics::DiagnosticsService* diag,
              std::string_view                                 counter_name) noexcept
        : diag_(diag), name_(counter_name), start_(std::chrono::steady_clock::now()) {}

    SpanGuard(const SpanGuard&)            = delete;
    SpanGuard& operator=(const SpanGuard&) = delete;

    ~SpanGuard() noexcept {
        if (!diag_) return; // diagnostics not available – nothing to record
        const auto end = std::chrono::steady_clock::now();
        const auto us  = std::chrono::duration_cast<std::chrono::microseconds>(end - start_).count();
        diag_->incrementCounter(name_, static_cast<int64_t>(us));
    }

 private:
    launcher::core::diagnostics::DiagnosticsService* diag_;
    std::string_view                                 name_;
    std::chrono::steady_clock::time_point            start_;
};

}  // namespace launcher::core::util 