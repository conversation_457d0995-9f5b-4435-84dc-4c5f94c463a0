/*
 * result.h – lightweight alias around Expected<T, E> with Ok/Err helpers.
 */
#pragma once

#include "core/util/expected.h"
#include <string>
#include <utility>

namespace launcher::core::util {

// Generic alias usable as util::Result<int>, util::Result<void>, etc.
template <typename T, typename E = std::string>
using Result = Expected<T, E>;

// Factory helpers -----------------------------------------------------------

// Ok(value) – success result
template <typename T>
[[nodiscard]] inline Result<T> Ok(T&& v) {
    return Result<T>::success(std::forward<T>(v));
}

// Err(errorCode, message) – convenience overload when E is HttpError or custom
template <typename E>
[[nodiscard]] inline Result<void, E> Err(E&& e) {
    return Result<void, E>::failure(std::forward<E>(e));
}

} // namespace launcher::core::util

// ---------------------------------------------------------------------------
// Transitional nested namespace `util::result`