#include "file_watcher_macos.h"
#include <CoreServices/CoreServices.h>
#include <Foundation/Foundation.h>
#include <chrono>
#include <filesystem>
#include <iostream>
#include <sstream>
#include "../../util/debug.h"
#include "../../util/result.h"
#include <dispatch/dispatch.h>

namespace launcher {
namespace core {

// -----------------------------------------------------------------------------
// Implementation (PIMPL) struct definition – hides platform-only types
// -----------------------------------------------------------------------------

struct MacOSFileWatcher::Impl {
    std::vector<std::string> watchedDirectories;
    mutable std::mutex mutex;
    std::atomic<bool> isWatching{false};
    FSEventStreamRef eventStream{nullptr};
    FileEventCallback callback;
    bool recursive{true};
    dispatch_queue_t dispatchQueue{nullptr};

    // Strong self‐reference to avoid destruction during async callbacks
    std::shared_ptr<MacOSFileWatcher> selfRetain;
};

MacOSFileWatcher::MacOSFileWatcher() : impl_(std::make_unique<Impl>()) {
}

MacOSFileWatcher::~MacOSFileWatcher() {
    stopWatching();
    // Now safe to release self-reference; no callbacks will be delivered
    impl_->selfRetain.reset();
}

util::Result<void> MacOSFileWatcher::addWatch(const std::string& path, bool recursive) {
    std::lock_guard<std::mutex> lock(impl_->mutex);
    
    // Check if directory exists
    NSFileManager* fileManager = [NSFileManager defaultManager];
    NSString* dirPath = [NSString stringWithUTF8String:path.c_str()];
    BOOL isDir = NO;
    
    if (![fileManager fileExistsAtPath:dirPath isDirectory:&isDir] || !isDir) {
        // Use the renamed error logging function
        ERM(@"Directory does not exist: %@", dirPath);
        return util::Result<void>::failure("Directory does not exist");
    }
    
    // Check if already watching this directory
    auto it = std::find(impl_->watchedDirectories.begin(), impl_->watchedDirectories.end(), path);
    if (it != impl_->watchedDirectories.end()) {
        return util::Result<void>::success(); // Already watching
    }
    
    // Add to watched directories
    impl_->watchedDirectories.push_back(path);
    impl_->recursive = recursive;
    
    // If already watching, restart to include the new directory
    if (impl_->isWatching) {
        stopWatching();
        return startWatching(impl_->callback);
    }
    
    return util::Result<void>::success();
}

util::Result<void> MacOSFileWatcher::removeWatch(const std::string& path) {
    std::lock_guard<std::mutex> lock(impl_->mutex);
    
    auto it = std::find(impl_->watchedDirectories.begin(), impl_->watchedDirectories.end(), path);
    if (it == impl_->watchedDirectories.end()) {
        return util::Result<void>::failure("Path not in watch list");
    }
    
    impl_->watchedDirectories.erase(it);
    
    // If already watching, restart to update the watched directories
    if (impl_->isWatching) {
        stopWatching();
        if (!impl_->watchedDirectories.empty()) {
            return startWatching(impl_->callback);
        }
    }
    
    return util::Result<void>::success();
}

util::Result<void> MacOSFileWatcher::startWatching(FileEventCallback callback) {
    std::lock_guard<std::mutex> lock(impl_->mutex);
    
    if (impl_->isWatching) {
        return util::Result<void>::success(); // Already watching
    }
    
    if (impl_->watchedDirectories.empty()) {
        return util::Result<void>::failure("No directories to watch");
    }
    
    impl_->callback = callback;
    
    // Create array of paths to watch
    CFMutableArrayRef pathsToWatch = CFArrayCreateMutable(nullptr, 0, &kCFTypeArrayCallBacks);
    
    for (const auto& dir : impl_->watchedDirectories) {
        CFStringRef cfPath = CFStringCreateWithCString(nullptr, dir.c_str(), kCFStringEncodingUTF8);
        CFArrayAppendValue(pathsToWatch, cfPath);
        CFRelease(cfPath);
    }
    
    // Create context for callback
    FSEventStreamContext context = {0, this, nullptr, nullptr, nullptr};
    
    // Create the event stream
    FSEventStreamCreateFlags flags = kFSEventStreamCreateFlagFileEvents | kFSEventStreamCreateFlagNoDefer;
    if (!impl_->recursive) {
        flags |= kFSEventStreamCreateFlagWatchRoot;
    }
    
    impl_->eventStream = FSEventStreamCreate(
        nullptr,
        (FSEventStreamCallback)&MacOSFileWatcher::fsEventsCallback,
        &context,
        pathsToWatch,
        kFSEventStreamEventIdSinceNow,
        0.5, // Latency in seconds
        flags
    );
    
    CFRelease(pathsToWatch);
    
    if (!impl_->eventStream) {
        return util::Result<void>::failure("Failed to create FSEventStream");
    }
    
    // Create a dispatch queue for the event stream
    impl_->dispatchQueue = dispatch_queue_create("com.launcher.filewatcher.queue", DISPATCH_QUEUE_SERIAL);
    if (!impl_->dispatchQueue) {
        FSEventStreamInvalidate(impl_->eventStream);
        FSEventStreamRelease(impl_->eventStream);
        impl_->eventStream = nullptr;
        return util::Result<void>::failure("Failed to create dispatch queue");
    }

    // Set the dispatch queue for the event stream
    FSEventStreamSetDispatchQueue(impl_->eventStream, impl_->dispatchQueue);

    // Start the event stream
    if (!FSEventStreamStart(impl_->eventStream)) {
        FSEventStreamInvalidate(impl_->eventStream);
        FSEventStreamRelease(impl_->eventStream);
        impl_->eventStream = nullptr;
        return util::Result<void>::failure("Failed to start FSEventStream");
    }
    
    impl_->isWatching = true;

    // Hold a strong self-reference until stopWatching() completes to prevent
    // the object from being destroyed while callbacks are still in flight.
    impl_->selfRetain = shared_from_this();
    
    return util::Result<void>::success();
}

util::Result<void> MacOSFileWatcher::stopWatching() {
    std::lock_guard<std::mutex> lock(impl_->mutex);
    
    if (!impl_->isWatching) {
        return util::Result<void>::success(); // Not watching
    }
    
    impl_->isWatching = false;
    
    // No auxiliary thread to stop; callbacks run on dispatchQueue_.
    
    // Stop and release the event stream
    if (impl_->eventStream) {
        FSEventStreamStop(impl_->eventStream);
        FSEventStreamInvalidate(impl_->eventStream);
        FSEventStreamRelease(impl_->eventStream);
        impl_->eventStream = nullptr;

        // Ensure all pending callbacks on the dispatch queue have
        // completed before we allow the watcher object to be destroyed.
        // This avoids a use-after-free where the async block still
        // references `this` after it has been deleted.
        if (impl_->dispatchQueue) {
            dispatch_barrier_sync(impl_->dispatchQueue, ^{});
        }
    }
    
    return util::Result<void>::success();
}

bool MacOSFileWatcher::isWatching() const {
    return impl_->isWatching;
}

std::vector<std::string> MacOSFileWatcher::getWatchedDirectories() const {
    std::lock_guard<std::mutex> lock(impl_->mutex);
    return impl_->watchedDirectories;
}

void MacOSFileWatcher::fsEventsCallback(const FSEventStreamRef __unused streamRef,
                                      void* clientCallBackInfo, size_t numEvents,
                                      void* eventPaths, const unsigned int* eventFlags,
                                      const unsigned long long* __unused eventIds) {
    MacOSFileWatcher* watcher = static_cast<MacOSFileWatcher*>(clientCallBackInfo);
    if (!watcher || !watcher->isWatching()) {
        return;
    }

    // According to the FSEvents API, the `eventPaths` parameter is a C array of
    // NUL-terminated UTF-8 strings (char **), unless the stream was created with
    // kFSEventStreamCreateFlagUseCFTypes. We did not request CF types, so we
    // access the C array directly. Treating it as an Objective-C NSArray would
    // lead to undefined behaviour and crashes (see ASan trace).

    char **paths = static_cast<char **>(eventPaths);

    for (size_t i = 0; i < numEvents; ++i) {
        std::string path(paths[i]);
        
        // Determine the event type
        FileEventType eventType = FileEventType::Unknown;
        
        if (eventFlags[i] & kFSEventStreamEventFlagItemCreated) {
            eventType = FileEventType::Created;
        } else if (eventFlags[i] & kFSEventStreamEventFlagItemModified) {
            eventType = FileEventType::Modified;
        } else if (eventFlags[i] & kFSEventStreamEventFlagItemRemoved) {
            eventType = FileEventType::Deleted;
        } else if (eventFlags[i] & kFSEventStreamEventFlagItemRenamed) {
            eventType = FileEventType::Renamed;
        }
        
        // Create the file event
        if (watcher->impl_->callback) {
            watcher->impl_->callback(FileEvent(path, eventType));
        }
    }
}

}  // namespace core
}  // namespace launcher 