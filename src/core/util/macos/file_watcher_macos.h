#pragma once

#include <atomic>
#include <functional>
#include <map>
#include <mutex>
#include <string>
#include <memory>
#include <vector>

#include "../file_watcher.h"
#include "../../util/result.h"

// Forward declarations for Objective-C types
#ifdef __OBJC__
#include <CoreServices/CoreServices.h>
#include <dispatch/dispatch.h>
#else
typedef struct __FSEventStream* FSEventStreamRef;
#endif

namespace launcher {
namespace core {

class MacOSFileWatcher : public FileWatcher, public std::enable_shared_from_this<MacOSFileWatcher> {
 public:
    MacOSFileWatcher();
    ~MacOSFileWatcher() override;

    util::Result<void> addWatch(const std::string& path, bool recursive = true) override;
    util::Result<void> removeWatch(const std::string& path) override;
    util::Result<void> startWatching(FileEventCallback callback) override;
    util::Result<void> stopWatching() override;
    bool isWatching() const override;
    std::vector<std::string> getWatchedDirectories() const override;

 private:
    // FSEvents callback function
    static void fsEventsCallback(const FSEventStreamRef streamRef, void* clientCallBackInfo,
                                 size_t numEvents, void* eventPaths, const unsigned int* eventFlags,
                                 const unsigned long long* eventIds);

    // -----------------------------------------------------------------
    //  All implementation details, including FSEvents handles and state,
    //  are hidden behind an opaque Impl struct to avoid exposing Apple
    //  framework types (FSEventStreamRef) in the public
    //  header.  This keeps cross-platform compilation clean and follows
    //  the same PIMPL approach used for HttpClient.
    // -----------------------------------------------------------------

    struct Impl;
    std::unique_ptr<Impl> impl_;
};

}  // namespace core
}  // namespace launcher