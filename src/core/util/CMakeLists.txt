# Util module CMakeLists.txt

# Modern split implementation below replaces the legacy mechanism that
# injected util sources directly into the monolithic `core` target.

# Util module split into its own static library so edits
# only rebuild & relink the affected units.
# -----------------------------------------------------------------------------
# OBJECT library that owns the compilation units (reusable by other static libs)
add_library(core_util_obj OBJECT
    file_watcher.cpp
    json_writer.cpp
    $<$<BOOL:${APPLE}>:${CMAKE_CURRENT_SOURCE_DIR}/macos/file_watcher_macos.mm>
)

# Mark Objective-C++ source with correct flags on macOS.
if(APPLE)
    set_source_files_properties(
        macos/file_watcher_macos.mm
        PROPERTIES
            COMPILE_FLAGS "-x objective-c++"
            SKIP_PRECOMPILE_HEADERS ON
    )
endif()

# Header-only files live next to sources – export include dir to consumers.
target_include_directories(core_util_obj
    PUBLIC ${CMAKE_CURRENT_SOURCE_DIR}
)

# -----------------------------------------------------------------------------
# Thin STATIC façade that downstream targets link against.  Keeps behaviour
# identical to pre-split build (archive with proper usage-requirements).
add_library(core_util STATIC)
# Link object files into the archive
target_link_libraries(core_util PUBLIC core_util_obj yyjson)

# Propagate platform frameworks required by util implementation (file watcher).
if(APPLE)
    find_library(CORE_SERVICES CoreServices REQUIRED)
    find_library(FOUNDATION Foundation REQUIRED)
    target_link_libraries(core_util PUBLIC ${CORE_SERVICES} ${FOUNDATION})
endif() 