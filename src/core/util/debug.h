/**
 * @file debug.h
 * @brief Debug utilities for conditional logging
 */

#ifndef LAUNCHER_DEBUG_H
#define LAUNCHER_DEBUG_H

#include <filesystem>
// #include <iostream> // No longer strictly needed by this header if fmt is used everywhere
// #include <sstream> // No longer strictly needed by this header if fmt is used everywhere
#include <string>
#include <vector>
#include <source_location>
#include "time_utils.h"
#ifndef FMT_HEADER_ONLY
#define FMT_HEADER_ONLY 1
#endif
#include <fmt/core.h> // Ensure fmt/core.h is included for fmt::format
#include <fmt/format.h> // Ensure full fmt/format.h for fmt::memory_buffer and format_to
#include <fmt/format.h> // Ensure full fmt/format.h for fmt::memory_buffer and format_to

// Import Foundation framework for Objective-C/Objective-C++ support
#ifdef __OBJC__
#import <Foundation/Foundation.h>
#endif

#ifndef LAUNCHER_ASYNC_LOG
#define LAUNCHER_ASYNC_LOG 1
#endif

#if LAUNCHER_ASYNC_LOG
#include "async_logger.h"
#endif

/**
 * Console color codes for terminal output
 */
struct ConsoleColor {
    static const std::string kReset;
    static const std::string kRed;
    static const std::string kGreen;
    static const std::string kYellow;
    static const std::string kBlue;
    static const std::string kMagenta;
    static const std::string kCyan;
    static const std::string kWhite;
    static const std::string kBold;
    
    // Bright/high-intensity colors
    static const std::string kBrightRed;
    static const std::string kBrightGreen;
    static const std::string kBrightYellow;
    static const std::string kBrightBlue;
    static const std::string kBrightMagenta;
    static const std::string kBrightCyan;

    // Color configurations for different log types
    static const std::string kDebugColor;
    static const std::string kErrorColor;
    static const std::string kInfoColor;
    static const std::string kWarnColor;

    // New color palette for file paths
    static const std::vector<std::string>& getFilePathColors();

    // Color utility functions
    static bool& useColors();
    static void enableColors(bool enable);
    static std::string colorize(const std::string& text, const std::string& color);
    
    // RGB color support
    static std::string rgb(uint8_t r, uint8_t g, uint8_t b);
    static std::string generateColorFromHash(size_t hash);
    static std::string getFilePathColor(const char* file_path);
};

// Implementation of static members
inline const std::string ConsoleColor::kReset = "\033[0m";
inline const std::string ConsoleColor::kRed = "\033[31m";
inline const std::string ConsoleColor::kGreen = "\033[32m";
inline const std::string ConsoleColor::kYellow = "\033[33m";
inline const std::string ConsoleColor::kBlue = "\033[34m";
inline const std::string ConsoleColor::kMagenta = "\033[35m";
inline const std::string ConsoleColor::kCyan = "\033[36m";
inline const std::string ConsoleColor::kWhite = "\033[37m";
inline const std::string ConsoleColor::kBold = "\033[1m";

// Bright/high-intensity colors
inline const std::string ConsoleColor::kBrightRed = "\033[91m";
inline const std::string ConsoleColor::kBrightGreen = "\033[92m";
inline const std::string ConsoleColor::kBrightYellow = "\033[93m";
inline const std::string ConsoleColor::kBrightBlue = "\033[94m";
inline const std::string ConsoleColor::kBrightMagenta = "\033[95m";
inline const std::string ConsoleColor::kBrightCyan = "\033[96m";

inline const std::string ConsoleColor::kDebugColor = ConsoleColor::kCyan;
inline const std::string ConsoleColor::kErrorColor = ConsoleColor::kRed + ConsoleColor::kBold;
inline const std::string ConsoleColor::kInfoColor = ConsoleColor::kGreen;
inline const std::string ConsoleColor::kWarnColor = ConsoleColor::kYellow;

// Define the file path color palette (without white)
inline const std::vector<std::string>& ConsoleColor::getFilePathColors() {
    static const std::vector<std::string> colors = {
        kYellow, kBlue, kMagenta, kCyan, kBrightRed, kBrightGreen, 
        kBrightYellow, kBrightBlue, kBrightMagenta, kBrightCyan,
        rgb(255, 130, 0),   // Orange
        rgb(180, 90, 210),  // Purple
        rgb(0, 180, 170),   // Teal
        rgb(220, 160, 0),   // Gold
        rgb(100, 180, 0),   // Lime
        rgb(0, 140, 220)    // Sky Blue
    };
    return colors;
}

inline bool& ConsoleColor::useColors() {
    static bool enabled = true;
    return enabled;
}

inline void ConsoleColor::enableColors(bool enable) {
    useColors() = enable;
}

inline std::string ConsoleColor::colorize(const std::string& text, const std::string& color) {
    if (!useColors()) {
        return text;
    }
    return color + text + kReset;
}

// Generate 24-bit RGB color code for terminals
inline std::string ConsoleColor::rgb(uint8_t r, uint8_t g, uint8_t b) {
    return fmt::format("\033[38;2;{};{};{}m", static_cast<int>(r), static_cast<int>(g), static_cast<int>(b));
}

inline std::string ConsoleColor::generateColorFromHash(size_t hash) {
    // Use the hash to create vibrant colors by ensuring at least one channel is high (>180)
    uint8_t r = static_cast<uint8_t>((hash & 0xFF) | (hash % 2 ? 0x80 : 0));
    uint8_t g = static_cast<uint8_t>(((hash >> 8) & 0xFF) | (hash % 3 ? 0x80 : 0));
    uint8_t b = static_cast<uint8_t>(((hash >> 16) & 0xFF) | (hash % 5 ? 0x80 : 0));
    
    // Ensure we never generate a white-ish or too dark color
    if (r > 200 && g > 200 && b > 200) {
        r = static_cast<uint8_t>(r * 0.7);
    }
    if (r < 60 && g < 60 && b < 60) {
        r = static_cast<uint8_t>(r + 60);
        g = static_cast<uint8_t>(g + 60);
        b = static_cast<uint8_t>(b + 60);
    }
    
    return rgb(r, g, b);
}

// Helper function to get a color based on file path hash
inline std::string ConsoleColor::getFilePathColor(const char* file_path) {
    if (!useColors()) {
        return ""; // Return empty if colors are disabled
    }
    
    // For non-empty paths, generate a color based on the file path
    std::string_view path_view(file_path);
    if (!path_view.empty()) {
        // Use different color generation strategies based on terminal support
        static bool use_rgb_colors = true; // Set to false if 24-bit colors aren't supported
        
        if (use_rgb_colors) {
            // Use the hash of the file path to generate a unique color
            size_t path_hash = std::hash<std::string_view>{}(path_view);
            return generateColorFromHash(path_hash);
        } else {
            // Fallback to the standard color palette for terminals without 24-bit color support
            const auto& colors = getFilePathColors();
            if (!colors.empty()) {
                size_t path_hash = std::hash<std::string_view>{}(path_view);
                return colors[path_hash % colors.size()];
            }
        }
    }
    
    return kCyan; // Default color if we can't generate one
}

#ifndef LAUNCHER_MIN_LOG_LEVEL
#ifdef NDEBUG
#define LAUNCHER_MIN_LOG_LEVEL 1  // Release: only ERR and above
#else
#define LAUNCHER_MIN_LOG_LEVEL 0  // Debug: ERR, WRN, INF enabled; DBG handled separately
#endif
#endif

namespace detail {
inline void logInternal(const char* level_tag,
                        const std::string& level_color,
                        std::string_view msg_sv, // Changed to string_view
                        const std::source_location& loc = std::source_location::current()) {
#if LAUNCHER_ASYNC_LOG
    std::string line;
    std::string msg(msg_sv); // AsyncLogger expects std::string
    line.reserve(128 + msg.size());
    line += ConsoleColor::colorize(time_utils::currentTimeMsString(), ConsoleColor::kWhite);
    line += " ";
    line += ConsoleColor::colorize(level_tag, level_color);
    line += " ";
    line += ConsoleColor::colorize(std::filesystem::path(loc.file_name()).filename().string() + ":" + std::to_string(loc.line()),
                                   ConsoleColor::getFilePathColor(loc.file_name()));
    line += " ";
    line += msg;
    ::launcher::core::util::AsyncLogger::instance().enqueue(std::move(line));
#else
    const std::string timestamp = time_utils::currentTimeMsString();
    std::string file_loc = std::filesystem::path(loc.file_name()).filename().string() + ":" +
                           std::to_string(loc.line());
    std::ostream& stream = (level_tag[0] == 'E' || level_tag[0] == 'W') ? std::cerr : std::cout;
    stream << ConsoleColor::colorize(timestamp, ConsoleColor::kWhite) << " "
           << ConsoleColor::colorize(level_tag, level_color) << " "
           << ConsoleColor::colorize(file_loc, ConsoleColor::getFilePathColor(loc.file_name()))
           << " " << msg_sv << std::endl; // Use msg_sv directly with stream
#endif
}
} // namespace detail

// ==================================================================
// C++ Logging Macros (using stream insertion)
// ==================================================================

/**
 * Debug logging macro (C++ Version)
 * Only active in debug builds (!NDEBUG).
 * Format: DBG [file:line] Message
 */
#ifndef NDEBUG
#undef DBG
#define DBG(...) \
    do { \
        if constexpr(LAUNCHER_MIN_LOG_LEVEL <= 0) { \
            std::stringstream _ss; \
            (void)(_ss << __VA_ARGS__); \
            detail::logInternal("DBG", ConsoleColor::kDebugColor, _ss.str()); \
        } \
    } while (0)
#else
#undef DBG
#define DBG(...) do {} while (0)
#endif

/**
 * Error logging macro (C++ Version)
 * Format: ERR [file:line] Message
 */
#undef ERR
#define ERR(...) \
    do { \
        std::stringstream _ss; \
        (void)(_ss << __VA_ARGS__); \
        detail::logInternal("ERR", ConsoleColor::kErrorColor, _ss.str()); \
    } while (0)

#if LAUNCHER_MIN_LOG_LEVEL <= 2
/**
 * Warning logging macro (C++ Version)
 * Format: WRN [file:line] Message
 */
#undef WRN
#define WRN(...) \
    do { \
        std::stringstream _ss; \
        (void)(_ss << __VA_ARGS__); \
        detail::logInternal("WRN", ConsoleColor::kWarnColor, _ss.str()); \
    } while (0)
#else
#undef WRN
#define WRN(...) do {} while (0)
#endif

#if LAUNCHER_MIN_LOG_LEVEL <= 3
/**
 * Info logging macro (C++ Version)
 * Format: INF [file:line] Message
 */
#undef INF
#define INF(...) \
    do { \
        std::stringstream _ss; \
        (void)(_ss << __VA_ARGS__); \
        detail::logInternal("INF", ConsoleColor::kInfoColor, _ss.str()); \
    } while (0)
#else
#undef INF
#define INF(...) do {} while (0)
#endif

// ==================================================================
// Objective-C++ Specific Macros (_MM) using NSLog
// ==================================================================

#ifdef __OBJC__
/**
 * Error logging macro for Objective-C++ files (.mm)
 * Format: ERROR [file:line] Message
 */
#define ERM(fmt, ...)                                                                        \
    do {                                                                                     \
        NSString* _msg = [NSString stringWithFormat:fmt __VA_OPT__(,) __VA_ARGS__];          \
        detail::logInternal("ERR", ConsoleColor::kErrorColor, [_msg UTF8String]);           \
    } while(0)

/**
 * Warning logging macro for Objective-C++ files (.mm)
 * Format: WARN [file:line] Message
 */
#if LAUNCHER_MIN_LOG_LEVEL <= 2
#define WRM(fmt, ...)                                                                       \
    do {                                                                                     \
        NSString* _msg = [NSString stringWithFormat:fmt __VA_OPT__(,) __VA_ARGS__];          \
        detail::logInternal("WRN", ConsoleColor::kWarnColor, [_msg UTF8String]);            \
    } while(0)
#else
#define WRM(fmt, ...) do {} while (0)
#endif

/**
 * Info logging macro for Objective-C++ files (.mm)
 * Format: INFO [file:line] Message
 */
#if LAUNCHER_MIN_LOG_LEVEL <= 3
#define INM(fmt, ...)                                                                        \
    do {                                                                                     \
        NSString* _msg = [NSString stringWithFormat:fmt __VA_OPT__(,) __VA_ARGS__];          \
        detail::logInternal("INF", ConsoleColor::kInfoColor, [_msg UTF8String]);            \
    } while(0)
#else
#define INM(fmt, ...) do {} while (0)
#endif

/**
 * Debug logging macro for Objective-C++ files (.mm)
 * Only outputs in debug builds
 * Format: DEBUG [file:line] Message
 */
#ifndef NDEBUG
#define DBM(fmt, ...)                                                                      \
    do {                                                                                     \
        if constexpr(LAUNCHER_MIN_LOG_LEVEL <= 0) {                                           \
            NSString* _msg = [NSString stringWithFormat:fmt __VA_OPT__(,) __VA_ARGS__];        \
            detail::logInternal("DBG", ConsoleColor::kDebugColor, [_msg UTF8String]);        \
        }                                                                                    \
    } while(0)
#else
#define DBM(fmt, ...) do {} while (0)
#endif // NDEBUG

#else // __OBJC__ not defined

// Fallbacks to C++ macros when not in Objective-C++ context
#define ERM(fmt, ...) ERR(fmt, ##__VA_ARGS__)
#define WRM(fmt, ...) WRN(fmt, ##__VA_ARGS__)
#define INM(fmt, ...) INF(fmt, ##__VA_ARGS__)
#define DBM(fmt, ...) DBG(fmt, ##__VA_ARGS__)

#endif // __OBJC__

// ===============================================================
// fmt-style ultra-low-latency macros (recommended)
// Usage: ERRF("failed id={} rc={}", id, rc);
// ===============================================================
#ifndef NDEBUG
#define DBG_F(fmt_str, ...)                                                                                 \
    do {                                                                                                   \
        if constexpr(LAUNCHER_MIN_LOG_LEVEL <= 0) {                                                        \
            fmt::memory_buffer _buf;                                                                       \
            fmt::format_to(std::back_inserter(_buf), fmt::runtime(fmt_str) __VA_OPT__(,) __VA_ARGS__);      \
            detail::logInternal("DBG", ConsoleColor::kDebugColor,                                         \
                                 std::string(_buf.data(), _buf.size()));                                   \
        }                                                                                                  \
    } while (0)
#else
#define DBG_F(fmt_str, ...) do {} while (0)
#endif

#define ERR_F(fmt_str, ...)                                                                                 \
    do {                                                                                                   \
        fmt::memory_buffer _buf;                                                                           \
        fmt::format_to(std::back_inserter(_buf), fmt::runtime(fmt_str) __VA_OPT__(,) __VA_ARGS__);          \
        detail::logInternal("ERR", ConsoleColor::kErrorColor,                                            \
                             std::string(_buf.data(), _buf.size()));                                       \
    } while (0)

#if LAUNCHER_MIN_LOG_LEVEL <= 2
#define WRN_F(fmt_str, ...)                                                                                 \
    do {                                                                                                   \
        fmt::memory_buffer _buf;                                                                           \
        fmt::format_to(std::back_inserter(_buf), fmt::runtime(fmt_str) __VA_OPT__(,) __VA_ARGS__);          \
        detail::logInternal("WRN", ConsoleColor::kWarnColor,                                             \
                             std::string(_buf.data(), _buf.size()));                                       \
    } while (0)
#else
#define WRN_F(fmt_str, ...) do {} while (0)
#endif

#if LAUNCHER_MIN_LOG_LEVEL <= 3
#define INF_F(fmt_str, ...)                                                                                 \
    do {                                                                                                   \
        fmt::memory_buffer _buf;                                                                           \
        fmt::format_to(std::back_inserter(_buf), fmt::runtime(fmt_str) __VA_OPT__(,) __VA_ARGS__);          \
        detail::logInternal("INF", ConsoleColor::kInfoColor,                                             \
                             std::string(_buf.data(), _buf.size()));                                       \
    } while (0)
#else
#define INF_F(fmt_str, ...) do {} while (0)
#endif

#endif // LAUNCHER_DEBUG_H