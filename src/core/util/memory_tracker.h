#pragma once

#ifndef ENABLE_MEMORY_TRACKING
#define ENABLE_MEMORY_TRACKING 1
#endif

#if ENABLE_MEMORY_TRACKING

#include <cstddef>
#include <cstdint>
#include <mutex>
#include <string>
#include <unordered_map>
#include <vector>
#include <cassert>

namespace launcher::core {

class MemoryTracker;
MemoryTracker& TRACKER();

/**
 * @brief A memory usage tracker
 *
 * This class provides a way to track memory usage in the application.
 * It can be used to monitor memory allocations and deallocations,
 * and to detect memory leaks.
 */
class MemoryTracker {
 public:
    /**
     * @brief Record a memory allocation
     *
     * @param size The size of the allocation in bytes
     * @param tag An optional tag to categorize the allocation
     * @return void* The pointer to the allocated memory
     */
    void* recordAllocation(size_t size, const std::string& tag = "default") {
        std::lock_guard<std::mutex> lock(mutex_);

        void* ptr = ::operator new(size);
        allocations_[ptr] = {size, tag};

        // Update statistics
        totalAllocated_ += size;
        currentUsage_ += size;

        // Update tag statistics
        tagStats_[tag].currentUsage += size;
        tagStats_[tag].totalAllocated += size;
        tagStats_[tag].allocationCount++;

        if (currentUsage_ > peakUsage_) {
            peakUsage_ = currentUsage_;
        }

        return ptr;
    }

    /**
     * @brief Record a memory allocation with an existing pointer
     *
     * @param ptr The pointer to the allocated memory
     * @param size The size of the allocation in bytes
     * @param tag An optional tag to categorize the allocation
     */
    void recordAllocation(void* ptr, size_t size, const std::string& tag = "default") {
        std::lock_guard<std::mutex> lock(mutex_);

        allocations_[ptr] = {size, tag};

        // Update statistics
        totalAllocated_ += size;
        currentUsage_ += size;

        // Update tag statistics
        tagStats_[tag].currentUsage += size;
        tagStats_[tag].totalAllocated += size;
        tagStats_[tag].allocationCount++;

        if (currentUsage_ > peakUsage_) {
            peakUsage_ = currentUsage_;
        }
    }

    /**
     * @brief Record a memory deallocation
     *
     * @param ptr The pointer to the memory to deallocate
     */
    void recordDeallocation(void* ptr) {
        if (ptr == nullptr) {
            return;
        }

        std::lock_guard<std::mutex> lock(mutex_);

        auto it = allocations_.find(ptr);
        if (it != allocations_.end()) {
            const auto& alloc = it->second;

            // Update statistics
            currentUsage_ -= alloc.size;

            // Update tag statistics
            tagStats_[alloc.tag].currentUsage -= alloc.size;
            tagStats_[alloc.tag].deallocationCount++;

            // Remove the allocation from the map
            allocations_.erase(it);

            // Free the memory that was allocated via recordAllocation
            ::operator delete(ptr);
            return;
        }
        // If the pointer is not found, it might be a standard delete operation
        // for a pointer allocated with the custom new operator. In this case
        // the memory has already been released, so we simply ignore it.
    }

    /**
     * @brief Get the current memory usage in bytes
     *
     * @return size_t The current memory usage
     */
    size_t getCurrentUsage() const {
        std::lock_guard<std::mutex> lock(mutex_);
        return currentUsage_;
    }

    /**
     * @brief Get the peak memory usage in bytes
     *
     * @return size_t The peak memory usage
     */
    size_t getPeakUsage() const {
        std::lock_guard<std::mutex> lock(mutex_);
        return peakUsage_;
    }

    /**
     * @brief Get the total allocated memory in bytes
     *
     * @return size_t The total allocated memory
     */
    size_t getTotalAllocated() const {
        std::lock_guard<std::mutex> lock(mutex_);
        return totalAllocated_;
    }

    /**
     * @brief Get the number of active allocations
     *
     * @return size_t The number of active allocations
     */
    size_t getAllocationCount() const {
        std::lock_guard<std::mutex> lock(mutex_);
        return allocations_.size();
    }

    /**
     * @brief Get the memory usage for a specific tag
     *
     * @param tag The tag to get the usage for
     * @return size_t The memory usage for the tag
     */
    size_t getTagUsage(const std::string& tag) const {
        std::lock_guard<std::mutex> lock(mutex_);
        auto it = tagStats_.find(tag);
        if (it != tagStats_.end()) {
            return it->second.currentUsage;
        }
        return 0;
    }

    /**
     * @brief Get a list of all active allocations
     *
     * @return std::vector<std::pair<void*, size_t>> A list of (pointer, size) pairs
     */
    std::vector<std::pair<void*, size_t>> getActiveAllocations() const {
        std::lock_guard<std::mutex> lock(mutex_);
        std::vector<std::pair<void*, size_t>> result;
        result.reserve(allocations_.size());

        for (const auto& alloc : allocations_) {
            result.emplace_back(alloc.first, alloc.second.size);
        }

        return result;
    }

    /**
     * @brief Reset the tracker
     */
    void reset() {
        std::lock_guard<std::mutex> lock(mutex_);

        // Deallocate all memory
        for (const auto& alloc : allocations_) {
            ::operator delete(alloc.first);
        }

        // Reset statistics
        allocations_.clear();
        tagStats_.clear();
        currentUsage_ = 0;
        peakUsage_ = 0;
        totalAllocated_ = 0;
    }

    // Default constructor and destructor (public) so external code can create instances
    MemoryTracker() = default;
    ~MemoryTracker() { reset(); }

    // Prevent copying and assignment
    MemoryTracker(const MemoryTracker&) = delete;
    MemoryTracker& operator=(const MemoryTracker&) = delete;

 private:
    /**
     * @brief Allocation information
     */
    struct AllocationInfo {
        size_t size;      ///< Size of the allocation in bytes
        std::string tag;  ///< Tag for the allocation
    };

    /**
     * @brief Tag statistics
     */
    struct TagStats {
        size_t currentUsage = 0;       ///< Current memory usage for the tag
        size_t totalAllocated = 0;     ///< Total memory allocated for the tag
        size_t allocationCount = 0;    ///< Number of allocations for the tag
        size_t deallocationCount = 0;  ///< Number of deallocations for the tag
    };

 private:
    std::unordered_map<void*, AllocationInfo> allocations_;  ///< Map of active allocations
    std::unordered_map<std::string, TagStats> tagStats_;     ///< Statistics for each tag
    size_t currentUsage_ = 0;                                ///< Current memory usage in bytes
    size_t peakUsage_ = 0;                                   ///< Peak memory usage in bytes
    size_t totalAllocated_ = 0;                              ///< Total allocated memory in bytes
    mutable std::mutex mutex_;                               ///< Mutex for thread safety
};

} // namespace launcher::core

// Placement new/delete wrappers delegating to memory tracker
inline void* operator new(size_t size, const std::string& tag) {
    void* ptr = ::operator new(size);
    launcher::core::TRACKER().recordAllocation(ptr, size, tag);
    return ptr;
}

inline void operator delete(void* ptr, const std::string&) noexcept {
    launcher::core::TRACKER().recordDeallocation(ptr);
    ::operator delete(ptr);
}

#endif // ENABLE_MEMORY_TRACKING