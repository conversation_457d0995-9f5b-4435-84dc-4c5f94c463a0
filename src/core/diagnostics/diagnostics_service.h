#pragma once

#include <atomic>
#include <array>
#include <chrono>
#include <mutex>
#include <string>
#include <string_view>
#include <thread>
#include <unordered_map>
#include <condition_variable>

#include "../foundation/iservice.h"
#include "../foundation/service_base.h"
#include "../util/result.h"
#include "../util/debug.h"
#include "../async/executor_service.h"
#include "../memory/arena_allocator_service.h"
#include "../foundation/registry.h"

#include <nlohmann/json.hpp>

namespace launcher::core::events { class EventBusService; }

// ---------------------------------------------------------------------------
// Lightweight alias for the JSON document used throughout Kai.
// ---------------------------------------------------------------------------
namespace kai {
namespace json {
using Document = nlohmann::json;
}  // namespace json
}  // namespace kai

namespace launcher::core::diagnostics {

/**
 * @class DiagnosticsService
 * @brief Collects counters & gauges from across the process and periodically
 *        logs a JSON snapshot via DBG().
 *
 * Thread-safe – incrementCounter / setGauge may be called from any thread.
 */
class DiagnosticsService final
    : public foundation::ServiceBase<DiagnosticsService,
                                     launcher::core::async::ExecutorService,
                                     launcher::core::events::EventBusService,
                                     memory::ArenaAllocatorSvc> {
 public:
    static constexpr foundation::ServiceId kId = foundation::ServiceId::kDiagnosticsService;

    explicit DiagnosticsService(foundation::ServiceRegistry& registry);
    ~DiagnosticsService() override;

    // IService -------------------------------------------------------------
    // id() from ServiceBase
    util::Result<void> start() override;
    void             stop() noexcept override;

    // Public metric APIs ---------------------------------------------------
    util::Result<void> incrementCounter(std::string_view name, int64_t delta = 1) noexcept;
    util::Result<void> setGauge(std::string_view name, int64_t value) noexcept;

    [[nodiscard]] kai::json::Document snapshot() const;

    // Dependency pointers fetched from ServiceBase; no attach API needed.
    launcher::core::async::ExecutorService*           executor_{nullptr};
    events::EventBusService*          eventBus_{nullptr};
    memory::ArenaAllocatorSvc*        arenaSvc_{nullptr};

    foundation::ServiceRegistry& registry_;

 private:
    // Internal copyable atomic wrapper so it can live inside std::unordered_map.
    struct AtomicI64 {
        std::atomic<int64_t> v{0};
        AtomicI64() = default;
        explicit AtomicI64(int64_t init) : v(init) {}
        AtomicI64(const AtomicI64& other) : v(other.v.load(std::memory_order_relaxed)) {}
        AtomicI64& operator=(const AtomicI64& other) {
            v.store(other.v.load(std::memory_order_relaxed), std::memory_order_relaxed);
            return *this;
        }
    };

    using MetricMap = std::unordered_map<std::string, AtomicI64>;

    static constexpr std::size_t kShardCount = 8;

    struct Shard {
        mutable std::mutex mtx;
        MetricMap counters;
        MetricMap gauges;
    };

    std::array<Shard, kShardCount> shards_;

    std::atomic<bool> running_{false};

    // --- New: interruptible sleep state ---------------------------------
    mutable std::mutex            sleep_mtx_;
    std::condition_variable       sleep_cv_;
    // --------------------------------------------------------------------

    // Recurring emission ---------------------------------------------------
    void collectMetricsOnce();
    void emitLoop();

#ifdef TESTING
 public:
    // For unit-tests: force a synchronous metrics collection without rescheduling.
    void flushForTest() { collectMetricsOnce(); }
#endif
};

}  // namespace launcher::core::diagnostics 