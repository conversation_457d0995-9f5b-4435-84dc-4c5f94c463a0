// -----------------------------------------------------------------------------
// @file metrics_exporter.hh
// @brief Generic compile-time exporter that iterates over a tuple of MetricSource
//        pointers/references and forwards each Stats struct to the provided
//        callable.  Zero virtual dispatch – pure header-only utility.
// -----------------------------------------------------------------------------
#pragma once

#include <tuple>
#include <type_traits>
#include <utility>
#include <initializer_list>

#include "core/util/metrics.hh"

namespace launcher::core::diagnostics {

// -------------------------------------------------------------------------
// export_metrics – visit each MetricSource in the tuple and call `emit(stats)`.
// The tuple is expected to hold *pointers* or *references* to concrete
// MetricSource instances that outlive the call.
//
// Example usage:
//   auto tup = std::make_tuple(&cache, &queue);
//   export_metrics(tup, [](auto& src_stats){ /* ... */ });
// -------------------------------------------------------------------------

template <typename Tuple, typename EmitFn>
inline void export_metrics(const Tuple& sources, EmitFn&& emit) {
    constexpr std::size_t N = std::tuple_size_v<Tuple>;

    // Internal helper lambda using index sequence expansion.
    [&]<std::size_t... Is>(std::index_sequence<Is...>) {
        // Fold-expression to invoke emit() on every stats() result.
        (void(std::initializer_list<int>{
            ( ( [&]() {
                    // Resolve pointer/reference to concrete object
                    auto&& raw_elem = std::get<Is>(sources);
                    using ElemT = std::remove_reference_t<decltype(raw_elem)>;
                    // Dereference pointer if needed
                    auto&& obj_ref = [&]() -> decltype(auto) {
                        if constexpr (std::is_pointer_v<ElemT>) {
                            return *raw_elem;
                        } else {
                            return raw_elem;
                        }
                    }();
                    using SrcT = std::remove_reference_t<decltype(obj_ref)>;
                    static_assert(util::MetricSource<SrcT>,
                                  "All elements in the tuple must satisfy MetricSource");
                    emit.template operator()<SrcT>(obj_ref.stats());
                }() ),
              0 ) ... }));
    }(std::make_index_sequence<N>{});
}

} // namespace launcher::core::diagnostics

// -------------------------------------------------------------------------
// Extra helpers implemented in metrics_exporter.cpp
// -------------------------------------------------------------------------

namespace launcher::core::diagnostics {

template <typename Tuple>
void export_metrics_with_alerts(const Tuple& sources);

// Expose total number of queue alerts emitted since process start.
uint64_t queue_alerts_total();

} // namespace launcher::core::diagnostics 