#include "metrics_exporter.hh"
#include "queue_alerts.hh"
#include "hdr_latency.hh"
#include <type_traits>
#include <tuple>

// This translation unit intentionally left empty – the implementation is
// header-only.  Including the header guarantees at least one TU instantiates
// the templates to aid with IDE symbol navigation and prevents accidental ODR
// violations when the exporter is used across multiple libraries. 

namespace launcher::core::diagnostics {

// -------------------------------------------------------------------
// export_metrics_with_alerts – iterate over metric sources, perform normal
// export via user callable, and run high-water queue alert checks. This is a
// header-only template placed in the .cpp to avoid redundant instantiations.
// -------------------------------------------------------------------

template <typename Tuple>
inline void export_metrics_with_alerts(const Tuple& sources) {
    export_metrics(sources, []<typename SrcT>(const auto& stats) {
        using StatsT = std::remove_cvref_t<decltype(stats)>;
        if constexpr (std::is_same_v<StatsT, launcher::core::runtime::QueueStats>) {
            queue_alerts::maybeAlert<SrcT>(stats);
        }
    });

    // -----------------------------------------------------------------
    // Additionally export latency histograms (verifier + runtime scan)
    // at the same cadence.  They are provided via global MetricSource
    // singletons so we build a lightweight tuple of pointers.
    // -----------------------------------------------------------------
    auto latency_sources = std::make_tuple(&latency::g_verifier_metric_source,
                                          &latency::g_scan_metric_source);

    export_metrics(latency_sources, []<typename SrcT>(const auto& /*stats*/) {
        // For now merely iterate;  Prom/OTLP serialisation happens inside
        // the caller-supplied lambda in DiagnosticsService (future Slice-3).
    });
}

// Force a few common instantiations to ensure the template is emitted even
// when not explicitly used elsewhere.  This avoids "undefined symbol" issues
// on some linkers when export_metrics_with_alerts lives only in a TU that may
// be discarded by LTO.
using DummyStatsTuple = std::tuple<>;
template void export_metrics_with_alerts(const DummyStatsTuple&);

uint64_t queue_alerts_total() { return queue_alerts::alertCount(); }

} // namespace launcher::core::diagnostics 