#pragma once

#include <atomic>
#include <cstdint>
#include <type_traits>
#include <typeinfo>

#include "core/runtime/queue_traits.hh"
#include "core/runtime/mux_queue.hh"
#include "core/util/debug.h"

namespace launcher::core::diagnostics::queue_alerts {

// -------------------------------------------------------------------------
// Trait to detect MuxQueueBackend – special threshold 85 %
// -------------------------------------------------------------------------

template <typename T>
struct is_mux_queue : std::false_type {};

template <typename U, std::size_t Cap, std::size_t Levels>
struct is_mux_queue<launcher::core::runtime::MuxQueueBackend<U, Cap, Levels>> : std::true_type {};

// -------------------------------------------------------------------------
// Global alert counter – updated every time we emit WARN for high-water mark.
// -------------------------------------------------------------------------
extern std::atomic<uint64_t> g_alert_counter;

// -------------------------------------------------------------------------
// maybeAlert<QueueT>(stats)
//   Checks stats.high_water_pct against compile-time threshold and emits
//   WRN when exceeded. Returns true when an alert was emitted.
// -------------------------------------------------------------------------

template <typename QueueT>
inline bool maybeAlert(const launcher::core::runtime::QueueStats& stats) {
    constexpr std::uint32_t kThreshold = is_mux_queue<QueueT>::value ? 85u : 60u;

    if (stats.high_water_pct > kThreshold) {
        WRN_F("Queue {} high-water {}% exceeds threshold {}", typeid(QueueT).name(),
              stats.high_water_pct, kThreshold);
        g_alert_counter.fetch_add(1, std::memory_order_relaxed);
        return true;
    }
    return false;
}

inline uint64_t alertCount() noexcept { return g_alert_counter.load(std::memory_order_relaxed); }

inline void resetAlertCount() noexcept { g_alert_counter.store(0, std::memory_order_relaxed); }

} // namespace launcher::core::diagnostics::queue_alerts 