#include "diagnostics_service.h"
#include "../memory/arena_allocator.h" // arena stats
#include "../events/event_bus_service.h"
#include "core/interfaces/metric_source_api.h"
#include "core/diagnostics/queue_alerts.hh"
#include "core/diagnostics/metrics_exporter.hh"
#include "core/diagnostics/hdr_latency.hh"

#include <utility>

using namespace launcher::core;

namespace launcher::core::diagnostics {

// ----------------------------- ctor / dtor -------------------------------

DiagnosticsService::DiagnosticsService(foundation::ServiceRegistry& registry)
    : foundation::ServiceBase<DiagnosticsService,
                              async::ExecutorService,
                              launcher::core::events::EventBusService,
                              memory::ArenaAllocatorSvc>(registry), registry_{registry} {
    executor_  = &this->template get<async::ExecutorService>();
    eventBus_  = &this->template get<launcher::core::events::EventBusService>();
    arenaSvc_  = &this->template get<memory::ArenaAllocatorSvc>();
}

DiagnosticsService::~DiagnosticsService() {
    stop();
}

// -------------------------------- start ----------------------------------

util::Result<void> DiagnosticsService::start() {
    if (running_.exchange(true, std::memory_order_acq_rel)) {
        return util::Result<void>::success(); // already running
    }

    // Bootstrap first emission via the executor. This keeps the dedicated pool
    // thread budget flat (no extra thread owned by DiagnosticsService).
    executor_->submit([this]() { this->emitLoop(); });
    DBG("DiagnosticsService started – periodic snapshot every 1 s");
    return util::Result<void>::success();
}

// -------------------------------- stop -----------------------------------

void DiagnosticsService::stop() noexcept {
    if (!running_.exchange(false, std::memory_order_acq_rel)) {
        return; // not running
    }

    // Wake any sleeping emitLoop task so shutdown proceeds immediately.
    {
        std::lock_guard<std::mutex> lk(sleep_mtx_);
    }
    sleep_cv_.notify_all();

    // Prevent further pointer dereference after dependent services stop.
    eventBus_ = nullptr;

    DBG("DiagnosticsService stopped");
}

// ---------------------- public metric interfaces -------------------------

util::Result<void> DiagnosticsService::incrementCounter(std::string_view name_sv, int64_t delta) noexcept {
    const std::size_t shard_idx = std::hash<std::string_view>{}(name_sv) % kShardCount;
    Shard& shard = shards_[shard_idx];

    std::scoped_lock lk(shard.mtx);
    std::string name_str(name_sv); // MetricMap uses std::string key
    auto& cell = shard.counters[name_str];
    cell.v.fetch_add(delta, std::memory_order_relaxed);
    return util::Result<void>::success();
}

util::Result<void> DiagnosticsService::setGauge(std::string_view name_sv, int64_t value) noexcept {
    const std::size_t shard_idx = std::hash<std::string_view>{}(name_sv) % kShardCount;
    Shard& shard = shards_[shard_idx];

    std::scoped_lock lk(shard.mtx);
    std::string name_str(name_sv); // MetricMap uses std::string key
    auto& cell = shard.gauges[name_str];
    cell.v.store(value, std::memory_order_relaxed);
    return util::Result<void>::success();
}

// -------------------------------- snapshot -------------------------------

kai::json::Document DiagnosticsService::snapshot() const {
    kai::json::Document doc;
    auto& jc = doc["counters"] = kai::json::Document::object();
    auto& jg = doc["gauges"]   = kai::json::Document::object();
    auto& jp = doc["plugins"]  = kai::json::Document::object();

    for (const auto& shard : shards_) {
        std::scoped_lock lk(shard.mtx);
        for (const auto& [k, cell] : shard.counters) {
            const auto val = cell.v.load(std::memory_order_relaxed);
            jc[k] = val;
            if (k.rfind("plugins.", 0) == 0) {
                jp[k.substr(8)] = val;
            }
        }
        for (const auto& [k, cell] : shard.gauges) {
            jg[k] = cell.v.load(std::memory_order_relaxed);
        }
    }
    return doc;
}

// ------------------------------ emitLoop ---------------------------------

void DiagnosticsService::emitLoop() {
    collectMetricsOnce();

    // Reschedule after 1 second (if still running). Use a condition_variable
    // so stop() can interrupt the delay instead of blocking shutdown.
    if (running_.load(std::memory_order_acquire)) {
        executor_->submit([this]() {
            std::unique_lock<std::mutex> lk(sleep_mtx_);
            // Wait for either the 1-second period or a stop() notification.
            bool cancelled = sleep_cv_.wait_for(lk, std::chrono::seconds(1),
                                                [this] { return !running_.load(std::memory_order_acquire); });
            lk.unlock();
            if (!cancelled && running_.load(std::memory_order_acquire)) {
                this->emitLoop();
            }
        });
    }
}

// ------------------------ collectMetricsOnce ------------------------------

void DiagnosticsService::collectMetricsOnce() {
    if (!running_.load(std::memory_order_acquire)) {
        return;
    }

    // 1. Refresh allocator stats gauges -----------------------------------
    using launcher::core::memory::ArenaStats;
    using launcher::core::memory::getStats;
    const ArenaStats st = getStats();
    this->setGauge("arena.reserved",  static_cast<int64_t>(st.reserved));
    this->setGauge("arena.committed", static_cast<int64_t>(st.committed));
    this->setGauge("arena.allocated", static_cast<int64_t>(st.allocated));
    this->setGauge("arena.allocs",    static_cast<int64_t>(st.allocs));
    this->setGauge("arena.frees",     static_cast<int64_t>(st.frees));

    // Heap count (separate plugin heaps)
#if defined(RPMALLOC_FIRST_CLASS_HEAPS) && (RPMALLOC_FIRST_CLASS_HEAPS)
    if (arenaSvc_) {
        this->setGauge("arena.heap_count", static_cast<int64_t>(arenaSvc_->heapCount()));
    }
#endif

    // 2. EventBus queue metrics -------------------------------------------
    if (eventBus_) {
        this->setGauge("eventbus.queue_depth", static_cast<int64_t>(eventBus_->queueSizeApprox()));
        const int pct = eventBus_->queueUtilPct();
        if (pct >= 0) {
            this->setGauge("eventbus.queue_pct", static_cast<int64_t>(pct));
        }

        // High-water alert rule – generic 60 % for RingQueue
        {
            const auto stats = eventBus_->ringStats();
            using QB = launcher::core::events::EventBusService::QueueBackend;
            diagnostics::queue_alerts::maybeAlert<QB>(stats);
        }
    }

    // 3. Executor pending tasks -------------------------------------------
    this->setGauge("executor.pending_tasks", static_cast<int64_t>(executor_->pendingTasksApprox()));
    const int exec_pct = executor_->queueUtilPct();
    if (exec_pct >= 0) {
        this->setGauge("executor.queue_pct", static_cast<int64_t>(exec_pct));
    }

    // 4. Per-plugin heap gauges deferred until rpmalloc exposes per-heap stats.
#if defined(RPMALLOC_FIRST_CLASS_HEAPS) && (RPMALLOC_FIRST_CLASS_HEAPS)
    if (arenaSvc_) {
        for (const auto& hs : arenaSvc_->allHeapStats()) {
            std::string key = "plugins." + hs.plugin_id + ".bytes";
            this->setGauge(key, static_cast<int64_t>(hs.allocated_bytes));
        }
    }
#endif

    // 5. MetricSource iteration -------------------------------------------------
    registry_.forEachService([this](foundation::IService& svc){
        if(auto* ms = dynamic_cast<interfaces::IMetricSource*>(&svc)) {
            ms->emitMetrics(*this);
        }
    });

    // 6. Push queue alert counter as gauge so exporters can scrape it ---------
    this->setGauge("queue.alert_total", static_cast<int64_t>(queue_alerts_total()));

    // ---------------------------------------------------------------------
    // 7. HDR latency histograms – export bucket counts as gauges so external
    // exporters (Prom, OTLP) can scrape metrics `sec.verifier_ms` and
    // `runtime.scan_ms` with bucket suffixes.
    // ---------------------------------------------------------------------
    using latency::g_verifier_metric_source;
    using latency::g_scan_metric_source;

    auto latency_sources = std::make_tuple(&g_verifier_metric_source,
                                           &g_scan_metric_source);

    export_metrics(latency_sources, [this]<typename SrcT>(const auto& stats) {
        std::string prefix;
        if constexpr (std::is_same_v<SrcT, latency::VerifierMetricSource>) {
            prefix = "sec.verifier_ms";
        } else if constexpr (std::is_same_v<SrcT, latency::ScanMetricSource>) {
            prefix = "runtime.scan_ms";
        }

        for (const auto& b : stats.buckets) {
            // Gauge key example: sec.verifier_ms.le_8
            const int64_t upper = static_cast<int64_t>(b.upper_ms);
            std::string key = prefix + ".le_" + std::to_string(upper);
            this->setGauge(key, static_cast<int64_t>(b.count));
        }
    });

    // try {
    //     DBG(snapshot().dump());
    // } catch (const std::exception& ex) {
    //     ERR("DiagnosticsService failed to dump JSON snapshot: " << ex.what());
    // }
}

}  // namespace launcher::core::diagnostics 