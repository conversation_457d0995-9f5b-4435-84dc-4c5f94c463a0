// -----------------------------------------------------------------------------
// @file hdr_latency.hh
// @brief Latency histogram helpers for security verifier and runtime scan paths
//        backed by a header-only HDR Histogram implementation.  The functions
//        are header-only and safe to call from any thread.  Recording is
//        lock-free and O(1) thanks to the underlying hdr_histogram algorithm.
// -----------------------------------------------------------------------------
#pragma once

#include <atomic>
#include <cstdint>
#include <vector>
#include <array>
#include <cmath>
#include <algorithm>

// External header-only HDR Histogram – brought in via <PERSON><PERSON><PERSON> FetchContent.
// We tolerate slightly different include paths across versions by probing
// the most common ones via __has_include.
#if __has_include(<hdr_histogram/hdr_histogram.hpp>)
#include <hdr_histogram/hdr_histogram.hpp>
#elif __has_include(<hdr/histogram.hpp>)
#include <hdr/histogram.hpp>
#else
// ---------------------------------------------------------------------------
// Fallback stub when the external hdr_histogram header is unavailable. This
// stub provides a minimal-compatible interface (record_value) so that Kai can
// build offline.  The full HDR histogram functions (percentiles, iteration,
// etc.) are not needed by the current exporter path – only recording is used
// for future sampling.  The stub intentionally keeps memory impact tiny.
// ---------------------------------------------------------------------------
namespace hdr_histogram {
template <typename IntT, IntT Min, IntT Max, int SigDigits>
class Histogram {
 public:
  inline void record_value(IntT) noexcept {}
};
}  // namespace hdr_histogram
#endif

namespace launcher::core::diagnostics::latency {

// We track latencies in *milliseconds* with three significant digits which
// yields 0.1 % precision – good enough for UI surfacing and Prom scraping.
static constexpr int kSigDigits  = 3;
static constexpr int64_t kMinMs  = 1;         // 1 ms minimum bucket
static constexpr int64_t kMaxMs  = 60 * 1000; // 60 s upper bound – verifier & scan

// Type alias – underlying counter type is 64-bit to avoid overflow on long-running
// processes.
using HdrHist = hdr_histogram::Histogram<int64_t, kMinMs, kMaxMs, kSigDigits>;

// -------------------------------------------------------------------------
// Stats structure exposed by MetricSource implementations.  Declared early so
// helper structs can reference HistogramStats::Bucket without forward issues.
// -------------------------------------------------------------------------
struct HistogramStats {
    static constexpr bool is_metric_source = true;
    struct Bucket { double upper_ms; uint64_t count; };
    std::vector<Bucket> buckets;
};

namespace detail {
static constexpr size_t kBucketCount = 64; // log2 scale buckets for 1ms…60s
inline size_t bucketIndex(int64_t ms) {
    // Logarithmic bucket: each power-of-two millisecond range shares a bucket.
    // Clamp to valid range.
    if (ms <= 0) return 0;
    size_t idx = std::min(kBucketCount - 1,
                          static_cast<size_t>(63 - __builtin_clzll(static_cast<uint64_t>(ms))));
    return idx;
}

struct AtomicBuckets {
    std::array<std::atomic<uint64_t>, kBucketCount> data{};
    void record(int64_t ms) noexcept {
        data[bucketIndex(ms)].fetch_add(1, std::memory_order_relaxed);
    }
    void snapshot(std::vector<launcher::core::diagnostics::latency::HistogramStats::Bucket>& out) const {
        int64_t upper = 1;
        for (size_t i = 0; i < kBucketCount; ++i) {
            upper = (1LL << (i + 1));
            uint64_t c = data[i].load(std::memory_order_relaxed);
            if (c) out.push_back({static_cast<double>(upper), c});
        }
    }
};

inline AtomicBuckets& verifierBuckets() {
    static AtomicBuckets b; return b;
}
inline AtomicBuckets& scanBuckets() {
    static AtomicBuckets b; return b;
}

inline HdrHist& verifierHist() { static HdrHist h; return h; }
inline HdrHist& scanHist() { static HdrHist h; return h; }
} // namespace detail

// -------------------------------------------------------------------------

inline void recordVerifier(double ms) noexcept {
    const int64_t v = std::lround(ms);
    detail::verifierHist().record_value(v);
    detail::verifierBuckets().record(v);
}
inline void recordRuntimeScan(double ms) noexcept {
    const int64_t v = std::lround(ms);
    detail::scanHist().record_value(v);
    detail::scanBuckets().record(v);
}

class VerifierMetricSource {
public:
    static constexpr bool is_metric_source = true;
    [[nodiscard]] HistogramStats stats() const {
        HistogramStats out;
        detail::verifierBuckets().snapshot(out.buckets);
        return out;
    }
};
class ScanMetricSource {
public:
    static constexpr bool is_metric_source = true;
    [[nodiscard]] HistogramStats stats() const {
        HistogramStats out;
        detail::scanBuckets().snapshot(out.buckets);
        return out;
    }
};

// Global instances – their addresses are stable and safe to put in tuples
inline VerifierMetricSource g_verifier_metric_source;
inline ScanMetricSource     g_scan_metric_source;

} // namespace launcher::core::diagnostics::latency
