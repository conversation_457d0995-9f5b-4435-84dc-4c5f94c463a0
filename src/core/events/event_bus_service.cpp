#include "event_bus_service.h"

#include <chrono>

#include "../util/debug.h"

namespace launcher::core::events {

EventBusService::EventBusService(foundation::ServiceRegistry& registry, diagnostics::DiagnosticsService* diag)
    : foundation::ServiceBase<EventBusService, launcher::core::async::ExecutorService>(registry),
      executor_(&this->template get<launcher::core::async::ExecutorService>()),
      diag_(diag) {
    // queue_ is value-initialised; no dynamic allocation required.
}

EventBusService::~EventBusService() { stop(); }

// ---------------------------- start ---------------------------------------

util::Result<void> EventBusService::start() {
    if (dispatcher_.joinable()) {
        return util::Result<void>::success();
    }

    stop_flag_.store(false, std::memory_order_release);

    // Spawn dispatcher thread.
    dispatcher_ = std::thread([this]() { this->dispatcherLoop(); });

    // Immediately publish an initial idle gauge so DiagnosticsService callers
    // can observe a sane value even before the dispatcher thread has processed
    // any events (or unblocked from its initial wait state).  This avoids
    // tests flaking when the bus is completely idle and the dispatcher never
    // enters its reporting cadence.
    if (diag_) {
        diag_->setGauge("eventbus.idle_cpu_pct", 100);
    }

    DBG("EventBusService started (dispatcher thread id=" << dispatcher_.get_id() << ")");
    return util::Result<void>::success();
}

// ----------------------------- stop ---------------------------------------

void EventBusService::stop() noexcept {
    if (stop_flag_.exchange(true, std::memory_order_acq_rel)) {
        return; // already stopped
    }

    if (dispatcher_.joinable()) {
        // Always push a sentinel task so the consumer unblocks regardless of
        // current queue size.  When the queue happens to be full we spin-yield
        // until a slot becomes available (the dispatcher will free one as it
        // drains).  The window is bounded by queue capacity and therefore
        // sub-microsecond in practice.

        DispatcherTask sentinel{}; // empty task – evaluated as false in consumer
        while (!queue_.try_push(std::move(sentinel))) {
            std::this_thread::yield();
            sentinel = DispatcherTask{}; // reconstruct because previous was moved
        }

        dispatcher_.join();
    }
}

// ----------------------- dispatcherLoop -----------------------------------

void EventBusService::dispatcherLoop() {
    DispatcherTask task;

    // Idle CPU gauge bookkeeping.
    std::uint64_t sleep_ticks  = 0;
    std::uint64_t active_ticks = 0;

    const auto reportIdlePct = [&]() noexcept {
        const auto total = sleep_ticks + active_ticks;
        if (diag_ && total != 0 && (total & 0x3FFu) == 0) { // every 1024 iterations
            const int64_t pct = static_cast<int64_t>((sleep_ticks * 100) / total);
            diag_->setGauge("eventbus.idle_cpu_pct", pct);
        }
    };

    while (true) {
        queue_.wait_pop(task);            // blocks until item available

        // If stop requested and queue is now empty, exit loop.
        if (stop_flag_.load(std::memory_order_acquire) && !task) {
            break;
        }

        if (task) {
            auto fn = std::move(task); // preserve callable
            auto res = executor_->submit(std::move(fn));
            if (!res) {
                // Executor has already shut down – run the handler inline to
                // guarantee that destructors execute and to avoid leaking
                // tasks after shutdown.
                fn();
            } else {
                (void)res; // detach
            }
            ++active_ticks;
        } else {
            ++sleep_ticks; // should not happen but keep gauge sane
        }
        reportIdlePct();
    }

    // Drain any remaining tasks (should be none).
    while (queue_.try_pop(task)) {
        if (task) {
            auto fn = std::move(task);
            auto res = executor_->submit(std::move(fn));
            if (!res) {
                fn();
            } else {
                (void)res;
            }
        }
    }
}

}  // namespace launcher::core::events 