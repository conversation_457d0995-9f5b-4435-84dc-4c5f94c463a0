#pragma once

#ifndef KAI_EVENTBUS_CAPACITY
#define KAI_EVENTBUS_CAPACITY 1024
#endif

#include <atomic>
#include <functional>
#include <thread>
#include <unordered_map>
#include <typeindex>
#include <vector>
#include <shared_mutex>
#include <memory>

#include "../foundation/iservice.h"
#include "../foundation/service_base.h"
#include "../util/result.h"
#include "../runtime/ring_queue_backend.hh"
#include "../async/executor_service.h"
#include "../diagnostics/diagnostics_service.h"
#include "../util/move_only_function.h"
#include "../foundation/registry.h"

namespace launcher::core::events {

class SubscriptionHandle {
 public:
    SubscriptionHandle() = default;
    explicit SubscriptionHandle(std::weak_ptr<std::function<void(const void*)>> w) : weak_(std::move(w)) {}
    SubscriptionHandle(SubscriptionHandle&&) noexcept = default;
    SubscriptionHandle& operator=(SubscriptionHandle&&) noexcept = default;
    SubscriptionHandle(const SubscriptionHandle&)            = delete;
    SubscriptionHandle& operator=(const SubscriptionHandle&) = delete;
    ~SubscriptionHandle() { unsubscribe(); }

    void unsubscribe() noexcept {
        if (auto sp = weak_.lock()) {
            *sp = nullptr; // nullifies callback; publisher will skip
            weak_.reset();
        }
    }

 private:
    std::weak_ptr<std::function<void(const void*)>> weak_;
};

using HandlerPtr = std::shared_ptr<std::function<void(const void*)>>;

// Map macro 0 (unbounded legacy) to fallback compile-time capacity 1024 for the ring buffer.
static constexpr std::size_t kEventBusCapMacro = static_cast<std::size_t>(KAI_EVENTBUS_CAPACITY);
static constexpr bool       kEventBusUnbounded = (kEventBusCapMacro == 0);
static constexpr std::size_t kEventBusQueueCap = kEventBusUnbounded ? 1024 : kEventBusCapMacro;

class EventBusService final : public foundation::ServiceBase<EventBusService, launcher::core::async::ExecutorService> {
 public:
    // When the number of subscribers for a given event type is small,
    // executing the callbacks synchronously in the publishing thread avoids
    // the additional queue→dispatcher→executor hops.  This threshold is a
    // tunable compile-time constant chosen conservatively to keep callback
    // latency bounded while still benefiting common cases such as the
    // RuntimeScanner cold-scan benchmark that has a single subscriber.
    static constexpr std::size_t kInlineHandlerThreshold = 8;

    static constexpr foundation::ServiceId kId = foundation::ServiceId::kEventBusService;

    // Task type alias exposed early so dependent public aliases can refer to it.
    using DispatcherTask = util::MoveOnlyFunction<>;

    explicit EventBusService(foundation::ServiceRegistry& registry, diagnostics::DiagnosticsService* diag = nullptr);
    ~EventBusService() override;

    util::Result<void> start() override;
    void             stop() noexcept override;

    // Subscribe returns a handle that will automatically unsubscribe on destruction.
    // Handler now receives a const E* to the event data.
    template <typename E, typename Fn>
    SubscriptionHandle subscribe(Fn&& handler);

    // Publish event (by shared_ptr to const E).
    template <typename E>
    foundation::KaiVoidExpected publish(std::shared_ptr<const E> evt_ptr);

    void setDiagnostics(diagnostics::DiagnosticsService* diag) noexcept { diag_ = diag; }

    [[nodiscard]] std::size_t queueSizeApprox() const noexcept {
        return queue_.size();
    }

    // Queue utilisation percentage (0-100). Returns -1 when capacity==0 (unbounded).
    [[nodiscard]] int queueUtilPct() const noexcept {
        if constexpr (kEventBusUnbounded) {
            return -1;
        } else {
            return static_cast<int>(queue_util_pct_.load(std::memory_order_relaxed));
        }
    }

    // Expose underlying queue type so diagnostics can fetch stats and run
    // generic high-water alert checks without introducing additional
    // dependency edges.
    using QueueBackend = runtime::RingQueueBackend<DispatcherTask, kEventBusQueueCap>;

    [[nodiscard]] launcher::core::runtime::QueueStats ringStats() const noexcept { return queue_.stats(); }

 private:
    async::ExecutorService* executor_{nullptr};

    // Central queue with fixed compile-time capacity.
    runtime::RingQueueBackend<DispatcherTask, kEventBusQueueCap> queue_{};

    // Dispatcher thread pops from queue_ and enqueues to executor_.
    std::thread        dispatcher_;
    std::atomic<bool>  stop_flag_{false};

    // Subscriber registry --------------------------------------------------
    std::unordered_map<std::type_index, std::vector<HandlerPtr>> handlers_;
    mutable std::shared_mutex registry_mutex_;

    diagnostics::DiagnosticsService* diag_{nullptr};
    std::atomic<std::uint64_t> msgs_published_{0};
    std::atomic<std::uint64_t> queue_overflow_{0};

    std::atomic<int> queue_util_pct_{0};

    // Compile-time guard against accidental inlining size regressions.
    static_assert(sizeof(DispatcherTask) <= util::MoveOnlyFunction<>::kInlineSize + 2 * sizeof(void*),
                  "EventBusService::DispatcherTask size exceeds expected bounds – check MoveOnlyFunction InlineSize");

    void dispatcherLoop();
};

// ----------------------------- Templates ----------------------------------

template <typename E, typename Fn>
SubscriptionHandle EventBusService::subscribe(Fn&& handler) {
    auto fnPtr = std::make_shared<std::function<void(const void*)>>();
    // The lambda now directly calls the handler with const E*
    // The handler signature is expected to be void(const E*)
    *fnPtr = [fn = std::forward<Fn>(handler)](const void* raw_evt_ptr) {
        fn(static_cast<const E*>(raw_evt_ptr));
    };

    {
        std::unique_lock lock(registry_mutex_);
        handlers_[std::type_index(typeid(E))].push_back(fnPtr);
    }

    return SubscriptionHandle(std::weak_ptr<std::function<void(const void*)>>(fnPtr));
}

template <typename E>
foundation::KaiVoidExpected EventBusService::publish(std::shared_ptr<const E> evt_ptr) {
    if (!evt_ptr) {
        // Handle null event pointer if necessary, or assert.
        // For now, let it proceed; handlers should check for nullptr if they expect it.
        // Or, simply return failure or success without publishing.
        return foundation::KaiVoidExpected::success(); // Or an error
    }

    DispatcherTask task;
    std::vector<HandlerPtr> handlers_copy;
    {
        std::shared_lock lock(registry_mutex_);
        auto it = handlers_.find(std::type_index(typeid(E)));
        if (it == handlers_.end()) {
            return foundation::KaiVoidExpected::success();
        }
        handlers_copy = it->second; // shallow copy of shared_ptr vector
    }

    // ------------------------------------------------------------------
    // Fast-path: execute synchronously only when the queue is effectively
    // unbounded (capacity == 0) or reasonably large (>64).  This preserves
    // deterministic back-pressure behaviour in unit tests that intentionally
    // compile the EventBus with a tiny capacity (e.g. 8) while still
    // delivering the latency savings for production builds (default 1024) or
    // the unbounded legacy mode.
    // ------------------------------------------------------------------
    constexpr bool kInlineAllowed = kEventBusUnbounded || (kEventBusQueueCap > 64);
    if constexpr (kInlineAllowed) {
        if (handlers_copy.size() <= kInlineHandlerThreshold) {
            for (const auto& hp : handlers_copy) {
                if (hp && *hp) {
                    (*hp)(evt_ptr.get()); // Pass raw pointer from shared_ptr
                }
            }

            ++msgs_published_;
            if (diag_) diag_->incrementCounter("eventbus.msgs_published");
            return foundation::KaiVoidExpected::success();
        }
    }

    // Build initial DispatcherTask from handlers_copy
    // Lambda now captures evt_ptr (shared_ptr copy, cheap)
    task = [handlers_copy, evt_ptr]() {
        for (const auto& hp : handlers_copy) {
            if (hp && *hp) {
                (*hp)(evt_ptr.get()); // Pass raw pointer from shared_ptr
            }
        }
    };

    constexpr std::size_t kCapMacro = kEventBusCapMacro;
    constexpr bool        kUnbounded = kEventBusUnbounded;

    if constexpr (kUnbounded) {
        // Retry until the task is enqueued. Re-create DispatcherTask on each
        // iteration because MoveOnlyFunction is moved-from after a failed push.
        while (true) {
            if (queue_.try_push(std::move(task))) {
                break;
            }
            // Rebuild identical task for next attempt.
            // Lambda now captures evt_ptr (shared_ptr copy, cheap)
            task = [handlers_copy, evt_ptr]() {
                for (const auto& hp : handlers_copy) {
                    if (hp && *hp) {
                        (*hp)(evt_ptr.get()); // Pass raw pointer from shared_ptr
                    }
                }
            };
            std::this_thread::yield();
        }
        ++msgs_published_;
        if (diag_) diag_->incrementCounter("eventbus.msgs_published");
        return foundation::KaiVoidExpected::success();
    } else {
        // Bounded capacity – first check size and reject early when already full.
        const auto size_now_pre = queue_.size();
        if (size_now_pre >= kEventBusQueueCap) {
            ++queue_overflow_;
            if (diag_) diag_->incrementCounter("eventbus.back_pressure");
            queue_util_pct_.store(100, std::memory_order_relaxed);
            return foundation::KaiVoidExpected::failure(foundation::KaiError::BackPressure);
        }

        // Try non-blocking enqueue (may still fail if another producer won the race).
        if (queue_.try_push(std::move(task))) {
            const auto size_now = queue_.size();
            const int  pct      = static_cast<int>((size_now * 100) / kEventBusQueueCap);
            queue_util_pct_.store(pct, std::memory_order_relaxed);

            ++msgs_published_;
            if (diag_) diag_->incrementCounter("eventbus.msgs_published");
            return foundation::KaiVoidExpected::success();
        }

        // Enqueue failed – queue full.
        ++queue_overflow_;
        if (diag_) diag_->incrementCounter("eventbus.back_pressure");
        queue_util_pct_.store(100, std::memory_order_relaxed);
        return foundation::KaiVoidExpected::failure(foundation::KaiError::BackPressure);
    }
}

}  // namespace launcher::core::events 