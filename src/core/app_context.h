#pragma once

#include <memory>
#include "interfaces/iconfig_manager.h"
#include "interfaces/iapp_index.h"
namespace launcher { namespace ui { class IBrowserHistoryImporter; } }

namespace launcher {
namespace core {

class IHistoryManager; // forward declaration
class ContextManager;  // forward declaration
class ModelRegistry;    // forward declaration
class ProviderCatalog;  // forward declaration
class ModelFactory;     // forward declaration
namespace plugins { class RuntimeManagerSvc; } // forward declaration
namespace events { class EventBusService; }

/**
 * Centralised holder for shared runtime services that can be injected into
 * upper-layer components (UI, plugins, tests) instead of relying on singletons
 * throughout the code-base.  Add new service pointers here as they become
 * injectable.
 */
struct AppContext {
    // History subsystem used by UI to present recent items and record launches.
    std::shared_ptr<IHistoryManager> historyManager;
    std::shared_ptr<IConfigManager>  configManager;
    std::shared_ptr<IAppIndex>       appIndex;
    // Centralised context manager offering text-selection and other runtime context providers.
    std::shared_ptr<ContextManager> contextManager;
    std::shared_ptr<launcher::ui::IBrowserHistoryImporter> browserHistory;

    // LLM related shared services
    std::shared_ptr<ModelRegistry>   modelRegistry;
    std::shared_ptr<ProviderCatalog> providerCatalog;
    std::shared_ptr<ModelFactory>    modelFactory;

    // Runtime plugin discovery / management
    std::shared_ptr<plugins::RuntimeManagerSvc> runtimeManager;

    // Global event bus
    std::shared_ptr<events::EventBusService> eventBus;

    // Extend with more services as required, for example:
    // std::shared_ptr<IConfigManager> configManager;
    // std::shared_ptr<ILogRouter>     logRouter;
};

}  // namespace core
}  // namespace launcher 