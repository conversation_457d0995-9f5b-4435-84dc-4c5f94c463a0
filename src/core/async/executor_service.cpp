#include "executor_service.h"

#include <chrono>

#include "../util/debug.h"
#include "../diagnostics/diagnostics_service.h"

namespace launcher::core::async {

ExecutorService::ExecutorService(foundation::ServiceRegistry& registry)
    : foundation::ServiceBase<ExecutorService, launcher::core::memory::ArenaAllocatorSvc>(registry) {
    constexpr std::size_t kCap = static_cast<std::size_t>(KAI_EXECUTOR_CAPACITY);
    if constexpr (kCap == 0) {
        queue_ = std::make_unique<moodycamel::BlockingConcurrentQueue<Task>>();
    } else {
        queue_ = std::make_unique<moodycamel::BlockingConcurrentQueue<Task>>(kCap);
    }
}

ExecutorService::~ExecutorService() {
    stop();
}

// ----------------------------- start --------------------------------------

util::Result<void> ExecutorService::start() {
    if (!workers_.empty()) {
        return util::Result<void>::success(); // already started
    }

    const std::size_t thread_count = std::max<std::size_t>(1, std::thread::hardware_concurrency());
    DBG("ExecutorService starting with threads=" << thread_count);

    try {
        workers_.reserve(thread_count);
        for (std::size_t i = 0; i < thread_count; ++i) {
            workers_.emplace_back([this]() { this->workerLoop(); });
        }
    } catch (const std::exception& ex) {
        ERR("ExecutorService failed to spawn threads: " << ex.what());
        return util::Result<void>::failure("ExecutorService::start thread spawn failed");
    }

    if (diag_) {
        diag_->setGauge("executor.active_threads", static_cast<int64_t>(thread_count));
    }

    return util::Result<void>::success();
}

// ------------------------------ stop --------------------------------------

void ExecutorService::stop() noexcept {
    if (stop_flag_.exchange(true, std::memory_order_acq_rel)) {
        return; // already stopped
    }

    // Wait for all workers to finish.
    for (auto& t : workers_) {
        if (t.joinable()) {
            t.join();
        }
    }
    workers_.clear();
}

// --------------------------- workerLoop -----------------------------------

void ExecutorService::workerLoop() {
    while (true) {
        if (stop_flag_.load(std::memory_order_acquire)) {
            // Drain remaining tasks before exit.
            Task task;
            while (queue_->try_dequeue(task)) {
                task();
            }
            break;
        }

        Task task;
        // Wait up to 1 ms for a task; this bounds shutdown latency and avoids busy-spin.
        if (queue_->wait_dequeue_timed(task, std::chrono::milliseconds(1))) {
            task();
        }
    }
}

// ------------------------------------------------------------------------
// Private helpers
// ------------------------------------------------------------------------

void ExecutorService::backPressureHit() noexcept {
    if (diag_) {
        diag_->incrementCounter("executor.back_pressure");
    }
}

}  // namespace launcher::core::async 