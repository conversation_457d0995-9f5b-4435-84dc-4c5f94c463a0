set(ASYNC_SOURCES
    executor_service.cpp
)

add_library(core_async_obj OBJECT ${ASYNC_SOURCES})

# include directories (public)

target_include_directories(core_async_obj PUBLIC ${CMAKE_CURRENT_SOURCE_DIR})

# Depends on foundation (ServiceBase) and memory (allocator), and abseil maybe.

target_link_libraries(core_async_obj PUBLIC core_foundation core_memory absl::base)

add_library(core_async STATIC)

target_link_libraries(core_async PUBLIC core_async_obj core_foundation core_memory absl::base) 