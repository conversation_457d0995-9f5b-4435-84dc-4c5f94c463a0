#pragma once

#include <atomic>
#include <cstddef>
#include <future>
#include <functional>
#include <thread>
#include <type_traits>
#include <vector>
#include <memory>

#include "../foundation/service_base.h"
#include "../util/result.h"
#include <blockingconcurrentqueue.h>
#include "../util/move_only_function.h"
#include "../memory/arena_allocator_service.h"
// Forward declaration to avoid circular include – full definition in executor_service.cpp
namespace launcher::core::diagnostics { class DiagnosticsService; }

namespace launcher::core::async {

class ExecutorService final
    : public foundation::ServiceBase<ExecutorService, launcher::core::memory::ArenaAllocatorSvc> {
 public:
    using Base = foundation::ServiceBase<ExecutorService, launcher::core::memory::ArenaAllocatorSvc>;

    static constexpr foundation::ServiceId kId = foundation::ServiceId::kExecutorService;

    explicit ExecutorService(foundation::ServiceRegistry& registry);
    ~ExecutorService() override;

    // IService -------------------------------------------------------------
    // id() comes from ServiceBase

    util::Result<void> start() override;
    void             stop() noexcept override;

    // Submit an arbitrary callable which will be executed on the pool.
    // Returns std::future<R> where R is the result type of the callable.
    template <typename F, typename... Args>
    auto submit(F&& f, Args&&... args)
        -> foundation::KaiExpected<std::future<std::invoke_result_t<F, Args...>>>;

    void setDiagnostics(diagnostics::DiagnosticsService* diag) noexcept { diag_ = diag; }
    [[nodiscard]] std::size_t activeThreads() const noexcept { return workers_.size(); }
    [[nodiscard]] std::size_t pendingTasksApprox() const noexcept { return queue_->size_approx(); }
    [[nodiscard]] int queueUtilPct() const noexcept {
        if constexpr (static_cast<std::size_t>(KAI_EXECUTOR_CAPACITY) == 0) return -1;
        return static_cast<int>(queue_util_pct_.load(std::memory_order_relaxed));
    }

 private:
    using Task = util::MoveOnlyFunction<>;

    // Compile-time guard: ensure MoveOnlyFunction inline storage has not
    // unintentionally grown – Task should be exactly InlineSize + two
    // pointers (call + destroy). Adjust formula if MoveOnlyFunction layout
    // changes.
    static_assert(sizeof(Task) <= util::MoveOnlyFunction<>::kInlineSize + 2 * sizeof(void*),
                  "ExecutorService::Task size exceeds expected bounds – check MoveOnlyFunction InlineSize");

    void workerLoop();

    // Called when task submission fails due to queue capacity; increments
    // diagnostics counter if DiagnosticsService is wired. Defined in the .cpp
    // file to avoid pulling in diagnostics_service.h here.
    void backPressureHit() noexcept;

    std::vector<std::thread>              workers_;
    std::unique_ptr<moodycamel::BlockingConcurrentQueue<Task>> queue_;
    std::atomic<bool>                     stop_flag_{false};
    std::atomic<int> queue_util_pct_{0};
    diagnostics::DiagnosticsService* diag_{nullptr};
};

// ------------------------ template impl inline ----------------------------

template <typename F, typename... Args>
auto ExecutorService::submit(F&& f, Args&&... args) -> foundation::KaiExpected<std::future<std::invoke_result_t<F, Args...>>> {
    using ReturnT = std::invoke_result_t<F, Args...>;

    // ------------------------------------------------------------------
    // Refuse submissions once stop() has been called.  This prevents late
    // producers (e.g. EventBus dispatcher) from enqueuing tasks after the
    // worker threads have already terminated, which previously led to
    // use-after-free and aborts in RuntimeScan200Bench.
    // ------------------------------------------------------------------
    if (stop_flag_.load(std::memory_order_acquire)) {
        backPressureHit();
        return foundation::KaiExpected<std::future<ReturnT>>::failure(
            foundation::KaiError::BackPressure);
    }

    auto task_ptr = std::make_shared<std::packaged_task<ReturnT()>>(
        std::bind(std::forward<F>(f), std::forward<Args>(args)...));

    std::future<ReturnT> fut = task_ptr->get_future();

    constexpr std::size_t kCap = static_cast<std::size_t>(KAI_EXECUTOR_CAPACITY);

    auto wrapper = [task_ptr]() { (*task_ptr)(); };

    if constexpr (kCap == 0) {
        queue_->enqueue(std::move(wrapper));
        return foundation::KaiExpected<std::future<ReturnT>>::success(std::move(fut));
    } else {
        // Early size check to ensure deterministic back-pressure in tests.
        const auto size_pre = queue_->size_approx();
        if (size_pre >= kCap) {
            backPressureHit();
            queue_util_pct_.store(100, std::memory_order_relaxed);
            return foundation::KaiExpected<std::future<ReturnT>>::failure(foundation::KaiError::BackPressure);
        }

        if (queue_->try_enqueue(std::move(wrapper))) {
            const auto sz  = queue_->size_approx();
            const int pct  = static_cast<int>((sz * 100) / kCap);
            queue_util_pct_.store(pct, std::memory_order_relaxed);
            return foundation::KaiExpected<std::future<ReturnT>>::success(std::move(fut));
        }

        // Rejected – update metrics and signal back-pressure.
        backPressureHit();
        queue_util_pct_.store(100, std::memory_order_relaxed);
        return foundation::KaiExpected<std::future<ReturnT>>::failure(foundation::KaiError::BackPressure);
    }
}

}  // namespace launcher::core::async 