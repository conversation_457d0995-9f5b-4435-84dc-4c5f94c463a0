/*
 * AnthropicOptions - strongly typed configuration for Anthropic Claude models.
 */
#pragma once

#include <map>
#include <optional>
#include <string>
#include "llm/generic_chat_options.h"
#include "llm/reasoning_control.h"

namespace launcher {
namespace core {

struct AnthropicOptions : public GenericChatOptions {
    double top_p = 1.0; // required by Claude API
    std::optional<ReasoningControl> reasoning; // new optional
};

class AnthropicOptionsBuilder {
 public:
    static AnthropicOptions from(const std::map<std::string, std::string>& cfg);

 private:
    static double parseDouble(const std::map<std::string, std::string>& cfg,
                              const std::string& key,
                              double default_val);

    static int parseInt(const std::map<std::string, std::string>& cfg,
                        const std::string& key,
                        int default_val);
};

} // namespace core
} // namespace launcher 