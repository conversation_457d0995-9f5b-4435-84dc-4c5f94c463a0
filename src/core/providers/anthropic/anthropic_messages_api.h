#pragma once

#include "llm/i_api_endpoint.h"
#include "anthropic_options.h"
#include "../context/context.h"
#include "llm/reasoning_control.h"
#include <rapidjson/stringbuffer.h>
#include <rapidjson/writer.h>
#include "llm/chat_types.h"

namespace launcher {
namespace core {
namespace anthropic {

using Options = AnthropicOptions;

// Unified messages capability for Anthropic Claude models.
// Replaces the former ChatModule & ReasoningModule.
struct MessagesApi : public IApiEndpoint<Options> {
    std::string providerId() const override { return "anthropic"; }

    // Expose as Chat for routing simplicity; callers needing explicit reasoning
    // can still enable the hidden thinking tokens through options.reasoning.
    ApiFamily kind() const override { return ApiFamily::kMessages; }

    std::unordered_map<std::string, std::string>
    defaultHeaders(const Options&) const override {
        return {{"Content-Type", "application/json"},
                {"anthropic-version", "2023-06-01"}};
    }

    std::string endpoint(const Options&) const override {
        return "https://api.anthropic.com/v1/messages";
    }

    void buildPayload(rapidjson::Writer<rapidjson::StringBuffer>& writer,
                      std::string_view prompt,
                      const Context& context,
                      const Options& opts,
                      const std::string& model_name) const override {
        writer.StartObject();
        writer.Key("model"); writer.String(model_name.c_str());
        writer.Key("stream"); writer.Bool(true);
        writer.Key("max_tokens"); writer.Int(opts.max_tokens);
        writer.Key("temperature"); writer.Double(opts.temperature);
        writer.Key("top_p"); writer.Double(opts.top_p);

        // Optional reasoning/thinking extension.
        if (opts.reasoning) {
            ReasoningControl rc = *opts.reasoning;
            writer.Key("thinking"); writer.StartObject();
            writer.Key("type"); writer.String("enabled");
            writer.Key("budget_tokens"); writer.Int(rc.budget.value_or(1024));
            writer.EndObject();
        }

        // System message (optional)
        if (context.hasValue("system_message")) {
            auto v = context.getValue("system_message");
            if (v.isString()) {
                writer.Key("system"); writer.String(v.asString().c_str());
            }
        }

        // Messages array
        writer.Key("messages"); writer.StartArray();
        if (auto hist = context.getChatHistory()) {
            for (const auto& m : *hist) {
                if ((m.role == "user" || m.role == "assistant") && !m.contentRef().empty()) {
                    writer.StartObject();
                    writer.Key("role"); writer.String(m.role.c_str());
                    writer.Key("content"); writer.String(m.contentRef().c_str());
                    writer.EndObject();
                }
            }
        }
        writer.StartObject();
        writer.Key("role"); writer.String("user");
        writer.Key("content");
        writer.String(prompt.data(), static_cast<rapidjson::SizeType>(prompt.size()));
        writer.EndObject();
        writer.EndArray();

        writer.EndObject();
    }
};

} // namespace anthropic
} // namespace core
} // namespace launcher 