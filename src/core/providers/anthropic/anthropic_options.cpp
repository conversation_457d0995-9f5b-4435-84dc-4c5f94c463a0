#include "anthropic_options.h"

#include <stdexcept>

#include "../util/debug.h"

namespace launcher {
namespace core {

double AnthropicOptionsBuilder::parseDouble(const std::map<std::string, std::string>& cfg,
                                           const std::string& key,
                                           double default_val) {
    auto it = cfg.find(key);
    if (it == cfg.end()) return default_val;
    try {
        return std::stod(it->second);
    } catch (const std::exception& e) {
        ERR("Invalid double value for key '" << key << "': " << e.what());
        throw;
    }
}

int AnthropicOptionsBuilder::parseInt(const std::map<std::string, std::string>& cfg,
                                      const std::string& key,
                                      int default_val) {
    auto it = cfg.find(key);
    if (it == cfg.end()) return default_val;
    try {
        return std::stoi(it->second);
    } catch (const std::exception& e) {
        ERR("Invalid integer value for key '" << key << "': " << e.what());
        throw;
    }
}

AnthropicOptions AnthropicOptionsBuilder::from(const std::map<std::string, std::string>& cfg) {
    AnthropicOptions opts;

    opts.temperature = parseDouble(cfg, "temperature", opts.temperature);
    opts.max_tokens  = parseInt(cfg, "max_tokens", opts.max_tokens);
    opts.top_p       = parseDouble(cfg, "top_p", opts.top_p);

    auto it_cw = cfg.find("context_window_size");
    if (it_cw != cfg.end()) {
        try {
            opts.context_window_size = std::stoi(it_cw->second);
        } catch (const std::exception& e) {
            ERR("Invalid integer for context_window_size: " << e.what());
            throw;
        }
    }

    return opts;
}

} // namespace core
} // namespace launcher 