#pragma once

#include <functional>
#include <memory>
#include <string>
#include <vector>
#include <unordered_map>

#include "../http/http_client.h"
#include "llm/llm_model.h"
#include "../utilities/cancellation_token.h"
#include "../config/ai_provider_config.h"
#include "../util/stream_generator.h"
#include "../http/sse_decoder.h"
#include "anthropic_options.h"
#include "../util/result.h"
#include "llm/i_api_endpoint.h"
#include "llm/api_family_rules.h"
#include <rapidjson/writer.h>
#include <rapidjson/stringbuffer.h>
#include "llm/model_registry.h"

namespace launcher {
namespace core {

class Context;

/**
 * @brief Anthropic (Claude) model implementation
 *
 * Provides integration with Anthropic's Claude models using the /v1/messages API.
 */
class AnthropicModel : public LlmModel {
 public:
    // Primary constructor with options.
    AnthropicModel(const std::string& model_name,
                   const AnthropicOptions& opts = AnthropicOptions{},
                   const std::string& api_key = "");

    // Convenience ctor kept for legacy code (for tests)
    AnthropicModel(const std::string& model_name, const std::string& api_key);

    ~AnthropicModel() override = default;

    // Response generation ----------------------------------------------------
    std::string complete(std::string_view prompt, const Context& context) override;

    /**
     * @brief Generate a streaming response to a prompt
     *        (delegates to ProviderBaseT implementation)
     */
    std::string stream(std::string_view prompt, const Context& context,
                       ::http::DataCallback externalCallback,
                       std::shared_ptr<utilities::CancellationToken> cancellationToken) override;

    launcher::core::util::AsyncStream<::http::Delta> streamAsync(std::string_view prompt, const Context& context,
                                                              std::shared_ptr<utilities::CancellationToken> cancellationToken = nullptr) override;

    // Factory helpers --------------------------------------------------------
    static std::shared_ptr<AnthropicModel> create(const std::string& model_name = "",
                                                  const std::string& api_key = "");

    static std::shared_ptr<AnthropicModel> create(const std::string& model_name,
                                                  const AnthropicOptions& opts,
                                                  const std::string& api_key = "");

    // Introspection helpers
    util::Result<void> verifyApiKeyValid();

    static std::vector<ModelConfig> listModels(const AIProviderConfig& provider);

    static constexpr std::string_view kProviderId = "anthropic";

 protected:
    bool validateConfiguration() const;

 private:
    void initializeCapabilities();
    std::string extractVersion() const;

    std::string getApiKeyFromEnvironment(const std::string& env_var_name) const {
        const char* env_key = std::getenv(env_var_name.c_str());
        return env_key ? std::string(env_key) : "";
    }

    // LlmModel overrides
    std::string name() const override { return model_name_; }
    std::string provider() const override { return std::string(kProviderId); }
    CapabilitySet capabilities() const override { return capability_bits_; }

    bool hasCapability(Capability cap) const { return capability_bits_.test(static_cast<size_t>(cap)); }

    // Data members --------------------------------------------------
    std::string model_name_;
    AnthropicOptions options_;
    std::string api_key_;

    CapabilitySet capability_bits_;

    std::unordered_map<ApiFamily, std::unique_ptr<IApiEndpoint<AnthropicOptions>>> endpoints_;
    ApiFamily active_api_ {ApiFamily::kMessages};

    std::shared_ptr<http::HttpClient> httpClient_;
};

}  // namespace core
}  // namespace launcher 