#include "anthropic_model.h"
#include "llm/capability.h"
#include "llm/model_registry.h"
#include "anthropic_messages_api.h"
#include "llm/capability_heuristics.h"
#include "llm/stream_runner.h"
#include "llm/api_family_rules.h"
#include "llm/model_errors.h"

#include <algorithm>
#include <cstdlib>
#include <iostream>
#include <regex>
#include <sstream>
#include <rapidjson/writer.h>
#include <rapidjson/stringbuffer.h>
#include <rapidjson/document.h>

#if defined(KAI_JSON_PARSER_SIMDJSON)
#include <simdjson.h>
#endif

#include "../context/context.h"
#include "../util/debug.h"
#include "http/http_client.h"
#include "http/http_pool.h"
#include "../config/ai_provider_config.h"
#include "http/http_error.h"
#include "../util/stream_generator.h"

namespace launcher {
namespace core {

bool registerAnthropicProvider(ModelRegistry& registry) {
    return registry.registerProvider(
        "anthropic",
        [](const std::string& model, const std::string& key) {
            return std::make_shared<AnthropicModel>(model, AnthropicOptions{}, key);
        },
        [](const AIProviderConfig& cfg) {
            return AnthropicModel::listModels(cfg);
        });
}

AnthropicModel::AnthropicModel(const std::string& model_name,
                               const AnthropicOptions& opts,
                               const std::string& api_key)
    : model_name_(model_name), options_(opts), api_key_(api_key) {

    endpoints_.emplace(ApiFamily::kMessages, std::make_unique<anthropic::MessagesApi>());

    active_api_ = getDefaultApi("anthropic", model_name_); // likely kMessages

    const auto& defHeaders = endpoints_.at(active_api_)->defaultHeaders(options_);
    httpClient_ = ::http::HttpPool::instance().acquire("anthropic", defHeaders);

    if (api_key_.empty()) {
        api_key_ = getApiKeyFromEnvironment("ANTHROPIC_API_KEY");
    }
    if (!api_key_.empty()) {
        httpClient_->addDefaultHeader("x-api-key", api_key_);
    }

    initializeCapabilities();
}

AnthropicModel::AnthropicModel(const std::string& model_name, const std::string& api_key)
    : AnthropicModel(model_name, AnthropicOptions{}, api_key) {}

std::string AnthropicModel::complete(std::string_view prompt_sv, const Context& context) {
    std::string prompt(prompt_sv); // Current implementation uses std::string for body construction
    try {
        DBG("AnthropicModel starting API call for model: " + model_name_);
        if (!validateConfiguration()) {
            throw std::runtime_error("invalid config");
        }
        if (api_key_.empty()) {
            ERR("AnthropicModel: API key is not set");
            throw ConfigurationError("API key is not set");
        }
        httpClient_->setTimeout(60);

        // Build request body (RapidJSON)
        rapidjson::StringBuffer sb;
        sb.Reserve(4096);
        rapidjson::Writer<rapidjson::StringBuffer> writer(sb);

        writer.StartObject();
        writer.Key("model");        writer.String(model_name_.c_str());
        writer.Key("max_tokens");   writer.Int(options_.max_tokens);
        writer.Key("temperature");  writer.Double(options_.temperature);
        writer.Key("top_p");        writer.Double(options_.top_p);
        writer.Key("stream");       writer.Bool(false);

        // System message (optional)
        if (context.hasValue("system_message")) {
            auto v = context.getValue("system_message");
            if (v.isString()) {
                writer.Key("system"); writer.String(v.asString().c_str());
            }
        }

        // Messages array
        writer.Key("messages");
        writer.StartArray();

        // History
        if (auto histOpt = context.getChatHistory()) {
            for (const auto& m : *histOpt) {
                writer.StartObject();
                writer.Key("role");    writer.String(m.role.c_str());
                writer.Key("content"); writer.String(m.contentRef().c_str());
                writer.EndObject();
            }
        }

        // Current user message
        writer.StartObject();
        writer.Key("role");    writer.String("user");
        writer.Key("content"); writer.String(prompt.c_str(), static_cast<rapidjson::SizeType>(prompt.size()));
        writer.EndObject();

        writer.EndArray();
        writer.EndObject();

        std::string bodyStr(sb.GetString(), sb.GetSize());

        std::string endpoint = "https://api.anthropic.com/v1/messages";
        auto respResult = httpClient_->post(endpoint, bodyStr);
        if (!respResult) {
            std::string msg = "Anthropic API request failed: " + respResult.error().message;
            // Attempt to extract error message JSON
            try {
                rapidjson::Document edoc;
                if (!respResult.value().body.empty() && !edoc.Parse(respResult.value().body.c_str()).HasParseError()) {
                    if (edoc.HasMember("error")) {
                        rapidjson::StringBuffer sbuf; rapidjson::Writer<rapidjson::StringBuffer> w(sbuf);
                        edoc["error"].Accept(w);
                        msg += " - " + std::string(sbuf.GetString(), sbuf.GetSize());
                    }
                }
            } catch (...) {/* ignore */}
            ERR("AnthropicModel: " << msg);
            return "Error: " + msg;
        }

        const auto& resp = respResult.value();

        // Parse response (RapidJSON)
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wshadow"
#endif
        std::string result;
#if defined(KAI_JSON_PARSER_SIMDJSON)
        try {
            thread_local simdjson::ondemand::parser ondemand_parser;
            simdjson::ondemand::document doc = ondemand_parser.iterate(resp.body);
            auto content_arr = doc["content"];
            if (content_arr.type() != simdjson::ondemand::json_type::array) {
                throw ApiError("Unexpected response format from Anthropic API (content not array)");
            }
            for (simdjson::ondemand::value blk : content_arr) {
                if (blk["text"].error() == simdjson::SUCCESS) {
                    std::string_view text_sv = blk["text"].get_string().value();
                    result.append(text_sv.data(), text_sv.size());
                }
            }
        } catch (const simdjson::simdjson_error& e) {
            throw ApiError(std::string("Failed to parse JSON response from Anthropic API: ") + e.what());
        }
#else
        rapidjson::Document doc;
        if (doc.Parse(resp.body.c_str()).HasParseError()) {
            throw ApiError("Failed to parse JSON response from Anthropic API");
        }

        if (doc.HasMember("content") && doc["content"].IsArray()) {
            for (auto& blk : doc["content"].GetArray()) {
                if (blk.HasMember("text") && blk["text"].IsString()) {
                    result += blk["text"].GetString();
                }
            }
        } else {
            throw ApiError("Unexpected response format from Anthropic API");
        }
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
        return result;
    } catch (const ConfigurationError& e) {
        ERR("AnthropicModel: Configuration error: " << e.what());
        return "Error: " + std::string(e.what());
    } catch (const ApiError& e) {
        ERR("AnthropicModel: API error: " << e.what());
        return "Error: Failed to generate response from Anthropic API: " + std::string(e.what());
    } catch (const std::exception& e) {
        ERR("AnthropicModel: Exception: " << e.what());
        return "Error: Failed to generate response from Anthropic model: " + std::string(e.what());
    } catch (...) {
        ERR("AnthropicModel: Unknown exception occurred");
        return "Error: Unknown error occurred while generating response";
    }
}

std::shared_ptr<AnthropicModel> AnthropicModel::create(const std::string& model_name,
                                                       const AnthropicOptions& opts,
                                                       const std::string& api_key) {
    if (model_name.empty()) {
        throw ConfigurationError("AnthropicModel::create requires non-empty model_name");
    }
    return std::make_shared<AnthropicModel>(model_name, opts, api_key);
}

void AnthropicModel::initializeCapabilities() {
    using namespace launcher::core;
    capability_bits_ = inferCapabilities("anthropic", model_name_);

    if (hasCapability(Capability::LargeContext) && options_.context_window_size < 200000) {
        options_.context_window_size = 200000;
    }
}

std::string AnthropicModel::extractVersion() const {
    std::regex re("claude-(\\d+)");
    std::smatch m;
    if (std::regex_search(model_name_, m, re) && m.size() > 1) {
        return m[1].str();
    }
    return model_name_;
}

bool AnthropicModel::validateConfiguration() const {
    if (api_key_.empty()) {
        ERR("API key is not set");
        return false;
    }
    // Add OpenAI-specific validation
    if (model_name_.empty()) {
        ERR("Model name is not set");
        return false;
    }
    return true;
}

util::Result<void> AnthropicModel::verifyApiKeyValid() {
    httpClient_->setTimeout(10);
    std::string endpoint = "https://api.anthropic.com/v1/models";
    auto respResult = httpClient_->get(endpoint);
    if (respResult && respResult.value().status_code == 200) {
        return util::Result<void>::success();
    }
    std::string msg = "API key validation failed";
    if (respResult) {
        msg += ", status=" + std::to_string(respResult.value().status_code);
    } else {
        msg += ": " + respResult.error().message;
    }
    return util::Result<void>::failure(msg);
}

std::vector<ModelConfig> AnthropicModel::listModels(const AIProviderConfig& provider) {
    std::vector<ModelConfig> models;
    try {
        ::http::HttpClient client;
        std::string apiKey = provider.api_key;
        if (apiKey.empty() && !provider.api_key_variable.empty()) {
            const char* envApi = std::getenv(provider.api_key_variable.c_str());
            if (envApi) apiKey = envApi;
        }
        if (!apiKey.empty()) {
            client.addDefaultHeader("x-api-key", apiKey);
        }
        client.addDefaultHeader("anthropic-version", "2023-06-01");
        std::string url = provider.base_url.empty() ? "https://api.anthropic.com/v1/models"
                                                   : provider.base_url + "/models";
        auto respResult = client.get(url);
        if (!respResult || respResult.value().status_code != 200) return models;
        const auto& resp = respResult.value();
        rapidjson::Document doc;
        if (doc.Parse(resp.body.c_str()).HasParseError()) {
            return models;
        }
        if (doc.HasMember("models") && doc["models"].IsArray()) {
            for (auto& m : doc["models"].GetArray()) {
                if (!m.HasMember("id") || !m["id"].IsString()) continue;
                ModelConfig mc;
                mc.id = mc.name = mc.display_name = m["id"].GetString();

                CapabilitySet caps = inferCapabilities("anthropic", mc.id);
                mc.supports_image_input        = caps.test(static_cast<size_t>(Capability::ImageInput));
                mc.supports_audio_input        = caps.test(static_cast<size_t>(Capability::AudioInput));
                mc.supports_image_generation   = caps.test(static_cast<size_t>(Capability::ImageGeneration));
                mc.supports_speech_generation  = caps.test(static_cast<size_t>(Capability::SpeechGeneration));
                mc.supports_streaming          = true;
                mc.supports_reasoning          = caps.test(static_cast<size_t>(Capability::HighReasoning));
                mc.supports_large_context      = caps.test(static_cast<size_t>(Capability::LargeContext));
                mc.supports_multimodal_rt      = caps.test(static_cast<size_t>(Capability::MultimodalRealtime));

                models.push_back(std::move(mc));
            }
        } else if (doc.HasMember("data") && doc["data"].IsArray()) {
            for (auto& m : doc["data"].GetArray()) {
                if (!m.HasMember("id") || !m["id"].IsString()) continue;
                ModelConfig mc;
                mc.id = mc.name = mc.display_name = m["id"].GetString();

                CapabilitySet capsAlt = inferCapabilities("anthropic", mc.id);
                mc.supports_image_input        = capsAlt.test(static_cast<size_t>(Capability::ImageInput));
                mc.supports_audio_input        = capsAlt.test(static_cast<size_t>(Capability::AudioInput));
                mc.supports_image_generation   = capsAlt.test(static_cast<size_t>(Capability::ImageGeneration));
                mc.supports_speech_generation  = capsAlt.test(static_cast<size_t>(Capability::SpeechGeneration));
                mc.supports_streaming          = true;
                mc.supports_reasoning          = capsAlt.test(static_cast<size_t>(Capability::HighReasoning));
                mc.supports_large_context      = capsAlt.test(static_cast<size_t>(Capability::LargeContext));
                mc.supports_multimodal_rt      = capsAlt.test(static_cast<size_t>(Capability::MultimodalRealtime));

                models.push_back(std::move(mc));
            }
        }
    } catch (const std::exception& e) {
        ERR("AnthropicModel: listModels exception: " << e.what());
    }
    return models;
}

std::string AnthropicModel::stream(std::string_view prompt,
                                  const Context& context,
                                  ::http::DataCallback externalCallback,
                                  std::shared_ptr<utilities::CancellationToken> cancellationToken) {
    const auto& mod = endpoints_.at(active_api_);

    thread_local rapidjson::StringBuffer buf;
    buf.Clear(); buf.Reserve(4096);
    rapidjson::Writer<rapidjson::StringBuffer> writer(buf);
    mod->buildPayload(writer, prompt, context, options_, model_name_);

    std::string body(buf.GetString(), buf.GetSize());

    return StreamRunner::run(httpClient_, mod->providerId(), mod->endpoint(options_), body,
                             std::move(externalCallback), std::move(cancellationToken));
}

launcher::core::util::AsyncStream<::http::Delta> AnthropicModel::streamAsync(std::string_view prompt_sv,
                                                                           const Context& context,
                                                                           std::shared_ptr<utilities::CancellationToken> cancellationToken) {
    const auto& mod = endpoints_.at(active_api_);

    thread_local rapidjson::StringBuffer buf;
    buf.Clear(); buf.Reserve(4096);
    rapidjson::Writer<rapidjson::StringBuffer> writer(buf);
    mod->buildPayload(writer, prompt_sv, context, options_, model_name_);

    std::string body(buf.GetString(), buf.GetSize());

    return httpClient_->streamDeltas("anthropic", mod->endpoint(options_), "POST",
                                     cancellationToken, body, {});
}

}  // namespace core
}  // namespace launcher 