# Anthropic Provider Library
add_library(anthropic_provider STATIC
    anthropic_model.cpp
    anthropic_options.cpp
)

# Include directories for Anthropic provider
target_include_directories(anthropic_provider
    PUBLIC
        ${CMAKE_CURRENT_SOURCE_DIR}
        ${CMAKE_CURRENT_SOURCE_DIR}/../..  # For core headers
        ${rapidjson_SOURCE_DIR}/include
)

# Link dependencies
target_link_libraries(anthropic_provider
    PUBLIC
        http_client
        context
        config
        $<$<STREQUAL:${KAI_JSON_PARSER},simdjson>:simdjson>
) 