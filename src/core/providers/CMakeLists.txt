# AI Provider Libraries
# Each provider is built as a separate static library for maximum modularity

# Build flags for optional providers
option(KAI_ENABLE_OPENAI_PROVIDER "Enable OpenAI provider" ON)
option(KAI_ENABLE_ANTHROPIC_PROVIDER "Enable Anthropic provider" ON)

if(KAI_ENABLE_OPENAI_PROVIDER)
    add_subdirectory(openai)
    list(APPEND ENABLED_PROVIDERS openai_provider)
endif()

if(KAI_ENABLE_ANTHROPIC_PROVIDER)
    add_subdirectory(anthropic)
    list(APPEND ENABLED_PROVIDERS anthropic_provider)
endif()

# Create a unified providers target for easier linking
add_library(providers INTERFACE)
target_link_libraries(providers INTERFACE ${ENABLED_PROVIDERS})

# Export the list of enabled providers for parent CMakeLists
set(KAI_ENABLED_PROVIDERS ${ENABLED_PROVIDERS} PARENT_SCOPE) 