/*
 * openai_common.h
 *
 * Utility helpers shared across OpenAI provider implementation to resolve the
 * base API URL. This allows unit-tests to override the remote endpoint by
 * simply exporting the environment variable OPENAI_BASE_URL.
 */
#pragma once

#include <cstdlib>
#include <string>
#include <string_view>

namespace launcher {
namespace core {
namespace openai {

// Returns the base URL to be used for OpenAI REST calls.
// Order of precedence:
//   1. Environment variable OPENAI_BASE_URL (dev / unit-test override)
//   2. Default production endpoint "https://api.openai.com/v1"
inline std::string baseUrl() {
    const char *env = std::getenv("OPENAI_BASE_URL");
    if (env && env[0] != '\0') {
        std::string url(env);
        // Trim trailing slash to keep concatenation logic simple
        if (!url.empty() && url.back() == '/') {
            url.pop_back();
        }
        return url;
    }
    return "https://api.openai.com/v1";
}

// Helper that joins the base URL with an API-relative path.
inline std::string buildEndpoint(std::string_view path) {
    std::string base = baseUrl();
    if (!path.empty() && path.front() != '/') {
        return base + '/' + std::string(path);
    }
    return base + std::string(path);
}

} // namespace openai
} // namespace core
} // namespace launcher 