/*
 * OpenAIOptions - strongly typed configuration for OpenAI models.
 */
#pragma once

#include <map>
#include <optional>
#include <string>
#include "llm/generic_chat_options.h"
#include "llm/reasoning_control.h"

namespace launcher {
namespace core {

/**
 * Struct holding all runtime options required by OpenAIModel in typed form.
 */
struct OpenAIOptions : public GenericChatOptions {
    // Optional parameters specific to OpenAI
    std::optional<double> top_p;
    std::optional<ReasoningControl> reasoning;
};

/**
 * Builder that converts the legacy std::map<string,string> configuration into
 * a strongly-typed OpenAIOptions object. Centralises parsing & validation.
 */
class OpenAIOptionsBuilder {
 public:
    /**
     * Parse map and return typed options. Throws std::invalid_argument or
     * std::out_of_range on malformed input.
     */
    static OpenAIOptions from(const std::map<std::string, std::string>& cfg);

 private:
    static double parseDouble(const std::map<std::string, std::string>& cfg,
                              const std::string& key,
                              double default_val);

    static int parseInt(const std::map<std::string, std::string>& cfg,
                        const std::string& key,
                        int default_val);
};

} // namespace core
} // namespace launcher 