#pragma once

#include <functional>
#include <memory>
#include <string>
#include <vector>
#include <unordered_map>

#include "../../http/http_client.h"
#include "../../utilities/cancellation_token.h"
#include "../../config/ai_provider_config.h"
#include "../../util/stream_generator.h"
#include "../../http/sse_decoder.h"
#include "openai_options.h"
#include "../../util/result.h"
#include "llm/llm_model.h"
#include "llm/i_api_endpoint.h"
#include "llm/api_family_rules.h"
#include "rapidjson/writer.h"
#include "llm/model_registry.h"

namespace launcher {
namespace core {

class Context;

class OpenAIModel : public LlmModel {
 public:
    // Constructor with explicit options
    OpenAIModel(const std::string& model_name,
                const OpenAIOptions& opts,
                const std::string& api_key = "");

    // Convenience constructor kept for legacy usages (tests & benchmarks)
    OpenAIModel(const std::string& model_name, const std::string& api_key);

    // Traits already expose provider id; keep alias for backward code
    static constexpr std::string_view kProviderId = "openai";

    /**
     * @brief Destructor
     */
    virtual ~OpenAIModel() = default;

    /**
     * @brief Generate a response to a prompt
     * @param prompt The prompt to generate a response for
     * @param context The context for the request
     * @return The complete response
     */
    std::string complete(std::string_view prompt, const Context& context) override;

    /**
     * @brief Generate a streaming response to a prompt
     * @param prompt The prompt to generate a response for
     * @param context The context for the request
     * @param externalCallback The callback to call for each chunk of the response
     * @param cancellationToken The cancellation token to cancel the request
     * @return The complete response
     */
    std::string stream(std::string_view prompt, const Context& context,
                       ::http::DataCallback externalCallback,
                       std::shared_ptr<utilities::CancellationToken> cancellationToken) override;

    // Coroutine-based streaming. Returns generator that yields plain text deltas.
    launcher::core::util::AsyncStream<::http::Delta> streamAsync(std::string_view prompt, const Context& context,
                                                                 std::shared_ptr<utilities::CancellationToken> cancellationToken = nullptr) override;

    /**
     * @brief Create an OpenAI model instance
     * @param model_name The model name (e.g., "gpt-4", "gpt-3.5-turbo")
     * @param api_key Optional API key
     * @return Shared pointer to the created model
     */
    static std::shared_ptr<OpenAIModel> create(const std::string& model_name = "",
                                               const std::string& api_key = "");

    // Convenience factory with typed options.
    static std::shared_ptr<OpenAIModel> create(const std::string& model_name,
                                               const OpenAIOptions& opts,
                                               const std::string& api_key = "");

    /**
     * @brief Verify if the instance's API key is valid by making a test request.
     * @return true if the API key is valid, false otherwise
     */
    util::Result<void> verifyApiKeyValid();

    /**
     * @brief Fetch available models for the given provider configuration (static helper for discovery).
     */
    static std::vector<ModelConfig> listModels(const AIProviderConfig& provider);

    bool validateConfiguration() const;

    std::string extractVersion() const;

    std::string getApiKeyFromEnvironment(const std::string& env_var_name) const {
        const char* env_key = std::getenv(env_var_name.c_str());
        return env_key ? std::string(env_key) : "";
    }

    // Capability helper
    bool hasCapability(Capability cap) const { return capability_bits_.test(static_cast<size_t>(cap)); }

    // LlmModel overrides -------------------------------------------------
    std::string name() const override { return model_name_; }
    std::string provider() const override { return std::string(kProviderId); }
    CapabilitySet capabilities() const override { return capability_bits_; }

    // Data members --------------------------------------------------
    std::string model_name_;
    OpenAIOptions options_;
    std::string api_key_;

    CapabilitySet capability_bits_;

    std::unordered_map<ApiFamily, std::unique_ptr<IApiEndpoint<OpenAIOptions>>> endpoints_;
    ApiFamily active_api_ {ApiFamily::kChatCompletions};

    std::shared_ptr<http::HttpClient> httpClient_;

 private:
    void initializeCapabilities();
};

}  // namespace core
}  // namespace launcher