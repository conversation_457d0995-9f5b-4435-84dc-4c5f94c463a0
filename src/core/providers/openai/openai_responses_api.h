#pragma once

#include "llm/i_api_endpoint.h"
#include "openai_options.h"
#include "../context/context.h"
#include "llm/reasoning_control.h"
#include <rapidjson/stringbuffer.h>
#include <rapidjson/writer.h>
#include "openai_common.h"

namespace launcher {
namespace core {
namespace openai {

using Options = OpenAIOptions;

// Reasoning / Responses streaming capability for OpenAI models.
struct ResponsesApi : public IApiEndpoint<Options> {
    std::string providerId() const override { return "openai"; }
    ApiFamily kind() const override { return ApiFamily::kResponses; }

    std::unordered_map<std::string, std::string>
    defaultHeaders(const Options&) const override {
        return {{"Content-Type", "application/json"}};
    }

    std::string endpoint(const Options&) const override {
        return buildEndpoint("/responses");
    }

    void buildPayload(rapidjson::Writer<rapidjson::StringBuffer>& writer,
                      std::string_view prompt,
                      const Context& context,
                      const Options& opts,
                      const std::string& model_name) const override {
        writer.StartObject();
        writer.Key("model"); writer.String(model_name.c_str());
        writer.Key("stream"); writer.Bool(true);
        writer.Key("max_output_tokens"); writer.Int(opts.max_tokens);

        // Construct input array identical to chat but with additional reasoning options.
        writer.Key("input"); writer.StartArray();

        if (context.hasValue("system_message")) {
            auto v = context.getValue("system_message");
            if (v.isString()) {
                writer.StartObject();
                writer.Key("role"); writer.String("system");
                writer.Key("content"); writer.String(v.asString().c_str());
                writer.EndObject();
            }
        }

        if (auto hist = context.getChatHistory()) {
            for (const auto& m : *hist) {
                if (m.role == "user" || m.role == "assistant") {
                    writer.StartObject();
                    writer.Key("role"); writer.String(m.role.c_str());
                    writer.Key("content"); writer.String(m.contentRef().c_str());
                    writer.EndObject();
                }
            }
        }

        // Current user message
        writer.StartObject();
        writer.Key("role"); writer.String("user");
        writer.Key("content");
        writer.String(prompt.data(), static_cast<rapidjson::SizeType>(prompt.size()));
        writer.EndObject();

        writer.EndArray(); // input

        if (opts.top_p)             { writer.Key("top_p"); writer.Double(*opts.top_p); }
        if (opts.frequency_penalty) { writer.Key("frequency_penalty"); writer.Double(*opts.frequency_penalty); }
        if (opts.presence_penalty)  { writer.Key("presence_penalty"); writer.Double(*opts.presence_penalty); }

        // Reasoning block (effort/budget)
        ReasoningControl rc = opts.reasoning.value_or(ReasoningControl{});
        writer.Key("reasoning"); writer.StartObject();
        writer.Key("effort"); writer.String(rc.effort.c_str());
        if (rc.budget) { writer.Key("budget"); writer.Int(*rc.budget); }
        writer.EndObject();

        writer.EndObject();
    }
};

} // namespace openai
} // namespace core
} // namespace launcher 