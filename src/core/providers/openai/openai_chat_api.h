#pragma once

#include "llm/i_api_endpoint.h"
#include "openai_options.h"
#include "../context/context.h"
#include <rapidjson/stringbuffer.h>
#include <rapidjson/writer.h>
#include "llm/chat_types.h"
#include "openai_common.h"

namespace launcher {
namespace core {
namespace openai {

using Options = OpenAIOptions;

// Chat/completions capability for OpenAI models – supersedes the old ChatModule.
struct ChatApi : public IApiEndpoint<Options> {
    // IApiEndpoint ---------------------------------------------
    std::string providerId() const override { return "openai"; }

    ApiFamily kind() const override { return ApiFamily::kChatCompletions; }

    std::unordered_map<std::string, std::string>
    defaultHeaders(const Options&) const override {
        return {{"Content-Type", "application/json"}};
    }

    std::string endpoint(const Options&) const override {
        return buildEndpoint("/chat/completions");
    }

    void buildPayload(rapidjson::Writer<rapidjson::StringBuffer>& writer,
                      std::string_view prompt,
                      const Context& ctx,
                      const Options& opts,
                      const std::string& model_name) const override {
        // Serialise payload for standard chat/completions endpoint.
        writer.StartObject();
        writer.Key("model"); writer.String(model_name.c_str());

        // Stream always true – we use SSE/stream decoder.
        writer.Key("stream"); writer.Bool(true);

        // Sampling params
        writer.Key("temperature"); writer.Double(opts.temperature);
        writer.Key("max_tokens"); writer.Int(opts.max_tokens);

        // Messages array
        writer.Key("messages"); writer.StartArray();

        // System message
        if (ctx.hasValue("system_message")) {
            auto v = ctx.getValue("system_message");
            if (v.isString()) {
                writer.StartObject();
                writer.Key("role"); writer.String("system");
                writer.Key("content"); writer.String(v.asString().c_str());
                writer.EndObject();
            }
        }

        // Chat history
        if (auto hist = ctx.getChatHistory()) {
            for (const auto& m : *hist) {
                if (m.role == "user" || m.role == "assistant") {
                    writer.StartObject();
                    writer.Key("role"); writer.String(m.role.c_str());
                    writer.Key("content"); writer.String(m.contentRef().c_str());
                    writer.EndObject();
                }
            }
        }

        // Current user prompt
        writer.StartObject();
        writer.Key("role"); writer.String("user");
        writer.Key("content");
        writer.String(prompt.data(), static_cast<rapidjson::SizeType>(prompt.size()));
        writer.EndObject();

        writer.EndArray(); // messages

        // Optional params
        if (opts.top_p)             { writer.Key("top_p"); writer.Double(*opts.top_p); }
        if (opts.frequency_penalty) { writer.Key("frequency_penalty"); writer.Double(*opts.frequency_penalty); }
        if (opts.presence_penalty)  { writer.Key("presence_penalty"); writer.Double(*opts.presence_penalty); }

        writer.EndObject();
    }
};

} // namespace openai
} // namespace core
} // namespace launcher 