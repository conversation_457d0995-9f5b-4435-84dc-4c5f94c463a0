#include "openai_model.h"
#include "llm/model_registry.h"

#include <algorithm>          // For string transformations
#include <cstdlib>            // For getenv
#include <iostream>           // For error reporting
#include <regex>              // For regex matching
#include <sstream>            // For istringstream
#include <memory>
#include <rapidjson/writer.h>
#include <rapidjson/stringbuffer.h>
#include <rapidjson/document.h>
#include "core/util/json_writer.h"

#if defined(KAI_JSON_PARSER_SIMDJSON)
#include <simdjson.h>
#endif

#include "../context/context.h"
#include "../util/debug.h"
#include "http/http_client.h"
#include "http/http_pool.h"
#include "../config/ai_provider_config.h"
#include "llm/capability.h"
#include "http/http_error.h"
#include "../util/stream_generator.h"
#include "openai_chat_api.h"
#include "openai_responses_api.h"
#include "llm/capability_heuristics.h"
#include "llm/stream_runner.h"
#include "llm/api_family_rules.h"
#include "llm/model_errors.h"
#include "openai_common.h"

// Explicit provider registration function – called from provider_registration.cpp
namespace launcher {
namespace core {

bool registerOpenAIProvider(ModelRegistry& registry) {
    return registry.registerProvider(
        "openai",
        [](const std::string& model, const std::string& key) {
            return std::make_shared<OpenAIModel>(model, OpenAIOptions{}, key);
        },
        [](const AIProviderConfig& cfg) {
            return OpenAIModel::listModels(cfg);
        });
}

OpenAIModel::OpenAIModel(const std::string& model_name,
                         const OpenAIOptions& opts,
                         const std::string& api_key)
    : model_name_(model_name), options_(opts), api_key_(api_key) {

    // Build endpoint map
    endpoints_.emplace(ApiFamily::kChatCompletions, std::make_unique<openai::ChatApi>());
    endpoints_.emplace(ApiFamily::kResponses, std::make_unique<openai::ResponsesApi>());

    // Determine supported APIs and choose sensible default.
    active_api_ = getDefaultApi("openai", model_name_);

    // Acquire shared HTTP client with default headers of active endpoint
    const auto& defHeaders = endpoints_.at(active_api_)->defaultHeaders(options_);
    httpClient_ = ::http::HttpPool::instance().acquire("openai", defHeaders);

    // Resolve API key
    if (api_key_.empty()) {
        api_key_ = getApiKeyFromEnvironment("OPENAI_API_KEY");
    }
    if (!api_key_.empty()) {
        httpClient_->addDefaultHeader("Authorization", "Bearer " + api_key_);
    }

    initializeCapabilities();
}

// Convenience constructor keeping legacy signature
OpenAIModel::OpenAIModel(const std::string& model_name, const std::string& api_key)
    : OpenAIModel(model_name, OpenAIOptions{}, api_key) {}

std::string OpenAIModel::complete(std::string_view prompt_sv, const Context& context) {
    std::string userPrompt_std(prompt_sv); // Need std::string for modifications and RapidJSON
    try {
        DBG("Starting API call for model: " + model_name_);

        // Validate configuration
        validateConfiguration();

        if (api_key_.empty()) {
            ERR("API key is not set");
            throw ConfigurationError("API key is not set");
        }

        // Configure HTTP client
        httpClient_->setTimeout(60);

        // Optional organization header
        auto values = context.getValues();
        auto orgIt = std::find_if(values.begin(), values.end(), [](const auto& p) {
            return p.first == "openai_organization";
        });
        if (orgIt != values.end()) {
            httpClient_->addDefaultHeader("OpenAI-Organization", orgIt->second.asString());
        }

        // Detect JSON response request
        auto sysIt = std::find_if(values.begin(), values.end(), [](const auto& p) {
            return p.first == "system_message";
        });

        bool hasJsonInPrompt = (prompt_sv.find("json") != std::string_view::npos);
        bool useJsonFormat   = hasJsonInPrompt;
        if (!useJsonFormat && sysIt != values.end()) {
            useJsonFormat = (sysIt->second.asString().find("json") != std::string::npos);
        }

        // std::string userPrompt = std::string(prompt_sv); // Already created as userPrompt_std
        if (useJsonFormat && !hasJsonInPrompt &&
            (sysIt == values.end() || sysIt->second.asString().find("json") == std::string::npos)) {
            userPrompt_std += " Respond with a JSON object.";
        }

        // ---------------- Build payload (RapidJSON) -----------------
        using launcher::core::util::JsonWriter;
        JsonWriter jw;

        jw.startObject();
        jw.kv("model", model_name_);

        // Streaming disabled for this path
        jw.kv("stream", false);

        // Sampling parameters
        jw.kv("temperature", options_.temperature);
        jw.kv("max_tokens", options_.max_tokens);
        if (options_.top_p)             { jw.kv("top_p", *options_.top_p); }
        if (options_.frequency_penalty) { jw.kv("frequency_penalty", *options_.frequency_penalty); }
        if (options_.presence_penalty)  { jw.kv("presence_penalty", *options_.presence_penalty); }

        // Messages array
        jw.key("messages");
        jw.startArray();

        // System message
        if (sysIt != values.end()) {
            jw.startObject();
            jw.kv("role",   "system");
            jw.kv("content", sysIt->second.asString());
            jw.end();
        }

        // History
        if (auto histOpt = context.getChatHistory()) {
            for (const auto& msg : *histOpt) {
                jw.startObject();
                jw.kv("role", msg.role);
                jw.kv("content", msg.contentRef());
                jw.end();
            }
        }

        // Current user message
        jw.startObject();
        jw.kv("role", "user");
        jw.kv("content", userPrompt_std);
        jw.end();

        jw.end(); // end messages array

        // JSON mode request
        if (useJsonFormat) {
            jw.key("response_format");
            jw.startObject();
            jw.kv("type", "json_object");
            jw.end();
        }

        jw.end(); // end root object

        std::string requestBodyStr = jw.finish();

        // Endpoint
        std::string endpoint = openai::buildEndpoint("/chat/completions");
        auto httpResult = httpClient_->post(endpoint, requestBodyStr);
        if (!httpResult) {
            std::string err = "API request failed: " + httpResult.error().message;
            throw std::runtime_error(err);
        }

        const auto& httpResponse = httpResult.value();

        // ---------------- Parse response (RapidJSON) -----------------
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wshadow"
#endif
        std::string result;

#if defined(KAI_JSON_PARSER_SIMDJSON)
        try {
            thread_local simdjson::ondemand::parser ondemand_parser;
            // Ensure the input satisfies simdjson padding requirements by copying
            // into a padded_string when necessary. The copy is negligible for the
            // small unit-test responses and avoids INSUFFICIENT_PADDING errors.
            simdjson::padded_string padded(httpResponse.body);
            simdjson::ondemand::document doc = ondemand_parser.iterate(padded);

            auto choices = doc["choices"];
            if (choices.type() != simdjson::ondemand::json_type::array) {
                throw ApiError("Unexpected response format from OpenAI API (choices not array)");
            }

            auto first_choice = choices.at(0);
            auto message      = first_choice["message"];
            std::string_view content_sv = message["content"].get_string().value();
            result.assign(content_sv.data(), content_sv.size());
        } catch (const simdjson::simdjson_error& e) {
            throw ApiError(std::string("Failed to parse JSON response from OpenAI: ") + e.what());
        }
#else  // Fallback to RapidJSON
        rapidjson::Document doc;
        if (doc.Parse(httpResponse.body.c_str()).HasParseError()) {
            throw ApiError("Failed to parse JSON response from OpenAI");
        }

        if (doc.HasMember("choices") && doc["choices"].IsArray() && !doc["choices"].Empty()) {
            const auto& choice = doc["choices"].Begin()[0];
            if (choice.HasMember("message") && choice["message"].HasMember("content") &&
                choice["message"]["content"].IsString()) {
                result = choice["message"]["content"].GetString();
            } else {
                throw ApiError("Unexpected response format from OpenAI API");
            }
        } else {
            throw ApiError("Unexpected response format from OpenAI API");
        }
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif

        return result;

    } catch (const ConfigurationError& e) {
        ERR("Configuration error: " << e.what());
        return "Error: " + std::string(e.what());
    } catch (const ApiError& e) {
        ERR("API error: " << e.what());
        return "Error: Failed to generate response from OpenAI API: " + std::string(e.what());
    } catch (const std::exception& e) {
        ERR("Exception: " << e.what());
        return "Error: Failed to generate response from OpenAI model: " + std::string(e.what());
    } catch (...) {
        ERR("Unknown exception occurred");
        return "Error: Unknown error occurred while generating response";
    }
}

std::shared_ptr<OpenAIModel> OpenAIModel::create(const std::string& model_name,
                                                 const std::string& api_key) {
    if (model_name.empty()) {
        throw ConfigurationError("OpenAIModel::create requires non-empty model_name");
    }
    return std::make_shared<OpenAIModel>(model_name, api_key);
}

std::shared_ptr<OpenAIModel> OpenAIModel::create(const std::string& model_name,
                                                 const OpenAIOptions& opts,
                                                 const std::string& api_key) {
    if (model_name.empty()) {
        throw ConfigurationError("OpenAIModel::create requires non-empty model_name");
    }
    return std::make_shared<OpenAIModel>(model_name, opts, api_key);
}

void OpenAIModel::initializeCapabilities() {
    using namespace launcher::core;
    capability_bits_ = inferCapabilities("openai", model_name_);

    // Derive context_window_size from capability flags when not already set.
    if (hasCapability(Capability::LargeContext) && options_.context_window_size < 128000) {
        options_.context_window_size = 128000;
    }
}

std::string OpenAIModel::extractVersion() const {
    // Extract version from model name using regex
    std::regex re("gpt-(\\d+(?:\\.\\d+)?)(?:-|$)");
    std::smatch match;

    if (std::regex_search(model_name_, match, re) && match.size() > 1) {
        return match[1].str();
    }

    // Handle gpt-4o case
    if (model_name_.find("gpt-4o") != std::string::npos) {
        return "4o";
    }

    // If we can't extract a version, use the model name after "gpt-"
    size_t pos = model_name_.find("gpt-");
    if (pos != std::string::npos) {
        return model_name_.substr(pos + 4);
    }

    // Fallback
    return model_name_;
}

bool OpenAIModel::validateConfiguration() const {
    if (api_key_.empty()) {
        ERR("API key is not set");
        return false;
    }
    // Add OpenAI-specific validation
    if (model_name_.empty()) {
        ERR("Model name is not set");
        return false;
    }
    return true;
}

util::Result<void> OpenAIModel::verifyApiKeyValid() {
    try {
        // --- Use member HttpClient and set timeout ---
        httpClient_->setTimeout(10); // Short timeout for key verification

        // Send a simple request to check if the API key is valid
        std::string endpoint = openai::buildEndpoint("/models");
        auto result = httpClient_->get(endpoint);
        // --- End member HttpClient use ---

        // Check if the request was successful (status code 200)
        if (result && result.value().status_code == 200) {
            return util::Result<void>::success();
        }
        std::string msg = "API key validation failed";
        if (result) {
            msg += ", status=" + std::to_string(result.value().status_code);
        } else {
            msg += ": " + result.error().message;
        }
        return util::Result<void>::failure(msg);
    } catch (const std::exception& e) {
        ERR("Exception verifying API key: " + std::string(e.what()));
        return util::Result<void>::failure("Exception verifying API key: " + std::string(e.what()));
    }
}

std::vector<ModelConfig> OpenAIModel::listModels(const AIProviderConfig& provider) {
    std::vector<ModelConfig> models;
    try {
        ::http::HttpClient client;

        // Resolve API key – direct value overrides env var
        std::string apiKey = provider.api_key;
        if (apiKey.empty() && !provider.api_key_variable.empty()) {
            const char* envApi = std::getenv(provider.api_key_variable.c_str());
            if (envApi) apiKey = envApi;
        }
        if (!apiKey.empty()) {
            client.addDefaultHeader("Authorization", "Bearer " + apiKey);
        }

        std::string url = provider.base_url.empty() ? openai::buildEndpoint("/models")
                                                   : provider.base_url + "/models";
        auto respResult = client.get(url);
        if (!respResult || respResult.value().status_code != 200) {
            int status = respResult ? respResult.value().status_code : respResult.error().status_code;
            ERR("ModelDiscovery OpenAI listModels HTTP error status " << status);
            return models;
        }
        const auto& resp = respResult.value();

        rapidjson::Document doc;
        if (doc.Parse(resp.body.c_str()).HasParseError()) {
            ERR("ModelDiscovery OpenAI: JSON parse error");
            return models;
        }
        if (doc.HasMember("data") && doc["data"].IsArray()) {
            for (auto& v : doc["data"].GetArray()) {
                if (!v.HasMember("id") || !v["id"].IsString()) continue;
                std::string id = v["id"].GetString();
                ModelConfig mc;
                mc.id = mc.name = mc.display_name = id;

                CapabilitySet caps = inferCapabilities("openai", id);
                mc.supports_image_input        = caps.test(static_cast<size_t>(Capability::ImageInput));
                mc.supports_audio_input        = caps.test(static_cast<size_t>(Capability::AudioInput));
                mc.supports_image_generation   = caps.test(static_cast<size_t>(Capability::ImageGeneration));
                mc.supports_speech_generation  = caps.test(static_cast<size_t>(Capability::SpeechGeneration));
                mc.supports_streaming          = true; // default
                mc.supports_reasoning          = caps.test(static_cast<size_t>(Capability::HighReasoning));
                mc.supports_large_context      = caps.test(static_cast<size_t>(Capability::LargeContext));
                mc.supports_multimodal_rt      = caps.test(static_cast<size_t>(Capability::MultimodalRealtime));
                models.push_back(std::move(mc));
            }
        }
    } catch (const std::exception& e) {
        ERR("ModelDiscovery OpenAI listModels exception: " << e.what());
    }
    return models;
}

launcher::core::util::AsyncStream<::http::Delta> OpenAIModel::streamAsync(std::string_view prompt_sv,
                                                                        const Context& context,
                                                                        std::shared_ptr<utilities::CancellationToken> cancellationToken) {
    ApiFamily api = active_api_;

    // Allow promotion to Responses for reasoning when supported
    if (api == ApiFamily::kChatCompletions && hasCapability(Capability::HighReasoning) && endpoints_.count(ApiFamily::kResponses)) {
        api = ApiFamily::kResponses;
    }

    const auto& mod = endpoints_.at(api);

    thread_local rapidjson::StringBuffer buf;
    buf.Clear(); buf.Reserve(8192);
    rapidjson::Writer<rapidjson::StringBuffer> writer(buf);
    mod->buildPayload(writer, prompt_sv, context, options_, model_name_);

    std::string requestStr(buf.GetString(), buf.GetSize());
    return httpClient_->streamDeltas("openai", mod->endpoint(options_), "POST",
                                     cancellationToken, requestStr, {});
}

// ---------------------------------------------------------------------
// Forwarding implementation to template base – satisfies vtable.
// ---------------------------------------------------------------------
std::string OpenAIModel::stream(std::string_view prompt,
                               const Context& context,
                               ::http::DataCallback externalCallback,
                               std::shared_ptr<utilities::CancellationToken> cancellationToken) {
    ApiFamily api = active_api_;

    // Allow promotion to Responses for reasoning when supported
    if (api == ApiFamily::kChatCompletions && hasCapability(Capability::HighReasoning) && endpoints_.count(ApiFamily::kResponses)) {
        api = ApiFamily::kResponses;
    }

    const auto& mod = endpoints_.at(api);

    thread_local rapidjson::StringBuffer buf;
    buf.Clear(); buf.Reserve(8192);
    rapidjson::Writer<rapidjson::StringBuffer> writer(buf);
    mod->buildPayload(writer, prompt, context, options_, model_name_);

    std::string body(buf.GetString(), buf.GetSize());

    return StreamRunner::run(httpClient_, mod->providerId(), mod->endpoint(options_), body,
                             std::move(externalCallback), std::move(cancellationToken));
}

}  // namespace core
}  // namespace launcher