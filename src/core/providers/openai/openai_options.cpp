#include "openai_options.h"

#include <stdexcept>

#include "../util/debug.h"

namespace launcher {
namespace core {

// ---------- Helper parsing utilities --------------------------------------

double OpenAIOptionsBuilder::parseDouble(const std::map<std::string, std::string>& cfg,
                                         const std::string& key,
                                         double default_val) {
    auto it = cfg.find(key);
    if (it == cfg.end()) return default_val;
    try {
        return std::stod(it->second);
    } catch (const std::exception& e) {
        ERR("Invalid double value for key '" << key << "': " << e.what());
        throw;
    }
}

int OpenAIOptionsBuilder::parseInt(const std::map<std::string, std::string>& cfg,
                                   const std::string& key,
                                   int default_val) {
    auto it = cfg.find(key);
    if (it == cfg.end()) return default_val;
    try {
        return std::stoi(it->second);
    } catch (const std::exception& e) {
        ERR("Invalid integer value for key '" << key << "': " << e.what());
        throw;
    }
}

OpenAIOptions OpenAIOptionsBuilder::from(const std::map<std::string, std::string>& cfg) {
    OpenAIOptions opts;

    opts.temperature = parseDouble(cfg, "temperature", opts.temperature);
    opts.max_tokens  = parseInt(cfg, "max_tokens", opts.max_tokens);

    // Optional parameters
    auto setOptionalDouble = [&](const std::string& key, std::optional<double>& field) {
        auto it = cfg.find(key);
        if (it != cfg.end()) {
            try {
                field = std::stod(it->second);
            } catch (const std::exception& e) {
                ERR("Invalid optional double value for key '" << key << "': " << e.what());
                throw;
            }
        }
    };

    setOptionalDouble("top_p", opts.top_p);
    setOptionalDouble("frequency_penalty", opts.frequency_penalty);
    setOptionalDouble("presence_penalty", opts.presence_penalty);

    // context_window_size is integer optional
    auto it_cw = cfg.find("context_window_size");
    if (it_cw != cfg.end()) {
        try {
            opts.context_window_size = std::stoi(it_cw->second);
        } catch (const std::exception& e) {
            ERR("Invalid integer for context_window_size: " << e.what());
            throw;
        }
    }

    return opts;
}

} // namespace core
} // namespace launcher 