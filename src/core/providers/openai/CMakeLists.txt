# OpenAI Provider Library
add_library(openai_provider STATIC
    openai_model.cpp
    openai_options.cpp
)

# Include directories for OpenAI provider
target_include_directories(openai_provider
    PUBLIC
        ${CMAKE_CURRENT_SOURCE_DIR}
        ${CMAKE_CURRENT_SOURCE_DIR}/../..  # For core headers
        ${rapidjson_SOURCE_DIR}/include
)

# Link dependencies
target_link_libraries(openai_provider
    PUBLIC
        http_client
        context
        config
        $<$<STREQUAL:${KAI_JSON_PARSER},simdjson>:simdjson>
) 