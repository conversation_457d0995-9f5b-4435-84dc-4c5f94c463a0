#include "flat_snapshot.hh"

#include <fcntl.h>
#include <sys/mman.h>
#include <sys/stat.h>
#include <unistd.h>

#include <cstring>
#include <fstream>
#include <array>
#include <span>
#include <algorithm>
#include <limits>
#include <bit>

#ifdef __APPLE__
#include <CoreFoundation/CoreFoundation.h>
#include <Security/Security.h>
#endif

#ifdef KAI_USE_LIBSODIUM
#include <sodium.h>
#include "flat_snapshot_keys.hh"
#endif

namespace launcher::core::storage {

// --------------------------------- CRC32 ---------------------------------
namespace {
constexpr uint32_t kCrc32Poly = 0xEDB88320u;
constexpr std::array<uint32_t, 256> make_crc_table() {
    std::array<uint32_t, 256> table{};
    for (uint32_t i = 0; i < 256; ++i) {
        uint32_t c = i;
        for (int j = 0; j < 8; ++j) {
            c = (c & 1) ? (kCrc32Poly ^ (c >> 1)) : (c >> 1);
        }
        table[i] = c;
    }
    return table;
}

constexpr std::array<uint32_t, 256> kCrcTable = make_crc_table();

static_assert(std::endian::native == std::endian::little, "FlatSnapshot expects little-endian host");
} // namespace

uint32_t FlatSnapshot::crc32(const std::byte* data, size_t len) noexcept {
    uint32_t c = 0xFFFFFFFFu;
    const auto* p = reinterpret_cast<const uint8_t*>(data);
    for (size_t i = 0; i < len; ++i) {
        c = kCrcTable[(c ^ p[i]) & 0xFFu] ^ (c >> 8);
    }
    return c ^ 0xFFFFFFFFu;
}

// --------------------------------- ctor/dtor ---------------------------------
FlatSnapshot::FlatSnapshot(int fd, const std::byte* map_ptr, size_t map_size) noexcept
    : fd_(fd), map_size_(map_size), map_ptr_(map_ptr) {
    header_ = reinterpret_cast<const Header*>(map_ptr_);
    signature_ = reinterpret_cast<const std::byte*>(map_ptr_ + sizeof(Header));
    sig_len_ = header_->sig_len;
    payload_ = signature_ + sig_len_;
    payload_len_ = map_size_ - sizeof(Header) - sig_len_;
}

FlatSnapshot::~FlatSnapshot() {
    if (map_ptr_ && map_size_) {
        ::munmap(const_cast<std::byte*>(map_ptr_), map_size_);
    }
    if (fd_ >= 0) {
        ::close(fd_);
    }
}

FlatSnapshot::FlatSnapshot(FlatSnapshot&& other) noexcept {
    // Steal the resources from 'other' instead of delegating to the
    // move-assignment operator. Delegation would read uninitialised
    // members because the default constructor leaves them indeterminate.
    header_       = other.header_;
    signature_    = other.signature_;
    payload_      = other.payload_;
    sig_len_      = other.sig_len_;
    payload_len_  = other.payload_len_;
    fd_           = other.fd_;
    map_size_     = other.map_size_;
    map_ptr_      = other.map_ptr_;

    // Reset the moved-from object so its destructor is safe.
    other.fd_          = -1;
    other.map_ptr_     = nullptr;
    other.map_size_    = 0;
    other.header_      = nullptr;
    other.signature_   = nullptr;
    other.payload_     = nullptr;
    other.sig_len_     = 0;
    other.payload_len_ = 0;
}

FlatSnapshot& FlatSnapshot::operator=(FlatSnapshot&& other) noexcept {
    if (this != &other) {
        // Release current resources held by *this* safely.
        if (map_ptr_ && map_size_) {
            ::munmap(const_cast<std::byte*>(map_ptr_), map_size_);
        }
        if (fd_ >= 0) {
            ::close(fd_);
        }

        // Transfer ownership from *other*.
        fd_          = other.fd_;
        map_size_    = other.map_size_;
        map_ptr_     = other.map_ptr_;
        header_      = other.header_;
        signature_   = other.signature_;
        payload_     = other.payload_;
        sig_len_     = other.sig_len_;
        payload_len_ = other.payload_len_;

        // Null-out the moved-from object.
        other.fd_          = -1;
        other.map_ptr_     = nullptr;
        other.map_size_    = 0;
        other.header_      = nullptr;
        other.signature_   = nullptr;
        other.payload_     = nullptr;
        other.sig_len_     = 0;
        other.payload_len_ = 0;
    }
    return *this;
}

std::span<const std::byte> FlatSnapshot::payload() const noexcept {
    return {payload_, payload_len_};
}

// --------------------------------- create ---------------------------------
FlatExpectedVoid FlatSnapshot::create(const std::vector<std::byte>& payload,
                                      const std::filesystem::path& out_path,
                                      std::span<const std::byte> cms_signature) noexcept {
    // Prepare header
    Header hdr{};
    std::memcpy(hdr.magic, kMagic, sizeof(kMagic));
    hdr.version = kCurrentVersion;

#ifdef KAI_BUILD_SHA
    {
        constexpr size_t kShaLen = 20;
        const char* sha = KAI_BUILD_SHA;
        std::memcpy(hdr.build_sha, sha, std::min(kShaLen, std::strlen(sha)));
        if (std::strlen(sha) < kShaLen) {
            std::memset(hdr.build_sha + std::strlen(sha), '\0', kShaLen - std::strlen(sha));
        }
    }
#else
    std::memset(hdr.build_sha, 0, sizeof(hdr.build_sha));
#endif

    // Guard: signature size must fit into 32-bit field.
    if (cms_signature.size() > std::numeric_limits<uint32_t>::max()) {
        ERR("FlatSnapshot::create signature too large");
        return FlatExpectedVoid::failure(FlatSnapshotError::SizeOverflow);
    }

    hdr.crc32 = crc32(payload.data(), payload.size());

#ifdef KAI_USE_LIBSODIUM
    // -----------------------------------------------------------------
    // Auto-generate Ed25519 detached signature only when the caller has
    // not supplied an explicit `cms_signature`.  This preserves backwards
    // compatibility with tests that create *unsigned* snapshots by passing
    // an empty span, while still enabling signing when desired.
    // -----------------------------------------------------------------
    if (cms_signature.empty()) {
        unsigned char signature[64];
        using snapshot_key::kPriv;

        // Sanity: derive public key from secret key and ensure it matches the
        // compiled-in kPub constant. This catches accidental key mismatch at
        // start-up rather than deep in unit-tests.
        unsigned char derived_pk[32];
        crypto_sign_ed25519_sk_to_pk(derived_pk, kPriv.data());
        if (!std::equal(std::begin(derived_pk), std::end(derived_pk), snapshot_key::kPub.begin())) {
            ERR("FlatSnapshot::create – kPriv/kPub mismatch; signature may be invalid");
        }

        if (sodium_init() < 0) {
            return FlatExpectedVoid::failure(FlatSnapshotError::AppleApiError);
        }

        crypto_sign_detached(signature, nullptr,
                             reinterpret_cast<const unsigned char*>(payload.data()),
                             payload.size(),
                             kPriv.data());

        cms_signature = std::as_bytes(std::span(signature, 64));
    }
#endif

    hdr.sig_len = static_cast<uint32_t>(cms_signature.size());

    std::ofstream ofs(out_path, std::ios::binary | std::ios::trunc);
    if (!ofs) {
        ERR("FlatSnapshot::create unable to open output " << out_path);
        return FlatExpectedVoid::failure(FlatSnapshotError::IoError);
    }

    ofs.write(reinterpret_cast<const char*>(&hdr), sizeof(hdr));

    if (hdr.sig_len > 0) {
        ofs.write(reinterpret_cast<const char*>(cms_signature.data()), hdr.sig_len);
    }

    ofs.write(reinterpret_cast<const char*>(payload.data()), payload.size());
    if (!ofs.good()) {
        ERR("FlatSnapshot::create failed to write " << out_path);
        return FlatExpectedVoid::failure(FlatSnapshotError::IoError);
    }

    ofs.close();
    return FlatExpectedVoid::success();
}

// --------------------------------- mmapReadOnly ---------------------------------
FlatExpected<FlatSnapshot> FlatSnapshot::mmapReadOnly(const std::filesystem::path& path) noexcept {
    if (!std::filesystem::exists(path)) {
        return FlatExpected<FlatSnapshot>::failure(FlatSnapshotError::FileNotFound);
    }

    int fd = ::open(path.c_str(), O_RDONLY);
    if (fd < 0) {
        ERR("FlatSnapshot::mmapReadOnly open failed for " << path);
        return FlatExpected<FlatSnapshot>::failure(FlatSnapshotError::IoError);
    }

    struct stat st;
    if (::fstat(fd, &st) < 0) {
        ::close(fd);
        return FlatExpected<FlatSnapshot>::failure(FlatSnapshotError::IoError);
    }

    size_t size = static_cast<size_t>(st.st_size);
    if (size < sizeof(Header)) {
        ::close(fd);
        return FlatExpected<FlatSnapshot>::failure(FlatSnapshotError::TruncatedHeader);
    }

    void* addr = ::mmap(nullptr, size, PROT_READ, MAP_PRIVATE, fd, 0);
    if (addr == MAP_FAILED) {
        ::close(fd);
        return FlatExpected<FlatSnapshot>::failure(FlatSnapshotError::MapFailed);
    }

    // Validate header resides fully inside mapping before constructing object to
    // avoid pointer arithmetic going out-of-bounds when sig_len is malicious.
    const Header* hdr = reinterpret_cast<const Header*>(addr);
    if (sizeof(Header) + hdr->sig_len > size) {
        ::munmap(addr, size);
        ::close(fd);
        return FlatExpected<FlatSnapshot>::failure(FlatSnapshotError::TruncatedHeader);
    }

    FlatSnapshot snap(fd, reinterpret_cast<const std::byte*>(addr), size);
    // Basic sanity – check magic/version early; defer heavy verify to caller.
    if (std::memcmp(snap.header_->magic, kMagic, sizeof(kMagic)) != 0) {
        return FlatExpected<FlatSnapshot>::failure(FlatSnapshotError::InvalidMagic);
    }
    if (snap.header_->version != kCurrentVersion) {
        return FlatExpected<FlatSnapshot>::failure(FlatSnapshotError::UnsupportedVersion);
    }

    return FlatExpected<FlatSnapshot>::success(std::move(snap));
}

// --------------------------------- verify ---------------------------------
FlatExpectedVoid FlatSnapshot::verify() const noexcept {
    if (!header_) {
        return FlatExpectedVoid::failure(FlatSnapshotError::TruncatedHeader);
    }

    if (sizeof(Header) + sig_len_ > map_size_) {
        return FlatExpectedVoid::failure(FlatSnapshotError::TruncatedHeader);
    }

    if (std::memcmp(header_->magic, kMagic, sizeof(kMagic)) != 0) {
        return FlatExpectedVoid::failure(FlatSnapshotError::InvalidMagic);
    }
    if (header_->version != kCurrentVersion) {
        return FlatExpectedVoid::failure(FlatSnapshotError::UnsupportedVersion);
    }

    uint32_t calc_crc = crc32(payload_, payload_len_);
    if (calc_crc != header_->crc32) {
        return FlatExpectedVoid::failure(FlatSnapshotError::CrcMismatch);
    }

    // ------------------------------------------------------------------
    // Signature length zero handling – unsigned snapshot.
    // ------------------------------------------------------------------
    if (sig_len_ == 0) {
#if KAI_ENABLE_FLATSNAPSHOT_SIGN
        // Signing enforced – unsigned snapshot is invalid.
        return FlatExpectedVoid::failure(FlatSnapshotError::SignatureInvalid);
#else
        // Signatures optional – accept.
        return FlatExpectedVoid::success();
#endif
    }

    // ----------------------- signature branches ----------------------
#ifdef KAI_USE_LIBSODIUM
    // Ed25519 path takes precedence when sig_len == 64.
    if (sig_len_ == 64) {
        using snapshot_key::kPub;
        if (sodium_init() < 0) {
            return FlatExpectedVoid::failure(FlatSnapshotError::AppleApiError);
        }
        if (crypto_sign_verify_detached(reinterpret_cast<const unsigned char*>(signature_),
                                         reinterpret_cast<const unsigned char*>(payload_),
                                         payload_len_,
                                         kPub.data()) != 0) {
            return FlatExpectedVoid::failure(FlatSnapshotError::SignatureInvalid);
        }
        return FlatExpectedVoid::success();
    }
#endif // libsodium

#ifdef __APPLE__
    // Fallback to CMS verification on macOS when signature is not Ed25519.
    {
        // Prepare CFData with signature
        CFDataRef sig_data = CFDataCreateWithBytesNoCopy(kCFAllocatorDefault,
                                                         reinterpret_cast<const UInt8*>(signature_),
                                                         sig_len_, kCFAllocatorNull);
        if (!sig_data) {
            return FlatExpectedVoid::failure(FlatSnapshotError::AppleApiError);
        }

        // Detached content: payload bytes
        CFDataRef payload_data = CFDataCreateWithBytesNoCopy(kCFAllocatorDefault,
                                                             reinterpret_cast<const UInt8*>(payload_),
                                                             payload_len_, kCFAllocatorNull);

        CMSDecoderRef decoder = nullptr;
        OSStatus status = CMSDecoderCreate(&decoder);
        if (status != errSecSuccess) {
            CFRelease(sig_data);
            CFRelease(payload_data);
            return FlatExpectedVoid::failure(FlatSnapshotError::AppleApiError);
        }

        status = CMSDecoderUpdateMessage(decoder,
                                         CFDataGetBytePtr(sig_data),
                                         CFDataGetLength(sig_data));
        if (status == errSecSuccess) {
            status = CMSDecoderSetDetachedContent(decoder, payload_data);
        }
        if (status == errSecSuccess) {
            status = CMSDecoderFinalizeMessage(decoder);
        }
        if (status == errSecSuccess) {
            CMSSignerStatus signer_status = kCMSSignerValid;
            status = CMSDecoderCopySignerStatus(decoder, 0, nullptr, false, &signer_status, nullptr, nullptr);
            if (status == errSecSuccess && signer_status == kCMSSignerValid) {
                CFRelease(sig_data);
                CFRelease(payload_data);
                CFRelease(decoder);
                return FlatExpectedVoid::success();
            }
            status = errSecCSSignatureFailed;
        }

        // Clean up
        CFRelease(sig_data);
        CFRelease(payload_data);
        CFRelease(decoder);
        return FlatExpectedVoid::failure(status == errSecCSSignatureFailed ? FlatSnapshotError::SignatureInvalid
                                                                           : FlatSnapshotError::AppleApiError);
    }
#else
    // Non-Apple platforms without Ed25519 support: accept unsigned when
    // enforcement flag is OFF, otherwise invalidate.
#if KAI_ENABLE_FLATSNAPSHOT_SIGN
    return FlatExpectedVoid::failure(FlatSnapshotError::SignatureInvalid);
#else
    return FlatExpectedVoid::success();
#endif
#endif
}

} // namespace launcher::core::storage 