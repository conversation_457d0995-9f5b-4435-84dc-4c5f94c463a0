#pragma once

#include <cstddef>
#include <cstdint>
#include <filesystem>
#include <span>
#include <vector>

#include "core/util/expected.h"
#include "core/util/debug.h"

namespace launcher::core::storage {

enum class FlatSnapshotError {
    FileNotFound,
    IoError,
    MapFailed,
    InvalidMagic,
    UnsupportedVersion,
    TruncatedHeader,
    CrcMismatch,
    SignatureInvalid,
    SizeOverflow,
    AppleApiError,
};

using FlatExpectedVoid = launcher::core::util::Expected<void, FlatSnapshotError>;

template <typename T>
using FlatExpected = launcher::core::util::Expected<T, FlatSnapshotError>;

class FlatSnapshot {
private:
    // Packed binary header – do not introduce alignment padding.
    struct __attribute__((packed)) Header {
        char     magic[4];      // "KFSN"
        uint16_t version;       // 1
        char     build_sha[20]; // First 20 chars of git SHA
        uint32_t crc32;         // CRC32 of payload
        uint32_t sig_len;       // number of bytes following header that constitute CMS sig
        uint8_t  reserved[6]{}; // Padding to bring header to 40 bytes (8-byte aligned)
    };

    // Internal helpers
    static constexpr char kMagic[4] = {'K','F','S','N'};
    static constexpr uint16_t kCurrentVersion = 1;

public:
    FlatSnapshot() = default;
    ~FlatSnapshot();
    FlatSnapshot(FlatSnapshot&& other) noexcept;
    FlatSnapshot& operator=(FlatSnapshot&& other) noexcept;

    FlatSnapshot(const FlatSnapshot&) = delete;
    FlatSnapshot& operator=(const FlatSnapshot&) = delete;

    // The payload stored inside the snapshot (read-only, mmapped)
    std::span<const std::byte> payload() const noexcept;

    // Verify header, CRC32 and (if present) CMS signature.
    FlatExpectedVoid verify() const noexcept;

    // Factory helpers
    static FlatExpectedVoid create(const std::vector<std::byte>& payload,
                                   const std::filesystem::path& out_path,
                                   std::span<const std::byte> cms_signature = {}) noexcept;

    static FlatExpected<FlatSnapshot> mmapReadOnly(const std::filesystem::path& path) noexcept;

    // Size of binary header (v1). Exposed for tests.
    static constexpr size_t kHeaderSize = sizeof(Header);

private:
    const Header* header_{nullptr};
    const std::byte* signature_{nullptr};
    const std::byte* payload_{nullptr};
    size_t sig_len_{0};
    size_t payload_len_{0};

    int    fd_{-1};
    size_t map_size_{0};
    const std::byte* map_ptr_{nullptr};

    // Internal ctor used by mmapReadOnly
    explicit FlatSnapshot(int fd, const std::byte* map_ptr, size_t map_size) noexcept;

    static uint32_t crc32(const std::byte* data, size_t len) noexcept;
};

} // namespace launcher::core::storage 