// -----------------------------------------------------------------------------
// @file flat_snapshot_keys.hh
// @brief Embedded Ed25519 key pair used when KAI_ENABLE_FLATSNAPSHOT_SIGN=ON.
//        In production only the public key array should be shipped; the
//        private key is kept solely for unit-tests / CI snapshot generation.
//        Regenerate with libsodium `crypto_sign_keypair()` when rotating.
// -----------------------------------------------------------------------------
#pragma once

#include <array>
#include <cstdint>

namespace launcher::core::storage::snapshot_key {

// Test vector from the original Ed25519 paper (also used by libsodium test
// suite). This deterministic pair ensures that unit-tests can create and
// verify signatures without relying on runtime key generation.

inline constexpr std::array<std::uint8_t,32> kPub = {
    0xd7,0x5a,0x98,0x01,0x82,0xb1,0x0a,0xb7,
    0xd5,0x4b,0xfe,0xd3,0xc9,0x64,0x07,0x3a,
    0x0e,0xe1,0x72,0xf3,0xda,0xa6,0x23,0x25,
    0xaf,0x02,0x1a,0x68,0xf7,0x07,0x51,0x1a};

// 64-byte private key (crypto_sign_SECRETKEYBYTES) – DO NOT SHIP in prod
inline constexpr std::array<std::uint8_t,64> kPriv = {
    0x9d,0x61,0xb1,0x9d,0xef,0xfd,0x5a,0x60,
    0xba,0x84,0x4a,0xf4,0x92,0xec,0x2c,0xc4,
    0x44,0x49,0xc5,0x69,0x7b,0x32,0x69,0x19,
    0x70,0x3b,0xac,0x03,0x1c,0xae,0x7f,0x60,
    // public key (duplicated as per Ed25519 secret-key layout)
    0xd7,0x5a,0x98,0x01,0x82,0xb1,0x0a,0xb7,
    0xd5,0x4b,0xfe,0xd3,0xc9,0x64,0x07,0x3a,
    0x0e,0xe1,0x72,0xf3,0xda,0xa6,0x23,0x25,
    0xaf,0x02,0x1a,0x68,0xf7,0x07,0x51,0x1a};

} // namespace launcher::core::storage::snapshot_key 