# ConfigManager Documentation

## Overview

The `ConfigManager` is a singleton class that manages application configuration in the Micro Launcher application. It provides functionality for:

-   Loading configuration from a JSON file
-   Validating configuration against a schema
-   Saving configuration to a JSON file
-   Retrieving configuration values of different types (string, int, bool, double, string arrays)
-   Setting configuration values of different types
-   Accessing nested configuration values using dot notation

The class uses the [nlohmann/json](https://github.com/nlohmann/json) library for JSON parsing and serialization.

## Basic Usage

### Initialization

```cpp
#include "core/config/config_manager.h"

// Get the singleton instance
launcher::core::ConfigManager& config = launcher::core::ConfigManager::getInstance();

// Initialize with default config path
bool success = config.initialize();

// Or initialize with a custom config path
bool success = config.initialize("/path/to/config.json");

// Always check initialization success
if (!success) {
    std::cerr << "Failed to initialize configuration" << std::endl;
    // Handle error appropriately
}
```

### Reading Configuration Values

```cpp
// Get values with default fallbacks using dot notation for nested values
std::string theme = config.getString("general.theme", "system");
int maxResults = config.getInt("launcher_bar.max_results", 10);
bool startupOnLogin = config.getBool("general.startup_on_login", true);
double opacity = config.getDouble("launcher_bar.opacity", 0.95);

// Get an array of strings
std::vector<std::string> enabledProviders = config.getStringArray("context.enabled_providers");

// Using convenience methods for common configuration values
std::string launcherHotkey = config.getLauncherHotkey();
std::string theme = config.getTheme();
bool startupOnLogin = config.getStartupOnLogin();
int launcherBarWidth = config.getLauncherBarWidth();
```

### Writing Configuration Values

```cpp
// Set various types of values using dot notation for nested values
config.setString("general.theme", "dark");
config.setInt("launcher_bar.max_results", 15);
config.setBool("general.startup_on_login", true);
config.setDouble("launcher_bar.opacity", 0.9);

// Set an array of strings
std::vector<std::string> enabledProviders = {"clipboard", "active_window", "selected_text"};
config.setStringArray("context.enabled_providers", enabledProviders);

// Save changes to disk
bool saved = config.save();
if (!saved) {
    std::cerr << "Failed to save configuration" << std::endl;
    // Handle error appropriately
}
```

### Checking if a Key Exists

```cpp
// Check if a configuration key exists
if (config.hasKey("general.theme")) {
    // Key exists, use it
    std::string theme = config.getString("general.theme");
} else {
    // Key doesn't exist, use default
    std::string theme = "system";
}
```

## Default Configuration Path

The `ConfigManager` uses platform-specific default paths if no configuration path is provided:

-   **Windows**: `%APPDATA%\MicroLauncher\config.json`
-   **macOS**: `~/Library/Application Support/MicroLauncher\config.json`
-   **Linux**: `~/.config/microlauncher\config.json`

## Implementation Details

### Singleton Pattern

The `ConfigManager` is implemented as a singleton to ensure there's only one instance managing the configuration throughout the application. Access the instance using `ConfigManager::getInstance()`.

### PIMPL Idiom

The class uses the PIMPL (Pointer to Implementation) idiom to hide implementation details and reduce compilation dependencies. This pattern:

-   Reduces compile-time dependencies
-   Allows changing the implementation without affecting clients
-   Provides better binary compatibility

### Configuration Validation

The `ConfigManager` now includes validation of the configuration against a schema. The validation:

-   Checks for required properties
-   Validates property types
-   Validates enumerated values (e.g., theme must be one of "system", "light", or "dark")
-   Provides detailed error messages for validation failures

### Nested Configuration Access

The `ConfigManager` now supports accessing nested configuration values using dot notation. For example:

```cpp
// Access a nested value
std::string theme = config.getString("general.theme");

// Set a nested value
config.setString("general.theme", "dark");
```

### Error Handling

-   All methods that retrieve values provide default values that are returned in case of errors or if the key doesn't exist
-   Errors during loading, validation, or saving are logged to `std::cerr`
-   The `initialize()` and `save()` methods return boolean values indicating success or failure

## Configuration File Format

The configuration is stored as a structured JSON file following the schema defined in `config-schema.json`. Example:

```json
{
    "version": "1.0.0",
    "general": {
        "launcher_hotkey": "Alt+Space",
        "theme": "system",
        "language": "en",
        "startup_on_login": true,
        "check_updates": true,
        "telemetry_enabled": false
    },
    "launcher_bar": {
        "width": 600,
        "height": 600,
        "max_results": 10,
        "animation_speed": "normal",
        "font_size": 14,
        "opacity": 0.95,
        "position": "center",
        "blur_background": true
    },
    "actions": {
        "default_ai_provider": "openai",
        "default_ai_model": "gpt-4",
        "suggestion_algorithm": {
            "recency_weight": 0.3,
            "frequency_weight": 0.3,
            "relevance_weight": 0.4
        },
        "require_approval_for_dangerous_actions": true,
        "actions_directory": "~/.config/microlauncher/actions"
    },
    "context": {
        "enabled_providers": ["clipboard", "active_window", "selected_text", "current_directory"],
        "max_context_size": 4096
    },
    "performance": {
        "memory_limit_mb": 200,
        "background_process_priority": "low",
        "cache_size_mb": 50,
        "search_cache": {
            "enabled": true,
            "max_size": 100,
            "ttl_seconds": 300
        }
    },
    "security": {
        "encryption_enabled": true,
        "api_key_storage": "system_keychain"
    }
}
```

## Configuration Schema

The configuration schema is defined in `config-schema.json` and follows the [JSON Schema](https://json-schema.org/) standard. The schema defines:

-   Required properties
-   Property types
-   Enumerated values
-   Minimum and maximum values
-   Default values
-   Descriptions

The schema is used to validate the configuration when it's loaded and saved, ensuring that the configuration is always valid.

## Thread Safety

The `ConfigManager` is not thread-safe by default. If you need to access it from multiple threads, you should implement appropriate synchronization mechanisms. Consider using:

-   Mutex locks when accessing the ConfigManager
-   A thread-safe wrapper around ConfigManager
-   Accessing ConfigManager only from a single thread

## Best Practices

1. **Initialize Early**: Call `initialize()` early in your application startup
2. **Save When Needed**: Call `save()` after making changes that need to be persisted
3. **Use Default Values**: Always provide sensible default values when getting configuration values
4. **Error Checking**: Check the return value of `initialize()` and `save()` to handle errors appropriately
5. **Use Dot Notation**: Use dot notation to access nested configuration values
6. **Use Convenience Methods**: Use the provided convenience methods for commonly accessed configuration values
7. **Check Key Existence**: Use `hasKey()` to check if a configuration key exists before using it

## Common Configuration Keys

Based on the current schema, these are the common configuration keys:

| Key                           | Type    | Default           | Description                                |
| ----------------------------- | ------- | ----------------- | ------------------------------------------ |
| `general.launcher_hotkey`     | string  | "Alt+Space"       | Keyboard shortcut to activate the launcher |
| `general.theme`               | string  | "system"          | UI theme (system, light, dark)             |
| `general.language`            | string  | "en"              | Application language                       |
| `general.startup_on_login`    | boolean | true              | Whether to launch at login                 |
| `launcher_bar.width`          | integer | 600               | Width of the launcher window               |
| `launcher_bar.max_results`    | integer | 10                | Maximum results to display                 |
| `actions.default_ai_provider` | string  | "openai"          | Default AI provider                        |
| `actions.default_ai_model`    | string  | "gpt-4"           | Default AI model                           |
| `context.enabled_providers`   | array   | [...]             | Enabled context providers                  |
| `performance.memory_limit_mb` | integer | 200               | Maximum memory usage in MB                 |
| `security.api_key_storage`    | string  | "system_keychain" | Storage method for API keys                |

## Convenience Methods

The `ConfigManager` provides convenience methods for commonly accessed configuration values:

```cpp
// General settings
std::string getLauncherHotkey() const;
std::string getTheme() const;
std::string getLanguage() const;
bool getStartupOnLogin() const;

// Launcher bar settings
int getLauncherBarWidth() const;
int getMaxResults() const;

// Actions settings
std::string getDefaultAIProvider() const;
std::string getDefaultAIModel() const;

// Context settings
std::vector<std::string> getEnabledContextProviders() const;
```

## Example Use Cases

### Application Settings

```cpp
// Reading application settings
std::string theme = config.getTheme();
bool startupOnLogin = config.getStartupOnLogin();
std::string launcherHotkey = config.getLauncherHotkey();

// Saving user preferences
config.setString("general.theme", selectedTheme);
config.setBool("general.startup_on_login", shouldLaunchAtLogin);
config.setString("general.launcher_hotkey", selectedHotkey);
config.save();
```

### Launcher Bar Configuration

```cpp
// Reading launcher bar settings
int width = config.getLauncherBarWidth();
int maxResults = config.getMaxResults();
double opacity = config.getDouble("launcher_bar.opacity");
std::string position = config.getString("launcher_bar.position");

// Saving launcher bar settings
config.setInt("launcher_bar.width", selectedWidth);
config.setInt("launcher_bar.max_results", selectedMaxResults);
config.setDouble("launcher_bar.opacity", selectedOpacity);
config.setString("launcher_bar.position", selectedPosition);
config.save();
```

### Context Providers Configuration

```cpp
// Reading context providers settings
std::vector<std::string> enabledProviders = config.getEnabledContextProviders();
int maxContextSize = config.getInt("context.max_context_size");

// Saving context providers settings
config.setStringArray("context.enabled_providers", selectedProviders);
config.setInt("context.max_context_size", selectedMaxContextSize);
config.save();
```

### Settings Window Implementation

```cpp
// Loading settings into UI controls
void loadSettings() {
    ConfigManager& config = ConfigManager::getInstance();

    // General settings
    std::string hotkey = config.getLauncherHotkey();
    hotkeyField.setText(hotkey);

    std::string theme = config.getTheme();
    themeComboBox.setSelectedItem(theme);

    bool startupOnLogin = config.getStartupOnLogin();
    startupOnLoginCheckbox.setChecked(startupOnLogin);

    // Launcher bar settings
    int maxResults = config.getMaxResults();
    maxResultsSlider.setValue(maxResults);

    double opacity = config.getDouble("launcher_bar.opacity");
    opacitySlider.setValue(opacity);
}

// Saving settings from UI controls
void saveSettings() {
    ConfigManager& config = ConfigManager::getInstance();

    // General settings
    config.setString("general.launcher_hotkey", hotkeyField.getText());
    config.setString("general.theme", themeComboBox.getSelectedItem());
    config.setBool("general.startup_on_login", startupOnLoginCheckbox.isChecked());

    // Launcher bar settings
    config.setInt("launcher_bar.max_results", maxResultsSlider.getValue());
    config.setDouble("launcher_bar.opacity", opacitySlider.getValue());

    config.save();
}
```

## Implemented Improvements

The following improvements from the previous suggestions have been implemented:

1. **✅ Configuration Validation**: Added validation for configuration values to ensure they are within acceptable ranges or formats.

2. **✅ Configuration Schema**: Defined a schema for the configuration to document and validate the structure.

3. **✅ Configuration Versioning**: Added version information to the configuration file to handle migrations between versions.

4. **✅ Configuration Categories**: Grouped related configuration keys into categories or sections.

## Future Improvements

The following improvements are still planned:

1. **Configuration Backup**: Implement backup and restore functionality for the configuration file.

2. **Configuration Encryption**: Add encryption for sensitive configuration values.

3. **Configuration Change Notifications**: Implement an observer pattern to notify components when configuration values change.

4. **Configuration UI Generation**: Generate UI controls automatically based on the configuration schema.

5. **Configuration Profiles**: Support multiple configuration profiles that users can switch between.

6. **Configuration Import/Export**: Add functionality to import and export configuration files.
