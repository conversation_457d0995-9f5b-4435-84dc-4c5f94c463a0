#pragma once

#include <string>
#include <vector>
#include <nlohmann/json.hpp>

namespace launcher {
namespace core {

struct ModelConfig {
    std::string id;
    std::string name;           // internal name
    std::string display_name;   // user-visible
    bool supports_tool_calling = false;
    bool supports_image_input = false;
    bool supports_audio_input = false; // STT (Whisper)
    bool supports_image_generation = false;
    bool supports_speech_generation = false;
    bool supports_streaming = true;
    bool supports_reasoning = false;   // High reasoning capability
    bool supports_large_context = false; // ≥128k tokens
    bool supports_multimodal_rt = false; // Real-time vision+audio
    int max_tokens = 0;
    int max_tool_calls = 0;
    double input_token_cost = 0.0;   // USD per 1k tokens
    double output_token_cost = 0.0;
    int priority = 0;  // Higher number = shown earlier in lists
    std::string category;  // Optional group label (e.g., "Flagship", "Vision")
};

struct AIProviderConfig {
    std::string provider_id;    // canonical lowercase key ("openai")
    std::string name;           // user-visible ("OpenAI")
    bool enabled = true;

    // Auth / network
    std::string api_key;            // may be blank => use env var
    std::string api_key_variable;   // fallback env var name
    std::string base_url;

    // Operational settings
    std::string default_model;  // id of model in models[]
    int request_timeout_seconds = 30;
    int rate_limit_requests_per_minute = 60;

    std::vector<ModelConfig> models;
};

// JSON conversion helpers ---------------------------------------------------

inline void to_json(nlohmann::json& j, const ModelConfig& m) {
    j = nlohmann::json{{"id", m.id},
                       {"name", m.name},
                       {"display_name", m.display_name},
                       {"supports_tool_calling", m.supports_tool_calling},
                       {"supports_image_input", m.supports_image_input},
                       {"supports_audio_input", m.supports_audio_input},
                       {"supports_image_generation", m.supports_image_generation},
                       {"supports_speech_generation", m.supports_speech_generation},
                       {"supports_streaming", m.supports_streaming},
                       {"supports_reasoning", m.supports_reasoning},
                       {"supports_large_context", m.supports_large_context},
                       {"supports_multimodal_rt", m.supports_multimodal_rt},
                       {"max_tokens", m.max_tokens},
                       {"max_tool_calls", m.max_tool_calls},
                       {"input_token_cost", m.input_token_cost},
                       {"output_token_cost", m.output_token_cost},
                       {"priority", m.priority},
                       {"category", m.category}};
}

inline void from_json(const nlohmann::json& j, ModelConfig& m) {
    j.at("id").get_to(m.id);
    if (j.contains("name")) j.at("name").get_to(m.name);
    if (j.contains("display_name")) j.at("display_name").get_to(m.display_name);
    if (j.contains("supports_tool_calling")) j.at("supports_tool_calling").get_to(m.supports_tool_calling);
    if (j.contains("supports_image_input")) j.at("supports_image_input").get_to(m.supports_image_input);
    if (j.contains("supports_audio_input")) j.at("supports_audio_input").get_to(m.supports_audio_input);
    if (j.contains("supports_image_generation")) j.at("supports_image_generation").get_to(m.supports_image_generation);
    if (j.contains("supports_speech_generation")) j.at("supports_speech_generation").get_to(m.supports_speech_generation);
    if (j.contains("supports_streaming")) j.at("supports_streaming").get_to(m.supports_streaming);
    if (j.contains("supports_reasoning")) j.at("supports_reasoning").get_to(m.supports_reasoning);
    if (j.contains("supports_large_context")) j.at("supports_large_context").get_to(m.supports_large_context);
    if (j.contains("supports_multimodal_rt")) j.at("supports_multimodal_rt").get_to(m.supports_multimodal_rt);
    if (j.contains("max_tokens")) j.at("max_tokens").get_to(m.max_tokens);
    if (j.contains("max_tool_calls")) j.at("max_tool_calls").get_to(m.max_tool_calls);
    if (j.contains("input_token_cost")) j.at("input_token_cost").get_to(m.input_token_cost);
    if (j.contains("output_token_cost")) j.at("output_token_cost").get_to(m.output_token_cost);
    if (j.contains("priority")) j.at("priority").get_to(m.priority);
    if (j.contains("category")) j.at("category").get_to(m.category);
}

inline void to_json(nlohmann::json& j, const AIProviderConfig& p) {
    j = nlohmann::json{{"provider_id", p.provider_id},
                       {"name", p.name},
                       {"enabled", p.enabled},
                       {"api_key", p.api_key},
                       {"api_key_variable", p.api_key_variable},
                       {"base_url", p.base_url},
                       {"models", p.models},
                       {"default_model", p.default_model},
                       {"request_timeout_seconds", p.request_timeout_seconds},
                       {"rate_limit_requests_per_minute", p.rate_limit_requests_per_minute}};
}

inline void from_json(const nlohmann::json& j, AIProviderConfig& p) {
    j.at("provider_id").get_to(p.provider_id);
    if (j.contains("name")) j.at("name").get_to(p.name);
    if (j.contains("enabled")) j.at("enabled").get_to(p.enabled);
    if (j.contains("api_key")) j.at("api_key").get_to(p.api_key);
    if (j.contains("api_key_variable")) j.at("api_key_variable").get_to(p.api_key_variable);
    if (j.contains("base_url")) j.at("base_url").get_to(p.base_url);
    if (j.contains("models")) j.at("models").get_to(p.models);
    if (j.contains("default_model")) j.at("default_model").get_to(p.default_model);
    if (j.contains("request_timeout_seconds")) j.at("request_timeout_seconds").get_to(p.request_timeout_seconds);
    if (j.contains("rate_limit_requests_per_minute")) j.at("rate_limit_requests_per_minute").get_to(p.rate_limit_requests_per_minute);
}

}  // namespace core
}  // namespace launcher 