#pragma once

#include "../foundation/service_base.h"
#include "../util/expected.h"

namespace launcher::core::config {

class ConfigService final : public foundation::ServiceBase<ConfigService> {
 public:
    static constexpr foundation::ServiceId kId = foundation::ServiceId::kConfigService;

    explicit ConfigService(foundation::ServiceRegistry& registry)
        : foundation::ServiceBase<ConfigService>(registry) {}

    // IService -------------------------------------------------------------
    util::Result<void> start() override { return util::Result<void>::success(); }
    void             stop() noexcept override {}
};

} // namespace launcher::core::config 