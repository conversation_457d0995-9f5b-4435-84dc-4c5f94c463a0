#include "config_manager.h"

#include <filesystem>
#include <fstream>
#include <iostream>
#include <nlohmann/json.hpp>
#include <sstream>
#include <shared_mutex>
#include <atomic>
#include <thread>
#include <chrono>
#include <utility>

#include "../util/debug.h"
#include "ai_provider_config.h"

namespace launcher {
namespace core {

using json = nlohmann::json;

class ConfigManager::Impl {
 public:
    Impl() : configLoaded(false), cache_dirty_(false), save_scheduled_{false} {}

    bool validateConfig() {
        // Basic schema validation
        bool valid = true;

        // Check required top-level properties
        const std::vector<std::string> requiredProperties = {
            "version", "general", "launcher_bar", "default_ai_provider", "context", "performance", "security"};

        for (const auto& prop : requiredProperties) {
            if (!config.contains(prop)) {
                ERR("Missing required property: " << prop);
                valid = false;
            }
        }

        // Check version format
        if (config.contains("version")) {
            if (!config["version"].is_string()) {
                ERR("'version' must be a string");
                valid = false;
            }
            // Could add regex validation for version format here
        }

        // Check general section
        if (config.contains("general") && config["general"].is_object()) {
            const std::vector<std::string> requiredGeneralProps = {"launcher_hotkey", "theme",
                                                                   "language", "startup_on_login"};

            for (const auto& prop : requiredGeneralProps) {
                if (!config["general"].contains(prop)) {
                    ERR("Missing required property general.'" << prop);
                    valid = false;
                }
            }

            // Check theme is one of the allowed values
            if (config["general"].contains("theme")) {
                const auto& theme = config["general"]["theme"];
                if (theme.is_string()) {
                    std::string themeStr = theme.get<std::string>();
                    if (themeStr != "system" && themeStr != "light" && themeStr != "dark") {
                        ERR("'general.theme' must be one of: 'system', 'light', 'dark'");
                        valid = false;
                    }
                } else {
                    ERR("'general.theme' must be a string");
                    valid = false;
                }
            }
        }

        // Similar validation could be added for other sections

        return valid;
    }

    bool loadConfig(const std::string& configPath) {
        std::string path = configPath;
        if (path.empty()) {
            path = getDefaultConfigPath();
        }
        configPath_ = path;

        try {
            if (std::filesystem::exists(path)) {
                std::ifstream file(path);
                if (file.is_open()) {
                    file >> config;

                    // Validate configuration
                    if (!validateConfig()) {
                        ERR("loadConfig: Configuration does not match expected schema. Using anyway.");
                    }

                    configLoaded = true;
                    return true;
                }
            } else {
                // Create default config
                createDefaultConfig();
                configLoaded = true;
                return true;
            }
        } catch (const std::exception& e) {
            // Capture e.what() into a std::string to ensure it's an lvalue for make_format_args
            std::string error_msg = e.what();
            ERR("loadConfig: Error loading config: " << error_msg);
        }

        return false;
    }

    void createDefaultConfig() {
        // Create a default configuration based on config.json
        config = {
            {"version", "1.0.0"},
            {"general",
             {{"launcher_hotkey", "Cmd+Space"},
              {"theme", "system"},
              {"language", "en"},
              {"startup_on_login", true},
              {"check_updates", true},
              {"telemetry_enabled", false}}},
            {"launcher_bar",
             {{"width", 700},
              {"height", 550},
              {"max_results", 50},
              {"animation_speed", "normal"},
              {"font_size", 14},
              {"opacity", 0.95},
              {"position", "center"},
              {"blur_background", true}}},
            {"default_ai_provider", "openai"},
            {"context",
             {{"enabled_providers",
               {"clipboard", "active_window", "selected_text", "current_directory",
                "current_application"}},
              {"max_context_size", 4096}}},
            {"performance",
             {{"memory_limit_mb", 200},
              {"background_process_priority", "low"},
              {"cache_size_mb", 50},
              {"search_cache", {{"enabled", true}, {"max_size", 100}, {"ttl_seconds", 300}}}}},
            {"security", {{"encryption_enabled", true}, {"api_key_storage", "system_keychain"}}},
            {"ai_providers",
             {{{"provider_id", "openai"},
               {"name", "OpenAI"},
               {"enabled", true},
               {"api_key_variable", "OPENAI_API_KEY"},
               {"base_url", "https://api.openai.com/v1"},
               {"models",
                {{{"id", "gpt-4"},
                  {"name", "GPT-4"},
                  {"display_name", "GPT-4"},
                  {"supports_tool_calling", true},
                  {"supports_image_input", true},
                  {"supports_streaming", true},
                  {"max_tokens", 8192},
                  {"max_tool_calls", 15},
                  {"input_token_cost", 0.03},
                  {"output_token_cost", 0.06}},
                 {{"id", "gpt-3.5-turbo"},
                  {"name", "GPT-3.5 Turbo"},
                  {"display_name", "GPT-3.5"},
                  {"supports_tool_calling", true},
                  {"supports_image_input", false},
                  {"supports_streaming", true},
                  {"max_tokens", 4096},
                  {"max_tool_calls", 10},
                  {"input_token_cost", 0.0015},
                  {"output_token_cost", 0.002}}}},
               {"request_timeout_seconds", 30},
               {"rate_limit_requests_per_minute", 60}},
              {{"provider_id", "anthropic"},
               {"name", "Anthropic"},
               {"enabled", true},
               {"api_key_variable", "ANTHROPIC_API_KEY"},
               {"base_url", "https://api.anthropic.com/v1"},
               {"models",
                {{{"id", "claude-3-7-sonnet"},
                  {"name", "Claude 3.7 Sonnet"},
                  {"display_name", "Claude 3.7 Sonnet"},
                  {"supports_tool_calling", true},
                  {"supports_image_input", true},
                  {"supports_streaming", true},
                  {"max_tokens", 24576},
                  {"max_tool_calls", 20},
                  {"input_token_cost", 0.0035},
                  {"output_token_cost", 0.0175}},
                 {{"id", "claude-3-5-sonnet"},
                  {"name", "Claude 3.5 Sonnet"},
                  {"display_name", "Claude 3.5 Sonnet"},
                  {"supports_tool_calling", true},
                  {"supports_image_input", true},
                  {"supports_streaming", true},
                  {"max_tokens", 24576},
                  {"max_tool_calls", 15},
                  {"input_token_cost", 0.003},
                  {"output_token_cost", 0.015}},
                 {{"id", "claude-3-opus"},
                  {"name", "Claude 3 Opus"},
                  {"display_name", "Claude 3 Opus"},
                  {"supports_tool_calling", true},
                  {"supports_image_input", true},
                  {"supports_streaming", true},
                  {"max_tokens", 32768},
                  {"max_tool_calls", 20},
                  {"input_token_cost", 0.015},
                  {"output_token_cost", 0.075}},
                 {{"id", "claude-3-sonnet"},
                  {"name", "Claude 3 Sonnet"},
                  {"display_name", "Claude 3 Sonnet"},
                  {"supports_tool_calling", true},
                  {"supports_image_input", true},
                  {"supports_streaming", true},
                  {"max_tokens", 24576},
                  {"max_tool_calls", 15},
                  {"input_token_cost", 0.003},
                  {"output_token_cost", 0.015}}}},
               {"request_timeout_seconds", 45},
               {"rate_limit_requests_per_minute", 50}}}},
            {"context_providers",
             {{{"provider_id", "clipboard"},
               {"name", "Clipboard"},
               {"enabled", true},
               {"max_content_size", 2048},
               {"refresh_interval_ms", 500},
               {"include_images", false},
               {"privacy",
                {{"mask_sensitive_data", true},
                 {"sensitive_patterns",
                  {"\\b(?:\\d[ -]*?){13,16}\\b",
                   "\\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}\\b"}}}}},
              {{"provider_id", "active_window"},
               {"name", "Active Window"},
               {"enabled", true},
               {"max_content_size", 1024},
               {"refresh_interval_ms", 1000},
               {"privacy",
                {{"mask_sensitive_data", true},
                 {"sensitive_patterns", {"password", "secret", "token", "key"}}}}},
              {{"provider_id", "selected_text"},
               {"name", "Selected Text"},
               {"enabled", true},
               {"max_content_size", 4096},
               {"refresh_interval_ms", 300}},
              {{"provider_id", "current_directory"},
               {"name", "Current Directory"},
               {"enabled", true},
               {"max_content_size", 8192},
               {"refresh_interval_ms", 2000},
               {"include_hidden_files", false}},
              {{"provider_id", "current_application"},
               {"name", "Current Application"},
               {"enabled", true},
               {"refresh_interval_ms", 1000}}}},
            {"platform_specific",
             {{"macos",
               {{"use_native_notifications", true},
                {"status_bar_icon", true},
                {"use_touchbar", true},
                {"scan_applications", true}}},
              {"windows",
               {{"use_native_notifications", true},
                {"tray_icon", true},
                {"scan_start_menu", true}}},
              {"linux",
               {{"use_native_notifications", true},
                {"tray_icon", true},
                {"desktop_environment_integration", true}}}}}};
    }

    bool saveConfig() {
        if (!configLoaded) {
            return false;
        }

        try {
            // Validate before saving
            if (!validateConfig()) {
                ERR("Configuration validation failed. Saving anyway, but this may cause issues.");
            }

            // Create directory if it doesn't exist
            std::filesystem::path configDir = std::filesystem::path(configPath_).parent_path();
            if (!configDir.empty() && !std::filesystem::exists(configDir)) {
                std::filesystem::create_directories(configDir);
            }

            std::ofstream file(configPath_);
            if (file.is_open()) {
                file << std::setw(4) << config << std::endl;
                // Successfully persisted – reset dirty flag
                cache_dirty_ = false;
                return true;
            }
        } catch (const std::exception& e) {
            // Capture e.what() into a std::string to ensure it's an lvalue for make_format_args
            std::string error_msg = e.what();
            ERR("Error saving config: " << error_msg);
        }

        return false;
    }

    std::string getDefaultConfigPath() const {
        std::filesystem::path configDir;

#ifdef _WIN32
        // Windows: %APPDATA%\MicroLauncher
        const char* appData = std::getenv("APPDATA");
        if (appData) {
            configDir = std::filesystem::path(appData) / "MicroLauncher";
        }
#elif defined(__APPLE__)
        // macOS: ~/Library/Application Support/MicroLauncher
        const char* home = std::getenv("HOME");
        if (home) {
            configDir =
                std::filesystem::path(home) / "Library" / "Application Support" / "MicroLauncher";
        }
#else
        // Linux: ~/.config/microlauncher
        const char* home = std::getenv("HOME");
        if (home) {
            configDir = std::filesystem::path(home) / ".config" / "microlauncher";
        }
#endif

        return (configDir / "config.json").string();
    }

    // Nested access methods for structured config
    json getNestedValue(std::string_view path, const json& defaultValue = json()) const {
        try {
            std::vector<std::string_view> parts;
            std::string_view current_sv = path;
            size_t start = 0;
            size_t end = current_sv.find('.');
            while (end != std::string_view::npos) {
                parts.push_back(current_sv.substr(start, end - start));
                start = end + 1;
                end = current_sv.find('.', start);
            }
            parts.push_back(current_sv.substr(start));

            // Navigate through the JSON structure
            const json* current_json = &config;
            for (size_t i = 0; i < parts.size(); ++i) {
                const auto& part_sv = parts[i];

                // Check if this part has array indexing
                size_t open_bracket = part_sv.find('[');
                if (open_bracket != std::string_view::npos) {
                    size_t close_bracket = part_sv.find(']', open_bracket);
                    if (close_bracket != std::string_view::npos) {
                        // Handle array indexing
                        std::string_view array_name_sv = part_sv.substr(0, open_bracket);
                        std::string index_str_std(part_sv.substr(open_bracket + 1, close_bracket - open_bracket - 1));

                        // Check if the array exists
                        if (current_json->is_object() && current_json->contains(array_name_sv)) {
                            const json& array_json = (*current_json)[array_name_sv];

                            if (array_json.is_array()) {
                                try {
                                    int index = std::stoi(index_str_std);
                                    if (index >= 0 && index < static_cast<int>(array_json.size())) {
                                        current_json = &array_json[index];
                                        continue;
                                    } else {
                                        return defaultValue;
                                    }
                                } catch (const std::exception& e) {
                                    // Capture e.what() into a std::string to ensure it's an lvalue for make_format_args
                                    std::string error_msg = e.what();
                                    ERR("getNestedValue: Error parsing array index: " << error_msg);
                                    return defaultValue;
                                }
                            } else {
                                // Not an array, but we'll try to proceed anyway
                                current_json = &array_json;
                            }
                        } else {
                            return defaultValue;
                        }
                    }
                } else if (current_json->is_object() && current_json->contains(part_sv)) {
                    current_json = &(*current_json)[part_sv];
                } else {
                    return defaultValue;
                }
            }

            return *current_json;
        } catch (const std::exception& e) {
            // Capture e.what() into a std::string to ensure it's an lvalue for make_format_args
            std::string error_msg = "Error accessing nested value at " + std::string(path) + ": " + e.what();
            ERR("getNestedValue: " << error_msg);
            return defaultValue;
        }
    }

    void setNestedValue(std::string_view path, const json& value) {
        try {
            std::vector<std::string_view> parts;
            std::string_view current_sv = path;
            size_t start = 0;
            size_t end = current_sv.find('.');
            while (end != std::string_view::npos) {
                parts.push_back(current_sv.substr(start, end - start));
                start = end + 1;
                end = current_sv.find('.', start);
            }
            parts.push_back(current_sv.substr(start));


            if (parts.empty()) {
                return;
            }

            // Navigate and create the path as needed
            json* current_json = &config;
            for (size_t i = 0; i < parts.size() - 1; ++i) {
                const auto& part_sv = parts[i];
                if (!current_json->contains(part_sv) || !(*current_json)[part_sv].is_object()) {
                    (*current_json)[part_sv] = json::object();
                }
                current_json = &(*current_json)[part_sv];
            }

            // Set the value at the final path component
            std::string last_part_str(parts.back());
            (*current_json)[last_part_str] = value;

            // Mark config as dirty so it will be persisted by a future requestSave()
            cache_dirty_ = true;
        } catch (const std::exception& e) {
            // Capture e.what() into a std::string to ensure it's an lvalue for make_format_args
            std::string error_msg = "Error setting nested value at " + std::string(path) + ": " + e.what();
            ERR("setNestedValue: " << error_msg);
        }
    }

    json config;
    std::string configPath_;
    bool configLoaded;
    // New members for provider caching
    mutable std::shared_mutex providers_mutex_;
    bool cache_dirty_;
    std::atomic_bool save_scheduled_{false};
};

// ---------------------------------------------------------------------
// Construction – default initialises pImpl.
// ---------------------------------------------------------------------

ConfigManager::ConfigManager() : pImpl(std::make_unique<Impl>()) {}

ConfigManager::~ConfigManager() {
    DBG("Destroying ConfigManager instance");
}

util::Result<void> ConfigManager::initialize(const std::string& configPath) {
    // Ensure the format string is a literal, pass dynamic parts as arguments
    DBG("Initializing with config path: " + (configPath.empty() ? "default" : configPath));

    // Try to load configuration from the given path
    bool success = pImpl->loadConfig(configPath);

    if (success) {
        return util::Result<void>::success();
    } else {
        DBG("Failed to load config, creating default");
        // Create default configuration
        pImpl->createDefaultConfig();

        // Try to save it
        if (pImpl->saveConfig()) {
            DBG("Successfully saved default config");
            return util::Result<void>::success();
        } else {
            DBG("Failed to save default config");
            return util::Result<void>::failure("Failed to save default configuration");
        }
    }
}

util::Result<void> ConfigManager::save() {
    bool ok = pImpl->saveConfig();
    if (ok) {
        return util::Result<void>::success();
    } else {
        return util::Result<void>::failure("Failed to save configuration");
    }
}

void ConfigManager::requestSave(uint32_t debounce_ms) {
    // Mark dirty immediately so that a pending flush knows there is work.
    pImpl->cache_dirty_ = true;

    // Try to schedule a background flush if one is not already pending.
    bool expected = false;
    if (pImpl->save_scheduled_.compare_exchange_strong(expected, true)) {
        // Spawn detached thread that sleeps for debounce duration then flushes once.
        std::thread([this, debounce_ms]() {
            std::this_thread::sleep_for(std::chrono::milliseconds(debounce_ms));

            // Only flush if still dirty – another thread might have saved already.
            if (pImpl->cache_dirty_) {
                this->save();
            }

            // Allow future schedules.
            pImpl->save_scheduled_ = false;
        }).detach();
    }
}

std::string ConfigManager::getConfigPath() const {
    return pImpl->configPath_;
}

std::string ConfigManager::getString(std::string_view key,
                                     const std::string& defaultValue) const {
    // Removed excessive debug logging

    if (!pImpl->configLoaded) {
        return defaultValue;
    }

    try {
        // Split the key by '.' to navigate nested JSON
        std::vector<std::string_view> keys;
        std::string_view current_key_part = key;
        size_t start = 0;
        size_t end = current_key_part.find('.');
        while (end != std::string_view::npos) {
            keys.push_back(current_key_part.substr(start, end - start));
            start = end + 1;
            end = current_key_part.find('.', start);
        }
        keys.push_back(current_key_part.substr(start));

        // Start with the root and navigate to the requested key
        const json* current = &pImpl->config;

        for (size_t i = 0; i < keys.size(); ++i) {
            const std::string_view k_sv = keys[i];

            // Handle array indexing
            if (k_sv.find('[') != std::string_view::npos && k_sv.find(']') != std::string_view::npos) {
                std::string_view arrayName_sv = k_sv.substr(0, k_sv.find('['));
                std::string indexStr_std(k_sv.substr(k_sv.find('[') + 1, k_sv.find(']') - k_sv.find('[') - 1));
                int index = std::stoi(indexStr_std);

                // Check if the current level has the array
                if (!current->contains(arrayName_sv)) {
                    return defaultValue;
                }

                // Get the array
                const auto& array = (*current)[arrayName_sv];

                // Check if it's an array
                if (!array.is_array()) {
                    return defaultValue;
                }

                // Check if the index is valid
                if (index < 0 || index >= static_cast<int>(array.size())) {
                    return defaultValue;
                }

                // Update current to point to the array element
                current = &array[index];
            } else {
                // Regular object property access
                if (!current->contains(k_sv)) {
                    return defaultValue;
                }

                current = &(*current)[k_sv];
            }
        }

        // Check if the final value is a string
        if (current->is_string()) {
            return current->get<std::string>();
        }

        // If the value is a number or boolean, convert to string
        if (current->is_number() || current->is_boolean()) {
            return current->dump();
        }

        return defaultValue;
    } catch (const std::exception& e) {
        std::cerr << "Error getting string value: " << e.what() << std::endl;
        return defaultValue;
    } catch (...) {
        return defaultValue;
    }
}

int ConfigManager::getInt(std::string_view key, int defaultValue) const {
    try {
        json value = pImpl->getNestedValue(key);
        if (value.is_number_integer()) {
            return value.get<int>();
        }
    } catch (const std::exception& e) {
        std::cerr << "Error getting int value: " << e.what() << std::endl;
    }

    return defaultValue;
}

bool ConfigManager::getBool(std::string_view key, bool defaultValue) const {
    // Removed excessive debug logging

    if (!pImpl->configLoaded) {
        return defaultValue;
    }

    try {
        std::string strVal = getString(key, defaultValue ? "true" : "false");

        // Convert string to lowercase for comparison
        std::transform(strVal.begin(), strVal.end(), strVal.begin(),
                       [](unsigned char c) { return std::tolower(c); });

        if (strVal == "true" || strVal == "1" || strVal == "yes" || strVal == "on") {
            return true;
        } else if (strVal == "false" || strVal == "0" || strVal == "no" || strVal == "off") {
            return false;
        }

        return defaultValue;
    } catch (const std::exception& e) {
        std::cerr << "Error getting bool value: " << e.what() << std::endl;
        return defaultValue;
    } catch (...) {
        return defaultValue;
    }
}

double ConfigManager::getDouble(std::string_view key, double defaultValue) const {
    try {
        json value = pImpl->getNestedValue(key);
        if (value.is_number()) {
            return value.get<double>();
        }
    } catch (const std::exception& e) {
        std::cerr << "Error getting double value: " << e.what() << std::endl;
    }

    return defaultValue;
}

std::vector<std::string> ConfigManager::getStringArray(std::string_view key) const {
    std::vector<std::string> result;

    try {
        json value = pImpl->getNestedValue(key);
        if (value.is_array()) {
            for (const auto& item : value) {
                if (item.is_string()) {
                    result.push_back(item.get<std::string>());
                }
            }
        }
    } catch (const std::exception& e) {
        std::cerr << "Error getting string array: " << e.what() << std::endl;
    }

    return result;
}

void ConfigManager::setString(std::string_view key, const std::string& value) {
    pImpl->setNestedValue(key, value);
}

void ConfigManager::setInt(std::string_view key, int value) {
    pImpl->setNestedValue(key, value);
}

void ConfigManager::setBool(std::string_view key, bool value) {
    pImpl->setNestedValue(key, value);
}

void ConfigManager::setDouble(std::string_view key, double value) {
    pImpl->setNestedValue(key, value);
}

void ConfigManager::setStringArray(std::string_view key, const std::vector<std::string>& value) {
    json array = json::array();
    for (const auto& item : value) {
        array.push_back(item);
    }
    pImpl->setNestedValue(key, array);
}

bool ConfigManager::hasKey(std::string_view key) const {
    // Removed excessive debug logging

    if (!pImpl->configLoaded) {
        return false;
    }

    try {
        // Split the key by '.' to navigate nested JSON
        std::vector<std::string_view> keys;
        std::string_view current_key_part = key;
        size_t start = 0;
        size_t end = current_key_part.find('.');
        while (end != std::string_view::npos) {
            keys.push_back(current_key_part.substr(start, end - start));
            start = end + 1;
            end = current_key_part.find('.', start);
        }
        keys.push_back(current_key_part.substr(start));

        // Start with the root and navigate to the requested key
        const json* current = &pImpl->config;

        for (size_t i = 0; i < keys.size(); ++i) {
            const std::string_view k_sv = keys[i];

            // Handle array indexing
            if (k_sv.find('[') != std::string_view::npos && k_sv.find(']') != std::string_view::npos) {
                std::string_view arrayName_sv = k_sv.substr(0, k_sv.find('['));
                std::string indexStr_std(k_sv.substr(k_sv.find('[') + 1, k_sv.find(']') - k_sv.find('[') - 1));
                int index = std::stoi(indexStr_std);

                // Check if the current level has the array
                if (!current->contains(arrayName_sv)) {
                    return false;
                }

                // Get the array
                const auto& array = (*current)[arrayName_sv];

                // Check if it's an array
                if (!array.is_array()) {
                    return false;
                }

                // Check if the index is valid
                if (index < 0 || index >= static_cast<int>(array.size())) {
                    return false;
                }

                // Update current to point to the array element
                current = &array[index];
            } else {
                // Regular object property access
                if (!current->contains(k_sv)) {
                    return false;
                }

                // If this is the last key, we found it
                if (i == keys.size() - 1) {
                    return true;
                }

                // Otherwise, move to the next level
                current = &(*current)[k_sv];
            }
        }

        // If we got here, we found the key
        return true;
    } catch (const std::exception& e) {
        std::cerr << "Error checking key existence: " << e.what() << std::endl;
        return false;
    } catch (...) {
        return false;
    }
}

int ConfigManager::getArraySize(std::string_view key) const {
    // Removed excessive debug logging

    if (!pImpl->configLoaded) {
        return 0;
    }

    try {
        // Split the key by '.' to navigate nested JSON
        std::vector<std::string_view> keys;
        std::string_view current_key_part = key;
        size_t start = 0;
        size_t end = current_key_part.find('.');
        while (end != std::string_view::npos) {
            keys.push_back(current_key_part.substr(start, end - start));
            start = end + 1;
            end = current_key_part.find('.', start);
        }
        keys.push_back(current_key_part.substr(start));

        // Start with the root and navigate to the requested key
        const json* current = &pImpl->config;

        for (const auto& k_sv : keys) {
            if (!current->contains(k_sv)) {
                return 0;
            }

            current = &(*current)[k_sv];
        }

        // Check if it's an array
        if (current->is_array()) {
            return static_cast<int>(current->size());
        }

        return 0;
    } catch (const std::exception& e) {
        std::cerr << "Error getting array size: " << e.what() << std::endl;
        return 0;
    } catch (...) {
        return 0;
    }
}

// ============================================================================
// New AI provider typed API implementation
// ============================================================================

std::vector<AIProviderConfig> ConfigManager::getAIProviders() const {
    std::shared_lock<std::shared_mutex> lock(pImpl->providers_mutex_);
    std::vector<AIProviderConfig> result;

    if (!pImpl->config.contains("ai_providers")) {
        return result;
    }

    const auto& providers_json = pImpl->config["ai_providers"];
    if (!providers_json.is_array()) {
        // Support object map form {"openai": {...}, ...} by converting to array
        if (providers_json.is_object()) {
            for (auto it = providers_json.begin(); it != providers_json.end(); ++it) {
                AIProviderConfig p;
                from_json(it.value(), p);
                // Ensure provider_id field if missing
                if (p.provider_id.empty()) {
                    p.provider_id = it.key();
                }
                result.push_back(std::move(p));
            }
        }
        return result;
    }

    for (const auto& item : providers_json) {
        try {
            AIProviderConfig p;
            from_json(item, p);
            result.push_back(std::move(p));
        } catch (const std::exception& e) {
            ERR("getAIProviders: Failed to parse provider entry: " << e.what());
        }
    }
    return result;
}

util::Result<void> ConfigManager::replaceAIProviders(const std::vector<AIProviderConfig>& providers) {
    std::unique_lock<std::shared_mutex> lock(pImpl->providers_mutex_);

    nlohmann::json providers_json = nlohmann::json::array();
    for (const auto& p : providers) {
        nlohmann::json j;
        to_json(j, p);
        providers_json.push_back(std::move(j));
    }
    pImpl->config["ai_providers"] = std::move(providers_json);
    pImpl->cache_dirty_ = true;  // mark for save
    return util::Result<void>::success();
}

void ConfigManager::setProviderDefaultModel(std::string_view provider_id_sv,
                                            std::string_view model_id_sv) {
    std::string provider_id(provider_id_sv);
    std::string model_id(model_id_sv);
    auto providers = getAIProviders();
    bool modified = false;
    for (auto& p : providers) {
        if (p.provider_id == provider_id) {
            if (p.default_model != model_id) {
                p.default_model = model_id;
                modified = true;
            }
        }
    }
    if (modified) {
        replaceAIProviders(providers);
        // Persist top-level default provider for quick access.
        setString("default_ai_provider", provider_id);
        requestSave();
    }
}

void ConfigManager::setGlobalDefaultModel(std::string_view provider_id_sv,
                                          std::string_view model_id_sv) {
    // Legacy wrapper – now delegates to per-provider helper.
    setProviderDefaultModel(provider_id_sv, model_id_sv);
}

std::string ConfigManager::getApiKeyFor(std::string_view provider_id_sv) const {
    std::string provider_id(provider_id_sv);
    auto providers = getAIProviders();
    for (const auto &p : providers) {
        if (p.provider_id == provider_id) {
            std::string key = p.api_key;
            if (key.empty() && !p.api_key_variable.empty()) {
                const char *envv = std::getenv(p.api_key_variable.c_str());
                if (envv) key = envv;
            }
            return key;
        }
    }
    return {};
}

std::string ConfigManager::getProviderDefaultModel(std::string_view provider_id_sv) const {
    std::string provider_id(provider_id_sv);
    auto providers = getAIProviders();
    for (const auto& p : providers) {
        if (p.provider_id == provider_id) {
            return p.default_model;
        }
    }
    return {};
}

}  // namespace core
}  // namespace launcher