#pragma once

#include <memory>
#include <optional>
#include <string>
#include <string_view>
#include <unordered_map>
#include <vector>
#include "ai_provider_config.h"
#include "../interfaces/iconfig_manager.h"
#include "../util/result.h"

namespace launcher {
namespace core {

/**
 * @brief Configuration manager
 *
 * This class manages application configuration, including loading and saving
 * configuration from/to disk, and providing access to configuration values.
 */
class ConfigManager : public IConfigManager {
 public:
    // ---------------------------------------------------------------------
    // Construction & lifetime management – no global singleton required.
    // ---------------------------------------------------------------------
    Config<PERSON>anager();
    ~ConfigManager();

    /**
     * @brief Initialize the configuration manager
     *
     * @param configPath Path to the configuration file
     * @return true if initialization was successful, false otherwise
     */
    util::Result<void> initialize(const std::string& configPath = "");

    /**
     * @brief Save the configuration to disk
     *
     * @return true if the configuration was saved successfully, false otherwise
     */
    util::Result<void> save();

    /**
     * @brief Get the path to the configuration file
     *
     * @return The path to the configuration file
     */
    std::string getConfigPath() const;

    /**
     * @brief Get a string value from the configuration
     *
     * @param key Configuration key (can use dot notation for nested values, e.g., "general.theme")
     * @param defaultValue Default value to return if the key is not found
     * @return The configuration value, or the default value if the key is not found
     */
    std::string getString(std::string_view key, const std::string& defaultValue = "") const;

    /**
     * @brief Get an integer value from the configuration
     *
     * @param key Configuration key (can use dot notation for nested values, e.g.,
     * "launcher_bar.width")
     * @param defaultValue Default value to return if the key is not found
     * @return The configuration value, or the default value if the key is not found
     */
    int getInt(std::string_view key, int defaultValue = 0) const;

    /**
     * @brief Get a boolean value from the configuration
     *
     * @param key Configuration key (can use dot notation for nested values, e.g.,
     * "general.startup_on_login")
     * @param defaultValue Default value to return if the key is not found
     * @return The configuration value, or the default value if the key is not found
     */
    bool getBool(std::string_view key, bool defaultValue = false) const;

    /**
     * @brief Get a double value from the configuration
     *
     * @param key Configuration key (can use dot notation for nested values, e.g.,
     * "launcher_bar.opacity")
     * @param defaultValue Default value to return if the key is not found
     * @return The configuration value, or the default value if the key is not found
     */
    double getDouble(std::string_view key, double defaultValue = 0.0) const;

    /**
     * @brief Get a string array from the configuration
     *
     * @param key Configuration key (can use dot notation for nested values, e.g.,
     * "context.enabled_providers")
     * @return The configuration value, or an empty vector if the key is not found
     */
    std::vector<std::string> getStringArray(std::string_view key) const;

    /**
     * @brief Set a string value in the configuration
     *
     * @param key Configuration key (can use dot notation for nested values, e.g., "general.theme")
     * @param value Value to set
     */
    void setString(std::string_view key, const std::string& value);

    /**
     * @brief Set an integer value in the configuration
     *
     * @param key Configuration key (can use dot notation for nested values, e.g.,
     * "launcher_bar.width")
     * @param value Value to set
     */
    void setInt(std::string_view key, int value);

    /**
     * @brief Set a boolean value in the configuration
     *
     * @param key Configuration key (can use dot notation for nested values, e.g.,
     * "general.startup_on_login")
     * @param value Value to set
     */
    void setBool(std::string_view key, bool value);

    /**
     * @brief Set a double value in the configuration
     *
     * @param key Configuration key (can use dot notation for nested values, e.g.,
     * "launcher_bar.opacity")
     * @param value Value to set
     */
    void setDouble(std::string_view key, double value);

    /**
     * @brief Set a string array in the configuration
     *
     * @param key Configuration key (can use dot notation for nested values, e.g.,
     * "context.enabled_providers")
     * @param value Value to set
     */
    void setStringArray(std::string_view key, const std::vector<std::string>& value);

    /**
     * @brief Check if a key exists in the configuration
     *
     * @param key Configuration key (can use dot notation for nested values)
     * @return true if the key exists, false otherwise
     */
    bool hasKey(std::string_view key) const;

    /**
     * @brief Get the size of an array in the configuration
     *
     * @param key Configuration key (can use dot notation for nested values)
     * @return The size of the array, or 0 if the key is not an array or does not exist
     */
    int getArraySize(std::string_view key) const;

    /**
     * @brief Get the launcher hotkey
     *
     * @return The launcher hotkey string
     */
    std::string getLauncherHotkey() const {
        return getString("general.launcher_hotkey", "Alt+Space");
    }

    /**
     * @brief Get the theme setting
     *
     * @return The theme setting ("system", "light", or "dark")
     */
    std::string getTheme() const { return getString("general.theme", "system"); }

    /**
     * @brief Get the language setting
     *
     * @return The language setting (ISO code)
     */
    std::string getLanguage() const { return getString("general.language", "en"); }

    /**
     * @brief Check if the application should start on login
     *
     * @return true if the application should start on login, false otherwise
     */
    bool getStartupOnLogin() const { return getBool("general.startup_on_login", true); }

    /**
     * @brief Get the launcher bar width
     *
     * @return The launcher bar width in pixels
     */
    int getLauncherBarWidth() const { return getInt("launcher_bar.width", 600); }

    /**
     * @brief Get the maximum number of results to display
     *
     * @return The maximum number of results
     */
    int getMaxResults() const { return getInt("launcher_bar.max_results", 50); }

    /**
     * @brief Get the default AI provider
     *
     * @return The default AI provider ID
     */
    std::string getDefaultAIProvider() const {
        return getString("default_ai_provider", "openai");
    }

    // Deprecated: global default model removed – use getProviderDefaultModel instead.
    [[deprecated("Use getProviderDefaultModel() – global default AI model removed")]]
    std::string getDefaultAIModel() const { return ""; }

    /**
     * @brief Get the enabled context providers
     *
     * @return Vector of enabled context provider IDs
     */
    std::vector<std::string> getEnabledContextProviders() const {
        return getStringArray("context.enabled_providers");
    }

    /**
     * @brief Get all configured AI providers.
     *
     * @return Vector of provider configs; empty if none.
     */
    std::vector<AIProviderConfig> getAIProviders() const;

    /**
     * @brief Replace the whole AI provider list and mark config dirty.
     *
     * @param providers New provider list to store.
     * @return true if successful, false otherwise.
     */
    util::Result<void> replaceAIProviders(const std::vector<AIProviderConfig>& providers);

    /**
     * @brief Set the global default provider and model.
     */
    void setGlobalDefaultModel(std::string_view provider_id, std::string_view model_id);

    /**
     * @brief Schedule a debounced save of dirty configuration to disk.
     *
     * Every call marks the configuration as dirty and requests that it be
     * persisted after a short delay.  Multiple calls within the same delay
     * window are coalesced into a single disk write.
     *
     * @param debounce_ms Milliseconds to wait before flushing (default 1000ms)
     */
    void requestSave(uint32_t debounce_ms = 1000);

    /**
     * @brief Resolve API key for a provider.
     *
     * Looks at loaded provider list and environment fallback; returns empty
     * string if none found.
     */
    std::string getApiKeyFor(std::string_view provider_id) const;

    // ---------------------------------------------------------------------
    // Per-provider default model helpers
    // ---------------------------------------------------------------------
    std::string getProviderDefaultModel(std::string_view provider_id) const;
    void setProviderDefaultModel(std::string_view provider_id, std::string_view model_id);

 private:
    ConfigManager(const ConfigManager&) = delete;
    ConfigManager& operator=(const ConfigManager&) = delete;

    class Impl;
    std::unique_ptr<Impl> pImpl;
};

}  // namespace core
}  // namespace launcher