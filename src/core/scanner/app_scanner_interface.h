#pragma once

#include <chrono>
#include <memory>
#include <string>
#include <vector>

namespace launcher {
namespace core {

// Forward declarations
class AppIndex;

/**
 * @brief Result of an application scan
 */
struct AppResult {
    std::string name;
    std::string path;
    std::string iconPath;
    float score;
    std::vector<std::string> keywords;

    AppResult() : score(0.0f) {}

    AppResult(const std::string& name, const std::string& path, const std::string& iconPath,
              float score = 1.0f)
        : name(name), path(path), iconPath(iconPath), score(score) {}

    // Allow sorting by score (descending)
    bool operator<(const AppResult& other) const { return score > other.score; }
};

/**
 * @brief Interface for application scanners
 *
 * This interface defines the methods that platform-specific
 * application scanners must implement to discover applications
 * and add them to the application index.
 */
class AppScannerInterface : public std::enable_shared_from_this<AppScannerInterface> {
 public:
    virtual ~AppScannerInterface() = default;

    /**
     * @brief Scan the system for applications and add them to the index
     *
     * @param index The application index to populate
     * @return int Number of applications added to the index
     */
    virtual int scanApplications(AppIndex& index) = 0;

    /**
     * @brief Check for new or updated applications since the last scan
     *
     * @param index The application index to update
     * @return int Number of applications added or updated
     */
    virtual int updateApplications(AppIndex& index) = 0;

    /**
     * @brief Scan for all applications and return them
     *
     * @return std::vector<AppResult> List of all applications found
     */
    virtual std::vector<AppResult> scanForApplications() = 0;

    /**
     * @brief Scan for all applications (alias for scanForApplications)
     *
     * @return std::vector<AppResult> List of all applications found
     */
    std::vector<AppResult> scanForApps() { return scanForApplications(); }

    /**
     * @brief Get information about an application from its file path
     *
     * @param path Path to the application file
     * @return AppResult Information about the application
     */
    virtual AppResult getApplicationInfo(const std::string& path) = 0;

    /**
     * @brief Check if a file is an application file
     *
     * @param path Path to the file
     * @return true if the file is an application file
     */
    virtual bool isApplicationFile(const std::string& path) = 0;

    /**
     * @brief Get the directories where applications are stored
     *
     * @return std::vector<std::string> List of application directories
     */
    virtual std::vector<std::string> getApplicationDirectories() = 0;

    /**
     * @brief Get the last scan time
     *
     * @return std::chrono::system_clock::time_point The time of the last scan
     */
    virtual std::chrono::system_clock::time_point getLastScanTime() const = 0;

    /**
     * @brief Create a platform-specific scanner instance
     *
     * @return std::shared_ptr<AppScannerInterface> A shared pointer to the scanner
     */
    static std::shared_ptr<AppScannerInterface> create();
};

}  // namespace core
}  // namespace launcher