#pragma once

#include <chrono>
#include <string>
#include <unordered_set>
#include <vector>

#include "../../index/app_index.h"
#include "../app_scanner_interface.h"

namespace launcher {
namespace core {

/**
 * @brief macOS implementation of the application scanner
 *
 * This class uses the LaunchServices API to discover applications
 * installed on the system and adds them to the application index.
 */
class MacOSAppScanner : public AppScannerInterface {
 public:
    MacOSAppScanner();
    ~MacOSAppScanner() override;

    /**
     * @brief Scan the system for applications and add them to the index
     *
     * @param index The application index to populate
     * @return int Number of applications added to the index
     */
    int scanApplications(AppIndex& index) override;

    /**
     * @brief Check for new or updated applications since the last scan
     *
     * @param index The application index to update
     * @return int Number of applications added or updated
     */
    int updateApplications(AppIndex& index) override;

    /**
     * @brief Scan for all applications and return them
     *
     * @return std::vector<AppResult> List of all applications found
     */
    std::vector<AppResult> scanForApplications() override;

    /**
     * @brief Get information about an application from its file path
     *
     * @param path Path to the application file
     * @return AppResult Information about the application
     */
    AppResult getApplicationInfo(const std::string& path) override;

    /**
     * @brief Check if a file is an application file
     *
     * @param path Path to the file
     * @return true if the file is an application file
     */
    bool isApplicationFile(const std::string& path) override;

    /**
     * @brief Get the directories where applications are stored
     *
     * @return std::vector<std::string> List of application directories
     */
    std::vector<std::string> getApplicationDirectories() override;

    /**
     * @brief Get the last scan time
     *
     * @return std::chrono::system_clock::time_point The time of the last scan
     */
    std::chrono::system_clock::time_point getLastScanTime() const override;

 private:
    // Time of the last scan
    std::chrono::system_clock::time_point last_scan_time_;

    // Set of application bundle IDs that have been scanned
    std::unordered_set<std::string> scanned_app_ids_;

    // Additional directories to scan for applications
    std::vector<std::string> additional_app_directories_;

    // Scan a specific directory for .app bundles
    int scanDirectory(const std::string& directory, AppIndex& index);

    // Scan a specific directory for .app bundles and add them to results
    void scanDirectoryForResults(const std::string& directory, std::vector<AppResult>& results);

    // Extract application information from a bundle
    bool extractAppInfo(const std::string& bundle_path, AppEntry& app_entry);

    // Generate a unique ID for an application
    std::string generateAppId(const std::string& bundle_id, const std::string& path);
};

}  // namespace core
}  // namespace launcher