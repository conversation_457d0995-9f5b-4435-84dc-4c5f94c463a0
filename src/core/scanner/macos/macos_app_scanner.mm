#include "macos_app_scanner.h"
#include "../../index/app_index.h"
#include "../../util/debug.h"

#include <algorithm>
#include <filesystem>
#include <iostream>
#include <sstream>

// macOS specific headers
#import <Foundation/Foundation.h>
#import <AppKit/AppKit.h>

namespace launcher {
namespace core {

// Helper function to convert NSString to std::string
static std::string NSStringToStdString(NSString* nsString) {
    if (!nsString) { return {}; }

    // Fast-path: UTF8String returns non-null C string for ASCII/UTF-8 content.
    const char* cstr = [nsString UTF8String];
    if (cstr) {
        return std::string(cstr, std::strlen(cstr));
    }

    // Slow path – UTF8String returned nullptr (e.g. conversion failed). Fallback by
    // creating NSData in UTF-8 and copying its bytes explicitly. This avoids UB
    // from passing nullptr into std::string ctor and guarantees accurate length.
    NSData* data = [nsString dataUsingEncoding:NSUTF8StringEncoding];
    if (!data) { return {}; }
    return std::string(reinterpret_cast<const char*>(data.bytes), data.length);
}

// Helper function to convert std::string to NSString
static NSString* StdStringToNSString(const std::string& stdString) {
    return [NSString stringWithUTF8String:stdString.c_str()];
}

MacOSAppScanner::MacOSAppScanner() {
    // Initialize with standard application directories
    additional_app_directories_ = {
        "/Applications",
        "/System/Applications",
        // "/System/Library/CoreServices",
        // "/Users/<USER>/Applications"
    };
    
    // Add user's Applications folder
    NSString* userAppsPath = [NSString stringWithFormat:@"%@/Applications", 
                             NSHomeDirectory()];
    additional_app_directories_.push_back(NSStringToStdString(userAppsPath));
    
    // Initialize last scan time to now
    last_scan_time_ = std::chrono::system_clock::now();
}

MacOSAppScanner::~MacOSAppScanner() {
    // Clean up resources if needed
}

int MacOSAppScanner::scanApplications(AppIndex& index) {
    // Use the new updateApplications method
    auto apps = scanForApplications();
    index.updateApplications(shared_from_this());
    return static_cast<int>(apps.size());
}

int MacOSAppScanner::updateApplications(AppIndex& index) {
    // For simplicity, just do a full scan for now
    return scanApplications(index);
}

std::vector<AppResult> MacOSAppScanner::scanForApplications() {
    std::vector<AppResult> results;
    
    // Clear the set of scanned app IDs for a fresh scan
    scanned_app_ids_.clear();
    
    // // Use LaunchServices to get all applications
    // NSWorkspace* workspace = [NSWorkspace sharedWorkspace];
    // NSArray<NSRunningApplication*>* runningApps = [workspace runningApplications];
    
    // // First, scan running applications
    // for (NSRunningApplication* app in runningApps) {
    //     NSString* bundleID = [app bundleIdentifier];
    //     NSString* bundlePath = [[app bundleURL] path];
        
    //     if (bundleID && bundlePath) {
    //         AppEntry app_entry;
    //         std::string path = NSStringToStdString(bundlePath);
            
    //         if (extractAppInfo(path, app_entry)) {
    //             // Create AppResult from AppEntry fields
    //             results.emplace_back(app_entry.name, app_entry.path, app_entry.iconPath);
    //             scanned_app_ids_.insert(app_entry.id);
    //         }
    //     }
    // }
    
    // Then scan additional directories for .app bundles
    for (const auto& dir : additional_app_directories_) {
        scanDirectoryForResults(dir, results);
    }
    
    // Update the last scan time
    last_scan_time_ = std::chrono::system_clock::now();
    
    return results;
}

AppResult MacOSAppScanner::getApplicationInfo(const std::string& path) {
    AppEntry app_entry;
    
    if (extractAppInfo(path, app_entry)) {
        // Create AppResult from AppEntry fields
        return AppResult(app_entry.name, app_entry.path, app_entry.iconPath);
    }
    
    return AppResult();
}

bool MacOSAppScanner::isApplicationFile(const std::string& path) {
    // Check if the path ends with .app
    if (path.size() >= 4 && path.substr(path.size() - 4) == ".app") {
        return true;
    }
    
    // Check if it's an executable inside an .app bundle
    std::filesystem::path fs_path(path);
    auto parent_path = fs_path.parent_path();
    
    while (!parent_path.empty()) {
        if (parent_path.extension() == ".app") {
            return true;
        }
        parent_path = parent_path.parent_path();
    }
    
    return false;
}

std::vector<std::string> MacOSAppScanner::getApplicationDirectories() {
    return additional_app_directories_;
}

std::chrono::system_clock::time_point MacOSAppScanner::getLastScanTime() const {
    return last_scan_time_;
}

int MacOSAppScanner::scanDirectory(const std::string& directory, AppIndex& index) {
    // Use the scanDirectoryForResults method and update the index
    std::vector<AppResult> results;
    scanDirectoryForResults(directory, results);
    
    // This is a simplified approach; in a real implementation, we would
    // add each application to the index individually
    return static_cast<int>(results.size());
}

void MacOSAppScanner::scanDirectoryForResults(const std::string& directory, std::vector<AppResult>& results) {
    try {
        // Check if directory exists
        if (!std::filesystem::exists(directory)) {
            return;
        }
        
        // Check if the directory itself is an app bundle
        std::filesystem::path dir_path(directory);
        if (dir_path.extension() == ".app") {
            // If the directory is an app bundle, process it directly
            std::string app_path = directory;
            
            // Skip if we've already processed this app
            bool already_processed = false;
            for (const auto& id : scanned_app_ids_) {
                if (id.find(app_path) != std::string::npos) {
                    already_processed = true;
                    break;
                }
            }
            
            if (!already_processed) {
                AppEntry app_entry;
                if (extractAppInfo(app_path, app_entry)) {
                    // Create AppResult from AppEntry fields
                    results.emplace_back(app_entry.name, app_entry.path, app_entry.iconPath);
                    scanned_app_ids_.insert(app_entry.id);
                }
            }
            return;
        }
        
        // For non-app directories, scan the directory (non-recursively first)
        for (const auto& entry : std::filesystem::directory_iterator(directory)) {
            if (entry.is_directory()) {
                std::string path = entry.path().string();
                
                // If it's an app bundle, process it
                if (entry.path().extension() == ".app") {
                    // Skip if we've already processed this app
                    bool already_processed = false;
                    for (const auto& id : scanned_app_ids_) {
                        if (id.find(path) != std::string::npos) {
                            already_processed = true;
                            break;
                        }
                    }
                    
                    if (!already_processed) {
                        AppEntry app_entry;
                        if (extractAppInfo(path, app_entry)) {
                            // Create AppResult from AppEntry fields
                            results.emplace_back(app_entry.name, app_entry.path, app_entry.iconPath);
                            scanned_app_ids_.insert(app_entry.id);
                        }
                    }
                } else {
                    // If it's a normal directory, recursively scan it
                    scanDirectoryForResults(path, results);
                }
            }
        }
    } catch (const std::exception& e) {
        // Use the renamed error logging function
        std::string directory_copy = directory;
        std::string error_what = e.what();
        NSString* directory_ns = [NSString stringWithUTF8String:directory_copy.c_str()];
        NSString* error_what_ns = [NSString stringWithUTF8String:error_what.c_str()];
        ERM(@"Error scanning directory %@: %@", directory_ns, error_what_ns);
    }
}

bool MacOSAppScanner::extractAppInfo(const std::string& bundle_path, AppEntry& app_entry) {
    @autoreleasepool {
        NSString* bundlePath = StdStringToNSString(bundle_path);
        NSBundle* bundle = [NSBundle bundleWithPath:bundlePath];
        
        if (!bundle) {
            return false;
        }
        
        // Get bundle information
        NSString* bundleID = [bundle bundleIdentifier];
        NSString* displayName = [bundle objectForInfoDictionaryKey:@"CFBundleDisplayName"];
        
        // If display name is not available, use bundle name
        if (!displayName) {
            displayName = [bundle objectForInfoDictionaryKey:@"CFBundleName"];
        }
        
        // If still no name, use the last component of the path
        if (!displayName) {
            displayName = [[bundlePath lastPathComponent] stringByDeletingPathExtension];
        }
        
        // Get executable path
        NSString* executablePath = [bundle executablePath];
        
        // Get icon path
        NSString* iconName = [bundle objectForInfoDictionaryKey:@"CFBundleIconFile"];
        NSString* iconPath = @"";
        
        if (iconName && [iconName length] > 0) {
            // If the icon name doesn't have an extension, add .icns
            if (![iconName pathExtension].length) {
                iconName = [iconName stringByAppendingPathExtension:@"icns"];
            }
            
            // Look for the icon in the Resources directory
            iconPath = [bundle pathForResource:[iconName stringByDeletingPathExtension]
                                        ofType:[iconName pathExtension]];
        }
        
        // Generate a unique ID for the app
        std::string id = bundleID ? generateAppId(NSStringToStdString(bundleID), bundle_path)
                                  : generateAppId("", bundle_path);
        
        // Fill in the app entry
        app_entry.id = id;
        app_entry.name = displayName ? NSStringToStdString(displayName) : "";
        // Store the bundle path so LaunchServices opens the application properly rather
        // than directly executing the internal binary. This ensures correct activation
        // behaviour (single Dock icon, sandbox, Apple-Events, etc.).
        app_entry.path = bundle_path;
        app_entry.iconPath = iconPath ? NSStringToStdString(iconPath) : "";
        
        // Add keywords for better search
        app_entry.keywords.clear();
        
        // Add bundle ID as a keyword if available
        if (bundleID) {
            app_entry.keywords.push_back(NSStringToStdString(bundleID));
        }
        
        // Add bundle name components as keywords
        NSArray* bundleIDComponents = [bundleID componentsSeparatedByString:@"."];
        for (NSString* component in bundleIDComponents) {
            std::string keyword = NSStringToStdString(component);
            if (!keyword.empty() && keyword != "app" && keyword != "com" && keyword != "org") {
                app_entry.keywords.push_back(keyword);
            }
        }
        
        return true;
    }
}

std::string MacOSAppScanner::generateAppId(const std::string& bundle_id, const std::string& path) {
    if (!bundle_id.empty()) {
        return bundle_id;
    }
    
    // If no bundle ID, generate one from the path
    std::stringstream ss;
    ss << "generated.";
    
    // Extract the app name from the path
    std::filesystem::path fs_path(path);
    std::string app_name = fs_path.filename().string();
    
    // Remove .app extension if present
    size_t dot_pos = app_name.rfind(".app");
    if (dot_pos != std::string::npos) {
        app_name = app_name.substr(0, dot_pos);
    }
    
    // Convert to lowercase and remove spaces
    std::transform(app_name.begin(), app_name.end(), app_name.begin(),
                   [](unsigned char c) { return std::tolower(c); });
    app_name.erase(std::remove(app_name.begin(), app_name.end(), ' '), app_name.end());
    
    ss << app_name << "." << std::hash<std::string>{}(path);
    return ss.str();
}

// Factory method implementation for AppScannerInterface
std::shared_ptr<AppScannerInterface> AppScannerInterface::create() {
#ifdef __APPLE__
    return std::make_shared<MacOSAppScanner>();
#else
    // Return nullptr or a default implementation for unsupported platforms
    return nullptr;
#endif
}

} // namespace core
} // namespace launcher 