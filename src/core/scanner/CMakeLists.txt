# Scanner module CMakeLists.txt

# Add source files based on platform
if(APPLE)
    set(SCANNER_SOURCES
        app_scanner_interface.h
        macos/macos_app_scanner.h
        macos/macos_app_scanner.mm
    )
    
    # Set Objective-C++ properties for .mm files
    set_source_files_properties(
        macos/macos_app_scanner.mm
        PROPERTIES
        COMPILE_FLAGS "-x objective-c++"
    )
else()
    # else/other platforms unsupported
endif()

# Add sources to the core library
target_sources(core PRIVATE ${SCANNER_SOURCES})

# Include directories
target_include_directories(core PRIVATE ${CMAKE_CURRENT_SOURCE_DIR})

# Platform-specific libraries
if(APPLE)
    find_library(FOUNDATION_FRAMEWORK Foundation REQUIRED)
    find_library(APPKIT_FRAMEWORK AppKit REQUIRED)
    target_link_libraries(core PRIVATE ${FOUNDATION_FRAMEWORK} ${APPKIT_FRAMEWORK})
endif() 