#pragma once

#include <functional>
#include <string>
#include <unordered_map>
#include <vector>
#include <memory>
#include "core/util/expected.h"
#include "http_error.h"
#include <coroutine>
#include "core/util/stream_generator.h"
#include "sse_decoder.h"
#include <string_view>

// Forward declare CancellationToken
namespace launcher {
namespace core {
namespace utilities {
class CancellationToken;
}
}
}

// Logging control macros
#ifdef NDEBUG
// Release mode - disable all debug logging
#define HTTP_LOG_ERROR(...) ((void)0)
#define HTTP_LOG_WARN(...) ((void)0)
#define HTTP_LOG_INFO(...) ((void)0)
#define HTTP_LOG_DEBUG(...) ((void)0)
#define HTTP_LOG_TRACE(...) ((void)0)
#define HTTP_LOG_NETWORK(...) ((void)0)
#define HTTP_LOG_PERFORMANCE(...) ((void)0)
#define HTTP_LOG_HTTP2(...) ((void)0)
#define HTTP_LOG_MEMORY(...) ((void)0)
#else
// Debug mode - enable conditionally based on log level/category
#define HTTP_LOG_ERROR(client, msg) (client)->log(LogLevel::ERROR, LOG_NETWORK, msg)
#define HTTP_LOG_WARN(client, msg) (client)->log(LogLevel::WARN, LOG_NETWORK, msg)
#define HTTP_LOG_INFO(client, msg) (client)->log(LogLevel::INFO, LOG_NETWORK, msg)
#define HTTP_LOG_DEBUG(client, msg) (client)->log(LogLevel::DEBUG, LOG_NETWORK, msg)
#define HTTP_LOG_TRACE(client, msg) (client)->log(LogLevel::TRACE, LOG_NETWORK, msg)
#define HTTP_LOG_NETWORK(client, msg) (client)->log(LogLevel::DEBUG, LOG_NETWORK, msg)
#define HTTP_LOG_PERFORMANCE(client, msg) (client)->log(LogLevel::DEBUG, LOG_PERFORMANCE, msg)
#define HTTP_LOG_HTTP2(client, msg) (client)->log(LogLevel::DEBUG, LOG_HTTP2, msg)
#define HTTP_LOG_MEMORY(client, msg) (client)->log(LogLevel::DEBUG, LOG_MEMORY, msg)
#endif

// Platform-specific includes
#ifdef __APPLE__
// Forward declaration of Objective-C classes to avoid including Foundation.h in header
#ifdef __OBJC__
@class HttpStreamingDelegate;
#else
// When included from C++ code
class HttpStreamingDelegate;
#endif
#else
// Windows and other platforms includes will go here
#include <memory>
class HttpStreamingDelegate;
#endif

namespace http {

/**
 * Callback type for streaming HTTP data
 */
using DataCallback = std::function<void(std::string_view)>;

/**
 * Response structure to hold HTTP response data
 */
struct Response {
    int status_code = 0;
    std::unordered_map<std::string, std::string> headers;
    std::string body;
};

using HttpResult = launcher::core::util::Expected<Response, HttpError>;

/**
 * Log level enumeration
 */
enum class LogLevel {
    NONE = 0,   // No logging
    ERROR = 1,  // Error messages only
    WARN = 2,   // Warnings and errors
    INFO = 3,   // General information plus warnings and errors
    DEBUG = 4,  // Detailed debug information
    TRACE = 5   // Very verbose tracing information
};

/**
 * Log category flags for selective logging
 */
enum LogCategory {
    LOG_NONE = 0,
    LOG_NETWORK = 1 << 0,      // Network-related logs
    LOG_PERFORMANCE = 1 << 1,  // Performance metrics
    LOG_HTTP2 = 1 << 2,        // HTTP/2 specific information
    LOG_MEMORY = 1 << 3,       // Memory management
    LOG_ALL = 0xFFFF           // All categories
};

struct HttpRequestAwaitable; // defined in http_request_awaitable.h

/**
 * A HTTP client class that provides functionality for making HTTP requests,
 * including streaming capabilities.
 */
class HttpClient {
 public:
    /**
     * Constructor
     */
    HttpClient();

    /**
     * Destructor
     */
    ~HttpClient();

    /**
     * Sets default headers for all requests
     *
     * @param headers Map of header names to values
     */
    void setDefaultHeaders(const std::unordered_map<std::string, std::string>& headers);

    /**
     * Add a specific default header
     *
     * @param name Header name
     * @param value Header value
     */
    void addDefaultHeader(std::string_view name, std::string_view value);

    /**
     * Get the default headers
     */
    const std::unordered_map<std::string, std::string>& getDefaultHeaders() const {
        return defaultHeaders_;
    }

    /**
     * Sets the default timeout for requests in seconds
     *
     * @param timeout_seconds Timeout value in seconds
     */
    void setTimeout(int timeout_seconds);

    /**
     * Configure batch processing for streaming requests
     *
     * @param batch_size Size threshold in bytes for triggering a batch (0 = no batching)
     * @param flush_interval Maximum time to wait before flushing a batch in seconds (0 = no
     * time-based flushing)
     */
    void configureBatchProcessing(size_t batch_size, double flush_interval = 0.0);

    /**
     * Configure memory pooling for streaming requests to reduce allocations
     *
     * @param use_pooling Whether to use memory pooling (default = true)
     * @param pool_size Number of data objects to keep in the pool (default = 10)
     */
    void configureMemoryPooling(bool use_pooling = true, size_t pool_size = 10);

    /**
     * Configure adaptive batching for streaming requests
     *
     * @param use_adaptive_batching Whether to use adaptive batching (default = true)
     * @param min_batch_size Minimum batch size in bytes (default = 1024)
     * @param max_batch_size Maximum batch size in bytes (default = 131072)
     * @param adaptive_factor Factor for adaptive adjustments (0.0-1.0, default = 0.2)
     */
    void configureAdaptiveBatching(bool use_adaptive_batching = true, size_t min_batch_size = 1024,
                                   size_t max_batch_size = 131072, double adaptive_factor = 0.2);

    /**
     * Checks if a host is reachable
     *
     * @param host The hostname to check
     * @param timeout_seconds Timeout for the check in seconds
     * @return true if the host is reachable, false otherwise
     */
    bool checkConnectivity(std::string_view host, int timeout_seconds = 10);

    /**
     * Performs a GET request
     *
     * @param url The URL to request
     * @param headers Optional additional headers for this request
     * @return Response object containing status code, headers, and body
     */
    HttpResult get(std::string_view url,
                   const std::unordered_map<std::string, std::string>& headers = {});

    /**
     * Performs a POST request
     *
     * @param url The URL to request
     * @param body The request body
     * @param headers Optional additional headers for this request
     * @return Response object containing status code, headers, and body
     */
    HttpResult post(std::string_view url, std::string_view body,
                    const std::unordered_map<std::string, std::string>& headers = {});

    /**
     * Performs a PUT request
     *
     * @param url The URL to request
     * @param body The request body
     * @param headers Optional additional headers for this request
     * @return Response object containing status code, headers, and body
     */
    HttpResult put(std::string_view url, std::string_view body,
                   const std::unordered_map<std::string, std::string>& headers = {});

    /**
     * Performs a DELETE request
     *
     * @param url The URL to request
     * @param headers Optional additional headers for this request
     * @return Response object containing status code, headers, and body
     */
    HttpResult del(std::string_view url,
                   const std::unordered_map<std::string, std::string>& headers = {});

    /**
     * Performs a streaming GET request
     *
     * @param url The URL to request
     * @param callback The callback to invoke with each chunk of data
     * @param headers Optional additional headers for this request
     * @return Response object containing final status code and error info if any
     */
    HttpResult streamingGet(
        std::string_view url, const DataCallback& callback,
        std::shared_ptr<::launcher::core::utilities::CancellationToken> cancellationToken = nullptr,
        const std::unordered_map<std::string, std::string>& headers = {});

    /**
     * Performs a streaming POST request with SSE support
     *
     * @param url The URL to request
     * @param body The request body
     * @param callback The callback to invoke with each chunk of data
     * @param headers Optional additional headers for this request
     * @return Response object containing final status code and error info if any
     */
    HttpResult streamingPost(std::string_view url, std::string_view body,
                             const DataCallback& callback,
                             std::shared_ptr<::launcher::core::utilities::CancellationToken> cancellationToken,
                             const std::unordered_map<std::string, std::string>& headers = {});

    /**
     * Helper function to escape JSON string values
     *
     * @param input The string to escape
     * @return The escaped string
     */
    static std::string escapeJsonString(std::string_view input);

    /**
     * Configure HTTP/2 usage for the client
     *
     * @param use_http2 Whether to use HTTP/2 when available (true by default)
     * @param allow_fallback Whether to allow fallback to HTTP/1.1 when HTTP/2 is not supported
     */
    void configureHttp2(bool use_http2 = true, bool allow_fallback = true);

    /**
     * Check if HTTP/2 is being used for connections
     *
     * @return true if HTTP/2 is enabled and in use
     */
    bool isUsingHttp2() const;

    /**
     * Get performance metrics for the client
     *
     * @return Map of performance metrics
     */
    std::unordered_map<std::string, double> getPerformanceMetrics() const;

    /**
     * Configure logging settings
     *
     * @param log_level Maximum log level to display
     * @param log_categories Categories to enable (bitmask of LogCategory flags)
     * @param use_async Whether to use asynchronous logging (true by default)
     */
    void configureLogging(LogLevel log_level = LogLevel::ERROR, int log_categories = LOG_NETWORK,
                          bool use_async = true);

    /**
     * Set log sampling rate for high-frequency logs
     *
     * @param sampling_rate Log 1 message per N messages (0 = no sampling)
     * @param category_mask Which log categories to apply sampling to
     */
    void setLogSamplingRate(int sampling_rate, int category_mask = LOG_ALL);

    // ---------------------- Async / Coroutine API ---------------------

    // Fire-and-forget request that delivers result via callback. Does not block.
    void requestAsync(std::string_view url, std::string_view method, std::string_view body,
                      const std::unordered_map<std::string, std::string>& headers,
                      const std::function<void(HttpResult)>& completion,
                      std::shared_ptr<::launcher::core::utilities::CancellationToken> token = nullptr);

    // Convenience helpers that return awaitable for direct `co_await`.
    HttpRequestAwaitable coRequest(std::string_view url, std::string_view method,
                                   std::string_view body = "",
                                   const std::unordered_map<std::string, std::string>& headers = {},
                                   std::shared_ptr<::launcher::core::utilities::CancellationToken> token = nullptr);

    ::launcher::core::util::AsyncStream<std::string_view> streamChunks(
        std::string_view url,
        std::string_view method,
        std::string_view body = "",
        const std::unordered_map<std::string, std::string>& headers = {},
        std::shared_ptr<::launcher::core::utilities::CancellationToken> cancellationToken = nullptr);

    // High-level parsed stream producing Delta tokens.
    ::launcher::core::util::AsyncStream<http::Delta> streamDeltas(std::string_view provider_id,
                                                                  std::string_view url,
                                                                  std::string_view method,
                                                                  std::shared_ptr<::launcher::core::utilities::CancellationToken> token,
                                                                  std::string_view body = "",
                                                                  const std::unordered_map<std::string, std::string>& headers = {});

    // Coroutine-friendly streaming APIs (non-blocking, zero-copy)
    ::launcher::core::util::AsyncStream<std::string_view> coStreamingGet(
        std::string_view url,
        std::shared_ptr<::launcher::core::utilities::CancellationToken> cancellationToken = nullptr,
        const std::unordered_map<std::string, std::string>& headers = {});

    ::launcher::core::util::AsyncStream<std::string_view> coStreamingPost(
        std::string_view url,
        std::string_view body,
        std::shared_ptr<::launcher::core::utilities::CancellationToken> cancellationToken = nullptr,
        const std::unordered_map<std::string, std::string>& headers = {});

 private:
    /**
     * Performs a non-streaming HTTP request
     *
     * @param url The URL to request
     * @param method The HTTP method to use
     * @param body The request body (optional)
     * @param headers Additional headers for this request
     * @return Response object containing status code, headers, and body
     */
    HttpResult request(std::string_view url, std::string_view method,
                       std::string_view body = "",
                       const std::unordered_map<std::string, std::string>& headers = {});

    /**
     * Performs a streaming HTTP request with SSE support
     *
     * @param url The URL to request
     * @param method The HTTP method to use
     * @param body The request body (optional)
     * @param callback The callback to invoke with each chunk of data
     * @param headers Additional headers for this request
     * @return Response object containing final status code and error info if any
     */
    HttpResult streamingRequest(std::string_view url, std::string_view method,
                                std::string_view body, const DataCallback& callback,
                                std::shared_ptr<::launcher::core::utilities::CancellationToken> cancellationToken,
                                const std::unordered_map<std::string, std::string>& headers = {});

    // Member variables
    std::unordered_map<std::string, std::string> defaultHeaders_;
    int timeoutSeconds_;
    size_t batchSize_;
    double flushInterval_;
    bool useHttp2_;            // Whether to use HTTP/2 when available
    bool allowHttp2Fallback_;  // Whether to allow fallback to HTTP/1.1 when HTTP/2 is not supported

    // Memory pooling configuration
    bool useMemoryPooling_;
    size_t memoryPoolSize_;

    // Adaptive batching configuration
    bool useAdaptiveBatching_;
    size_t minBatchSize_;
    size_t maxBatchSize_;
    double adaptiveFactor_;

    // Logging configuration
    LogLevel logLevel_;
    int logCategories_;
    bool useAsyncLogging_;
    int logSamplingRate_;
    int logSamplingMask_;

    // Counters for log sampling – fixed array up to 16 categories for O(1) access
    static constexpr int kMaxCategories = 16;
    mutable std::array<int, kMaxCategories> logCounters_{};

    static int categoryIndex(int category) {
        if (category == 0) return -1;
        int idx = __builtin_ctz(category); // position of first set bit
        return (idx < kMaxCategories) ? idx : -1;
    }

    // Log message to appropriate output based on settings
    void log(LogLevel level, int category, std::string_view message) const;

    // Helper for log sampling
    bool shouldLog(int category) const;

    // ---------------------------------------------------------------------
    //  Platform-specific state is stored in an opaque implementation struct
    //  defined in the corresponding *.mm / *.cpp file for each platform.
    //  This removes Objective-C and Win32 types from the public header and
    //  eliminates the need for conditional compilation in client code.
    // ---------------------------------------------------------------------

    struct Impl;                      // forward declaration
    std::unique_ptr<Impl> impl_;      // PIMPL pointer shared across platforms
    void* delegateMux_ {nullptr};     // HttpDelegateMux*
};

}  // namespace http

// -----------------------------------------------------------------------------
// Platform support guard
// -----------------------------------------------------------------------------
// Currently we have full implementations for macOS (__APPLE__) and Windows
// (_WIN32).  Other platforms only contain placeholder stubs in the .cpp file
// which should NOT be shipped in production.  Trigger a compile-time error so
// downstream users become aware immediately.
#if !defined(__APPLE__) && !defined(_WIN32)
#error "HttpClient: platform not supported – please provide a real implementation"
#endif