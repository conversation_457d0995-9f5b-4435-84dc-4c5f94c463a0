#pragma once

#ifdef __APPLE__
#include <Foundation/Foundation.h>

#include <functional>
#include <string>
#include <vector>
#include <string_view>

namespace http {

/**
 * Forward declaration of the http::DataCallback type
 */
using DataCallback = std::function<void(std::string_view)>;

/**
 * Callback type for streaming request completion
 */
using CompletionCallback = std::function<void(int statusCode, NSError* error)>;

}  // namespace http

// --- Added C++ Forward Declaration ---
namespace launcher { namespace core { namespace utilities {
class CancellationToken;
}}}
// --- End Added C++ Forward Declaration ---

/**
 * Delegate interface for handling HTTP streaming responses
 * This class handles the NSURLSessionDataDelegate callbacks and processes SSE data
 */
@interface HttpStreamingDelegate : NSObject <NSURLSessionDataDelegate, NSURLSessionTaskDelegate>
{
    // --- Added CancellationToken Instance Variable ---
    std::shared_ptr<launcher::core::utilities::CancellationToken> _cancellationToken;
    // --- End Added CancellationToken Instance Variable ---
}

@property(nonatomic, assign) int statusCode;
@property(nonatomic, assign) BOOL completed;
@property(nonatomic, assign) BOOL receivedData;
@property(nonatomic, assign) BOOL streamingComplete;
@property(nonatomic, strong) NSURLSessionDataTask* dataTask;
@property(nonatomic, strong) dispatch_queue_t callbackQueue;

// Batch processing properties
@property(nonatomic, strong) NSMutableData* dataBuffer;
@property(nonatomic, assign) NSUInteger batchSize;
@property(nonatomic, strong) NSTimer* flushTimer;

// Memory pooling properties
@property(nonatomic, assign) BOOL useMemoryPooling;
@property(nonatomic, assign) NSUInteger memoryPoolSize;
@property(nonatomic, assign) BOOL useAdaptiveBatching;
@property(nonatomic, assign) double adaptiveFactor;
@property(nonatomic, assign) NSUInteger maxBatchSize;
@property(nonatomic, assign) NSUInteger minBatchSize;

// Memory usage tracking
@property(nonatomic, assign) NSUInteger totalBytesProcessed;
@property(nonatomic, assign) NSUInteger totalAllocations;
@property(nonatomic, assign) double averageBatchSize;
@property(nonatomic, assign) NSUInteger messageCount;

// HTTP/2 metrics tracking
@property(nonatomic, assign) BOOL collectMetrics;
@property(nonatomic, assign) BOOL usingHttp2;

// String conversion optimization
@property(nonatomic, assign) BOOL optimizeStringConversions;

// Logging properties
@property(nonatomic, assign) int logLevel;
@property(nonatomic, assign) int logCategories;
@property(nonatomic, assign) int logSamplingRate;
@property(nonatomic, assign) BOOL useAsyncLogging;

// SSE parsing properties
@property(nonatomic, strong) NSMutableData* sseBuffer;

// Buffer to capture response body for non-2xx status codes
@property(nonatomic, strong) NSMutableData* errorBodyBuffer;

/**
 * Initializes the delegate with a callback function
 * @param callback The function to call with each chunk of data
 * @return The initialized delegate
 */
- (instancetype)initWithCallback:(http::DataCallback)callback;

/**
 * Initializes the delegate with callbacks for data, completion, and cancellation token
 * @param callback The function to call with each chunk of data
 * @param completionCallback The function to call when streaming completes or fails
 * @param cancellationToken Shared pointer to the C++ cancellation token
 * @return The initialized delegate
 */
- (instancetype)initWithCallback:(http::DataCallback)callback
              completionCallback:(http::CompletionCallback)completionCallback
             cancellationToken:(std::shared_ptr<launcher::core::utilities::CancellationToken>)cancellationToken;

/**
 * Configure batch processing parameters
 * @param batchSize The size threshold in bytes that triggers a batch flush (0 = no batching)
 * @param flushInterval The maximum time to wait before flushing the buffer in seconds (0 = no
 * time-based flush)
 */
- (void)configureBatchProcessing:(NSUInteger)batchSize flushInterval:(NSTimeInterval)flushInterval;

/**
 * Configure adaptive batching parameters
 * @param useAdaptive Whether to use adaptive batching
 * @param minSize Minimum batch size in bytes
 * @param maxSize Maximum batch size in bytes
 * @param adaptiveFactor Factor for adaptive adjustments (0.0-1.0)
 */
- (void)configureAdaptiveBatching:(BOOL)useAdaptive
                          minSize:(NSUInteger)minSize
                          maxSize:(NSUInteger)maxSize
                   adaptiveFactor:(double)adaptiveFactor;

/**
 * Configure memory pooling
 * @param usePooling Whether to use memory pooling
 * @param poolSize The number of data objects to keep in the pool
 */
- (void)configureMemoryPooling:(BOOL)usePooling poolSize:(NSUInteger)poolSize;

/**
 * Manually flush the current data buffer
 */
- (void)flushBuffer;

/**
 * Get performance metrics as a dictionary
 * @return Dictionary with performance metrics
 */
- (NSDictionary*)getPerformanceMetrics;

/**
 * Cancels the current data task
 */
- (void)cancel;

/**
 * Determines if HTTP/2 is being used for the current connection
 * @return YES if HTTP/2 is being used, NO otherwise
 */
- (BOOL)isUsingHttp2;

/**
 * Sets whether to collect metrics for protocol detection
 * @param collect YES to collect metrics, NO otherwise
 */
- (void)setCollectMetrics:(BOOL)collect;

/**
 * Configure logging settings
 * @param level Log level (0=None, 1=Error, 2=Warn, 3=Info, 4=Debug, 5=Trace)
 * @param categories Bitmask of log categories to enable
 * @param samplingRate Log 1 out of N messages (0 = log all)
 * @param useAsyncLogging Whether to log asynchronously
 */
- (void)setLogLevel:(int)level
         categories:(int)categories
       samplingRate:(int)samplingRate
    useAsyncLogging:(BOOL)useAsyncLogging;

/**
 * Log a message with the configured settings
 * @param level Log level
 * @param category Log category
 * @param message The message to log
 */
- (void)log:(int)level category:(int)category message:(NSString*)message;

/**
 * Check if a message should be logged based on level, category and sampling
 * @param level Log level
 * @param category Log category
 * @return YES if the message should be logged, NO otherwise
 */
- (BOOL)shouldLog:(int)level category:(int)category;

/**
 * Process the internal SSE buffer to extract complete events
 * Internal method that handles SSE framing correctly
 */
- (void)processSSEBuffer;

@end

#endif  // __APPLE__