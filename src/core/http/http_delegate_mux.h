#pragma once

#ifdef __APPLE__
#import <Foundation/Foundation.h>

@class HttpStreamingDelegate;

/**
 * HttpDelegateMux – one per HttpClient. Acts as NSURLSession delegate and
 * forwards callbacks to per-request HttpStreamingDelegate registered against
 * the NSURLSessionTask. Enables reuse of a single NSURLSession while still
 * having per-request delegate logic.
 */
@interface HttpDelegateMux : NSObject <NSURLSessionDataDelegate, NSURLSessionTaskDelegate>

- (void)registerDelegate:(HttpStreamingDelegate*)delegate forTask:(NSURLSessionTask*)task;

@end

#endif 