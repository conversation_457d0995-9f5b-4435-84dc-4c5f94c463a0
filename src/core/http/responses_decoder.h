#pragma once

#include <string_view>
#include <variant>
#include <optional>
#include <memory>

namespace launcher { namespace core { namespace memory { class MemorySink; }}}

namespace http {

enum class ResponseKind {
    kStarted,
    kAssistantDelta,
    kThoughtDelta,
    kImageChunk,
    kImageDone,
    kToolCall,
    kOutputItem,
    kMcpApproval,
    kCompleted,
    kError,
    kUnknown
};

struct AssistantDelta { std::string_view text; };
struct ThoughtDelta   { std::string_view text; };
struct ImageChunk     { std::string_view b64; uint32_t index{}; };
struct ImageDone      {};
struct ToolCall       { std::string_view json; };
struct OutputItem     { std::string_view json; };
struct McpApproval    { std::string_view json; };
struct ErrorEvt       { std::string_view code; std::string_view message; };
struct StartedMeta    { std::string_view model; double created_at{0}; };

using ResponsePayload = std::variant<std::monostate,
                                     AssistantDelta,
                                     ThoughtDelta,
                                     ImageChunk,
                                     ImageDone,
                                     ToolCall,
                                     OutputItem,
                                     McpApproval,
                                     ErrorEvt,
                                     StartedMeta>;

struct ResponseEvent {
    ResponseKind kind{ResponseKind::kUnknown};
    ResponsePayload payload{};
};

class ResponsesDecoder {
public:
    virtual ~ResponsesDecoder() = default;
    virtual void setSink(std::shared_ptr<launcher::core::memory::MemorySink> sink) { sink_ = std::move(sink); }
    virtual std::optional<ResponseEvent> feed(std::string_view raw_line) = 0;

protected:
    std::shared_ptr<launcher::core::memory::MemorySink> sink_;
};

// Factory (provider id is lowercase e.g. "openai")
std::unique_ptr<ResponsesDecoder> createResponsesDecoder(const std::string& provider_id);

} // namespace http 