# HTTP Client Library

A modern C++ HTTP client for macOS/iOS with streaming support based on NSURLSession.

## Features

-   Synchronous and asynchronous HTTP requests
-   Support for all common HTTP methods (GET, POST, PUT, DELETE)
-   HTTP streaming with Server-Sent Events (SSE) support
-   Customizable headers and timeouts
-   Automatic error handling
-   JSON string escaping utilities
-   Connectivity testing

## Usage

### Basic Requests

```cpp
#include "http_client.h"
#include <iostream>

int main() {
    // Create client instance
    http::HttpClient client;

    // Set default timeout (in seconds)
    client.setTimeout(30);

    // Add default headers for all requests
    client.addDefaultHeader("User-Agent", "MyApp/1.0");

    // Perform a GET request (using the Expected-based API)
    http::HttpResult result = client.get("https://example.com/api/resource");

    if (result) { // success path
        const http::Response& response = *result;
        std::cout << "Status code: " << response.status_code << std::endl;
        std::cout << "Response body: " << response.body << std::endl;

        // Access response headers
        for (const auto& header : response.headers) {
            std::cout << header.first << ": " << header.second << std::endl;
        }
    } else { // failure path
        const http::HttpError& err = result.error();
        std::cerr << "Error (" << err.status_code << "): " << err.message << std::endl;
    }

    // POST request with custom headers
    http::HttpResult postRes = client.post(
        "https://example.com/api/resource",
        "{\"key\":\"value\"}",
        {{"Content-Type", "application/json"}}
    );

    return 0;
}
```

### Streaming Requests

```cpp
#include "http_client.h"
#include <iostream>

int main() {
    http::HttpClient client;

    // Create a callback to process streaming data chunks
    auto streamingCallback = [](const std::string& chunk) {
        std::cout << "Received data: " << chunk << std::endl;
        // Process each chunk as it arrives
    };

    // Perform a streaming GET request
    http::HttpResult response = client.streamingGet(
        "https://example.com/api/stream",
        streamingCallback
    );

    // Streaming POST with Server-Sent Events
    http::HttpResult streamingPost = client.streamingPost(
        "https://example.com/api/stream",
        "{\"prompt\":\"tell me a story\"}",
        streamingCallback,
        {
            {"Content-Type", "application/json"},
            {"Accept", "text/event-stream"}
        }
    );

    return 0;
}
```

## Building

The HTTP client is built using CMake:

```bash
mkdir -p build && cd build
cmake ..
cmake --build .
```

To run the test example:

```bash
./build/src/http/http_client_test
```

## Implementation Details

-   Platform-specific implementation using NSURLSession for Apple platforms
-   Uses dispatch semaphores for synchronous API over async system APIs
-   Specially handles SSE format parsing for streaming responses
-   Designed to work well with long-running streaming connections

## Requirements

-   macOS 10.12+ or iOS
-   C++17 compatible compiler
-   CMake 3.10+
