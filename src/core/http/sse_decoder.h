#pragma once

#include <optional>
#include <string>
#include <string_view>
#include <memory>
#include "core/memory/memory_sink.h"

namespace http {

/**
 * High-level token yielded by SSE decoders.
 */
struct Delta {
    std::string role;            // e.g. "assistant"; may be empty.
    std::string_view content;    // view into external buffer (arena)
    bool done = false;           // true when stream signals completion
};

/**
 * Abstract Server-Sent-Events decoder. Feed raw text chunks (each may contain
 * one or multiple SSE "data:" lines) and obtain structured Delta tokens.
 */
class SSEDecoder {
 public:
    virtual ~SSEDecoder() = default;

    // Provide sink for decoder to append text; may be nullptr.
    virtual void setSink(std::shared_ptr<launcher::core::memory::MemorySink> sink) { sink_ = std::move(sink); }
    // Backward-compat overload accepting raw pointer (non-owning)
    void setSink(launcher::core::memory::MemorySink* sink) {
        sink_ = std::shared_ptr<launcher::core::memory::MemorySink>(sink, [](auto*){});
    }

    // Parse |raw_line|; return std::nullopt if nothing to emit yet. Accepts non-owning view.
    virtual std::optional<Delta> feed(std::string_view raw_line) = 0;

    // Factory based on provider id (lowercase: "openai", "anthropic" …)
    static std::unique_ptr<SSEDecoder> create(const std::string& provider_id);

    bool hasError() const noexcept { return parse_error_; }

 protected:
    std::shared_ptr<launcher::core::memory::MemorySink> sink_;
    bool parse_error_ = false;
};

}  // namespace http 