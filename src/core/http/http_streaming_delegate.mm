#ifdef __APPLE__
#include "http_streaming_delegate.h"
#include "http_client.h"
#include "../util/debug.h"
#include "../../core/util/debug.h"
#include <iostream>
#include <vector>
#include <deque>
#include <numeric>
#include <chrono>
#include <iomanip>
#include <sstream>
#include <string>
#include <string_view>

// Define delegate logging macros that can be disabled in release builds
#ifdef NDEBUG
    #define DELEGATE_LOG(level, category, message)    ((void)0)
#else
    #define DELEGATE_LOG(level, category, message)    if (self.logLevel >= level && ((category) == 0 || (self.logCategories & (category)))) { NSLog(@"[%@] %@", (level == 1 ? @"ERROR" : (level == 2 ? @"WARN" : (level == 3 ? @"INFO" : (level == 4 ? @"DEBUG" : @"TRACE")))), message); }
#endif

// Global logging queue shared with HttpClient
static dispatch_queue_t getSharedLoggingQueue() {
    static dispatch_queue_t loggingQueue = nullptr;
    static dispatch_once_t onceToken;
    
    dispatch_once(&onceToken, ^{
        loggingQueue = dispatch_queue_create("com.httpclient.sharedLoggingQueue", DISPATCH_QUEUE_SERIAL);
    });
    
    return loggingQueue;
}

// Helper for string formatting
static std::string formatLogMessage(int level, const char* message) {
    // Format the log message with timestamp and level prefix
    auto now = std::chrono::system_clock::now();
    auto now_time_t = std::chrono::system_clock::to_time_t(now);
    auto now_ms = std::chrono::duration_cast<std::chrono::milliseconds>(
        now.time_since_epoch()).count() % 1000;
    
    std::stringstream msg;
    msg << "[" << std::put_time(std::localtime(&now_time_t), "%Y-%m-%d %H:%M:%S") 
        << "." << std::setfill('0') << std::setw(3) << now_ms << "] ";
    
    // Add level prefix
    switch (level) {
        case 1: msg << "[ERROR] "; break;
        case 2: msg << "[WARN] "; break;
        case 3: msg << "[INFO] "; break;
        case 4: msg << "[DEBUG] "; break;
        case 5: msg << "[TRACE] "; break;
        default: break;
    }
    
    // Add the message
    msg << message;
    
    return msg.str();
}

#if __has_feature(objc_arc)
#define SAFE_RETAIN(obj) ((void)0)
#define SAFE_RELEASE(obj) ((void)0)
#define DISPATCH_RELEASE_IF_NEEDED(q) ((void)0)
#else
#define SAFE_RETAIN(obj) [obj retain]
#define SAFE_RELEASE(obj) [obj release]
#define DISPATCH_RELEASE_IF_NEEDED(q) dispatch_release(q)
#endif

@implementation HttpStreamingDelegate {
    http::DataCallback callback_;
    http::CompletionCallback completionCallback_;
    BOOL hasCompletionCallback_;
    NSTimeInterval flushInterval_;
    BOOL _collectMetrics;
    BOOL _usingHttp2;
    
    // Logging state
    int _logLevel;
    int _logCategories;
    int _logSamplingRate;
    BOOL _useAsyncLogging;
    NSMutableDictionary* _logCounters;
    
    // Memory pool for NSData objects
    NSMutableArray* _dataPool;
    NSLock* _poolLock;
    
    // Adaptive batching tracking
    NSDate* _lastFlushTime;
    NSMutableArray* _recentBatchSizes;
    NSMutableArray* _processingTimes;
    dispatch_source_t _adaptiveTimer;
    
    // Smart boundary detection
    BOOL _lastChunkEndsWithBoundary;
    
    // Reusable buffer for event data
    std::vector<char> _eventDataBuffer;
    
    // Buffer to capture raw response body for error status codes (HTTP >= 400)
    NSMutableData* _errorBodyBuffer;
}

- (instancetype)initWithCallback:(http::DataCallback)callback {
    self = [super init];
    if (self) {
        callback_ = callback;
        hasCompletionCallback_ = NO;
        _statusCode = 0;
        _completed = NO;
        _receivedData = NO;
        _streamingComplete = NO;
        _collectMetrics = NO;
        _usingHttp2 = NO;
        
        // Create a dedicated serial queue for processing callbacks
        _callbackQueue = dispatch_queue_create("com.httpclient.callbackQueue", DISPATCH_QUEUE_SERIAL);
        
        // Initialize batching properties with defaults
        _dataBuffer = [[NSMutableData alloc] init];
        _batchSize = 0;  // No batching by default
        flushInterval_ = 0;  // No timer-based flush by default
        
        // Initialize buffer to capture raw response body for error status codes (HTTP >= 400)
        _errorBodyBuffer = [[NSMutableData alloc] init];
        
        // Initialize memory pooling with defaults
        _useMemoryPooling = NO;
        _memoryPoolSize = 10;
        _dataPool = [[NSMutableArray alloc] initWithCapacity:_memoryPoolSize];
        _poolLock = [[NSLock alloc] init];
        
        // Initialize adaptive batching
        _useAdaptiveBatching = NO;
        _adaptiveFactor = 0.2;
        _minBatchSize = 1024;     // 1 KB minimum
        _maxBatchSize = 1024*128; // 128 KB maximum
        _lastFlushTime = [[NSDate alloc] init];
        _recentBatchSizes = [[NSMutableArray alloc] initWithCapacity:10];
        _processingTimes = [[NSMutableArray alloc] initWithCapacity:10];
        
        // Initialize metrics
        _totalBytesProcessed = 0;
        _totalAllocations = 0;
        _averageBatchSize = 0;
        _messageCount = 0;
        
        // Initialize boundary detection
        _lastChunkEndsWithBoundary = NO;
        
        // Pre-allocate event data buffer
        _eventDataBuffer.reserve(4096);
        
        // Initialize logging properties with defaults
        _logLevel = 1;  // ERROR by default
        _logCategories = (1 << 0);  // LOG_NETWORK by default
        _logSamplingRate = 0;  // No sampling by default
        _useAsyncLogging = YES;  // Async by default
        _logCounters = [[NSMutableDictionary alloc] init];
    }
    return self;
}

- (instancetype)initWithCallback:(http::DataCallback)callback
               completionCallback:(http::CompletionCallback)completionCallback
              cancellationToken:(std::shared_ptr<launcher::core::utilities::CancellationToken>)cancellationToken {
    self = [self initWithCallback:callback];
    if (self) {
        completionCallback_ = completionCallback;
        hasCompletionCallback_ = YES;
    }
    return self;
}

- (void)configureMemoryPooling:(BOOL)usePooling poolSize:(NSUInteger)poolSize {
    _useMemoryPooling = usePooling;
    _memoryPoolSize = poolSize;
    
    // Pre-populate the pool with reusable data objects
    if (_useMemoryPooling && _dataPool.count < _memoryPoolSize) {
        [_poolLock lock];
        
        while (_dataPool.count < _memoryPoolSize) {
            NSMutableData* data = [[NSMutableData alloc] initWithCapacity:4096];
            [_dataPool addObject:data];
        }
        
        [_poolLock unlock];
    }
}

- (void)configureAdaptiveBatching:(BOOL)useAdaptive 
                          minSize:(NSUInteger)minSize 
                          maxSize:(NSUInteger)maxSize 
                   adaptiveFactor:(double)adaptiveFactor {
    _useAdaptiveBatching = useAdaptive;
    _minBatchSize = minSize;
    _maxBatchSize = maxSize;
    _adaptiveFactor = adaptiveFactor;
    
    // Setup adaptive timer - runs every second to adjust the batch size
    if (_useAdaptiveBatching && !_adaptiveTimer) {
        _adaptiveTimer = dispatch_source_create(DISPATCH_SOURCE_TYPE_TIMER, 0, 0, _callbackQueue);
        dispatch_source_set_timer(_adaptiveTimer, 
                                  dispatch_time(DISPATCH_TIME_NOW, 1 * NSEC_PER_SEC),
                                  1 * NSEC_PER_SEC, 0.1 * NSEC_PER_SEC);
        
        dispatch_source_set_event_handler(_adaptiveTimer, ^{
            [self adjustBatchSizeBasedOnPerformance];
        });
        
        dispatch_resume(_adaptiveTimer);
    }
}

- (void)configureBatchProcessing:(NSUInteger)batchSize flushInterval:(NSTimeInterval)flushInterval {
    _batchSize = batchSize;
    flushInterval_ = flushInterval;
    
    // Stop any existing timer
    if (_flushTimer) {
        [_flushTimer invalidate];
        _flushTimer = nil;
    }
    
    // Set up flush timer if an interval is specified
    if (flushInterval > 0) {
        _flushTimer = [NSTimer scheduledTimerWithTimeInterval:flushInterval
                                                       target:self
                                                     selector:@selector(timerFlushedBuffer:)
                                                     userInfo:nil
                                                      repeats:YES];
    }
}

- (NSMutableData*)getPooledDataObject {
    if (!_useMemoryPooling) {
        return [[NSMutableData alloc] init]; // No autorelease in manual reference counting
    }
    
    NSMutableData* data = nil;
    
    [_poolLock lock];
    if (_dataPool.count > 0) {
        data = [_dataPool lastObject]; // Get reference from pool
        SAFE_RETAIN(data); // Retain when not under ARC
        [_dataPool removeLastObject];
    }
    [_poolLock unlock];
    
    if (!data) {
        data = [[NSMutableData alloc] init]; // Create new, owned object
        _totalAllocations++;
    }
    
    return data; // Returned object has +1 retain count
}

- (void)returnDataObjectToPool:(NSMutableData*)data {
    if (!_useMemoryPooling || !data) {
        SAFE_RELEASE(data); // Release if not using pool
        return;
    }
    
    // Reset the data object for reuse
    [data setLength:0];
    
    [_poolLock lock];
    // Only keep up to _memoryPoolSize objects in the pool
    if (_dataPool.count < _memoryPoolSize) {
        [_dataPool addObject:data]; // Add to pool without changing retain count
        SAFE_RELEASE(data); // Release our ownership, pool now owns it
    } else {
        // Pool is full, just release it
        SAFE_RELEASE(data);
    }
    [_poolLock unlock];
}

- (void)adjustBatchSizeBasedOnPerformance {
    if (!_useAdaptiveBatching || _recentBatchSizes.count == 0) {
        return;
    }
    
    // Average recent batch sizes
    double avgBatchSize = 0.0;
    for (NSNumber* size in _recentBatchSizes) {
        avgBatchSize += [size doubleValue];
    }
    avgBatchSize /= _recentBatchSizes.count;
    
    // Average processing times
    double avgProcessingTime = 0.0;
    if (_processingTimes.count > 0) {
        for (NSNumber* time in _processingTimes) {
            avgProcessingTime += [time doubleValue];
        }
        avgProcessingTime /= _processingTimes.count;
    }
    
    // Calculate optimal batch size based on processing time
    // If processing is fast, increase batch size. If slow, decrease
    NSUInteger currentBatchSize = _batchSize;
    
    if (avgProcessingTime < 0.005) {  // Less than 5ms
        // Processing is fast, increase batch size
        currentBatchSize = (NSUInteger)(currentBatchSize * (1.0 + _adaptiveFactor));
    } else if (avgProcessingTime > 0.020) {  // More than 20ms
        // Processing is slow, decrease batch size
        currentBatchSize = (NSUInteger)(currentBatchSize * (1.0 - _adaptiveFactor));
    }
    
    // Ensure within bounds
    currentBatchSize = MAX(_minBatchSize, MIN(_maxBatchSize, currentBatchSize));
    
    // Update batch size
    _batchSize = currentBatchSize;
    
    // Keep metrics arrays from growing too large
    while (_recentBatchSizes.count > 10) {
        [_recentBatchSizes removeObjectAtIndex:0];
    }
    while (_processingTimes.count > 10) {
        [_processingTimes removeObjectAtIndex:0];
    }
    
    // Update average batch size metric
    _averageBatchSize = avgBatchSize;
}

- (void)timerFlushedBuffer:(NSTimer*)timer {
    dispatch_async(_callbackQueue, ^{
        if (_dataBuffer.length > 0) {
            [self processDataBuffer:_dataBuffer];
            [_dataBuffer setLength:0];
        }
    });
}

- (void)flushBuffer {
    // Streaming now handled fully in C++ layer; nothing to flush.
    dispatch_async(_callbackQueue, ^{ (void)self; });
}

- (void)processDataBuffer:(NSData*)buffer {
    if (!buffer || buffer.length == 0 || !callback_) {
        return;
    }
    
    // Track metrics
    _totalBytesProcessed += buffer.length;
    [_recentBatchSizes addObject:@(buffer.length)];
    
    // Track processing time
    NSDate* startTime = [NSDate date];
    
    @autoreleasepool {
        const char* bytes = reinterpret_cast<const char*>(buffer.bytes);
        std::string_view sv(bytes, buffer.length);
        if (!sv.empty()) {
            callback_(sv);
            _messageCount++;
        }
    }
    
    // Calculate processing time
    NSTimeInterval processingTime = -[startTime timeIntervalSinceNow];
    [_processingTimes addObject:@(processingTime)];
}

- (NSDictionary*)getPerformanceMetrics {
    return @{
        @"totalBytesProcessed": @(_totalBytesProcessed),
        @"totalAllocations": @(_totalAllocations),
        @"averageBatchSize": @(_averageBatchSize),
        @"messageCount": @(_messageCount),
        @"currentBatchSize": @(_batchSize),
        @"usingHttp2": @(_usingHttp2),
        @"usingMemoryPooling": @(_useMemoryPooling),
        @"usingAdaptiveBatching": @(_useAdaptiveBatching)
    };
}

- (void)URLSession:(NSURLSession *)session
          dataTask:(NSURLSessionDataTask *)dataTask
didReceiveResponse:(NSURLResponse *)response
 completionHandler:(void (^)(NSURLSessionResponseDisposition))completionHandler {
    
    if ([response isKindOfClass:[NSHTTPURLResponse class]]) {
        NSHTTPURLResponse *httpResponse = (NSHTTPURLResponse *)response;
        _statusCode = (int)httpResponse.statusCode;
        
        // Log response headers at DEBUG level using the new logging system
        if ([self shouldLog:4 category:(1 << 0)]) {  // 4 = DEBUG, (1 << 0) = LOG_NETWORK
            NSDictionary* headers = [httpResponse allHeaderFields];
            NSMutableString* headerStr = [NSMutableString string];
            [headerStr appendFormat:@"Response headers for %@ - Status: %d\n", 
                      [[response URL] absoluteString], _statusCode];
            
            for (NSString* key in headers) {
                [headerStr appendFormat:@"  %@: %@\n", key, [headers objectForKey:key]];
            }
            
            DELEGATE_LOG(4, (1 << 0), headerStr);
        }
    }
    
    // Allow the session to continue receiving data
    completionHandler(NSURLSessionResponseAllow);
}

- (void)URLSession:(NSURLSession *)session 
          dataTask:(NSURLSessionDataTask *)dataTask 
    didReceiveData:(NSData *)data {
    // Track that we're receiving data
    _receivedData = YES;

    // +++ Add check here +++
    if (_streamingComplete) {
        DELEGATE_LOG(4, (1 << 0), @"didReceiveData: Ignoring data after stream completion.");
        return; 
    }
    // +++ End check +++

    if (!data || data.length == 0) {
        return;
    }
    
    // +++ Capture error body for non-success HTTP status codes +++
    if (_statusCode >= 400) {
        if (data && data.length > 0) {
            [_errorBodyBuffer appendData:data];
        }
        // No need to process further (not SSE)
        return;
    }
    // +++ End capture +++

    // Fast-path: forward raw chunk to the C++ callback with a single copy.
    dispatch_async(_callbackQueue, ^{
        if (callback_) {
            // Copy bytes into an std::string so the memory remains valid for the
            // duration of the callback. Down-stream code will move/append into its
            // own arena immediately, so this temporary is short-lived.
            std::string chunkStr(reinterpret_cast<const char*>([data bytes]), [data length]);
            callback_(std::string_view(chunkStr));
            // chunkStr is destroyed right after callback_ returns.
        }
    });
    
    // Log data chunk received at TRACE level with sampling
    if ([self shouldLog:5 category:(1 << 0)]) {  // 5 = TRACE, (1 << 0) = LOG_NETWORK
        NSString* logMessage = [NSString stringWithFormat:@"Received data chunk: %lu bytes", 
                     (unsigned long)data.length];
        DELEGATE_LOG(5, (1 << 0), logMessage);
    }
}

- (void)URLSession:(NSURLSession *)session
              task:(NSURLSessionTask *)task
didCompleteWithError:(NSError *)error {
    _completed = YES;
    
    if (error) {
        // Check if the error is cancellation AND the token requested it
        bool wasCancelledByToken = false;
        if ([error.domain isEqualToString:NSURLErrorDomain] && error.code == NSURLErrorCancelled) {
            if (_cancellationToken && _cancellationToken->isCancelled()) {
                 DBM(@"Task cancelled by CancellationToken.");
                wasCancelledByToken = true;
            }
        }
        
        // Log the error appropriately
        if (!wasCancelledByToken) {
            NSString* logMessage = [NSString stringWithFormat:@"ERROR: %@", [error localizedDescription]];
            DELEGATE_LOG(1, (1 << 0), logMessage); // Log as ERROR if not cancelled by token
        }
    }
    
    // Flush any remaining data in the buffer
    [self flushBuffer];
    
    // Stop the adaptive timer if active
    if (_adaptiveTimer) {
        dispatch_source_cancel(_adaptiveTimer);
        _adaptiveTimer = nil;
    }
    
    // Ensure completion callback is run on the callback queue to prevent blocking the delegate queue
    if (hasCompletionCallback_) {
        // Prepare error object with detailed message if needed
        NSError* effectiveError = error;
        if (!effectiveError && _statusCode >= 400) {
            NSString *bodyStr = nil;
            if (_errorBodyBuffer && _errorBodyBuffer.length > 0) {
                bodyStr = [[NSString alloc] initWithData:_errorBodyBuffer encoding:NSUTF8StringEncoding];
            }
            NSString *desc = bodyStr ? bodyStr : [NSString stringWithFormat:@"HTTP status code %d", _statusCode];
            effectiveError = [NSError errorWithDomain:@"com.httpclient.error"
                                                 code:(NSInteger)_statusCode
                                             userInfo:@{NSLocalizedDescriptionKey: desc}];
        }

        // No need to retain with ARC
        NSError* errorCopy = effectiveError; // ARC will handle reference correctly
        
        dispatch_async(_callbackQueue, ^{
            // If it was cancelled by our token, report CANCELLED error code
            if (error && [error.domain isEqualToString:NSURLErrorDomain] && 
                error.code == NSURLErrorCancelled && 
                _cancellationToken && _cancellationToken->isCancelled()) {
                
                // Create an NSError with the appropriate domain and code
                NSError *cancelledError = [NSError errorWithDomain:@"com.httpclient.error"
                                                             code:(NSInteger)1001 // Use a custom code for cancellation
                                                         userInfo:@{NSLocalizedDescriptionKey: @"Request cancelled by user"}];
                completionCallback_(_statusCode, cancelledError);
            } else {
                // Report the original error (now possibly enriched)
                completionCallback_(_statusCode, errorCopy);
            }
            // Remove explicit release since ARC handles this
        });
    }
}

- (void)cancel {
    // Stop the flush timer
    if (_flushTimer) {
        [_flushTimer invalidate];
        _flushTimer = nil;
    }
    
    // Synchronously cancel the adaptive timer on its queue
    // This ensures the timer block won't run after cancellation starts
    // or concurrently with dealloc.
    if (_adaptiveTimer) {
        dispatch_source_t timerToCancel = _adaptiveTimer; // Keep reference before setting to nil
        _adaptiveTimer = nil; // Prevent further use

        // Check if we are already on the callback queue to avoid deadlock
        if (dispatch_get_current_queue() == _callbackQueue) {
            dispatch_source_cancel(timerToCancel);
            // Note: Release of timerToCancel happens in dealloc after sync
        } else {
            dispatch_sync(_callbackQueue, ^{
                // This block executes synchronously on the callback queue.
                // By the time dispatch_sync returns, we know the timer is cancelled
                // and its event handler will not execute again.
                dispatch_source_cancel(timerToCancel);
                // Note: Release of timerToCancel happens in dealloc after sync
            });
        }
    }
    
    // Flush any remaining data (needs to happen after timer is guaranteed stopped)
    // Dispatch synchronously to the callback queue to ensure flush completes
    // before cancel returns, especially relevant for dealloc.
    if (_callbackQueue) {
         if (dispatch_get_current_queue() == _callbackQueue) {
             [self flushBuffer]; // Already on the queue, call directly
         } else {
             dispatch_sync(_callbackQueue, ^{
                 [self flushBuffer];
             });
         }
    } else {
         // Fallback if queue is somehow nil (should not happen in normal flow)
         [self flushBuffer];
    }

    if (_dataTask) {
        [_dataTask cancel]; // Cancel the underlying URLSession task
        _dataTask = nil;   // Release reference to the task
    }
}

#if !__has_feature(objc_arc)

- (void)dealloc {
    // Ensure all asynchronous work is cancelled first
    [self cancel];

    SAFE_RELEASE(_dataBuffer);
    SAFE_RELEASE(_dataPool);
    SAFE_RELEASE(_poolLock);
    SAFE_RELEASE(_lastFlushTime);
    SAFE_RELEASE(_recentBatchSizes);
    SAFE_RELEASE(_processingTimes);
    SAFE_RELEASE(_logCounters);
    SAFE_RELEASE(_flushTimer);
    SAFE_RELEASE(_errorBodyBuffer);

    if (_callbackQueue) {
        DISPATCH_RELEASE_IF_NEEDED(_callbackQueue);
        _callbackQueue = nil;
    }

    [super dealloc];
}

#endif // !__has_feature(objc_arc)

- (BOOL)isUsingHttp2 {
    return _usingHttp2;
}

- (void)setCollectMetrics:(BOOL)collect {
    _collectMetrics = collect;
}

// Method to check protocol version from task metrics (iOS 10+/macOS 10.12+)
- (void)URLSession:(NSURLSession *)session 
              task:(NSURLSessionTask *)task 
didFinishCollectingMetrics:(NSURLSessionTaskMetrics *)metrics API_AVAILABLE(macos(10.12), ios(10.0)) {
    if (_collectMetrics) {
        // Get the transaction metrics for the last transaction (most current)
        NSURLSessionTaskTransactionMetrics *lastTransaction = metrics.transactionMetrics.lastObject;
        if (lastTransaction) {
            // Check if using HTTP/2
            NSString *networkProtocol = lastTransaction.networkProtocolName;
            if (networkProtocol) {
                _usingHttp2 = [networkProtocol isEqualToString:@"h2"] || 
                            [networkProtocol isEqualToString:@"h2c"] ||
                            [networkProtocol isEqualToString:@"HTTP/2"];
                
                // Replace direct logging with structured logging
                NSString* protocolMessage = [NSString stringWithFormat:@"Using protocol: %@", networkProtocol];
                DELEGATE_LOG(3, (1 << 2), protocolMessage);  // 3 = INFO, (1 << 2) = LOG_HTTP2
                
                // Capture additional metrics if useful
                NSTimeInterval totalTime = metrics.taskInterval.duration;
                NSTimeInterval latency = lastTransaction.connectEndDate 
                    ? [lastTransaction.connectEndDate timeIntervalSinceDate:lastTransaction.fetchStartDate]
                    : 0;
                NSTimeInterval transferTime = lastTransaction.responseEndDate
                    ? [lastTransaction.responseEndDate timeIntervalSinceDate:lastTransaction.requestStartDate]
                    : 0;
                
                // Replace direct logging with structured logging
                NSString* metricsMessage = [NSString stringWithFormat:@"Request Metrics - Total: %.3fs, Latency: %.3fs, Transfer: %.3fs",
                              totalTime, latency, transferTime];
                DELEGATE_LOG(3, (1 << 1), metricsMessage);  // 3 = INFO, (1 << 1) = LOG_PERFORMANCE
            }
        }
    }
}

// Optimize HTTP/2 connection settings when connection is established
- (void)URLSession:(NSURLSession *)session
              task:(NSURLSessionTask *)task
willPerformHTTPRedirection:(NSHTTPURLResponse *)response
        newRequest:(NSURLRequest *)request
 completionHandler:(void (^)(NSURLRequest * _Nullable))completionHandler {
    // Handle redirects while preserving HTTP/2 settings
    NSMutableURLRequest *mutableRequest = [request mutableCopy];
    
    // Preserve HTTP/2 specific headers if they exist
    NSDictionary *originalHeaders = task.originalRequest.allHTTPHeaderFields;
    for (NSString *key in originalHeaders) {
        if (![mutableRequest valueForHTTPHeaderField:key]) {
            [mutableRequest setValue:[originalHeaders objectForKey:key] forHTTPHeaderField:key];
        }
    }
    
    // If we're using HTTP/2, optimize the connection for streaming
    if (@available(macOS 10.15, iOS 13.0, *)) {
        mutableRequest.networkServiceType = NSURLNetworkServiceTypeResponsiveData;
    }
    
    completionHandler(mutableRequest);
}

- (void)setLogLevel:(int)level 
         categories:(int)categories 
       samplingRate:(int)samplingRate
    useAsyncLogging:(BOOL)useAsyncLogging {
    _logLevel = level;
    _logCategories = categories;
    _logSamplingRate = samplingRate;
    _useAsyncLogging = useAsyncLogging;
    
    // Log configuration at INFO level if enabled
    if (_logLevel >= 3) {  // 3 = INFO
        NSString* logMessage = [NSString stringWithFormat:@"Delegate logging configured - Level: %d, Categories: %d, Sampling: 1/%d", 
                                         level, categories, samplingRate > 0 ? samplingRate : 1];
        DELEGATE_LOG(3, 0, logMessage);
    }
}

- (BOOL)shouldLog:(int)level category:(int)category {
    #ifdef NDEBUG
        return NO;  // In release builds, never log
    #else
        // First check if this log level and category should be logged at all
        if (level > _logLevel || (category != 0 && (category & _logCategories) == 0)) {
            return NO;
        }
        
        // If no sampling or this category isn't being sampled, log everything
        if (_logSamplingRate <= 1) {
            return YES;
        }
        
        // Implement sampling - only log 1 out of N messages per category
        NSNumber* categoryKey = @(category);
        NSNumber* counter = [_logCounters objectForKey:categoryKey];
        int count = counter ? [counter intValue] : 0;
        
        // Update counter
        count = (count + 1) % _logSamplingRate;
        [_logCounters setObject:@(count) forKey:categoryKey];
        
        // Only log when counter is 0
        return count == 0;
    #endif
}

- (void)log:(int)level category:(int)category message:(NSString*)message {
    #ifndef NDEBUG
        // Skip if we shouldn't log this message
        if (![self shouldLog:level category:category]) {
            return;
        }
        
        // Get the log message in C++ format
        const char* messageStr = [message UTF8String];
        std::string formattedMsg = formatLogMessage(level, messageStr);
        
        // Log based on configuration
        if (_useAsyncLogging) {
            // Copy for async processing
            std::string msgCopy = formattedMsg;
            dispatch_async(getSharedLoggingQueue(), ^{
                std::cerr << msgCopy << std::endl;
            });
        } else {
            // Synchronous logging - avoid in performance-critical paths
            std::cerr << formattedMsg << std::endl;
        }
    #endif
}

@end

#endif // __APPLE__ 