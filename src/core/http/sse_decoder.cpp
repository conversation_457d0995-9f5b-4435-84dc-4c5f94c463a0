#include "sse_decoder.h"
#include "core/util/debug.h"

#include <algorithm>
#include <cctype>
#include <simdjson.h>
#ifndef KAI_JSON_PARSER_SIMDJSON
#include <rapidjson/reader.h>
#include <rapidjson/stream.h>
#include <rapidjson/memorystream.h>
#endif
#include <utility>
#include <string_view>
#include <string>

namespace http {

// -------------------- Helper -------------------------
// trim leading ASCII whitespace; zero-copy (view)
static std::string_view ltrim_view(std::string_view s) {
    while (!s.empty() && std::isspace(static_cast<unsigned char>(s.front()))) {
        s.remove_prefix(1);
    }
    return s;
}

// Remove optional "data:" prefix and leading spaces; zero-copy.
static std::string_view stripDataPrefix(std::string_view line) {
    if (line.rfind("data:", 0) == 0) {
        line.remove_prefix(5);
    }
    line = ltrim_view(line);
    while (!line.empty() && (line.back() == '\r' || line.back() == '\n')) {
        line.remove_suffix(1);
    }
    return line;
}

// -------------------- OpenAI decoder -----------------
#ifndef KAI_JSON_PARSER_SIMDJSON
class OpenAISSEDecoder final : public SSEDecoder {
 public:
    std::optional<Delta> feed(std::string_view raw_line) override {
        if (!sink_) return std::nullopt;
        std::string_view line = stripDataPrefix(raw_line);
        if (line.empty()) return std::nullopt;
        if (line == std::string_view("[DONE]") || line == std::string_view("DONE")) {
            return Delta{"assistant", "", true};
        }

        // ----------- First pass: try to parse new "responses" SSE payload -----------
        {
            struct NewHandler : public rapidjson::BaseReaderHandler<rapidjson::UTF8<>, NewHandler> {
                explicit NewHandler(::launcher::core::memory::MemorySink* s) : sink(s) {}
                ::launcher::core::memory::MemorySink* sink;
                std::string_view delta;
                std::string_view type;
                bool expect_delta = false;
                bool expect_type = false;
                bool captured_type = false;
                bool Key(const char* str, rapidjson::SizeType len, bool /*copy*/) {
                    std::string_view key(str, len);
                    if (key == "delta") {
                        expect_delta = true;
                    } else if (key == "type") {
                        expect_type = true;
                    }
                    return true;
                }
                bool String(const char* str, rapidjson::SizeType len, bool /*copy*/) {
                    if (expect_delta) {
                        delta = sink->append(str, len);
                        expect_delta = false;
                    } else if (expect_type) {
                        if (!captured_type) {
                            type = sink->append(str, len);
                            captured_type = true;
                        }
                        expect_type = false;
                    }
                    return true;
                }
                // accept others
                bool Null() { return true; }
                bool Bool(bool) { return true; }
                bool Int(int) { return true; }
                bool Uint(unsigned) { return true; }
                bool Int64(int64_t) { return true; }
                bool Uint64(uint64_t) { return true; }
                bool Double(double) { return true; }
                bool StartObject() { return true; }
                bool EndObject(rapidjson::SizeType) { return true; }
                bool StartArray() { return true; }
                bool EndArray(rapidjson::SizeType) { return true; }
            } handler(sink_.get());

            rapidjson::Reader reader;
            rapidjson::MemoryStream ms(line.data(), line.size());
            bool ok_parse = reader.Parse<rapidjson::kParseStopWhenDoneFlag>(ms, handler);
            if (!ok_parse && reader.HasParseError()) {
                // First-pass parse failed; we'll attempt a fallback parser next.
                // Do NOT mark parse_error_ yet – many provider events (e.g. ping)
                // are non-JSON and would otherwise abort the entire stream.
                DBG("OpenAISSEDecoder JSON parse error first pass – will retry with delta parser");
            }

            if (!handler.delta.empty() || (!handler.type.empty() && (handler.type == "response.completed" || handler.type == "error"))) {
                // Determine role and done flag. Treat provider-level error events as terminal.
                bool is_done = (handler.type == "response.completed") || (handler.type == "error");
                if (is_done) {
                    return Delta{"assistant", "", true};
                }

                const std::string role = (handler.type.find("reasoning") != std::string_view::npos) ? "thought" : "assistant";
                if (!handler.delta.empty()) {
                    return Delta{role, handler.delta, false};
                }
            }
        }

        // RapidJSON SAX parsing – capture either content or reasoning_content.
        struct Handler : public rapidjson::BaseReaderHandler<rapidjson::UTF8<>, Handler> {
            explicit Handler(::launcher::core::memory::MemorySink* s) : sink(s) {}
            ::launcher::core::memory::MemorySink* sink;
            std::string_view content;
            std::string role;
            bool in_choices = false;
            bool in_delta = false;
            bool next_is_content = false;
            bool next_is_reasoning = false;
            bool String(const char* str, rapidjson::SizeType len, bool /*copy*/) {
                if (next_is_content || next_is_reasoning) {
                    content = sink->append(str, len);
                    role = next_is_reasoning ? "thought" : "assistant";
                    return false; // early exit
                }
                return true;
            }
            bool Key(const char* str, rapidjson::SizeType len, bool /*copy*/) {
                std::string_view key(str, len);
                if (!in_choices && key == "choices") {
                    in_choices = true;
                } else if (in_choices && key == "delta") {
                    in_delta = true;
                } else if (in_delta && key == "content") {
                    next_is_content = true;
                } else if (in_delta && key == "reasoning_content") {
                    next_is_reasoning = true;
                }
                return true;
            }
            // accept others
            bool Null() { return true; }
            bool Bool(bool) { return true; }
            bool Int(int) { return true; }
            bool Uint(unsigned) { return true; }
            bool Int64(int64_t) { return true; }
            bool Uint64(uint64_t) { return true; }
            bool Double(double) { return true; }
            bool StartObject() { return true; }
            bool EndObject(rapidjson::SizeType) { return true; }
            bool StartArray() { return true; }
            bool EndArray(rapidjson::SizeType) { return true; }
        } handler(sink_.get());

        rapidjson::Reader reader;
        rapidjson::MemoryStream ms(line.data(), line.size());
        bool ok2 = reader.Parse<rapidjson::kParseStopWhenDoneFlag>(ms, handler);
        if (!ok2 && reader.HasParseError()) {
            // Could be an incomplete chunk or keep-alive ping – ignore and wait for next line.
            DBG("OpenAISSEDecoder JSON parse error delta path");
            return std::nullopt;
        } else {
            // Successful second-pass parse clears any previous error flag so the
            // decoder can continue processing subsequent lines.
            parse_error_ = false;
        }

        if (!handler.content.empty()) {
            return Delta{handler.role.empty() ? "assistant" : handler.role, handler.content, false};
        }
        return std::nullopt;
    }
};
#endif

// -------------------- Anthropic decoder --------------
#ifndef KAI_JSON_PARSER_SIMDJSON
class AnthropicSSEDecoder final : public SSEDecoder {
 public:
    std::optional<Delta> feed(std::string_view raw_line) override {
        if (!sink_) return std::nullopt;
        std::string_view line = stripDataPrefix(raw_line);
        if (line.empty()) return std::nullopt;
        if (line.find("\"message_stop\"") != std::string_view::npos) {
            return Delta{"assistant", "", true};
        }
        if (line == std::string_view("[DONE]") || line == std::string_view("DONE")) {
            return Delta{"assistant", "", true};
        }

        // -------- New Anthropic extended thinking format --------
        {
            struct NewHandler : public rapidjson::BaseReaderHandler<rapidjson::UTF8<>, NewHandler> {
                explicit NewHandler(::launcher::core::memory::MemorySink* s) : sink(s) {}
                ::launcher::core::memory::MemorySink* sink;
                std::string_view content;
                std::string role{"assistant"};
                // parsing state
                bool expect_top_type = false;
                bool expect_delta_type = false;
                bool expect_thinking = false;
                bool expect_text = false;
                bool in_delta = false;

                bool Key(const char* str, rapidjson::SizeType len, bool) {
                    std::string_view key(str, len);
                    if (!in_delta && key == "type") {
                        expect_top_type = true;
                    } else if (key == "delta") {
                        in_delta = true;
                    } else if (in_delta && key == "type") {
                        expect_delta_type = true;
                    } else if (in_delta && key == "thinking") {
                        expect_thinking = true;
                    } else if (in_delta && key == "text") {
                        expect_text = true;
                    }
                    return true;
                }

                bool String(const char* str, rapidjson::SizeType len, bool) {
                    if (expect_top_type) {
                        // top-level type maybe message_stop etc; already handled earlier but ok
                        expect_top_type = false;
                        return true;
                    }
                    if (expect_delta_type) {
                        std::string_view t(str, len);
                        if (t == "thinking_delta") {
                            role = "thought";
                        } else {
                            role = "assistant";
                        }
                        expect_delta_type = false;
                        return true;
                    }
                    if (expect_thinking || expect_text) {
                        content = sink->append(str, len);
                        expect_thinking = expect_text = false;
                        // stop further parsing
                        return false;
                    }
                    return true;
                }

                bool StartObject() { return true; }
                bool EndObject(rapidjson::SizeType) { return true; }
                bool StartArray() { return true; }
                bool EndArray(rapidjson::SizeType) { return true; }
                bool Null() { return true; }
                bool Bool(bool) { return true; }
                bool Int(int) { return true; }
                bool Uint(unsigned) { return true; }
                bool Int64(int64_t) { return true; }
                bool Uint64(uint64_t) { return true; }
                bool Double(double) { return true; }
            } handler(sink_.get());

            rapidjson::Reader reader;
            rapidjson::MemoryStream ms(line.data(), line.size());
            bool ok_parseA = reader.Parse<rapidjson::kParseStopWhenDoneFlag>(ms, handler);
            if (!ok_parseA && reader.HasParseError()) {
                // Early termination is expected when the handler returns false after capturing
                // the desired token (RapidJSON sets kParseErrorTermination). Treat this as a
                // non-fatal condition – simply wait for the next chunk instead of surfacing an
                // error that would flood the log and trip ui error indicators.
                DBG("AnthropicSSEDecoder JSON parse error new format");
                // Do NOT flip |parse_error_| here – most of these cases are benign (e.g. keep-alive
                // pings, partial JSON fragments) and should not bubble up as fatal errors.
            }

            if (!handler.content.empty()) {
                // Successfully captured a delta; clear any previous error flag.
                parse_error_ = false;
                return Delta{handler.role, handler.content, false};
            }
            // otherwise fallthrough to legacy format
        }

        struct Handler : public rapidjson::BaseReaderHandler<rapidjson::UTF8<>, Handler> {
            explicit Handler(::launcher::core::memory::MemorySink* s) : sink(s) {}
            ::launcher::core::memory::MemorySink* sink;
            std::string_view content;
            bool next_is_text = false;
            bool next_is_completion = false;
            bool Key(const char* str, rapidjson::SizeType len, bool) {
                std::string_view key(str, len);
                if (key == "text") {
                    next_is_text = true;
                } else if (key == "completion") {
                    next_is_completion = true;
                }
                return true;
            }
            bool String(const char* str, rapidjson::SizeType len, bool) {
                if (next_is_text || next_is_completion) {
                    content = sink->append(str, len);
                    next_is_text = next_is_completion = false;
                    return false; // early stop
                }
                return true;
            }
            // Accept other events
            bool Null() { return true; }
            bool Bool(bool) { return true; }
            bool Int(int) { return true; }
            bool Uint(unsigned) { return true; }
            bool Int64(int64_t) { return true; }
            bool Uint64(uint64_t) { return true; }
            bool Double(double) { return true; }
            bool StartObject() { return true; }
            bool EndObject(rapidjson::SizeType) { return true; }
            bool StartArray() { return true; }
            bool EndArray(rapidjson::SizeType) { return true; }
        } handler(sink_.get());

        rapidjson::Reader reader;
        rapidjson::MemoryStream ms(line.data(), line.size());
        bool okB = reader.Parse<rapidjson::kParseStopWhenDoneFlag>(ms, handler);
        if (!okB && reader.HasParseError()) {
            // Same rationale as above – downgrade to debug-level noise suppression.
            DBG("AnthropicSSEDecoder JSON parse error legacy format");
            // Flag as error only if we failed to extract any meaningful token.
            if (handler.content.empty()) {
                parse_error_ = true;
            }
        }

        if (!handler.content.empty()) {
            parse_error_ = false;
            return Delta{"assistant", handler.content, false};
        }
        return std::nullopt;
    }
};
#endif  // !KAI_JSON_PARSER_SIMDJSON for Anthropic decoder

// -------------------- Factory ------------------------
#ifdef KAI_JSON_PARSER_SIMDJSON
// Forward declaration implemented in sse_decoder_simd.cpp
std::unique_ptr<SSEDecoder> createSimdDecoder(const std::string& provider_id);
#endif
std::unique_ptr<SSEDecoder> SSEDecoder::create(const std::string& provider_id) {
    std::string id = provider_id;
    std::transform(id.begin(), id.end(), id.begin(), [](unsigned char c){ return std::tolower(c); });
#ifdef KAI_JSON_PARSER_SIMDJSON
    if (auto simd = createSimdDecoder(id); simd) {
        return simd;
    }
#endif
#ifndef KAI_JSON_PARSER_SIMDJSON
    if (id == "openai" || id.rfind("openai",0)==0) {
        return std::make_unique<OpenAISSEDecoder>();
    }
    if (id == "anthropic" || id.rfind("anthropic",0)==0) {
        return std::make_unique<AnthropicSSEDecoder>();
    }
#endif
    return nullptr;
}

}  // namespace http 