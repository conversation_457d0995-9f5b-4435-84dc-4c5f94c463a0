#include "sse_decoder.h"
#include <simdjson.h>

namespace http {

class OpenAISSEDecoder final : public SSEDecoder {
 public:
    std::optional<Delta> feed(std::string_view raw) override {
        // SSE lines start with "data: "
        if (!raw.starts_with("data:")) return std::nullopt;
        std::string_view payload = raw.substr(5);
        if (!payload.empty() && payload.front() == ' ') payload.remove_prefix(1);
        if (payload == "[DONE]" || payload.starts_with("[DONE]")) {
            return Delta{"", "", true};
        }
        simdjson::ondemand::parser parser;
        try {
            // parse in-situ without extra allocation
            auto doc = parser.iterate(payload.data(), payload.size(), /*realloc_if_needed=*/false);
            auto choices = doc["choices"];
            auto first = choices.at(0);
            auto delta = first["delta"];
            auto contentField = delta["content"];
            std::string_view content = contentField.get_string().value();
            return Delta{"assistant", content, false};
        } catch (...) {
            parse_error_ = true;
            return std::nullopt;
        }
    }
};

std::unique_ptr<SSEDecoder> SSEDecoder::create(const std::string& provider_id) {
    if (provider_id == "openai") {
        return std::make_unique<OpenAISSEDecoder>();
    }
    return nullptr;
}

} // namespace http 