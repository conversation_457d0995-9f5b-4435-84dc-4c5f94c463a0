cmake_minimum_required(VERSION 3.10)

# HTTP Client library
add_library(http_client STATIC)

if(APPLE)
    set(HTTP_CLIENT_SOURCES
        http_client.mm
        http_streaming_delegate.mm
        http_pool.cpp
        sse_decoder.cpp
        sse_decoder_simd.cpp
        responses_decoder_simd.cpp
        http_delegate_mux.mm
        http_delegate_mux.h
    )
    
    target_sources(http_client PRIVATE
        ${HTTP_CLIENT_SOURCES}
        http_client.h
        http_streaming_delegate.h
        http_pool.h
        sse_decoder_openai.cpp
    )
    
    # Compile Objective-C++ sources with ARC enabled for safer memory management
    set_source_files_properties(http_client.mm http_streaming_delegate.mm http_delegate_mux.mm
                               PROPERTIES COMPILE_FLAGS "-x objective-c++ -fobjc-arc")
    
    # Link against required frameworks
    target_link_libraries(http_client
        "-framework Foundation"
        nlohmann_json::nlohmann_json
        $<$<STREQUAL:${KAI_JSON_PARSER},simdjson>:simdjson>
        simdjson
    )

# Non-macOS platforms are currently not supported.
elseif(NOT APPLE)
    message(FATAL_ERROR "HttpClient: Only macOS is supported in this build configuration.")
endif()

target_include_directories(http_client PUBLIC "${CMAKE_CURRENT_SOURCE_DIR}")
