#include "responses_decoder.h"
#include "core/util/debug.h"
#include <simdjson.h>
#include <algorithm>
#include <cctype>
#include "core/memory/memory_sink.h"

namespace http {
namespace {
inline std::string_view makeStable(std::string_view src,const std::shared_ptr<launcher::core::memory::MemorySink>& sink) {
    if (!sink || src.empty()) return src;
    return sink->append(src.data(), src.size());
}

static std::string_view ltrim(std::string_view s) {
    while (!s.empty() && std::isspace(static_cast<unsigned char>(s.front()))) s.remove_prefix(1);
    return s;
}
static std::string_view stripPrefix(std::string_view line) {
    if (line.rfind("data:",0)==0) line.remove_prefix(5);
    line = ltrim(line);
    while (!line.empty() && (line.back()=='\r' || line.back()=='\n')) line.remove_suffix(1);
    return line;
}

struct EventInfo { std::string_view type; ResponseKind kind; };
static constexpr EventInfo kTable[] = {
    {"response.output_text.delta",   ResponseKind::kAssistantDelta},
    {"response.reasoning.delta",     ResponseKind::kThoughtDelta},
    {"response.thinking.delta",      ResponseKind::kThoughtDelta},
    {"response.completed",           ResponseKind::kCompleted},
    {"response.started",             ResponseKind::kStarted},
    {"response.image_generation_call.partial_image", ResponseKind::kImageChunk},
    {"response.image_generation_call.done",          ResponseKind::kImageDone},
    {"response.tool_call",           ResponseKind::kToolCall},
    {"response.output_item.added",   ResponseKind::kOutputItem},
    {"response.mcp_approval_request",ResponseKind::kMcpApproval},
    {"response.error",               ResponseKind::kError}
};

inline ResponseKind lookupKind(std::string_view t) {
    for(const auto& e: kTable) if (e.type==t) return e.kind;
    return ResponseKind::kUnknown;
}
}

class OpenAIResponsesDecoderSimd final : public ResponsesDecoder {
public:
    std::optional<ResponseEvent> feed(std::string_view raw_line) override {
        std::string_view line = stripPrefix(raw_line);
        if (line.empty()) return std::nullopt;
        if (line=="[DONE]" || line=="DONE") return ResponseEvent{ResponseKind::kCompleted,{}};

        thread_local simdjson::ondemand::parser parser;
        ResponseEvent evt;
        try {
            simdjson::padded_string padded(line);
            simdjson::ondemand::document doc = parser.iterate(padded);
            simdjson::ondemand::object root;
            if (doc.get(root)) return std::nullopt;

            std::string_view type_sv;
            std::string_view delta_sv;
            std::string_view b64_sv;
            uint32_t img_idx = 0;
            std::string_view json_blob = line; // raw line sans prefix
            std::string_view err_code, err_msg;
            std::string_view model_sv; double created_ts = 0;

            for (auto field : root) {
                std::string_view k = field.unescaped_key();
                if (k=="type") {
                    type_sv = std::string_view(field.value().get_string());
                } else if (k=="delta") {
                    if (field.value().type()==simdjson::ondemand::json_type::string) {
                        delta_sv = std::string_view(field.value().get_string());
                    }
                } else if (k=="partial_image") {
                    b64_sv = std::string_view(field.value().get_string());
                } else if (k=="partial_image_index") {
                    img_idx = uint32_t(field.value().get_uint64());
                } else if (k=="code") {
                    err_code = std::string_view(field.value().get_string());
                } else if (k=="message") {
                    err_msg = std::string_view(field.value().get_string());
                } else if (k=="model") {
                    model_sv = std::string_view(field.value().get_string());
                } else if (k=="created_at") {
                    created_ts = double(field.value().get_double());
                }
            }

            ResponseKind kind = lookupKind(type_sv);
            evt.kind = kind;
            switch(kind) {
                case ResponseKind::kAssistantDelta:
                    evt.payload = AssistantDelta{makeStable(delta_sv,sink_)}; break;
                case ResponseKind::kThoughtDelta:
                    evt.payload = ThoughtDelta{makeStable(delta_sv,sink_)}; break;
                case ResponseKind::kImageChunk:
                    evt.payload = ImageChunk{makeStable(b64_sv,sink_), img_idx}; break;
                case ResponseKind::kImageDone:
                    evt.payload = ImageDone{}; break;
                case ResponseKind::kToolCall:
                    evt.payload = ToolCall{makeStable(json_blob, sink_)}; break;
                case ResponseKind::kOutputItem:
                    evt.payload = OutputItem{makeStable(json_blob, sink_)}; break;
                case ResponseKind::kMcpApproval:
                    evt.payload = McpApproval{makeStable(json_blob, sink_)}; break;
                case ResponseKind::kError:
                    evt.payload = ErrorEvt{err_code, err_msg}; break;
                case ResponseKind::kStarted:
                    evt.payload = StartedMeta{makeStable(model_sv,sink_), created_ts}; break;
                default: break;
            }
            return evt;
        } catch(const simdjson::simdjson_error& e) {
            DBG("ResponsesDecoderSimd parse error: " << e.what());
            return std::nullopt;
        }
    }
};

std::unique_ptr<ResponsesDecoder> createResponsesDecoder(const std::string& provider_id) {
    std::string id=provider_id; std::transform(id.begin(),id.end(),id.begin(),[](unsigned char c){return std::tolower(c);} );
    if(id=="openai" || id.rfind("openai",0)==0) return std::make_unique<OpenAIResponsesDecoderSimd>();
    return nullptr;
}

} // namespace http 