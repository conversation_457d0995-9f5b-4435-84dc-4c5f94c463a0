#ifdef __APPLE__
#import "http_delegate_mux.h"
#import "http_streaming_delegate.h"

@interface HttpDelegateMux ()
@property(nonatomic, strong) NSMapTable<NSURLSessionTask*, HttpStreamingDelegate*>* map;
@end

@implementation HttpDelegateMux

- (instancetype)init {
    self = [super init];
    if (self) {
        // weak keys (tasks), strong values (delegates)
        _map = [NSMapTable weakToStrongObjectsMapTable];
    }
    return self;
}

- (void)registerDelegate:(HttpStreamingDelegate*)delegate forTask:(NSURLSessionTask*)task {
    if (!delegate || !task) return;
    @synchronized(self) {
        [self.map setObject:delegate forKey:task];
    }
}

#pragma mark - NSURLSessionDataDelegate forwarding

- (void)URLSession:(NSURLSession *)session dataTask:(NSURLSessionDataTask *)dataTask didReceiveData:(NSData *)data {
    HttpStreamingDelegate* del = nil;
    @synchronized(self) { del = [self.map objectForKey:dataTask]; }
    if (del) {
        [del URLSession:session dataTask:dataTask didReceiveData:data];
    }
}

- (void)URLSession:(NSURLSession *)session task:(NSURLSessionTask *)task didCompleteWithError:(NSError *)error {
    HttpStreamingDelegate* del = nil;
    @synchronized(self) {
        del = [self.map objectForKey:task];
        [self.map removeObjectForKey:task];
    }
    if (del) {
        [del URLSession:session task:task didCompleteWithError:error];
    }
}

- (void)URLSession:(NSURLSession *)session dataTask:(NSURLSessionDataTask *)dataTask didReceiveResponse:(NSURLResponse *)response completionHandler:(void (^)(NSURLSessionResponseDisposition))completionHandler {
    HttpStreamingDelegate* del = nil;
    @synchronized(self) { del = [self.map objectForKey:dataTask]; }
    if (del && [del respondsToSelector:@selector(URLSession:dataTask:didReceiveResponse:completionHandler:)]) {
        [del URLSession:session dataTask:dataTask didReceiveResponse:response completionHandler:completionHandler];
    } else {
        completionHandler(NSURLSessionResponseAllow);
    }
}

@end

#endif 