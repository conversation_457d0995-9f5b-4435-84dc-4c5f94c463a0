#pragma once

#include <coroutine>
#include <unordered_map>
#include <string>
#include <memory>
#include "http_client.h"
#include "utilities/cancellation_token.h"

namespace http {

struct HttpRequestAwaitable {
    HttpRequestAwaitable(HttpClient* client,
                         std::string_view url_sv,
                         std::string_view method_sv,
                         std::string_view body_sv,
                         std::unordered_map<std::string, std::string> headers,
                         std::shared_ptr<::launcher::core::utilities::CancellationToken> token)
        : client_(client), url_(url_sv), method_(method_sv),
          body_(body_sv), headers_(std::move(headers)), token_(std::move(token)),
          result_(HttpResult::failure(HttpError{0, "pending"})) {}

    bool await_ready() const noexcept { return false; }

    void await_suspend(std::coroutine_handle<> h) {
        // Capture coroutine handle and launch async request
        client_->requestAsync(url_, method_, body_, headers_,
            [this, h](HttpResult res) mutable {
                result_ = std::move(res);
                h.resume();
            }, token_);
    }

    HttpResult await_resume() { return std::move(result_); }

 private:
    HttpClient* client_;
    std::string url_;
    std::string method_;
    std::string body_;
    std::unordered_map<std::string, std::string> headers_;
    std::shared_ptr<::launcher::core::utilities::CancellationToken> token_;
    HttpResult result_;
};

}  // namespace http 