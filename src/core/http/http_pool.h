/*
 * Http Pool - shared, keep-alive friendly HttpClient instances per provider.
 *
 * Thread-safe singleton. Uses weak_ptr in map so unused clients can be
 * garbage-collected automatically. acquire() will create a new client if none
 * exists or if the previous one has expired.
 */
#pragma once

#include <memory>
#include <mutex>
#include <string>
#include <unordered_map>
#include <utility>
#include <list>
#include <chrono>

#include "http_client.h"

namespace http {

class HttpPool {
 public:
    // Obtain singleton instance.
    static HttpPool& instance();

    // Acquire a shared HttpClient for |provider_id|. Optional |base_headers|
    // are applied only on first creation (or when headers are missing); they
    // do NOT override existing values deliberately set by other users.
    std::shared_ptr<HttpClient> acquire(
        const std::string& provider_id,
        const std::unordered_map<std::string, std::string>& base_headers = {});

    // Optional knob to tune limits (call at startup)
    void setLimits(size_t max_clients, std::chrono::minutes idle_ttl) {
        std::lock_guard<std::mutex> lock(mutex_);
        max_clients_ = max_clients;
        idle_ttl_ = idle_ttl;
    }

    // Deleted copy/assign to enforce singleton semantics
    HttpPool(const HttpPool&) = delete;
    HttpPool& operator=(const HttpPool&) = delete;

 private:
    HttpPool() = default;

    struct Entry {
        std::weak_ptr<HttpClient> weak;
        std::chrono::steady_clock::time_point last_used;
        std::list<std::string>::iterator lru_it;
    };

    // Helper to build a new client.
    std::shared_ptr<HttpClient> createClient(
        const std::unordered_map<std::string, std::string>& base_headers);

    // Maintenance helpers (caller must hold mutex_)
    void purgeExpiredLocked();
    void evictIfNeededLocked();

    std::unordered_map<std::string, Entry> map_;
    std::list<std::string> lru_;               // MRU at front

    size_t max_clients_ = 12;
    std::chrono::minutes idle_ttl_ = std::chrono::minutes(5);

    std::mutex mutex_;
};

}  // namespace http 