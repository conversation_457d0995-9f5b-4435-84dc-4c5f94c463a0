#pragma once

#include <string>

namespace http {

//------------------------------------------------------------------------------
// HttpError – lightweight error object returned by HttpClient operations when
// they fail.  A status_code of 0 means the request failed before a response was
// received (e.g. invalid URL, connectivity failure).  For HTTP responses with
// status codes outside the 2xx range, status_code will contain the server
// status and message will hold a descriptive error string.
//------------------------------------------------------------------------------
struct Http<PERSON>rror {
    int         status_code = 0;      // HTTP status or 0 on transport/error
    std::string message;              // Human-readable error description
};

} // namespace http 