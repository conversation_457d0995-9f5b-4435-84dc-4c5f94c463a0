#include "http_pool.h"

#include <chrono>

namespace http {

using Clock = std::chrono::steady_clock;

HttpPool& HttpPool::instance() {
    static HttpPool inst;
    return inst;
}

std::shared_ptr<HttpClient> HttpPool::createClient(
    const std::unordered_map<std::string, std::string>& base_headers) {
    auto client = std::make_shared<HttpClient>();
    if (!base_headers.empty()) {
        client->setDefaultHeaders(base_headers);
    }
    // Use a generous timeout by default to avoid premature termination.
    client->setTimeout(60);
    return client;
}

void HttpPool::purgeExpiredLocked() {
    auto now = Clock::now();
    // Iterate from LRU tail backwards
    for (auto it = lru_.rbegin(); it != lru_.rend();) {
        const std::string& key = *it;
        auto map_it = map_.find(key);
        if (map_it == map_.end()) {
            // Should not happen, but keep list consistent
            it = decltype(it){ lru_.erase(std::next(it).base()) };
            continue;
        }
        const Entry& entry = map_it->second;
        bool expired_ptr = entry.weak.expired();
        bool idle_timeout = (now - entry.last_used) > idle_ttl_;
        if (expired_ptr || idle_timeout) {
            lru_.erase(std::next(it).base());
            map_.erase(map_it);
            it = lru_.rbegin(); // restart iterator safety
            continue;
        }
        ++it;
    }
}

void HttpPool::evictIfNeededLocked() {
    while (map_.size() > max_clients_ && !lru_.empty()) {
        const std::string& key = lru_.back();
        auto map_it = map_.find(key);
        if (map_it != map_.end()) {
            map_.erase(map_it);
        }
        lru_.pop_back();
    }
}

std::shared_ptr<HttpClient> HttpPool::acquire(
    const std::string& provider_id,
    const std::unordered_map<std::string, std::string>& base_headers) {
    std::lock_guard<std::mutex> lock(mutex_);

    // Cleanup first
    purgeExpiredLocked();

    auto map_it = map_.find(provider_id);
    if (map_it != map_.end()) {
        std::shared_ptr<HttpClient> existing = map_it->second.weak.lock();
        if (existing) {
            // Ensure headers present
            for (const auto& kv : base_headers) {
                if (existing->getDefaultHeaders().find(kv.first) == existing->getDefaultHeaders().end()) {
                    existing->addDefaultHeader(kv.first, kv.second);
                }
            }
            // Move to front of LRU
            lru_.splice(lru_.begin(), lru_, map_it->second.lru_it);
            map_it->second.lru_it = lru_.begin();
            map_it->second.last_used = Clock::now();
            return existing;
        } else {
            // Weak expired, remove stale entry
            lru_.erase(map_it->second.lru_it);
            map_.erase(map_it);
        }
    }

    // Need a new client
    auto fresh = createClient(base_headers);
    lru_.push_front(provider_id);
    Entry e;
    e.weak = fresh;
    e.last_used = Clock::now();
    e.lru_it = lru_.begin();
    map_.emplace(provider_id, std::move(e));

    // Enforce capacity
    evictIfNeededLocked();

    return fresh;
}

}  // namespace http 