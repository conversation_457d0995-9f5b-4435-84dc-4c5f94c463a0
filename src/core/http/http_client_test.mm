#ifdef __APPLE__
#include "http_client.h"
#include <iostream>
#include <string>
#include <sstream>
#include <iomanip>
#include <chrono>
#include <string_view>

/**
 * Simple example demonstrating the usage of the HTTP client
 */
void testHttpClient() {
    std::cout << "\n=== Testing HTTP Client ===\n" << std::endl;
    
    // Create HTTP client instance
    http::HttpClient client;
    
    // Set custom timeout
    client.setTimeout(30);
    
    // Add custom headers
    client.addDefaultHeader("X-Custom-Header", "CustomValue");
    
    // Test connectivity
    std::string host = "httpbin.org";
    std::cout << "Checking connectivity to " << host << "..." << std::endl;
    bool connectivity = client.checkConnectivity(host);
    std::cout << "Connectivity check result: " << (connectivity ? "Success" : "Failed") << std::endl;
    
    if (!connectivity) {
        std::cerr << "WARNING: Connectivity test failed. Proceeding anyway..." << std::endl;
    }
    
    // Perform a GET request
    std::cout << "\nPerforming GET request..." << std::endl;
    auto getRes = client.get("https://httpbin.org/get");
    if (!getRes) {
        std::cerr << "GET failed: " << getRes.error().message << std::endl;
    } else {
        const auto& getResponse = getRes.value();
        std::cout << "GET Status: " << getResponse.status_code << std::endl;
        std::cout << "GET Body Sample: " << getResponse.body.substr(0, 100) << "..." << std::endl;
    }
    
    // Perform a POST request with JSON body
    std::cout << "\nPerforming POST request..." << std::endl;
    std::string postBody = "{\"name\":\"test\",\"value\":123}";
    std::unordered_map<std::string, std::string> headers = {{"Content-Type", "application/json"}};
    auto postRes = client.post("https://httpbin.org/post", postBody, headers);
    if (!postRes) {
        std::cerr << "POST failed: " << postRes.error().message << std::endl;
    } else {
        const auto& postResponse = postRes.value();
        std::cout << "POST Status: " << postResponse.status_code << std::endl;
        std::cout << "POST Body Sample: " << postResponse.body.substr(0, 100) << "..." << std::endl;
    }
    
    // Perform a streaming GET request
    std::cout << "\nPerforming streaming GET request..." << std::endl;
    auto streamingCallback = [](std::string_view data) {
        std::cout << "Received chunk: " << std::string(data.substr(0, 30)) << 
                     (data.length() > 30 ? "..." : "") << std::endl;
    };
    
    auto streamingRes = client.streamingGet(
        "https://httpbin.org/stream/3", 
        streamingCallback,
        nullptr
    );
    
    if (streamingRes) {
        std::cout << "Streaming GET Status: " << streamingRes.value().status_code << std::endl;
    } else {
        std::cerr << "Streaming GET failed: " << streamingRes.error().message << std::endl;
    }
    
    // Perform a streaming POST request
    std::cout << "\nPerforming streaming POST request (this may not work on all endpoints)..."
              << std::endl;
    auto streamingPostRes = client.streamingPost(
        "https://httpbin.org/post",
        "{\"test\":\"streaming\"}",
        streamingCallback,
        nullptr,
        {{"Content-Type", "application/json"}}
    );
    
    if (streamingPostRes) {
        std::cout << "Streaming POST Status: " << streamingPostRes.value().status_code << std::endl;
    } else {
        std::cerr << "Streaming POST failed: " << streamingPostRes.error().message << std::endl;
    }
    
    std::cout << "\n=== HTTP Client Test Complete ===\n" << std::endl;
}

/**
 * Helper function to escape JSON string values
 */
std::string escapeJsonString(const std::string& input) {
    std::ostringstream result;
    for (auto ch : input) {
        switch (ch) {
            case '\"': result << "\\\""; break;
            case '\\': result << "\\\\"; break;
            case '\b': result << "\\b"; break;
            case '\f': result << "\\f"; break;
            case '\n': result << "\\n"; break;
            case '\r': result << "\\r"; break;
            case '\t': result << "\\t"; break;
            default:
                if (ch < ' ') {
                    // Control characters must be escaped
                    result << "\\u" << std::setfill('0') << std::setw(4) << std::hex 
                           << static_cast<int>(ch);
                } else {
                    result << ch;
                }
                break;
        }
    }
    return result.str();
}

/**
 * Test the OpenAI GPT-4o streaming API
 * 
 * To run this test, you need to:
 * 1. Set the OPENAI_API_KEY environment variable with your OpenAI API key
 *    export OPENAI_API_KEY=your_api_key
 * 2. Build and run the test
 */
void testOpenAIStreaming() {
    std::cout << "\n=== Testing OpenAI GPT-4o Streaming ===\n" << std::endl;
    
    // Get API key from environment
    const char* api_key = std::getenv("OPENAI_API_KEY");
    if (!api_key) {
        std::cerr << "ERROR: OPENAI_API_KEY environment variable not set" << std::endl;
        std::cerr << "Please set it with: export OPENAI_API_KEY=your_api_key" << std::endl;
        return;
    }
    
    // Create HTTP client
    http::HttpClient client;
    
    // Set a longer timeout for AI model responses
    client.setTimeout(120);
    
    // Set the API key header
    std::string auth_header = std::string("Bearer ") + api_key;
    client.addDefaultHeader("Authorization", auth_header);
    
    // Test connectivity to OpenAI
    std::string host = "api.openai.com";
    std::cout << "Checking connectivity to " << host << "..." << std::endl;
    bool connectivity = client.checkConnectivity(host);
    std::cout << "Connectivity check result: " << (connectivity ? "Success" : "Failed") << std::endl;
    
    if (!connectivity) {
        std::cerr << "WARNING: Cannot connect to OpenAI API. Check your internet connection." << std::endl;
        std::cerr << "Proceeding anyway..." << std::endl;
    }
    
    // Define the prompt
    std::string prompt = "Briefly explain how HTTP streaming works.";
    std::string escapedPrompt = escapeJsonString(prompt);
    
    // Prepare the request body for GPT-4o with additional parameters
    std::string requestBody = "{"
                            "\"model\":\"gpt-4o\","
                            "\"messages\":[{\"role\":\"user\",\"content\":\"" + 
                            escapedPrompt + "\"}],"
                            "\"temperature\": 0.7,"  // Controls randomness (0.0-2.0)
                            "\"max_tokens\": 500,"   // Limit response length
                            "\"stream\":true"
                            "}";
    
    std::cout << "Sending request to OpenAI GPT-4o..." << std::endl;
    std::cout << "Prompt: " << prompt << std::endl;
    
    // Full response buffer
    std::string fullResponse;
    bool receivingResponse = false;
    
    // Create a callback to process streaming chunks
    auto streamingCallback = [&fullResponse, &receivingResponse](std::string_view data) {
        // Skip empty chunks
        if (data.empty()) return;
        
        // OpenAI streaming delivers JSON chunks in the SSE format
        // Each chunk contains a delta with content to append to the response
        
        // Only print raw JSON in debug mode
        // std::cout << "Raw JSON: " << data << std::endl;
        
        // Check for JSON data
        if (data.find("{") == 0 || data.find("[") == 0) {
            // Look for content in delta (common in ChatGPT responses)
            size_t deltaPos = data.find("\"delta\":");
            if (deltaPos != std::string::npos) {
                size_t contentPos = data.find("\"content\":", deltaPos);
                if (contentPos != std::string::npos) {
                    size_t quoteStart = data.find("\"", contentPos + 10);
                    if (quoteStart != std::string::npos) {
                        size_t quoteEnd = data.find("\"", quoteStart + 1);
                        if (quoteEnd != std::string::npos) {
                            std::string content = std::string(data.substr(quoteStart + 1, quoteEnd - quoteStart - 1));
                            if (!content.empty()) {
                                // Print without newline to show streaming effect
                                if (!receivingResponse) {
                                    std::cout << "\nResponse: ";
                                    receivingResponse = true;
                                }
                                std::cout << content << std::flush;
                                fullResponse += content;
                            }
                        }
                    }
                }
            }
            // Also look for finish_reason to detect end of response
            else if (data.find("\"finish_reason\"") != std::string::npos) {
                if (receivingResponse) {
                    std::cout << std::endl; // Add a newline at the end
                }
            }
        } else {
            // Non-JSON data
            if (!data.empty()) {
                std::cout << "Non-JSON data: " << data << std::endl;
            }
        }
    };
    
    // Start the timer
    auto start_time = std::chrono::high_resolution_clock::now();
    
    // Make the streaming request
    auto streamRes = client.streamingPost(
        "https://api.openai.com/v1/chat/completions",
        requestBody,
        streamingCallback,
        nullptr,
        {
            {"Content-Type", "application/json"},
            {"Accept", "text/event-stream"}
        }
    );
    
    // Calculate elapsed time
    auto end_time = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(
        end_time - start_time).count();
    
    int status_code = streamRes ? streamRes.value().status_code : streamRes.error().status_code;
    std::cout << "\nStreaming complete. Status code: " << status_code << std::endl;
    std::cout << "Completed in " << duration << "ms" << std::endl;
    
    if (streamRes) {
        std::cout << "Response length: " << fullResponse.length() << " characters" << std::endl;
    } else {
        std::cerr << "Error: " << streamRes.error().message << std::endl;
    }
    
    std::cout << "\n=== OpenAI Streaming Test Complete ===\n" << std::endl;
}

/**
 * Main function for testing the HTTP client
 */
int main(int argc, char* argv[]) {
    bool run_openai_test = false;
    bool run_basic_test = false;
    
    // Simple command line arg parsing
    for (int i = 1; i < argc; i++) {
        std::string arg = argv[i];
        if (arg == "--openai" || arg == "-o") {
            run_openai_test = true;
        } else if (arg == "--basic" || arg == "-b") {
            run_basic_test = true;
        } else if (arg == "--all" || arg == "-a") {
            run_basic_test = true;
            run_openai_test = true;
        } else if (arg == "--help" || arg == "-h") {
            std::cout << "HTTP Client Test" << std::endl;
            std::cout << "Usage: http_client_test [options]" << std::endl;
            std::cout << "Options:" << std::endl;
            std::cout << "  -a, --all     Run all tests (default)" << std::endl;
            std::cout << "  -b, --basic   Run basic HTTP tests only" << std::endl;
            std::cout << "  -o, --openai  Run OpenAI streaming test only" << std::endl;
            std::cout << "  -h, --help    Show this help message" << std::endl;
            std::cout << std::endl;
            std::cout << "For OpenAI tests, set OPENAI_API_KEY environment variable." << std::endl;
            return 0;
        }
    }
    
    // If no specific test was selected, run all
    if (!run_basic_test && !run_openai_test) {
        run_basic_test = true;
        run_openai_test = true;
    }
    
    if (run_basic_test) {
        // Test basic HTTP functionality
        testHttpClient();
    }
    
    if (run_openai_test) {
        // Test OpenAI GPT-4o streaming
        testOpenAIStreaming();
    }
    
    return 0;
}

#endif // __APPLE__ 