#include "sse_decoder.h"
#include "core/util/debug.h"
#include <simdjson.h>
#include <algorithm>
#include <cctype>
#include <string_view>

namespace http {

namespace {
inline std::string_view makeStable(std::string_view src, std::shared_ptr<launcher::core::memory::MemorySink> sink) {
    if (!sink || src.empty()) return src;
    return sink->append(src.data(), src.size());
}
}

static std::string_view ltrim_view_sv(std::string_view s) {
    while (!s.empty() && std::isspace(static_cast<unsigned char>(s.front()))) {
        s.remove_prefix(1);
    }
    return s;
}

static std::string_view stripDataPrefixSv(std::string_view line) {
    if (line.rfind("data:", 0) == 0) {
        line.remove_prefix(5);
    }
    line = ltrim_view_sv(line);
    while (!line.empty() && (line.back() == '\r' || line.back() == '\n')) {
        line.remove_suffix(1);
    }
    return line;
}

static inline bool isDoneToken(std::string_view line) {
    return line == "[DONE]" || line == "DONE" || line.find("\"message_stop\"") != std::string_view::npos;
}

// -------------------- simdjson OpenAI decoder -----------------------------
class OpenAISSEDecoderSimd final : public SSEDecoder {
public:
    std::optional<Delta> feed(std::string_view raw_line) override {
        if (!sink_) return std::nullopt;
        std::string_view line = stripDataPrefixSv(raw_line);
        if (line.empty()) return std::nullopt;
        if (isDoneToken(line)) {
            return Delta{"assistant", "", true};
        }

        thread_local simdjson::ondemand::parser parser;
        simdjson::ondemand::document doc;
        try {
            simdjson::padded_string json_padded(line);
            doc = parser.iterate(json_padded);

            simdjson::ondemand::object root;
            if (doc.get(root)) {
                return std::nullopt;
            }

            bool saw_stop = false;
            std::string role; // resolved once we examine "type"
            for (auto top_field : root) {
                std::string_view k = top_field.unescaped_key();
                if (k == "type") {
                    std::string_view t = std::string_view(top_field.value().get_string());
                    if (t == "response.completed") {
                        saw_stop = true;
                    } else if (t == "error") {
                        // Provider-level error event – terminate the stream immediately.
                        return Delta{"assistant", "", true};
                    }
                    else if (t == "response.output_text.delta") {
                        role = "assistant";
                    } else if (t == "response.reasoning.delta" || t == "response.thinking.delta") {
                        role = "thought";
                    }
                } else if (k == "delta") {
                    auto val = top_field.value();
                    // Case 1: new Responses API – delta is *string*
                    if (val.type() == simdjson::ondemand::json_type::string) {
                        std::string_view sv = std::string_view(val.get_string());
                        if (!sv.empty()) {
                            // role has been decided earlier via event type; default to assistant
                            std::string use_role = role.empty() ? "assistant" : role;
                            return Delta{use_role, makeStable(sv, sink_), false};
                        }
                    }
                    // Case 2: legacy/new chat format – delta is object
                    else if (val.type() == simdjson::ondemand::json_type::object) {
                        simdjson::ondemand::object delta_obj = val.get_object();
                        for (auto field : delta_obj) {
                            std::string_view key = field.unescaped_key();
                            std::string_view sv = std::string_view(field.value().get_string());
                            if (key == "reasoning_content" && !sv.empty()) {
                                return Delta{"thought", makeStable(sv, sink_), false};
                            }
                            if (key == "content" && !sv.empty()) {
                                return Delta{"assistant", makeStable(sv, sink_), false};
                            }
                        }
                    }
                }
            }

            // Existing root-level parsing done above.
            // Fallback: legacy ChatCompletions delta format inside "choices" array.
            try {
                auto choices_val = root.find_field_unordered("choices");
                if (!choices_val.error() && choices_val.type() == simdjson::ondemand::json_type::array) {
                    simdjson::ondemand::array choices_arr = choices_val.get_array();
                    auto it = choices_arr.begin();
                    if (it != choices_arr.end()) {
                        simdjson::ondemand::object first_choice = *it;
                        auto delta_field = first_choice.find_field_unordered("delta");
                        if (!delta_field.error()) {
                            if (delta_field.type() == simdjson::ondemand::json_type::object) {
                                simdjson::ondemand::object delta_obj = delta_field.get_object();
                                for (auto dfield : delta_obj) {
                                    std::string_view dkey = dfield.unescaped_key();
                                    if (dkey == "content") {
                                        std::string_view sv = std::string_view(dfield.value().get_string());
                                        if (!sv.empty()) {
                                            return Delta{"assistant", makeStable(sv, sink_), false};
                                        }
                                    }
                                    else if (dkey == "reasoning_content") {
                                        std::string_view sv = std::string_view(dfield.value().get_string());
                                        if (!sv.empty()) {
                                            return Delta{"thought", makeStable(sv, sink_), false};
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            } catch (const simdjson::simdjson_error&){ /* ignore */ }

            if (saw_stop) {
                return Delta{"assistant", "", true};
            }
        } catch (const simdjson::simdjson_error& e) {
            DBG("OpenAISSEDecoderSimd on-demand error: " << e.what());
            return std::nullopt; // gracefully ignore partial/malformed JSON
        }

        return std::nullopt;
    }
};

// -------------------- simdjson Anthropic decoder --------------------------
class AnthropicSSEDecoderSimd final : public SSEDecoder {
public:
    std::optional<Delta> feed(std::string_view raw_line) override {
        if (!sink_) return std::nullopt;
        std::string_view line = stripDataPrefixSv(raw_line);
        if (line.empty()) return std::nullopt;
        if (isDoneToken(line)) {
            return Delta{"assistant", "", true};
        }

        thread_local simdjson::ondemand::parser parser;
        simdjson::ondemand::document doc;
        simdjson::padded_string json_padded(line);
        try {
            doc = parser.iterate(json_padded);
        } catch (const simdjson::simdjson_error& e) {
            DBG("AnthropicSSEDecoderSimd parse exception: " << e.what());
            return std::nullopt;
        }

        simdjson::ondemand::object root;
        if (doc.get(root)) {
            return std::nullopt;
        }

        // New thinking format
        if (auto delta_val = root.find_field_unordered("delta"); !delta_val.error()) {
            simdjson::ondemand::object delta;
            if (delta_val.get(delta)) {
                // not object; ignore
            } else {
                std::string_view type_sv;
                // Gather fields in one pass
                for (auto field : delta) {
                    std::string_view key = field.unescaped_key();
                    if (key == "type") {
                        field.value().get_string().get(type_sv);
                    } else if (key == "thinking" && type_sv == "thinking_delta") {
                        std::string_view sv;
                        if (!field.value().get_string().get(sv) && !sv.empty()) {
                            return Delta{"thought", makeStable(sv, sink_), false};
                        }
                    } else if (key == "text") {
                        std::string_view sv;
                        if (!field.value().get_string().get(sv) && !sv.empty()) {
                            return Delta{"assistant", makeStable(sv, sink_), false};
                        }
                    }
                }
            }
        }
        // Legacy: top-level text/completion
        auto text_val = root.find_field_unordered("text");
        if (!text_val.error()) {
            std::string_view sv;
            if (!text_val.get_string().get(sv) && !sv.empty()) {
                return Delta{"assistant", makeStable(sv, sink_), false};
            }
        }
        auto comp_val = root.find_field_unordered("completion");
        if (!comp_val.error()) {
            std::string_view sv;
            if (!comp_val.get_string().get(sv) && !sv.empty()) {
                return Delta{"assistant", makeStable(sv, sink_), false};
            }
        }
        return std::nullopt;
    }
};

// --------------- Registration helper ------------------------------------
std::unique_ptr<SSEDecoder> createSimdDecoder(const std::string& provider_id) {
    std::string id = provider_id;
    std::transform(id.begin(), id.end(), id.begin(), [](unsigned char c){ return std::tolower(c); });
    if (id == "openai" || id.rfind("openai",0)==0) {
        return std::make_unique<OpenAISSEDecoderSimd>();
    }
    if (id == "anthropic" || id.rfind("anthropic",0)==0) {
        return std::make_unique<AnthropicSSEDecoderSimd>();
    }
    return nullptr;
}

} // namespace http 