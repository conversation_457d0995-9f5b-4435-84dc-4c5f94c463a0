#ifdef __APPLE__
#include "http_client.h"
#include "http_streaming_delegate.h"
#include <chrono>
#include <iomanip>
#include <iostream>
#include <sstream>
#include <thread>
#include <queue>
#include <mutex>
#include <memory>
#include <vector>
#include <string_view>

// Import necessary headers for TLS constants
#import <Foundation/Foundation.h>
#import "http_error.h"
#include "core/util/expected.h"
#include "http_request_awaitable.h"
#include "core/util/stream_channel.h"
#include "core/util/stream_generator.h"
#include "core/http/http_error.h"
#include "core/memory/memory_sink.h"
#include "core/memory/slab_arena.h"  // stable append-only allocator
#include "core/memory/thread_safe_arena.h"  // thread-safe append-only allocator
#include "http_delegate_mux.h"

namespace http {

// Static dispatch queue for async logging
static dispatch_queue_t g_logging_queue = nullptr;

// Static helper function to initialize logging queue
static dispatch_queue_t getLoggingQueue() {
    static std::once_flag once;
    std::call_once(once, []() {
        g_logging_queue = dispatch_queue_create("com.httpclient.loggingQueue", DISPATCH_QUEUE_SERIAL);
    });
    return g_logging_queue;
}

static HttpResult makeResult(Response&& resp, const std::string& errMsg = "");

// ----------------------------------------------------------------------------------
//  Implementation details hidden behind PIMPL (HttpClient::Impl)
// ----------------------------------------------------------------------------------

struct HttpClient::Impl {
    void* session_            = nullptr;  // NSURLSession*
    void* delegateQueue_      = nullptr;  // NSOperationQueue*
    void* sessionConfiguration_ = nullptr;  // NSURLSessionConfiguration*
};

constexpr size_t kDefaultBatchSize = 2048;          // 2 KiB threshold
constexpr double kDefaultFlushInterval = 0.05;      // 50 ms max latency
constexpr int kDefaultLogSamplingRate = 10;         // log 1/10 DEBUG traces

HttpClient::HttpClient() : timeoutSeconds_(30), batchSize_(kDefaultBatchSize), flushInterval_(kDefaultFlushInterval), 
                          useHttp2_(true), allowHttp2Fallback_(true),
                          useMemoryPooling_(true), memoryPoolSize_(10),
                          useAdaptiveBatching_(true), minBatchSize_(1024), 
                          maxBatchSize_(1024*128), adaptiveFactor_(0.2),
                          logLevel_(LogLevel::ERROR), logCategories_(LOG_NETWORK),
                          useAsyncLogging_(true), logSamplingRate_(kDefaultLogSamplingRate), logSamplingMask_(LOG_NETWORK | LOG_PERFORMANCE),
                          impl_(std::make_unique<Impl>()) {
    // Set some sensible default headers
    defaultHeaders_["User-Agent"] = "HttpClient/1.0";
    
    // Create a dedicated operation queue for delegate callbacks
    NSOperationQueue* delegateQueue = [[NSOperationQueue alloc] init];
    [delegateQueue setName:@"HttpClientDelegateQueue"];
    constexpr int kDelegateQueueConcurrency = 10; // Tuneable default; 1 = serial
    [delegateQueue setMaxConcurrentOperationCount:kDelegateQueueConcurrency];
    // Allow limited parallelism for NSURLSession delegate callbacks to fully utilise HTTP/2 multiplexing
    impl_->delegateQueue_ = (__bridge_retained void*)delegateQueue;
    
    // Create a session configuration
    NSURLSessionConfiguration* config = [NSURLSessionConfiguration defaultSessionConfiguration];
    
    // Enable HTTP/2 by default
    if (@available(macOS 10.11, iOS 9.0, *)) {
        // Use modern networking protocols - HTTP/2 is enabled by default in these versions
        config.HTTPMaximumConnectionsPerHost = 10;  // Allow up to 10 concurrent connections per host
        
        if (!useHttp2_) {
            // Explicitly disable HTTP/2
            config.HTTPShouldUsePipelining = NO;
            // Use the network service type property directly on config
            config.networkServiceType = NSURLNetworkServiceTypeDefault;
            // Store HTTP/1.1 protocols in TLSMinimumSupportedProtocol property
        }
    }
    
    // Configure TLS settings based on OS version
    if (@available(macOS 10.15, iOS 13.0, *)) {
        // Modern systems - use consistent TLS configuration
        #pragma clang diagnostic push
        #pragma clang diagnostic ignored "-Wdeprecated-declarations"
        config.TLSMinimumSupportedProtocol = kTLSProtocol12;
        #pragma clang diagnostic pop
        
        // Enable additional performance optimizations
        config.HTTPShouldUsePipelining = YES;
    } else if (@available(macOS 10.13, iOS 11.0, *)) {
        // Intermediate systems with consistent TLS 1.2 settings
        #pragma clang diagnostic push
        #pragma clang diagnostic ignored "-Wdeprecated-declarations"
        config.TLSMinimumSupportedProtocol = kTLSProtocol12;
        #pragma clang diagnostic pop
        
        // Enable performance optimizations available on this OS version
        config.HTTPShouldUsePipelining = YES;
    } else {
        // Fall back for older systems with appropriate compiler directives
        #pragma clang diagnostic push
        #pragma clang diagnostic ignored "-Wdeprecated-declarations"
        config.TLSMinimumSupportedProtocol = kTLSProtocol12;
        #pragma clang diagnostic pop
    }
    
    // Configure TLS session resumption for performance on all versions
    config.sessionSendsLaunchEvents = YES;
    
    // Enable waiting for connectivity - improves reliability on spotty networks
    if (@available(macOS 10.14, iOS 12.0, *)) {
        config.waitsForConnectivity = YES;
    }
    
    // Enable performance optimizations on modern systems
    if (@available(macOS 11.0, iOS 14.0, *)) {
        // Use modern HTTP optimizations without platform-specific features
        config.HTTPShouldUsePipelining = YES;
        
        // Note: The following features are not used as they're not available on all platforms:
        // - requiresCertificateTransparency (not widely available)
        // - multipathServiceType (not available on macOS)
    }
    
    config.timeoutIntervalForRequest = timeoutSeconds_;
    config.timeoutIntervalForResource = timeoutSeconds_;
    
    // Store the configuration
    impl_->sessionConfiguration_ = (__bridge_retained void*)config;
    
    // Create delegate multiplexer for per-task forwarding.
    HttpDelegateMux* mux = [[HttpDelegateMux alloc] init];
    delegateMux_ = (__bridge_retained void*)mux;

    // Create the persistent session using mux as delegate.
    HttpDelegateMux* muxCurrent = (__bridge HttpDelegateMux*)delegateMux_;
    NSURLSession* session = [NSURLSession sessionWithConfiguration:config
                                                         delegate:muxCurrent
                                                    delegateQueue:(__bridge NSOperationQueue*)impl_->delegateQueue_];
    impl_->session_ = (__bridge_retained void*)session;
    
    HTTP_LOG_INFO(this, "HttpClient initialized with HTTP/2 enabled: " + std::to_string(useHttp2_));
}

HttpClient::~HttpClient() {
    if (!impl_) {
        return;
    }

    if (impl_->session_) {
        NSURLSession* session = (__bridge_transfer NSURLSession*)impl_->session_;
        [session invalidateAndCancel];
        impl_->session_ = nullptr;
    }

    if (impl_->sessionConfiguration_) {
        NSURLSessionConfiguration* config = (__bridge_transfer NSURLSessionConfiguration*)impl_->sessionConfiguration_;
        (void)config; // not used further
        impl_->sessionConfiguration_ = nullptr;
    }

    if (impl_->delegateQueue_) {
        NSOperationQueue* queue = (__bridge_transfer NSOperationQueue*)impl_->delegateQueue_;
        (void)queue;
        impl_->delegateQueue_ = nullptr;
    }

    if (delegateMux_) {
        HttpDelegateMux* mux = (__bridge_transfer HttpDelegateMux*)delegateMux_;
        (void)mux; // ARC handles release
        delegateMux_ = nullptr;
    }
}

void HttpClient::configureHttp2(bool use_http2, bool allow_fallback) {
    useHttp2_ = use_http2;
    allowHttp2Fallback_ = allow_fallback;
    
    // Update the session configuration
    if (impl_->sessionConfiguration_) {
        NSURLSessionConfiguration* config = (__bridge NSURLSessionConfiguration*)impl_->sessionConfiguration_;
        
        if (@available(macOS 10.11, iOS 9.0, *)) {
            if (useHttp2_) {
                // HTTP/2 is enabled by default in modern versions
                config.HTTPShouldUsePipelining = YES;
                [config setHTTPAdditionalHeaders:nil];  // Remove any HTTP/1.1 specific headers
            } else {
                // Explicitly disable HTTP/2
                config.HTTPShouldUsePipelining = NO;
                [config setHTTPAdditionalHeaders:@{@"Connection": @"keep-alive"}];
            }
        }
        
        // Re-apply TLS configuration to ensure consistency
        if (@available(macOS 10.15, iOS 13.0, *)) {
            // Modern systems - use consistent TLS configuration
            #pragma clang diagnostic push
            #pragma clang diagnostic ignored "-Wdeprecated-declarations"
            config.TLSMinimumSupportedProtocol = kTLSProtocol12;
            #pragma clang diagnostic pop
        } else if (@available(macOS 10.13, iOS 11.0, *)) {
            // Intermediate systems with consistent TLS 1.2 settings
            #pragma clang diagnostic push
            #pragma clang diagnostic ignored "-Wdeprecated-declarations"
            config.TLSMinimumSupportedProtocol = kTLSProtocol12;
            #pragma clang diagnostic pop
        }
        
        // If we have an existing session, recreate it with the new configuration
        if (impl_->session_) {
            NSURLSession* oldSession = (__bridge NSURLSession*)impl_->session_;
            [oldSession invalidateAndCancel];
            
            HttpDelegateMux* muxCurrent = (__bridge HttpDelegateMux*)delegateMux_;
            NSURLSession* newSession = [NSURLSession sessionWithConfiguration:config
                                                                   delegate:muxCurrent
                                                              delegateQueue:(__bridge NSOperationQueue*)impl_->delegateQueue_];
            impl_->session_ = (__bridge_retained void*)newSession;
            
            // Properly release old session
            CFRelease((__bridge CFTypeRef)oldSession);
        }
    }
}

bool HttpClient::isUsingHttp2() const {
    return useHttp2_;
}

void HttpClient::setDefaultHeaders(const std::unordered_map<std::string, std::string>& headers) {
    defaultHeaders_ = headers;
}

void HttpClient::addDefaultHeader(std::string_view name_sv, std::string_view value_sv) {
    defaultHeaders_[std::string(name_sv)] = std::string(value_sv);
}

void HttpClient::setTimeout(int timeout_seconds) {
    timeoutSeconds_ = timeout_seconds;
}

void HttpClient::configureBatchProcessing(size_t batch_size, double flush_interval) {
    batchSize_ = batch_size;
    flushInterval_ = flush_interval;
}

void HttpClient::configureMemoryPooling(bool use_pooling, size_t pool_size) {
    useMemoryPooling_ = use_pooling;
    memoryPoolSize_ = pool_size;
}

void HttpClient::configureAdaptiveBatching(bool use_adaptive_batching, 
                                          size_t min_batch_size, 
                                          size_t max_batch_size, 
                                          double adaptive_factor) {
    useAdaptiveBatching_ = use_adaptive_batching;
    minBatchSize_ = min_batch_size;
    maxBatchSize_ = max_batch_size;
    adaptiveFactor_ = adaptive_factor;
}

std::unordered_map<std::string, double> HttpClient::getPerformanceMetrics() const {
    std::unordered_map<std::string, double> metrics;
    
    // Add basic configuration info
    metrics["http2_enabled"] = useHttp2_ ? 1.0 : 0.0;
    metrics["memory_pooling_enabled"] = useMemoryPooling_ ? 1.0 : 0.0;
    metrics["adaptive_batching_enabled"] = useAdaptiveBatching_ ? 1.0 : 0.0;
    metrics["batch_size"] = static_cast<double>(batchSize_);
    metrics["memory_pool_size"] = static_cast<double>(memoryPoolSize_);
    
    // We don't have access to the delegate's runtime metrics here
    // Client code should call streamingRequest and check Response::headers for metrics
    
    return metrics;
}

bool HttpClient::checkConnectivity(std::string_view host_sv, int timeout_seconds) {
    std::string host(host_sv);
    // Create an NSURL for a simple HTTPS request to the host
    NSString* urlString = [NSString stringWithFormat:@"https://%s", host.c_str()];
    NSURL* url = [NSURL URLWithString:urlString];
    if (!url) {
        std::cerr << "ERROR: Invalid URL for connectivity test" << std::endl;
        return false;
    }
    
    // Create a request
    NSMutableURLRequest* request = [NSMutableURLRequest requestWithURL:url];
    [request setHTTPMethod:@"GET"];
    [request setTimeoutInterval:timeout_seconds];
    
    // Create a semaphore to make the request synchronous
    dispatch_semaphore_t semaphore = dispatch_semaphore_create(0);
    
    // Variables to store results
    __block bool success = false;
    __block int statusCode = 0;
    __block NSError* requestError = nil;
    
    // Create and start the task
    NSURLSession* session = (__bridge NSURLSession*)impl_->session_;
    // Fall back to shared session if our persistent session is not available
    if (!session) {
        session = [NSURLSession sharedSession];
    }
    NSURLSessionDataTask* task = [session dataTaskWithRequest:request 
                                     completionHandler:^(NSData* data, NSURLResponse* response, NSError* error) {
        if (error) {
            requestError = error;
        } else if ([response isKindOfClass:[NSHTTPURLResponse class]]) {
            NSHTTPURLResponse* httpResponse = (NSHTTPURLResponse*)response;
            statusCode = (int)httpResponse.statusCode;
            
            // Consider 2xx, 3xx, and 401 as "success" for connectivity purposes
            success = (statusCode >= 200 && statusCode < 400) || statusCode == 401;
        }
        
        dispatch_semaphore_signal(semaphore);
    }];
    
    [task resume];
    
    // Wait for the request to complete
    long result = dispatch_semaphore_wait(semaphore, 
                     dispatch_time(DISPATCH_TIME_NOW, timeout_seconds * NSEC_PER_SEC));
    
    if (result != 0) {
        return false;  // Timeout
    }
    
    if (requestError || !success) {
        return false;  // Connection failed
    }
    
    return true;
}

HttpResult HttpClient::get(
    std::string_view url_sv,
    const std::unordered_map<std::string, std::string>& headers) {
    
    return request(url_sv, "GET", "", headers);
}

HttpResult HttpClient::post(
    std::string_view url_sv,
    std::string_view body_sv,
    const std::unordered_map<std::string, std::string>& headers) {
    
    return request(url_sv, "POST", body_sv, headers);
}

HttpResult HttpClient::put(
    std::string_view url_sv,
    std::string_view body_sv,
    const std::unordered_map<std::string, std::string>& headers) {
    
    return request(url_sv, "PUT", body_sv, headers);
}

HttpResult HttpClient::del(
    std::string_view url_sv,
    const std::unordered_map<std::string, std::string>& headers) {
    
    return request(url_sv, "DELETE", "", headers);
}

HttpResult HttpClient::streamingGet(
    std::string_view url_sv,
    const DataCallback& callback,
    std::shared_ptr<::launcher::core::utilities::CancellationToken> cancellationToken /* = nullptr */,
    const std::unordered_map<std::string, std::string>& headers) {
    
    auto stream = streamChunks(url_sv, "GET", "", headers, cancellationToken);
    while (true) {
        if (cancellationToken && cancellationToken->isCancelled()) {
            return HttpResult::failure(HttpError{-2, "cancelled"});
        }
        auto opt = stream.waitNext(cancellationToken);
        if (!opt) break;
        callback(*opt);
    }
    if (auto err = stream.error()) {
        return HttpResult::failure(*err);
    }
    Response resp; resp.status_code = 200;
    return HttpResult::success(std::move(resp));
}

HttpResult HttpClient::streamingPost(
    std::string_view url_sv,
    std::string_view body_sv,
    const DataCallback& callback,
    std::shared_ptr<::launcher::core::utilities::CancellationToken> cancellationToken,
    const std::unordered_map<std::string, std::string>& headers) {
    
    auto stream = streamChunks(url_sv, "POST", body_sv, headers, cancellationToken);
    while (true) {
        if (cancellationToken && cancellationToken->isCancelled()) {
            return HttpResult::failure(HttpError{-2, "cancelled"});
        }
        auto opt = stream.waitNext(cancellationToken);
        if (!opt) break;
        callback(*opt);
    }
    if (auto err = stream.error()) {
        return HttpResult::failure(*err);
    }
    Response resp; resp.status_code = 200;
    return HttpResult::success(std::move(resp));
}

std::string HttpClient::escapeJsonString(std::string_view input_sv) {
    std::string input(input_sv); // ostringstream needs std::string or char*
    std::ostringstream result;
    for (auto ch : input) {
        switch (ch) {
            case '\"': result << "\\\""; break;
            case '\\': result << "\\\\"; break;
            case '\b': result << "\\b"; break;
            case '\f': result << "\\f"; break;
            case '\n': result << "\\n"; break;
            case '\r': result << "\\r"; break;
            case '\t': result << "\\t"; break;
            default:
                if (ch < ' ') {
                    // Control characters must be escaped
                    result << "\\u" << std::setfill('0') << std::setw(4) << std::hex 
                           << static_cast<int>(ch);
                } else {
                    result << ch;
                }
                break;
        }
    }
    return result.str();
}

HttpResult HttpClient::request(
    std::string_view url_sv,
    std::string_view method_sv,
    std::string_view body_sv,
    const std::unordered_map<std::string, std::string>& headers) {
    
    // Prepare the response object
    Response response;
    std::string url(url_sv);
    std::string method(method_sv);
    std::string body(body_sv);
    
    // Prepare URL
    NSURL* nsUrl = [NSURL URLWithString:[NSString stringWithUTF8String:url.c_str()]];
    if (!nsUrl) {
        return makeResult(std::move(response), "Invalid URL");
    }
    
    // Create request
    NSMutableURLRequest* request = [NSMutableURLRequest requestWithURL:nsUrl];
    [request setHTTPMethod:[NSString stringWithUTF8String:method.c_str()]];
    [request setTimeoutInterval:timeoutSeconds_];
    
    // Set default headers
    for (const auto& header : defaultHeaders_) {
        [request setValue:[NSString stringWithUTF8String:header.second.c_str()]
            forHTTPHeaderField:[NSString stringWithUTF8String:header.first.c_str()]];
    }
    
    // Set additional headers
    for (const auto& header : headers) {
        [request setValue:[NSString stringWithUTF8String:header.second.c_str()]
            forHTTPHeaderField:[NSString stringWithUTF8String:header.first.c_str()]];
    }
    
    // Set body if provided
    if (!body.empty()) {
        NSData* bodyData = [[NSString stringWithUTF8String:body.c_str()] 
                           dataUsingEncoding:NSUTF8StringEncoding];
        [request setHTTPBody:bodyData];
    }
    
    // Create semaphore for synchronous request
    dispatch_semaphore_t semaphore = dispatch_semaphore_create(0);
    
    // Variables to capture response data
    __block int statusCode = 0;
    __block bool requestSuccess = false;
    __block std::string errorMessage;
    __block std::unordered_map<std::string, std::string> responseHeaders;
    __block std::string responseBody;
    
    // Perform the request using our persistent session
    NSURLSession* session = (__bridge NSURLSession*)impl_->session_;
    NSURLSessionDataTask* task = [session dataTaskWithRequest:request
                                         completionHandler:^(NSData* data, 
                                                          NSURLResponse* urlResponse, 
                                                          NSError* error) {
        if (error) {
            requestSuccess = false;
            errorMessage = [[error localizedDescription] UTF8String];
        } else {
            NSHTTPURLResponse* httpResponse = (NSHTTPURLResponse*)urlResponse;
            statusCode = (int)httpResponse.statusCode;
            requestSuccess = (statusCode >= 200 && statusCode < 300);
            
            // Extract headers
            NSDictionary* headers = [httpResponse allHeaderFields];
            for (NSString* key in headers) {
                NSString* value = [headers objectForKey:key];
                responseHeaders[[key UTF8String]] = [value UTF8String];
            }
            
            // Extract body
            if (data) {
                NSString* bodyStr = [[NSString alloc] initWithData:data 
                                                         encoding:NSUTF8StringEncoding];
                if (bodyStr) {
                    responseBody = [bodyStr UTF8String];
                }
            }
        }
        
        dispatch_semaphore_signal(semaphore);
    }];
    
    [task resume];
    
    // Wait for completion
    long result = dispatch_semaphore_wait(semaphore, 
                     dispatch_time(DISPATCH_TIME_NOW, timeoutSeconds_ * NSEC_PER_SEC));
    
    // Populate response object
    response.status_code = statusCode;
    response.headers = responseHeaders;
    response.body = responseBody;
    
    std::string err;
    if (result != 0) {
        err = "Request timed out";
    } else if (!requestSuccess && errorMessage.empty()) {
        err = "Request failed with status code: " + std::to_string(statusCode);
    } else if (!errorMessage.empty()) {
        err = errorMessage;
    }
    
    return makeResult(std::move(response), err);
}

HttpResult HttpClient::streamingRequest(
    std::string_view url_sv,
    std::string_view method_sv,
    std::string_view body_sv,
    const DataCallback& callback,
    std::shared_ptr<::launcher::core::utilities::CancellationToken> cancellationToken,
    const std::unordered_map<std::string, std::string>& headers) {
    
    Response response;
    std::string finalErr;
    std::string url(url_sv);
    std::string method(method_sv);
    std::string body(body_sv);
    
    try {
        // Log at debug level that we're starting a streaming request
        HTTP_LOG_DEBUG(this, "Starting streaming " + method + " request to " + url);
        
        // Prepare URL
        NSURL* nsUrl = [NSURL URLWithString:[NSString stringWithUTF8String:url.c_str()]];
        if (!nsUrl) {
            return makeResult(std::move(response), "Invalid URL");
        }
        
        // Create request
        NSMutableURLRequest* request = [NSMutableURLRequest requestWithURL:nsUrl];
        [request setHTTPMethod:[NSString stringWithUTF8String:method.c_str()]];
        [request setTimeoutInterval:timeoutSeconds_];
        
        // Set default headers
        for (const auto& header : defaultHeaders_) {
            [request setValue:[NSString stringWithUTF8String:header.second.c_str()]
                forHTTPHeaderField:[NSString stringWithUTF8String:header.first.c_str()]];
        }
        
        // Set additional headers
        for (const auto& header : headers) {
            [request setValue:[NSString stringWithUTF8String:header.second.c_str()]
                forHTTPHeaderField:[NSString stringWithUTF8String:header.first.c_str()]];
        }
        
        // Set Accept header for SSE if not explicitly set
        if (headers.find("Accept") == headers.end() && defaultHeaders_.find("Accept") == defaultHeaders_.end()) {
            [request setValue:@"text/event-stream" forHTTPHeaderField:@"Accept"];
        }
        
        // For HTTP/2 optimization, set priority
        if (@available(macOS 10.15, iOS 13.0, *)) {
            // Set priority to high for streaming requests, which helps with HTTP/2 stream prioritization
            request.networkServiceType = NSURLNetworkServiceTypeResponsiveData;
        }
        
        // Set body if provided
        if (!body.empty()) {
            NSData* bodyData = [[NSString stringWithUTF8String:body.c_str()] 
                               dataUsingEncoding:NSUTF8StringEncoding];
            [request setHTTPBody:bodyData];
        }
        
        // Create a dispatch group to wait for completion
        dispatch_group_t requestGroup = dispatch_group_create();
        dispatch_group_enter(requestGroup);
        
        // Variables to store completion status
        __block bool requestSucceeded = false;
        __block std::string errorMsg;
        __block int statusCode = 0;
        
        // Create the streaming delegate with completion handler
        HttpStreamingDelegate* delegate = [[HttpStreamingDelegate alloc] 
            initWithCallback:callback
            completionCallback:^(int code, NSError* error) {
                statusCode = code;
                
                if (error) {
                    requestSucceeded = false;
                    errorMsg = [[error localizedDescription] UTF8String];
                    
                    // Log error with optimized approach
                    std::string errorString = errorMsg;
                    HTTP_LOG_ERROR(this, "Request failed: " + errorString);
                } else {
                    requestSucceeded = (code >= 200 && code < 300);
                    
                    // Log completion status
                    std::stringstream status_msg;
                    status_msg << "Request completed with status code: " << code;
                    LogLevel status_level = requestSucceeded ? LogLevel::DEBUG : LogLevel::ERROR;
                    log(status_level, LOG_NETWORK, status_msg.str());
                }
                
                // Signal completion
                dispatch_group_leave(requestGroup);
            }
            cancellationToken:cancellationToken];
        
        // Configure batch processing if enabled
        if (batchSize_ > 0 || flushInterval_ > 0.0) {
            [delegate configureBatchProcessing:batchSize_ flushInterval:flushInterval_];
        }
        
        // Configure memory pooling
        if (useMemoryPooling_) {
            [delegate configureMemoryPooling:YES poolSize:memoryPoolSize_];
        }
        
        // Configure adaptive batching
        if (useAdaptiveBatching_) {
            [delegate configureAdaptiveBatching:YES 
                                       minSize:minBatchSize_ 
                                       maxSize:maxBatchSize_ 
                                adaptiveFactor:adaptiveFactor_];
        }
        
        // Configure delegate with logging settings
        [delegate setLogLevel:static_cast<int>(logLevel_)
                    categories:logCategories_
                   samplingRate:logSamplingRate_
                   useAsyncLogging:useAsyncLogging_];
        
        // Reuse the shared session configuration with HTTP/2 settings
        NSURLSessionConfiguration* config = (__bridge NSURLSessionConfiguration*)impl_->sessionConfiguration_;
        
        // Set timeout intervals to let iOS handle timeouts natively
        config.timeoutIntervalForRequest = timeoutSeconds_;
        config.timeoutIntervalForResource = timeoutSeconds_;
        
        NSURLSession* baseSession = (__bridge NSURLSession*)impl_->session_;
        NSURLSessionDataTask* dataTask = [baseSession dataTaskWithRequest:request];
        delegate.dataTask = dataTask;
        [dataTask resume];
        
        // Wait for completion - without timeout
        dispatch_group_wait(requestGroup, DISPATCH_TIME_FOREVER);
        
        response.status_code = statusCode;
        
        if (@available(macOS 10.12, iOS 10.0, *)) {
            response.headers["protocol_version"] = [delegate isUsingHttp2] ? "HTTP/2" : "HTTP/1.1";
        }
        
        NSDictionary* metrics = [delegate getPerformanceMetrics];
        for (NSString* key in metrics) {
            NSString* value = [metrics objectForKey:key];
            response.headers[std::string([key UTF8String])] = std::string([value description].UTF8String);
        }
        
        if (!requestSucceeded && !errorMsg.empty()) {
            finalErr = errorMsg;
        } else if (statusCode < 200 || statusCode >= 300) {
            finalErr = "Request failed with status code: " + std::to_string(statusCode);
        }
        
        if (static_cast<int>(logLevel_) >= static_cast<int>(LogLevel::DEBUG) && (logCategories_ & LOG_PERFORMANCE)) {
            std::stringstream metrics_msg;
            metrics_msg << "Request metrics - ";
            for (const auto& pair : response.headers) {
                if (pair.first.find("protocol_") == 0 || pair.first.find("totalBytes") == 0 || pair.first.find("average") == 0) {
                    metrics_msg << pair.first << ":" << pair.second << " ";
                }
            }
            HTTP_LOG_PERFORMANCE(this, metrics_msg.str());
        }
        
    } catch (const std::exception& e) {
        finalErr = e.what();
        HTTP_LOG_ERROR(this, std::string("Exception during streaming request: ") + finalErr);
    } catch (...) {
        finalErr = "Unknown exception during streaming request";
        HTTP_LOG_ERROR(this, finalErr);
    }
    
    return makeResult(std::move(response), finalErr);
}

void HttpClient::configureLogging(LogLevel log_level, int log_categories, bool use_async) {
    logLevel_ = log_level;
    logCategories_ = log_categories;
    useAsyncLogging_ = use_async;
    
    // Initialize logging queue if using async logging
    if (useAsyncLogging_) {
        getLoggingQueue();
    }
    
    // Log configuration change at INFO level
    std::stringstream ss;
    ss << "Logging configured - Level: " << static_cast<int>(log_level) 
       << ", Categories: " << log_categories 
       << ", Async: " << (use_async ? "true" : "false");
    log(LogLevel::INFO, LOG_NONE, ss.str());
}

void HttpClient::setLogSamplingRate(int sampling_rate, int category_mask) {
    logSamplingRate_ = sampling_rate;
    logSamplingMask_ = category_mask;
    
    // Reset counters when changing sampling rate
    std::fill(logCounters_.begin(), logCounters_.end(), 0);
    
    // Log sampling configuration change
    std::stringstream ss;
    ss << "Log sampling configured - Rate: 1/" << sampling_rate 
       << ", Category mask: " << category_mask;
    log(LogLevel::INFO, LOG_NONE, ss.str());
}

bool HttpClient::shouldLog(int category) const {
    #ifdef NDEBUG
        return false;  // In release builds, always return false for conditional runtime checks
    #else
        // If logging is disabled or category is not enabled, don't log
        if (logLevel_ == LogLevel::NONE || (category & logCategories_) == 0) {
            return false;
        }
        
        // If no sampling is configured, always log
        if (logSamplingRate_ <= 1 || (category & logSamplingMask_) == 0) {
            return true;
        }
        
        int idx = categoryIndex(category);
        if (idx < 0) return true; // no sampling for undefined

        // Apply sampling - only log 1 out of N messages per category
        int& counter = logCounters_[idx];
        counter = (counter + 1) % logSamplingRate_;

        return counter == 0;
    #endif
}

void HttpClient::log(LogLevel level, int category, std::string_view message_sv) const {
    std::string message(message_sv);
    #ifndef NDEBUG
        // Skip if log level is too low or category is not enabled
        if (level > logLevel_ || (category != LOG_NONE && (category & logCategories_) == 0)) {
            return;
        }
        
        // Apply sampling if configured
        if (category != LOG_NONE && !shouldLog(category)) {
            return;
        }
        
        // Format the log message with timestamp and level prefix
        auto now = std::chrono::system_clock::now();
        auto now_time_t = std::chrono::system_clock::to_time_t(now);
        auto now_ms = std::chrono::duration_cast<std::chrono::milliseconds>(
            now.time_since_epoch()).count() % 1000;
        
        std::stringstream msg;
        msg << "[" << std::put_time(std::localtime(&now_time_t), "%Y-%m-%d %H:%M:%S") 
            << "." << std::setfill('0') << std::setw(3) << now_ms << "] ";
        
        // Add level prefix
        switch (level) {
            case LogLevel::ERROR: msg << "[ERROR] "; break;
            case LogLevel::WARN:  msg << "[WARN] "; break;
            case LogLevel::INFO:  msg << "[INFO] "; break;
            case LogLevel::DEBUG: msg << "[DEBUG] "; break;
            case LogLevel::TRACE: msg << "[TRACE] "; break;
            default: break;
        }
        
        // Add the actual message
        msg << message;
        
        // Output based on configuration
        if (useAsyncLogging_) {
            // Create a copy for async processing
            std::string log_message = msg.str();
            dispatch_async(getLoggingQueue(), ^{
                std::cerr << log_message << std::endl;
            });
        } else {
            // Synchronous logging
            std::cerr << msg.str() << std::endl;
        }
    #endif
}

static HttpResult makeResult(Response&& resp, const std::string& errMsg) {
    bool ok = errMsg.empty() && resp.status_code >= 200 && resp.status_code < 300;
    if (ok) {
        return HttpResult::success(std::move(resp));
    }
    HttpError err{resp.status_code, errMsg.empty() ? "HTTP request failed" : errMsg};
    return HttpResult::failure(std::move(err));
}

// ======================= Async / Coroutine API =========================
void HttpClient::requestAsync(std::string_view url_sv, std::string_view method_sv, std::string_view body_sv,
                              const std::unordered_map<std::string, std::string>& headers,
                              const std::function<void(HttpResult)>& completion,
                              std::shared_ptr<::launcher::core::utilities::CancellationToken> token) {
    std::string url(url_sv);
    std::string method(method_sv);
    std::string body(body_sv);
    // Prepare URL and request similar to synchronous path, but no sema.
    NSURL* nsUrl = [NSURL URLWithString:[NSString stringWithUTF8String:url.c_str()]];
    if (!nsUrl) {
        if (completion) {
            completion(HttpResult::failure(HttpError{-1, "Invalid URL"}));
        }
        return;
    }

    NSMutableURLRequest* req = [NSMutableURLRequest requestWithURL:nsUrl];
    [req setHTTPMethod:[NSString stringWithUTF8String:method.c_str()]];
    [req setTimeoutInterval:timeoutSeconds_];

    // Apply default headers
    for (const auto& kv : defaultHeaders_) {
        [req setValue:[NSString stringWithUTF8String:kv.second.c_str()]
           forHTTPHeaderField:[NSString stringWithUTF8String:kv.first.c_str()]];
    }
    // Apply extra headers
    for (const auto& kv : headers) {
        [req setValue:[NSString stringWithUTF8String:kv.second.c_str()]
           forHTTPHeaderField:[NSString stringWithUTF8String:kv.first.c_str()]];
    }
    if (!body.empty()) {
        NSData* bodyData = [[NSString stringWithUTF8String:body.c_str()] dataUsingEncoding:NSUTF8StringEncoding];
        [req setHTTPBody:bodyData];
    }

    NSURLSession* session = (__bridge NSURLSession*)impl_->session_;
    if (!session) {
        session = [NSURLSession sharedSession];
    }

    __block Response resp;
    NSURLSessionDataTask* task = [session dataTaskWithRequest:req
                                           completionHandler:^(NSData* data, NSURLResponse* urlResponse, NSError* error) {
        if (error) {
            HttpError he{(int)error.code, [[error localizedDescription] UTF8String]};
            if (completion) {
                completion(HttpResult::failure(std::move(he)));
            }
            return;
        }

        NSHTTPURLResponse* httpResp = (NSHTTPURLResponse*)urlResponse;
        resp.status_code = (int)httpResp.statusCode;

        NSDictionary* hdrs = [httpResp allHeaderFields];
        for (id key in hdrs) {
            NSString* k = [key description];
            NSString* v = [[hdrs objectForKey:key] description];
            resp.headers[[k UTF8String]] = [v UTF8String];
        }
        if (data) {
            resp.body.assign((const char*)[data bytes], [data length]);
        }
        if (completion) {
            completion(HttpResult::success(std::move(resp)));
        }
    }];
    [task resume];
}

http::HttpRequestAwaitable HttpClient::coRequest(
    std::string_view url_sv, std::string_view method_sv, std::string_view body_sv,
    const std::unordered_map<std::string, std::string>& headers,
    std::shared_ptr<::launcher::core::utilities::CancellationToken> token) {
    return http::HttpRequestAwaitable{this, std::string(url_sv), std::string(method_sv), std::string(body_sv), headers, token};
}

::launcher::core::util::AsyncStream<std::string_view> HttpClient::streamChunks(
    std::string_view url_sv, std::string_view method_sv, std::string_view body_sv,
    const std::unordered_map<std::string, std::string>& headers,
    std::shared_ptr<::launcher::core::utilities::CancellationToken> cancellationToken) {
    using ::launcher::core::util::StreamChannel;
    auto channel = std::make_shared<StreamChannel<std::string_view>>();
    std::string url(url_sv);
    std::string method(method_sv);
    std::string body(body_sv);

    // Use thread-safe arena because GCD serial queues may execute on varying threads.
    auto arena = std::make_shared<::launcher::core::memory::ThreadSafeSlabArena>();
    channel->setKeepAlive(std::static_pointer_cast<void>(arena));

    // Early-exit if caller already cancelled
    if (cancellationToken && cancellationToken->isCancelled()) {
        channel->abort(HttpError{-2, "cancelled"});
        return ::launcher::core::util::AsyncStream<std::string_view>{channel};
    }

    // Lambda copies incoming chunk into arena and pushes stable view.
    DataCallback dataCb = [channel, arena, cancellationToken](std::string_view chunk) {
        if (cancellationToken && cancellationToken->isCancelled()) {
            channel->abort(HttpError{-2, "cancelled"});
            return;
        }
        std::string_view sv = arena->append(chunk);
        channel->push(sv);
    };
    CompletionCallback completionCb = [channel, cancellationToken](int status, NSError* error) {
        if (error) {
            channel->abort(HttpError{status, [[error localizedDescription] UTF8String]});
        } else {
            if (cancellationToken && cancellationToken->isCancelled()) {
                channel->abort(HttpError{status, "cancelled"});
            } else {
                channel->close();
            }
        }
    };

    // Build delegate
    HttpStreamingDelegate* delegate = [[HttpStreamingDelegate alloc] initWithCallback:dataCb
                                                                 completionCallback:completionCb
                                                                cancellationToken:cancellationToken];

    // Minimal config – we reuse defaults.

    NSURL* nsUrl = [NSURL URLWithString:[NSString stringWithUTF8String:url.c_str()]];
    if (!nsUrl) {
        channel->abort(HttpError{-1, "Invalid URL"});
        return ::launcher::core::util::AsyncStream<std::string_view>{channel};
    }
    NSMutableURLRequest* req = [NSMutableURLRequest requestWithURL:nsUrl];
    [req setHTTPMethod:[NSString stringWithUTF8String:method.c_str()]];
    [req setTimeoutInterval:timeoutSeconds_];

    for (const auto& kv : defaultHeaders_) {
        [req setValue:[NSString stringWithUTF8String:kv.second.c_str()]
           forHTTPHeaderField:[NSString stringWithUTF8String:kv.first.c_str()]];
    }
    for (const auto& kv : headers) {
        [req setValue:[NSString stringWithUTF8String:kv.second.c_str()]
           forHTTPHeaderField:[NSString stringWithUTF8String:kv.first.c_str()]];
    }
    if (!body.empty()) {
        NSData* bodyData = [[NSString stringWithUTF8String:body.c_str()] dataUsingEncoding:NSUTF8StringEncoding];
        [req setHTTPBody:bodyData];
    }

    NSURLSession* baseSession = (__bridge NSURLSession*)impl_->session_;
    if (!baseSession) {
        channel->abort(HttpError{-1, "session not initialised"});
        return ::launcher::core::util::AsyncStream<std::string_view>{channel};
    }
    NSURLSessionDataTask* task = [baseSession dataTaskWithRequest:req];
    delegate.dataTask = task;
    // Register delegate with multiplexer
    HttpDelegateMux* mux = (__bridge HttpDelegateMux*)delegateMux_;
    [mux registerDelegate:delegate forTask:task];
    [task resume];

    if (cancellationToken) {
        __weak HttpStreamingDelegate* weakDelegate = delegate;
        dispatch_queue_t monitorQ = dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_LOW, 0);
        dispatch_source_t timer = dispatch_source_create(DISPATCH_SOURCE_TYPE_TIMER, 0, 0, monitorQ);
        dispatch_source_set_timer(timer, dispatch_time(DISPATCH_TIME_NOW, 30 * NSEC_PER_MSEC), 30 * NSEC_PER_MSEC, 5 * NSEC_PER_MSEC);
        std::shared_ptr<::launcher::core::utilities::CancellationToken> strongTok = cancellationToken;
        dispatch_source_set_event_handler(timer, ^{
            if (strongTok->isCancelled()) {
                dispatch_source_cancel(timer);
                dispatch_async(dispatch_get_main_queue(), ^{
                    if (weakDelegate) { [weakDelegate cancel]; }
                });
            }
        });
        dispatch_resume(timer);
        // Keep timer alive as long as channel
        channel->setKeepAlive(std::shared_ptr<void>((__bridge_retained void*)timer, [](void* t){ dispatch_source_cancel((__bridge dispatch_source_t)t); }));
    }

    return ::launcher::core::util::AsyncStream<std::string_view>{channel};
}

::launcher::core::util::AsyncStream<http::Delta> HttpClient::streamDeltas(
    std::string_view provider_id_sv, std::string_view url_sv, std::string_view method_sv,
    std::shared_ptr<::launcher::core::utilities::CancellationToken> token,
    std::string_view body_sv, const std::unordered_map<std::string, std::string>& headers) {
    using namespace launcher::core::util;
    auto channel = std::make_shared<StreamChannel<http::Delta>>();
    channel->setCapacity(128);
    std::string provider_id(provider_id_sv);
    std::string url(url_sv);
    std::string method(method_sv);
    std::string body(body_sv);

    auto arena = std::make_shared<::launcher::core::memory::ThreadSafeSlabArena>();
    channel->setKeepAlive(std::static_pointer_cast<void>(arena));

    // Reuse one decoder per thread per provider to avoid malloc churn.
    static thread_local std::unordered_map<std::string, std::shared_ptr<http::SSEDecoder>> decoder_cache;
    std::shared_ptr<http::SSEDecoder> decoder;
    {
        auto it = decoder_cache.find(provider_id);
        if (it != decoder_cache.end()) {
            decoder = it->second;
            if (decoder->hasError()) {
                // Previous request flagged parse error – recreate fresh decoder.
                decoder = nullptr;
                decoder_cache.erase(it);
            }
        }
        if (!decoder) {
            auto tmp = http::SSEDecoder::create(provider_id);
            if (tmp) {
                decoder = std::shared_ptr<http::SSEDecoder>(std::move(tmp));
                decoder_cache.emplace(provider_id, decoder);
            }
        }
    }

    std::shared_ptr<http::SSEDecoder> decoderUnique = decoder; // maintain original var name for minimal diff

    if (decoderUnique) {
        decoderUnique->setSink(std::static_pointer_cast<launcher::core::memory::MemorySink>(arena));
    }

    DataCallback dataCb;
    if (decoderUnique) {
        // Promote to shared_ptr for safe capture
        std::shared_ptr<http::SSEDecoder> decoder = std::move(decoderUnique);
        dataCb = [decoder, channel](std::string_view raw) {
            size_t start = 0;
            while (true) {
                size_t pos = raw.find('\n', start);
                std::string_view lineView = (pos == std::string::npos) ? raw.substr(start) : raw.substr(start, pos - start);
                if (!lineView.empty()) {
                    auto dOpt = decoder->feed(lineView);
                    if (decoder->hasError()) {
                        channel->abort(HttpError{-3, "decoder parse error"});
                        return;
                    }
                    if (dOpt) {
                        channel->push(*dOpt);
                        if (dOpt->done) channel->close();
                    }
                }
                if (pos == std::string::npos) break;
                start = pos + 1;
            }
        };
    } else {
        // Fallback: copy chunk into SlabArena and push stable view
        dataCb = [channel, arena](std::string_view chunk) {
            std::string_view sv = arena->append(chunk);
            channel->push(http::Delta{"assistant", sv, false});
        };
    }

    CompletionCallback completionCb = [channel, token](int status, NSError* error) {
        if (error) {
            channel->abort(HttpError{status, [[error localizedDescription] UTF8String]});
            return;
        }
        if (token && token->isCancelled()) {
            channel->abort(HttpError{status, "cancelled"});
            return;
        }
        if (status < 200 || status >= 300) {
            std::string msg = "HTTP status code " + std::to_string(status);
            channel->abort(HttpError{status, msg});
            return;
        }
        channel->close();
    };

    // Launch streaming request using same approach as streamChunks but custom delegate.
    HttpStreamingDelegate* delegate = [[HttpStreamingDelegate alloc] initWithCallback:dataCb
                                                                 completionCallback:completionCb
                                                                cancellationToken:token];

    NSURL* nsUrl = [NSURL URLWithString:[NSString stringWithUTF8String:url.c_str()]];
    if (!nsUrl) {
        channel->abort(HttpError{-1, "Invalid URL"});
        return AsyncStream<http::Delta>{channel};
    }
    NSMutableURLRequest* req = [NSMutableURLRequest requestWithURL:nsUrl];
    [req setHTTPMethod:[NSString stringWithUTF8String:method.c_str()]];
    [req setTimeoutInterval:timeoutSeconds_];

    for (const auto& kv : defaultHeaders_) {
        [req setValue:[NSString stringWithUTF8String:kv.second.c_str()]
           forHTTPHeaderField:[NSString stringWithUTF8String:kv.first.c_str()]];
    }
    for (const auto& kv : headers) {
        [req setValue:[NSString stringWithUTF8String:kv.second.c_str()]
           forHTTPHeaderField:[NSString stringWithUTF8String:kv.first.c_str()]];
    }
    if (!body.empty()) {
        NSData* data = [[NSString stringWithUTF8String:body.c_str()] dataUsingEncoding:NSUTF8StringEncoding];
        [req setHTTPBody:data];
    }

    NSURLSession* baseSession2 = (__bridge NSURLSession*)impl_->session_;
    if (!baseSession2) {
        channel->abort(HttpError{-1, "session not initialised"});
        return AsyncStream<http::Delta>{channel};
    }
    NSURLSessionDataTask* task = [baseSession2 dataTaskWithRequest:req];
    HttpDelegateMux* mux2 = (__bridge HttpDelegateMux*)delegateMux_;
    [mux2 registerDelegate:delegate forTask:task];
    delegate.dataTask = task;
    [task resume];

    if (token) {
        __weak HttpStreamingDelegate* weakDelegate = delegate;
        dispatch_queue_t monitorQ = dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_LOW, 0);
        dispatch_source_t timer = dispatch_source_create(DISPATCH_SOURCE_TYPE_TIMER, 0, 0, monitorQ);
        dispatch_source_set_timer(timer, dispatch_time(DISPATCH_TIME_NOW, 30 * NSEC_PER_MSEC), 30 * NSEC_PER_MSEC, 5 * NSEC_PER_MSEC);
        std::shared_ptr<::launcher::core::utilities::CancellationToken> strongTok = token;
        dispatch_source_set_event_handler(timer, ^{
            if (strongTok->isCancelled()) {
                dispatch_source_cancel(timer);
                dispatch_async(dispatch_get_main_queue(), ^{
                    if (weakDelegate) { [weakDelegate cancel]; }
                });
            }
        });
        dispatch_resume(timer);
        // Keep timer alive as long as channel
        channel->setKeepAlive(std::shared_ptr<void>((__bridge_retained void*)timer, [](void* t){ dispatch_source_cancel((__bridge dispatch_source_t)t); }));
    }

    return AsyncStream<http::Delta>{channel};
}

// ------------------ Coroutine helpers (non-blocking) ------------------
::launcher::core::util::AsyncStream<std::string_view> HttpClient::coStreamingGet(
    std::string_view url,
    std::shared_ptr<::launcher::core::utilities::CancellationToken> token,
    const std::unordered_map<std::string, std::string>& headers) {
    // Currently token is not wired into streamChunks; caller may cancel by closing channel.
    if (token && token->isCancelled()) {
        return streamChunks(url, "GET", "", headers, token);
    }
    return streamChunks(url, "GET", "", headers, token);
}

::launcher::core::util::AsyncStream<std::string_view> HttpClient::coStreamingPost(
    std::string_view url,
    std::string_view body,
    std::shared_ptr<::launcher::core::utilities::CancellationToken> token,
    const std::unordered_map<std::string, std::string>& headers) {
    if (token && token->isCancelled()) {
        return streamChunks(url, "POST", body, headers, token);
    }
    return streamChunks(url, "POST", body, headers, token);
}

} // namespace http

#endif // __APPLE__ 