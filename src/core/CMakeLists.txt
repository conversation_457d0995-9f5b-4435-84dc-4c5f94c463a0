# Create core library
add_library(core STATIC
    index/app_index.cpp
    search/search_history.cpp
    search/search_cache.cpp
    platform/common/platform_common.cpp
    chat/unified_chat_backend.cpp
    chat/conversation_manager.cpp
    persistence/conversation_store.cpp
    persistence/session_serializer.cpp
    events/event_bus_service.cpp
    diagnostics/diagnostics_service.cpp
    diagnostics/metrics_exporter.cpp
    $<$<BOOL:${KAI_ENABLE_VPATCH}>:security/hotpatch_table_service.cpp>
    diagnostics/queue_alerts.cpp
    foundation/service_static_checks_inst.cpp
    security/sandbox.cpp
    security/dynamic_verifier_registry.cpp
    runtime/runtime_scanner.cpp
)

# Include directories
target_include_directories(core
    PUBLIC
        ${CMAKE_SOURCE_DIR}/include
        ${CMAKE_CURRENT_SOURCE_DIR}
        ${CMAKE_CURRENT_SOURCE_DIR}/include
        ${CMAKE_CURRENT_SOURCE_DIR}/persistence
        ${CMAKE_CURRENT_SOURCE_DIR}/history
        ${absl_SOURCE_DIR}
    PRIVATE
        ${cmark_SOURCE_DIR}/src
        ${cmark_BINARY_DIR}/src
        ${cmark_SOURCE_DIR}/extensions
        ${cmark_BINARY_DIR}/extensions
)

# Add platform-specific sources and libraries
if(APPLE)
    target_sources(core PRIVATE
        platform/macos/macos_platform.mm
        scanner/macos/macos_app_scanner.mm
        model/Message.mm
        renderer/MarkdownHTMLConverter.mm
        renderer/RenderCache.mm
        renderer/HighlightJSBridge.mm
        chat/unified_chat_backend.cpp
        history/mac_history_backend.mm
        security/codesign_verify.mm
        security/seatbelt_verifier.mm
        $<$<BOOL:${KAI_ENABLE_VPATCH}>:security/hotpatch_verifier_table.cpp>
    )
    
    # Set Objective-C++ properties for .mm files
    set_source_files_properties(
        platform/macos/macos_platform.mm
        scanner/macos/macos_app_scanner.mm
        model/Message.mm
        renderer/MarkdownHTMLConverter.mm
        renderer/RenderCache.mm
        renderer/HighlightJSBridge.mm
        chat/unified_chat_backend.cpp
        history/mac_history_backend.mm
        security/codesign_verify.mm
        security/seatbelt_verifier.mm
        PROPERTIES
        COMPILE_FLAGS "-x objective-c++"
        SKIP_PRECOMPILE_HEADERS ON
    )
    
    find_library(APPKIT_LIBRARY AppKit REQUIRED)
    find_library(FOUNDATION_LIBRARY Foundation REQUIRED)
    find_library(CARBON_LIBRARY Carbon REQUIRED)
    find_library(SERVICE_MANAGEMENT_LIBRARY ServiceManagement REQUIRED)
    find_library(JAVASCRIPTCORE_LIBRARY JavaScriptCore REQUIRED)
    find_library(SCRIPTINGBRIDGE_LIBRARY ScriptingBridge REQUIRED)
    find_library(SECURITY_LIBRARY Security REQUIRED)
    target_link_libraries(core PUBLIC ${APPKIT_LIBRARY} ${FOUNDATION_LIBRARY} ${CARBON_LIBRARY} ${SERVICE_MANAGEMENT_LIBRARY} ${JAVASCRIPTCORE_LIBRARY} ${SCRIPTINGBRIDGE_LIBRARY} ${SECURITY_LIBRARY})
endif()

# Include hotpatch verifier table for non-Apple builds when feature flag is ON
if(NOT APPLE AND KAI_ENABLE_VPATCH)
    target_sources(core PRIVATE security/hotpatch_verifier_table.cpp)
endif()

# Link with dependencies
target_link_libraries(core
    PUBLIC
        nlohmann_json::nlohmann_json
        libcmark-gfm_static
        libcmark-gfm-extensions_static
        ranking
        context
        llm
        config
        core_util
        core_memory
        core_foundation
        core_async
        absl::hash
        absl::flat_hash_map
)

# Propagate allocator library to consumers (launcher) so the final link includes it
target_link_libraries(core PUBLIC ${KAI_ALLOCATOR_LIB})

# If FlatSnapshot signing is enabled link crypto helper.
if(KAI_ENABLE_FLATSNAPSHOT_SIGN)
    target_link_libraries(core PUBLIC kai_crypto)

    # Ensure libsodium (provider of generated headers) is fully built before core sources compile if the target exists (built via FetchContent).
    if(TARGET libsodium)
        add_dependencies(core libsodium)
    endif()
endif()

# Add subdirectories
add_subdirectory(index)
add_subdirectory(scanner)
add_subdirectory(util)
add_subdirectory(memory)
add_subdirectory(foundation)
add_subdirectory(search)
add_subdirectory(platform)
add_subdirectory(config)
add_subdirectory(context)
add_subdirectory(providers)
add_subdirectory(llm)
add_subdirectory(http)
add_subdirectory(plugins)
add_subdirectory(container)
add_subdirectory(storage)
add_subdirectory(net)
add_subdirectory(async)

# Add history manager sources
target_sources(core PRIVATE
    history/history_manager.cpp
)

# Include history directory
target_include_directories(core
    PUBLIC
        ${CMAKE_CURRENT_SOURCE_DIR}/history
)

# (G-6) rpmalloc include directory already propagated globally via FetchContent;
# omit duplicate include to avoid path clutter.

if(BUILD_TESTING)
    target_compile_definitions(core PUBLIC TESTING)
endif()

# Ensure service headers are generated before compiling core (Slice-2)
add_dependencies(core generate_service_headers)

# -----------------------------------------------------------------------------
# Seatbelt profile perfect-hash header generation
# -----------------------------------------------------------------------------

find_package(Python3 COMPONENTS Interpreter REQUIRED)

set(SEATBELT_PROFILE_LIST "${CMAKE_SOURCE_DIR}/resources/seatbelt_profiles.txt")
set(SEATBELT_PROFILE_HEADER "${CMAKE_BINARY_DIR}/generated/seatbelt_profile.phf.h")

add_custom_command(
    OUTPUT ${SEATBELT_PROFILE_HEADER}
    COMMAND ${Python3_EXECUTABLE} ${CMAKE_SOURCE_DIR}/tools/seatbelt_delta_gen.py
            ${SEATBELT_PROFILE_LIST} ${SEATBELT_PROFILE_HEADER}
    DEPENDS ${SEATBELT_PROFILE_LIST} ${CMAKE_SOURCE_DIR}/tools/seatbelt_delta_gen.py
    COMMENT "Generating Seatbelt profile perfect-hash header"
    VERBATIM
)

add_custom_target(generate_seatbelt_phf DEPENDS ${SEATBELT_PROFILE_HEADER})

# Make core depend on generated header and expose include dir
add_dependencies(core generate_seatbelt_phf)
target_include_directories(core BEFORE PUBLIC ${CMAKE_BINARY_DIR}/generated)
target_include_directories(core BEFORE PUBLIC ${CMAKE_BINARY_DIR}/generated/services)

# Precompiled header ---------------------------------------------------------
# Use generator expressions so the PCH is only applied to C++ compilation
# units and not Objective-C++ (.mm) sources which would otherwise trigger
# Clang warnings.
if(CMAKE_CXX_COMPILER_ID MATCHES "Clang" OR CMAKE_CXX_COMPILER_ID MATCHES "GNU" OR CMAKE_CXX_COMPILER_ID MATCHES "AppleClang")
    target_precompile_headers(core PUBLIC
        $<$<COMPILE_LANGUAGE:CXX>:${CMAKE_CURRENT_SOURCE_DIR}/pch.h>
    )
endif()

# Link the umbrella core library to networking components
target_link_libraries(core PUBLIC core_net) 

# Add compile definition for release builds to enable cache preallocation
target_compile_definitions(core PRIVATE
    $<$<CONFIG:Release>:KAI_CACHE_PREALLOC_MAX_WAYS=1>
    $<$<CONFIG:RelWithDebInfo>:KAI_CACHE_PREALLOC_MAX_WAYS=1>
)

# ---------------- JSON utility wrapper ---------------------
add_library(json_util
    json/json_writer.cpp
)

target_link_libraries(json_util PUBLIC yyjson)

target_include_directories(json_util PUBLIC ${CMAKE_CURRENT_SOURCE_DIR})