#pragma once

#include <string>
#include <string_view>
#include <functional>
#include <memory>
#include "capability.h"
#include "../util/stream_generator.h"
#include "../http/sse_decoder.h"

namespace http { using DataCallback = std::function<void(std::string_view)>; }

namespace launcher {
namespace core {
class Context;
namespace utilities { class CancellationToken; }

/**
 * @brief Provider-agnostic interface for all LLM back-ends.
 *
 *  The goal is to expose the minimal surface that the higher-level LlmClient
 *  needs while keeping provider-specific details hidden behind the adapter.
 */
class LlmModel {
 public:
    virtual ~LlmModel() = default;

    /** Concrete model identifier (e.g. "gpt-4o", "claude-3-opus"). */
    virtual std::string name() const = 0;

    /** Provider identifier (e.g. "openai", "anthropic"). */
    virtual std::string provider() const = 0;

    // Capability helpers
    virtual CapabilitySet capabilities() const = 0;
    bool has(Capability cap) const { return capabilities().test(static_cast<size_t>(cap)); }

    // Core inference APIs (canonical names match existing concrete classes)
    virtual std::string complete(std::string_view prompt, const Context& ctx) = 0;
    virtual std::string stream(std::string_view prompt, const Context& ctx,
                                                  http::DataCallback cb,
                                                  std::shared_ptr<utilities::CancellationToken> token) = 0;

    // Coroutine-based streaming – returns parsed delta objects.
    virtual ::launcher::core::util::AsyncStream<http::Delta> streamAsync(
        std::string_view prompt, const Context& ctx,
        std::shared_ptr<utilities::CancellationToken> token) = 0;
};

} // namespace core
} // namespace launcher 