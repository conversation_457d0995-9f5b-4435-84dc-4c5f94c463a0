#include "api_family_rules.h"

namespace launcher::core {

constexpr std::array<ApiRule, 8> kRules = {
    ApiRule{"openai", "gpt-4o",    apiBit(ApiFamily::kChatCompletions) | apiBit(ApiFamily::kResponses), ApiFamily::kResponses},
    ApiRule{"openai", "gpt-4",     apiBit(ApiFamily::kChatCompletions),                              ApiFamily::kChatCompletions},
    ApiRule{"openai", "gpt-3.5",   apiBit(ApiFamily::kChatCompletions),                              ApiFamily::kChatCompletions},
    ApiRule{"openai", "o3",        apiBit(ApiFamily::kResponses),                                    ApiFamily::kResponses},
    ApiRule{"openai", "o1",        apiBit(ApiFamily::kResponses),                                    ApiFamily::kResponses},
    ApiRule{"openai", "computer-use", apiBit(ApiFamily::kResponses),                                 ApiFamily::kResponses},
    ApiRule{"anthropic", "",        apiBit(ApiFamily::kMessages),                                    ApiFamily::kMessages}, // matches any anthropic model
    ApiRule{"*", "",                apiBit(ApiFamily::kUnknown),                                     ApiFamily::kUnknown}
};

const std::array<ApiRule, 8> kApiFamilyRules = kRules;

} // namespace launcher::core 