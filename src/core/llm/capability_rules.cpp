#include "capability_rules.h"
#include <span>
#include <string_view> // Required for _sv literal

namespace launcher::core {

// Helper alias
using R = CapabilityRule;
using namespace std::string_view_literals; // For _sv literal

constexpr std::array<R, 15> kCapabilityRules = {
    // ---------------- OPENAI -------------------------------------------------
    R{ "openai"sv, "gpt-4.1"sv,     bit(Capability::LargeContext) |
                                    bit(Capability::ImageInput)   | 
                                    bit(Capability::AudioInput)   |
                                    bit(Capability::SpeechGeneration) },

    R{ "openai"sv, "gpt-4o"sv,      bit(Capability::LargeContext) |
                                    bit(Capability::ImageInput)   |
                                    bit(Capability::AudioInput)   |
                                    bit(Capability::SpeechGeneration) |
                                    bit(Capability::MultimodalRealtime) },

    R{ "openai"sv, "gpt-4-turbo"sv, bit(Capability::LargeContext) |
                                    bit(Capability::ImageInput) },

    // o-series reasoning operators (e.g. o200, gpt-4o-mini)
    R{ "openai"sv, "o1"sv,          bit(Capability::HighReasoning) }, // prefix handled separately
    R{ "openai"sv, "o3"sv,          bit(Capability::HighReasoning) }, // prefix handled separately
    R{ "openai"sv, "o4"sv,          bit(Capability::HighReasoning) }, // prefix handled separately

    // Image & audio specialist
    R{ "openai"sv, "dall-e"sv,      bit(Capability::ImageGeneration) },
    R{ "openai"sv, "whisper"sv,     bit(Capability::AudioInput) | bit(Capability::SpeechGeneration) },

    // ---------------- ANTHROPIC ---------------------------------------------
    R{ "anthropic"sv, "opus-4"sv,   bit(Capability::LargeContext) |
                                    bit(Capability::HighReasoning) |
                                    bit(Capability::ImageInput) },

    R{ "anthropic"sv, "sonnet-4"sv, bit(Capability::LargeContext) |
                                    bit(Capability::HighReasoning) |
                                    bit(Capability::ImageInput) },

    R{ "anthropic"sv, "sonnet-3.7"sv,   bit(Capability::LargeContext) | 
                                        bit(Capability::HighReasoning) |
                                        bit(Capability::ImageInput) },

    R{ "anthropic"sv, "claude-3"sv,  bit(Capability::LargeContext) | bit(Capability::ImageInput) },

    R{ "anthropic"sv, "claude-2"sv,  bit(Capability::LargeContext) },
    
    // Placeholder for potential future rules to maintain array size if needed by extern declaration.
    // If the size is strictly 12, these can be removed.
    // For now, assuming 15 is the correct size based on the header.
    // If these are not actual rules, the array size in capability_rules.h should be 12.
    // Let's assume the size 15 is correct and these are padding or future slots.
    R{ ""sv, ""sv, 0 },
    R{ ""sv, ""sv, 0 },

    // ------------------------------------------------------------------------
    // Add new rules above this comment.
    // ------------------------------------------------------------------------
};

// ---------------------------------------------------------------------------

} // namespace launcher::core