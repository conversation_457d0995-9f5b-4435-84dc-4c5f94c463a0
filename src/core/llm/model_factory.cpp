#include "model_factory.h"

#include "llm_model.h"
#include "../util/debug.h"
#include <algorithm>
#include <cstdlib>
#include <memory>

#ifdef __APPLE__
#include <dlfcn.h>
#endif

namespace launcher {
namespace core {

std::shared_ptr<LlmModel> ModelFactory::create(const std::string& provider_id,
                                                    const std::string& model_id,
                                                    const std::string& explicit_key) {
    if (!registry_) {
        ERR("ModelFactory: registry not set");
        return nullptr;
    }

    // Lookup provider configuration from IConfigManager
    auto providers = cfg_.getAIProviders();
    auto pit = std::find_if(providers.begin(), providers.end(), [&](const AIProviderConfig& p) {
        return p.provider_id == provider_id;
    });
    if (pit == providers.end()) {
        ERR("ModelFactory: unknown provider id " << provider_id << ", attempting plugin load");
#ifdef __APPLE__
        std::string libName = "libkai_provider_" + provider_id + ".dylib";
        void* handle = dlopen(libName.c_str(), RTLD_LAZY | RTLD_GLOBAL);
        if (!handle) {
            ERR("dlopen failed: " << dlerror());
            return nullptr;
        }
        // assume plugin's static initializers register provider into registry
        // reload providers list
        providers = cfg_.getAIProviders();
        pit = std::find_if(providers.begin(), providers.end(), [&](const AIProviderConfig& p) { return p.provider_id == provider_id; });
        if (pit == providers.end()) {
            ERR("Provider config missing even after plugin load");
            // we still can proceed with dummy cfg
            AIProviderConfig dummy; dummy.provider_id = provider_id; providers.push_back(dummy); pit = providers.end()-1;
        }
#else
        return nullptr;
#endif
    }
    const AIProviderConfig& provCfg = *pit;

    std::string apiKey = explicit_key.empty() ? provCfg.api_key : explicit_key;
    if (apiKey.empty() && !provCfg.api_key_variable.empty()) {
        const char* envVal = std::getenv(provCfg.api_key_variable.c_str());
        if (envVal) apiKey = envVal;
    }

    // Delegate directly to registry; options will be handled inside provider-specific constructors.
    return registry_->create(provider_id, model_id, apiKey);
}

} // namespace core
} // namespace launcher 