/*
 * reasoning_control.h – optional provider-agnostic container for reasoning / extended-thinking parameters
 */
#pragma once

#include <optional>
#include <string>

namespace launcher {
namespace core {

struct ReasoningControl {
    // Desired reasoning effort (provider-specific – e.g. "low", "medium", "high")
    std::string effort = "medium";
    // Requested summary granularity ("auto", "detailed", "none")
    std::string summary = "auto";
    // Budget for hidden thinking tokens (Anthropic)
    std::optional<int> budget; // nullopt → provider default
};

} // namespace core
} // namespace launcher 