#include "plugin_loader.h"

#include "../util/debug.h"
#include <filesystem>
#ifdef __APPLE__
#include <dlfcn.h>
#endif

namespace fs = std::filesystem;

namespace launcher {
namespace core {

bool PluginLoader::load(const std::string& path) {
#ifdef __APPLE__
    // Hardened flags: resolve symbols immediately (RTLD_NOW) but keep them
    // local to this image (RTLD_LOCAL) to avoid symbol leakage across namespaces.
    // Do NOT use RTLD_GLOBAL which violates hardened-runtime isolation rules.
#  if defined(RTLD_FIRST) // macOS 10.14+
    void* handle = dlopen(path.c_str(), RTLD_NOW | RTLD_LOCAL | RTLD_FIRST);
#  else
    void* handle = dlopen(path.c_str(), RTLD_NOW | RTLD_LOCAL);
#  endif
    if (!handle) {
        ERR("PluginLoader: dlopen failed for " << path << ": " << dlerror());
        return false;
    }
    DBG("PluginLoader: loaded " << path);
    return true;
#else
    ERR("Plugin loading not supported on this platform yet");
    return false;
#endif
}

void PluginLoader::loadDirectory(const std::string& dir_path) {
    if (!fs::exists(dir_path)) {
        DBG("PluginLoader: directory " << dir_path << " not found – skipping");
        return;
    }

    try {
        for (const auto& entry : fs::directory_iterator(dir_path)) {
            if (!entry.is_regular_file()) continue;
            const auto& p = entry.path();
            if (p.extension() == ".dylib") {
                load(p.string());
            }
        }
    } catch (const std::exception& e) {
        ERR("PluginLoader: directory scan failed: " << e.what());
    }
}

} // namespace core
} // namespace launcher 