#pragma once

#include <rapidjson/stringbuffer.h>
#include <rapidjson/writer.h>
#include <string_view>
#include <string>
#include <unordered_map>
#include <memory>

#include "../context/context.h"
#include "http/sse_decoder.h"
#include "api_family.h"

// Forward declare generic HTTP SSE decoder interface (global namespace)
namespace http { class ISseDecoder; }

namespace launcher {
namespace core {

// Capability module interface implemented by provider-specific endpoint handlers.
// TOptions is the provider's strongly-typed Options struct.
template <typename TOptions>
class IApiEndpoint {
 public:
    virtual ~IApiEndpoint() = default;

    // Canonical provider id (lowercase, e.g. "openai" or "anthropic").
    virtual std::string providerId() const = 0;

    // Returns the api family used for api endpoint routing.
    virtual ApiFamily kind() const = 0;

    // Default headers applied when creating/borrowing a pooled HttpClient.
    virtual std::unordered_map<std::string, std::string>
    defaultHeaders(const TOptions& options) const = 0;

    // Fully-qualified HTTPS endpoint URL for this capability.
    virtual std::string endpoint(const TOptions& options) const = 0;

    // Serialise request payload – called per invocation.
    virtual void buildPayload(rapidjson::Writer<rapidjson::StringBuffer>& writer,
                              std::string_view prompt,
                              const Context& context,
                              const TOptions& options,
                              const std::string& model_name) const = 0;

    // Optional custom SSE decoder.  Return nullptr to fall back to the shared
    // generic JSON delta decoder.
    virtual std::unique_ptr<http::ISseDecoder> createDecoder() const { return nullptr; }

    // Optional provider-specific post-processing hook (e.g. base64 image → png).
    // Default implementation is no-op.
    virtual std::string postProcess(std::string_view rawResponse) const { return std::string(rawResponse); }
};

} // namespace core
} // namespace launcher 