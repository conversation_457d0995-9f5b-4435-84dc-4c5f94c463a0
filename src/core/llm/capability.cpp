#include "capability.h"

#include <algorithm>
#include <unordered_map>

namespace launcher {
namespace core {

namespace {
static const std::unordered_map<Capability, std::string> kCapToString = {
    {Capability::TextGeneration, "text_generation"},
    {Capability::JsonMode, "json_mode"},
    {Capability::ToolUse, "tool_use"},
    {Capability::FunctionCalling, "function_calling"},
    {Capability::HighReasoning, "high_reasoning"},
    {Capability::ImageInput, "image_input"},
    {Capability::AudioInput, "audio_input"},
    {Capability::LargeContext, "large_context"},
    {Capability::ImageGeneration, "image_generation"},
    {Capability::SpeechGeneration, "speech_generation"},
    {Capability::MultimodalRealtime, "multimodal_realtime"},
    {Capability::ComputerUse, "computer_use"},
};

static std::unordered_map<std::string, Capability> buildReverse() {
    std::unordered_map<std::string, Capability> m;
    for (const auto& kv : kCapToString) {
        m[kv.second] = kv.first;
    }
    return m;
}

static const std::unordered_map<std::string, Capability> kStringToCap = buildReverse();
} // namespace

std::string capabilityToString(Capability cap) {
    auto it = kCapToString.find(cap);
    return (it == kCapToString.end()) ? "unknown" : it->second;
}

std::optional<Capability> capabilityFromString(const std::string& name) {
    auto lc = name;
    std::transform(lc.begin(), lc.end(), lc.begin(), ::tolower);
    auto it = kStringToCap.find(lc);
    if (it == kStringToCap.end()) return std::nullopt;
    return it->second;
}

CapabilitySet capabilitySetFromStrings(const std::vector<std::string>& names) {
    CapabilitySet out;
    for (const auto& n : names) {
        if (auto cap = capabilityFromString(n)) {
            out.set(static_cast<size_t>(*cap));
        }
    }
    return out;
}

} // namespace core
} // namespace launcher 