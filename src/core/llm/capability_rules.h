#pragma once

// capability_rules.h – constexpr table describing provider/model pattern → capability mask
//
//  Stage-1 optimisation: move heuristic logic into data to improve
//  maintainability and allow compile-time evaluation.

#include "capability.h"
#include <array>
#include <cstdint>
#include <string_view>
#include <span>

namespace launcher::core {

// -----------------------------------------------------------------------------
// Encode CapabilitySet as a 64-bit mask (kCapabilityCount <= 64).
// -----------------------------------------------------------------------------
constexpr uint64_t bit(Capability cap) {
    return 1ULL << static_cast<size_t>(cap);
}

struct CapabilityRule {
    std::string_view provider; // already lower-case
    std::string_view pattern;  // lower-case substring to match in model id
    uint64_t         mask;     // OR of bit(C) flags
};

// Forward declaration & inline definition – must be visible in every TU to avoid
// undefined symbol errors caused by the previous out-of-line-only constexpr.
// The backing array `kCapabilityRules` is still defined in capability_rules.cpp
// and declared here via `extern` so we don't duplicate large data in every TU.

// The size of the array (15) needs to be updated if rules are added/removed.
// Consider using `std::to_array` in C++23 if the definition can be moved to the header,
// or a helper function to get the span if the array size is dynamic or complex to determine here.
// For now, assuming the size is fixed and known.
extern const std::array<CapabilityRule, 15> kCapabilityRules;

constexpr inline std::span<const CapabilityRule> getCapabilityRules() noexcept {
    return std::span<const CapabilityRule>(kCapabilityRules.data(), kCapabilityRules.size());
}

} // namespace launcher::core