#pragma once

// Shared utilities for grouping and sorting model IDs across the UI.
// Extracted from models_pane_controller.mm / ComposerView.mm so the logic
// is defined in a single place.
//
// Author: kai-agent
// -----------------------------------------------------------------------------

#include <algorithm>
#include <cctype>
#include <regex>
#include <string>
#include <vector>

namespace launcher {
namespace core {

/** Parsed metadata about a model id (e.g. "gpt-4o", "claude-3-sonnet"). */
struct ParsedModelId {
    double major = 0.0;          // numeric major version (4, 3.5)
    int context_k = 0;           // context window length (tokens)
    int variant_rank = 99;       // rank for full id variant (e.g. vision, turbo)
    int group_variant_rank = 99; // rank for prefix variant (e.g. "4o" beats "4")
    std::string group_key;       // grouping key (e.g., "gpt-4o", "gpt-4", "claude-3")
    std::string group_title;     // user-visible title ("GPT-4o", "GPT-4", "Claude 3")
};

// -----------------------------------------------------------------------------
// Variant ranking helpers (lower value = higher priority in display order)
// -----------------------------------------------------------------------------
inline int rankOpenAIVariant(const std::string& token) {
    if (token == "o")               return 0;   // gpt-4o
    if (token == "turbo")           return 1;
    if (token == "vision" || token == "vision-preview") return 2;
    if (token == "32k" || token == "16k")     return 3;
    return 10;
}

inline int rankAnthropicVariant(const std::string& token) {
    if (token == "opus")   return 0;
    if (token == "sonnet" || token == "3.7-sonnet" || token == "3.5-sonnet") return 1;
    if (token == "haiku")  return 2;
    return 10;
}

// -----------------------------------------------------------------------------
// Main parser – returns rich metadata for grouping & sorting.
// -----------------------------------------------------------------------------
inline ParsedModelId parseModelId(const std::string& id, const std::string& provider) {
    ParsedModelId out;

    // 1) Tokenise on '-' preserving order.
    std::vector<std::string> tokens;
    size_t start = 0, end;
    while ((end = id.find('-', start)) != std::string::npos) {
        tokens.push_back(id.substr(start, end - start));
        start = end + 1;
    }
    tokens.push_back(id.substr(start));

    if (tokens.empty()) {
        out.group_key   = id;
        out.group_title = id;
        return out;
    }

    const std::string& base_prefix = tokens[0];
    std::string provider_lc = provider;
    std::transform(provider_lc.begin(), provider_lc.end(), provider_lc.begin(), ::tolower);

    // 2) Extract numeric *major* version either from second token or from prefix.
    std::string numeric_part;
    std::smatch match_num;
    if (tokens.size() >= 2 && std::regex_search(tokens[1], match_num, std::regex(R"((\d+(?:\.\d+)?))"))) {
        numeric_part = match_num[1].str();
    } else if (std::regex_search(base_prefix, match_num, std::regex(R"((\d+(?:\.\d+)?))"))) {
        numeric_part = match_num[1].str();
    }
    if (!numeric_part.empty()) {
        try { out.major = std::stod(numeric_part); } catch (...) { out.major = 0.0; }
    }

    // 3) Anthropic special-case – distinguish 3-5 / 3-7 families.
    const bool isAnthropicClaude = (provider_lc == "anthropic" && base_prefix == "claude");
    std::string group_key_override;
    if (isAnthropicClaude && tokens.size() >= 3 && tokens[1] == "3") {
        const std::string& minor_token = tokens[2];
        if (std::all_of(minor_token.begin(), minor_token.end(), ::isdigit)) {
            group_key_override = base_prefix + "-" + tokens[1] + "-" + minor_token;
            try { out.major = std::stod(tokens[1] + "." + minor_token); } catch (...) {}
        }
    }

    const bool prefix_has_digit = std::any_of(base_prefix.begin(), base_prefix.end(), ::isdigit);
    const std::string token1 = (tokens.size() >= 2) ? tokens[1] : "";

    if (prefix_has_digit) {
        out.group_key = base_prefix;
    } else if (!token1.empty()) {
        out.group_key = group_key_override.empty() ? base_prefix + "-" + token1 : group_key_override;
    } else {
        out.group_key = base_prefix;
    }

    // 4) Group-level variant rank for GPT & Claude families.
    if (!token1.empty()) {
        std::smatch mnum; std::string suffix;
        if (std::regex_search(token1, mnum, std::regex(R"((\d+(?:\.\d+)?))"))) {
            suffix = token1.substr(mnum.length());
        } else {
            suffix = token1;
        }
        if (provider_lc == "openai" && base_prefix == "gpt")
            out.group_variant_rank = rankOpenAIVariant(suffix);
        else if (provider_lc == "anthropic" && base_prefix == "claude")
            out.group_variant_rank = rankAnthropicVariant(suffix);
    }

    // 5) Friendly title.
    if (provider_lc == "openai" && base_prefix == "gpt" && !token1.empty()) {
        out.group_title = "GPT-" + token1;
    } else if (provider_lc == "anthropic" && base_prefix == "claude" && !token1.empty()) {
        if (!group_key_override.empty()) {
            out.group_title = "Claude " + group_key_override.substr(strlen("claude-"));
        } else {
            out.group_title = "Claude " + token1;
        }
    } else {
        out.group_title = out.group_key;
        if (!out.group_title.empty()) out.group_title[0] = static_cast<char>(std::toupper(out.group_title[0]));
    }

    // 6) Per-model variant rank (turbo, vision …) – still GPT/Claude specific.
    std::string variant; if (tokens.size() >= 3) variant = tokens[2];
    if (provider_lc == "openai" && base_prefix == "gpt") {
        out.variant_rank = rankOpenAIVariant(variant);
    } else if (provider_lc == "anthropic" && base_prefix == "claude") {
        out.variant_rank = rankAnthropicVariant(variant);
    }

    return out;
}

} // namespace core
} // namespace launcher 