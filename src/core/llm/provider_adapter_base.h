#pragma once

#include "il_provider.h"
#include "../foundation/registry.h"
#include "../util/debug.h"
#include "../plugins/abi.h"
#include <cstring>

namespace kai::llm {

template <class Derived>
class ProviderAdapterBase : public IProvider {
 public:
    std::string_view providerId() const noexcept final { return Derived::kProviderId; }
    CapabilityMask capabilities() const noexcept final { return Derived::kCaps; }

    // IService hooks ----------------------------------------------------
    [[nodiscard]] launcher::core::foundation::ServiceId id() const noexcept override {
        return launcher::core::foundation::ServiceId::kLlmFactoryService;
    }

    launcher::core::util::Result<void> start() override {
        return launcher::core::util::Result<void>::success();
    }

    void stop() noexcept override {}

    // Default lifecycle does nothing; Derived may override.
};

} // namespace kai::llm

// ---------------- Plugin registration macro -------------------------
#define KAI_REGISTER_PROVIDER(DerivedType)                                            \
static std::unique_ptr<DerivedType> _kai_provider_instance;                           \
extern "C" int kai_plugin_initialize(launcher::core::foundation::ServiceRegistry* r){ \
    _kai_provider_instance = std::make_unique<DerivedType>();                         \
    if(auto res = _kai_provider_instance->init(); !res) { _kai_provider_instance.reset(); return 1; } \
    auto regRes = r->registerService(*_kai_provider_instance);                        \
    if(!regRes) {                                                                    \
        if(regRes.error() != launcher::core::foundation::KaiError::AlreadyRegistered){\
            _kai_provider_instance.reset();                                          \
            return 1;                                                                \
        }                                                                            \
    }                                                                                \
    return 0;                                                                        \
}                                                                                     \
extern "C" void kai_plugin_stop(){ if(_kai_provider_instance){ _kai_provider_instance->shutdown(); } _kai_provider_instance.reset(); } \
extern "C" KaiPluginInfo kai_plugin_get_info(){                                      \
    KaiPluginInfo info{};                                                             \
    std::strncpy(info.id, DerivedType::kProviderId, sizeof(info.id));                         \
    info.runtime = KAI_RUNTIME_NULL;                                                  \
    info.abi_major = kKaiAbiMajor; info.abi_minor = kKaiAbiMinor;                     \
    std::memcpy(info.capabilities, &(DerivedType::kCaps), sizeof(info.capabilities)); \
    return info;                                                                      \
} 