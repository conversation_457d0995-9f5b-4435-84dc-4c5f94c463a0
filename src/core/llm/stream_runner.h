#pragma once

// stream_runner.h – provider-agnostic helper that executes a streaming POST
// request and decodes Server-Sent Event (SSE) deltas using the shared
// provider-specific SSEDecoder implementation.
//
// This is a direct, dependency-injected variant of the old
// BaseChatModel::runStreamRequest method, converted to a free function to
// remove deep inheritance.

#include <string>
#include <string_view>
#include <memory>
#include "http/http_client.h"
#include "http/sse_decoder.h"
#include "stream_retry_policy.h"
#include "core/memory/slab_arena.h"
#include "core/util/debug.h"

namespace launcher {
namespace core {
namespace utilities { class CancellationToken; }

struct StreamRunner {
    // Execute streaming request. Throws std::runtime_error on error / cancel.
    static std::string run(std::shared_ptr<http::HttpClient> http_client,
                           std::string_view provider_id,
                           const std::string& endpoint,
                           const std::string& json_body,
                           http::DataCallback external_cb,
                           std::shared_ptr<utilities::CancellationToken> cancel_token);
};

} // namespace core
} // namespace launcher 