/*
 * options_builder.h – Provider-agnostic helper that converts a generic
 * AIProviderConfig into strongly typed Options structure consumed by concrete
 * model adapters.  Implemented via template specialisations keyed by provider
 * Traits.
 */
#pragma once

#include "../config/ai_provider_config.h"
#include "openai/openai_options.h"
#include "anthropic/anthropic_options.h"
#include "../util/result.h"
#include <string>
#include <map>
#include <algorithm>

namespace launcher {
namespace core {

// Primary template – intentionally undefined to trigger a compile-time error
// when a provider lacks a matching specialisation.
// Usage: OptionsBuilder<OpenAIOptions>::fromConfig(cfg);
//        OptionsBuilder<AnthropicOptions>::fromConfig(cfg);

template <typename OptionsT>
struct OptionsBuilder;

// ---------------- OpenAI specialisation ----------------

template <>
struct OptionsBuilder<OpenAIOptions> {
    static OpenAIOptions fromConfig(const AIProviderConfig& cfg) {
        std::map<std::string, std::string> kv;
        if (cfg.request_timeout_seconds) {
            kv["temperature"] = std::to_string(cfg.request_timeout_seconds / 60.0);
        }
        for (const auto &m : cfg.models) {
            if (m.id == cfg.default_model) {
                if (m.max_tokens > 0) kv["max_tokens"] = std::to_string(m.max_tokens);
                if (m.priority > 0) {
                    double top_p = std::min(1.0, static_cast<double>(m.priority) / 100.0);
                    kv["top_p"] = std::to_string(top_p);
                }
            }
        }
        return OpenAIOptionsBuilder::from(kv);
    }
};

// ---------------- Anthropic specialisation ------------

template <>
struct OptionsBuilder<AnthropicOptions> {
    static AnthropicOptions fromConfig(const AIProviderConfig& cfg) {
        std::map<std::string, std::string> kv;
        if (cfg.request_timeout_seconds) {
            kv["temperature"] = std::to_string(cfg.request_timeout_seconds / 60.0);
        }
        for (const auto &m : cfg.models) {
            if (m.id == cfg.default_model) {
                if (m.max_tokens > 0) kv["max_tokens"] = std::to_string(m.max_tokens);
                if (m.priority > 0) {
                    double top_p = std::min(1.0, static_cast<double>(m.priority) / 100.0);
                    kv["top_p"] = std::to_string(top_p);
                }
            }
        }
        return AnthropicOptionsBuilder::from(kv);
    }
};

} // namespace core
} // namespace launcher 