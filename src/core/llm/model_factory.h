/*
 * model_factory.h – runtime façade that creates ILanguageModel instances and
 * lists available models per provider. Combines responsibilities of the old
 * LlmFactory + ProviderCatalog while delegating static registration to
 * ModelRegistry.
 */
#pragma once

#include "model_registry.h"
#include "provider_catalog.h" // reuse cache implementation for now
#include "../interfaces/iconfig_manager.h"
#include "llm_model.h"
#include "plugin_loader.h"

#include <memory>
#include <string>
#include <vector>

namespace launcher {
namespace core {

class ModelFactory {
 public:
    ModelFactory(const IConfigManager& cfg, std::shared_ptr<ModelRegistry> registry)
        : cfg_(cfg), registry_(std::move(registry)), catalog_(cfg, *registry_) {
        // Attempt to load plugins from $KAI_PLUGIN_DIR or default path.
        const char* dir = std::getenv("KAI_PLUGIN_DIR");
        std::string pluginDir = dir ? dir : "plugins";
        PluginLoader::loadDirectory(pluginDir);
    }

    // Disable copy
    ModelFactory(const ModelFactory&) = delete;
    ModelFactory& operator=(const ModelFactory&) = delete;

    // Create model using default config resolution
    std::shared_ptr<LlmModel> create(const std::string& provider_id,
                                     const std::string& model_id,
                                     const std::string& explicit_key = "");

    // List models (cached)
    std::vector<ModelConfig> list(const std::string& provider_id) {
        return catalog_.list(provider_id);
    }

    // Refresh cache
    void refreshAsync() { catalog_.refreshAsync(); }

 private:
    const IConfigManager& cfg_;
    std::shared_ptr<ModelRegistry> registry_;
    ProviderCatalog catalog_;   // owns cache & persistence
};

} // namespace core
} // namespace launcher 