#pragma once

#include <array>
#include <string>
#include <string_view>
#include <cctype>
#include <span>
#include <algorithm>
#include "api_family.h"

namespace launcher::core {

// -----------------------------------------------------------------------------
// API families – which REST endpoint a model can be used with.
// -----------------------------------------------------------------------------

using launcher::core::ApiFamily;
using launcher::core::ApiMask;

struct ApiRule {
    std::string_view provider;   // lower-case provider id ("*" matches any)
    std::string_view pattern;    // substring to match in model id (also lower-case)
    ApiMask          mask;       // OR of apiBit(...)
    ApiFamily        default_api;// Recommended default endpoint (must be one of |mask|)
};

// Rules table lives in api_family_rules.cpp
extern const std::array<ApiRule, 8> kApiFamilyRules;

constexpr inline std::span<const ApiRule> getApiFamilyRules() noexcept {
    return std::span<const ApiRule>(kApiFamilyRules.data(), kApiFamilyRules.size());
}

// -----------------------------------------------------------------------------
// Helper – compute mask for provider+model id (case-insensitive; caller should
// already have provider lowercase; we lowercase the model id here).
// -----------------------------------------------------------------------------

inline ApiMask getSupportedApis(std::string_view provider, std::string_view model_id) {
    auto lower = [](std::string_view sv){
        std::string out(sv);
        std::transform(out.begin(), out.end(), out.begin(), [](unsigned char c){return std::tolower(c);});
        return out;
    };
    const std::string prov = lower(provider);
    const std::string model = lower(model_id);
    ApiMask mask = 0;
    for (const auto& r : getApiFamilyRules()) {
        if (prov == r.provider && model.find(r.pattern) != std::string::npos) {
            mask |= r.mask;
        }
    }
    return mask ? mask : apiBit(ApiFamily::kUnknown);
}

// Return recommended default ApiFamily for provider/model.
// Falls back to kChatCompletions when no rule matches.
inline ApiFamily getDefaultApi(std::string_view provider, std::string_view model_id) {
    auto lower = [](std::string_view sv){
        std::string out(sv);
        std::transform(out.begin(), out.end(), out.begin(), [](unsigned char c){return std::tolower(c);});
        return out;
    };
    const std::string prov = lower(provider);
    const std::string model = lower(model_id);
    for (const auto& r : getApiFamilyRules()) {
        if ((r.provider == "*" || prov == r.provider) && (r.pattern.empty() || model.find(r.pattern) != std::string::npos)) {
            return r.default_api;
        }
    }
    return ApiFamily::kChatCompletions;
}

} // namespace launcher::core 