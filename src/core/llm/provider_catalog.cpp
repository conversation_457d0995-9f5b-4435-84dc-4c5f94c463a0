#include "provider_catalog.h"

#include "../util/debug.h"
#include "../config/config_manager.h"
#include "../util/result.h"

#include <filesystem>
#include <fstream>
#include <future>
#include <nlohmann/json.hpp>
#include <iomanip>

namespace launcher {
namespace core {

using json = nlohmann::json;

ProviderCatalog::ProviderCatalog(const IConfigManager& cfg, const ModelRegistry& reg) : cfg_(&cfg), registry_(&reg) {
    // Load cache once at construction
    loadCacheFromDisk();
}

// ---------------- Persistence helpers -------------------------

std::string ProviderCatalog::getCachePath() const {
#ifdef __APPLE__
    const char* home = std::getenv("HOME");
    std::filesystem::path p = std::filesystem::path(home ? home : "") / "Library" / "Caches" / "kai-agent";
#else
    const char* xdg = std::getenv("XDG_CACHE_HOME");
    std::filesystem::path p;
    if (xdg && *xdg) {
        p = std::filesystem::path(xdg);
    } else {
        const char* home = std::getenv("HOME");
        p = std::filesystem::path(home ? home : "") / ".cache";
    }
    p /= "kai-agent";
#endif
    if (!std::filesystem::exists(p)) {
        std::filesystem::create_directories(p);
    }
    return (p / "models-cache.json").string();
}

bool ProviderCatalog::isCacheFresh(std::time_t ts, int ttl_hours) const {
    auto now = std::chrono::system_clock::to_time_t(std::chrono::system_clock::now());
    return difftime(now, ts) < ttl_hours * 3600;
}

util::Result<void> ProviderCatalog::loadCacheFromDisk() {
    std::string path = getCachePath();
    if (!std::filesystem::exists(path)) {
        return util::Result<void>::failure("Cache file does not exist");
    }

    try {
        std::ifstream in(path);
        json j; in >> j;
        if (!j.contains("timestamp") || !j.contains("providers")) return util::Result<void>::failure("Invalid cache format");

        int ttl = cfg_ ? cfg_->getInt("performance.models_cache_ttl_hours", 6) : 6;
        std::time_t ts = j["timestamp"].get<std::time_t>();
        if (!isCacheFresh(ts, ttl)) {
            DBG("ProviderCatalog cache stale");
            return util::Result<void>::failure("Cache stale");
        }

        std::lock_guard lock(mutex_);
        last_timestamp_ = ts;
        const auto& provJson = j["providers"];
        for (auto it = provJson.begin(); it != provJson.end(); ++it) {
            std::vector<ModelConfig> vec = it.value().get<std::vector<ModelConfig>>();
            cache_[it.key()] = std::move(vec);
        }
        DBG("ProviderCatalog loaded cache with " << cache_.size() << " providers");
    } catch (const std::exception& e) {
        ERR("ProviderCatalog: failed to load cache: " << e.what());
        return util::Result<void>::failure(e.what());
    }
    return util::Result<void>::success();
}

util::Result<void> ProviderCatalog::saveCacheToDisk() {
    std::string path = getCachePath();
    json j;
    j["timestamp"] = std::chrono::system_clock::to_time_t(std::chrono::system_clock::now());
    {
        std::lock_guard lock(mutex_);
        j["providers"] = cache_;
    }
    try {
        std::ofstream out(path);
        out << std::setw(2) << j;
    } catch (const std::exception& e) {
        ERR("ProviderCatalog: failed to write cache: " << e.what());
        return util::Result<void>::failure(e.what());
    }
    return util::Result<void>::success();
}

// ---------------- Fetch & list -------------------------

std::vector<ModelConfig> ProviderCatalog::fetch(const AIProviderConfig& provider) {
    auto models = std::vector<ModelConfig>();
    try {
        if (registry_) {
            models = registry_->listModels(provider.provider_id, provider);
        }
    } catch (const std::exception& e) {
        ERR("ProviderCatalog fetch failed for " << provider.provider_id << ": " << e.what());
    }
    return models;
}

std::vector<ModelConfig> ProviderCatalog::list(const std::string& provider_id) {
    int ttl = cfg_ ? cfg_->getInt("performance.models_cache_ttl_hours", 6) : 6;
    {
        std::lock_guard lock(mutex_);
        if (auto it = cache_.find(provider_id); it != cache_.end()) {
            if (isCacheFresh(last_timestamp_, ttl)) {
                return it->second;
            }
        }
    }

    // not cached or stale, fetch anew
    if (!cfg_) { return {}; }
    auto providers = cfg_->getAIProviders();
    auto pit = std::find_if(providers.begin(), providers.end(), [&](const AIProviderConfig& p) {
        return p.provider_id == provider_id;
    });
    if (pit == providers.end()) {
        ERR("ProviderCatalog: unknown provider id " << provider_id);
        return {};
    }

    auto models = fetch(*pit);
    {
        std::lock_guard lock(mutex_);
        cache_[provider_id] = models;
        last_timestamp_ = std::chrono::system_clock::to_time_t(std::chrono::system_clock::now());
    }
    saveCacheToDisk();
    return models;
}

void ProviderCatalog::clear() {
    std::lock_guard lock(mutex_);
    cache_.clear();
    last_timestamp_ = 0;
    saveCacheToDisk();
}

void ProviderCatalog::refreshAsync() {
    std::async(std::launch::async, [this]() {
        if (!cfg_) { return; }
        auto providers = cfg_->getAIProviders();
        bool changed = false;
        for (const auto& p : providers) {
            auto models = fetch(p);
            if (!models.empty()) {
                std::lock_guard lock(mutex_);
                cache_[p.provider_id] = std::move(models);
                changed = true;
            }
        }
        if (changed) {
            last_timestamp_ = std::chrono::system_clock::to_time_t(std::chrono::system_clock::now());
            saveCacheToDisk();
        }
    });
}

} // namespace core
} // namespace launcher 