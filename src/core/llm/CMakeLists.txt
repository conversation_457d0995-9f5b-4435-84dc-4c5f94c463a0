add_library(llm STATIC
    model_registry.cpp
    provider_catalog.cpp
    capability.cpp
    provider_registration.cpp
    model_factory.cpp
    plugin_loader.cpp
    capability_heuristics.cpp
    capability_rules.cpp
    api_family_rules.cpp
    stream_runner.cpp
)

# Public include paths
# 1) This directory (src/core/llm)
# 2) Core directory for convenience so downstream targets can simply `#include "context/context.h"`
# 3) Providers directory for provider headers

target_include_directories(llm
    PUBLIC
        ${CMAKE_CURRENT_SOURCE_DIR}
        ${CMAKE_CURRENT_SOURCE_DIR}/..
        ${CMAKE_CURRENT_SOURCE_DIR}/../providers
        ${rapidjson_SOURCE_DIR}/include
)

# Link dependencies that llm implementation relies on
# http_client provides HTTP requests; context & config for prompt handling
# core_util needed for DBG macros; core_memory intentionally NOT linked here to avoid
# duplicate global new/delete definitions (benchmarks provide their own overrides).
# providers provides the AI provider implementations

target_link_libraries(llm
    PUBLIC
        http_client
        context
        config
        providers
        core_util
) 