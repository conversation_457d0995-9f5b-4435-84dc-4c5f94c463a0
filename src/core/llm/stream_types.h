#pragma once

#include <string_view>
#include <string>

namespace kai::llm {

struct Usage {
    int32_t prompt_tokens{0};
    int32_t completion_tokens{0};
    int32_t total_tokens{0};
};

struct Delta {
    std::string_view role;
    std::string_view content;
    bool             done{false};
};

struct Response {
    std::string text;
    Usage       usage;
};

struct ModelInfo {
    std::string        model_id;
    std::string_view   provider;
    std::string_view   family; // e.g. "gpt-4" or empty
};

} // namespace kai::llm 