#include "provider_registration.h"
#include "model_registry.h"

namespace launcher {
namespace core {

// Forward declarations of per-provider registration helpers implemented in their .cpp files.
bool registerOpenAIProvider(ModelRegistry& registry);
bool registerAnthropicProvider(ModelRegistry& registry);

void registerBuiltInProviders(ModelRegistry& registry) {
    registerOpenAIProvider(registry);
    registerAnthropicProvider(registry);
}

} // namespace core
} // namespace launcher 