/*
 * plugin_loader.h – minimal runtime loader for provider plug-ins (.dylib)
 */
#pragma once

#include <string>
#include <vector>
#include <memory>

namespace launcher {
namespace core {

class PluginLoader {
 public:
    // Load single .dylib, returns true on success.
    static bool load(const std::string& path);

    // Load all .dylib files from directory (non-recursive).
    static void loadDirectory(const std::string& dir_path);

 private:
    PluginLoader() = default;
};

} // namespace core
} // namespace launcher 