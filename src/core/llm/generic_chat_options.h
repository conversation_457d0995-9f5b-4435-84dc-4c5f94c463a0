#pragma once

#include <optional>

namespace launcher {
namespace core {

// Common generation parameters shared by all chat providers.
struct GenericChatOptions {
    double temperature = 0.7;
    int    max_tokens  = 2048;

    std::optional<double> frequency_penalty;
    std::optional<double> presence_penalty;

    // For informational purposes only – not sent to API.
    std::optional<int> context_window_size;
};

} // namespace core
} // namespace launcher 