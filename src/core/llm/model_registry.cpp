#include "model_registry.h"

#include "../util/debug.h"

namespace launcher {
namespace core {

bool ModelRegistry::registerProvider(const std::string& provider_id, FactoryFn factory, ListFn listFn) {
    std::lock_guard lock(mutex_);
    if (entries_.contains(provider_id)) {
        ERR("Duplicate registration for provider " << provider_id);
        return false;  // second registration ignored
    }
    entries_[provider_id] = Entry{std::move(factory), std::move(listFn)};
    DBG("Registered provider " << provider_id);
    return true;
}

std::shared_ptr<LlmModel> ModelRegistry::create(const std::string& provider_id,
                                                const std::string& model_name,
                                                const std::string& api_key) const {
    std::lock_guard lock(mutex_);
    auto it = entries_.find(provider_id);
    if (it == entries_.end()) {
        ERR("Unknown provider '" << provider_id << "'");
        return nullptr;
    }
    return it->second.factory(model_name, api_key);
}

std::vector<ModelConfig> ModelRegistry::listModels(const std::string& provider_id, const AIProviderConfig& cfg) const {
    std::lock_guard lock(mutex_);
    auto it = entries_.find(provider_id);
    if (it == entries_.end() || !it->second.listFn) {
        ERR("Unknown provider " << provider_id);
        return {};
    }
    return it->second.listFn(cfg);
}

ModelRegistry& ModelRegistry::global() {
    static ModelRegistry instance;
    return instance;
}

} // namespace core
} // namespace launcher 