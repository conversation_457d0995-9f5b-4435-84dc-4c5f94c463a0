#include "stream_runner.h"
#include "http/http_error.h"
#include "llm/model_errors.h"
#include <thread>
#include <chrono>
#include <sstream>

namespace launcher {
namespace core {

std::string StreamRunner::run(std::shared_ptr<http::HttpClient> http_client,
                              std::string_view provider_id_sv,
                              const std::string& endpoint,
                              const std::string& json_body,
                              http::DataCallback external_cb,
                              std::shared_ptr<utilities::CancellationToken> cancel_token) {
    if (!http_client) {
        throw std::runtime_error("StreamRunner: http_client is null");
    }

    std::string provider_id(provider_id_sv);

    // Ensure we request SSE.
    http_client->addDefaultHeader("Accept", "text/event-stream");

    StreamRetryPolicy policy;
    ::launcher::core::memory::SlabArena arena;

    for (int attempt = 0; attempt < static_cast<int>(policy.maxAttempts()); ++attempt) {
        if (cancel_token && cancel_token->isCancelled()) {
            throw std::runtime_error("cancelled");
        }

        arena.clear();

        auto decoder = http::SSEDecoder::create(provider_id);
        if (!decoder) {
            ERR("StreamRunner: no SSE decoder available for provider " << provider_id);
            throw std::runtime_error("Unsupported provider for SSE decoding");
        }
        // Ownership borrowed by shared_ptr so we can store in std::function.
        std::shared_ptr<launcher::core::memory::MemorySink> sinkWrapper(
            &arena, [](launcher::core::memory::MemorySink*) {});
        decoder->setSink(sinkWrapper);

        auto parsingCb = [&](std::string_view raw_chunk) {
            if (raw_chunk.empty()) return;
            auto deltaOpt = decoder->feed(raw_chunk);
            if (!deltaOpt) return;
            if (deltaOpt->done) return;
            if (!deltaOpt->content.empty()) {
                if (external_cb) external_cb(deltaOpt->content);
            }
        };

        DBG_F("StreamRunner POST endpoint={} bodyPreview={}", endpoint, std::string(json_body));

        auto result = http_client->streamingPost(endpoint, json_body, parsingCb, cancel_token);

        if (result) {
            std::string out;
            arena.copyTo(out);
            arena.clear();
            return out;
        }

        int status = result.error().status_code;
        bool should_retry = policy.shouldRetry(status, attempt);
        if (!should_retry) {
            std::string bodyMsg = result.error().message;
            ERR("StreamRunner abort: HTTP " << status << " - " << bodyMsg);
            throw *makeApiError(status, bodyMsg);
        }

        // exponential backoff.
        int backoff = static_cast<int>(policy.backoffMs(attempt));
        int slept = 0;
        const int step = 50;
        while (slept < backoff) {
            if (cancel_token && cancel_token->isCancelled()) {
                throw std::runtime_error("cancelled");
            }
            std::this_thread::sleep_for(std::chrono::milliseconds(std::min(step, backoff - slept)));
            slept += step;
        }
    }
    throw std::runtime_error("StreamRunner unreachable exit");
}

} // namespace core
} // namespace launcher 