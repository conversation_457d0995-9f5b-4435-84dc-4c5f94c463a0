#pragma once

#include "../util/expected.h"
#include "../util/stream_generator.h"
#include "../diagnostics/diagnostics_service.h"
#include "../security/mask128.hh"
#include "stream_types.h"
#include <span>
#include <string_view>
#include "../foundation/iservice.h"

namespace kai::llm {

using KaiError = launcher::core::foundation::KaiError;

template<typename T>
using ExpectedT = launcher::core::util::Expected<T, KaiError>;

template<typename T>
using AsyncStreamT = launcher::core::util::AsyncStream<T>;

using Mask128 = launcher::core::security::Mask128;

// Capability mask helper alias (still 128-bit)
using CapabilityMask = Mask128;

class IProvider : public launcher::core::foundation::IService {
 public:
    virtual std::string_view providerId() const noexcept = 0;

    // May perform network fetch; returns Expected so callers handle errors.
    virtual ExpectedT<std::span<const ModelInfo>>
    models() const noexcept = 0;

    virtual ExpectedT<Response>
    complete(std::string_view prompt, std::string_view model = {}) noexcept = 0;

    virtual ExpectedT< AsyncStreamT< ExpectedT<Delta> > >
    stream(std::string_view prompt, std::string_view model = {}) = 0;

    // Optional lifecycle (default no-op)
    virtual launcher::core::util::Result<void> init() { return launcher::core::util::Result<void>::success(); }
    virtual void shutdown() noexcept {}

    virtual CapabilityMask capabilities() const noexcept = 0;

    virtual ~IProvider() = default;
};

} // namespace kai::llm 