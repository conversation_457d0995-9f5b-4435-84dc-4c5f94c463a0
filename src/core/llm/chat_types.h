#pragma once

#include <string>
#include <vector>
#include <optional>

namespace launcher {
namespace core {

/** Lightweight representation of a single chat message. */
struct ChatMessage {
    std::string role;     // "user", "assistant", "system", "tool", ...
    std::string content;  // UTF-8 text (may be empty for tool calls)

    ChatMessage() = default;
    ChatMessage(std::string r, std::string c) : role(std::move(r)), content(std::move(c)) {}
};

/**
 * Provider-agnostic request DTO used by BaseChatModel.  It aggregates the full
 * list of messages plus common generation parameters so they can be converted
 * into provider-specific JSON payloads by adapter classes.
 */
struct ChatRequest {
    std::vector<ChatMessage> messages;

    // Generation parameters -------------------------------------------------
    double temperature = 0.7;
    int    max_tokens  = 2048;

    // Optional parameters – use std::optional to keep memory footprint low.
    std::optional<double> top_p;
    std::optional<double> frequency_penalty;
    std::optional<double> presence_penalty;
};

} // namespace core
} // namespace launcher 