#pragma once

#include <map>
#include <string>
#include <vector>
#include <mutex>
#include <time.h>

#include "model_registry.h"
#include "../config/ai_provider_config.h"
#include "../interfaces/iconfig_manager.h"
#include "../util/result.h"

namespace launcher {
namespace core {

struct ModelInfo { // thin wrapper around existing ModelConfig
    std::string provider_id;
    ModelConfig config;
};

/**
 * @brief Lightweight catalog that returns model lists per provider with simple in-memory cache.
 */
class ProviderCatalog {
 public:
    ProviderCatalog(const IConfigManager& cfg, const ModelRegistry& registry);

    /**
     * Return list of models for given provider. Fetches from remote if not cached.
     */
    std::vector<ModelConfig> list(const std::string& provider_id);

    /** Refresh all enabled providers asynchronously (returns immediately). */
    void refreshAsync();

    /** Force refresh for all providers (async not implemented). */
    void clear();

 private:
    std::vector<ModelConfig> fetch(const AIProviderConfig& provider);

    std::mutex mutex_;
    std::map<std::string, std::vector<ModelConfig>> cache_;
    std::time_t last_timestamp_ = 0;

    const IConfigManager* cfg_;
    const ModelRegistry* registry_;

    // persistence helpers
    std::string getCachePath() const;
    util::Result<void> loadCacheFromDisk();
    util::Result<void> saveCacheToDisk();
    bool isCacheFresh(std::time_t ts, int ttl_hours) const;
};

} // namespace core
} // namespace launcher 