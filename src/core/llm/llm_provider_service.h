#pragma once

#include "../foundation/iservice.h"
#include "../util/expected.h"
#include "../foundation/registry.h"
#include "chat_types.h"  // request/response types placeholder
#include <span>
#include <string_view>
#include <string>
#include <vector>

namespace launcher::core::llm {

struct LlmRequest {
    std::string_view prompt;
};

struct LlmResponse {
    std::string text;
};

struct LlmModelInfo {
    std::string_view    model_id;
    std::string_view    provider_id;
};

// ILlmProviderService – abstract interface implemented by each provider plugin.
class ILlmProviderService : public foundation::IService {
 public:
    virtual std::string_view providerId() const noexcept = 0;
    virtual std::span<const LlmModelInfo> models() const noexcept = 0;
    virtual launcher::core::util::Expected<LlmResponse, foundation::KaiError>
    complete(const LlmRequest& req) noexcept = 0;
};

} // namespace launcher::core::llm 