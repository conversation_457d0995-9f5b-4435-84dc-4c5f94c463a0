#pragma once

#include <bitset>
#include <optional>
#include <string>
#include <vector>

namespace launcher {
namespace core {

// Enumerates known model capabilities. Add new values only at the end to keep
// the stable indices used by CapabilitySet.
enum class Capability {
    TextGeneration = 0,
    JsonMode,
    ToolUse,
    Function<PERSON><PERSON>ing,
    HighReasoning,
    ImageInput,
    AudioInput,
    LargeContext,       // ≥ 128 k tokens (GPT-4.1, Claude 4, …)
    ImageGeneration,    // DALL·E-class generative models
    SpeechGeneration,   // TTS (OpenAI TTS v2, etc.)
    MultimodalRealtime, // Real-time audio+vision streaming (GPT-4o)
    ComputerUse,        // Agent takes direct computer control / RPA
    // --- add new ones above this line ---
    Count
};

constexpr size_t kCapabilityCount = static_cast<size_t>(Capability::Count);

using CapabilitySet = std::bitset<kCapabilityCount>;

// Helpers -------------------------------------------------------------
std::string capabilityToString(Capability cap);
std::optional<Capability> capabilityFromString(const std::string& name);
CapabilitySet capabilitySetFromStrings(const std::vector<std::string>& names);

} // namespace core
} // namespace launcher 