#include "capability_heuristics.h"
#include "capability_rules.h"

#include <algorithm>
#include <string>
#include <string_view>

namespace launcher::core {

static bool contains(std::string_view hay, std::string_view needle) {
    return hay.find(needle) != std::string_view::npos;
}

// Helper: convert 64-bit mask → CapabilitySet
static CapabilitySet fromMask(uint64_t mask) {
    CapabilitySet out;
    for (size_t i = 0; i < kCapabilityCount; ++i) {
        if (mask & (1ULL << i)) out.set(i);
    }
    return out;
}

CapabilitySet inferCapabilities(std::string_view provider_sv,
                                std::string_view model_sv) {
    std::string provider_lower_str;
    provider_lower_str.reserve(provider_sv.length());
    for(char c : provider_sv) { provider_lower_str += static_cast<char>(std::tolower(static_cast<unsigned char>(c))); }
    std::string_view provider(provider_lower_str);

    std::string model_lower_str;
    model_lower_str.reserve(model_sv.length());
    for(char c : model_sv) { model_lower_str += static_cast<char>(std::tolower(static_cast<unsigned char>(c))); }
    std::string_view model(model_lower_str);


    CapabilitySet caps;

    // Baseline flags ----------------------------------------------------------
    if (!contains(model, "dall-e") && !contains(model, "whisper")) {
        caps.set(static_cast<size_t>(Capability::TextGeneration));
    }
    caps.set(static_cast<size_t>(Capability::JsonMode));

    // Fast path: scan rule table ---------------------------------------------
    for (const auto& rule : getCapabilityRules()) {
        // Assuming rule.provider and rule.pattern are already lowercase std::string_view
        if (rule.provider != provider) continue;
        if (!contains(model, rule.pattern)) continue;
        caps |= fromMask(rule.mask);
    }

    return caps;
}

} // namespace launcher::core