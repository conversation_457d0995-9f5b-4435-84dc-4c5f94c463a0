#pragma once

// -----------------------------------------------------------------------------
// @file llm_provider_adapter_base.h
// @brief Header-only CRTP adapter that minimises boilerplate for provider
//        plugins.  Derive from this base and implement:
//            static constexpr char   kProviderId[];
//            static constexpr Mask128 kCaps;
//            auto                    modelsImpl() const noexcept;
//            Expected<LlmResponse, KaiError> completeImpl(const LlmRequest&) noexcept;
// -----------------------------------------------------------------------------

#include "llm_provider_service.h"
#include "llm_factory_service.h"
#include "../foundation/registry.h"
#include "../security/mask128.hh"
#include "../util/debug.h"
#include "../diagnostics/diagnostics_service.h"
#include <cstring>

namespace launcher::core::llm {

template <class Derived>
class LlmProviderAdapterBase : public ILlmProviderService {
 public:
    // ILlmProviderService API ----------------------------------------------
    [[nodiscard]] std::string_view providerId() const noexcept override {
        return Derived::kProviderId;
    }

    [[nodiscard]] std::span<const LlmModelInfo> models() const noexcept override {
        return static_cast<const Derived*>(this)->modelsImpl();
    }

    [[nodiscard]] launcher::core::util::Expected<LlmResponse, foundation::KaiError>
    complete(const LlmRequest& req) noexcept override {
        return static_cast<Derived*>(this)->completeImpl(req);
    }

    // IService API ----------------------------------------------------------
    [[nodiscard]] foundation::ServiceId id() const noexcept override {
        // Return factory service id proxy – plugins registered separately.
        return foundation::ServiceId::kLlmFactoryService;
    }

    launcher::core::util::Result<void> start() override {
        return launcher::core::util::Result<void>::success();
    }

    void stop() noexcept override {}
};

// -------------------------------------------------------------------------
// Plugin registration helper macro (header-only to avoid link-order issues)
// -------------------------------------------------------------------------
#define KAI_REGISTER_LLM_PROVIDER(DerivedType)                                   \
static std::unique_ptr<DerivedType> _kai_provider_instance;                      \
extern "C" int kai_plugin_initialize(launcher::core::foundation::ServiceRegistry* reg) { \
    _kai_provider_instance = std::make_unique<DerivedType>();                    \
    /* Register provider with factory, not ServiceRegistry */                  \
    if(auto facExp = reg->tryGet<launcher::core::llm::LlmFactoryService>(); facExp) { \
        facExp.value()->registerProvider(*_kai_provider_instance);               \
    }                                                                           \
    /* attempt diagnostics wiring */                                             \
    if(auto diagExp = reg->tryGet<launcher::core::diagnostics::DiagnosticsService>(); diagExp) { \
        _kai_provider_instance->initDiagnostics(diagExp.value());               \
    }                                                                           \
    return 0;                                                                  \
}                                                                               \
extern "C" int kai_plugin_start() { return 0; }                                \
extern "C" void kai_plugin_stop() { _kai_provider_instance.reset(); }          \
extern "C" KaiPluginInfo kai_plugin_get_info() {                               \
    KaiPluginInfo info{};                                                       \
    std::strncpy(info.id, DerivedType::kProviderId, sizeof(info.id));           \
    info.abi_major = kKaiAbiMajor;                                              \
    info.abi_minor = kKaiAbiMinor;                                              \
    std::memcpy(info.capabilities, &(DerivedType::kCaps),                       \
                sizeof(DerivedType::kCaps));                                    \
    info.runtime = KAI_RUNTIME_NULL;                                           \
    return info;                                                                \
}

} // namespace launcher::core::llm 