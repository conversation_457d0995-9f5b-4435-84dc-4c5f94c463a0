#pragma once

#include <cstdint>

namespace launcher::core {

// -----------------------------------------------------------------------------
// API families – distinct REST endpoint groups across providers.
// -----------------------------------------------------------------------------

enum class ApiFamily : uint8_t {
    kChatCompletions = 0,
    kResponses       = 1,
    kMessages        = 2,
    kUnknown         = 7
};

constexpr inline uint32_t apiBit(ApiFamily fam) {
    return 1u << static_cast<uint8_t>(fam);
}

using ApiMask = uint32_t;

} // namespace launcher::core 