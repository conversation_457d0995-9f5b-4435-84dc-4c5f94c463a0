#pragma once

#include <string_view>
#include "capability.h"

namespace launcher {
namespace core {

// Heuristically infer the capability bit-set for a given (provider, model) pair.
// This is a stop-gap until providers expose machine-readable capability metadata.
CapabilitySet inferCapabilities(std::string_view provider,
                                std::string_view model_id);

} // namespace core
} // namespace launcher 