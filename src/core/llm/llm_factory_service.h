#pragma once

#include "../foundation/service_base.h"
#include "../util/expected.h"
#include <vector>
#include <span>

namespace launcher::core::llm {

class ILlmProviderService; // fwd

class LlmFactoryService final : public foundation::ServiceBase<LlmFactoryService> {
 public:
    static constexpr foundation::ServiceId kId = foundation::ServiceId::kLlmFactoryService;

    explicit LlmFactoryService(foundation::ServiceRegistry& registry)
        : foundation::ServiceBase<LlmFactoryService>(registry) {}

    util::Result<void> start() override { return util::Result<void>::success(); }
    void             stop() noexcept override {}

    // -----------------------------------------------------------------
    // Provider management – allows unlimited adapters without touching
    // ServiceRegistry.  Called by plugin initialise macro.
    // -----------------------------------------------------------------
    void registerProvider(ILlmProviderService& provider) noexcept {
        providers_.push_back(&provider);
    }

    // Alias matching roadmap wording
    void addProvider(ILlmProviderService& p) noexcept { registerProvider(p); }

    [[nodiscard]] std::span<ILlmProviderService* const> providers() const noexcept {
        return providers_;
    }

 private:
    std::vector<ILlmProviderService*> providers_;
};

} // namespace launcher::core::llm 