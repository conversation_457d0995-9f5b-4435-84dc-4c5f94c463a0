#pragma once

#include <functional>
#include <memory>
#include <mutex>
#include <string>
#include <unordered_map>
#include <vector>
#include "../config/ai_provider_config.h"

namespace launcher {
namespace core {

class LlmModel; // forward declaration

/**
 * @brief Thread-safe registry that maps provider identifiers to factory functions.
 *
 *  – Providers self-register via the REGISTER_LLM_PROVIDER macro placed in their .cpp file.
 *  – Registration happens during static initialisation; duplicate keys are rejected.
 *  – Clients obtain concrete models through create().
 */
class ModelRegistry {
 public:
    using FactoryFn = std::function<std::shared_ptr<LlmModel>(const std::string& model_name,
                                                             const std::string& api_key)>;
    using ListFn = std::function<std::vector<ModelConfig>(const AIProviderConfig&)>;

    struct Entry {
        FactoryFn factory;
        ListFn    listFn;
    };

    /**
     * @brief Construct an empty registry. Providers must be registered by the
     *        composition-root via registerBuiltInProviders() or similar helper.
     */
    ModelRegistry() = default;

    /**
     * Register a factory for the given provider.
     * Returns true on success, false if the id already exists (second registration is ignored).
     */
    bool registerProvider(const std::string& provider_id, FactoryFn factory, ListFn listFn);

    /**
     * Create a model via registered factory. Returns nullptr on unknown provider.
     */
    std::shared_ptr<LlmModel> create(const std::string& provider_id,
                                     const std::string& model_name,
                                     const std::string& api_key = "") const;

    std::vector<ModelConfig> listModels(const std::string& provider_id, const AIProviderConfig& cfg) const;

    // Global singleton accessor for plugin registration convenience.
    static ModelRegistry& global();

 private:
    ModelRegistry(const ModelRegistry&) = delete;
    ModelRegistry& operator=(const ModelRegistry&) = delete;

    mutable std::mutex mutex_;
    std::unordered_map<std::string, Entry> entries_;
};

} // namespace core
} // namespace launcher

// -----------------------------------------------------------------------------
// Helper macro for provider self-registration.
// Usage (inside provider .cpp, outside any namespace):
//     #include "model_registry.h"
//     REGISTER_LLM_PROVIDER("openai", OpenAIModel)
// ----------------------------------------------------------------------------- 