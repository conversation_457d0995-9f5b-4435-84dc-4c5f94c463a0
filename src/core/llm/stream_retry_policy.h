#pragma once
// stream_retry_policy.h – generic back-off / retry rules used by BaseChatModel
//
// Stage-2 optimisation: centralise retry decision logic so providers can
// specialise it later without touching BaseChatModel’s core loop.

#include <cstdint>

namespace launcher::core {

struct StreamRetryPolicy {
    // Maximum number of attempts (initial + retries)
    constexpr std::uint32_t maxAttempts() const noexcept { return 5; }

    // Decide if we should retry for HTTP |status| after |attempt| (0-based)
    // This mirrors previous behaviour: retry on 429 and transient 5xx.
    constexpr bool shouldRetry(int status, std::uint32_t attempt) const noexcept {
        if (attempt + 1 >= maxAttempts()) return false;
        return status == 429 || (status >= 500 && status < 600);
    }

    // Exponential back-off delay in milliseconds
    constexpr std::uint32_t backoffMs(std::uint32_t attempt) const noexcept {
        constexpr std::uint32_t kBaseDelay = 500;          // initial 0.5 s
        return kBaseDelay * (1u << attempt);               // 0.5,1,2,4,8 s
    }
};

} // namespace launcher::core