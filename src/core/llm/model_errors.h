#pragma once

#include <stdexcept>
#include <string>
#include <memory>

namespace launcher {
namespace core {

//------------------------------------------------------------------------------
// Common error classes thrown by LLM provider adapters.
// These were previously defined in BaseModel but have been extracted into their
// own lightweight header to decouple the new composition-based architecture
// from the deprecated inheritance tree.
//------------------------------------------------------------------------------

// Thrown when a provider instance is mis-configured (e.g. missing API key).
class ConfigurationError : public std::runtime_error {
 public:
    explicit ConfigurationError(const std::string& msg)
        : std::runtime_error(msg) {}
};

// Thrown when the remote REST API returned an error or an unexpected payload.
class ApiError : public std::runtime_error {
 public:
    explicit ApiError(const std::string& msg)
        : std::runtime_error(msg) {}
};

class RateLimitError : public ApiError {
 public:
    explicit RateLimitError(const std::string& msg): ApiError(msg) {}
};

class PermissionError : public ApiError {
 public:
    explicit PermissionError(const std::string& msg): ApiError(msg) {}
};

class ServerError : public ApiError {
 public:
    explicit ServerError(const std::string& msg): ApiError(msg) {}
};

class ParsingError : public ApiError {
 public:
    explicit ParsingError(const std::string& msg): ApiError(msg) {}
};

// Map HTTP status → concrete ApiError helper.
inline std::unique_ptr<ApiError> makeApiError(int status, const std::string& body_or_msg) {
    std::string msg = "HTTP " + std::to_string(status) + ": " + body_or_msg;
    if (status == 429) {
        return std::make_unique<RateLimitError>(msg);
    }
    if (status == 401 || status == 403) {
        return std::make_unique<PermissionError>(msg);
    }
    if (status >= 500 && status < 600) {
        return std::make_unique<ServerError>(msg);
    }
    return std::make_unique<ApiError>(msg);
}

} // namespace core
} // namespace launcher 