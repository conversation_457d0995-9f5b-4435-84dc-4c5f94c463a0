#pragma once

#include "../foundation/service_base.h"
#include "../util/expected.h"

namespace launcher::core::index {

class IndexService final : public foundation::ServiceBase<IndexService> {
 public:
    static constexpr foundation::ServiceId kId = foundation::ServiceId::kIndexService;

    explicit IndexService(foundation::ServiceRegistry& registry)
        : foundation::ServiceBase<IndexService>(registry) {}

    util::Result<void> start() override { return util::Result<void>::success(); }
    void             stop() noexcept override {}
};

} // namespace launcher::core::index 