#pragma once

#include <chrono>
#include <functional>
#include <memory>
#include <string>
#include <unordered_map>
#include <vector>
#include <shared_mutex>
#include <mutex>
#include <nlohmann/json.hpp>
#include <variant>

#include "../util/file_watcher.h"
#include "../scanner/app_scanner_interface.h"
#include "../search/search_cache.h"
#include "../search/search_history.h"
#include "../interfaces/iapp_index.h"
#include "../util/result.h"

namespace launcher {
namespace core {

/**
 * @brief Represents an application entry in the index
 */
struct AppEntry {
    std::string id;                                       // Unique identifier for the application
    std::string name;                                     // Display name of the application
    std::string path;                                     // Launch path (e.g., *.app bundle on macOS or executable on other platforms)
    std::string iconPath;                                 // Path to the application icon
    std::vector<std::string> keywords;                    // Additional keywords for search
    std::chrono::system_clock::time_point last_accessed;  // Last time the app was accessed
    int launch_count;  // Number of times the app has been launched

    AppEntry() : launch_count(0) {}

    AppEntry(const std::string& id, const std::string& name, const std::string& path,
             const std::string& iconPath = "")
        : id(id), name(name), path(path), iconPath(iconPath), launch_count(0) {
        last_accessed = std::chrono::system_clock::now();
    }
};

/**
 * @brief Search result with relevance score
 */
struct SearchResult {
    std::shared_ptr<AppEntry> app;
    float score;

    // Default constructor required for std::vector
    SearchResult() : app(nullptr), score(0.0f) {}

    SearchResult(std::shared_ptr<AppEntry> app, float score) : app(app), score(score) {}

    // Allow sorting by score (descending)
    bool operator<(const SearchResult& other) const { return score > other.score; }
};

/**
 * @brief Application index that stores and searches application entries
 */
class AppIndex : public IAppIndex {
 public:
    AppIndex();
    ~AppIndex();

    /**
     * @brief Add an application to the index
     *
     * @param app The application to add
     */
    void addApp(const AppResult& app);

    /**
     * @brief Update the application index with all applications
     *
     * @param scanner The application scanner to use
     * @return true if the update was successful
     */
    util::Result<void> updateApplications(const std::shared_ptr<AppScannerInterface>& scanner);

    /**
     * @brief Enable incremental updates for the application index
     *
     * This will start watching for file system changes and update the index
     * when applications are added, removed, or modified.
     *
     * @param scanner The application scanner to use for updates
     * @return true if incremental updates were enabled successfully
     */
    util::Result<void> enableIncrementalUpdates(const std::shared_ptr<AppScannerInterface>& scanner);

    /**
     * @brief Disable incremental updates for the application index
     *
     * @return true if incremental updates were disabled successfully
     */
    util::Result<void> disableIncrementalUpdates();

    /**
     * @brief Check if incremental updates are enabled
     *
     * @return true if incremental updates are enabled
     */
    bool isIncrementalUpdatesEnabled() const;

    /**
     * @brief Search for applications by name
     *
     * @param query The search query
     * @param searchHistory Optional search history for frequency-based ranking
     * @return std::vector<AppResult> The search results
     */
    std::vector<AppResult> search(const std::string_view& query,
                                  const SearchHistory* searchHistory = nullptr) const override;

    /**
     * @brief Get all applications in the index
     *
     * @return std::vector<AppResult> All applications
     */
    std::vector<AppResult> getAllApplications() const;

    /**
     * @brief Get the number of applications in the index
     *
     * @return size_t The number of applications
     */
    size_t size() const;

    /**
     * @brief Save the index to disk
     *
     * @param filePath Path to save the index to
     */
    void saveToFile(const std::string& filePath) const;

    /**
     * @brief Load the index from disk
     *
     * @param filePath Path to load the index from
     */
    void loadFromFile(const std::string& filePath);

    /**
     * @brief Get the default path for the index file
     *
     * @return std::string The default path
     */
    static std::string getDefaultIndexPath();

    // Add or update an application in the index
    void addOrUpdateApplication(const std::shared_ptr<AppEntry>& app);

    // Remove an application from the index
    void removeApplication(const std::string& id);

    // Clear all applications from the index
    void clear();

    // Handle a file event
    void handleFileEvent(const FileEvent& event);

    /**
     * @brief Enable or disable search result caching
     *
     * @param enabled Whether caching should be enabled
     * @param maxCacheSize Maximum number of queries to cache
     * @param ttl Time-to-live for cache entries in seconds
     */
    void setCaching(bool enabled, size_t maxCacheSize = 50,
                    std::chrono::seconds ttl = std::chrono::seconds(60));

    /**
     * @brief Check if caching is enabled
     *
     * @return true if caching is enabled
     */
    bool isCachingEnabled() const;

    /**
     * @brief Clear the search cache
     */
    void clearCache();

 private:
    void updateApplication(const std::string& path);
    void removeApplicationByPath(const std::string& path);

    // Map of application ID to application entry
    std::unordered_map<std::string, std::shared_ptr<AppEntry>> apps_;

    // Calculate search score for an application given a query
    float calculateScore(const AppEntry& app, std::string_view query,
                         const SearchHistory* searchHistory = nullptr) const;

    // Fuzzy matching algorithm
    float fuzzyMatch(std::string_view str, std::string_view query) const;

    // Prefix matching algorithm
    float prefixMatch(std::string_view str,
                         std::string_view query) const;

    std::vector<AppResult> applications_;
    std::unordered_map<std::string, size_t> pathToIndexMap_;
    std::shared_ptr<FileWatcher> fileWatcher_;
    std::shared_ptr<AppScannerInterface> scanner_;
    bool incrementalUpdatesEnabled_;

    // Search result caching
    bool cachingEnabled_;
    std::unique_ptr<SearchCache> cache_;

    // Mutex for thread-safe access (shared for reads, exclusive for writes)
    mutable std::shared_mutex mutex_;

    // Global singleton access removed – create an instance and inject where needed.
};

}  // namespace core
}  // namespace launcher