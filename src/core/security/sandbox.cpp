// -----------------------------------------------------------------------------
// @file sandbox.cpp
// -----------------------------------------------------------------------------

#include "sandbox.h"

#include "../util/debug.h"

#include <fstream>
#include <sstream>
#include <string>
#include <cstdlib>

#ifdef __APPLE__
#  include <dlfcn.h>
#  if defined(__has_include)
#    if __has_include(<sandbox.h>)
#      include <sandbox.h>
#      define KAI_HAS_SANDBOX_HEADER 1
#    endif
#  endif
#endif

namespace launcher::core::security {

using launcher::core::foundation::KaiError;
using KaiV<PERSON>Expected = launcher::core::foundation::KaiExpected<void>;

// -----------------------------------------------------------------------------
// Helper: Read entire file into string
// -----------------------------------------------------------------------------
static launcher::core::util::Expected<std::string, KaiError>
readFile(const std::filesystem::path& p) {
    std::ifstream ifs(p, std::ios::in | std::ios::binary);
    if (!ifs) { return launcher::core::util::Expected<std::string, KaiError>::failure(KaiError::CapabilityDenied); }
    std::ostringstream ss;
    ss << ifs.rdbuf();
    return launcher::core::util::Expected<std::string, KaiError>::success(ss.str());
}

// -----------------------------------------------------------------------------
// Main API
// -----------------------------------------------------------------------------
KaiVoidExpected applySandboxProfile(const std::filesystem::path& profile_path) noexcept {
#ifndef __APPLE__
    // Non-Apple build: sandbox unsupported – treat as success.
    (void)profile_path;
    return KaiVoidExpected::success();
#else
    if (!std::filesystem::exists(profile_path)) {
        // Nothing to apply.
        return KaiVoidExpected::success();
    }

    auto textExp = readFile(profile_path);
    if (!textExp) {
        ERR("Failed to read sandbox profile " << profile_path);
        return KaiVoidExpected::failure(KaiError::CapabilityDenied);
    }
    const std::string& profile_txt = textExp.value();

    // Allow developers to disable sandbox in Debug via env var (NO-OP in Release builds).
#ifndef NDEBUG
    const char* sb_disable = std::getenv("KAI_SB_DISABLE");
    if (sb_disable && std::string(sb_disable) == "1") {
        DBG("KAI_SB_DISABLE=1 – skipping sandbox apply (Debug only)");
        return KaiVoidExpected::success();
    }
#endif

    void* handle = ::dlopen("/usr/lib/system/libsystem_sandbox.dylib", RTLD_LAZY);
#ifdef NDEBUG
    if (!handle) {
        ERR("Sandbox library missing – hardened runtime requires seatbelt");
        return KaiVoidExpected::failure(KaiError::CapabilityDenied);
    }
#else
    if (!handle) {
        DBG("libsystem_sandbox.dylib unavailable; skipping sandbox apply (Debug)");
        return KaiVoidExpected::success();
    }
#endif

    using SandboxInitFn = int (*)(const char*, uint64_t, const char* const*, char**);
    auto* init_fn = reinterpret_cast<SandboxInitFn>(::dlsym(handle, "sandbox_init_with_parameters"));

#ifdef NDEBUG
    if (!init_fn) {
        ERR("sandbox_init_with_parameters symbol missing in Release build – failing");
        ::dlclose(handle);
        return KaiVoidExpected::failure(KaiError::CapabilityDenied);
    }
#else
    if (!init_fn) {
        DBG("sandbox_init_with_parameters missing; skipping apply (Debug)");
        ::dlclose(handle);
        return KaiVoidExpected::success();
    }
#endif

    char* errbuf = nullptr;
    int rc = init_fn(profile_txt.c_str(), /*flags*/0, nullptr, &errbuf);
    if (rc != 0) {
        ERR("sandbox_init_with_parameters failed rc=" << rc << " : " << (errbuf ? errbuf : "<no msg>"));
        if (errbuf) { ::free(errbuf); }
        ::dlclose(handle);
        return KaiVoidExpected::failure(KaiError::CapabilityDenied);
    }

    if (errbuf) { ::free(errbuf); }
    ::dlclose(handle);
    return KaiVoidExpected::success();
#endif // __APPLE__
}

} // namespace launcher::core::security 