#import <core/security/seatbelt_verifier.hh>

#include "../util/debug.h"
#include "../util/expected.h"
#include "../foundation/registry.h"

#include <cstdlib>
#include <string>
#include <dlfcn.h>

#if defined(__APPLE__)
#  include <TargetConditionals.h>
#  include <errno.h>
#  include <unistd.h>
#endif

// Pull in the generated perfect-hash table (build dir takes precedence)
#include "seatbelt_profile.phf.h"

namespace launcher::core::security {

using launcher::core::foundation::KaiError;
using KaiVoidExpected = launcher::core::foundation::KaiVoidExpected;

// -----------------------------------------------------------------------------
// Internal helper – invoke generated perfect-hash lookup.
// -----------------------------------------------------------------------------
static inline bool lookupAllowed(std::string_view profile) noexcept {
    uint32_t h = fnv_detail::fnv1a32(profile);
    return isSeatbeltProfileAllowed(h, profile);
}

// -----------------------------------------------------------------------------
// verifyProfileName – fast path used by unit tests and runtime shim.
// -----------------------------------------------------------------------------
KaiVoidExpected verifyProfileName(std::string_view profile) noexcept {
#ifdef KAI_DISABLE_SEATBELT_VERIFIER
    (void)profile;
    return KaiVoidExpected::success();
#endif

    const char* disable_env = std::getenv("KAI_DISABLE_SEATBELT_VERIFIER");
    if (disable_env && std::string_view(disable_env) == "1") {
        DBM(@"SeatbeltVerifier bypassed via env var");
        return KaiVoidExpected::success();
    }

    if (lookupAllowed(profile)) {
        DBM(@"SeatbeltVerifier allowed profile %s", std::string(profile).c_str());
        return KaiVoidExpected::success();
    }

    ERM(@"SeatbeltVerifier rejected profile %s", std::string(profile).c_str());
    return KaiVoidExpected::failure(KaiError::SeatbeltInvalid);
}

// -----------------------------------------------------------------------------
// verifySeatbeltProfile – fetch profile string of *current* process and verify.
// -----------------------------------------------------------------------------
KaiVoidExpected verifySeatbeltProfile() noexcept {
#ifdef KAI_DISABLE_SEATBELT_VERIFIER
    return KaiVoidExpected::success();
#endif

    const char* disable_env = std::getenv("KAI_DISABLE_SEATBELT_VERIFIER");
    if (disable_env && std::string_view(disable_env) == "1") {
        DBM(@"SeatbeltVerifier bypassed via env var");
        return KaiVoidExpected::success();
    }

#if !defined(__APPLE__)
    // Non-Apple platforms – sandbox unsupported, treat as success.
    return KaiVoidExpected::success();
#else
    // ---------------------------------------------------------------------
    // Strategy hierarchy to obtain profile string:
    //   1. Honour override env var (for CI/unit-tests): KAI_TEST_EXPECTED_PROFILE
    //   2. Attempt to call private symbol sandbox_inspect_pid (10.15+)
    //   3. Fallback – unable to retrieve profile, log warning and succeed.
    // ---------------------------------------------------------------------

    if (const char* ov = std::getenv("KAI_TEST_EXPECTED_PROFILE")) {
        return verifyProfileName(std::string_view(ov));
    }

    // Load libsystem_sandbox dynamically to avoid hard link and to keep binary
    // compatible when running on systems where the symbol is absent.
    void* handle = ::dlopen("/usr/lib/system/libsystem_sandbox.dylib", RTLD_LAZY);
    if (!handle) {
        WRM(@"SeatbeltVerifier: libsystem_sandbox.dylib unavailable – skipping validation");
        return KaiVoidExpected::success();
    }

    using sandbox_inspect_pid_fn = int (*)(pid_t, char**, int*);
    auto* inspect_fn = reinterpret_cast<sandbox_inspect_pid_fn>(::dlsym(handle, "sandbox_inspect_pid"));
    if (!inspect_fn) {
        // Older macOS – cannot inspect.  Skip with warning.
        ::dlclose(handle);
        WRM(@"SeatbeltVerifier: sandbox_inspect_pid missing – skipping validation");
        return KaiVoidExpected::success();
    }

    char*  buf  = nullptr;
    int    size = 0;
    int rc = inspect_fn(getpid(), &buf, &size);
    if (rc != 0 || buf == nullptr || size == 0) {
        ::dlclose(handle);
        WRM(@"SeatbeltVerifier: sandbox_inspect_pid failed (rc=%d, errno=%d) – skipping validation", rc, errno);
        return KaiVoidExpected::success();
    }

    // The buffer starts with the profile name as a C string.  It may contain
    // additional NUL-separated data; we only care about first token.
    std::string_view profile{buf};
    auto res = verifyProfileName(profile);

    // sandbox_inspect_pid allocates with malloc(); free via libc free.
    ::free(buf);
    ::dlclose(handle);
    return res;
#endif // __APPLE__
}

} // namespace launcher::core::security 