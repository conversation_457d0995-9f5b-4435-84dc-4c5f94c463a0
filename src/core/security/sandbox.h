#pragma once

// -----------------------------------------------------------------------------
// @file sandbox.h
// @brief Lightweight wrapper around macOS seatbelt API (sandbox_init_with_parameters)
//        allowing RuntimeManagerSvc to apply a per-plugin sandbox profile.
//        On non-Apple platforms the functions are stubbed out.
// -----------------------------------------------------------------------------

#include <filesystem>
#include "../foundation/registry.h"       // KaiExpected alias & KaiError
#include "../util/expected.h"

namespace launcher::core::security {

// Apply the seatbelt profile located at `profile_path` to the *current process*.
// Returns Success when the profile is applied (or profile absent), otherwise
// returns KaiError::CapabilityDenied.
[[nodiscard]] launcher::core::foundation::KaiExpected<void>
applySandboxProfile(const std::filesystem::path& profile_path) noexcept;

} // namespace launcher::core::security 