/*
 * @file dynamic_verifier_registry.hh
 * @brief Registry for run-time loaded verifiers supplied by plugins.
 *        Enforces hard caps:
 *          • ≤ 8 verifiers in total.
 *          • ≤ 1 registration per second (sliding window).
 *          • verify() call budget ≤ 250 µs, otherwise KaiError::VerifierTimeout.
 *
 * The registry is thread-safe:
 *   • Readers (`verify`) are lock-free – they only perform atomic loads.
 *   • Writers (`registerVerifier`) are serialised by a mutex as the path is
 *     extremely cold (max 8 calls per session).
 *
 * To minimise allocations we rely on the small-buffer optimisation of
 * `std::function` for the tiny lambdas used by plugin adapters.  Should a
 * future adapter capture large state it may incur a heap allocation – in that
 * case we can switch to a custom small-fun wrapper.  For now this keeps the
 * implementation simple while still remaining allocation-free for the
 * expected workloads.
 */

#pragma once

#include <array>
#include <atomic>
#include <chrono>
#include <mutex>
#include <functional>

#include "hash_types.hh"
#include "../foundation/registry.h"   // KaiExpected / KaiError aliases

namespace launcher::core::security {

// ---------------------------------------------------------------------------
// DynVerifierAdapter – owns a callable `Verdict(const CDHash&)`.
// ---------------------------------------------------------------------------
struct DynVerifierAdapter {
    std::function<Verdict(const CDHash&)> fn;

    DynVerifierAdapter() = default;

    template <typename F>
    DynVerifierAdapter(F&& callable) : fn(std::forward<F>(callable)) {}
};

// ---------------------------------------------------------------------------
// DynamicVerifierRegistry – runtime extender for security pipeline.
// ---------------------------------------------------------------------------
class DynamicVerifierRegistry {
 public:
    using KaiVoidExpected     = launcher::core::foundation::KaiVoidExpected;
    using KaiExpectedVerdict  = launcher::core::foundation::KaiExpected<Verdict>;

    explicit DynamicVerifierRegistry(std::chrono::microseconds budget = std::chrono::microseconds{250},
                                     std::chrono::seconds window = std::chrono::seconds{1}) noexcept
        : budget_{budget}, reg_window_{window} {}
    DynamicVerifierRegistry(const DynamicVerifierRegistry&)            = delete;
    DynamicVerifierRegistry& operator=(const DynamicVerifierRegistry&) = delete;

    // Registers a new dynamic verifier. Returns TooManyVerifiers on hard-limit
    // or when called more than once per 1-second sliding window.
    KaiVoidExpected registerVerifier(DynVerifierAdapter adapter) noexcept;

    // Executes all registered verifiers in order until a conclusive verdict or
    // budget exhaustion. Returns KaiError::VerifierTimeout when the 250 µs
    // wall-clock budget is exceeded.
    KaiExpectedVerdict verify(const CDHash& cdhash) noexcept;

    [[nodiscard]] std::size_t verifierCount() const noexcept {
        return count_.load(std::memory_order_acquire);
    }

    // MetricSource tag – not yet exporting stats but makes enumeration easier.
    static constexpr bool is_metric_source = true;

 private:
    static constexpr std::size_t kMaxVerifiers = 8;

    // Per-instance limits
    const std::chrono::microseconds budget_;
    const std::chrono::seconds      reg_window_;

    std::array<DynVerifierAdapter, kMaxVerifiers> slots_{};
    std::atomic<std::size_t>                      count_{0};

    // Registration side state – mu_ guards both count_ mutation and window
    // timestamp update.
    std::mutex                                   mu_;
    std::chrono::steady_clock::time_point        last_reg_{};
};

} // namespace launcher::core::security 