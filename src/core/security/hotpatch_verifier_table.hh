#pragma once

/*
 * @file hotpatch_verifier_table.hh
 * @brief Signed FlatSnapshot-based override table for security verdicts.
 *        When KAI_ENABLE_VPATCH==1 the table is mmapped read-only, verified
 *        via SecStaticCodeCheckValidity (codesign) and FlatSnapshot::verify().
 *        When the flag is OFF this header provides a lightweight stub so that
 *        dependent code can compile without extra #ifdef noise.
 */

#include <filesystem>
#include <cstddef>
#include <cstdint>
#include <atomic>

#include "../util/expected.h"
#include "../storage/flat_snapshot.hh"
#include "../util/debug.h"
#include "hash_types.hh"
#include "codesign_verify.h"

namespace launcher::core::security {

// ---------------------------------------------------------------------------
// Error domain returned by Hot-Patch table loader
// ---------------------------------------------------------------------------
enum class VPatchError {
    FileNotFound,
    IoError,
    InvalidSnapshot,
    InvalidSignature,
};

#if KAI_ENABLE_VPATCH

// Forward declaration implemented in .cpp
class HotpatchVerifierTable {
 public:
    using KaiExpectedTable = launcher::core::util::Expected<HotpatchVerifierTable, VPatchError>;

    // Loads, verifies and memory-maps |snapshot|. On success returns fully
    // initialised table instance (move-only).
    static KaiExpectedTable load(const std::filesystem::path& snapshot) noexcept;

    // Returns pointer to Verdict for |cdhash| if present, else nullptr.
    [[nodiscard]] const Verdict* find(const CDHash& cdhash) const noexcept;

    [[nodiscard]] std::size_t entryCount() const noexcept { return entry_cnt_; }

    // Simple metrics – atomics are cheap and contention-free for read-mostly
    // workloads.
    struct Stats { uint32_t hit; uint32_t miss; };
    [[nodiscard]] Stats stats() const noexcept { return {hit_.load(), miss_.load()}; }

    static constexpr bool is_metric_source = true;

    // Move-only, non-copyable (owns mmap)
    HotpatchVerifierTable(HotpatchVerifierTable&&) noexcept = default;
    HotpatchVerifierTable& operator=(HotpatchVerifierTable&&) noexcept = default;
    HotpatchVerifierTable(const HotpatchVerifierTable&)            = delete;
    HotpatchVerifierTable& operator=(const HotpatchVerifierTable&) = delete;

 private:
    // Internal binary payload layout – packed to 24 bytes.
    struct __attribute__((packed)) Entry {
        CDHash  cdhash;           // 20 B
        uint8_t verdict_code;     // maps to Verdict::Code
        uint8_t reserved[3]{};    // padding to 24 B total
    };

    HotpatchVerifierTable(storage::FlatSnapshot&& snap,
                          const Entry* entries,
                          std::size_t cnt) noexcept;

    // mmapped snapshot (owns fd + mapping lifetime)
    storage::FlatSnapshot snap_;
    const Entry*          entries_{nullptr};
    std::size_t           entry_cnt_{0};

    // cheap counters
    std::atomic<uint32_t> hit_{0};
    std::atomic<uint32_t> miss_{0};
};

#else // KAI_ENABLE_VPATCH == 0

// ----------------------------------------------------------------------------
// Stub implementation – compiled out when feature flag is OFF.
// ----------------------------------------------------------------------------
class HotpatchVerifierTable {
 public:
    using KaiExpectedTable = launcher::core::util::Expected<HotpatchVerifierTable, VPatchError>;
    static KaiExpectedTable load(const std::filesystem::path&) noexcept {
        return KaiExpectedTable::success(HotpatchVerifierTable{});
    }
    const Verdict* find(const CDHash&) const noexcept { return nullptr; }
    std::size_t entryCount() const noexcept { return 0; }
    struct Stats { uint32_t hit{0}; uint32_t miss{0}; };
    Stats stats() const noexcept { return {}; }
    static constexpr bool is_metric_source = true;
};

#endif // KAI_ENABLE_VPATCH

} // namespace launcher::core::security 