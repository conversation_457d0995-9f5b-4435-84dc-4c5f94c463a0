#pragma once

/*
 * @file hotpatch_table_service.h
 * @brief Skeleton of HotpatchTableSvc – service wrapper around
 *        HotpatchVerifierTable that exposes metrics and provides
 *        ServiceBase wiring for dependency injection.
 *
 * NOTE: This header is intentionally **not** compiled anywhere yet because
 *       the auto-generated enum ServiceId has no entry for this service.
 *       Once the ServiceId generator is updated, the following static_assert
 *       should be revised/removed and the implementation (.cpp) added.
 */

#include "../foundation/service_base.h"
#include "core/config/config_service.h"
#include "../diagnostics/diagnostics_service.h"
#include "verification_store.hh"
#include "hotpatch_verifier_table.hh"

namespace launcher::core::security {

class HotpatchTableSvc final : public foundation::ServiceBase<HotpatchTableSvc,
                                                         launcher::core::config::ConfigService,
                                                         launcher::core::diagnostics::DiagnosticsService> {
 public:
    static constexpr foundation::ServiceId kId = foundation::ServiceId::kHotpatchTableSvc;

    HotpatchTableSvc(foundation::ServiceRegistry& reg,
                     VerificationStore&           store)
        : ServiceBase<HotpatchTableSvc,
                      launcher::core::config::ConfigService,
                      launcher::core::diagnostics::DiagnosticsService>(reg),
          vstore_(store) {
        cfg_  = &this->template get<launcher::core::config::ConfigService>();
        diag_ = &this->template get<launcher::core::diagnostics::DiagnosticsService>();
    }

    // IService hooks – currently no-op skeleton
    ::launcher::core::util::Result<void> start() override;
    void                             stop() noexcept override;

 private:
    VerificationStore&                                   vstore_;
    launcher::core::config::ConfigService*               cfg_{nullptr};
    launcher::core::diagnostics::DiagnosticsService*     diag_{nullptr};

#if KAI_ENABLE_VPATCH
    HotpatchVerifierTable                                table_;
    bool                                                 loaded_{false};
#endif
};

} // namespace launcher::core::security 