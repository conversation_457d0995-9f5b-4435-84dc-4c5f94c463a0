// -----------------------------------------------------------------------------
// @file verification_store.hh
// @brief Header-only cache for plugin verification results.
//        Wraps AdaptiveCache<CDHash,Verdict> to provide thread-safe lookup,
//        insertion, and stats with zero allocations after warm-up.
//        Part of Task-3 Slice-2.
// -----------------------------------------------------------------------------

#pragma once

#include <filesystem>

#include "../container/adaptive_cache.hh"
#include "hash_types.hh"
#include "../util/expected.h"
#include "snapshot/verdict_store.hh"
#if KAI_ENABLE_VPATCH
#include "hotpatch_verifier_table.hh"
#endif

// Forward declaration to avoid heavy snapshot header include.
namespace launcher::snapshot { class VerdictStore; }

namespace launcher::core::security {

// -------------------------------------------------------------------------
// Error domain for VerificationStore operations.
// -------------------------------------------------------------------------
enum class StoreError { kNotFound };

/**
 * @class VerificationStore
 * @brief Thread-safe, zero-allocation (after warm-up) cache for code-signature
 *        verification results. Internally wraps AdaptiveCache<CDHash,Verdict>.
 *
 * The instance is intended to be *injected* into services (e.g.
 * RuntimeManagerSvc) rather than accessed through hidden globals. This keeps
 * test seams obvious and removes implicit ordering constraints.
 */
class VerificationStore {
 public:
    using Stats = launcher::core::container::AdaptiveCache<CDHash, Verdict,
                                                         launcher::core::container::NullPolicy,
                                                         64, 4>::Stats;

    explicit VerificationStore(std::size_t sets_pow2 = 4096)
        : cache_{sets_pow2} {}

    // Disable copying – a single cache instance should be shared.
    VerificationStore(const VerificationStore&)            = delete;
    VerificationStore& operator=(const VerificationStore&) = delete;

    // ---------------------------------------------------------------------
    // API – mirrors previous free-function contract but as member methods.
    // ---------------------------------------------------------------------
    [[nodiscard]] launcher::core::util::Expected<Verdict, StoreError>
    find(const CDHash& cdhash) noexcept {
#if KAI_ENABLE_VPATCH
        if (vpatch_) {
            if (const Verdict* patched = vpatch_->find(cdhash)) {
                return launcher::core::util::Expected<Verdict, StoreError>::success(*patched);
            }
        }
#endif
        const Verdict unknown{};  // sentinel kUnknown value
        auto& v = cache_.getOrInsert(cdhash, [&] { return unknown; });
        if (v.code == Verdict::Code::kUnknown) {
            return launcher::core::util::Expected<Verdict, StoreError>::failure(StoreError::kNotFound);
        }
        return launcher::core::util::Expected<Verdict, StoreError>::success(v);
    }

    [[nodiscard]] launcher::core::util::Expected<void, StoreError>
    insert(const CDHash& cdhash, Verdict verdict) noexcept {
        auto& v = cache_.getOrInsert(cdhash, [&] { return verdict; });
        v = verdict;  // update existing or set new
        // Persist to L2 snapshot store when attached. This guarantees that
        // cold-start #2 benefits from a memory-mapped VerdictStore hit and
        // contributes towards ProbeCache ≥95 % warm-start hit-rate (AC-3).
        if (verdict_store_) {
            verdict_store_->persist(cdhash, verdict);
        }
        return launcher::core::util::Expected<void, StoreError>::success();
    }

    [[nodiscard]] Stats stats() const noexcept { return cache_.stats(); }

    // MetricSource tag
    static constexpr bool is_metric_source = true;

#if KAI_ENABLE_VPATCH
    void attachHotpatchTable(const HotpatchVerifierTable& table) noexcept {
        vpatch_ = &table;
    }
#endif

    // -----------------------------------------------------------------
    // Optional L2 VerdictStore attachment helper.
    // The caller (Service bootstrap / main.cpp) owns the store lifetime and
    // guarantees it outlives this VerificationStore instance.
    // -----------------------------------------------------------------
    void attachVerdictStore(::launcher::snapshot::VerdictStore& store) noexcept {
        verdict_store_ = &store;
    }

 private:
    launcher::core::container::AdaptiveCache<CDHash, Verdict,
                                             launcher::core::container::NullPolicy,
                                             64, 4>
        cache_;
#if KAI_ENABLE_VPATCH
    const HotpatchVerifierTable* vpatch_{nullptr};
#endif
    ::launcher::snapshot::VerdictStore* verdict_store_{nullptr};
};

// -----------------------------------------------------------------------------
// Helper: compute a deterministic 20-byte hash for |path| on non-Apple builds.
// In real production we would extract the true CDHash via Security.framework.
// -----------------------------------------------------------------------------
inline CDHash computeCdHash(const std::filesystem::path& path) noexcept {
    CDHash h{};
    const std::string str = path.string();
    constexpr uint64_t fnv_offset = 0xcbf29ce484222325ULL;
    constexpr uint64_t fnv_prime  = 0x100000001b3ULL;
    uint64_t acc = fnv_offset;
    for (unsigned char c : str) {
        acc ^= static_cast<uint64_t>(c);
        acc *= fnv_prime;
    }
    // Fill bytes by repeating acc (little-endian) until 20 bytes populated.
    for (int i = 0; i < 20; ++i) {
        h.bytes[i] = static_cast<uint8_t>((acc >> ((i % 8) * 8)) & 0xFF);
    }
    return h;
}

} // namespace launcher::core::security 