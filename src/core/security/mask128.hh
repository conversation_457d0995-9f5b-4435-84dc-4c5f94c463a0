// -----------------------------------------------------------------------------
// @file mask128.hh
// @brief 128-bit capability mask utilities (Slice-2 security substrate)
//        Header-only, zero-allocation, constexpr-enabled helper struct.
//        Split into 64-bit hi/lo fields to maintain compiler portability.
// -----------------------------------------------------------------------------

#pragma once

#include <bit>          // std::popcount, std::rotl, etc.
#include <cstdint>      // uint64_t
#include <string>       // std::string
#include <type_traits>  // std::is_enum_v, std::underlying_type_t
#include <cstddef>      // size_t
#include <cassert>
#include <cctype>

// -----------------------------------------------------------------------------
// Fixed-size string implementation: constexpr, zero-allocation, POD-like.
// Provides minimal interface (data(), size(), operator[]). Placed here so
// Mask128 can reference it regardless of namespace.
// -----------------------------------------------------------------------------

template <std::size_t N>
struct StaticString {
    char buf[N]{};

    [[nodiscard]] constexpr const char *data() const noexcept { return buf; }
    [[nodiscard]] constexpr std::size_t size() const noexcept { return N; }

    [[nodiscard]] constexpr const char &operator[](std::size_t idx) const noexcept {
        return buf[idx];
    }

    // Non-constexpr helper that converts to std::string (may allocate); handy
    // for logging paths that need ownership beyond constexpr context.
    [[nodiscard]] std::string to_std_string() const { return std::string(buf, N); }
};

static_assert(std::is_trivially_copyable_v<StaticString<32>>, "StaticString must be trivial");

namespace launcher::core::security {

inline constexpr uint16_t kCapabilityBits = 128;

// 128-bit bitset represented as two 64-bit unsigned integers.
struct Mask128 {
    uint64_t lo{0};  // bits   0‒63 (LSB)
    uint64_t hi{0};  // bits  64‒127

    // ---------------------------------------------------------------------
    // constexpr queries
    // ---------------------------------------------------------------------

    // Return true if |bit| is set (0-based index).
    [[nodiscard]] constexpr bool has(uint8_t bit) const noexcept {
        assert(bit < 128);
        return bit < 64 ? (lo & (uint64_t{1} << bit)) != 0
                         : (hi & (uint64_t{1} << (bit - 64))) != 0;
    }

    // Count number of set bits.
    [[nodiscard]] constexpr uint32_t popcount() const noexcept {
#if defined(__cpp_lib_bitops)
        return static_cast<uint32_t>(std::popcount(lo) + std::popcount(hi));
#else
        return static_cast<uint32_t>(__builtin_popcountll(lo) + __builtin_popcountll(hi));
#endif
    }

    // ---------------------------------------------------------------------
    // mutators
    // ---------------------------------------------------------------------

    // Set bit to 1.
    constexpr void set(uint8_t bit) noexcept {
        assert(bit < 128);
        if (bit < 64)
            lo |= (uint64_t{1} << bit);
        else
            hi |= (uint64_t{1} << (bit - 64));
    }

    // Clear bit to 0.
    constexpr void clear(uint8_t bit) noexcept {
        assert(bit < 128);
        if (bit < 64)
            lo &= ~(uint64_t{1} << bit);
        else
            hi &= ~(uint64_t{1} << (bit - 64));
    }

    // ---------------------------------------------------------------------
    // Enum adapters (works for any enum with underlying integral type)
    // ---------------------------------------------------------------------

    template <typename Enum>
        requires std::is_enum_v<Enum>
    [[nodiscard]] constexpr bool has(Enum cap) const noexcept {
        using U = std::underlying_type_t<Enum>;
        static_assert(sizeof(U) <= sizeof(uint8_t),
                      "Capability enum underlying type must be ≤ uint8_t (0-127)");
        return has(static_cast<uint8_t>(cap));
    }

    template <typename Enum>
        requires std::is_enum_v<Enum>
    constexpr void set(Enum cap) noexcept {
        set(static_cast<uint8_t>(cap));
    }

    template <typename Enum>
        requires std::is_enum_v<Enum>
    constexpr void clear(Enum cap) noexcept {
        clear(static_cast<uint8_t>(cap));
    }

    // ---------------------------------------------------------------------
    // String / hex helpers
    // ---------------------------------------------------------------------

    // Return a 32-char lowercase hexadecimal representation (hi first).
    // constexpr & zero-allocation: returns StaticString<32> by value.
    [[nodiscard]] constexpr StaticString<32> to_string() const noexcept {
        StaticString<32> out{};

        auto nibble_to_hex = [](uint8_t v) constexpr -> char {
            return static_cast<char>(v < 10 ? '0' + v : 'a' + (v - 10));
        };

        auto write_word = [&](uint64_t word, std::size_t ofs) constexpr {
            for (int i = 15; i >= 0; --i) {
                uint8_t nibble = static_cast<uint8_t>(word & 0xF);
                out.buf[ofs + i] = nibble_to_hex(nibble);
                word >>= 4U;
            }
        };

        write_word(hi, 0);   // hi part first
        write_word(lo, 16);  // then low part
        return out;
    }

    // Convenience (runtime) helper for logging/UI — allocates.
    [[nodiscard]] std::string toStdString() const {
        return to_string().to_std_string();
    }

    // Deprecated alias kept for graceful migration; will be removed.
    [[deprecated("Use toStdString() instead for clarity.")]]
    [[nodiscard]] std::string to_string_alloc() const { return toStdString(); }
};

// Compile-time invariants
static_assert(sizeof(Mask128) == 16, "Mask128 must be 16 bytes");
static_assert(std::is_trivially_copyable_v<Mask128>, "Mask128 must be trivially copyable");

// Sanity static-assert: all-ones popcount == 128
static_assert(Mask128{~0ULL, ~0ULL}.popcount() == 128, "Mask128 popcount incorrect");

// Compile-time sanity: default mask -> hex string length is 32.
static_assert(Mask128{}.to_string().size() == 32, "Mask128 to_string length incorrect");

} // namespace launcher::core::security

// -------------------------------------------------------------------------
// kai::Capability free-function adapters (kept outside Mask128 to avoid
// cross-layer dependencies).
// -------------------------------------------------------------------------
#ifdef __has_include
#  if __has_include("core/foundation/capability256.h")
#    include "core/foundation/capability256.h"
#  endif
#endif

#ifdef KAI_CAPABILITY_ADAPTER_ENABLED // allow unit tests to toggle include absence
#endif

#ifdef __has_include
#  if __has_include("core/foundation/capability256.h")
namespace kai {

using CapabilityMask = launcher::core::security::Mask128;

inline constexpr void set(CapabilityMask& m, Capability cap) noexcept {
    m.set(static_cast<uint8_t>(cap));
}

inline constexpr bool has(const CapabilityMask& m, Capability cap) noexcept {
    return m.has(static_cast<uint8_t>(cap));
}

inline constexpr void clear(CapabilityMask& m, Capability cap) noexcept {
    m.clear(static_cast<uint8_t>(cap));
}

static_assert(sizeof(Capability) == 1, "Capability enum must be backed by uint8_t");

} // namespace kai
#  endif
#endif 