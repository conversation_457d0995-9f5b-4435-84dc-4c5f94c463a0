#pragma once

// -----------------------------------------------------------------------------
// @file seatbelt_verifier.hh
// @brief Verifies that the running process is constrained by an approved
//        App Sandbox ("Seatbelt") profile.  Zero-allocation, header-only fast
//        path – the heavy lifting lives in Objective-C++ shim.
// -----------------------------------------------------------------------------

#include <string_view>
#include <cstdint>

#include "../foundation/registry.h"

namespace launcher::core::security {

// -----------------------------------------------------------------------------
// Internal – constexpr FNV-1a 32-bit hash (little-endian, offset basis 0x811C9DC5)
// -----------------------------------------------------------------------------
namespace fnv_detail {
constexpr uint32_t fnv1a32(std::string_view str) noexcept {
    uint32_t h = 0x811C9DC5u;
    for (unsigned char c : str) {
        h ^= c;
        h *= 0x01000193u; // prime 16777619
    }
    return h;
}
} // namespace fnv_detail

// Forward declaration – implemented in generated perfect-hash table header.
[[nodiscard]] bool isSeatbeltProfileAllowed(uint32_t hash,
                                            std::string_view profile) noexcept;

// -----------------------------------------------------------------------------
// Public API
// -----------------------------------------------------------------------------
// Verifies *current* process sandbox profile (uses ObjC++ shim on macOS).
[[nodiscard]] launcher::core::foundation::KaiVoidExpected verifySeatbeltProfile() noexcept;

// Verifies an *explicit* profile string – used by unit tests and as the
// internal implementation once the runtime profile string is retrieved.
[[nodiscard]] launcher::core::foundation::KaiVoidExpected verifyProfileName(std::string_view profile) noexcept;

} // namespace launcher::core::security 