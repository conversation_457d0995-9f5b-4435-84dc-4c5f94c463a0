#include "hotpatch_verifier_table.hh"

#if KA<PERSON>_ENABLE_VPATCH

#include <cstring>  // std::memcmp, std::memcpy
#include <algorithm>

namespace launcher::core::security {

using launcher::core::storage::FlatSnapshot;
using launcher::core::util::Expected;

// -------------------------------- Entry helpers ----------------------------
namespace {
inline int compareCdHash(const CDHash& a, const CDHash& b) noexcept {
    return std::memcmp(a.bytes, b.bytes, sizeof(a.bytes));
}

constexpr Verdict kAllowed{Verdict::Code::kAllowed};
constexpr Verdict kDenied {Verdict::Code::kDenied};
constexpr Verdict kUnknown{Verdict::Code::kUnknown};
}

// --------------------------- ctor (private) ---------------------------------
HotpatchVerifierTable::HotpatchVerifierTable(FlatSnapshot&& snap,
                                             const Entry* entries,
                                             std::size_t cnt) noexcept
    : snap_(std::move(snap)), entries_(entries), entry_cnt_(cnt) {}

// ------------------------------- load() -------------------------------------
HotpatchVerifierTable::KaiExpectedTable
HotpatchVerifierTable::load(const std::filesystem::path& path) noexcept {
    // 1. Codesign signature (static code) – mandatory.
    {
        auto sig_res = verifyCodeSignature(path);
        if (!sig_res) {
            return KaiExpectedTable::failure(VPatchError::InvalidSignature);
        }
    }

    // 2. mmap FlatSnapshot.
    auto snap_res = FlatSnapshot::mmapReadOnly(path);
    if (!snap_res) {
        VPatchError err = (snap_res.error() == launcher::core::storage::FlatSnapshotError::FileNotFound) ?
                              VPatchError::FileNotFound : VPatchError::IoError;
        return KaiExpectedTable::failure(err);
    }
    FlatSnapshot snap = std::move(snap_res.value());

    // 3. Header/CRC/CMS verification.
    if (!snap.verify()) {
        return KaiExpectedTable::failure(VPatchError::InvalidSnapshot);
    }

    auto payload = snap.payload();
    if (payload.size() < sizeof(uint32_t)) {
        return KaiExpectedTable::failure(VPatchError::InvalidSnapshot);
    }

    // Safe decode entry count (little-endian host assumed by FlatSnapshot).
    uint32_t count = 0;
    std::memcpy(&count, payload.data(), sizeof(uint32_t));

    constexpr size_t kEntrySize = sizeof(Entry);
    const size_t expected_size  = static_cast<size_t>(count) * kEntrySize + sizeof(uint32_t);
    if (expected_size > payload.size()) {
        return KaiExpectedTable::failure(VPatchError::InvalidSnapshot);
    }

    const auto* entries = reinterpret_cast<const Entry*>(payload.data() + sizeof(uint32_t));

    // Construct final table (move snapshot into instance)
    HotpatchVerifierTable table{std::move(snap), entries, count};
    return KaiExpectedTable::success(std::move(table));
}

// -------------------------------- find() ------------------------------------
const Verdict* HotpatchVerifierTable::find(const CDHash& cdhash) const noexcept {
    size_t left = 0;
    size_t right = entry_cnt_;
    while (left < right) {
        size_t mid = left + ((right - left) >> 1);
        const Entry& ent = entries_[mid];
        int cmp = compareCdHash(cdhash, ent.cdhash);
        if (cmp == 0) {
            // Update counters
            hit_.fetch_add(1, std::memory_order_relaxed);
            switch (ent.verdict_code) {
                case static_cast<uint8_t>(Verdict::Code::kAllowed):
                    return &kAllowed;
                case static_cast<uint8_t>(Verdict::Code::kDenied):
                    return &kDenied;
                default:
                    return &kUnknown;
            }
        }
        if (cmp < 0) {
            right = mid;
        } else {
            left = mid + 1;
        }
    }
    miss_.fetch_add(1, std::memory_order_relaxed);
    return nullptr;
}

} // namespace launcher::core::security

#endif // KAI_ENABLE_VPATCH 