#pragma once

#include <filesystem>
#include "../util/expected.h"

namespace launcher::core::security {

enum class CodesignError {
    FileNotFound,
    AppleApiError,
    InvalidSignature,
};

using KaiVoidExpected = launcher::core::util::Expected<void, CodesignError>;

// Wrapper around SecStaticCodeCheckValidity. Returns success() when the code
// signature is valid or verification is compiled out via KAI_DISABLE_CODESIGN_VERIFY.
KaiVoidExpected verifyCodeSignature(const std::filesystem::path& path) noexcept;

} // namespace launcher::core::security 