// -----------------------------------------------------------------------------
// @file hash_types.hh
// @brief Common small POD hash/key/value structs used across security subsystem.
//        These were initially declared in implementation files; we promote them
//        to a shared header for reuse by VerificationStore and others. All
//        types are trivially copyable to satisfy AdaptiveCache constraints.
// -----------------------------------------------------------------------------

#pragma once

#include <cstdint>   // uint8_t, uint64_t
#include <cstddef>   // size_t
#include <cstring>   // std::memcpy
#include <functional> // std::hash
#include <string_view>

namespace launcher::core::security {

// -----------------------------------------------------------------------------
// CDHash – 20-byte Code Directory SHA-1 digest used by Apple code signing.
// -----------------------------------------------------------------------------
struct CDHash {
    uint8_t bytes[20]{};
    bool operator==(const CDHash&) const noexcept = default;
};

// -----------------------------------------------------------------------------
// Verdict – result of code-signature & runtime verification pipelines.
// -----------------------------------------------------------------------------
struct Verdict {
    enum class Code : uint8_t { kUnknown, kAllowed, kDenied };
    Code code{Code::kUnknown};
};

// -----------------------------------------------------------------------------
// PathHash – auxiliary 64-bit path hash for internal caches (Task-2).
// -----------------------------------------------------------------------------
struct PathHash {
    uint64_t value{0};
    bool operator==(const PathHash&) const noexcept = default;
};

// ProbeResult – placeholder for future file probe outcome (Task-2).
struct ProbeResult {
    bool valid{false};
};

} // namespace launcher::core::security

// -----------------------------------------------------------------------------
// Standard library hash specialisations
// -----------------------------------------------------------------------------
namespace std {

template<>
struct hash<launcher::core::security::CDHash> {
    size_t operator()(const launcher::core::security::CDHash& h) const noexcept {
        // 64-bit FNV-1a over 20 bytes (cheap, good dispersion).
        constexpr uint64_t fnv_offset = 0xcbf29ce484222325ULL;
        constexpr uint64_t fnv_prime  = 0x100000001b3ULL;
        uint64_t acc = fnv_offset;
        for (uint8_t b : h.bytes) {
            acc ^= b;
            acc *= fnv_prime;
        }
        return static_cast<size_t>(acc);
    }
};

template<>
struct hash<launcher::core::security::PathHash> {
    size_t operator()(const launcher::core::security::PathHash& h) const noexcept {
        return static_cast<size_t>(h.value);
    }
};

} // namespace std 