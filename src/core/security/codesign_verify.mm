#include "codesign_verify.h"
#include "../util/debug.h"

#if defined(__APPLE__)
#import <Foundation/Foundation.h>
#import <Security/Security.h>
#endif

namespace launcher::core::security {

// Helper to map Security.framework status codes to enum
static CodesignError mapStatus(OSStatus status) noexcept {
    switch (status) {
        case errSecSuccess:
            return CodesignError::InvalidSignature; // Should never be consulted when success
        case errSecCSSignatureFailed:
        case errSecCSUnsigned:
        case errSecCSReqFailed:
        case errSecCSBadResource:
#if defined(errSecCSBadResourceSignature) && (errSecCSBadResourceSignature != errSecCSBadResource)
        case errSecCSBadResourceSignature:
#endif
#if defined(errSecCSBadResourceSize) && (errSecCSBadResourceSize != errSecCSBadResource)
        case errSecCSBadResourceSize:
#endif
#if defined(errSecCSBadNestedCode)
        case errSecCSBadNestedCode:
#endif
        case errSecCSBadMainExecutable:
            return CodesignError::InvalidSignature;
        default:
            return CodesignError::AppleApiError;
    }
}

KaiVoidExpected verifyCodeSignature(const std::filesystem::path& path) noexcept {
#ifdef KAI_DISABLE_CODESIGN_VERIFY
    (void)path;
    return KaiVoidExpected::success();
#endif

#if !defined(__APPLE__)
    (void)path;
    return KaiVoidExpected::success();
#else
    if (!std::filesystem::exists(path)) {
        ERR("codesign_verify: file not found " << path);
        return KaiVoidExpected::failure(CodesignError::FileNotFound);
    }

    @autoreleasepool {
        // Convert std::filesystem::path to CFURL in a NUL-safe way (handles non-UTF-8)
        std::string path_str = path.string();
        CFURLRef url = CFURLCreateFromFileSystemRepresentation(nullptr,
                                                              reinterpret_cast<const UInt8*>(path_str.data()),
                                                              path_str.length(),
                                                              /*isDirectory*/ false);
        if (!url) {
            ERR("codesign_verify: CFURLCreateFromFileSystemRepresentation failed for " << path);
            return KaiVoidExpected::failure(CodesignError::AppleApiError);
        }

        SecStaticCodeRef static_code = nullptr;
        OSStatus status = SecStaticCodeCreateWithPath(url, kSecCSDefaultFlags, &static_code);
        CFRelease(url);

        if (status != errSecSuccess || !static_code) {
            ERR("codesign_verify: SecStaticCodeCreateWithPath failed status=" << status);
            return KaiVoidExpected::failure(CodesignError::AppleApiError);
        }

        status = SecStaticCodeCheckValidity(static_code, kSecCSStrictValidate, nullptr);
        CFRelease(static_code);

        if (status == errSecSuccess) {
            DBG("codesign_verify: valid signature for " << path);
            return KaiVoidExpected::success();
        }

        auto mapped = mapStatus(status);
        if (mapped == CodesignError::InvalidSignature) {
            DBG("codesign_verify: invalid signature status=" << status << " for " << path);
        } else {
            ERR("codesign_verify: Apple API error status=" << status << " for " << path);
        }
        return KaiVoidExpected::failure(mapped);
    }
#endif
}

} // namespace launcher::core::security 