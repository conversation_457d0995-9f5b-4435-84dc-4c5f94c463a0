#pragma once

// -----------------------------------------------------------------------------
// @file seatbelt_profile.phf.h  (AUTOGENERATED – DO NOT EDIT MANUALLY)
// -----------------------------------------------------------------------------
// This header is generated at build-time by tools/seatbelt_delta_gen.py.  The
// checked-in stub exists only so that IDEs and non-configured builds compile.
// The generator will overwrite it in the build tree with the authoritative
// table derived from resources/seatbelt_profiles.txt.
// -----------------------------------------------------------------------------

#include <string_view>
#include <cstdint>

namespace launcher::core::security {
namespace generated {
struct Entry { uint32_t hash; const char* str; };

// A minimal allow-list so that unit tests exercising *rejection* still work.
static constexpr Entry kEntries[] = {
    {0x6ae6e52dU, "platform_app"}, // FNV-1a32("platform_app")
    {0x2f4f3ad0U, "unrestricted"},  // FNV-1a32("unrestricted")
};
static constexpr std::size_t kTableSize = sizeof(kEntries)/sizeof(kEntries[0]);
} // namespace generated

// -----------------------------------------------------------------------------
// Linear lookup fallback (O(n) – not on hot path).  The build-time generator
// will emit a branch-free O(1) function which shadows this weak symbol.
// -----------------------------------------------------------------------------
[[maybe_unused]] inline bool isSeatbeltProfileAllowed(uint32_t hash,
                                                     std::string_view profile) noexcept {
    for (const auto& e : generated::kEntries) {
        if ((e.hash == hash) || profile == e.str) {
            return true;
        }
    }
    return false;
}

} // namespace launcher::core::security
