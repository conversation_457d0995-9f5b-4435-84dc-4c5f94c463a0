#include "dynamic_verifier_registry.hh"
#include "../util/debug.h"

namespace launcher::core::security {

using launcher::core::foundation::KaiError;

DynamicVerifierRegistry::KaiVoidExpected
DynamicVerifierRegistry::registerVerifier(DynVerifierAdapter adapter) noexcept {
    using Clock = std::chrono::steady_clock;
    const auto now = Clock::now();

    std::lock_guard<std::mutex> lock(mu_);

    // Enforce registration frequency window.
    if (reg_window_ > std::chrono::seconds::zero() && last_reg_.time_since_epoch().count() != 0 && (now - last_reg_) < reg_window_) {
        ERR("DynamicVerifierRegistry: registration too frequent");
        return KaiVoidExpected::failure(KaiError::TooManyVerifiers);
    }

    const std::size_t idx = count_.load(std::memory_order_relaxed);
    if (idx >= kMaxVerifiers) {
        ERR("DynamicVerifierRegistry: verifier capacity reached");
        return KaiVoidExpected::failure(KaiError::TooManyVerifiers);
    }

    slots_[idx] = std::move(adapter);
    count_.store(idx + 1, std::memory_order_release);
    last_reg_ = now;

    DBG("DynamicVerifierRegistry: registered verifier idx=" << idx);
    return KaiVoidExpected::success();
}

DynamicVerifierRegistry::KaiExpectedVerdict
DynamicVerifierRegistry::verify(const CDHash& cdhash) noexcept {
    using Clock = std::chrono::steady_clock;
    const auto t_start = Clock::now();
    const auto deadline = t_start + budget_;

    Verdict result; // kUnknown by default

    const std::size_t n = count_.load(std::memory_order_acquire);
    for (std::size_t i = 0; i < n; ++i) {
        auto& adapter = slots_[i];
        if (!adapter.fn) continue; // defensive – should not happen.

        result = adapter.fn(cdhash);

        // Always check budget immediately after adapter returns to guarantee
        // wall-clock bound even when the adapter produced a conclusive verdict.
        const auto now = Clock::now();
        if (now >= deadline) {
            ERR("DynamicVerifierRegistry: verify() exceeded budget");
            return KaiExpectedVerdict::failure(KaiError::VerifierTimeout);
        }

        if (result.code != Verdict::Code::kUnknown) {
            return KaiExpectedVerdict::success(result);
        }
    }

    // Final budget check for case of 0 verifiers or all unknown.
    if (Clock::now() - t_start >= budget_) {
        return KaiExpectedVerdict::failure(KaiError::VerifierTimeout);
    }

    return KaiExpectedVerdict::success(result); // unknown verdict
}

} // namespace launcher::core::security 