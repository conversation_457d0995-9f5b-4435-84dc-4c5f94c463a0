// -----------------------------------------------------------------------------
// @file verifier_strategy.hh
// @brief Compile-time configurable verifier chain driven by a constexpr
//        PolicyMask.  The strategy is header-only and removes calls to
//        verifiers that are disabled at compile time via `if constexpr` so
//        there is zero runtime branching cost.
//        
//        Bit layout (VerifierBit enum) must remain stable across the whole
//        codebase – changing the position of a verifier requires bumping the
//        snapshot version and regenerating PHF tables.
//        
//        This header intentionally keeps the implementation lightweight: heavy
//        Objective-C++ or Security.framework work continues to live in
//        separate translation units called by the thin wrappers below.
// -----------------------------------------------------------------------------

#pragma once

#include <cstdint>              // uint8_t
#include <type_traits>          // std::underlying_type_t
#include <initializer_list>

#include "mask128.hh"          // Mask128 helpers
#include "hash_types.hh"       // CDHash / Verdict
#include "seatbelt_verifier.hh"// verifySeatbeltProfile
#include "dynamic_verifier_registry.hh"
#include "codesign_verify.h"   // verifyCodeSignature (path-based but stubbed here)

namespace launcher::core::security {

// -----------------------------------------------------------------------------
// Bit positions for individual verifiers inside PolicyMask (lo part).
// -----------------------------------------------------------------------------

enum class VerifierBit : uint8_t {
    kCodeSign = 0,  // Apple SecStaticCodeCheckValidity or stub
    kSeatbelt = 1,  // Sandbox profile verifier
    kDynamic  = 2,  // Runtime-registered verifiers
    // When a new verifier is added append it *after* existing bits and assign
    // monotonically increasing values to preserve deterministic order.
};

inline constexpr uint8_t toIndex(VerifierBit v) {
    return static_cast<uint8_t>(v);
}

using PolicyMask = Mask128;

// -----------------------------------------------------------------------------
// Helper: build PolicyMask literals at constexpr time.
// -----------------------------------------------------------------------------
constexpr PolicyMask makePolicy(std::initializer_list<VerifierBit> bits) noexcept {
    PolicyMask m{};
    for (VerifierBit b : bits) {
        m.set(toIndex(b));
    }
    return m;
}

// Pre-defined commonly used policies.
inline constexpr PolicyMask kPolicyAll       = makePolicy({VerifierBit::kCodeSign,
                                                           VerifierBit::kSeatbelt,
                                                           VerifierBit::kDynamic});
inline constexpr PolicyMask kPolicyNoSeatbelt = makePolicy({VerifierBit::kCodeSign,
                                                            VerifierBit::kDynamic});
inline constexpr PolicyMask kPolicyCodeSignOnly = makePolicy({VerifierBit::kCodeSign});
inline constexpr PolicyMask kPolicyNone      = PolicyMask{}; // everything disabled

// -----------------------------------------------------------------------------
// Verifier wrappers – expose a uniform `static Verdict verify(const CDHash&)`.
// The heavy lifting lives elsewhere; these wrappers translate results to the
// common Verdict struct expected by higher-level code.
// -----------------------------------------------------------------------------

struct CodeSignVerifier {
    static constexpr uint8_t kBit   = toIndex(VerifierBit::kCodeSign);
    static constexpr uint8_t kOrder = 0;

    static inline Verdict verify(const CDHash& /*cdhash*/) noexcept {
#ifdef KAI_DISABLE_VERIFIER_RUNTIME
        // Unit tests / benchmarks can define this macro to stub-out expensive
        // Security.framework work.  Return Allowed so mask skipping logic can
        // still be observed deterministically.
        return Verdict{Verdict::Code::kAllowed};
#else
        // Production path – real verification implemented elsewhere.  We do
        // not have the file path here (Pipeline V3 will supply it).  Until
        // then we conservatively return Unknown so the next verifier in the
        // chain can decide.
        (void)verifyCodeSignature; // silence unused include warning
        return Verdict{}; // kUnknown
#endif
    }
};

struct SeatbeltVerifier {
    static constexpr uint8_t kBit   = toIndex(VerifierBit::kSeatbelt);
    static constexpr uint8_t kOrder = 1;

    static inline Verdict verify(const CDHash& /*cdhash*/) noexcept {
#ifdef KAI_DISABLE_VERIFIER_RUNTIME
        return Verdict{Verdict::Code::kAllowed};
#else
        auto res = verifySeatbeltProfile();
        return res ? Verdict{Verdict::Code::kAllowed}
                   : Verdict{Verdict::Code::kDenied};
#endif
    }
};

struct DynamicVerifierWrapper {
    static constexpr uint8_t kBit   = toIndex(VerifierBit::kDynamic);
    static constexpr uint8_t kOrder = 2;

    // Registry instance is injected by runtime code (or tests) prior to use.
    static void attachRegistry(DynamicVerifierRegistry* reg) noexcept {
        registry_ = reg;
    }

    static inline Verdict verify(const CDHash& cdhash) noexcept {
        if (!registry_) return Verdict{}; // unknown -> fallthrough
        auto exp = registry_->verify(cdhash);
        if (exp) return exp.value();
        // On timeout or error we map to Denied to be conservative.
        return Verdict{Verdict::Code::kDenied};
    }

 private:
    static inline DynamicVerifierRegistry* registry_{nullptr};
};

// -----------------------------------------------------------------------------
// Helper for deterministic ordering – compile-time comparison.
// -----------------------------------------------------------------------------
static_assert(CodeSignVerifier::kOrder < SeatbeltVerifier::kOrder,
              "Verifier execution order must be deterministic (codesign < seatbelt)");
static_assert(SeatbeltVerifier::kOrder < DynamicVerifierWrapper::kOrder,
              "Verifier execution order must be deterministic (seatbelt < dynamic)");

// -----------------------------------------------------------------------------
// Template strategy parameterised by a *constexpr* PolicyMask.
// -----------------------------------------------------------------------------

template <PolicyMask Policy>
struct VerifierStrategy {
    [[nodiscard]] static Verdict verify(const CDHash& cdhash) noexcept {
        Verdict out{}; // kUnknown initially
        run<CodeSignVerifier>(cdhash, out);
        run<SeatbeltVerifier>(cdhash, out);
        run<DynamicVerifierWrapper>(cdhash, out);
        return out;
    }

 private:
    template <typename VerifierT>
    static inline void run(const CDHash& cdhash, Verdict& out) noexcept {
        if constexpr (Policy.has(VerifierT::kBit)) {
            if (out.code == Verdict::Code::kUnknown) {
                out = VerifierT::verify(cdhash);
            }
        }
    }
};

} // namespace launcher::core::security 