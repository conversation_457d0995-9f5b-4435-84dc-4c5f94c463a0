// -----------------------------------------------------------------------------
// @file discovery_facade.h
// @brief Aggregates VerificationStore and optional ProbeCache to provide a
//        fast lookup/update path for runtime discovery and verification.
// -----------------------------------------------------------------------------
#pragma once

#include <filesystem>
#include <array>
#include "core/security/verification_store.hh"
#include "core/interfaces/probe_cache_api.h"

namespace launcher::core::security {

class DiscoveryFacade {
public:
    DiscoveryFacade(VerificationStore& vstore,
                    launcher::core::interfaces::IProbeCache* probe_cache) noexcept
        : vstore_{vstore}, probe_cache_{probe_cache} {}

    [[nodiscard]] Verdict fastLookup(const std::filesystem::path& path,
                                     const std::array<uint8_t,32>& mtime_sha) noexcept {
        // Try probe cache first
        if (probe_cache_) {
            Verdict v = probe_cache_->lookup(path, mtime_sha);
            if (v.code != Verdict::Code::kUnknown) return v;
        }
        // fall back to verification store (CDHash path) – compute pseudo cdhash
        CDHash cd = computeCdHash(path);
        auto exp = vstore_.find(cd);
        if (exp) return exp.value();
        return Verdict{}; // unknown
    }

    void persist(const std::filesystem::path& path,
                 const std::array<uint8_t,32>& mtime_sha,
                 Verdict verdict) noexcept {
        if (probe_cache_) probe_cache_->update(path, mtime_sha, verdict);
        CDHash cd = computeCdHash(path);
        (void)vstore_.insert(cd, verdict);
    }

private:
    VerificationStore& vstore_;
    launcher::core::interfaces::IProbeCache* probe_cache_; // may be null
};

} // namespace launcher::core::security 