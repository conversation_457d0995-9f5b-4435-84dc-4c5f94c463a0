/*
 * @file capability_threat.hh
 * @brief Compile-time mapping from Capability enum → threat class (safe/sensitive/dangerous).
 *        Used by UI for colour-coding capability masks.
 */

#pragma once

#include "../foundation/capability256.h"
#include <array>

namespace launcher::core::security {

// Threat classification buckets for UI colouring.
enum class ThreatClass : uint8_t {
    kSafe = 0,
    kSensitive = 1,
    kDangerous = 2
};

// -----------------------------------------------------------------------------
// constexpr lookup table.  Only first 128 entries needed for current Mask128 UI.
// Update as security policy evolves.
// -----------------------------------------------------------------------------
namespace detail {
// Build constexpr array with default kSafe, then override selectively.
constexpr auto buildThreatTable() {
    std::array<ThreatClass, 128> tbl{};
    tbl.fill(ThreatClass::kSafe);

    tbl[1]  = ThreatClass::kSensitive;  // Bluetooth
    tbl[2]  = ThreatClass::kDangerous;  // Camera
    tbl[4]  = ThreatClass::kSensitive;  // ClipboardWrite
    tbl[5]  = ThreatClass::kDangerous;  // Exec
    tbl[7]  = ThreatClass::kSensitive;  // GpuCompute
    tbl[8]  = ThreatClass::kDangerous;  // Jit
    tbl[9]  = ThreatClass::kSensitive;  // KeyboardInput
    tbl[10] = ThreatClass::kSensitive;  // Location
    tbl[11] = ThreatClass::kSensitive;  // MouseInput
    tbl[12] = ThreatClass::kSensitive;  // NetworkHttp
    tbl[13] = ThreatClass::kSensitive;  // NetworkHttps
    tbl[14] = ThreatClass::kSensitive;  // NetworkWebsocket
    tbl[16] = ThreatClass::kSensitive;  // OpenUrl
    tbl[17] = ThreatClass::kSensitive;  // PersistentStorage
    tbl[18] = ThreatClass::kDangerous;  // PowerManagement
    tbl[19] = ThreatClass::kSensitive;  // ReadFs
    tbl[21] = ThreatClass::kSensitive;  // Screenshot
    tbl[22] = ThreatClass::kDangerous;  // Serial
    tbl[23] = ThreatClass::kSensitive;  // SystemInfo
    tbl[25] = ThreatClass::kDangerous;  // Usb
    tbl[27] = ThreatClass::kSensitive;  // WindowOverlay
    tbl[28] = ThreatClass::kDangerous;  // WriteFs

    return tbl;
}

inline constexpr auto kThreatTable = buildThreatTable();
} // namespace detail

// constexpr helper
inline constexpr ThreatClass threatClass(kai::Capability cap) noexcept {
    const uint8_t idx = static_cast<uint8_t>(cap);
    return idx < 128 ? detail::kThreatTable[idx]
                      : ThreatClass::kSafe;
}

} // namespace launcher::core::security 