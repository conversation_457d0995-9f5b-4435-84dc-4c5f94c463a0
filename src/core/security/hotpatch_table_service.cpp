#include "hotpatch_table_service.h"
#include "hotpatch_verifier_table.hh"
#include "../diagnostics/diagnostics_service.h"

namespace launcher::core::security {

// Currently the service owns nothing; attachment is still done in main until
// Slice-3 wires actual loader + metrics.  We just expose start/stop logging.

::launcher::core::util::Result<void> HotpatchTableSvc::start() {
    DBG("HotpatchTableSvc start");

#if KAI_ENABLE_VPATCH
    std::string default_path;
    if (const char* home = std::getenv("HOME")) {
        default_path = std::string(home) + "/.microlauncher/hotpatch_verifier.kfsn";
    } else {
        default_path = "hotpatch_verifier.kfsn";
    }

    std::string cfg_path = cfg_ ? cfg_->getString("security.vpatch_path", default_path)
                                : default_path;

    std::filesystem::path vpatch_path = cfg_path;
    if (!std::filesystem::exists(vpatch_path)) {
        DBG("Hotpatch table not present at " << vpatch_path);
        return ::launcher::core::util::Result<void>::success();
    }

    auto tbl_res = HotpatchVerifierTable::load(vpatch_path);
    if (!tbl_res) {
        ERR("Hotpatch table load failed for " << vpatch_path);
        return ::launcher::core::util::Result<void>::success(); // non-fatal
    }

    table_ = std::move(tbl_res.value());
    vstore_.attachHotpatchTable(table_);
    loaded_ = true;

    if (diag_) {
        diag_->incrementCounter("security.vpatch_load", 1);
        diag_->setGauge("security.vpatch_entries", static_cast<int64_t>(table_.entryCount()));
    }

    INF("HotpatchTableSvc loaded " << table_.entryCount() << " entries from " << vpatch_path);
#endif
    return ::launcher::core::util::Result<void>::success();
}

void HotpatchTableSvc::stop() noexcept {
#if KAI_ENABLE_VPATCH
    loaded_ = false;
#endif
    DBG("HotpatchTableSvc stop");
}

} // namespace launcher::core::security 