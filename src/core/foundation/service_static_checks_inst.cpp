// -----------------------------------------------------------------------------
// @file service_static_checks_inst.cpp
// @brief Translation unit that includes auto-generated service_static_checks.h
//        to enforce compile-time dependency validation.  The static_assert
//        inside the header will fail the build when the Service DAG or sort
//        order is inconsistent with services.yaml.
// -----------------------------------------------------------------------------

#include "service_static_checks.h"

// Empty TU – all work done in header static_assert. 