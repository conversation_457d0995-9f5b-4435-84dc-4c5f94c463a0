/*
 * @file iservice.h
 * @brief Abstract interface implemented by all core services.
 */
#pragma once

#include <service_id.h>
#include "../util/result.h"
//#include other maybe not needed for interface

namespace launcher::core::foundation {

// Forward declaration of error type for IService operations.
// Each concrete service can return richer error info, but at
// ServiceRegistry level we treat only success/failure (void).

struct IService {
    virtual ~IService() = default;

    // Numeric identifier of the concrete service. Must be unique and match
    // the enumerator declared in ServiceId; dependencies are reflected via
    // ServiceBase::kDeps.
    [[nodiscard]] virtual ServiceId id() const noexcept = 0;

    // Start the service – acquire resources, spawn threads, etc.
    // Should be idempotent; called exactly once by ServiceRegistry.
    // Use util::Expected to propagate error.
    virtual launcher::core::util::Result<void> start() = 0;

    // Stop the service – release resources.  Must NOT throw; should
    // tolerate repeated calls.
    virtual void stop() noexcept = 0;
};

}  // namespace launcher::core::foundation 