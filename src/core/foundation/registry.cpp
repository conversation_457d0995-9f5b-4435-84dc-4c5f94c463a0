#include "registry.h"
#include "service_id.h"
#include "service_topology.h"
#include "service_name_phf.h"
#include "service_static_checks.h"
#include "service_dependencies.h"

#include "../util/debug.h"
#include "../diagnostics/diagnostics_service.h"
#include "../util/span_guard.h"
#include <string>

namespace launcher::core::foundation {

// ----------------------------- ctor ----------------------------------------
ServiceRegistry::ServiceRegistry() {
    slots_.fill(nullptr);
}

// -------------------------------------------------------------------------
// Dependency span now provided by service_dependencies.h
// -------------------------------------------------------------------------

// --------------------------- registerService -------------------------------
KaiVoidExpected ServiceRegistry::registerService(IService& svc) noexcept {
    const ServiceId id = svc.id();
    const std::size_t idx = toIndex(id);

    if (slots_[idx] != nullptr) {
        ERR("ServiceRegistry::registerService – duplicate registration id=" << static_cast<int>(id));
        return KaiExpected<void>::failure(KaiError::AlreadyRegistered);
    }

    slots_[idx] = &svc;
    return KaiExpected<void>::success();
}

// ----------------------------- get(name) -----------------------------------
IService* ServiceRegistry::get(std::string_view name) noexcept {
    auto idOpt = launcher::core::foundation::lookupService(name);
    if (!idOpt) return nullptr;
    return slots_[toIndex(*idOpt)];
}

// --------------------------- startAll --------------------------------------
KaiVoidExpected ServiceRegistry::startAll() noexcept {
    if (started_) return KaiExpected<void>::success();

    // Use compile-time pre-sorted order from generated header.
    constexpr auto& order = launcher::core::foundation::kSorted;
    static_assert(order.size() == kMaxServices, "kSorted size mismatch");

    for (std::size_t idx = 0; idx < order.size(); ++idx) {
        const ServiceId id = order[idx];
        IService* svc = slots_[toIndex(id)];
        if (!svc) {
            // Service not registered – allowed for optional services; skip.
            continue;
        }
        DBG("Starting service id=" << static_cast<int>(id));

        // Validate that all declared dependencies are registered.
#ifndef KAI_DISABLE_RUNTIME_DEP_CHECK
        // Compile-time topology validation already guarantees acyclic order.
        // This runtime pass only guards accidental omission of optional
        // services during manual wiring in main().  Disable via
        // -DKAI_DISABLE_RUNTIME_DEP_CHECK for production builds where the
        // additional branch impacts cold-start micro-benchmarks by ~30 µs.
        for (ServiceId dep_id : dependencySpan(id)) {
            if (!slots_[toIndex(dep_id)]) {
                ERR("ServiceRegistry::startAll – missing dependency id=" << static_cast<int>(dep_id)
                    << " for id=" << static_cast<int>(id));
                return KaiExpected<void>::failure(KaiError::NotRegistered);
            }
        }
#endif  // !KAI_DISABLE_RUNTIME_DEP_CHECK

        // Measure start latency ------------------------------------------------
        diagnostics::DiagnosticsService* diag_ptr = static_cast<diagnostics::DiagnosticsService*>(
            slots_[toIndex(ServiceId::kDiagnosticsService)]);
        const std::string metric_key = std::string("service.start_us.") + std::string(toString(id));
        util::SpanGuard _sg(diag_ptr, metric_key);

        auto result = svc->start();
        if (!result) {
            ERR("Service start failed id=" << static_cast<int>(id));
            // Attempt partial rollback – stop what we started so far.
            for (std::size_t k = idx; k-- > 0;) {
                const ServiceId started_id = order[k];
                IService* started_svc = slots_[toIndex(started_id)];
                if (started_svc) {
                    started_svc->stop();
                }
            }
            return KaiExpected<void>::failure(KaiError::NotRegistered); // degrade generic error
        }
        started_order_[started_count_++] = id; // record successful start sequence
    }

    started_ = true;
    return KaiExpected<void>::success();
}

// --------------------------- stopAll ---------------------------------------
void ServiceRegistry::stopAll() noexcept {
    if (!started_) return;

    // Stop in *exact* reverse order of successful starts (LIFO). The previous
    // implementation used the idiom `for (std::size_t idx = n; idx-- > 0;)`
    // which evaluates `idx--` *before* the body, yielding an initial index of
    // `n` (out-of-range) and skipping the element at 0. Replace with an
    // explicit decrement inside the loop for clarity and correctness.

    for (std::size_t idx = started_count_; idx > 0; --idx) {
        const ServiceId id = started_order_[idx - 1];
        if (IService* svc = slots_[toIndex(id)]; svc) {
            DBG("Stopping service id=" << static_cast<int>(id));
            svc->stop();
        }
    }

    started_ = false;
    started_count_ = 0;
}

} // namespace launcher::core::foundation 