# Service foundation module – provides ServiceReg<PERSON>ry & friends as a
# dedicated library so edits rebuild only the affected code.

set(FOUNDATION_SOURCES
    registry.cpp
    service_graph_check.cpp
)

# ----------------------------------------------------------------------------
# Capability header generation (unchanged logic, but hook target differently)
# ----------------------------------------------------------------------------
add_custom_command(
    OUTPUT ${CMAKE_CURRENT_SOURCE_DIR}/capability256.h
    COMMAND $<TARGET_FILE:kai-capability-gen> -s ${PROJECT_SOURCE_DIR}/manifest.schema.json -o ${CMAKE_CURRENT_SOURCE_DIR}/capability256.h
    DEPENDS kai-capability-gen ${PROJECT_SOURCE_DIR}/manifest.schema.json
    COMMENT "Generating capability256.h from manifest.schema.json" VERBATIM
)
add_custom_target(generate_capabilities DEPENDS ${CMAKE_CURRENT_SOURCE_DIR}/capability256.h)

set(GEN_DIR ${CMAKE_BINARY_DIR}/generated/services)

# ----------------------------------------------------------------------------
# OBJECT library owns compilation units
# ----------------------------------------------------------------------------
add_library(core_foundation_obj OBJECT ${FOUNDATION_SOURCES})
add_dependencies(core_foundation_obj generate_service_headers)

target_include_directories(core_foundation_obj
    PUBLIC ${GEN_DIR} ${CMAKE_CURRENT_SOURCE_DIR}
)

# Propagate dependency on memory layer so allocator headers are visible and
# symbols linked correctly.
target_link_libraries(core_foundation_obj PUBLIC core_memory nlohmann_json::nlohmann_json)

add_dependencies(core_foundation_obj generate_capabilities)

# ----------------------------------------------------------------------------
# Thin STATIC façade (public)
# ----------------------------------------------------------------------------
add_library(core_foundation STATIC)

target_link_libraries(core_foundation PUBLIC core_foundation_obj core_memory nlohmann_json::nlohmann_json)

# Export include paths upstream – maintain previous behaviour for clients
# that link `core` (which in turn links core_foundation).
target_include_directories(core_foundation
    PUBLIC ${GEN_DIR} ${CMAKE_CURRENT_SOURCE_DIR}
)

# Legacy dependency back-compat – keep core depending on generated header until
# all modules are migrated off.
if(TARGET core)
    add_dependencies(core generate_capabilities)
endif()

# Remove legacy addition of sources to `core` target (was below) – obsolete. 