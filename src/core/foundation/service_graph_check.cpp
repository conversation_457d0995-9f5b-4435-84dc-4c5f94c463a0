// Slice-2: Stand-alone translation unit that validates the service dependency
// graph at compile-time.  No symbols are emitted; the file exists solely to
// trigger the static_assert so that the build fails early when services.yaml
// drifts without regenerating headers.

#include "service_static_checks.h"

static_assert(launcher::core::foundation::_validateServiceOrder(),
              "Service dependency graph invalid – regenerate headers via kai-service-gen"); 