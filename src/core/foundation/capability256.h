//------------------------------------------------------------------------------
// ⚠️  AUTO-GENERATED FILE – DO NOT EDIT.
// Generated by: kai-capability-gen
// Schema hash: 2C7D033983C0FE6D
//------------------------------------------------------------------------------

#pragma once

#include <array>
#include <cstdint>
#include <initializer_list>
#include <string_view>
#include <optional>

namespace kai {

enum class Capability : uint8_t {
    kAudioOutput = 0,
    kBluetooth = 1,
    kCameraAccess = 2,
    kClipboardRead = 3,
    kClipboardWrite = 4,
    kExec = 5,
    kFileDialog = 6,
    kGpuCompute = 7,
    kJit = 8,
    kKeyboardInput = 9,
    kLocation = 10,
    kMouseInput = 11,
    kNetworkHttp = 12,
    kNetworkHttps = 13,
    kNetworkWebsocket = 14,
    kNotifications = 15,
    kOpenUrl = 16,
    kPersistentStorage = 17,
    kPowerManagement = 18,
    kReadFs = 19,
    kScheduler = 20,
    kScreenshot = 21,
    kSerial = 22,
    kSystemInfo = 23,
    kTempStorage = 24,
    kUsb = 25,
    kWindowOpen = 26,
    kWindowOverlay = 27,
    kWriteFs = 28,
    _RESERVED_29 = 29,
    _RESERVED_30 = 30,
    _RESERVED_31 = 31,
    _RESERVED_32 = 32,
    _RESERVED_33 = 33,
    _RESERVED_34 = 34,
    _RESERVED_35 = 35,
    _RESERVED_36 = 36,
    _RESERVED_37 = 37,
    _RESERVED_38 = 38,
    _RESERVED_39 = 39,
    _RESERVED_40 = 40,
    _RESERVED_41 = 41,
    _RESERVED_42 = 42,
    _RESERVED_43 = 43,
    _RESERVED_44 = 44,
    _RESERVED_45 = 45,
    _RESERVED_46 = 46,
    _RESERVED_47 = 47,
    _RESERVED_48 = 48,
    _RESERVED_49 = 49,
    _RESERVED_50 = 50,
    _RESERVED_51 = 51,
    _RESERVED_52 = 52,
    _RESERVED_53 = 53,
    _RESERVED_54 = 54,
    _RESERVED_55 = 55,
    _RESERVED_56 = 56,
    _RESERVED_57 = 57,
    _RESERVED_58 = 58,
    _RESERVED_59 = 59,
    _RESERVED_60 = 60,
    _RESERVED_61 = 61,
    _RESERVED_62 = 62,
    _RESERVED_63 = 63,
    _RESERVED_64 = 64,
    _RESERVED_65 = 65,
    _RESERVED_66 = 66,
    _RESERVED_67 = 67,
    _RESERVED_68 = 68,
    _RESERVED_69 = 69,
    _RESERVED_70 = 70,
    _RESERVED_71 = 71,
    _RESERVED_72 = 72,
    _RESERVED_73 = 73,
    _RESERVED_74 = 74,
    _RESERVED_75 = 75,
    _RESERVED_76 = 76,
    _RESERVED_77 = 77,
    _RESERVED_78 = 78,
    _RESERVED_79 = 79,
    _RESERVED_80 = 80,
    _RESERVED_81 = 81,
    _RESERVED_82 = 82,
    _RESERVED_83 = 83,
    _RESERVED_84 = 84,
    _RESERVED_85 = 85,
    _RESERVED_86 = 86,
    _RESERVED_87 = 87,
    _RESERVED_88 = 88,
    _RESERVED_89 = 89,
    _RESERVED_90 = 90,
    _RESERVED_91 = 91,
    _RESERVED_92 = 92,
    _RESERVED_93 = 93,
    _RESERVED_94 = 94,
    _RESERVED_95 = 95,
    _RESERVED_96 = 96,
    _RESERVED_97 = 97,
    _RESERVED_98 = 98,
    _RESERVED_99 = 99,
    _RESERVED_100 = 100,
    _RESERVED_101 = 101,
    _RESERVED_102 = 102,
    _RESERVED_103 = 103,
    _RESERVED_104 = 104,
    _RESERVED_105 = 105,
    _RESERVED_106 = 106,
    _RESERVED_107 = 107,
    _RESERVED_108 = 108,
    _RESERVED_109 = 109,
    _RESERVED_110 = 110,
    _RESERVED_111 = 111,
    _RESERVED_112 = 112,
    _RESERVED_113 = 113,
    _RESERVED_114 = 114,
    _RESERVED_115 = 115,
    _RESERVED_116 = 116,
    _RESERVED_117 = 117,
    _RESERVED_118 = 118,
    _RESERVED_119 = 119,
    _RESERVED_120 = 120,
    _RESERVED_121 = 121,
    _RESERVED_122 = 122,
    _RESERVED_123 = 123,
    _RESERVED_124 = 124,
    _RESERVED_125 = 125,
    _RESERVED_126 = 126,
    _RESERVED_127 = 127,
    _RESERVED_128 = 128,
    _RESERVED_129 = 129,
    _RESERVED_130 = 130,
    _RESERVED_131 = 131,
    _RESERVED_132 = 132,
    _RESERVED_133 = 133,
    _RESERVED_134 = 134,
    _RESERVED_135 = 135,
    _RESERVED_136 = 136,
    _RESERVED_137 = 137,
    _RESERVED_138 = 138,
    _RESERVED_139 = 139,
    _RESERVED_140 = 140,
    _RESERVED_141 = 141,
    _RESERVED_142 = 142,
    _RESERVED_143 = 143,
    _RESERVED_144 = 144,
    _RESERVED_145 = 145,
    _RESERVED_146 = 146,
    _RESERVED_147 = 147,
    _RESERVED_148 = 148,
    _RESERVED_149 = 149,
    _RESERVED_150 = 150,
    _RESERVED_151 = 151,
    _RESERVED_152 = 152,
    _RESERVED_153 = 153,
    _RESERVED_154 = 154,
    _RESERVED_155 = 155,
    _RESERVED_156 = 156,
    _RESERVED_157 = 157,
    _RESERVED_158 = 158,
    _RESERVED_159 = 159,
    _RESERVED_160 = 160,
    _RESERVED_161 = 161,
    _RESERVED_162 = 162,
    _RESERVED_163 = 163,
    _RESERVED_164 = 164,
    _RESERVED_165 = 165,
    _RESERVED_166 = 166,
    _RESERVED_167 = 167,
    _RESERVED_168 = 168,
    _RESERVED_169 = 169,
    _RESERVED_170 = 170,
    _RESERVED_171 = 171,
    _RESERVED_172 = 172,
    _RESERVED_173 = 173,
    _RESERVED_174 = 174,
    _RESERVED_175 = 175,
    _RESERVED_176 = 176,
    _RESERVED_177 = 177,
    _RESERVED_178 = 178,
    _RESERVED_179 = 179,
    _RESERVED_180 = 180,
    _RESERVED_181 = 181,
    _RESERVED_182 = 182,
    _RESERVED_183 = 183,
    _RESERVED_184 = 184,
    _RESERVED_185 = 185,
    _RESERVED_186 = 186,
    _RESERVED_187 = 187,
    _RESERVED_188 = 188,
    _RESERVED_189 = 189,
    _RESERVED_190 = 190,
    _RESERVED_191 = 191,
    _RESERVED_192 = 192,
    _RESERVED_193 = 193,
    _RESERVED_194 = 194,
    _RESERVED_195 = 195,
    _RESERVED_196 = 196,
    _RESERVED_197 = 197,
    _RESERVED_198 = 198,
    _RESERVED_199 = 199,
    _RESERVED_200 = 200,
    _RESERVED_201 = 201,
    _RESERVED_202 = 202,
    _RESERVED_203 = 203,
    _RESERVED_204 = 204,
    _RESERVED_205 = 205,
    _RESERVED_206 = 206,
    _RESERVED_207 = 207,
    _RESERVED_208 = 208,
    _RESERVED_209 = 209,
    _RESERVED_210 = 210,
    _RESERVED_211 = 211,
    _RESERVED_212 = 212,
    _RESERVED_213 = 213,
    _RESERVED_214 = 214,
    _RESERVED_215 = 215,
    _RESERVED_216 = 216,
    _RESERVED_217 = 217,
    _RESERVED_218 = 218,
    _RESERVED_219 = 219,
    _RESERVED_220 = 220,
    _RESERVED_221 = 221,
    _RESERVED_222 = 222,
    _RESERVED_223 = 223,
    _RESERVED_224 = 224,
    _RESERVED_225 = 225,
    _RESERVED_226 = 226,
    _RESERVED_227 = 227,
    _RESERVED_228 = 228,
    _RESERVED_229 = 229,
    _RESERVED_230 = 230,
    _RESERVED_231 = 231,
    _RESERVED_232 = 232,
    _RESERVED_233 = 233,
    _RESERVED_234 = 234,
    _RESERVED_235 = 235,
    _RESERVED_236 = 236,
    _RESERVED_237 = 237,
    _RESERVED_238 = 238,
    _RESERVED_239 = 239,
    _RESERVED_240 = 240,
    _RESERVED_241 = 241,
    _RESERVED_242 = 242,
    _RESERVED_243 = 243,
    _RESERVED_244 = 244,
    _RESERVED_245 = 245,
    _RESERVED_246 = 246,
    _RESERVED_247 = 247,
    _RESERVED_248 = 248,
    _RESERVED_249 = 249,
    _RESERVED_250 = 250,
    _RESERVED_251 = 251,
    _RESERVED_252 = 252,
    _RESERVED_253 = 253,
    _RESERVED_254 = 254,
    _RESERVED_255 = 255,
};

using CapabilityBitmap = std::array<uint64_t, 4>;

constexpr void set(CapabilityBitmap& bm, Capability cap) {
    const auto idx = static_cast<uint8_t>(cap);
    bm[idx >> 6] |= uint64_t{1} << (idx & 63);
}

constexpr bool has(const CapabilityBitmap& bm, Capability cap) {
    const auto idx = static_cast<uint8_t>(cap);
    return (bm[idx >> 6] & (uint64_t{1} << (idx & 63))) != 0ULL;
}

constexpr CapabilityBitmap makeBitmap(std::initializer_list<Capability> caps_init) {
    CapabilityBitmap bm{0, 0, 0, 0};
    for (auto cap : caps_init) { set(bm, cap); }
    return bm;
}

// Array mapping enum → string_view (size 256).
inline constexpr std::array<std::string_view, 256> kCapabilityNames = {
    "audio_output",
    "bluetooth",
    "camera_access",
    "clipboard_read",
    "clipboard_write",
    "exec",
    "file_dialog",
    "gpu_compute",
    "jit",
    "keyboard_input",
    "location",
    "mouse_input",
    "network_http",
    "network_https",
    "network_websocket",
    "notifications",
    "open_url",
    "persistent_storage",
    "power_management",
    "read_fs",
    "scheduler",
    "screenshot",
    "serial",
    "system_info",
    "temp_storage",
    "usb",
    "window_open",
    "window_overlay",
    "write_fs",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
};

constexpr std::string_view toString(Capability cap) noexcept {
    return kCapabilityNames[static_cast<uint8_t>(cap)];
}

constexpr std::optional<Capability> fromString(std::string_view name) noexcept {
    for (uint16_t i = 0; i < kCapabilityNames.size(); ++i) {
        if (kCapabilityNames[i] == name) return static_cast<Capability>(i);
    }
    return std::nullopt;
}

} // namespace kai
