/*
 * @file service_base.h
 * @brief CRTP helper that injects dependencies via constructor, stores
 *        them as references, and auto-registers the derived service in
 *        the ServiceRegistry.
 */
#pragma once

#include <tuple>
#include <functional>
#include <type_traits>
#include <array>

#include "iservice.h"
#include "registry.h"
#include "../util/debug.h"

namespace launcher::core::foundation {

// ---------------------------------------------------------------------------
// ServiceBase – CRTP mix-in for constructor-based dependency injection.
// Usage:
//   class FooSvc final : public ServiceBase<FooSvc, BarSvc, BazSvc> {
//       using Base = ServiceBase<FooSvc, BarSvc, BazSvc>;
//    public:
//       explicit FooSvc(ServiceRegistry& reg) : Base(reg) {}
//       ...
//   };
//
// At construction time the base will:
//   • Look up each dependency BarSvc, BazSvc in the registry and store a
//     reference in a std::tuple so later access is zero-cost.
//   • Register *this into the registry, eliminating separate calls.
//
// A compile-time error is triggered when attempting to access a dependency
// that was not declared in the template parameter pack.
// ---------------------------------------------------------------------------

template <class Derived, class... Deps>
class ServiceBase : public IService {
 protected:
    explicit ServiceBase(ServiceRegistry& registry) : deps_(std::ref(registry.get<Deps>())...) {
        // Auto-register into the registry; duplicate registration is treated
        // as a non-fatal logical error in Release but will trip an ERR log.
        auto reg_res = registry.registerService(*static_cast<Derived*>(this));
        if (!reg_res) {
            ERR("ServiceBase – duplicate registration of id="
                << static_cast<int>(Derived::kId));
        }
    }

 public:
    // Convenience constant holding the number of declared dependencies.
    static constexpr std::size_t kDepCount = sizeof...(Deps);

    // Default implementation of IService::id() using Derived::kId.
    [[nodiscard]] ServiceId id() const noexcept final { return Derived::kId; }

    // Access declared dependency by type – compile-time checked.
    template <typename DepT>
    [[nodiscard]] DepT& get() noexcept {
        static_assert(sizeof...(Deps) > 0, "ServiceBase::get – service declares no dependencies");
        static_assert((std::is_same_v<DepT, Deps> || ...),
                      "ServiceBase::get – requested type not in dependency list");
        return std::get<std::reference_wrapper<DepT>>(deps_).get();
    }

    // Expose dependency list for compile-time reflection.
    using DependenciesType = std::tuple<Deps...>;
    static constexpr std::array<ServiceId, sizeof...(Deps)> kDeps{Deps::kId...};

 private:
    std::tuple<std::reference_wrapper<Deps>...> deps_;
};

} // namespace launcher::core::foundation 