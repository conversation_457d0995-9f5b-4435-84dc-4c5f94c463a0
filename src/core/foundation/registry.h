/*
 * @file registry.h
 * @brief Fixed-size service registry with deterministic start/stop order.
 */
#pragma once

#include <array>
#include <span>
#include <cstdint>
#include <cassert>
#include <string_view>

#include "iservice.h"
#include "../util/expected.h"
#include "../util/debug.h"
#include "service_topology.h"
#include "service_dependencies.h"

namespace launcher::core::foundation {

// ---------------------------------------------------------------------------
// Error codes returned by ServiceRegistry operations
// ---------------------------------------------------------------------------

enum class KaiError {
    CircularDependency,
    AlreadyRegistered,
    NotRegistered,
    CapabilityDenied,
    BackPressure,
    UnsupportedRuntime,   // plugin runtime kind not supported by host
    RuntimeInitFailed,    // plugin runtime initialise() returned failure
    InvalidSignature,     // codesign verification failed
    DlopenFailed,         // failed to load shared library
    MissingSymbol,        // required symbol not found in dylib
    AbiIncompatible,      // abi or version mismatch
    UidMismatch,          // owner UID of plugin file mismatched current user
    StatFailed,           // stat() on plugin file failed
    SeatbeltInvalid,      // sandbox profile invalid or denied
    CapabilitiesMissing,  // plugin manifest missing capabilities field
    TooManyVerifiers,     // dynamic verifier registry saturation / rate limit
    VerifierTimeout,      // verifier chain exceeded execution budget
    RemoteError,          // HTTP 5xx or API-level error
    NetworkError,         // TCP/connect/timeout
    JsonMalformed,        // JSON parse or schema error
    PermInsecure,         // insecure directory permissions or ownership
};

// Convenience alias using existing util::Expected with our error enum
// ---------------------------------------------------------------------------

template <typename T>
using KaiExpected = launcher::core::util::Expected<T, KaiError>;

using KaiVoidExpected = KaiExpected<void>;

// ---------------------------------------------------------------------------
// ServiceRegistry – lightweight container for core IService instances
// ---------------------------------------------------------------------------

class ServiceRegistry {
 public:
    ServiceRegistry();

    // Non-copyable / movable: registry lives at global scope.
    ServiceRegistry(const ServiceRegistry&) = delete;
    ServiceRegistry& operator=(const ServiceRegistry&) = delete;

    // Register the concrete service instance (non-owning pointer).
    [[nodiscard]] KaiVoidExpected registerService(IService& svc) noexcept;

    // Fast O(1) lookup by compile-time type.
    template <typename ServiceT>
    [[nodiscard]] ServiceT& get() noexcept;

    // Safe lookup returning Expected instead of UB when missing.
    template <typename ServiceT>
    [[nodiscard]] KaiExpected<ServiceT*> tryGet() noexcept;

    // Legacy shim: O(1) lookup by service *name* (string). Returns nullptr
    // when the name is unknown or the service was not registered.
    [[nodiscard]] IService* get(std::string_view name) noexcept;

    // Start / stop full DAG.
    [[nodiscard]] KaiVoidExpected startAll() noexcept;
    void                        stopAll() noexcept;

    template <typename F>
    void forEachService(F&& fn) noexcept {
        for (auto* s : slots_) {
            if (s) fn(*s);
        }
    }

 private:
    std::array<IService*, kMaxServices> slots_{}; // nullptr-initialised
    std::array<ServiceId, kMaxServices> started_order_{}; // ids actually started
    std::size_t                         started_count_{0};
    bool                                started_{false};

    // helpers
    [[nodiscard]] static std::span<const ServiceId> dependencies(ServiceId id) noexcept {
        return dependencySpan(id);
    }
};

// --------------------------- Inline definitions -----------------------------

template <typename ServiceT>
ServiceT& ServiceRegistry::get() noexcept {
    constexpr ServiceId id = ServiceT::kId; // requires kId static member – fallback compile error.
    auto* ptr = slots_[toIndex(id)];
    assert(ptr && "ServiceRegistry::get – service not registered");
#ifdef KAI_TRACE_SERVICE_LOOKUP
    DBG("ServiceRegistry::get() lookup id=" << static_cast<int>(id) << ", ptr=" << ptr);
#endif  // KAI_TRACE_SERVICE_LOOKUP
    return *static_cast<ServiceT*>(ptr);
}

template <typename ServiceT>
KaiExpected<ServiceT*> ServiceRegistry::tryGet() noexcept {
    constexpr ServiceId id = ServiceT::kId;
    IService* ptr = slots_[toIndex(id)];
    if (!ptr) {
        return KaiExpected<ServiceT*>::failure(KaiError::NotRegistered);
    }
    return KaiExpected<ServiceT*>::success(static_cast<ServiceT*>(ptr));
}

} // namespace launcher::core::foundation 