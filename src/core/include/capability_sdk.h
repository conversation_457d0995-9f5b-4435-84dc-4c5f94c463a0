#pragma once

// Public-facing header that exposes Kai security capabilities to plugins and
// external components. It simply includes the generated capability256.h from
// the core foundation layer and re-exports its primary types into the
// `kai` namespace, guaranteeing ABI stability.
//
// NOTE: This header is part of the stable SDK surface. Do not introduce
// breaking changes (renames, removals) without bumping the ABI version.

#include <core/foundation/capability256.h>

namespace kai {
using ::kai::Capability;        // enum
using ::kai::CapabilityBitmap;  // std::array<uint64_t,4>
using ::kai::set;
using ::kai::has;
using ::kai::makeBitmap;
using ::kai::toString;
using ::kai::fromString;
} // namespace kai 