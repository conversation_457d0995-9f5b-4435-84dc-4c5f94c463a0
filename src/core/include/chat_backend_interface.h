#pragma once

#include <functional>
#include <memory>
#include <string>
#include <string_view>
#include <variant>
#include <vector>
#include "../../utilities/cancellation_token.h" // Corrected path

namespace launcher {
namespace core {

// Forward declare the token class from the utilities namespace
namespace utilities {
class CancellationToken;
} // namespace utilities

/**
 * @struct MentionableItem
 * @brief Data structure for a mention suggestion item.
 */
struct MentionableItem {
    std::string id;
    std::string display_name;
    // Add other fields as needed (e.g., avatar_url)
};

/**
 * @enum BackendErrorCode
 * @brief Defines error codes for backend operations.
 */
enum class BackendErrorCode {
    NONE,
    CANCELLED,
    NETWORK_ERROR,
    INTERNAL_ERROR,
    INVALID_ARGUMENT,
    // Add more specific codes as needed
};

/**
 * @struct BackendErrorInfo
 * @brief Data structure for backend error information.
 */
struct BackendErrorInfo {
    BackendErrorCode code;
    std::string message;
    // Consider adding std::exception_ptr for underlying C++ exceptions
};

// Define result types using std::variant
using MentionResult = std::variant<std::vector<MentionableItem>, BackendErrorInfo>;
using HistoryResult = std::variant<std::vector<std::string>, BackendErrorInfo>;
using LLMSuggestionResult = std::variant<std::vector<std::string>, BackendErrorInfo>;

// Define callback types using std::function
using MentionCallback = std::function<void(MentionResult)>;
using HistoryCallback = std::function<void(HistoryResult)>;
using LLMSuggestionCallback = std::function<void(LLMSuggestionResult)>;

// --- Add Stream Callback Type ---
using StreamDataCallback = std::function<void(const std::string& /* chunk */)>;
using StreamCompletionCallback = std::function<void(const BackendErrorInfo& /* error */)>;
// --- End Add Stream Callback Type ---

/**
 * @interface IChatBackend
 * @brief Interface for the core chat backend logic.
 *
 * Defines methods for retrieving mention suggestions, history, and LLM completions.
 * Implementations should perform these tasks asynchronously.
 */
class IChatBackend {
public:
    virtual ~IChatBackend() = default;

    /**
     * @brief Asynchronously searches for mentionable items based on a query.
     * @param query The search query string.
     * @param cancellation_token A token to signal cancellation.
     * @param callback The callback function to invoke with the result or error.
     */
    virtual void searchMentionableItems(
        std::string_view query, // Changed to string_view
        std::shared_ptr<utilities::CancellationToken> cancellation_token,
        MentionCallback callback
    ) = 0;

    /**
     * @brief Asynchronously searches for chat history based on a prefix.
     * @param prefix The prefix to search for.
     * @param cancellation_token A token to signal cancellation.
     * @param callback The callback function to invoke with the result or error.
     */
    virtual void searchHistory(
        std::string_view prefix, // Changed to string_view
        std::shared_ptr<utilities::CancellationToken> cancellation_token,
        HistoryCallback callback
    ) = 0;

    /**
     * @brief Asynchronously requests LLM suggestions based on context.
     * @param context The context text for the LLM.
     * @param cancellation_token A token to signal cancellation.
     * @param callback The callback function to invoke with the result or error.
     */
    virtual void requestLLMSuggestions(
        std::string_view context, // Changed to string_view
        std::shared_ptr<utilities::CancellationToken> cancellation_token,
        LLMSuggestionCallback callback
    ) = 0;

    /**
     * @brief Asynchronously sends a message and streams the response.
     *
     * @param message The user message content.
     * @param context The current context (history, etc.).
     * @param cancellation_token Token to signal cancellation.
     * @param provider_id The ID of the provider.
     * @param model_id The ID of the model.
     * @param dataCallback Callback for each chunk of the streamed response.
     * @param completionCallback Callback invoked when the stream finishes or errors.
     */
    virtual void sendMessageStream(
        std::string_view message, // Changed to string_view
        const class Context& context,
        std::shared_ptr<utilities::CancellationToken> cancellation_token,
        const std::string& provider_id,
        const std::string& model_id,
        StreamDataCallback dataCallback,
        StreamCompletionCallback completionCallback
    ) = 0;

    // Note: Implementations must wrap core logic in try-catch to convert exceptions
    // to BackendErrorInfo before invoking the callback.
};

} // namespace core
} // namespace launcher 