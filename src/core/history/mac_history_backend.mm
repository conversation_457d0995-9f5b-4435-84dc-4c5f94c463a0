#import "mac_history_backend.h"

#ifdef __APPLE__

#import <AppKit/AppKit.h>
#import <CoreServices/CoreServices.h>
#include "history_manager.h"
#include "../util/debug.h"
#include "../config/config_manager.h"
#import <ScriptingBridge/ScriptingBridge.h>
#import "../../ui/macos/browser_history_importer.h"
#include "../../ranking/spotlight_metadata.h"
#include <cstdint>
#include "../../ui/common/result_item.h"

#ifndef NSWorkspaceDidOpenFileNotification
#define NSWorkspaceDidOpenFileNotification @"NSWorkspaceDidOpenFileNotification"
#endif
#ifndef NSWorkspaceFilename
#define NSWorkspaceFilename @"NSWorkspaceFilename"
#endif

// Forward declaration for SpotlightBootstrapper
@class SpotlightBootstrapper;

@interface MacHistoryObserver : NSObject
@property(nonatomic, assign) launcher::core::HistoryManager *manager;
- (instancetype)initWithManager:(launcher::core::HistoryManager *)mgr;
@end

@implementation MacHistoryObserver

- (instancetype)initWithManager:(launcher::core::HistoryManager *)mgr {
    self = [super init];
    if (self) {
        _manager = mgr;

        NSNotificationCenter *nc = [NSNotificationCenter defaultCenter];
        [nc addObserver:self
               selector:@selector(appLaunched:)
                   name:NSWorkspaceDidLaunchApplicationNotification
                 object:nil];

        // Observe document/file openings
        if (@available(macOS 10.15, *)) {
            [nc addObserver:self
                   selector:@selector(fileOpened:)
                       name:(NSNotificationName)NSWorkspaceDidOpenFileNotification
                     object:nil];
        }
    }
    return self;
}

- (void)dealloc {
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

- (void)appLaunched:(NSNotification *)notification {
    NSDictionary *info = [notification userInfo];
    NSRunningApplication *app = info[NSWorkspaceApplicationKey];
    if (!app) { return; }
    NSString *bundlePath = app.bundleURL.path ?: @"";
    NSString *name = app.localizedName ?: bundlePath.lastPathComponent ?: @"";

    launcher::ui::ResultItem item([name UTF8String],
                                               [bundlePath UTF8String],
                                               "", /* iconPath */
                                               1.0,
                                               "app");
    if (_manager) {
        _manager->recordLaunch(item);
    }
    DBM(@"Recorded system launch for %@", name);
}

- (void)fileOpened:(NSNotification *)notification {
    NSDictionary *info = [notification userInfo];
    NSString *filepath = info[(id)NSWorkspaceFilename];
    if (!filepath) { return; }

    NSString *name = filepath.lastPathComponent;

    launcher::ui::ResultItem item([name UTF8String],
                                               [filepath UTF8String],
                                               "",
                                               1.0,
                                               "file");

    if (_manager) {
        _manager->recordLaunch(item);
    }
    DBM(@"Recorded document open for %@", filepath);
}
@end

// -------------------- Spotlight Bootstrapper ----------------------------

@interface SpotlightBootstrapper : NSObject
@property(nonatomic, assign) launcher::core::MacHistoryBackend *backend;
@property(nonatomic, strong) NSMetadataQuery *query;
- (instancetype)initWithBackend:(launcher::core::MacHistoryBackend *)backend;
- (void)startQuery;
@end

@implementation SpotlightBootstrapper

- (instancetype)initWithBackend:(launcher::core::MacHistoryBackend *)backend {
    self = [super init];
    if (self) {
        _backend = backend;
    }
    return self;
}

- (void)startQuery {
    if (self.query) { return; } // already running

    self.query = [[NSMetadataQuery alloc] init];
    if (!self.query) { return; }

    // Fetch recently used application bundles only, ordered by last used date.
    // Predicate logic: (1) item is an application bundle (UTI) AND (2) it has a last-used timestamp.
    NSDate *distantPast = [NSDate dateWithTimeIntervalSince1970:0];
    NSPredicate *pred = [NSPredicate predicateWithFormat:
        @"%K == 'com.apple.application-bundle' && %K > %@",
        (NSString *)kMDItemContentTypeTree,
        (NSString *)kMDItemLastUsedDate,
        distantPast];
    self.query.predicate = pred;
    self.query.sortDescriptors = @[ [NSSortDescriptor sortDescriptorWithKey:(NSString *)kMDItemLastUsedDate
                                                                   ascending:NO] ];

    NSNotificationCenter *nc = [NSNotificationCenter defaultCenter];
    [nc addObserver:self
           selector:@selector(queryFinished:)
               name:NSMetadataQueryDidFinishGatheringNotification
             object:self.query];

    [self.query startQuery];
}

- (void)queryFinished:(NSNotification *)notification {
    [self.query disableUpdates];

    // Import all Spotlight results (no cap)
    NSUInteger importCount = self.query.resultCount;

    DBM(@"Spotlight resultCount=%lu, importing all (%lu) items", (unsigned long)self.query.resultCount,
             (unsigned long)importCount);

    launcher::core::HistoryManager* mgr = self.backend ? self.backend->manager() : nullptr;

    for (NSUInteger i = 0; i < importCount; ++i) {
        NSMetadataItem *mdItem = self.query.results[i];
        NSString *path = [mdItem valueForAttribute:(NSString *)kMDItemPath];
        if (!path) { continue; }

        NSString *name = [mdItem valueForAttribute:(NSString *)kMDItemDisplayName];
        if (!name) { name = path.lastPathComponent; }

        launcher::ui::ResultItem ri([name UTF8String],
                                                 [path UTF8String],
                                                 "",
                                                 1.0,
                                                 "app");

        // Fetch Spotlight usage statistics for more accurate history import.
        std::string pathStr = [path UTF8String] ? [path UTF8String] : "";
        auto stats = ranking::getSpotlightStats(pathStr);

        std::time_t now_ts = std::time(nullptr);
        std::time_t last_open = now_ts;
        if (stats.seconds_since_last_use >= 0) {
            double ago = stats.seconds_since_last_use;
            // Protect against pathological values > now.
            if (ago < static_cast<double>(INT64_MAX)) {
                last_open = now_ts - static_cast<std::time_t>(ago);
            }
        }

        uint32_t opens = stats.use_count >= 0 ? static_cast<uint32_t>(stats.use_count) : 1u;

        if (mgr) {
            mgr->recordBootstrap(ri, last_open, opens);
        }
    }

    DBM(@"Spotlight bootstrap added %lu items", (unsigned long)importCount);

    // Clean-up
    NSNotificationCenter *nc = [NSNotificationCenter defaultCenter];
    [nc removeObserver:self
                  name:NSMetadataQueryDidFinishGatheringNotification
                object:self.query];

    [self.query stopQuery];
    self.query = nil;

    // Release self from backend
    if (self.backend) {
        self.backend->releaseSpotlightBootstrapper();
    }
}
@end

namespace launcher {
namespace core {

class MacHistoryBackend::Impl {
 public:
    __strong MacHistoryObserver *observer;
    dispatch_source_t refreshTimer{nullptr};
    dispatch_source_t safariTimer{nullptr};
    std::string lastSafariURL;
    __strong SpotlightBootstrapper *spotlightBootstrapper{nil};
};

MacHistoryBackend::MacHistoryBackend(HistoryManager* owner, launcher::ui::IBrowserHistoryImporter* importer)
    : pImpl(new Impl()), owner_(owner), importer_(importer) {}

MacHistoryBackend::~MacHistoryBackend() {
    delete pImpl;
}

void MacHistoryBackend::startSystemObservers() {
    pImpl->observer = [[MacHistoryObserver alloc] initWithManager:owner_];

    // Perform one-time bootstrap from recent applications if the history file is empty.
    populateInitialHistoryIfNeeded();

    // startPeriodicBrowserRefresh();
    // startSafariLiveCapture();
}

void MacHistoryBackend::stopSystemObservers() {
    pImpl->observer = nil;
    stopPeriodicBrowserRefresh();
    stopSafariLiveCapture();
}

void MacHistoryBackend::populateInitialHistoryIfNeeded() {
    if (!owner_) return;
    launcher::core::HistoryManager* mgr = owner_;
    if (!owner_->getTopItems(1).empty()) {
        return; // Already have history – skip bootstrap.
    }

    DBM(@"Bootstrapping history from RecentApplications and Documents list");

    if (!pImpl->spotlightBootstrapper) {
        pImpl->spotlightBootstrapper = [[SpotlightBootstrapper alloc] initWithBackend:this];
        [pImpl->spotlightBootstrapper startQuery];
    }

    LSSharedFileListRef recentList = LSSharedFileListCreate(NULL, kLSSharedFileListRecentApplicationItems, NULL);
    if (!recentList) { return; }

    UInt32 seed = 0;
    CFArrayRef items = LSSharedFileListCopySnapshot(recentList, &seed);
    if (!items) {
        CFRelease(recentList);
        return;
    }

    CFIndex count = CFArrayGetCount(items);
    for (CFIndex i = 0; i < count; ++i) {
        LSSharedFileListItemRef item = (LSSharedFileListItemRef)CFArrayGetValueAtIndex(items, i);
        if (!item) continue;

        CFURLRef urlRef = nullptr;
        if (LSSharedFileListItemResolve(item, 0, &urlRef, nullptr) != noErr || !urlRef) {
            continue;
        }

        NSURL *url = (__bridge_transfer NSURL *)urlRef; // ARC takes ownership
        if (!url) continue;

        NSString *path = url.path;
        if (!path) continue;

        NSString *name = [[NSFileManager defaultManager] displayNameAtPath:path];
        if (!name) name = path.lastPathComponent;

        launcher::ui::ResultItem ri([name UTF8String],
                                                 [path UTF8String],
                                                 "",
                                                 1.0,
                                                 "app");

        // Fetch Spotlight usage statistics for more accurate history import.
        std::string pathStr = [path UTF8String] ? [path UTF8String] : "";
        auto stats = ranking::getSpotlightStats(pathStr);

        std::time_t now_ts = std::time(nullptr);
        std::time_t last_open = now_ts;
        if (stats.seconds_since_last_use >= 0) {
            double ago = stats.seconds_since_last_use;
            // Protect against pathological values > now.
            if (ago < static_cast<double>(INT64_MAX)) {
                last_open = now_ts - static_cast<std::time_t>(ago);
            }
        }

        uint32_t opens = stats.use_count >= 0 ? static_cast<uint32_t>(stats.use_count) : 1u;

        if (mgr) {
            mgr->recordBootstrap(ri, last_open, opens);
        }
    }

    CFRelease(items);
    CFRelease(recentList);

    return;

    // ---- Recent Documents --------------------------------------------------
    LSSharedFileListRef docList = LSSharedFileListCreate(NULL, kLSSharedFileListRecentDocumentItems, NULL);
    if (docList) {
        CFArrayRef docItems = LSSharedFileListCopySnapshot(docList, &seed);
        if (docItems) {
            CFIndex docCount = CFArrayGetCount(docItems);
            for (CFIndex i = 0; i < docCount; ++i) {
                LSSharedFileListItemRef item = (LSSharedFileListItemRef)CFArrayGetValueAtIndex(docItems, i);
                if (!item) continue;

                CFURLRef urlRef = nullptr;
                if (LSSharedFileListItemResolve(item, 0, &urlRef, nullptr) != noErr || !urlRef) {
                    continue;
                }
                NSURL *url = (__bridge_transfer NSURL *)urlRef;
                if (!url) continue;
                NSString *path = url.path;
                if (!path) continue;

                NSString *name = path.lastPathComponent ?: path;

                launcher::ui::ResultItem ri([name UTF8String],
                                                         [path UTF8String],
                                                         "",
                                                         1.0,
                                                         "file");

                // Fetch Spotlight usage statistics to seed document history with realistic
                // last-opened timestamp and open-count, mirroring the logic used for
                // recent applications above.
                std::string pathStr = [path UTF8String] ? [path UTF8String] : "";
                auto stats = ranking::getSpotlightStats(pathStr);

                std::time_t now_ts = std::time(nullptr);
                std::time_t last_open = now_ts;
                if (stats.seconds_since_last_use >= 0) {
                    double ago = stats.seconds_since_last_use;
                    if (ago < static_cast<double>(INT64_MAX)) {
                        last_open = now_ts - static_cast<std::time_t>(ago);
                    }
                }

                uint32_t opens = stats.use_count >= 0 ? static_cast<uint32_t>(stats.use_count) : 1u;

                if (mgr) {
                    mgr->recordBootstrap(ri, last_open, opens);
                }
            }
            CFRelease(docItems);
        }
        CFRelease(docList);
    }
}

// Interval for refresh in seconds
static const uint64_t kBrowserRefreshIntervalSec = 300; // 5 minutes

void MacHistoryBackend::startPeriodicBrowserRefresh() {
    if (pImpl->refreshTimer) return; // already running

    dispatch_queue_t q = dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_BACKGROUND, 0);
    pImpl->refreshTimer = dispatch_source_create(DISPATCH_SOURCE_TYPE_TIMER, 0, 0, q);
    if (!pImpl->refreshTimer) return;

    dispatch_source_set_timer(pImpl->refreshTimer,
                              dispatch_time(DISPATCH_TIME_NOW, kBrowserRefreshIntervalSec * NSEC_PER_SEC),
                              kBrowserRefreshIntervalSec * NSEC_PER_SEC,
                              10 * NSEC_PER_SEC); // leeway

    __weak MacHistoryBackend *weakSelf = this;
    dispatch_source_set_event_handler(pImpl->refreshTimer, ^{
        MacHistoryBackend *strongSelf = weakSelf;
        if (!strongSelf) return;

        if (!strongSelf->importer_) { return; }
        int newCount = strongSelf->importer_->continueImportAllBrowserHistory();
        if (newCount > 0) {
            auto *mgr = strongSelf ? strongSelf->manager() : nullptr;
            auto latest = strongSelf->importer_->getTopEntries(20);
            for (const auto &site : latest) {
                launcher::ui::ResultItem item(site.title.empty() ? site.domain : site.title,
                                                           site.url,
                                                           "",
                                                           1.0,
                                                           "website");
                if (mgr) { mgr->recordLaunch(item); }
            }
        }
    });

    dispatch_resume(pImpl->refreshTimer);
}

void MacHistoryBackend::stopPeriodicBrowserRefresh() {
    if (pImpl->refreshTimer) {
        dispatch_source_cancel(pImpl->refreshTimer);
        pImpl->refreshTimer = nullptr;
    }
}

static const uint64_t kSafariPollIntervalSec = 1; // 1 Hz

void MacHistoryBackend::startSafariLiveCapture() {
#if __has_include(<ScriptingBridge/ScriptingBridge.h>)
    if (pImpl->safariTimer) return;

    dispatch_queue_t q = dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_BACKGROUND, 0);
    pImpl->safariTimer = dispatch_source_create(DISPATCH_SOURCE_TYPE_TIMER, 0, 0, q);
    if (!pImpl->safariTimer) return;

    dispatch_source_set_timer(pImpl->safariTimer,
                              dispatch_time(DISPATCH_TIME_NOW, kSafariPollIntervalSec * NSEC_PER_SEC),
                              kSafariPollIntervalSec * NSEC_PER_SEC,
                              0.2 * NSEC_PER_SEC);

    __weak MacHistoryBackend *weakSelf = this;
    dispatch_source_set_event_handler(pImpl->safariTimer, ^{
        MacHistoryBackend *self = weakSelf;
        if (!self) return;

        SBApplication *safari = [SBApplication applicationWithBundleIdentifier:@"com.apple.Safari"];
        if (![safari isRunning]) { return; }

        @try {
            id frontWin = [[safari valueForKey:@"windows"] firstObject];
            id currentTab = [frontWin valueForKey:@"currentTab"];
            NSString *urlStr = [currentTab valueForKey:@"URL"];
            if (!urlStr || urlStr.length == 0) { return; }

            std::string url = [urlStr UTF8String];
            if (url == self->pImpl->lastSafariURL) { return; }
            self->pImpl->lastSafariURL = url;

            NSString *titleStr = [currentTab valueForKey:@"name"];
            std::string title = titleStr ? [titleStr UTF8String] : url;

            launcher::ui::ResultItem item(title,
                                                       url,
                                                       "",
                                                       1.0,
                                                       "website");
            if (self->manager()) {
                self->manager()->recordLaunch(item);
            }
            DBM(@"Captured live Safari tab: %@", urlStr);
        } @catch (NSException *ex) {
            // Likely missing Apple Events permission; no-op.
        }
    });

    dispatch_resume(pImpl->safariTimer);
#endif
}

void MacHistoryBackend::stopSafariLiveCapture() {
    if (pImpl->safariTimer) {
        dispatch_source_cancel(pImpl->safariTimer);
        pImpl->safariTimer = nullptr;
    }
}

// Helper called by SpotlightBootstrapper when query completes
void MacHistoryBackend::releaseSpotlightBootstrapper() {
    pImpl->spotlightBootstrapper = nil;
}

}  // namespace core
}  // namespace launcher

#endif  // __APPLE__ 