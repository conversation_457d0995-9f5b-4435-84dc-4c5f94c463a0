#include "history_manager.h"
#include "../../ui/common/result_item.h"
#include "../util/debug.h"
#include "../util/expected.h"
#include "../util/result.h"
#include "../interfaces/iconfig_manager.h"

#include <algorithm>
#include <cmath>
#include <iomanip>
#include <filesystem>
#include <fstream>
#include <nlohmann/json.hpp>

#ifdef __APPLE__
#include "mac_history_backend.h"
#endif

namespace launcher {
namespace core {

namespace {
constexpr uint32_t kMaxStoredEntries = 1000;
constexpr double   kRecencyDecayDays = 7.0;  // Exp. half-life ~1 week

using launcher::core::util::Expected;
using json = nlohmann::json;

Expected<void,std::string> loadHistoryFile(const std::string& path,
                                           std::unordered_map<std::string, HistoryManager::Entry>& entries,
                                           std::unordered_map<std::string,std::string>& id_lookup) {
    if (!std::filesystem::exists(path)) { return Expected<void,std::string>::success(); }
    std::ifstream f(path);
    if (!f.is_open()) {
        return Expected<void,std::string>::failure("cannot open file");
    }
    try {
        json j; f >> j;
        for (const auto& el : j) {
            launcher::ui::ResultItem item(
                el.value("name", ""), el.value("path", ""), el.value("iconPath", ""), 0.0,
                el.value("type", "app"), el.value("identifier", ""));
            HistoryManager::Entry e{item, el.value("last_open", std::time_t(0)), el.value("opens", 0u)};
            std::string key = !item.identifier.empty() ? item.identifier : item.name + "-" + item.path;
            entries.emplace(key, std::move(e));
            if (!item.identifier.empty()) { id_lookup[item.identifier] = key; }
        }
    } catch (const std::exception& ex) {
        return Expected<void,std::string>::failure(ex.what());
    }
    return Expected<void,std::string>::success();
}
}

HistoryManager::HistoryManager(IConfigManager& cfg, launcher::ui::IBrowserHistoryImporter* importer)
    : cfg_(cfg), importer_(importer) {
    // Determine history path next to config.json
    std::filesystem::path cfgPath = cfg_.getConfigPath();
    history_path_ = (cfgPath.empty() ? "history.json"
                                     : (std::filesystem::path(cfgPath).parent_path() / "history.json").string());
    load();

#ifdef __APPLE__
    backend_ = std::make_unique<MacHistoryBackend>(this, importer_);
    backend_->startSystemObservers();
#endif
    DBG("History file: " << history_path_);
}

HistoryManager::~HistoryManager() {
#ifdef __APPLE__
    if (backend_) backend_->stopSystemObservers();
#endif
    save();
}

void HistoryManager::recordLaunch(const launcher::ui::ResultItem& item) {
    if (!enabled_) { return; }
    std::lock_guard<std::mutex> lock(mutex_);

    const std::string fallback_key = item.name + "-" + item.path;
    const bool has_id             = !item.identifier.empty();
    const std::string canonical_key = has_id ? item.identifier : fallback_key;

    // If the item just gained an identifier, merge & remove any stale fallback entry.
    if (has_id) {
        // Track mapping for future quick lookup.
        id_lookup_[item.identifier] = canonical_key;

        auto it_fallback = entries_.find(fallback_key);
        if (it_fallback != entries_.end() && fallback_key != canonical_key) {
            // Merge statistics into canonical (which may or may not already exist).
            Entry fallback_entry = it_fallback->second;
            entries_.erase(it_fallback);

            auto it_canon = entries_.find(canonical_key);
            if (it_canon == entries_.end()) {
                // Promote fallback entry under canonical key.
                entries_.emplace(canonical_key, std::move(fallback_entry));
                it_canon = entries_.find(canonical_key);
            }

            // Merge statistics from fallback into canonical.
            it_canon->second.last_open = std::max(it_canon->second.last_open, fallback_entry.last_open);
            it_canon->second.opens     = std::max(it_canon->second.opens, fallback_entry.opens);

            // item's details will be refreshed later in the main insert/update logic.
        }
    }

    auto it = entries_.find(canonical_key);
    std::time_t now = std::time(nullptr);
    if (it == entries_.end()) {
        Entry e{item, now, 1};
        entries_.emplace(canonical_key, std::move(e));
    } else {
        it->second.last_open = now;
        it->second.opens += 1;
        it->second.item = item; // refresh details
    }

    // Prune if over capacity
    if (entries_.size() > kMaxStoredEntries) {
        auto oldest = std::min_element(entries_.begin(), entries_.end(),
            [](const auto& a, const auto& b) { return a.second.last_open < b.second.last_open; });
        if (oldest != entries_.end()) { entries_.erase(oldest); }
    }

    save();
}

void HistoryManager::recordBootstrap(const launcher::ui::ResultItem& item,
                                     std::time_t last_open,
                                     uint32_t opens) {
    if (!enabled_) { return; }
    std::lock_guard<std::mutex> lock(mutex_);

    const std::string fallback_key = item.name + "-" + item.path;
    const bool has_id             = !item.identifier.empty();
    const std::string canonical_key = has_id ? item.identifier : fallback_key;

    // Dedup similar to recordLaunch
    if (has_id) {
        id_lookup_[item.identifier] = canonical_key;
        auto it_fallback = entries_.find(fallback_key);
        if (it_fallback != entries_.end() && fallback_key != canonical_key) {
            Entry fallback_entry = it_fallback->second;
            entries_.erase(it_fallback);
            auto it_canon = entries_.find(canonical_key);
            if (it_canon == entries_.end()) {
                entries_.emplace(canonical_key, std::move(fallback_entry));
                it_canon = entries_.find(canonical_key);
            }
            // Merge statistics from fallback into canonical.
            it_canon->second.last_open = std::max(it_canon->second.last_open, fallback_entry.last_open);
            it_canon->second.opens     = std::max(it_canon->second.opens, fallback_entry.opens);
            // later update section will handle remaining fields.
        }
    }

    auto it = entries_.find(canonical_key);
    if (it == entries_.end()) {
        Entry e{item, last_open, opens == 0 ? 1u : opens};
        entries_.emplace(canonical_key, std::move(e));
    } else {
        it->second.last_open = std::max(it->second.last_open, last_open);
        it->second.opens     = std::max(it->second.opens, opens == 0 ? 1u : opens);
        it->second.item      = item;
    }

    if (entries_.size() > kMaxStoredEntries) {
        auto oldest = std::min_element(entries_.begin(), entries_.end(),
            [](const auto& a, const auto& b) { return a.second.last_open < b.second.last_open; });
        if (oldest != entries_.end()) { entries_.erase(oldest); }
    }

    save();
}

std::vector<launcher::ui::ResultItem> HistoryManager::getTopItems(std::size_t max_items) {
    std::lock_guard<std::mutex> lock(mutex_);
    if (!enabled_) { return {}; }

    // Option A: strict recency ordering – newest launches first.
    std::vector<Entry*> sorted;
    sorted.reserve(entries_.size());
    for (auto& [key, entry] : entries_) {
        sorted.push_back(&entry);
    }

    // Sort by last_open descending (most recent at index 0).
    std::sort(sorted.begin(), sorted.end(), [](const Entry* a, const Entry* b) {
        return a->last_open > b->last_open;
    });

    std::vector<launcher::ui::ResultItem> out;
    for (std::size_t i = 0; i < sorted.size() && i < max_items; ++i) {
        const Entry* entry = sorted[i];
        launcher::ui::ResultItem item = entry->item;
        // Use epoch seconds as the score so existing UI can still display something meaningful.
        item.score = static_cast<double>(entry->last_open);
        out.emplace_back(std::move(item));
    }
    return out;
}

double HistoryManager::computeScore(const Entry& entry, std::time_t now) const {
    const double frequencyWeight = 0.3;
    const double recencyWeight   = 0.7;

    double days_since = double(now - entry.last_open) / 86400.0;
    double recency    = std::exp(-days_since / kRecencyDecayDays);
    double freq       = std::log(double(entry.opens) + 1.0);
    return recencyWeight * recency + frequencyWeight * freq;
}

util::Result<void> HistoryManager::load() {
    DBG("Loading history from " << history_path_);
    std::lock_guard<std::mutex> lock(mutex_);
    entries_.clear();
    id_lookup_.clear();
    auto res = loadHistoryFile(history_path_, entries_, id_lookup_);
    if (!res) {
        ERR("Failed to load history: " << res.error());
        return util::Result<void>::failure(res.error());
    }
    return util::Result<void>::success();
}

util::Result<void> HistoryManager::save() {
    DBG("Saving history to " << history_path_);
    if (!enabled_) { return util::Result<void>::success(); }
    try {
        std::filesystem::create_directories(std::filesystem::path(history_path_).parent_path());
        json j = json::array();
        for (const auto& [key, entry] : entries_) {
            j.push_back({
                {"name", entry.item.name},
                {"path", entry.item.path},
                {"iconPath", entry.item.iconPath},
                {"type", entry.item.type},
                {"last_open", entry.last_open},
                {"opens", entry.opens},
                {"identifier", entry.item.identifier}
            });
        }
        std::ofstream f(history_path_);
        if (f.is_open()) {
            f << std::setw(2) << j << std::endl;
        }
    } catch (const std::exception& ex) {
        ERR("Failed to save history: " << ex.what());
        return util::Result<void>::failure(ex.what());
    }
    return util::Result<void>::success();
}

void HistoryManager::setEnabled(bool enabled) {
    enabled_ = enabled;
    if (enabled_) {
        load();
    }
}

void HistoryManager::clearAll() {
    {
        std::lock_guard<std::mutex> lock(mutex_);
        entries_.clear();
    }
    try {
        std::filesystem::remove(history_path_);
    } catch (...) {
        // ignore errors
    }
}

}  // namespace core
}  // namespace launcher 