#pragma once

#ifdef __APPLE__

namespace launcher {
namespace ui { class IBrowserHistoryImporter; }
namespace core {

class HistoryManager; // forward declaration

class Mac<PERSON>istoryBackend {
 public:
    MacHistoryBackend(HistoryManager* owner, launcher::ui::IBrowserHistoryImporter* importer);
    ~MacHistoryBackend();

    void startSystemObservers();
    void stopSystemObservers();

    // Populate history with recent applications via Spotlight/LSSharedFileList if file is empty.
    void populateInitialHistoryIfNeeded();

    // Safari live tab polling
    void startSafariLiveCapture();
    void stopSafariLiveCapture();

    // Internal: called once Spotlight bootstrap completes to release resources
    void releaseSpotlightBootstrapper();

    // Return owning HistoryManager pointer (non-owning)
    HistoryManager* manager() const { return owner_; }

 private:
    class Impl;
    Impl* pImpl;

    HistoryManager* owner_; // non-owning back-reference
    launcher::ui::IBrowserHistoryImporter* importer_; // non-owning pointer to browser history importer

    void startPeriodicBrowserRefresh();
    void stopPeriodicBrowserRefresh();
};

}  // namespace core
}  // namespace launcher

#endif  // __APPLE__ 