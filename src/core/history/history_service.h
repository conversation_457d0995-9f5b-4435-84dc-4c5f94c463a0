#pragma once

#include "../foundation/service_base.h"
#include "../util/expected.h"

namespace launcher::core::history {

class HistoryService final : public foundation::ServiceBase<HistoryService> {
 public:
    static constexpr foundation::ServiceId kId = foundation::ServiceId::kHistoryService;

    explicit HistoryService(foundation::ServiceRegistry& registry)
        : foundation::ServiceBase<HistoryService>(registry) {}

    util::Result<void> start() override { return util::Result<void>::success(); }
    void             stop() noexcept override {}
};

} // namespace launcher::core::history 