/*
 * @file history_manager_interface.h
 * @brief Abstract interface for HistoryManager implementations.
 */

#pragma once

#include <ctime>
#include <memory>
#include <string>
#include <vector>

#include "../../ui/common/result_item.h"

namespace launcher {
namespace core {

/**
 * Pure-virtual interface that captures the public contract of a history-storage
 * component.  Enables dependency-injection and facilitates unit-testing with
 * mock implementations while keeping existing singleton-based HistoryManager
 * as the default concrete provider.
 */
class IHistoryManager {
 public:
    virtual ~IHistoryManager() = default;

    // Disable copy/move for interface pointers.
    IHistoryManager(const IHistoryManager&) = delete;
    IHistoryManager& operator=(const IHistoryManager&) = delete;

    // Provide protected default constructor so derived classes can
    // initialise the interface even though we declared copy-ctor,
    // which suppresses the implicit default constructor.
    IHistoryManager() = default;

    /** Record that an item has been launched just now. */
    virtual void recordLaunch(const launcher::ui::ResultItem& item) = 0;

    /** Import a bootstrap record with explicit timestamp/opens count. */
    virtual void recordBootstrap(const launcher::ui::ResultItem& item,
                                 std::time_t                    last_open,
                                 uint32_t                       opens) = 0;

    /** Retrieve at most max_items most relevant history entries. */
    virtual std::vector<launcher::ui::ResultItem> getTopItems(std::size_t max_items) = 0;

    /** Enable/disable collection. */
    virtual void setEnabled(bool enabled) = 0;
    virtual bool isEnabled() const = 0;

    /** Remove all entries permanently. */
    virtual void clearAll() = 0;
};

using IHistoryManagerPtr = std::shared_ptr<IHistoryManager>;

} // namespace core
} // namespace launcher 