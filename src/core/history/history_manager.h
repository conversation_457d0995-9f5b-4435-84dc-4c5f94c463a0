#pragma once

#include <mutex>
#include <string>
#include <unordered_map>
#include <vector>
#include <memory>
#include <ctime>

#include "../../ui/common/result_item.h"
#include "history_manager_interface.h"
#include "../interfaces/iconfig_manager.h"
// include for browser importer interface
namespace launcher { namespace ui { class IBrowserHistoryImporter; } }
#include "../util/result.h"

namespace launcher {
namespace core {

class MacHistoryBackend; // Forward declaration for macOS specific backend

/**
 * @brief Persistent history of recently launched items.
 *
 * Thread-safe singleton storing recency+frequency statistics.  Used by the
 * LauncherBar to display most-recent items when the query is empty.
 */
class HistoryManager : public IHistoryManager {
 public:
    struct Entry {  // moved to public for Expected refactor helper
        launcher::ui::ResultItem item;
        std::time_t               last_open;
        uint32_t                  opens;
    };

    HistoryManager(IConfigManager& cfg, launcher::ui::IBrowserHistoryImporter* importer);
    ~HistoryManager();

    /** Record that a @p item has been opened at the current time. */
    void recordLaunch(const launcher::ui::ResultItem& item);

    /** Record a historical entry imported from Spotlight or other bootstrap sources. */
    void recordBootstrap(const launcher::ui::ResultItem& item,
                         std::time_t last_open,
                         uint32_t opens);

    /**
     * Return at most @p max_items of the most relevant history entries ordered
     * by score (recency+frequency).  Thread-safe.  Returned ResultItems have
     * their `score` field already populated with the computed ranking.
     */
    std::vector<launcher::ui::ResultItem> getTopItems(std::size_t max_items);

    // Enable or disable history collection/display at runtime.
    void setEnabled(bool enabled);
    bool isEnabled() const { return enabled_; }

    // Clear all stored history and delete on-disk file.
    void clearAll();

    // Delete copy/move
    HistoryManager(const HistoryManager&) = delete;
    HistoryManager& operator=(const HistoryManager&) = delete;

 private:
    util::Result<void> load();
    util::Result<void> save();

    double computeScore(const Entry& entry, std::time_t now) const;

    std::mutex                                         mutex_;
    std::unordered_map<std::string, Entry>             entries_;  // keyed by identifier or fallback (name+path)

    // Maps stable identifier → key stored in `entries_`.  Allows O(1) detection of
    // earlier fallback entries when the same item is later emitted with a proper
    // identifier so we can merge and erase duplicates quickly.
    std::unordered_map<std::string, std::string>       id_lookup_;

#ifdef __APPLE__
    std::unique_ptr<MacHistoryBackend> backend_;
#endif

    std::string history_path_;

    bool enabled_{true};

    IConfigManager& cfg_;
    launcher::ui::IBrowserHistoryImporter* importer_; // non-owning
};

}  // namespace core
}  // namespace launcher 