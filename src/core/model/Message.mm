#import "Message.h"
#include "core/util/debug.h"
#import "../renderer/MarkdownHTMLConverter.h"
#import "../renderer/RenderCache.h"
#import <AppKit/AppKit.h>

// Define padding constant to match MessageView's content padding
static const CGFloat kContentPadding = 10.0;

@implementation Message {
    NSString* _contentHash;
    NSAttributedString* _processedContent;
    NSArray<NSImage*>* _extractedImages;
    NSMutableDictionary<NSNumber*, NSNumber*>* _cachedHeightForWidth; // Cache heights for specific widths
}

- (instancetype)initWithContent:(NSString*)content 
                       senderId:(NSString*)senderId 
                     senderName:(NSString*)senderName
                           type:(MessageType)type {
    self = [super init];
    if (self) {
        self.messageId = [[NSUUID UUID] UUIDString];
        self.rawContent = content;
        self.senderId = senderId;
        self.senderName = senderName;
        self.type = type;
        self.timestamp = [NSDate date];
        self.isRead = NO;
        self.isContentParsed = NO;
        self.cachedHeight = 0;
        self.embeddedImages = @[];
        
        // Initialize height cache dictionary
        _cachedHeightForWidth = [NSMutableDictionary dictionary];
        
        // Generate content hash for caching
        _contentHash = [RenderCache hashForContent:content];
    }
    return self;
}

- (instancetype)initWithId:(NSString *)messageId 
                  senderId:(NSString *)senderId 
                senderName:(NSString *)senderName 
                      type:(MessageType)type 
                   content:(NSString *)content {
    self = [super init];
    if (self) {
        self.messageId = messageId;
        self.rawContent = content;
        self.senderId = senderId;
        self.senderName = senderName;
        self.type = type;
        self.timestamp = [NSDate date];
        self.isRead = NO;
        self.isContentParsed = NO;
        self.cachedHeight = 0;
        self.embeddedImages = @[];
        
        // Initialize height cache dictionary
        _cachedHeightForWidth = [NSMutableDictionary dictionary];
        
        // Generate content hash for caching
        _contentHash = [RenderCache hashForContent:content];
    }
    return self;
}

- (void)parseContent {
    if (self.isContentParsed) {
        // Already parsed, log and return
        DBM(@"Using cached content (already parsed)");
        return;
    }
    
    // DBM(@"Starting content parsing");

    @autoreleasepool {
        // First check if we have a cached version
        RenderCache* cache = [RenderCache sharedCache];
        CGFloat estimatedWidth = 600.0; // A reasonable default, will be adjusted in heightForWidth:
        
        NSAttributedString* cachedContent = [cache attributedStringForHash:_contentHash 
                                                                   atWidth:estimatedWidth];
        
        if (cachedContent) {
            DBM(@"Using cached content from RenderCache");
            self.cachedAttributedContent = cachedContent;
            self.embeddedImages = [cache imagesForHash:_contentHash];
            self.isContentParsed = YES;
            return;
        }
        
        // No cached version, process the markdown using our new HTML converter
        NSMutableArray<NSImage*>* images = [NSMutableArray array];
        NSAttributedString* processedContent = [MarkdownHTMLConverter 
                                                 convertMarkdownToAttributedString:self.rawContent
                                                 maxWidth:estimatedWidth
                                                 extractImages:images];
        

        // Cache the results
        [cache cacheAttributedString:processedContent 
                              forHash:_contentHash 
                              atWidth:estimatedWidth];
        
        [cache cacheImages:images forHash:_contentHash];

        self.cachedAttributedContent = processedContent;
        self.embeddedImages = images;
        self.isContentParsed = YES;
        // NOTE: We considered releasing rawContent here to save memory, but it is still
        // needed for potential re-layout at substantially different widths.  Instead we
        // rely on zero-copy bridging when messages are created (see MacOSChatUI) so that
        // rawContent itself does not hold a duplicate UTF-8 buffer when loaded from the
        // ConversationManager.
        // DBM(@"Finished parsing and caching content");
    }
}

- (NSAttributedString*)getAttributedContent {
    if (!self.isContentParsed) {
        [self parseContent];
    }
    return self.cachedAttributedContent;
}

- (CGFloat)heightForWidth:(CGFloat)width {
    // Round width to nearest point to avoid minor width differences causing recalculations
    width = round(width);
    
    // Fast path: During streaming, always recalculate for TEXT messages
    if (self.type == MessageType::TEXT && self.rawContent.length > 0) {
        // Skip cache check during active text streaming to ensure accurate height
        return [self calculateHeightForWidth:width force:YES];
    }
    
    // Regular path: Check cache first
    NSNumber *widthKey = @(width);
    NSNumber *cachedHeightNum = _cachedHeightForWidth[widthKey];
    if (cachedHeightNum) {
        return [cachedHeightNum floatValue];
    }
    
    // Otherwise, check the global cache
    RenderCache* cache = [RenderCache sharedCache];
    CGFloat cachedHeight = [cache heightForHash:_contentHash atWidth:width];
    
    // If we have a cached height, save it in our local cache and return it
    if (cachedHeight > 0) {
        _cachedHeightForWidth[widthKey] = @(cachedHeight);
        return cachedHeight;
    }
    
    // For the typing indicator, use a fixed height
    if (self.type == MessageType::TYPING_INDICATOR) {
        CGFloat fixedHeight = 32.0;
        _cachedHeightForWidth[widthKey] = @(fixedHeight);
        [cache cacheHeight:fixedHeight forHash:_contentHash atWidth:width];
        return fixedHeight;
    }
    
    // Calculate height using a helper method
    return [self calculateHeightForWidth:width force:NO];
}

// Helper method to calculate height with option to force recalculation
- (CGFloat)calculateHeightForWidth:(CGFloat)width force:(BOOL)force {
    // Skip some cache checks when forced
    if (!force) {
        // Otherwise calculate the height normally
        if (self.cachedHeight > 0 && width == self.lastCalculatedWidth) {
            _cachedHeightForWidth[@(width)] = @(self.cachedHeight);
            return self.cachedHeight;
        }
        
        // For large messages, we might need to re-process the markdown if width changes substantially
        if (self.isContentParsed && fabs(width - self.lastCalculatedWidth) > 50.0) {
            // Get a new attributed string at the correct width
            RenderCache* cache = [RenderCache sharedCache];
            NSAttributedString* cachedContent = [cache attributedStringForHash:_contentHash atWidth:width];
            
            if (!cachedContent) {
                // Clear the cache to force reprocessing with new width
                [self clearCache];
            } else {
                // Use the cached version at the new width
                self.cachedAttributedContent = cachedContent;
            }
        }
    }
    
    if (!self.isContentParsed) {
        [self parseContent];
    }
    
    // Create text container with specified width, accounting for content padding
    NSTextContainer *textContainer = [[NSTextContainer alloc] initWithContainerSize:
                                     NSMakeSize(width, CGFLOAT_MAX)];
    
    // Create layout manager
    NSLayoutManager *layoutManager = [[NSLayoutManager alloc] init];
    [layoutManager addTextContainer:textContainer];
    
    // Create text storage with attributed content
    NSTextStorage *textStorage = [[NSTextStorage alloc] initWithAttributedString:self.cachedAttributedContent];
    [textStorage addLayoutManager:layoutManager];
    
    // Calculate height
    [layoutManager glyphRangeForTextContainer:textContainer];
    NSRect usedRect = [layoutManager usedRectForTextContainer:textContainer];
    
    // Compute final height with consistent padding
    CGFloat calculatedHeight = MAX(20.0, usedRect.size.height);
    self.cachedHeight = calculatedHeight;
    self.lastCalculatedWidth = width;
    
    // Cache the height for this width (but don't cache streaming text if forced)
    if (!force) {
        _cachedHeightForWidth[@(width)] = @(calculatedHeight);
        [[RenderCache sharedCache] cacheHeight:calculatedHeight forHash:_contentHash atWidth:width];
    }
    
    return calculatedHeight;
}

- (void)clearCache {
    self.cachedAttributedContent = nil;
    self.cachedHeight = 0;
    self.isContentParsed = NO;
    self.lastCalculatedWidth = 0;
    
    // Clear width-specific height cache
    [_cachedHeightForWidth removeAllObjects];
    
    // Also clear from global cache
    if (_contentHash) {
        [[RenderCache sharedCache] clearCacheForHash:_contentHash];
    }
}

- (NSAttributedString*)processedContent {
    if (!_processedContent) {
        NSMutableArray<NSImage*>* images = [NSMutableArray array];
        NSAttributedString* processed = [MarkdownHTMLConverter 
                                       convertMarkdownToAttributedString:self.rawContent
                                       maxWidth:600.0 
                                       extractImages:images];
        _processedContent = processed;
        _extractedImages = images;
    }
    return _processedContent;
}

@end
