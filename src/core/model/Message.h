#ifndef MINICHAT_MESSAGE_H
#define MINICHAT_MESSAGE_H

#import <AppKit/AppKit.h>
#import <Foundation/Foundation.h>
#include <chrono>
#include <memory>
#include <string>
#include <vector>

// Forward declarations
@class MessageView;

/**
 * @enum MessageType
 * @brief Defines different types of messages
 */
enum class MessageType { 
    TEXT, 
    IMAGE, 
    SYSTEM, 
    TYPING_INDICATOR, // Added for streaming placeholder
    ERROR             // Added for error messages
};

/**
 * @class Message
 * @brief Represents a chat message with markdown content
 *
 * This class stores the original content and cached renderings
 * for optimal performance when displaying messages
 */
@interface Message : NSObject

// Properties
@property(nonatomic, strong) NSString *messageId;
@property(nonatomic, strong) NSString *senderId;
@property(nonatomic, strong) NSString *senderName;
@property(nonatomic, strong) NSString *rawContent;
@property(nonatomic, assign) MessageType type;
@property(nonatomic, strong) NSDate *timestamp;
@property(nonatomic, assign) BOOL isRead;

// Cached rendering (for performance)
@property(nonatomic, strong) NSAttributedString *cachedAttributedContent;
@property(nonatomic, strong) NSArray<NSImage *> *embeddedImages;
@property(nonatomic, assign) CGFloat cachedHeight;
@property(nonatomic, assign) CGFloat lastCalculatedWidth;
@property(nonatomic, assign) BOOL isContentParsed;

// Initialization
- (instancetype)initWithContent:(NSString *)content
                       senderId:(NSString *)senderId
                     senderName:(NSString *)senderName
                           type:(MessageType)type;

/**
 * @brief Initialize with a specific message ID
 * @param messageId The unique ID for this message 
 * @param senderId The unique ID of the sender 
 * @param senderName The display name of the sender
 * @param type The type of message
 * @param content The raw content of the message
 */
- (instancetype)initWithId:(NSString *)messageId
                  senderId:(NSString *)senderId
                senderName:(NSString *)senderName
                      type:(MessageType)type
                   content:(NSString *)content;

// Methods
- (void)parseContent;
- (NSAttributedString *)getAttributedContent;
- (CGFloat)heightForWidth:(CGFloat)width;
- (void)clearCache;

@end

#endif  // MINICHAT_MESSAGE_H
