#include "unified_chat_backend.h"

#include <dispatch/dispatch.h>

#include "../context/context.h"
#include "../util/debug.h"

namespace launcher {
namespace core {

// ---------------------------------------------------------------------------
// Internal helper: fetch or build a cached LlmModel
// ---------------------------------------------------------------------------
std::shared_ptr<LlmModel> UnifiedChatBackend::getOrCreateModel(const std::string& provider,
                                                                 const std::string& model,
                                                                 BackendErrorInfo& err_out) {
    const std::string key = provider + "::" + model;

    {
        std::lock_guard<std::mutex> lock(cache_mutex_);
        auto it = model_cache_.find(key);
        if (it != model_cache_.end()) { return it->second; }
    }

    // Create outside of lock (ModelRegistry could do heavy work / networking)
    auto mdl = factory_ ? std::static_pointer_cast<LlmModel>(factory_->create(provider, model)) : nullptr;
    if (!mdl) {
        err_out = {BackendErrorCode::INVALID_ARGUMENT, "Unknown provider/model"};
        return nullptr;
    }

    {
        std::lock_guard<std::mutex> lock(cache_mutex_);
        model_cache_.try_emplace(key, mdl);
    }
    return mdl;
}

// ---------------------------------------------------------------------------
// sendMessageStream – main routing logic
// ---------------------------------------------------------------------------
void UnifiedChatBackend::sendMessageStream(std::string_view message_sv, // Changed to string_view
                                           const Context& ctx,
                                           std::shared_ptr<utilities::CancellationToken> token,
                                           const std::string& provider_id,
                                           const std::string& model_id,
                                           StreamDataCallback onChunk,
                                           StreamCompletionCallback onDone) {
    BackendErrorInfo err{BackendErrorCode::NONE, ""};
    auto model = getOrCreateModel(provider_id, model_id, err);
    if (!model) {
        if (onDone) onDone(err);
        return;
    }

    // Offload async stream processing to background queue
    dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), [=, message = std::string(message_sv)]() { // Capture message_sv by value as std::string
        BackendErrorInfo finalErr{BackendErrorCode::NONE, ""};
        try {
            auto deltaStream = model->streamAsync(message, ctx, token); // Pass captured std::string

            while (true) {
                if (token && token->isCancelled()) {
                    finalErr = {BackendErrorCode::CANCELLED, "Operation cancelled"};
                    break;
                }

                auto opt = deltaStream.waitNext(token);
                if (!opt.has_value()) { break; }
                const auto &d = *opt;
                if (!d.content.empty() && onChunk) { onChunk(std::string(d.content)); }
                if (d.done) { break; }
            }

            if (auto errOpt = deltaStream.error()) {
                finalErr = {BackendErrorCode::NETWORK_ERROR, errOpt->message};
            }

        } catch (const std::exception &e) {
            ERR("Exception during async stream: " << e.what());
            finalErr = {BackendErrorCode::INTERNAL_ERROR, e.what()};
        } catch (...) {
            ERR("Unknown exception during async stream");
            finalErr = {BackendErrorCode::INTERNAL_ERROR, "Unknown backend error"};
        }
        if (onDone) { onDone(finalErr); }
    });
}

// ---------------------------------------------------------------------------
// Stubs for optional APIs – currently return NOT_IMPLEMENTED
// ---------------------------------------------------------------------------
void UnifiedChatBackend::searchMentionableItems(std::string_view query_sv, // Changed to string_view
                                               std::shared_ptr<utilities::CancellationToken> token,
                                               MentionCallback cb) {
    if (token && token->isCancelled()) {
        cb(BackendErrorInfo{BackendErrorCode::CANCELLED, "Operation cancelled"});
    } else {
        // Example: if backend needs std::string, convert here
        // std::string query_str(query_sv);
        // (void)query_str; // use query_str
        cb(BackendErrorInfo{BackendErrorCode::INVALID_ARGUMENT, "Not implemented"});
    }
}

void UnifiedChatBackend::searchHistory(std::string_view prefix_sv, // Changed to string_view
                                       std::shared_ptr<utilities::CancellationToken> token,
                                       HistoryCallback cb) {
    if (token && token->isCancelled()) {
        cb(BackendErrorInfo{BackendErrorCode::CANCELLED, "Operation cancelled"});
    } else {
        cb(BackendErrorInfo{BackendErrorCode::INVALID_ARGUMENT, "Not implemented"});
    }
}

void UnifiedChatBackend::requestLLMSuggestions(std::string_view context_sv, // Changed to string_view
                                               std::shared_ptr<utilities::CancellationToken> token,
                                               LLMSuggestionCallback cb) {
    if (token && token->isCancelled()) {
        cb(BackendErrorInfo{BackendErrorCode::CANCELLED, "Operation cancelled"});
    } else {
        cb(BackendErrorInfo{BackendErrorCode::INVALID_ARGUMENT, "Not implemented"});
    }
}

CapabilitySet UnifiedChatBackend::capabilitiesFor(const std::string &provider, const std::string &model) {
    BackendErrorInfo err{BackendErrorCode::NONE, ""};
    auto mdl = getOrCreateModel(provider, model, err);
    if (!mdl) { return CapabilitySet{}; }
    return mdl->capabilities();
}

} // namespace core
} // namespace launcher 