#include "conversation_manager.h"
#include <numeric> // For std::accumulate
#include "../util/debug.h" // For logging (optional)
#include <uuid/uuid.h>
#include <nlohmann/json.hpp>
#include "../persistence/conversation_store.h"

// Placeholder: Include tiktoken header if/when available
// #include "tiktoken_cpp.h"

namespace launcher {
namespace core {

void ConversationManager::addMessageShared(const std::string& role, const std::shared_ptr<std::string>& contentPtr) {
    ensureConversationId();
    addMessageShared(role, contentPtr, "", "");
}

void ConversationManager::addMessage(const std::string& role, const std::string& content) {
    // Forward to the shared_ptr version, moving the content if it's an rvalue.
    // If 'content' is an lvalue, it will be copied. If it's an rvalue (e.g., from a temporary),
    // it will be moved into the make_shared constructor.
    addMessageShared(role, std::make_shared<std::string>(std::move(content)));
}

void ConversationManager::addMessage(const std::string& role,
                                     const std::string& content,
                                     const std::string& provider,
                                     const std::string& model) {
    // Forward to the shared_ptr version, moving the content.
    addMessageShared(role, std::make_shared<std::string>(std::move(content)), provider, model);
}

void ConversationManager::addMessageShared(const std::string& role,
                                           const std::shared_ptr<std::string>& contentPtr,
                                           const std::string& provider,
                                           const std::string& model) {
    ensureConversationId();

    {
        std::lock_guard<std::mutex> lock(historyMutex_);
        history_.emplace_back(role, contentPtr, provider, model);
    }

    auto json = exportToJson();
    persistence::ConversationStore::instance().saveConversation(conversation_id_, json);
}

std::vector<ApiChatMessage> ConversationManager::getFormattedHistoryForContext(size_t maxTokens) const {
    std::lock_guard<std::mutex> lock(historyMutex_);

    std::vector<ApiChatMessage> fullHistory;
    size_t currentTokens = 0;

    // Add system message first if it exists
    if (!systemMessage_.empty()) {
        fullHistory.emplace_back("system", std::make_shared<std::string>(systemMessage_));
        currentTokens += estimateTokenCount(systemMessage_);
    }

    // Add regular history messages
    fullHistory.insert(fullHistory.end(), history_.begin(), history_.end());

    // Calculate total tokens (if not done partially above)
    // This recalculates if system message was added, which is slightly inefficient
    // but simpler for now. A more optimized approach would track tokens incrementally.
    currentTokens = 0;
    for(const auto& msg : fullHistory) {
        currentTokens += estimateTokenCount(msg.contentRef());
        // Optionally add tokens for role/metadata if the model requires it
    }

    // DBG("ConversationManager::getFormattedHistory", "Initial history size: %zu messages, %zu tokens. Max allowed: %zu", fullHistory.size(), currentTokens, maxTokens);

    // Truncate if necessary
    if (currentTokens > maxTokens) {
        // DBG("ConversationManager::getFormattedHistory", "Truncation needed.");
        return truncateHistory(fullHistory, maxTokens);
    } else {
        // DBG("ConversationManager::getFormattedHistory", "No truncation needed.");
        return fullHistory;
    }
}

void ConversationManager::setSystemMessage(const std::string& systemMessage) {
    std::lock_guard<std::mutex> lock(historyMutex_);
    systemMessage_ = systemMessage;
    // DBG("ConversationManager::setSystemMessage", "System message set.");
}

std::string ConversationManager::getSystemMessage() const {
    std::lock_guard<std::mutex> lock(historyMutex_);
    return systemMessage_;
}

void ConversationManager::clear() {
    std::lock_guard<std::mutex> lock(historyMutex_);
    history_.clear();
    systemMessage_.clear();
    // DBG("ConversationManager::clear", "History and system message cleared.");
}

// --- Private Helper Methods --- 

size_t ConversationManager::estimateTokenCount(const std::string& text) const {
    // Placeholder implementation: Very rough estimate (e.g., words + buffer)
    // Replace with actual tiktoken calculation when available.
    size_t wordCount = 0;
    bool inWord = false;
    for (char c : text) {
        if (std::isspace(c)) {
            if (inWord) {
                inWord = false;
            }
        } else {
            if (!inWord) {
                wordCount++;
                inWord = true;
            }
        }
    }
    // Add a buffer, assuming roughly 1.3 tokens per word on average
    return static_cast<size_t>(static_cast<double>(wordCount) * 1.3) + 5; // Add fixed buffer too
}

std::vector<ApiChatMessage> ConversationManager::truncateHistory(const std::vector<ApiChatMessage>& history, size_t maxTokens) const {
    // Assumes history is already locked or this is called from a locked context
    std::vector<ApiChatMessage> truncatedHistory = history;
    size_t currentTokens = 0;

    // Calculate initial token count
    for(const auto& msg : truncatedHistory) {
        currentTokens += estimateTokenCount(msg.contentRef());
    }

    // Determine the starting index for removal (skip system message if present)
    size_t removalStartIndex = 0;
    if (!truncatedHistory.empty() && truncatedHistory[0].role == "system") {
        removalStartIndex = 1;
    }

    // Remove messages from the beginning (after potential system message) until token limit is met
    while (currentTokens > maxTokens && truncatedHistory.size() > removalStartIndex) {
        const auto& msgToRemove = truncatedHistory[removalStartIndex];
        size_t tokensToRemove = estimateTokenCount(msgToRemove.contentRef());
        
        currentTokens -= tokensToRemove;
        truncatedHistory.erase(truncatedHistory.begin() + removalStartIndex);
        // DBG("Removed message at index " << removalStartIndex << ". Tokens remaining: " << currentTokens);
    }

    // Final check: If even after removing all non-system messages, it's still too large,
    // we might have an issue (e.g., system message alone is too big).
    if (currentTokens > maxTokens) {
        // This case is problematic. Maybe just return the system message if it exists?
        // Or return an empty history? For now, log a warning.
        ERR("Warning: History still exceeds maxTokens (" << currentTokens << " > " << maxTokens << ") even after removing all non-system messages. Returning potentially oversized history.");
        // If only the system message remains and it's too big, we might return just that.
        if (removalStartIndex == 1 && truncatedHistory.size() == 1) {
             // Return just the system message (even if too large)
        } else if (removalStartIndex == 0 && truncatedHistory.empty()) {
            // Should not happen based on loop condition, but defensively return empty
            return {};
        } else {
             // Return the current state, even if slightly over.
        }
    }

    return truncatedHistory;
}

void ConversationManager::ensureConversationId() {
    if (!conversation_id_.empty()) { return; }
    uuid_t uuid;
    uuid_generate_random(uuid);
    char buf[37];
    uuid_unparse_lower(uuid, buf);
    conversation_id_ = buf;
}

const std::string& ConversationManager::getConversationId() const {
    return conversation_id_;
}

nlohmann::json ConversationManager::exportToJson() const {
    const_cast<ConversationManager*>(this)->ensureConversationId();
    std::lock_guard<std::mutex> lock(historyMutex_);
    nlohmann::json j;
    j["conversation_id"] = conversation_id_;
    j["system_message"]  = systemMessage_;
    j["messages"]        = nlohmann::json::array();
    for (const auto &msg : history_) {
        nlohmann::json m = {{"role", msg.role}, {"content", msg.contentRef()}};
        if (!msg.provider.empty()) {
            m["provider"] = msg.provider;
        }
        if (!msg.model.empty()) {
            m["model"] = msg.model;
        }
        if (!msg.extra_meta.is_null() && !msg.extra_meta.empty()) {
            m["meta"] = msg.extra_meta;
        }
        j["messages"].push_back(std::move(m));
    }
    // Persist metadata if present
    if (!meta_.is_null() && !meta_.empty()) {
        j["meta"] = meta_;
    }
    return j;
}

void ConversationManager::loadFromJson(const nlohmann::json& obj) {
    std::lock_guard<std::mutex> lock(historyMutex_);
    history_.clear();
    systemMessage_.clear();

    // Reset meta_ first
    meta_.clear();

    if (obj.contains("conversation_id")) {
        conversation_id_ = obj["conversation_id"].get<std::string>();
    }
    if (obj.contains("system_message")) {
        systemMessage_ = obj["system_message"].get<std::string>();
    }
    if (obj.contains("messages") && obj["messages"].is_array()) {
        for (const auto &m : obj["messages"]) {
            std::string role = m.value("role", "user");
            std::string content = m.value("content", "");

            std::string provider = m.value("provider", "");
            std::string model    = m.value("model", "");

            nlohmann::json extra;
            if (m.contains("meta")) {
                extra = m["meta"];
            }

            history_.emplace_back(role, std::make_shared<std::string>(content), provider, model, extra);
        }
    }

    // Load metadata if available
    if (obj.contains("meta")) {
        meta_ = obj["meta"];
    }
}

std::vector<ApiChatMessage> ConversationManager::getHistory() const {
    std::lock_guard<std::mutex> lock(historyMutex_);
    return history_;
}

void ConversationManager::setMeta(const nlohmann::json &meta) {
    meta_ = meta;
}

nlohmann::json ConversationManager::getMeta() const {
    return meta_;
}

} // namespace core
} // namespace launcher
 