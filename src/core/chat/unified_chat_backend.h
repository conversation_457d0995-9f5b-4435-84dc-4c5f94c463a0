#pragma once

#include <memory>
#include <mutex>
#include <string>
#include <unordered_map>

#include "include/chat_backend_interface.h"
#include "llm/model_factory.h"
#include "llm/llm_model.h"
#include "llm/capability.h"

namespace launcher {
namespace core {

/**
 * @brief Backend router that dynamically dispatches messages to any registered
 *        provider/model pair at runtime.
 *
 *  – Keeps an internal cache of ILanguageModel instances so HTTP/TLS setup is
 *    amortised.  Cache is thread-safe.
 *  – All provider-agnostic helper APIs (mentions, history, suggestions) are
 *    currently stubs; they can be wired to a preferred provider later.
 */
class UnifiedChatBackend : public IChatBackend {
 public:
    explicit UnifiedChatBackend(std::shared_ptr<launcher::core::ModelFactory> factory)
        : factory_(std::move(factory)) {}
    ~UnifiedChatBackend() override = default;

    // Non-copyable
    UnifiedChatBackend(const UnifiedChatBackend&) = delete;
    UnifiedChatBackend& operator=(const UnifiedChatBackend&) = delete;

    // IChatBackend -----------------------------------------------------------
    void sendMessageStream(std::string_view message,
                           const Context& ctx,
                           std::shared_ptr<utilities::CancellationToken> token,
                           const std::string& provider_id,
                           const std::string& model_id,
                           StreamDataCallback onChunk,
                           StreamCompletionCallback onDone) override;

    void searchMentionableItems(std::string_view query,
                                std::shared_ptr<utilities::CancellationToken> token,
                                MentionCallback cb) override;

    void searchHistory(std::string_view prefix,
                       std::shared_ptr<utilities::CancellationToken> token,
                       HistoryCallback cb) override;

    void requestLLMSuggestions(std::string_view context,
                           std::shared_ptr<utilities::CancellationToken> token,
                           LLMSuggestionCallback cb) override;

    // Helper: retrieve capabilities for a given provider/model (empty set on failure)
    CapabilitySet capabilitiesFor(const std::string &provider, const std::string &model);

 private:
    std::shared_ptr<LlmModel> getOrCreateModel(const std::string& provider,
                                                     const std::string& model,
                                                     BackendErrorInfo& err_out);

    std::unordered_map<std::string, std::shared_ptr<LlmModel>> model_cache_;
    std::mutex cache_mutex_;

    std::shared_ptr<launcher::core::ModelFactory> factory_;
};

} // namespace core
} // namespace launcher 