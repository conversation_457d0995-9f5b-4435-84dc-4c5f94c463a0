#pragma once

#include <vector>
#include <string>
#include <mutex>
#include <optional>
#include <cstddef> // For size_t
#include <memory>

#include "../context/context.h" // For ApiChatMessage struct
#include "nlohmann/json.hpp"

namespace launcher {
namespace core {

/**
 * @brief Manages the state of a single conversation.
 *
 * Handles storing messages, retrieving formatted history for API context,
 * managing the system message, and ensuring thread safety for history modifications.
 * Includes token-based truncation logic.
 */
class ConversationManager {
public:
    ConversationManager() = default;

    /**
     * @brief Adds a message to the conversation history.
     *
     * @param role The role of the message sender (e.g., "user", "assistant").
     * @param content The content of the message.
     */
    void addMessage(const std::string& role, const std::string& content);

    /**
     * @brief Adds a message with explicit provider/model provenance.
     *
     * Convenience overload allowing callers to specify the LLM provider and
     * concrete model that produced the message.  Old code can keep calling the
     * 2-argument version; it internally forwards to this overload with empty
     * provider/model strings so binary compatibility is preserved.
     */
    void addMessage(const std::string& role,
                    const std::string& content,
                    const std::string& provider,
                    const std::string& model);

    /**
     * @brief Adds a message where the caller already owns the string memory.
     *        The ConversationManager will share the same allocation via
     *        std::shared_ptr, eliminating one copy.
     */
    void addMessageShared(const std::string& role, const std::shared_ptr<std::string>& contentPtr);

    /**
     * @brief Shared-ptr variant with provenance.
     */
    void addMessageShared(const std::string& role,
                          const std::shared_ptr<std::string>& contentPtr,
                          const std::string& provider,
                          const std::string& model);

    /**
     * @brief Gets the conversation history formatted for API context, truncated by token count.
     *
     * Retrieves the history, including the system message (if set), and truncates it
     * starting from the oldest messages until it fits within the specified token limit.
     * Assumes the existence of a token estimation function.
     *
     * @param maxTokens The maximum number of tokens allowed for the context (history + system message).
     * @return A vector of ChatMessage objects representing the formatted and truncated history.
     */
    std::vector<ApiChatMessage> getFormattedHistoryForContext(size_t maxTokens) const;

    /**
     * @brief Sets the system message for the conversation.
     *
     * @param systemMessage The content of the system message.
     */
    void setSystemMessage(const std::string& systemMessage);

    /**
     * @brief Gets the current system message.
     *
     * @return The system message string. Returns an empty string if not set.
     */
    std::string getSystemMessage() const;

    /**
     * @brief Clears the entire conversation history and system message.
     */
    void clear();

    // TODO(hole): Implement optional persistence methods save(path) and load(path)
    // bool save(const std::string& path);
    // bool load(const std::string& path);

    // New: Conversation identifier (stable across launches)
    const std::string& getConversationId() const;

    // Persistence helpers
    nlohmann::json exportToJson() const;
    void loadFromJson(const nlohmann::json& obj);

    // NEW: metadata helpers
    void setMeta(const nlohmann::json &meta);
    nlohmann::json getMeta() const;

    /**
     * @brief Returns a snapshot copy of the current conversation history.
     *
     * Thread-safe: obtains the internal mutex and returns a copy so callers
     * can iterate without holding the lock.
     */
    std::vector<ApiChatMessage> getHistory() const;

private:
    /**
     * @brief Estimates the token count for a given text string.
     *
     * Placeholder function. Actual implementation should use a library like tiktoken.
     * @param text The text to estimate tokens for.
     * @return Estimated number of tokens.
     */
    size_t estimateTokenCount(const std::string& text) const;

    /**
     * @brief Truncates the history to fit within the token limit.
     *
     * Removes messages from the beginning of the history (oldest first) until
     * the total token count is below the maximum allowed.
     *
     * @param history The history vector to truncate.
     * @param maxTokens The maximum number of tokens allowed.
     * @return The truncated history vector.
     */
    std::vector<ApiChatMessage> truncateHistory(const std::vector<ApiChatMessage>& history, size_t maxTokens) const;

    std::vector<ApiChatMessage> history_;
    std::string systemMessage_;
    mutable std::mutex historyMutex_; // Mutable to allow locking in const methods like getFormattedHistory
    std::string conversation_id_;
    void ensureConversationId();

    // NEW: optional metadata (icon, tint, title, etc.)
    nlohmann::json meta_ = nlohmann::json::object();
};

} // namespace core
} // namespace launcher 