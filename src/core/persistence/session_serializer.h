#pragma once

#include <filesystem>
#include <optional>
#include <string>
#include <vector>
#include <nlohmann/json.hpp>
#include "../util/result.h"

namespace launcher::core::persistence {

struct WindowSnapshot {
    std::string uuid;           // Stable id
    std::string title;
    std::string group_id;       // TabGroupManager id
    double frame_x{0}, frame_y{0}, frame_w{0}, frame_h{0};
    std::string conversation_id;
    nlohmann::json ui_state;    // Arbitrary future extensions
};

// Snapshot of a logical tab group (for full fidelity restore)
struct GroupSnapshot {
    std::string group_id;     // TabGroupManager UUID
    std::string title;        // Display title (may be empty for auto-titled groups)
    std::string tint_hex;     // Icon tint encoded as #RRGGBBAA
    bool auto_title{false};   // Whether title should be dynamic ("N Tabs")
    std::vector<WindowSnapshot> closed_tabs;
};

struct SessionMeta {
    int schema_version{2}; // incremented for GroupSnapshot support
    uint64_t timestamp{0}; // epoch seconds
    std::vector<WindowSnapshot> windows;
    std::vector<WindowSnapshot> closed_stack; // LIFO of recently closed
    std::vector<GroupSnapshot> groups;        // Metadata for each logical group
};

class SessionSerializer {
public:
    static nlohmann::json toJson(const SessionMeta &meta);
    static std::optional<SessionMeta> fromJson(const nlohmann::json &j);

    // Convenience helpers to read/write path
    static std::optional<SessionMeta> read(const std::filesystem::path &path);
    static util::Result<void> write(const SessionMeta &meta, const std::filesystem::path &path);
};

} // namespace launcher::core::persistence 