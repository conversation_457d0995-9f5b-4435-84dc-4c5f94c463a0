#include "conversation_store.h"

#include <cstdio>           // std::rename
#include <fstream>
#include <iostream>

#include "../util/debug.h"
#include "../util/result.h"

namespace launcher {
namespace core {
namespace persistence {

using namespace std::string_literals;
namespace fs = std::filesystem;

// Convenience helper to find ~/Library/Application Support/<bundle-id>/Conversations
static fs::path applicationSupportDir() {
#ifdef __APPLE__
    const char *home = getenv("HOME");
    if (!home) { return fs::temp_directory_path() / "MicroLauncher"; }
    fs::path base = fs::path(home) / "Library" / "Application Support" / "MicroLauncher";
    return base;
#else
    return fs::path(".");
#endif
}

ConversationStore &ConversationStore::instance() {
    static ConversationStore inst;
    return inst;
}

ConversationStore::ConversationStore() {
    conversationsDir_ = applicationSupportDir() / "Conversations";
    std::error_code ec;
    fs::create_directories(conversationsDir_, ec);
    if (ec) {
        ERR("Failed to create conversations directory: " << ec.message());
    }
}

fs::path ConversationStore::filePathFor(const std::string &cid) const {
    return conversationsDir_ / (cid + ".json");
}

util::Result<void> ConversationStore::saveConversation(const std::string &cid, const nlohmann::json &data) {
    std::lock_guard<std::mutex> lock(ioMutex_);

    fs::path finalPath = filePathFor(cid);
    fs::path tmpPath   = finalPath;
    tmpPath += ".tmp";

    std::ofstream ofs(tmpPath, std::ios::binary);
    if (!ofs.is_open()) {
        ERR("Failed to open temp file for conversation " << cid);
        return util::Result<void>::failure("Failed to open temp file");
    }

    ofs << data.dump(2); // pretty-print with indent 2
    ofs.close();

    std::error_code ec;
    fs::rename(tmpPath, finalPath, ec);
    if (ec) {
        ERR("Atomic rename failed: " << ec.message());
        fs::remove(tmpPath, ec);
        return util::Result<void>::failure("Atomic rename failed: " + ec.message());
    }
    return util::Result<void>::success();
}

std::optional<nlohmann::json> ConversationStore::loadConversation(const std::string &cid) {
    std::lock_guard<std::mutex> lock(ioMutex_);

    fs::path path = filePathFor(cid);
    if (!fs::exists(path)) { return std::nullopt; }

    std::ifstream ifs(path, std::ios::binary);
    if (!ifs.is_open()) { return std::nullopt; }
    try {
        nlohmann::json j;
        ifs >> j;
        return j;
    } catch (const std::exception &e) {
        ERR("JSON parse error for conversation " << cid << ": " << e.what());
        return std::nullopt;
    }
}

util::Result<void> ConversationStore::deleteConversation(const std::string &cid) {
    std::lock_guard<std::mutex> lock(ioMutex_);
    std::error_code ec;
    fs::remove(filePathFor(cid), ec);
    if (ec) {
        return util::Result<void>::failure(ec.message());
    }
    return util::Result<void>::success();
}

} // namespace persistence
} // namespace core
} // namespace launcher 