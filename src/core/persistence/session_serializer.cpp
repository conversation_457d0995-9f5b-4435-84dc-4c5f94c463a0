#include "session_serializer.h"

#include <fstream>
#include <chrono>
#include "../util/debug.h"
#include "../util/result.h"

namespace launcher::core::persistence {

using nlohmann::json;
namespace fs = std::filesystem;

static json windowToJson(const WindowSnapshot &w) {
    json j;
    j["uuid"] = w.uuid;
    j["title"] = w.title;
    j["group_id"] = w.group_id;
    j["frame"] = {w.frame_x, w.frame_y, w.frame_w, w.frame_h};
    j["conversation_id"] = w.conversation_id;
    j["ui_state"] = w.ui_state;
    return j;
}

static std::optional<WindowSnapshot> windowFromJson(const json &j) {
    if (!j.contains("uuid")) { return std::nullopt; }
    WindowSnapshot w;
    w.uuid = j.value("uuid", "");
    w.title = j.value("title", "");
    w.group_id = j.value("group_id", "");
    auto arr = j.value("frame", json::array());
    if (arr.size() == 4) {
        w.frame_x = arr[0].get<double>();
        w.frame_y = arr[1].get<double>();
        w.frame_w = arr[2].get<double>();
        w.frame_h = arr[3].get<double>();
    }
    w.conversation_id = j.value("conversation_id", "");
    w.ui_state = j.value("ui_state", json::object());
    return w;
}

static json groupToJson(const GroupSnapshot &g) {
    json j;
    j["group_id"] = g.group_id;
    j["title"] = g.title;
    j["tint_hex"] = g.tint_hex;
    j["auto_title"] = g.auto_title;
    j["closed_tabs"] = json::array();
    for (const auto &w : g.closed_tabs) {
        j["closed_tabs"].push_back(windowToJson(w));
    }
    return j;
}

static std::optional<GroupSnapshot> groupFromJson(const json &j) {
    if (!j.contains("group_id")) { return std::nullopt; }
    GroupSnapshot g;
    g.group_id = j.value("group_id", "");
    g.title = j.value("title", "");
    g.tint_hex = j.value("tint_hex", "");
    g.auto_title = j.value("auto_title", false);
    for (auto &elem : j.value("closed_tabs", json::array())) {
        if (auto w = windowFromJson(elem)) g.closed_tabs.push_back(*w);
    }
    return g;
}

json SessionSerializer::toJson(const SessionMeta &meta) {
    json j;
    j["schema_version"] = meta.schema_version;
    j["timestamp"] = meta.timestamp;
    j["windows"] = json::array();
    for (const auto &w : meta.windows) j["windows"].push_back(windowToJson(w));
    j["closed_stack"] = json::array();
    for (const auto &w : meta.closed_stack) j["closed_stack"].push_back(windowToJson(w));
    j["groups"] = json::array();
    for (const auto &g : meta.groups) j["groups"].push_back(groupToJson(g));
    return j;
}

std::optional<SessionMeta> SessionSerializer::fromJson(const json &j) {
    if (!j.is_object()) return std::nullopt;
    SessionMeta meta;
    meta.schema_version = j.value("schema_version", 1);
    meta.timestamp = j.value("timestamp", (uint64_t)0);
    for (auto &elem : j.value("windows", json::array())) {
        if (auto w = windowFromJson(elem)) meta.windows.push_back(*w);
    }
    for (auto &elem : j.value("closed_stack", json::array())) {
        if (auto w = windowFromJson(elem)) meta.closed_stack.push_back(*w);
    }
    for (auto &elem : j.value("groups", json::array())) {
        if (auto g = groupFromJson(elem)) meta.groups.push_back(*g);
    }
    return meta;
}

std::optional<SessionMeta> SessionSerializer::read(const fs::path &path) {
    if (!fs::exists(path)) return std::nullopt;
    try {
        std::ifstream ifs(path);
        json j; ifs >> j;
        return fromJson(j);
    } catch (const std::exception &e) {
        ERR("Session read error: " << e.what());
        return std::nullopt;
    }
}

util::Result<void> SessionSerializer::write(const SessionMeta &meta, const fs::path &path) {
    try {
        fs::create_directories(path.parent_path());
        fs::path tmp = path; tmp += ".tmp";
        std::ofstream ofs(tmp);
        ofs << toJson(meta).dump(2);
        ofs.close();
        std::error_code ec; fs::rename(tmp, path, ec);
        if (ec) {
            ERR("Session rename error: " << ec.message());
            return util::Result<void>::failure(ec.message());
        }
        return util::Result<void>::success();
    } catch (const std::exception &e) {
        ERR("Session write error: " << e.what());
        return util::Result<void>::failure(e.what());
    }
}

} // namespace 