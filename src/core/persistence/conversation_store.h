/*
 * conversation_store.h
 * Provides simple thread-safe persistence of conversation JSON documents to disk.
 */
#pragma once

#include <filesystem>
#include <mutex>
#include <optional>
#include <string>

#include <nlohmann/json.hpp>
#include "../util/result.h"

namespace launcher {
namespace core {
namespace persistence {

/**
 * @brief Thread-safe singleton managing on-disk storage of conversation histories.
 *
 * Conversations are stored one-file-per-conversation under the user's
 * Application Support directory.  Filenames are <uuid>.json.
 */
class ConversationStore {
 public:
    /** Returns global singleton instance */
    static ConversationStore& instance();

    /**
     * Persist the supplied JSON document to the conversation file whose name
     * is <conversation_id>.json.  Writes are atomic via a temporary file & rename.
     *
     * @return true on success, false otherwise.
     */
    util::Result<void> saveConversation(const std::string& conversation_id,
                          const nlohmann::json& data);

    /** Load and parse the JSON document for @p conversation_id. */
    std::optional<nlohmann::json> loadConversation(const std::string& conversation_id);

    /** Remove the on-disk file for a conversation (used by "Clear History"). */
    util::Result<void> deleteConversation(const std::string& conversation_id);

 private:
    ConversationStore(); // Private ctor for singleton

    std::filesystem::path conversationsDir_;
    std::mutex ioMutex_;

    /* Helper → resolve file path for a conversation id */
    std::filesystem::path filePathFor(const std::string& conversation_id) const;
};

} // namespace persistence
} // namespace core
} // namespace launcher 