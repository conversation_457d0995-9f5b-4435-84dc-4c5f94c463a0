#pragma once

// Precompiled header for the core library.
// This file aggregates headers that are used *very* frequently across the
// code-base so the compiler parses them only once when PCH support is
// enabled (CMake `target_precompile_headers`).  Only add headers that are
// ubiquitous and *stable* (rarely change); otherwise PCH rebuilds will
// negate the benefit.

// STL — container / string / concurrency -----------------------------------
#include <algorithm>
#include <array>
#include <atomic>
#include <chrono>
#include <cstddef>
#include <cstdint>
#include <functional>
#include <memory>
#include <mutex>
#include <optional>
#include <string>
#include <string_view>
#include <thread>
#include <type_traits>
#include <utility>
#include <vector>

// Third-party – lightweight, header-only ------------------------------------
#include <absl/container/flat_hash_map.h>
#include <absl/container/flat_hash_set.h>
#include <absl/strings/str_format.h>
#include <absl/strings/string_view.h>
#include <nlohmann/json.hpp>

// Project headers -----------------------------------------------------------
#include "util/debug.h" 