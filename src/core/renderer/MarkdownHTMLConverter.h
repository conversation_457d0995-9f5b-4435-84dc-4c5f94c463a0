#ifndef MINICHAT_MARKDOWN_HTML_CONVERTER_H
#define MINICHAT_MARKDOWN_HTML_CONVERTER_H

#import <AppKit/AppKit.h>
#import <Foundation/Foundation.h>

/**
 * @class MarkdownHTMLConverter
 * @brief Converts markdown to NSAttributedString via HTML
 *
 * This class uses cmark to convert markdown to HTML and then uses
 * NSAttributedString's HTML capabilities to create the final attributed string.
 */
@interface MarkdownHTMLConverter : NSObject

/**
 * @brief Convert markdown to NSAttributedString using the HTML conversion path
 * @param markdownText The markdown text to convert
 * @param maxWidth Maximum width for tables, code blocks, etc.
 * @param images Optional array to receive extracted images
 * @return An attributed string with styled markdown
 */
+ (NSAttributedString*)convertMarkdownToAttributedString:(NSString*)markdownText
                                                maxWidth:(CGFloat)maxWidth
                                           extractImages:(NSMutableArray<NSImage*>*)images;

/**
 * @brief Convert markdown to HTML using cmark
 * @param markdownText The markdown text to convert
 * @return HTML string representation of the markdown
 */
+ (NSString*)convertMarkdownToHTML:(NSString*)markdownText;

/**
 * @brief Apply syntax highlighting to code blocks in HTML
 * @param html The HTML string containing code blocks
 * @return HTML with syntax highlighted code blocks
 */
+ (NSString*)processCodeBlocksWithSyntaxHighlighting:(NSString*)html;

@end

#endif  // MINICHAT_MARKDOWN_HTML_CONVERTER_H