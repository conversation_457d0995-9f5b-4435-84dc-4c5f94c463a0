#ifndef MINICHAT_RENDER_CACHE_H
#define MINICHAT_RENDER_CACHE_H

#import <AppKit/AppKit.h>
#import <Foundation/Foundation.h>
#include <string>
#include <unordered_map>

/**
 * @class RenderCache
 * @brief A cache for rendered content to improve performance
 *
 * This class provides a singleton cache for storing rendered
 * content (NSAttributedString) for markdown messages, to avoid
 * re-rendering the same content multiple times.
 */
@interface RenderCache : NSObject

// Singleton instance
+ (instancetype)sharedCache;

// Store an attributed string in the cache
- (void)cacheAttributedString:(NSAttributedString*)attributedString
                      forHash:(NSString*)hash
                      atWidth:(CGFloat)width;

// Retrieve an attributed string from the cache
- (NSAttributedString*)attributedStringForHash:(NSString*)hash atWidth:(CGFloat)width;

// Store image list in the cache
- (void)cacheImages:(NSArray<NSImage*>*)images forHash:(NSString*)hash;

// Retrieve images from the cache
- (NSArray<NSImage*>*)imagesForHash:(NSString*)hash;

// Store cached height
- (void)cacheHeight:(CGFloat)height forHash:(NSString*)hash atWidth:(CGFloat)width;

// Retrieve cached height
- (CGFloat)heightForHash:(NSString*)hash atWidth:(CGFloat)width;

// Clear all cached data
- (void)clearCache;

// Clear cached data for a specific hash
- (void)clearCacheForHash:(NSString*)hash;

// Get a hash for content
+ (NSString*)hashForContent:(NSString*)content;

@end

#endif  // MINICHAT_RENDER_CACHE_H
