#import "MarkdownHTMLConverter.h"
#import "HighlightJSBridge.h"
#include "../core/util/debug.h" // Corrected path

#include <string_view>

// Import cmark-gfm C library and extensions
#include <cmark-gfm.h>
#include <cmark-gfm-extension_api.h>
#include "cmark-gfm-core-extensions.h"
#include <table.h>
#include <strikethrough.h>
#include <autolink.h>
#include <tagfilter.h>
#include <tasklist.h>

@implementation MarkdownHTMLConverter

+ (NSAttributedString*)convertMarkdownToAttributedString:(NSString*)markdownText
                                                maxWidth:(CGFloat)maxWidth
                                           extractImages:(NSMutableArray<NSImage*>*)images {
    // DBM("convertMarkdownToAttributedString", @"Converting markdown to attributed string via HTML");
    
    if (!markdownText || markdownText.length == 0) {
        return [[NSAttributedString alloc] initWithString:@""];
    }
    
    // Convert Markdown to HTML
    NSString* html = [self convertMarkdownToHTML:markdownText];
    if (!html || html.length == 0) {
        return [[NSAttributedString alloc] initWithString:markdownText];
    }
    
    // Process HTML to add syntax highlighting to code blocks
    html = [self processCodeBlocksWithSyntaxHighlighting:html];
    
    // Add basic styling to the HTML
    html = [self addStylesToHTML:html maxWidth:maxWidth];
    
    // Convert HTML to NSAttributedString using Apple's built-in method
    NSError* error = nil;
    NSData* htmlData = [html dataUsingEncoding:NSUTF8StringEncoding];
    
    if (!htmlData) {
        ERM(@"Failed to convert HTML to data");
        return [[NSAttributedString alloc] initWithString:markdownText];
    }
    
    NSDictionary* options = @{
        NSDocumentTypeDocumentAttribute: NSHTMLTextDocumentType,
        NSCharacterEncodingDocumentAttribute: @(NSUTF8StringEncoding)
    };
    
    NSAttributedString* attributedString = nil;
    
    @try {
        attributedString = [[NSAttributedString alloc] initWithHTML:htmlData
                                                            options:options 
                                                 documentAttributes:nil];
    } @catch (NSException* exception) {
        ERM(@"Exception converting HTML: %@", exception.reason);
        attributedString = [[NSAttributedString alloc] initWithString:markdownText];
    }
    
    if (error) {
        ERM(@"Failed to create attributed string from HTML: %@", error.localizedDescription);
        return [[NSAttributedString alloc] initWithString:markdownText];
    }
    
    // Process images if needed (this would be a more complex implementation)
    // In a real implementation, we would extract images from the HTML and add them to the images array
    
    return attributedString ?: [[NSAttributedString alloc] initWithString:markdownText];
}

+ (NSString*)convertMarkdownToHTML:(NSString*)markdownText {
    if (!markdownText || markdownText.length == 0) {
        return @"";
    }
    
    // Convert NSString to UTF-8 for cmark
    std::string_view markdown_sv = [markdownText UTF8String] ?: "";
    if (markdown_sv.empty() && markdownText.length > 0) { // Check if UTF8String failed
        ERM(@"Failed to convert markdownText to UTF-8 for cmark processing.");
        // Fallback or error handling, e.g., return the original text or an error message.
        // For now, returning the original text to avoid crashing.
        return markdownText;
    }
    
    // Register GFM extensions
    cmark_gfm_core_extensions_ensure_registered();
    
    // Create a parser object with Github Flavored Markdown options
    int options = CMARK_OPT_DEFAULT | CMARK_OPT_UNSAFE | CMARK_OPT_NORMALIZE | CMARK_OPT_SMART;
    cmark_parser* parser = cmark_parser_new(options);
    
    // Add GFM extensions to the parser
    cmark_syntax_extension* table_ext = cmark_find_syntax_extension("table");
    if (table_ext) {
        cmark_parser_attach_syntax_extension(parser, table_ext);
    }
    
    cmark_syntax_extension* strikethrough_ext = cmark_find_syntax_extension("strikethrough");
    if (strikethrough_ext) {
        cmark_parser_attach_syntax_extension(parser, strikethrough_ext);
    }
    
    cmark_syntax_extension* autolink_ext = cmark_find_syntax_extension("autolink");
    if (autolink_ext) {
        cmark_parser_attach_syntax_extension(parser, autolink_ext);
    }
    
    cmark_syntax_extension* tasklist_ext = cmark_find_syntax_extension("tasklist");
    if (tasklist_ext) {
        cmark_parser_attach_syntax_extension(parser, tasklist_ext);
    }
    
    // Feed the parser with the markdown text
    cmark_parser_feed(parser, markdown_sv.data(), markdown_sv.length()); // Use data() and length()
    
    // Finish parsing and get the document root node
    cmark_node* document = cmark_parser_finish(parser);
    
    // Convert to HTML
    char* html_cstr = cmark_render_html(document, options, 
                                    cmark_parser_get_syntax_extensions(parser));
    
    // Free the parser object
    cmark_parser_free(parser);
    
    // Free the document node
    cmark_node_free(document);
    
    // Convert C string to NSString and free the C string
    NSString* html = html_cstr ? @(html_cstr) : @"";
    free(html_cstr);
    
    return html;
}

+ (NSString*)processCodeBlocksWithSyntaxHighlighting:(NSString*)html {
    NSError* error = nil;

    // Create a regular expression to find code blocks in the HTML
    NSRegularExpression* codeBlockRegex = [NSRegularExpression
                                           regularExpressionWithPattern:@"<pre><code class=\"language-([^\"]+)\">(.*?)</code></pre>"
                                           options:NSRegularExpressionDotMatchesLineSeparators
                                           error:&error];
    
    if (error) {
        ERM(@"Error in code block regex: %@", error.localizedDescription);
        return html;
    }
    
    NSMutableString* processedHTML = [html mutableCopy];
    
    // Find all code blocks in reverse order (to avoid messing up text positions when replacing)
    NSArray* matches = [codeBlockRegex matchesInString:html
                                               options:0
                                                 range:NSMakeRange(0, html.length)];
    
    for (NSTextCheckingResult* match in [matches reverseObjectEnumerator]) {
        if (match.numberOfRanges >= 3) {
            NSRange languageRange = [match rangeAtIndex:1];
            NSRange codeRange = [match rangeAtIndex:2];
            
            NSString* language = [html substringWithRange:languageRange];
            NSString* code = [html substringWithRange:codeRange];
            
            // Unescape HTML entities in the code
            code = [code stringByReplacingOccurrencesOfString:@"&lt;" withString:@"<"];
            code = [code stringByReplacingOccurrencesOfString:@"&gt;" withString:@">"];
            code = [code stringByReplacingOccurrencesOfString:@"&amp;" withString:@"&"];
            code = [code stringByReplacingOccurrencesOfString:@"&quot;" withString:@"\""];
            
            // Apply syntax highlighting to the code
            NSString* highlightedCode = HighlightCodeWithJS(code, language);
            
            // Create the new code block with highlighted HTML
            NSString* newCodeBlock = [NSString stringWithFormat:@"<pre><code class=\"language-%@ hljs\">%@</code></pre>", 
                                     language, highlightedCode];
            
            // Replace the old code block with the new one
            [processedHTML replaceCharactersInRange:match.range withString:newCodeBlock];
        }
    }
    
    // Also handle unlabeled code blocks
    NSRegularExpression* unlabeledCodeBlockRegex = [NSRegularExpression
                                                  regularExpressionWithPattern:@"<pre><code>(.*?)</code></pre>"
                                                  options:NSRegularExpressionDotMatchesLineSeparators
                                                  error:&error];
    
    if (error) {
        ERM(@"Error extracting code blocks: %@", error.localizedDescription);
        return processedHTML;
    }
    
    matches = [unlabeledCodeBlockRegex matchesInString:processedHTML
                                               options:0
                                                 range:NSMakeRange(0, processedHTML.length)];
    
    for (NSTextCheckingResult* match in [matches reverseObjectEnumerator]) {
        if (match.numberOfRanges >= 2) {
            NSRange codeRange = [match rangeAtIndex:1];
            
            NSString* code = [processedHTML substringWithRange:codeRange];
            
            // Unescape HTML entities in the code
            code = [code stringByReplacingOccurrencesOfString:@"&lt;" withString:@"<"];
            code = [code stringByReplacingOccurrencesOfString:@"&gt;" withString:@">"];
            code = [code stringByReplacingOccurrencesOfString:@"&amp;" withString:@"&"];
            code = [code stringByReplacingOccurrencesOfString:@"&quot;" withString:@"\""];
            
            // Apply auto syntax highlighting
            NSString* highlightedCode = HighlightCodeWithJS(code, nil);
            
            // Create the new code block with highlighted HTML
            NSString* newCodeBlock = [NSString stringWithFormat:@"<pre><code class=\"hljs\">%@</code></pre>", 
                                     highlightedCode];
            
            // Replace the old code block with the new one
            [processedHTML replaceCharactersInRange:match.range withString:newCodeBlock];
        }
    }
    
    return processedHTML;
}

+ (NSString*)addStylesToHTML:(NSString*)html maxWidth:(CGFloat)maxWidth {
    // Add CSS styles with a complete design system approach, dark mode support, and interactive elements
    return [NSString stringWithFormat:@"\
    <!DOCTYPE html>\
    <html>\
    <head>\
    <meta charset=\"UTF-8\">\
    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1\">\
    <style type=\"text/css\">\
    /* Design tokens and CSS variables for the design system */\
    :root {\
        /* Base grid unit (8px system) */\
        --grid-unit: 8px;\
        \
        /* Typography */\
        --font-family-sans: -apple-system, BlinkMacSystemFont, 'Segoe UI', Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji';\
        --font-family-mono: ui-monospace, SFMono-Regular, SF Mono, Menlo, Consolas, Liberation Mono, monospace;\
        --font-size-base: 14px;\
        --font-size-sm: 12px;\
        --font-size-xs: 10px;\
        --font-size-lg: 16px;\
        --font-size-xl: 18px;\
        --font-size-2xl: 20px;\
        --font-size-3xl: 24px;\
        --font-weight-normal: 400;\
        --font-weight-medium: 500;\
        --font-weight-semibold: 600;\
        --font-weight-bold: 700;\
        --line-height-tight: 1.3;\
        --line-height-normal: 1.6;\
        --line-height-relaxed: 1.8;\
        \
        /* Colors - light theme */\
        --color-text-primary: #1F2328;\
        --color-text-secondary: #57606a;\
        --color-text-tertiary: #6e7781;\
        --color-text-link: #0969da;\
        --color-text-link-hover: #0550ae;\
        --color-text-code: #24292f;\
        --color-border-primary: #d0d7de;\
        --color-border-secondary: #eaecef;\
        --color-border-tertiary: #f6f8fa;\
        --color-bg-primary: #ffffff;\
        --color-bg-secondary: #f6f8fa;\
        --color-bg-tertiary: #f0f2f5;\
        --color-bg-code: rgba(175, 184, 193, 0.2);\
        --color-bg-code-block: #f6f8fa;\
        --color-bg-overlay: rgba(0, 0, 0, 0.04);\
        --color-accent-primary: #0969da;\
        --color-accent-secondary: #2da44e;\
        --color-accent-warning: #d29922;\
        --color-accent-danger: #cf222e;\
        --color-accent-info: #6639ba;\
        \
        /* Shadows */\
        --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);\
        --shadow-md: 0 2px 4px rgba(0, 0, 0, 0.06), 0 1px 2px rgba(0, 0, 0, 0.08);\
        --shadow-lg: 0 4px 6px rgba(0, 0, 0, 0.05), 0 10px 15px rgba(0, 0, 0, 0.07);\
        \
        /* Spacing (based on 8px grid) */\
        --space-1: calc(var(--grid-unit) * 0.5);  /* 4px */\
        --space-2: var(--grid-unit);               /* 8px */\
        --space-3: calc(var(--grid-unit) * 2);     /* 16px */\
        --space-4: calc(var(--grid-unit) * 3);     /* 24px */\
        --space-5: calc(var(--grid-unit) * 4);     /* 32px */\
        --space-6: calc(var(--grid-unit) * 6);     /* 48px */\
        --space-7: calc(var(--grid-unit) * 8);     /* 64px */\
        \
        /* Border radius */\
        --radius-sm: 4px;\
        --radius-md: 6px;\
        --radius-lg: 8px;\
        --radius-xl: 12px;\
        --radius-full: 9999px;\
        \
        /* Animation */\
        --transition-fast: 120ms;\
        --transition-normal: 220ms;\
        --transition-slow: 300ms;\
        --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);\
        --ease-out: cubic-bezier(0, 0, 0.2, 1);\
        --ease-in: cubic-bezier(0.4, 0, 1, 1);\
    }\
    \
    /* Dark theme colors */\
    @media (prefers-color-scheme: dark) {\
        :root {\
            --color-text-primary: #e6edf3;\
            --color-text-secondary: #adbac7;\
            --color-text-tertiary: #768390;\
            --color-text-link: #58a6ff;\
            --color-text-link-hover: #79c0ff;\
            --color-text-code: #c9d1d9;\
            --color-border-primary: #30363d;\
            --color-border-secondary: #21262d;\
            --color-border-tertiary: #2d333b;\
            --color-bg-primary: #0d1117;\
            --color-bg-secondary: #161b22;\
            --color-bg-tertiary: #1c2128;\
            --color-bg-code: rgba(110, 118, 129, 0.4);\
            --color-bg-code-block: #161b22;\
            --color-bg-overlay: rgba(255, 255, 255, 0.05);\
            --color-accent-primary: #58a6ff;\
            --color-accent-secondary: #3fb950;\
            --color-accent-warning: #d29922;\
            --color-accent-danger: #f85149;\
            --color-accent-info: #8957e5;\
            \
            /* Adjust shadows for dark mode */\
            --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.3);\
            --shadow-md: 0 2px 4px rgba(0, 0, 0, 0.4), 0 1px 2px rgba(0, 0, 0, 0.4);\
            --shadow-lg: 0 4px 6px rgba(0, 0, 0, 0.3), 0 10px 15px rgba(0, 0, 0, 0.4);\
        }\
    }\
    \
    /* Base styles */\
    html {\
        box-sizing: border-box;\
        font-size: var(--font-size-base);\
        line-height: var(--line-height-normal);\
        -webkit-text-size-adjust: 100%%;\
    }\
    \
    *, *:before, *:after {\
        box-sizing: inherit;\
    }\
    \
    body {\
        font-family: var(--font-family-sans);\
        color: var(--color-text-primary);\
        background-color: var(--color-bg-primary);\
        margin: 0;\
        padding: var(--space-3);\
        word-wrap: break-word;\
        max-width: %.0fpx;\
        text-rendering: optimizeLegibility;\
        -webkit-font-smoothing: antialiased;\
        -moz-osx-font-smoothing: grayscale;\
        scroll-behavior: smooth;\
        transition: color var(--transition-normal) var(--ease-in-out),\
                    background-color var(--transition-normal) var(--ease-in-out);\
    }\
    \
    /* Container for better spacing */\
    .markdown-body {\
        padding: var(--space-3);\
    }\
    \
    /* Paragraphs */\
    p {\
        margin-top: 0;\
        margin-bottom: var(--space-3);\
        line-height: var(--line-height-relaxed);\
    }\
    \
    /* Links with enhanced hover effects */\
    a {\
        color: var(--color-text-link);\
        text-decoration: none;\
        transition: color var(--transition-fast) var(--ease-out),\
                    background-color var(--transition-fast) var(--ease-out),\
                    box-shadow var(--transition-fast) var(--ease-out);\
        border-radius: var(--radius-sm);\
        padding: 0 var(--space-1);\
        margin: 0 calc(var(--space-1) * -1);\
    }\
    \
    a:hover {\
        color: var(--color-text-link-hover);\
        text-decoration: underline;\
        background-color: var(--color-bg-overlay);\
    }\
    \
    a:focus {\
        outline: 2px solid var(--color-accent-primary);\
        outline-offset: 2px;\
    }\
    \
    a:active {\
        transform: translateY(1px);\
    }\
    \
    /* Headers with improved spacing, typography and subtle enhancements */\
    h1, h2, h3, h4, h5, h6 {\
        margin-top: var(--space-5);\
        margin-bottom: var(--space-3);\
        font-weight: var(--font-weight-semibold);\
        line-height: var(--line-height-tight);\
        letter-spacing: -0.01em;\
        position: relative;\
    }\
    \
    h1:first-child, h2:first-child, h3:first-child {\
        margin-top: 0;\
    }\
    \
    /* Header hover effect with anchor link */\
    h1:hover::after, h2:hover::after, h3:hover::after,\
    h4:hover::after, h5:hover::after, h6:hover::after {\
        content: '#';\
        position: absolute;\
        left: -1.25em;\
        top: 0;\
        font-size: 0.85em;\
        opacity: 0.5;\
        color: var(--color-accent-primary);\
        transition: opacity var(--transition-fast) var(--ease-out);\
    }\
    \
    h1 {\
        font-size: var(--font-size-3xl);\
        font-weight: var(--font-weight-bold);\
        padding-bottom: var(--space-2);\
        border-bottom: 1px solid var(--color-border-secondary);\
        margin-bottom: var(--space-4);\
    }\
    \
    h2 {\
        font-size: var(--font-size-2xl);\
        padding-bottom: var(--space-2);\
        border-bottom: 1px solid var(--color-border-secondary);\
        margin-bottom: var(--space-3);\
    }\
    \
    h3 {\
        font-size: var(--font-size-xl);\
        margin-bottom: var(--space-3);\
    }\
    \
    h4 {\
        font-size: var(--font-size-lg);\
    }\
    \
    h5 {\
        font-size: var(--font-size-base);\
    }\
    \
    h6 {\
        font-size: var(--font-size-sm);\
        color: var(--color-text-secondary);\
    }\
    \
    /* Horizontal Rule with enhanced styling */\
    hr {\
        height: 2px;\
        padding: 0;\
        margin: var(--space-5) 0;\
        background-color: var(--color-border-primary);\
        border: 0;\
        border-radius: var(--radius-full);\
        background-image: linear-gradient(to right,\
            rgba(175, 184, 193, 0.2),\
            rgba(175, 184, 193, 0.6),\
            rgba(175, 184, 193, 0.2));\
    }\
    \
    @media (prefers-color-scheme: dark) {\
        hr {\
            background-image: linear-gradient(to right,\
                rgba(110, 118, 129, 0.2),\
                rgba(110, 118, 129, 0.6),\
                rgba(110, 118, 129, 0.2));\
        }\
    }\
    \
    /* Lists with improved spacing and styling */\
    ul, ol {\
        padding-left: calc(var(--space-3) + var(--space-2));\
        margin-top: 0;\
        margin-bottom: var(--space-4);\
    }\
    \
    ul ul, ul ol, ol ol, ol ul {\
        margin-top: var(--space-2);\
        margin-bottom: var(--space-2);\
    }\
    \
    li {\
        margin-top: var(--space-1);\
        margin-bottom: var(--space-1);\
        position: relative;\
    }\
    \
    li::marker {\
        color: var(--color-text-secondary);\
    }\
    \
    li + li {\
        margin-top: var(--space-1);\
    }\
    \
    /* Task Lists with better styling and interaction */\
    ul.task-list {\
        list-style-type: none;\
        padding-left: var(--space-2);\
    }\
    \
    ul.task-list li {\
        position: relative;\
        padding-left: calc(var(--space-4) - var(--space-1));\
        line-height: var(--line-height-normal);\
        margin: var(--space-2) 0;\
    }\
    \
    ul.task-list li input[type=checkbox] {\
        position: absolute;\
        left: 0;\
        top: 0.35em;\
        margin: 0;\
        appearance: none;\
        -webkit-appearance: none;\
        width: var(--space-3);\
        height: var(--space-3);\
        border: 1px solid var(--color-border-primary);\
        border-radius: var(--radius-sm);\
        background-color: var(--color-bg-primary);\
        cursor: pointer;\
        transition: all var(--transition-normal) var(--ease-in-out);\
    }\
    \
    ul.task-list li input[type=checkbox]:hover {\
        background-color: var(--color-bg-overlay);\
    }\
    \
    ul.task-list li input[type=checkbox]:checked {\
        background-color: var(--color-accent-secondary);\
        border-color: var(--color-accent-secondary);\
        background-image: url('data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 16 16\" fill=\"white\"><path d=\"M13.78 4.22a.75.75 0 0 1 0 1.06l-7.25 7.25a.75.75 0 0 1-1.06 0L2.22 9.28a.751.751 0 0 1 .018-1.042.751.751 0 0 1 1.042-.018L6 10.94l6.72-6.72a.75.75 0 0 1 1.06 0Z\"/></svg>');\
        background-position: center;\
        background-repeat: no-repeat;\
        background-size: 70%%;\
    }\
    \
    ul.task-list li input[type=checkbox]:focus {\
        outline: 2px solid var(--color-accent-primary);\
        outline-offset: 1px;\
    }\
    \
    /* Enhanced blockquotes with custom styling for different types */\
    blockquote {\
        padding: var(--space-3);\
        margin: var(--space-3) 0;\
        color: var(--color-text-secondary);\
        border-left: 4px solid var(--color-border-primary);\
        background-color: var(--color-bg-secondary);\
        border-radius: 0 var(--radius-md) var(--radius-md) 0;\
        box-shadow: var(--shadow-sm);\
        transition: border-color var(--transition-normal) var(--ease-in-out),\
                    background-color var(--transition-normal) var(--ease-in-out);\
    }\
    \
    blockquote > :last-child {\
        margin-bottom: 0;\
    }\
    \
    blockquote > :first-child {\
        margin-top: 0;\
    }\
    \
    /* Custom blockquote types based on first text character */\
    blockquote p:first-child:not(:only-child) {\
        font-weight: var(--font-weight-medium);\
    }\
    \
    /* Note style */\
    blockquote[data-type=\"note\"],\
    blockquote p:first-child:not(:only-child):before {\
        content: \"\";\
        border-left-color: var(--color-accent-info);\
    }\
    \
    /* Warning style */\
    blockquote[data-type=\"warning\"],\
    blockquote p:first-child:contains(\"Warning\"):not(:only-child),\
    blockquote p:first-child:contains(\"Caution\"):not(:only-child) {\
        border-left-color: var(--color-accent-warning);\
    }\
    \
    /* Important style */\
    blockquote[data-type=\"important\"],\
    blockquote p:first-child:contains(\"Important\"):not(:only-child) {\
        border-left-color: var(--color-accent-danger);\
    }\
    \
    /* Tip style */\
    blockquote[data-type=\"tip\"],\
    blockquote p:first-child:contains(\"Tip\"):not(:only-child) {\
        border-left-color: var(--color-accent-secondary);\
    }\
    \
    /* Tables with enhanced styling and responsiveness */\
    table {\
        border-spacing: 0;\
        border-collapse: collapse;\
        margin: var(--space-3) 0 var(--space-4) 0;\
        width: 100%%;\
        overflow: auto;\
        border-radius: var(--radius-md);\
        box-shadow: var(--shadow-sm);\
    }\
    \
    table th {\
        font-weight: var(--font-weight-semibold);\
        background-color: var(--color-bg-secondary);\
        padding: var(--space-2) var(--space-3);\
        text-align: left;\
        transition: background-color var(--transition-normal) var(--ease-in-out);\
    }\
    \
    table th:first-child {\
        border-top-left-radius: var(--radius-md);\
    }\
    \
    table th:last-child {\
        border-top-right-radius: var(--radius-md);\
    }\
    \
    table th, table td {\
        padding: var(--space-2) var(--space-3);\
        border: 1px solid var(--color-border-primary);\
    }\
    \
    table tr {\
        background-color: var(--color-bg-primary);\
        border-top: 1px solid var(--color-border-primary);\
        transition: background-color var(--transition-fast) var(--ease-in-out);\
    }\
    \
    table tr:hover {\
        background-color: var(--color-bg-secondary);\
    }\
    \
    table tr:nth-child(2n) {\
        background-color: var(--color-bg-secondary);\
    }\
    \
    table tr:nth-child(2n):hover {\
        background-color: var(--color-bg-tertiary);\
    }\
    \
    /* Images with enhanced styling and responsive behavior */\
    img {\
        max-width: 100%%;\
        height: auto;\
        display: block;\
        margin: var(--space-3) auto;\
        border-radius: var(--radius-md);\
        box-shadow: var(--shadow-md);\
        transition: transform var(--transition-normal) var(--ease-out),\
                    box-shadow var(--transition-normal) var(--ease-out);\
    }\
    \
    img:hover {\
        transform: scale(1.01);\
        box-shadow: var(--shadow-lg);\
    }\
    \
    /* Figure and caption support */\
    figure {\
        margin: var(--space-4) 0;\
        text-align: center;\
    }\
    \
    figcaption {\
        margin-top: var(--space-2);\
        font-size: var(--font-size-sm);\
        color: var(--color-text-secondary);\
        font-style: italic;\
    }\
    \
    /* Code and Syntax Highlighting with enhanced styling and interactions */\
    code, tt {\
        font-family: var(--font-family-mono);\
        font-size: 100%%;\
        background-color: var(--color-bg-code);\
        border-radius: var(--radius-sm);\
        padding: var(--space-1) var(--space-1);\
        margin: 0 var(--space-1);\
        transition: background-color var(--transition-fast) var(--ease-in-out);\
    }\
    \
    /* Enhanced code blocks with copy button and language label */\
    pre {\
        position: relative;\
        font-family: var(--font-family-mono);\
        word-wrap: normal;\
        margin: var(--space-3) 0 var(--space-4) 0;\
        padding: var(--space-3);\
        overflow: auto;\
        font-size: 85%%;\
        line-height: var(--line-height-normal);\
        background-color: var(--color-bg-code-block);\
        border-radius: var(--radius-md);\
        border: 1px solid var(--color-border-primary);\
        box-shadow: var(--shadow-sm);\
        transition: box-shadow var(--transition-normal) var(--ease-in-out),\
                    border-color var(--transition-normal) var(--ease-in-out);\
    }\
    \
    pre:hover {\
        box-shadow: var(--shadow-md);\
        border-color: var(--color-border-secondary);\
    }\
    \
    /* Language label for code blocks */\
    pre::before {\
        content: attr(data-lang);\
        position: absolute;\
        top: 0;\
        right: var(--space-3);\
        padding: var(--space-1) var(--space-2);\
        font-size: var(--font-size-xs);\
        color: var(--color-text-secondary);\
        background-color: var(--color-bg-tertiary);\
        border-radius: 0 0 var(--radius-sm) var(--radius-sm);\
        opacity: 0.8;\
    }\
    \
    pre code, pre tt {\
        display: inline;\
        max-width: auto;\
        padding: 0;\
        margin: 0;\
        overflow: visible;\
        line-height: inherit;\
        word-wrap: normal;\
        background-color: transparent;\
        border: 0;\
    }\
    \
    /* Copy button for code blocks */\
    pre .copy-button {\
        position: absolute;\
        top: var(--space-2);\
        right: var(--space-2);\
        opacity: 0;\
        padding: var(--space-1) var(--space-2);\
        background-color: var(--color-bg-tertiary);\
        border: 1px solid var(--color-border-primary);\
        border-radius: var(--radius-sm);\
        font-size: var(--font-size-xs);\
        color: var(--color-text-secondary);\
        cursor: pointer;\
        transition: all var(--transition-fast) var(--ease-out);\
    }\
    \
    pre:hover .copy-button {\
        opacity: 1;\
    }\
    \
    pre .copy-button:hover {\
        background-color: var(--color-bg-primary);\
        color: var(--color-text-primary);\
    }\
    \
    pre .copy-button:active {\
        transform: translateY(1px);\
    }\
    \
    /* GitHub Syntax Highlighting - with enhanced colors for both light and dark themes */\
    .hljs {\
        display: block;\
        overflow-x: auto;\
        padding: var(--space-1);\
        color: var(--color-text-primary);\
        background: transparent;\
    }\
    \
    .hljs-comment,\
    .hljs-quote {\
        color: var(--color-text-tertiary);\
        font-style: italic;\
    }\
    \
    .hljs-keyword,\
    .hljs-selector-tag {\
        color: var(--color-accent-danger);\
    }\
    \
    .hljs-literal,\
    .hljs-number,\
    .hljs-tag .hljs-attr,\
    .hljs-template-variable,\
    .hljs-variable {\
        color: var(--color-accent-primary);\
    }\
    \
    .hljs-doctag,\
    .hljs-string {\
        color: var(--color-accent-secondary);\
    }\
    \
    .hljs-section,\
    .hljs-selector-id,\
    .hljs-title {\
        color: var(--color-accent-info);\
        font-weight: bold;\
    }\
    \
    .hljs-subst {\
        color: var(--color-text-primary);\
        font-weight: normal;\
    }\
    \
    .hljs-type {\
        color: var(--color-accent-danger);\
        font-weight: bold;\
    }\
    \
    .hljs-class .hljs-title {\
        color: var(--color-accent-info);\
    }\
    \
    .hljs-attribute,\
    .hljs-name,\
    .hljs-tag {\
        color: var(--color-accent-secondary);\
        font-weight: normal;\
    }\
    \
    .hljs-link,\
    .hljs-regexp {\
        color: var(--color-accent-primary);\
    }\
    \
    .hljs-bullet,\
    .hljs-symbol {\
        color: var(--color-accent-primary);\
    }\
    \
    .hljs-built_in,\
    .hljs-builtin-name {\
        color: var(--color-accent-warning);\
    }\
    \
    .hljs-meta {\
        color: var(--color-text-tertiary);\
        font-weight: bold;\
    }\
    \
    .hljs-deletion {\
        background: rgba(207, 34, 46, 0.15);\
    }\
    \
    .hljs-addition {\
        background: rgba(45, 164, 78, 0.15);\
    }\
    \
    .hljs-emphasis {\
        font-style: italic;\
    }\
    \
    .hljs-strong {\
        font-weight: bold;\
    }\
    \
    /* Collapsible details/summary elements with animation */\
    details {\
        margin: var(--space-3) 0;\
        padding: var(--space-3);\
        background-color: var(--color-bg-secondary);\
        border-radius: var(--radius-md);\
        border: 1px solid var(--color-border-primary);\
        transition: background-color var(--transition-normal) var(--ease-in-out),\
                    box-shadow var(--transition-normal) var(--ease-in-out);\
    }\
    \
    details:hover {\
        background-color: var(--color-bg-tertiary);\
        box-shadow: var(--shadow-sm);\
    }\
    \
    details[open] {\
        padding-bottom: var(--space-3);\
        background-color: var(--color-bg-tertiary);\
    }\
    \
    summary {\
        cursor: pointer;\
        font-weight: var(--font-weight-medium);\
        list-style: none;\
        position: relative;\
        padding-left: var(--space-4);\
    }\
    \
    summary::-webkit-details-marker {\
        display: none;\
    }\
    \
    summary::before {\
        content: '▶';\
        position: absolute;\
        left: 0;\
        transform: translateY(-2px);\
        color: var(--color-text-tertiary);\
        font-size: var(--font-size-xs);\
        transition: transform var(--transition-normal) var(--ease-out);\
    }\
    \
    details[open] summary::before {\
        transform: rotate(90deg) translateX(-2px);\
    }\
    \
    details > div {\
        margin-top: var(--space-3);\
        opacity: 0;\
        animation: fadeIn var(--transition-normal) var(--ease-out) forwards;\
    }\
    \
    @keyframes fadeIn {\
        from { opacity: 0; transform: translateY(-8px); }\
        to { opacity: 1; transform: translateY(0); }\
    }\
    \
    /* Keyboard shortcuts styling */\
    kbd {\
        display: inline-block;\
        padding: var(--space-1) var(--space-2);\
        font-family: var(--font-family-mono);\
        font-size: var(--font-size-xs);\
        line-height: 1;\
        color: var(--color-text-primary);\
        background-color: var(--color-bg-tertiary);\
        border: 1px solid var(--color-border-primary);\
        border-radius: var(--radius-sm);\
        box-shadow: 0 1px 0 var(--color-border-primary);\
    }\
    \
    /* Modern spacing for general content */\
    .markdown-body > *:first-child {\
        margin-top: 0 !important;\
    }\
    \
    .markdown-body > *:last-child {\
        margin-bottom: 0 !important;\
    }\
    \
    /* Print optimizations */\
    @media print {\
        body {\
            color: #000;\
            background-color: #fff;\
        }\
        a, a:visited {\
            color: #000;\
            text-decoration: underline;\
        }\
        a[href]:after {\
            content: \" (\" attr(href) \")\";\
        }\
        pre, blockquote {\
            border: 1px solid #999;\
            page-break-inside: avoid;\
        }\
        thead {\
            display: table-header-group;\
        }\
        tr, img {\
            page-break-inside: avoid;\
        }\
        img {\
            max-width: 100%% !important;\
        }\
        p, h2, h3 {\
            orphans: 3;\
            widows: 3;\
        }\
        h2, h3 {\
            page-break-after: avoid;\
        }\
    }\
    \
    /* JavaScript for interactive elements */\
    <script>\
    document.addEventListener('DOMContentLoaded', function() {\
        // Add copy buttons to code blocks\
        document.querySelectorAll('pre code').forEach(function(codeBlock) {\
            const container = codeBlock.parentNode;\
            const copyButton = document.createElement('button');\
            copyButton.className = 'copy-button';\
            copyButton.textContent = 'Copy';\
            \
            copyButton.addEventListener('click', function() {\
                const code = codeBlock.textContent;\
                navigator.clipboard.writeText(code).then(function() {\
                    copyButton.textContent = 'Copied!';\
                    setTimeout(function() {\
                        copyButton.textContent = 'Copy';\
                    }, 2000);\
                }, function() {\
                    copyButton.textContent = 'Failed!';\
                });\
            });\
            \
            container.appendChild(copyButton);\
            \
            // Set language attribute for styling\
            const codeClass = codeBlock.className;\
            const lang = codeClass.includes('language-') \
                ? codeClass.split('language-')[1].split(' ')[0] \
                : '';\
            if (lang) {\
                container.setAttribute('data-lang', lang);\
            }\
        });\
        \
        // Add special styling to blockquotes based on first line\
        document.querySelectorAll('blockquote').forEach(function(blockquote) {\
            const firstPara = blockquote.querySelector('p:first-child');\
            if (firstPara) {\
                const text = firstPara.textContent;\
                if (text.startsWith('Note:')) {\
                    blockquote.setAttribute('data-type', 'note');\
                } else if (text.startsWith('Warning:') || text.startsWith('Caution:')) {\
                    blockquote.setAttribute('data-type', 'warning');\
                } else if (text.startsWith('Important:')) {\
                    blockquote.setAttribute('data-type', 'important');\
                } else if (text.startsWith('Tip:')) {\
                    blockquote.setAttribute('data-type', 'tip');\
                }\
            }\
        });\
    });\
    </script>\
    </style>\
    </head>\
    <body><div class=\"markdown-body\">%@</div></body>\
    </html>", maxWidth, html];
}

@end 