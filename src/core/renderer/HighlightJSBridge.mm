#import "HighlightJSBridge.h"
#include "core/util/debug.h"

// Wrap highlight.js in a function that we can call from our bridge
// This provides a clean interface between the JS and Objective-C++ code
static NSString* const kHighlightJSWrapper = @
"var self = this;\n"
"function highlightCode(code, language) {\n"
"    if (typeof hljs !== 'undefined') {\n"
"        if (language && language.length > 0 && hljs.getLanguage(language)) {\n"
"            try {\n"
"                return hljs.highlight(code, {language: language}).value;\n"
"            } catch (e) {\n"
"                console.error('Error highlighting:', e);\n"
"                return code;\n"
"            }\n"
"        } else {\n"
"            try {\n"
"                return hljs.highlightAuto(code).value;\n"
"            } catch (e) {\n"
"                console.error('Error auto-highlighting:', e);\n"
"                return code;\n"
"            }\n"
"        }\n"
"    }\n"
"    return code;\n"
"}";

@implementation HighlightJSBridge

+ (instancetype)sharedBridge {
    static HighlightJSBridge* instance = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        instance = [[HighlightJSBridge alloc] init];
    });
    return instance;
}

- (instancetype)init {
    self = [super init];
    if (self) {
        _jsContext = [[JSContext alloc] init];
        
        // Set up error handling
        _jsContext.exceptionHandler = ^(JSContext *context, JSValue *exception) {
            DBM(@"JS Error: %@", exception);
        };
        
        // Try to load highlight.js from the embedded resource
        NSString* highlightJSPath = [[NSBundle mainBundle] pathForResource:@"highlight.min" ofType:@"js"];
        if (highlightJSPath) {
            NSError* error = nil;
            NSString* highlightJSCode = [NSString stringWithContentsOfFile:highlightJSPath 
                                                                  encoding:NSUTF8StringEncoding 
                                                                     error:&error];
            if (!error && highlightJSCode) {
                [_jsContext evaluateScript:highlightJSCode];
                DBM(@"Loaded highlight.js from bundle");
            } else {
                ERM(@"Failed to load highlight.js from bundle: %@", error.localizedDescription);
                [self tryLoadFromResourcesDirectory];
            }
        } else {
            DBM(@"Could not find highlight.js in bundle, trying alternative locations");
            [self tryLoadFromResourcesDirectory];
        }
        
        // Check if highlight.js was successfully loaded
        JSValue* hljs = _jsContext[@"hljs"];
        if (!hljs || [hljs isUndefined]) {
            ERM(@"highlight.js is not available after initialization attempts");
        } else {
            DBM(@"highlight.js is successfully loaded and available");
        }
        
        // Add our wrapper function
        [_jsContext evaluateScript:kHighlightJSWrapper];
    }
    return self;
}

- (void)tryLoadFromResourcesDirectory {
    // Try to load from specific locations

    // 1. Try from main project resources directory
    NSString* resourcesPath = [[[NSBundle mainBundle] bundlePath] stringByAppendingPathComponent:@"Resources"];
    NSString* highlightJSPath = [resourcesPath stringByAppendingPathComponent:@"highlight.min.js"];
    
    NSError* error = nil;
    NSString* highlightJSCode = [NSString stringWithContentsOfFile:highlightJSPath 
                                                         encoding:NSUTF8StringEncoding 
                                                            error:&error];
    if (!error && highlightJSCode) {
        [_jsContext evaluateScript:highlightJSCode];
        DBM(@"Loaded highlight.js from Resources directory");
        return;
    }
    
    // 2. Try from project root resources directory
    NSString* projectPath = [[[NSBundle mainBundle] bundlePath] stringByDeletingLastPathComponent];
    NSString* rootResourcesPath = [projectPath stringByAppendingPathComponent:@"resources"];
    highlightJSPath = [rootResourcesPath stringByAppendingPathComponent:@"highlight.min.js"];
    
    error = nil;
    highlightJSCode = [NSString stringWithContentsOfFile:highlightJSPath 
                                               encoding:NSUTF8StringEncoding 
                                                  error:&error];
    if (!error && highlightJSCode) {
        [_jsContext evaluateScript:highlightJSCode];
        DBM(@"Loaded highlight.js from project root resources directory");
        return;
    }
    
    // No further fallbacks – file should be bundled via CMake and packaging scripts.
    ERM(@"Failed to load highlight.js from any location");
}

- (NSString*)highlightCode:(NSString*)codeText language:(NSString*)language {
    if (!codeText) {
        return @"";
    }
    
    // Check if highlight.js is available in the context
    JSValue* hljs = _jsContext[@"hljs"];
    if (!hljs || [hljs isUndefined]) {
        ERM(@"highlight.js not available");
        return codeText;
    }
    
    // Get our wrapper function
    JSValue* highlightFn = _jsContext[@"highlightCode"];
    if (!highlightFn || [highlightFn isUndefined]) {
        ERM(@"highlightCode function not found");
        return codeText;
    }
    
    // Call the JS highlight function
    JSValue* result = [highlightFn callWithArguments:@[codeText, language ?: @""]];
    if ([result isUndefined] || [result isNull]) {
        ERM(@"Highlighting failed");
        return codeText;
    }
    
    return [result toString];
}

- (void)javaScriptError:(NSString*)error withException:(NSException*)exception {
    DBM(@"JS Error: %@", exception);
    // ... existing code ...
}

@end

// Expose C function for convenience
extern "C" NSString* HighlightCodeWithJS(NSString* codeText, NSString* language) {
    return [[HighlightJSBridge sharedBridge] highlightCode:codeText language:language];
} 