#ifndef MINICHAT_HIGHLIGHT_JS_BRIDGE_H
#define MINICHAT_HIGHLIGHT_JS_BRIDGE_H

#import <Foundation/Foundation.h>
#import <JavaScriptCore/JavaScriptCore.h>

/**
 * @class HighlightJSBridge
 * @brief Provides a bridge to highlight.js for robust syntax highlighting
 *
 * This class uses JavaScriptCore to evaluate highlight.js and expose its
 * functionality to Objective-C++. It provides better, more accurate syntax
 * highlighting for code blocks in markdown content.
 */
@interface HighlightJSBridge : NSObject

/** @brief JavaScript execution context */
@property(nonatomic, strong) JSContext* jsContext;

/**
 * @brief Returns the shared singleton instance
 * @return The shared HighlightJSBridge instance
 */
+ (instancetype)sharedBridge;

/**
 * @brief Highlights code using highlight.js
 * @param codeText The code text to highlight
 * @param language The language identifier (e.g., "swift", "cpp", "javascript")
 * @return HTML string with syntax highlighting markup
 */
- (NSString*)highlightCode:(NSString*)codeText language:(NSString*)language;

@end

/**
 * @brief C function wrapper for highlight.js syntax highlighting
 * @param codeText The code text to highlight
 * @param language The language identifier
 * @return HTML string with syntax highlighting markup
 */
extern "C" NSString* HighlightCodeWithJS(NSString* codeText, NSString* language);

#endif  // MINICHAT_HIGHLIGHT_JS_BRIDGE_H