#import "RenderCache.h"
#include "core/util/debug.h"
#include <CommonCrypto/CommonDigest.h>

// Private cache entry class
@interface RenderCacheEntry : NSObject
@property (nonatomic, strong) NSAttributedString* attributedString;
@property (nonatomic, strong) NSArray<NSImage*>* images;
@property (nonatomic, assign) CGFloat width;
@property (nonatomic, assign) CGFloat height;
@property (nonatomic, strong) NSDate* lastAccessed;
@end

@implementation RenderCacheEntry

- (instancetype)init {
    self = [super init];
    if (self) {
        self.lastAccessed = [NSDate date];
    }
    return self;
}

- (void)updateLastAccessed {
    self.lastAccessed = [NSDate date];
}

@end

// Main cache implementation
@implementation RenderCache {
    NSMutableDictionary<NSString*, NSMutableDictionary<NSNumber*, RenderCacheEntry*>*>* _cache;
    NSMutableDictionary<NSString*, NSArray<NSImage*>*>* _imageCache;
    dispatch_queue_t _cacheQueue;
    NSInteger _maxEntries;
}

#pragma mark - Initialization

+ (instancetype)sharedCache {
    static RenderCache* sharedInstance = nil;
    static dispatch_once_t onceToken;
    
    dispatch_once(&onceToken, ^{
        sharedInstance = [[self alloc] init];
    });
    
    return sharedInstance;
}

- (instancetype)init {
    self = [super init];
    if (self) {
        _cache = [NSMutableDictionary dictionary];
        _imageCache = [NSMutableDictionary dictionary];
        _cacheQueue = dispatch_queue_create("com.minichat.rendercache", DISPATCH_QUEUE_CONCURRENT);
        _maxEntries = 100; // Limit cache size
        
        // Register for memory warnings - using NSWorkspace notifications instead
        [[NSNotificationCenter defaultCenter] addObserver:self 
                                                 selector:@selector(handleMemoryWarning:) 
                                                     name:NSWorkspaceWillSleepNotification 
                                                   object:nil];
    }
    return self;
}

- (void)dealloc {
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

#pragma mark - Caching Methods

- (void)cacheAttributedString:(NSAttributedString*)attributedString 
                      forHash:(NSString*)hash 
                      atWidth:(CGFloat)width {
    if (!attributedString || !hash || hash.length == 0) {
        return;
    }
    
    dispatch_barrier_async(_cacheQueue, ^{
        // Create width-specific entry
        NSNumber* widthKey = @(width);
        RenderCacheEntry* entry = [[RenderCacheEntry alloc] init];
        entry.attributedString = attributedString;
        entry.width = width;
        
        // Add or update cache
        NSMutableDictionary* widthCache = self->_cache[hash];
        if (!widthCache) {
            widthCache = [NSMutableDictionary dictionary];
            self->_cache[hash] = widthCache;
        }
        
        widthCache[widthKey] = entry;
        
        // Prune cache if needed
        [self pruneCache];
    });
}

- (NSAttributedString*)attributedStringForHash:(NSString*)hash 
                                       atWidth:(CGFloat)width {
    if (!hash || hash.length == 0) {
        return nil;
    }
        
    __block NSAttributedString* result = nil;
    
    dispatch_sync(_cacheQueue, ^{
        NSMutableDictionary* widthCache = self->_cache[hash];
        if (widthCache) {
            // Look for exact width match
            RenderCacheEntry* entry = widthCache[@(width)];
            
            // If no exact match, find closest width that's no more than 20px different
            if (!entry) {
                CGFloat closestDiff = CGFLOAT_MAX;
                NSNumber* closestKey = nil;
                
                for (NSNumber* widthKey in widthCache) {
                    CGFloat diff = fabs(widthKey.floatValue - width);
                    if (diff < 20.0 && diff < closestDiff) {
                        closestDiff = diff;
                        closestKey = widthKey;
                    }
                }
                
                if (closestKey) {
                    entry = widthCache[closestKey];
                }
            }
            
            if (entry) {
                result = entry.attributedString;
                [entry updateLastAccessed];
            }
        }
    });
    
    return result;
}

- (void)cacheImages:(NSArray<NSImage*>*)images 
            forHash:(NSString*)hash {
    if (!images || !hash || hash.length == 0) {
        return;
    }
    
    dispatch_barrier_async(_cacheQueue, ^{
        self->_imageCache[hash] = images;
    });
}

- (NSArray<NSImage*>*)imagesForHash:(NSString*)hash {
    if (!hash || hash.length == 0) {
        return nil;
    }
    
    __block NSArray<NSImage*>* result = nil;
    
    dispatch_sync(_cacheQueue, ^{
        result = self->_imageCache[hash];
    });
    
    return result;
}

- (void)cacheHeight:(CGFloat)height 
            forHash:(NSString*)hash 
            atWidth:(CGFloat)width {
    if (!hash || hash.length == 0) {
        return;
    }
    
    dispatch_barrier_async(_cacheQueue, ^{
        // Create or get width-specific entry
        NSNumber* widthKey = @(width);
        NSMutableDictionary* widthCache = self->_cache[hash];
        
        if (!widthCache) {
            widthCache = [NSMutableDictionary dictionary];
            self->_cache[hash] = widthCache;
        }
        
        RenderCacheEntry* entry = widthCache[widthKey];
        
        if (!entry) {
            entry = [[RenderCacheEntry alloc] init];
            entry.width = width;
            widthCache[widthKey] = entry;
        }
        
        entry.height = height;
        [entry updateLastAccessed];
    });
}

- (CGFloat)heightForHash:(NSString*)hash 
                 atWidth:(CGFloat)width {
    if (!hash || hash.length == 0) {
        return 0;
    }
    
    __block CGFloat result = 0;
    
    dispatch_sync(_cacheQueue, ^{
        NSMutableDictionary* widthCache = self->_cache[hash];
        if (widthCache) {
            // Look for exact width match
            RenderCacheEntry* entry = widthCache[@(width)];
            
            // If no exact match, find closest width that's no more than 20px different
            if (!entry) {
                CGFloat closestDiff = CGFLOAT_MAX;
                NSNumber* closestKey = nil;
                
                for (NSNumber* widthKey in widthCache) {
                    CGFloat diff = fabs(widthKey.floatValue - width);
                    if (diff < 20.0 && diff < closestDiff) {
                        closestDiff = diff;
                        closestKey = widthKey;
                    }
                }
                
                if (closestKey) {
                    entry = widthCache[closestKey];
                }
            }
            
            if (entry) {
                result = entry.height;
                [entry updateLastAccessed];
            }
        }
    });
    
    return result;
}

#pragma mark - Cache Management

- (void)clearCache {
    dispatch_barrier_async(_cacheQueue, ^{
        [self->_cache removeAllObjects];
        [self->_imageCache removeAllObjects];
    });
}

- (void)clearCacheForHash:(NSString*)hash {
    if (!hash || hash.length == 0) {
        return;
    }
    
    dispatch_barrier_async(_cacheQueue, ^{
        [self->_cache removeObjectForKey:hash];
        [self->_imageCache removeObjectForKey:hash];
    });
}

- (void)pruneCache {
    // DBM(@"Pruning cache");
    
    // Explicitly cast _maxEntries to NSUInteger for comparison
    if (_cache.count <= (NSUInteger)_maxEntries) {
        return;
    }
    
    // Find oldest accessed entries
    NSMutableArray* entries = [NSMutableArray array];
    
    for (NSString* hash in _cache) {
        NSMutableDictionary* widthCache = _cache[hash];
        
        for (NSNumber* widthKey in widthCache) {
            RenderCacheEntry* entry = widthCache[widthKey];
            [entries addObject:@{
                @"hash": hash,
                @"width": widthKey,
                @"entry": entry
            }];
        }
    }
    
    // Sort by last accessed date
    [entries sortUsingComparator:^NSComparisonResult(id obj1, id obj2) {
        RenderCacheEntry* entry1 = obj1[@"entry"];
        RenderCacheEntry* entry2 = obj2[@"entry"];
        return [entry1.lastAccessed compare:entry2.lastAccessed];
    }];
    
    // Remove oldest entries
    NSInteger entriesToRemove = entries.count - _maxEntries;
    
    for (NSInteger i = 0; i < entriesToRemove; i++) {
        NSDictionary* entryInfo = entries[i];
        NSString* hash = entryInfo[@"hash"];
        NSNumber* width = entryInfo[@"width"];
        
        NSMutableDictionary* widthCache = _cache[hash];
        [widthCache removeObjectForKey:width];
        
        // If no more entries for this hash, remove the hash entirely
        if (widthCache.count == 0) {
            [_cache removeObjectForKey:hash];
            [_imageCache removeObjectForKey:hash];
        }
    }
}

- (void)handleMemoryWarning:(NSNotification*)notification {
    DBM(@"Memory warning received, clearing cache");
    [self clearCache];
}

#pragma mark - Utility Methods

+ (NSString*)hashForContent:(NSString*)content {
    if (!content || content.length == 0) {
        return @"";
    }
    
    const char* cstr = [content UTF8String];
    unsigned char result[CC_SHA256_DIGEST_LENGTH];
    
    CC_SHA256(cstr, (CC_LONG)strlen(cstr), result);
    
    NSMutableString *hash = [NSMutableString stringWithCapacity:CC_SHA256_DIGEST_LENGTH * 2];
    for (int i = 0; i < CC_SHA256_DIGEST_LENGTH; i++) {
        [hash appendFormat:@"%02x", result[i]];
    }
    
    return hash;
}

@end
