#include "search_cache.h"

#include <algorithm>
// #include <sstream> // No longer needed for generateKey
#include <fmt/core.h> // Add fmt include
#include <fmt/core.h> // Add fmt include

namespace launcher {
namespace core {

SearchCache::SearchCache(size_t maxSize, std::chrono::seconds ttl) : maxSize_(maxSize), ttl_(ttl) {}

void SearchCache::addResults(const std::string& query, const std::vector<AppResult>& results,
                             bool useHistory) {
    // Generate cache key
    std::string key = generateKey(query, useHistory);

    // Create cache entry
    CacheEntry entry;
    entry.results = results;
    entry.timestamp = std::chrono::system_clock::now();

    // Add or update entry in cache
    cache_[key] = entry;

    // Update access order
    updateAccessOrder(key);

    // Evict if needed
    evictIfNeeded();
}

bool SearchCache::hasQuery(const std::string& query, bool useHistory) const {
    // Generate cache key
    std::string key = generateKey(query, useHistory);

    // Check if key exists in cache
    auto it = cache_.find(key);
    if (it == cache_.end()) {
        return false;
    }

    // Check if entry is expired
    if (isExpired(it->second.timestamp)) {
        return false;
    }

    return true;
}

std::vector<AppResult> SearchCache::getResults(const std::string& query, bool useHistory) {
    // Generate cache key
    std::string key = generateKey(query, useHistory);

    // Check if key exists in cache
    auto it = cache_.find(key);
    if (it == cache_.end()) {
        return {};
    }

    // Check if entry is expired
    if (isExpired(it->second.timestamp)) {
        // Remove expired entry
        accessMap_.erase(key);
        accessOrder_.remove(key);
        cache_.erase(key);
        return {};
    }

    // Update access order
    updateAccessOrder(key);

    // Return cached results
    return it->second.results;
}

void SearchCache::clear() {
    cache_.clear();
    accessOrder_.clear();
    accessMap_.clear();
}

void SearchCache::removeExpiredEntries() {
    // Collect keys to remove
    std::vector<std::string> keysToRemove;

    for (const auto& entry : cache_) {
        if (isExpired(entry.second.timestamp)) {
            keysToRemove.push_back(entry.first);
        }
    }

    // Remove expired entries
    for (const auto& key : keysToRemove) {
        accessMap_.erase(key);
        accessOrder_.remove(key);
        cache_.erase(key);
    }
}

std::string SearchCache::generateKey(const std::string& query, bool useHistory) const {
    // Combine query and useHistory flag to create a unique key
    // std::stringstream ss; // Old way
    // ss << query << "_" << (useHistory ? "1" : "0"); // Old way
    // return ss.str(); // Old way
    return fmt::format("{}_{}", query, useHistory ? "1" : "0"); // New way using fmt
    return fmt::format("{}_{}", query, useHistory ? "1" : "0"); // New way using fmt
}

bool SearchCache::isExpired(const std::chrono::system_clock::time_point& timestamp) const {
    // Get current time
    auto now = std::chrono::system_clock::now();

    // Calculate elapsed time
    auto elapsed = std::chrono::duration_cast<std::chrono::seconds>(now - timestamp);

    // Check if elapsed time exceeds TTL
    return elapsed > ttl_;
}

void SearchCache::updateAccessOrder(const std::string& key) {
    // Check if key already exists in access order
    auto it = accessMap_.find(key);

    if (it != accessMap_.end()) {
        // Remove existing entry
        accessOrder_.erase(it->second);
    }

    // Add key to front of access order
    accessOrder_.push_front(key);

    // Update access map
    accessMap_[key] = accessOrder_.begin();
}

void SearchCache::evictIfNeeded() {
    // Check if cache exceeds maximum size
    while (cache_.size() > maxSize_) {
        // Get least recently used key
        std::string lruKey = accessOrder_.back();

        // Remove from cache
        cache_.erase(lruKey);

        // Remove from access order
        accessOrder_.pop_back();

        // Remove from access map
        accessMap_.erase(lruKey);
    }
}

}  // namespace core
}  // namespace launcher