#include "search_history.h"

#include <algorithm>
#include <filesystem>
#include <fstream>
#include <iomanip>
#include <iostream>
#include <nlohmann/json.hpp>
#include <sstream>
#include <vector>

#include "../util/debug.h"
#include "../util/time_utils.h"

namespace launcher {
namespace core {

namespace fs = std::filesystem;

SearchHistory::SearchHistory(const std::string& filePath, size_t maxSize)
    : filePath_(filePath), maxSize_(maxSize) {
    // Try to load the history from the file
    if (fs::exists(filePath)) {
        loadFromFile();
    }
}

void SearchHistory::addQuery(const std::string& query) {
    // Check if the query already exists
    auto it = queryIndices_.find(query);
    if (it != queryIndices_.end()) {
        // Update existing query
        size_t index = it->second;
        queries_[index].frequency++;
        queries_[index].lastUsed = std::chrono::system_clock::now();

        // Move the query to the front of the list
        if (index > 0) {
            QueryEntry entry = queries_[index];
            queries_.erase(queries_.begin() + index);
            queries_.insert(queries_.begin(), entry);

            // Update indices
            for (auto& pair : queryIndices_) {
                if (pair.second < index) {
                    pair.second++;
                }
            }
            queryIndices_[query] = 0;
        }
    } else {
        // Add new query
        queries_.insert(queries_.begin(), QueryEntry(query));

        // Update indices
        for (auto& pair : queryIndices_) {
            pair.second++;
        }
        queryIndices_[query] = 0;

        // Trim history if needed
        trimHistory();
    }

    // Save the updated history to disk
    saveToFile();
}

std::vector<std::string> SearchHistory::getQueries() const {
    std::vector<std::string> result;
    result.reserve(queries_.size());

    for (const auto& entry : queries_) {
        result.push_back(entry.query);
    }

    return result;
}

int SearchHistory::getQueryFrequency(const std::string& query) const {
    auto it = queryIndices_.find(query);
    if (it != queryIndices_.end()) {
        return queries_[it->second].frequency;
    }
    return 0;
}

std::vector<std::string> SearchHistory::getMostFrequentQueries(size_t count) const {
    // Create a copy of the queries
    std::vector<QueryEntry> sortedQueries = queries_;

    // Sort by frequency (descending)
    std::sort(sortedQueries.begin(), sortedQueries.end(),
              [](const QueryEntry& a, const QueryEntry& b) { return a.frequency > b.frequency; });

    // Extract the top N queries
    std::vector<std::string> result;
    result.reserve(std::min(count, sortedQueries.size()));

    for (size_t i = 0; i < std::min(count, sortedQueries.size()); i++) {
        result.push_back(sortedQueries[i].query);
    }

    return result;
}

void SearchHistory::clear() {
    queries_.clear();
    queryIndices_.clear();
    saveToFile();
}

void SearchHistory::saveToFile() const {
    try {
        // Create directory if it doesn't exist
        fs::path path(filePath_);
        fs::create_directories(path.parent_path());

        // Create JSON object
        nlohmann::json json;
        json["version"] = 1;

        // Add queries
        nlohmann::json queriesArray = nlohmann::json::array();
        for (const auto& entry : queries_) {
            nlohmann::json queryJson;
            queryJson["query"] = entry.query;
            queryJson["frequency"] = entry.frequency;
            queryJson["last_used"] = utils::timePointToString(entry.lastUsed);
            queriesArray.push_back(queryJson);
        }
        json["queries"] = queriesArray;

        // Write to file
        std::ofstream file(filePath_);
        if (file.is_open()) {
            file << json.dump(4);  // Pretty print with 4-space indent
        } else {
            ERR("Failed to open file for writing: " + filePath_);
        }
    } catch (const std::exception& e) {
        std::string error_what = e.what();
        ERR("Error saving search history: " << error_what);
    }
}

void SearchHistory::loadFromFile() {
    try {
        // Check if file exists
        if (!fs::exists(filePath_)) {
            return;
        }

        // Open file
        std::ifstream file(filePath_);
        if (!file.is_open()) {
            ERR("Failed to open file for reading: " + filePath_);
            return;
        }

        // Parse JSON
        nlohmann::json json;
        file >> json;

        // Check version
        int version = json.value("version", 0);
        if (version != 1) {
            ERR("Unsupported search history version: " + std::to_string(version));
            return;
        }

        // Clear existing queries
        queries_.clear();

        // Load queries
        if (json.contains("queries") && json["queries"].is_array()) {
            for (const auto& queryJson : json["queries"]) {
                if (queryJson.contains("query")) {
                    QueryEntry entry(queryJson["query"]);
                    entry.frequency = queryJson["frequency"];
                    entry.lastUsed = utils::stringToTimePoint(queryJson["last_used"]);

                    size_t index = queries_.size();
                    queries_.push_back(entry);
                    queryIndices_[entry.query] = index;
                }
            }
        }
    } catch (const std::exception& e) {
        ERR("Error loading search history: " + std::string(e.what()));
    }
}

std::string SearchHistory::getDefaultHistoryPath() {
    // Get the user's home directory
    fs::path homePath;

#ifdef _WIN32
    // Windows
    const char* userProfile = std::getenv("USERPROFILE");
    if (userProfile) {
        homePath = userProfile;
    }
#else
    // macOS/Linux
    const char* home = std::getenv("HOME");
    if (home) {
        homePath = home;
    }
#endif

    // Create the path to the history file
    fs::path historyPath = homePath / ".launcher" / "search_history.json";
    return historyPath.string();
}

void SearchHistory::trimHistory() {
    if (queries_.size() > maxSize_) {
        // Remove excess queries from the end
        while (queries_.size() > maxSize_) {
            const std::string& query = queries_.back().query;
            queryIndices_.erase(query);
            queries_.pop_back();
        }
    }
}

}  // namespace core
}  // namespace launcher