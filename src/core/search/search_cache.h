#pragma once

#include <chrono>
#include <list>
#include <map>
#include <memory>
#include <string>
#include <unordered_map>
#include <vector>

#include "../scanner/app_scanner_interface.h"

namespace launcher {
namespace core {

/**
 * @brief A class for caching search results to improve performance.
 *
 * The SearchCache stores search results for queries to avoid redundant calculations.
 * It supports time-based expiration and size-based eviction of cache entries.
 */
class SearchCache {
 public:
    /**
     * @brief Construct a new Search Cache object
     *
     * @param maxSize Maximum number of queries to cache
     * @param ttl Time-to-live for cache entries
     */
    SearchCache(size_t maxSize = 50, std::chrono::seconds ttl = std::chrono::seconds(60));

    /**
     * @brief Add search results to the cache
     *
     * @param query The search query
     * @param results The search results to cache
     * @param useHistory Whether the results were generated using search history
     */
    void addResults(const std::string& query, const std::vector<AppResult>& results,
                    bool useHistory);

    /**
     * @brief Check if a query exists in the cache and is not expired
     *
     * @param query The search query
     * @param useHistory Whether to check for results generated using search history
     * @return true if the query is in the cache and not expired
     * @return false otherwise
     */
    bool hasQuery(const std::string& query, bool useHistory) const;

    /**
     * @brief Get cached results for a query
     *
     * @param query The search query
     * @param useHistory Whether to get results generated using search history
     * @return std::vector<AppResult> The cached results, or an empty vector if not found or expired
     */
    std::vector<AppResult> getResults(const std::string& query, bool useHistory);

    /**
     * @brief Clear all cached results
     */
    void clear();

    /**
     * @brief Remove expired entries from the cache
     */
    void removeExpiredEntries();

 private:
    /**
     * @brief Generate a cache key from a query and history flag
     *
     * @param query The search query
     * @param useHistory Whether the results were generated using search history
     * @return std::string The cache key
     */
    std::string generateKey(const std::string& query, bool useHistory) const;

    /**
     * @brief Check if a cache entry is expired
     *
     * @param timestamp The timestamp when the entry was added
     * @return true if the entry is expired
     * @return false otherwise
     */
    bool isExpired(const std::chrono::system_clock::time_point& timestamp) const;

    /**
     * @brief Update the access order of a cache key
     *
     * @param key The cache key to update
     */
    void updateAccessOrder(const std::string& key);

    /**
     * @brief Evict the least recently used entry if the cache is full
     */
    void evictIfNeeded();

    // Structure to hold cache entry data
    struct CacheEntry {
        std::vector<AppResult> results;
        std::chrono::system_clock::time_point timestamp;
    };

    // Maximum number of queries to cache
    size_t maxSize_;

    // Time-to-live for cache entries
    std::chrono::seconds ttl_;

    // Map of cache keys to entries
    std::unordered_map<std::string, CacheEntry> cache_;

    // List to track access order for LRU eviction
    std::list<std::string> accessOrder_;

    // Map to quickly find keys in the access order list
    std::unordered_map<std::string, std::list<std::string>::iterator> accessMap_;
};

}  // namespace core
}  // namespace launcher