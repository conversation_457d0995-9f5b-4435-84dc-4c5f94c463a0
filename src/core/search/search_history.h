#pragma once

#include <chrono>
#include <string>
#include <unordered_map>
#include <vector>

namespace launcher {
namespace core {

/**
 * @brief Class for tracking search history and query frequencies
 */
class SearchHistory {
 public:
    /**
     * @brief Default constructor
     */
    SearchHistory() : maxSize_(20) { filePath_ = getDefaultHistoryPath(); }

    /**
     * @brief Construct a new Search History object
     *
     * @param filePath Path to the history file
     * @param maxSize Maximum number of queries to keep in history
     */
    SearchHistory(const std::string& filePath, size_t maxSize = 20);

    /**
     * @brief Add a query to the history
     *
     * @param query The search query
     */
    void addQuery(const std::string& query);

    /**
     * @brief Record a search (alias for addQuery)
     *
     * @param query The search query
     */
    void recordSearch(const std::string& query) { addQuery(query); }

    /**
     * @brief Get all queries in the history, most recent first
     *
     * @return std::vector<std::string> The queries
     */
    std::vector<std::string> getQueries() const;

    /**
     * @brief Get the frequency of a query
     *
     * @param query The query to check
     * @return int The number of times the query has been searched
     */
    int getQueryFrequency(const std::string& query) const;

    /**
     * @brief Get the most frequent queries
     *
     * @param count Maximum number of queries to return
     * @return std::vector<std::string> The most frequent queries
     */
    std::vector<std::string> getMostFrequentQueries(size_t count) const;

    /**
     * @brief Clear the search history
     */
    void clear();

    /**
     * @brief Save the history to a file
     */
    void saveToFile() const;

    /**
     * @brief Load the history from a file
     */
    void loadFromFile();

    /**
     * @brief Get the default path for the history file
     *
     * @return std::string The default path
     */
    static std::string getDefaultHistoryPath();

 private:
    struct QueryEntry {
        std::string query;
        int frequency;
        std::chrono::system_clock::time_point lastUsed;

        QueryEntry(const std::string& q)
            : query(q), frequency(1), lastUsed(std::chrono::system_clock::now()) {}
    };

    std::vector<QueryEntry> queries_;
    std::unordered_map<std::string, size_t> queryIndices_;
    std::string filePath_;
    size_t maxSize_;

    // Helper method to trim the history to maxSize
    void trimHistory();
};

}  // namespace core
}  // namespace launcher