# Ranking library (contextual bandit reranker)
add_subdirectory(ranking)

# Build-time tools (code generators, CLI helpers)
add_subdirectory(tools)

# Add core library
add_subdirectory(core)
add_subdirectory(snapshot)

# Add UI library unless skipped (CI backend-only builds)
if(NOT SKIP_UI)
    add_subdirectory(ui)
else()
    message(STATUS "SKIP_UI=ON – UI targets not built")
endif()

# Add third_party
add_subdirectory(third_party)

# Add benchmarks
add_subdirectory(benchmarks)

# Add tests
add_subdirectory(tests)

# Add executable
add_executable(launcher 
    main.cpp
)

# Optional sanitizer instrumentation (Address, Thread, UB)
if(KAI_SAN_FLAGS)
    message(STATUS "Applying sanitizers to launcher: ${KAI_SAN_FLAGS_STR}")
    target_compile_options(launcher PRIVATE ${KAI_SAN_FLAGS})
    target_link_options(launcher PRIVATE ${KAI_SAN_FLAGS})
endif()

# Add link directories for cmark libraries
link_directories(${CMAKE_BINARY_DIR}/lib)

# Add cmark-gfm library
include_directories(
    ${CMAKE_BINARY_DIR}/lib/cmark-gfm/src
    ${CMAKE_BINARY_DIR}/lib/cmark-gfm/extensions
)

# Platform-specific dependencies for macOS
if(APPLE)
    find_library(APPKIT AppKit)
    find_library(FOUNDATION Foundation)
    find_library(CORE_FOUNDATION CoreFoundation)
    find_library(CORE_SERVICES CoreServices)
    find_library(SERVICE_MANAGEMENT ServiceManagement)
    find_library(SECURITY Security)
    find_library(JAVASCRIPTCORE JavaScriptCore)
    set(MACOS_LIBRARIES ${APPKIT} ${FOUNDATION} ${CORE_FOUNDATION} ${CORE_SERVICES} ${SERVICE_MANAGEMENT} ${SECURITY} ${JAVASCRIPTCORE})
endif()

# Link libraries
target_link_libraries(launcher
    PRIVATE
    snapshot_lib
    core
    ui
    ui_chat
    nlohmann_json::nlohmann_json
)

# Link platform-specific libraries
if(APPLE)
    target_link_libraries(launcher
        PRIVATE
        ${MACOS_LIBRARIES}
    )
endif()

# Apply project-wide warning flags (defined in root CMake) to launcher target.
if(PROJECT_WARNING_FLAGS)
    target_compile_options(launcher PRIVATE ${PROJECT_WARNING_FLAGS})
endif()

# Specify installation rules
install(TARGETS launcher DESTINATION bin)