#import <Foundation/Foundation.h>
#import <CoreServices/CoreServices.h>

#include "spotlight_metadata.h"
#include <unordered_map>
#include <list>
#include <chrono>

namespace ranking {

static double secondsSince(CFDateRef dateRef) {
  if (!dateRef) return -1.0;
  CFAbsoluteTime absTime = CFDateGetAbsoluteTime(dateRef); // seconds since Jan 1 2001
  CFAbsoluteTime now = CFAbsoluteTimeGetCurrent();
  return now - absTime; // seconds ago
}

struct CacheEntry {
  SpotlightStats stats;
  std::chrono::steady_clock::time_point ts;
};

static std::unordered_map<std::string, std::list<std::pair<std::string, CacheEntry>>::iterator> g_index;
static std::list<std::pair<std::string, CacheEntry>> g_lru;
static const size_t kMaxCache = 512;
static const std::chrono::seconds kTTL(600); // 10 min

static SpotlightStats fetchStats(const std::string &path) {
  SpotlightStats out;

  @autoreleasepool {
    NSString *nsPath = [NSString stringWithUTF8String:path.c_str()];
    if (!nsPath) return out;
    MDItemRef item = MDItemCreate(kCFAllocatorDefault, (__bridge CFStringRef)nsPath);
    if (!item) return out;

    // kMDItemLastUsedDate
    CFTypeRef val = MDItemCopyAttribute(item, kMDItemLastUsedDate);
    out.seconds_since_last_use = secondsSince((CFDateRef)val);
    if (val) CFRelease(val);

    // kMDItemDateAdded
    val = MDItemCopyAttribute(item, kMDItemDateAdded);
    out.seconds_since_added = secondsSince((CFDateRef)val);
    if (val) CFRelease(val);

    // kMDItemUseCount
    CFStringRef useKey;
#ifdef kMDItemUseCount
    useKey = kMDItemUseCount;
#else
    useKey = CFSTR("kMDItemUseCount");
#endif
    val = MDItemCopyAttribute(item, useKey);
    if (val && CFGetTypeID(val) == CFNumberGetTypeID()) {
      int64_t count = 0;
      CFNumberGetValue((CFNumberRef)val, kCFNumberSInt64Type, &count);
      out.use_count = static_cast<int>(count);
    }
    if (val) CFRelease(val);

    CFRelease(item);
  }

  return out;
}

SpotlightStats getSpotlightStats(const std::string &path) {
  using Clock = std::chrono::steady_clock;
  auto now = Clock::now();

  auto idxIt = g_index.find(path);
  if (idxIt != g_index.end()) {
    auto listIt = idxIt->second;
    if (now - listIt->second.ts < kTTL) {
      // Move to front (MRU)
      g_lru.splice(g_lru.begin(), g_lru, listIt);
      return listIt->second.stats;
    }
    // stale – erase and refetch
    g_lru.erase(listIt);
    g_index.erase(idxIt);
  }

  CacheEntry ce{fetchStats(path), now};
  g_lru.emplace_front(path, ce);
  g_index[path] = g_lru.begin();

  if (g_lru.size() > kMaxCache) {
    auto last = g_lru.end();
    --last;
    g_index.erase(last->first);
    g_lru.pop_back();
  }

  return ce.stats;
}

} // namespace ranking 