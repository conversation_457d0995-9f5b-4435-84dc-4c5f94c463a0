add_library(ranking STATIC
    feature_extractor.cpp
    linear_ucb.cpp
    rerank_service.cpp
    heuristic_scorer.cpp
    spotlight_metadata.mm
)

# Public header exposure

target_include_directories(ranking
    PUBLIC
        ${CMAKE_CURRENT_SOURCE_DIR}
)

# Dependencies
find_package(nlohmann_json QUIET)

# Enable C++23 and silence warnings for this target
set_target_properties(ranking PROPERTIES
    CXX_STANDARD 23
    CXX_STANDARD_REQUIRED ON
)

target_link_libraries(ranking
    PRIVATE
        nlohmann_json::nlohmann_json
        "-framework CoreServices" "-framework Foundation"
) 