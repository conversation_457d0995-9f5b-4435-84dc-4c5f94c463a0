#include "linear_ucb.h"

#include <cmath>
#include <stdexcept>

#include "core/util/debug.h"

namespace ranking {

namespace {
// Utility to compute dot product.
double dot(const std::vector<double>& a, const std::vector<double>& b) {
  if (a.size() != b.size()) {
    throw std::runtime_error("Dimension mismatch in dot product");
  }
  double sum = 0.0;
  for (size_t i = 0; i < a.size(); ++i) sum += a[i] * b[i];
  return sum;
}

// Multiply A (row-major dim x dim) with vector x.
std::vector<double> matVec(const std::vector<double>& A, const std::vector<double>& x, int dim) {
  std::vector<double> out(dim, 0.0);
  for (int r = 0; r < dim; ++r) {
    double acc = 0.0;
    int rowOffset = r * dim;
    for (int c = 0; c < dim; ++c) {
      acc += A[rowOffset + c] * x[c];
    }
    out[r] = acc;
  }
  return out;
}
}  // namespace

LinearUCB::LinearUCB(int dim, double alpha) : dim_(dim), alpha_(alpha) {
  // Initialise A_inv as identity matrix.
  A_inv_.assign(dim_ * dim_, 0.0);
  for (int i = 0; i < dim_; ++i) {
    A_inv_[i * dim_ + i] = 1.0;
  }
  b_.assign(dim_, 0.0);
  w_.assign(dim_, 0.0);
}

void LinearUCB::recomputeWeights() {
  // w = A_inv * b
  w_ = matVec(A_inv_, b_, dim_);
}

double LinearUCB::predict(const std::vector<double>& x) const {
  if (static_cast<int>(x.size()) != dim_) {
    ERR("Predict: invalid dimension " << x.size());
    return 0.0;
  }
  double res = 0.0;
  for (int i = 0; i < dim_; ++i) {
    res += w_[i] * x[i];
  }
  return res;
}

double LinearUCB::ucbScore(const std::vector<double>& x) const {
  // Compute x^T A_inv x
  auto Ax = matVec(A_inv_, x, dim_);
  double quad = dot(x, Ax);
  double bonus = alpha_ * std::sqrt(std::max(quad, 0.0));
  return predict(x) + bonus;
}

void LinearUCB::update(const std::vector<double>& x, double reward) {
  if (static_cast<int>(x.size()) != dim_) {
    ERR("Update: invalid dimension " << x.size());
    return;
  }
  // Sherman-Morrison update: A_inv = A_inv - (A_inv x x^T A_inv)/(1 + x^T A_inv x)
  auto Ax = matVec(A_inv_, x, dim_);
  double denom = 1.0;
  for (int i = 0; i < dim_; ++i) denom += x[i] * Ax[i];

  // outer = Ax * Ax^T (rank-1 matrix)
  for (int r = 0; r < dim_; ++r) {
    for (int c = 0; c < dim_; ++c) {
      A_inv_[r * dim_ + c] -= (Ax[r] * Ax[c]) / denom;
    }
  }

  // Update b and weights.
  for (int i = 0; i < dim_; ++i) {
    b_[i] += reward * x[i];
  }
  recomputeWeights();
}

// ---------------- Serialization helpers ----------------

void LinearUCB::save(std::ostream& out) const {
  int32_t dim32 = static_cast<int32_t>(dim_);
  out.write(reinterpret_cast<const char*>(&dim32), sizeof(dim32));
  out.write(reinterpret_cast<const char*>(&alpha_), sizeof(alpha_));

  auto writeVec = [&out](const std::vector<double>& v) {
    uint64_t sz = static_cast<uint64_t>(v.size());
    out.write(reinterpret_cast<const char*>(&sz), sizeof(sz));
    if (!v.empty()) {
      out.write(reinterpret_cast<const char*>(v.data()), sz * sizeof(double));
    }
  };

  writeVec(A_inv_);
  writeVec(b_);
}

LinearUCB LinearUCB::load(std::istream& in) {
  int32_t dim32;
  double alpha;

  if (!in.read(reinterpret_cast<char*>(&dim32), sizeof(dim32))) {
    throw std::runtime_error("Failed to read dim in LinearUCB::load");
  }
  if (!in.read(reinterpret_cast<char*>(&alpha), sizeof(alpha))) {
    throw std::runtime_error("Failed to read alpha in LinearUCB::load");
  }

  auto readVec = [&in](std::vector<double>& v) {
    uint64_t sz;
    if (!in.read(reinterpret_cast<char*>(&sz), sizeof(sz))) {
      throw std::runtime_error("Failed to read vector size");
    }
    v.resize(sz);
    if (sz > 0) {
      if (!in.read(reinterpret_cast<char*>(v.data()), sz * sizeof(double))) {
        throw std::runtime_error("Failed to read vector data");
      }
    }
  };

  LinearUCB model(dim32, alpha);
  readVec(model.A_inv_);
  readVec(model.b_);
  model.recomputeWeights();
  return model;
}

}  // namespace ranking 