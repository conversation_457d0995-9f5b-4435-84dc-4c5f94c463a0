#pragma once

#include <string>

namespace ranking {

struct SpotlightStats {
  double seconds_since_last_use = -1.0;  // -1 if unavailable
  double seconds_since_added = -1.0;     // -1 if unavailable
  int use_count = -1;                    // -1 if unavailable
};

// Fetch Spotlight metadata for the given file path. Safe to call from any thread.
// If an attribute is unavailable, fields are set to -1.
SpotlightStats getSpotlightStats(const std::string &path);

} // namespace ranking 