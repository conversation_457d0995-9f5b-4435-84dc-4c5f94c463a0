#include "heuristic_scorer.h"
#include "spotlight_metadata.h"

#include <algorithm>
#include <cctype>
#include <filesystem>
#include <cmath>

namespace fs = std::filesystem;

namespace ranking {

static bool equalsIgnoreCase(const std::string &a, const std::string &b) {
  if (a.size() != b.size()) return false;
  for (size_t i = 0; i < a.size(); ++i) {
    if (std::tolower(a[i]) != std::tolower(b[i])) return false;
  }
  return true;
}

double HeuristicScorer::boostFor(const std::string &path, const std::string &type) {
  fs::path p(path);
  double boost = 0.0;

  // Reward shallow depth
  int depth = std::distance(p.begin(), p.end());
  if (depth <= 3) boost += 0.05;
  else if (depth >= 6) boost -= 0.05;

  // Reward user directories
  std::string lower = p.string();
  std::transform(lower.begin(), lower.end(), lower.begin(), ::tolower);
  if (lower.find("/applications/") != std::string::npos ||
      lower.find("/desktop/") != std::string::npos ||
      lower.find("/documents/") != std::string::npos ||
      lower.find("/downloads/") != std::string::npos) {
    boost += 0.05;
  }

  // Penalise system
  if (lower.find("/system/") == 0 || lower.find("/library/") == 0) {
    boost -= 0.07;
  }

  // Type-based adjustments
  if (equalsIgnoreCase(type, "app")) boost += 0.05;
  if (equalsIgnoreCase(type, "file")) boost += 0.02;
  if (equalsIgnoreCase(type, "chat")) boost += 0.06;

  // ---------------------------------------------------------------------
  // New: cold-start bias for most-recent and most-frequently used items.
  // We leverage Spotlight usage metadata that is already fetched elsewhere
  // in the codebase. The goal is to surface recently or frequently opened
  // items near the top even before the contextual bandit has gathered any
  // personal click data.
  // ---------------------------------------------------------------------

  try {
    ranking::SpotlightStats stats = ranking::getSpotlightStats(path);

    // Recency component: decays with a 7-day half-life.
    double recency_component = 0.0;
    if (stats.seconds_since_last_use >= 0) {
      recency_component = std::exp(-stats.seconds_since_last_use / (86400.0 * 7.0));
    }

    // Frequency component: scaled by use_count (cap at 25 openings).
    double freq_component = 0.0;
    if (stats.use_count >= 0) {
      freq_component = std::min(stats.use_count / 25.0, 1.0);
    }

    // Apply weights. At maximum this adds +0.20 (0.10 + 0.10).
    boost += 0.10 * recency_component;
    boost += 0.10 * freq_component;
  } catch (const std::exception &e) {
    // Metadata unavailable – ignore.
  }

  // Clamp
  boost = std::clamp(boost, -0.25, 0.25);
  return boost;
}

}  // namespace ranking 