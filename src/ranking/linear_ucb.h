#pragma once

#include <vector>
#include <iostream>

namespace ranking {

// Very small-scale Linear Upper-Confidence-Bound model for contextual
// bandit problems. Optimised for on-device inference with tiny dimension.
class LinearUCB {
 public:
  explicit LinearUCB(int dim, double alpha = 0.8);

  // Predict expected reward w^T x.
  double predict(const std::vector<double>& x) const;

  // Compute UCB score w^T x + alpha * sqrt(x^T A^{-1} x).
  double ucbScore(const std::vector<double>& x) const;

  // Update internal state with observed reward.
  void update(const std::vector<double>& x, double reward);

  // Expose dimension.
  int dim() const { return dim_; }

  // Persist / restore (binary) helpers. Caller must ensure the stream is open
  // in the correct mode (std::ios::binary). The saved format is:
  //   int32 dim, double alpha,
  //   uint64 vec_size, vec<double> A_inv,
  //   uint64 vec_size, vec<double> b
  void save(std::ostream& out) const;
  static LinearUCB load(std::istream& in);

 private:
  int dim_;
  double alpha_;
  // We store the inverse covariance A^{-1} directly for fast computation.
  // Flattened row-major dim x dim matrix.
  std::vector<double> A_inv_;
  // Vector b = sum_{t} r_t x_t.
  std::vector<double> b_;
  // Cached weight vector w = A^{-1} b.
  std::vector<double> w_;

  void recomputeWeights();
};

}  // namespace ranking 