#include "feature_extractor.h"
#include "spotlight_metadata.h"

#include <algorithm>
#include <cctype>
#include <filesystem>
#include <unordered_set>
#include <unordered_map>
#include <chrono>
#include <cmath>

#include "core/util/debug.h"  // Logging macros

namespace fs = std::filesystem;

namespace ranking {

namespace {
// Helper: case-insensitive starts_with
bool startsWithIgnoreCase(const std::string &s, const std::string &prefix) {
  if (prefix.size() > s.size()) return false;
  for (size_t i = 0; i < prefix.size(); ++i) {
    if (std::tolower(s[i]) != std::tolower(prefix[i])) return false;
  }
  return true;
}

bool equalsIgnoreCase(const std::string &a, const std::string &b) {
  if (a.size() != b.size()) return false;
  for (size_t i = 0; i < a.size(); ++i) {
    if (std::tolower(a[i]) != std::tolower(b[i])) return false;
  }
  return true;
}

// Check if path is inside one of the known user directories.
bool isUserDir(const fs::path &p) {
  static const std::unordered_set<std::string> kDirs = {
      "Applications", "Desktop", "Documents", "Downloads", "Pictures", "Music",
      "Movies"};
  for (const auto &part : p) {
    auto dir = part.string();
    if (kDirs.find(dir) != kDirs.end()) {
      return true;
    }
  }
  return false;
}

bool isSystemDir(const fs::path &p) {
  static const std::vector<std::string> kPrefixes = {"/System", "/Library", "/usr",
                                                     "/bin", "/sbin"};
  auto str = p.string();
  for (const auto &pref : kPrefixes) {
    if (startsWithIgnoreCase(str, pref)) return true;
  }
  return false;
}
}  // namespace

// Static usage map to track local open counts (in-memory; persisted via bandit weights indirectly).
static std::unordered_map<std::string, int> g_open_counts;

void FeatureExtractor::recordOpen(const std::string &path) {
  auto &cnt = g_open_counts[path];
  cnt += 1;
}

std::vector<double> FeatureExtractor::extract(const std::string &path,
                                              const std::string &type,
                                              double base_score) {
  fs::path p(path);
  // Feature 0: bias
  double bias = 1.0;

  // Feature 1: base score (0..1)
  double base = base_score;

  // Feature 2: inverse directory depth
  int depth = 0;
  try {
    depth = std::distance(p.begin(), p.end());
  } catch (const std::exception &e) {
    depth = 10;  // fallback
  }
  double inv_depth = 1.0 / static_cast<double>(depth + 1);

  // Feature 3: is_user_dir
  double user_dir = isUserDir(p) ? 1.0 : 0.0;

  // Feature 4: is_system_dir
  double system_dir = isSystemDir(p) ? 1.0 : 0.0;

  // Feature 5: is_app_bundle
  bool is_app = equalsIgnoreCase(p.extension().string(), ".app") || equalsIgnoreCase(type, "app");
  double app_bundle = is_app ? 1.0 : 0.0;

  // Feature 6: is_file
  double is_file = equalsIgnoreCase(type, "file") ? 1.0 : 0.0;

  // Feature 7: is_website
  double is_website = equalsIgnoreCase(type, "website") ? 1.0 : 0.0;

  // Feature 8: is_chat
  double is_chat = equalsIgnoreCase(type, "chat") ? 1.0 : 0.0;

  // Feature 9: scaled_open_count (cap at 50, then /50)
  int opens = 0;
  auto it = g_open_counts.find(path);
  if (it != g_open_counts.end()) opens = it->second;
  double scaled_open = std::min(opens / 50.0, 1.0);

  // Feature 10: recency_score from file modification time (files only)
  double recency = 0.0;
  try {
    auto ftime = fs::last_write_time(p);
    auto sctp = std::chrono::time_point_cast<std::chrono::system_clock::duration>(
        ftime - fs::file_time_type::clock::now() + std::chrono::system_clock::now());
    auto age = std::chrono::system_clock::now() - sctp;
    double age_days = std::chrono::duration_cast<std::chrono::hours>(age).count() / 24.0;
    if (age_days < 0) age_days = 0;
    recency = std::exp(-age_days / 30.0);  // ~0.97 for same-day, ~0.37 for 30-day-old
  } catch (const std::exception &e) {
    recency = 0.0;
  }

  // Feature 11: last_used_score
  double last_used_score = 0.0;
  SpotlightStats stats = getSpotlightStats(path);
  if (stats.seconds_since_last_use >= 0) {
    last_used_score = std::exp(-stats.seconds_since_last_use / (86400.0 * 30.0));
  }

  // Feature 12: use_count_scaled
  double use_count_scaled = 0.0;
  if (stats.use_count >= 0) {
    use_count_scaled = std::min(stats.use_count / 100.0, 1.0);
  }

  // Feature 13: age_added_score
  double age_added_score = 0.0;
  if (stats.seconds_since_added >= 0) {
    age_added_score = std::exp(-stats.seconds_since_added / (86400.0 * 90.0));
  }

  return {bias, base,         inv_depth,  user_dir,   system_dir,  app_bundle,
          is_file, is_website, is_chat,    scaled_open, recency,
          last_used_score, use_count_scaled, age_added_score};
}

}  // namespace ranking 