#pragma once

#include <string>
#include <vector>

namespace ranking {

// Simple feature extractor to convert (path, type, base_score) into numerical feature vector.
class FeatureExtractor {
 public:
  // Extract features for the given item. The order of returned features is
  // fixed and must match the dimensionality expected by LinearUCB.
  // 0: bias (always 1)
  // 1: base score from upstream heuristic (0..1)
  // 2: inverse directory depth (1/(depth+1))
  // 3: is_user_dir (Applications, Desktop, Documents, Downloads, Pictures, Music, Movies)
  // 4: is_system_dir (/System, /Library, /usr, /bin, /sbin, etc.)
  // 5: is_app_bundle (path ends with .app OR type=="app")
  // 6: is_file (type=="file")
  // 7: is_website (type=="website")
  // 8: is_chat (type=="chat")
  // 9: scaled_open_count (min(open_count/50,1))
  // 10: recency_score = exp(-age_days/30)
  // 11: last_used_score = exp(-seconds_since_last_use / (86400*30))
  // 12: use_count_scaled = min(use_count/100,1)
  // 13: age_added_score = exp(-seconds_since_added / (86400*90))
  static std::vector<double> extract(const std::string &path,
                                     const std::string &type,
                                     double base_score);

  // Notify extractor that the given path was opened (positive reward).
  static void recordOpen(const std::string &path);

  // Dimension of the feature vector.
  static constexpr int kDim = 14;
};

}  // namespace ranking 