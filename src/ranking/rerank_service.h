#pragma once

#include <mutex>
#include <string>
#include <vector>
#include <unordered_map>
#include <shared_mutex>
#include <chrono>

#include "feature_extractor.h"
#include "linear_ucb.h"
#include "heuristic_scorer.h"

namespace ranking {

class RerankService {
 public:
  struct Item {
    std::string path;
    std::string type;
    double base_score;
  };

  static RerankService& shared();

  // Compute scores for each item (higher is better). The returned vector is
  // aligned with the input order.
  std::vector<double> scoreItems(const std::vector<Item>& items);

  // Record feedback for online learning.
  void recordReward(const Item& item, double reward);

  // Record that an item has been shown to the user (impression). May trigger
  // implicit zero-reward updates for stale impressions.
  void recordImpression(const Item& item);

  // Flush any pending impressions immediately assigning 0 reward (use when query changes).
  void flushPendingImpressions();

 private:
  RerankService();

  // Not copyable.
  RerankService(const RerankService&) = delete;
  RerankService& operator=(const RerankService&) = delete;

  FeatureExtractor extractor_;
  // Per-type contextual-bandit models – one LinearUCB instance per result category
  std::unordered_map<std::string, LinearUCB> bandits_;

  // Return a reference to the bandit corresponding to `type` (lazy-initialises if necessary).
  LinearUCB& banditForType(const std::string& type);

  // Persistence helpers
  void saveToDisk();
  void loadFromDisk();
  void flushStaleImpressions();

  struct ImpressionInfo {
    Item item;
    std::chrono::steady_clock::time_point ts;
  };
  std::unordered_map<std::string, ImpressionInfo> pending_impressions_;

  mutable std::shared_mutex mutex_;
};

}  // namespace ranking 