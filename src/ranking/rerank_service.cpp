#include "rerank_service.h"

#include <algorithm>
#include <vector>
#include <string>
#include <filesystem>
#include <fstream>
#include <shared_mutex>
#include <cstdlib>
#include <cstdint>
#include <chrono>

#include "core/util/debug.h"

namespace ranking {

namespace {
// Default exploration parameter for all bandits. Kept here for easy tuning.
constexpr double kAlphaDefault = 0.8;

// Known result types we want to pre-initialise. Others will be created lazily.
const std::vector<std::string> kPredefinedTypes = {"app", "chat", "website", "file"};

// Path to persistence file in user home directory.
std::filesystem::path persistencePath() {
  const char* home = std::getenv("HOME");
  std::filesystem::path base = home ? std::filesystem::path(home) : std::filesystem::path(".");
  return base / ".microlauncher_bandits.bin";
}
}  // namespace

RerankService& RerankService::shared() {
  static RerankService instance;
  return instance;
}

RerankService::RerankService() {
  // Eagerly create bandits for common categories to avoid map churn on hot path.
  bandits_.reserve(8);
  for (const auto& t : kPredefinedTypes) {
    bandits_.emplace(t, LinearUCB(FeatureExtractor::kDim, kAlphaDefault));
  }

  // Attempt to load persisted state.
  try {
    loadFromDisk();
  } catch (const std::exception& e) {
    ERR("RerankService: failed to load persisted state: " << e.what());
  }
}

LinearUCB& RerankService::banditForType(const std::string& type) {
  auto it = bandits_.find(type);
  if (it != bandits_.end()) {
    return it->second;
  }
  // Unknown type – create on demand with default params.
  auto [itNew, created] = bandits_.try_emplace(type, FeatureExtractor::kDim, kAlphaDefault);
  return itNew->second;
}

std::vector<double> RerankService::scoreItems(const std::vector<Item>& items) {
  std::shared_lock<std::shared_mutex> lock(mutex_);
  std::vector<double> scores;
  scores.reserve(items.size());
  for (const auto& it : items) {
    auto x = FeatureExtractor::extract(it.path, it.type, it.base_score);
    double score = banditForType(it.type).ucbScore(x);
    // Add small deterministic heuristic boost to stabilise early ranking.
    score += HeuristicScorer::boostFor(it.path, it.type);
    scores.push_back(score);
  }
  return scores;
}

void RerankService::recordReward(const Item& item, double reward) {
  std::unique_lock<std::shared_mutex> lock(mutex_);
  // If this was a click, remove from pending map to avoid double update.
  pending_impressions_.erase(item.path);
  auto x = FeatureExtractor::extract(item.path, item.type, item.base_score);
  banditForType(item.type).update(x, reward);

  if (reward > 0.0) {
    FeatureExtractor::recordOpen(item.path);
  }

  // Persist updated state (best-effort).
  try {
    saveToDisk();
  } catch (const std::exception& e) {
    ERR("RerankService: failed to save state: " << e.what());
  }
}

void RerankService::saveToDisk() {
  std::filesystem::path path = persistencePath();
  std::ofstream out(path, std::ios::binary | std::ios::trunc);
  if (!out) throw std::runtime_error("Cannot open persistence file for writing");

  uint64_t count = static_cast<uint64_t>(bandits_.size());
  out.write(reinterpret_cast<const char*>(&count), sizeof(count));
  for (const auto& [type, bandit] : bandits_) {
    uint32_t len = static_cast<uint32_t>(type.size());
    out.write(reinterpret_cast<const char*>(&len), sizeof(len));
    if (len) out.write(type.data(), len);
    bandit.save(out);
  }
}

void RerankService::loadFromDisk() {
  std::filesystem::path path = persistencePath();
  if (!std::filesystem::exists(path)) return;  // nothing to load

  std::ifstream in(path, std::ios::binary);
  if (!in) throw std::runtime_error("Cannot open persistence file for reading");

  uint64_t count;
  if (!in.read(reinterpret_cast<char*>(&count), sizeof(count))) {
    throw std::runtime_error("Failed to read bandit count");
  }

  for (uint64_t i = 0; i < count; ++i) {
    uint32_t len;
    if (!in.read(reinterpret_cast<char*>(&len), sizeof(len))) throw std::runtime_error("Failed to read type len");
    std::string type(len, '\0');
    if (len && !in.read(type.data(), len)) throw std::runtime_error("Failed to read type str");

    LinearUCB model = LinearUCB::load(in);
    if (model.dim() == FeatureExtractor::kDim) {
      bandits_.insert_or_assign(type, std::move(model));
    }
  }
}

void RerankService::flushStaleImpressions() {
  const auto now = std::chrono::steady_clock::now();
  const std::chrono::seconds window(10);  // 10-second grace period

  for (auto it = pending_impressions_.begin(); it != pending_impressions_.end();) {
    if (now - it->second.ts > window) {
      const Item& itm = it->second.item;
      auto x = FeatureExtractor::extract(itm.path, itm.type, itm.base_score);
      banditForType(itm.type).update(x, 0.0);  // implicit negative (no click)
      it = pending_impressions_.erase(it);
    } else {
      ++it;
    }
  }
}

void RerankService::recordImpression(const Item& item) {
  std::unique_lock<std::shared_mutex> lock(mutex_);
  flushStaleImpressions();
  pending_impressions_[item.path] = {item, std::chrono::steady_clock::now()};
}

void RerankService::flushPendingImpressions() {
  std::unique_lock<std::shared_mutex> lock(mutex_);
  for (auto &kv : pending_impressions_) {
    const Item &itm = kv.second.item;
    auto x = FeatureExtractor::extract(itm.path, itm.type, itm.base_score);
    banditForType(itm.type).update(x, 0.0);
  }
  pending_impressions_.clear();
}

}  // namespace ranking 