#include <chrono>
#include <ctime>
#include <filesystem>
#include <iostream>
#include <map>
#include <memory>
#include <string>
#include <thread>
#include <cstdlib>

#ifdef __APPLE__
// Include macOS-specific headers
#include <dispatch/dispatch.h>
// Add CoreFoundation for dispatch support
#include <CoreFoundation/CoreFoundation.h>
#endif

#include "core/index/app_index.h"
#include "core/scanner/app_scanner_interface.h"
#include "core/search/search_cache.h"
#include "core/search/search_history.h"

#include "core/config/config_manager.h"
#include "core/platform/macos/macos_platform.h"
#include "core/util/debug.h"
#include "ui/common/result_item.h"

#include "core/context/context.h"
#include "core/context/context_providers.h"

// Include UI headers
#include "ui/chat/chat_ui_interface.h"

#include "core/history/history_manager.h"
#include "core/app_context.h"
#include "core/util/memory_tracker_registry.h"

#include "ui/macos/macos_ui.h"

#include "core/interfaces/iconfig_manager.h"
#include "core/interfaces/iapp_index.h"

#include "ui/macos/browser_history_importer.h"

#include "core/util/memory_tracker.h"

#include "core/llm/model_factory.h"
#include "core/llm/provider_catalog.h"
#include "core/llm/model_registry.h"
#include "core/llm/provider_registration.h"

#include "core/util/async_logger.h"
#include "core/foundation/registry.h"
#include "core/memory/arena_allocator_service.h"
#include "core/async/executor_service.h"
#include "core/events/event_bus_service.h"
#include "core/diagnostics/diagnostics_service.h"

#include "core/plugins/runtime_manager_service.h"
#include "core/mcp/mcp_client_service.h"

#include "core/config/config_service.h"
#include "core/history/history_service.h"
#include "core/index/index_service.h"
#include "core/llm/llm_factory_service.h"

#include "core/security/verification_store.hh"
#include "core/security/hotpatch_table_service.h"
#if KAI_ENABLE_VPATCH
#include "core/security/hotpatch_verifier_table.hh"
#endif

#include "snapshot/probe_cache_service.hh"
#include "snapshot/verdict_store.hh"

using namespace launcher::core;
using namespace launcher::ui;

int main([[maybe_unused]] int argc, [[maybe_unused]] char* argv[]) {
    try {
        // Measure initialization time
        auto startTime = std::chrono::high_resolution_clock::now();

        // Log application start
        INF("MicroLauncher v0.1.0");

        // -----------------------------------------------------------------
        // Core service registry bootstrap ---------------------------------
        // -----------------------------------------------------------------

        launcher::core::foundation::ServiceRegistry registry;

        // Instantiate core services using constructor injection. They register
        // themselves into the registry via ServiceBase, so no explicit
        // registerService() calls are necessary.
        launcher::core::memory::ArenaAllocatorSvc arenaSvc{registry};
        launcher::core::async::ExecutorService    executorSvc{registry};
        static launcher::core::events::EventBusService eventBusSvc{registry};
        static launcher::core::diagnostics::DiagnosticsService diagSvc{registry};

        // ProbeCacheSvc – provides multi-tier runtime probe cache
        static launcher::snapshot::ProbeCacheSvc probeCacheSvc{registry, diagSvc};

        executorSvc.setDiagnostics(&diagSvc);
        eventBusSvc.setDiagnostics(&diagSvc);

        static launcher::core::security::VerificationStore verificationStore;

        // L2 VerdictStore persists verification results to FlatSnapshot so that
        // second cold-start avoids expensive Security.framework calls. Store
        // location lives under user cache directory for simplicity.
        std::filesystem::path verdict_dir = std::filesystem::path(std::getenv("HOME")) /
                                            ".microlauncher/verdict_cache";
        std::filesystem::create_directories(verdict_dir);
        static launcher::snapshot::VerdictStore verdictStore{verdict_dir};
        verificationStore.attachVerdictStore(verdictStore);

        // Runtime manager must be available for plugin loading later
        static launcher::core::plugins::RuntimeManagerSvc pluginMgr{registry, verificationStore};

        // MCP client service (placeholder) – now constructor-injected
        static launcher::core::mcp::McpClientService mcpSvc{registry};

        static launcher::core::config::ConfigService configSvc{registry};
        static launcher::core::history::HistoryService historySvc{registry};
        static launcher::core::index::IndexService indexSvcSvc{registry};
        static launcher::core::llm::LlmFactoryService llmFactorySvc{registry};

        registry.startAll();

        // Plugin loading deferred until configuration is ready (see below)

        // Ensure services are stopped on all exit paths
        struct RegistryGuard {
            launcher::core::foundation::ServiceRegistry& reg;
            ~RegistryGuard() { reg.stopAll(); }
        } regGuard{registry};

        // -----------------------------------------------------------------
        // Construct runtime service objects (no singletons!)
        // -----------------------------------------------------------------

        auto cfg = std::make_shared<ConfigManager>();
        if (!cfg->initialize()) {
            ERR("Failed to initialize configuration");
            return 1;
        }

        // Inject config into RuntimeManagerSvc before loading plugins so that
        // disabled-plugin settings are respected.
        pluginMgr.setConfig(cfg.get());

        // Now load plugins (instantiates NullRuntime for Slice-1 by default)
        {
            auto loadRes = pluginMgr.loadPlugins();
            if (!loadRes) {
                ERR("Failed to load plugins");
            }
        }

        auto indexPtr = std::make_shared<AppIndex>();

        // Instantiate a memory tracker and register mandatory global services early
        auto memTrackerPtr = std::make_shared<MemoryTracker>();

        // Register the global MemoryTracker to ensure operator new/delete hooks work.
        launcher::core::registerMemoryTracker(memTrackerPtr.get());

        // Create shared services context and wire default providers
        auto appContext = std::make_shared<launcher::core::AppContext>();

        // Store strongly-owned service instances directly – no global
        // singletons or sneaky empty-deleter wrappers necessary.
        appContext->configManager = cfg;
        appContext->appIndex     = indexPtr;

        // Bridge static service instances into AppContext using aliasing
        // shared_ptrs with no-op deleters so lifetime remains managed by the
        // static objects themselves.
        appContext->eventBus = std::shared_ptr<launcher::core::events::EventBusService>(&eventBusSvc, [](launcher::core::events::EventBusService*){});
        appContext->runtimeManager = std::shared_ptr<launcher::core::plugins::RuntimeManagerSvc>(&pluginMgr, [](launcher::core::plugins::RuntimeManagerSvc*){});

        // Browser history importer
        auto browserHistPtr = std::make_shared<launcher::ui::BrowserHistoryImporter>();
        appContext->browserHistory = browserHistPtr;

        // History manager (dependency-injected, no singleton)
        auto historyManager = std::make_shared<launcher::core::HistoryManager>(*cfg, browserHistPtr.get());
        // bool histEnabled = cfg->getBool("history.enabled", true);
        // historyManager->setEnabled(histEnabled);

        appContext->historyManager = historyManager;

        // Create a context manager instance for dependency injection.
        auto contextMgr = std::make_shared<ContextManager>();
        appContext->contextManager = contextMgr;

        // Initialize context providers (might be needed by UI or other components)
        auto &contextManager = *contextMgr;
        auto contextProviders = ContextProviderFactory::createAllProviders();
        for (auto& provider : contextProviders) {
            contextManager.registerProvider(std::move(provider));
        }

        // Convenience alias
        auto& appIndex = *indexPtr;

        // -----------------------------------------------------------------
        // Hot-patch verifier table (optional, config-driven)
        // -----------------------------------------------------------------
#if KAI_ENABLE_VPATCH
        static launcher::core::security::HotpatchTableSvc hotpatchSvc{registry, verificationStore};
#endif

        // Initialize platform (macOS specific)
        auto platform = std::make_unique<launcher::core::MacOSPlatform>(*cfg);

        // ---------------------------
        // LLM subsystem wiring (DI)
        // ---------------------------
        auto modelRegistryPtr = std::shared_ptr<ModelRegistry>(&ModelRegistry::global(), [](ModelRegistry*){});
        registerBuiltInProviders(ModelRegistry::global());

        auto providerCatalog = std::make_shared<ProviderCatalog>(*cfg, *modelRegistryPtr);
        auto modelFactory      = std::make_shared<ModelFactory>(*cfg, modelRegistryPtr);

        // Expose through AppContext for any interested component (UI, plugins…)
        appContext->modelRegistry   = modelRegistryPtr;
        appContext->providerCatalog = providerCatalog;
        appContext->modelFactory    = modelFactory;

        // Enable caching for the app index
        appIndex.setCaching(true, 100, std::chrono::minutes(10));

        // Initialize scanner
        auto scanner = platform->createAppScanner();
        if (!scanner) {
            ERR("Failed to create app scanner");
            return 1;
        }

        // Initialize search history
        SearchHistory searchHistory;

        // Initialize search cache
        SearchCache searchCache;

        // Initialize UI (macOS specific) with injected context
        auto ui = std::make_shared<launcher::ui::MacOSUI>(appContext);

        // Update UI with callbacks
        ui->initialize(
            /* launch */ [&](const launcher::ui::ResultItem& item) {
                DBG("Launching application: " << item.name);
                DBG("Path: " << item.path);

                auto launchRes = platform->launchApplication(item.path);
                if (launchRes) {
                    DBG("Application launched successfully");
                } else {
                    ERR("Failed to launch application: " << launchRes.error());
                }
                // Record launch in history
                if (historyManager) {
                    historyManager->recordLaunch(item);
                }
            });

        // Try to load cached index first
        appIndex.loadFromFile(AppIndex::getDefaultIndexPath());
        DBG("Loaded application index from cache");

        // Show launcher bar immediately
        ui->showLauncherBar();

        // Measure time to UI display
        auto uiDisplayTime  = std::chrono::high_resolution_clock::now();
        auto uiDisplayDurMs =
            std::chrono::duration_cast<std::chrono::milliseconds>(uiDisplayTime - startTime);
        DBG("Time to UI display: " << std::to_string(uiDisplayDurMs.count()) << "ms");

        // Start background scanning
        std::weak_ptr<launcher::ui::MacOSUI> weak_ui = ui;
        std::thread scanThread([&]() {
            DBG("Starting background scan for applications...");

            // Scan for applications
            auto scanStartTime = std::chrono::high_resolution_clock::now();
            auto apps = scanner->scanForApps();
            auto scanEndTime = std::chrono::high_resolution_clock::now();
            auto scanDuration =
                std::chrono::duration_cast<std::chrono::milliseconds>(scanEndTime - scanStartTime);

            DBG("Found " << std::to_string(apps.size()) << " applications in " <<
                                        std::to_string(scanDuration.count()) << "ms");

            // Add apps to index
            for (const auto& app : apps) {
                appIndex.addApp(app);
            }

            // Update UI with all applications - dispatch to main thread
            std::vector<launcher::ui::ResultItem> uiResults;
            auto allApps = appIndex.getAllApplications();
            for (const auto& app : allApps) {
                uiResults.emplace_back(app.name, app.path, app.iconPath, app.score);
            }

            // Use platform-specific method to update UI on main thread
            dispatch_async(dispatch_get_main_queue(), ^{
              if (auto shared_ui = weak_ui.lock()) {
                  shared_ui->updateResults(uiResults);
              }
            });

            // Save index for next launch
            appIndex.saveToFile(AppIndex::getDefaultIndexPath());
            DBG("Saved application index to cache");

            // Enable incremental updates
            appIndex.enableIncrementalUpdates(scanner);
        });

        // Run UI event loop
        int result = ui->run();

        if (scanThread.joinable()) {
            scanThread.join();
        }

        // Flush & stop background logger before exit
        ::launcher::core::util::AsyncLogger::instance().shutdown();

        return result;
    } catch (const std::exception& e) {
        ERR("Error: " + std::string(e.what()));
        return 1;
    }
}