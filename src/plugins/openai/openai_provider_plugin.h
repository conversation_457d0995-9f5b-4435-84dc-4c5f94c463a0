#pragma once

#include "../../core/llm/provider_adapter_base.h"
#include "../../core/providers/openai/openai_model.h"
#include <unordered_map>
#include <vector>
#include <memory>
#include <chrono>
#include <shared_mutex>

namespace launcher::plugins::openai {

namespace llm = kai::llm;

class OpenAiProviderPlugin final : public llm::ProviderAdapterBase<OpenAiProviderPlugin> {
 public:
    static constexpr char kProviderId[] = "openai";
    static constexpr llm::CapabilityMask kCaps{.lo = 0x1, .hi = 0}; // net.http outbound

    // ProviderAdapterBase already gives id() and capabilities().

    // -------- IProvider API -----------------------------------------
    llm::ExpectedT<std::span<const llm::ModelInfo>>
    models() const noexcept override;

    llm::ExpectedT<llm::Response>
    complete(std::string_view prompt, std::string_view model) noexcept override;

    llm::ExpectedT< llm::AsyncStreamT< llm::ExpectedT<llm::Delta> > >
    stream(std::string_view prompt, std::string_view model) override;

    // Lifecycle: warm model list if env instructs.
    launcher::core::util::Result<void> init() override;

 private:
    using TimePoint = std::chrono::steady_clock::time_point;
    mutable std::vector<llm::ModelInfo> models_cache_;
    mutable TimePoint cache_expiry_{};
    static constexpr std::chrono::seconds kTtl{600};

    mutable std::shared_mutex models_mtx_;

    // Model object cache – one per id
    std::unordered_map<std::string, std::shared_ptr<launcher::core::OpenAIModel>> model_objs_;
};

} // namespace launcher::plugins::openai 