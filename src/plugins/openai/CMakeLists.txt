add_library(openai SHARED
    openai_provider_plugin.cpp
)

find_package(Python3 COMPONENTS Interpreter REQUIRED)

# Seatbelt profile embed --------------------------------------------------
set(SEATBELT_SRC ${CMAKE_SOURCE_DIR}/resources/seatbelts/openai.sb)
set(SEATBELT_INC ${CMAKE_CURRENT_BINARY_DIR}/openai_sb.inc)
add_custom_command(
    OUTPUT ${SEATBELT_INC}
    COMMAND ${Python3_EXECUTABLE} ${CMAKE_SOURCE_DIR}/tools/kai-seatbelt-gen.py
            ${SEATBELT_SRC} ${SEATBELT_INC} openai
    DEPENDS ${SEATBELT_SRC} ${CMAKE_SOURCE_DIR}/tools/kai-seatbelt-gen.py
    COMMENT "Generating embedded seatbelt blob for OpenAI plugin"
    VERBATIM)
add_custom_target(openai_seatbelt_gen DEPENDS ${SEATBELT_INC})
add_dependencies(openai openai_seatbelt_gen)

target_sources(openai PRIVATE ${SEATBELT_INC})

set_target_properties(openai PROPERTIES
    PREFIX ""
    OUTPUT_NAME "openai"
    CXX_STANDARD 20
)

# Include dirs
 target_include_directories(openai PRIVATE
    ../../core
    ${CMAKE_SOURCE_DIR}/src
    ${CMAKE_CURRENT_BINARY_DIR}
 )

 target_link_libraries(openai PRIVATE
    fmt http_client core_util core_foundation core simdjson
 )

# Copy to Plugins directory
set(PLUGIN_DEST "${CMAKE_RUNTIME_OUTPUT_DIRECTORY}/Plugins")
add_custom_command(TARGET openai POST_BUILD
    COMMAND ${CMAKE_COMMAND} -E make_directory ${PLUGIN_DEST}
    COMMAND ${CMAKE_COMMAND} -E copy $<TARGET_FILE:openai> ${PLUGIN_DEST}
    COMMAND ${CMAKE_COMMAND} -E copy ${CMAKE_CURRENT_SOURCE_DIR}/manifest.toml ${PLUGIN_DEST}/manifest.toml
) 