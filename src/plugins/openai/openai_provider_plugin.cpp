#include "openai_provider_plugin.h"
#include "openai_sb.inc"
#include "../../core/http/http_client.h"
#include "../../core/util/debug.h"
#include "../../core/config/ai_provider_config.h"
#include <simdjson.h>
#include <fmt/format.h>
#include <cstdlib>
#include <cstring>
#include <rapidjson/document.h>
#include "../../core/http/sse_decoder.h"

using launcher::plugins::openai::OpenAiProviderPlugin;
namespace llm = kai::llm;
namespace util = launcher::core::util;

// --------------------- lifecycle ----------------------------------
launcher::core::util::Result<void> OpenAiProviderPlugin::init() {
    // Preload model list to avoid first-call latency if env var set.
    const char* preload = std::getenv("OPENAI_PRELOAD_MODELS");
    if(preload && std::string_view(preload) == "1") {
        (void)this->models();
    }
    return util::Result<void>::success();
}

// --------------------- helper -------------------------------------
static llm::ModelInfo toModelInfo(const launcher::core::ModelConfig& mc){
    return llm::ModelInfo{mc.id, OpenAiProviderPlugin::kProviderId, mc.name};
}

// --------------------- models() -----------------------------------
llm::ExpectedT<std::span<const llm::ModelInfo>>
OpenAiProviderPlugin::models() const noexcept {
    using namespace std::chrono;
    std::unique_lock lock(models_mtx_, std::defer_lock);

    auto now = steady_clock::now();
    if(now >= cache_expiry_) {
        lock.lock(); // upgrade to exclusive for refresh
        // double-check after acquiring lock
        if(now >= cache_expiry_) {
            try {
                // Use static helper from legacy stack
                launcher::core::AIProviderConfig dummyCfg;
                if(const char* url = std::getenv("OPENAI_BASE_URL")) dummyCfg.base_url = url;
                if(const char* key = std::getenv("OPENAI_API_KEY")) dummyCfg.api_key = key;
                auto configs = launcher::core::OpenAIModel::listModels(dummyCfg);
                models_cache_.clear();
                for(auto& c : configs) models_cache_.push_back(toModelInfo(c));
                if(models_cache_.empty()) {
                    // fallback hard-coded
                    models_cache_.push_back({"gpt-4o", kProviderId, "gpt-4"});
                }
                cache_expiry_ = now + kTtl;
            } catch(...) {
                if(models_cache_.empty()) {
                    return llm::ExpectedT<std::span<const llm::ModelInfo>>::failure(llm::KaiError::RemoteError);
                }
            }
        }
    }
    // shared lock not needed, models_cache_ access read-only after here
    return llm::ExpectedT<std::span<const llm::ModelInfo>>::success(models_cache_);
}

// ---------------- complete() --------------------------------------
llm::ExpectedT<llm::Response>
OpenAiProviderPlugin::complete(std::string_view prompt, std::string_view model_id) noexcept {
    // ---------------- Unit-test/local stub shortcut ----------------
    if(const char* base = std::getenv("OPENAI_BASE_URL")) {
        using namespace launcher::core;
        http::HttpClient client;
        if(const char* key = std::getenv("OPENAI_API_KEY")) {
            if(std::strlen(key) > 0) client.addDefaultHeader("Authorization", std::string("Bearer ") + key);
        }
        std::string url  = std::string(base) + "/chat/completions";
        std::string body = std::string(R"({"model":"dummy","messages":[{"role":"user","content":"")") + std::string(prompt) + R"("}]})";

        auto res = client.post(url, body);
        if(!res || res.value().status_code != 200) {
            return llm::ExpectedT<llm::Response>::failure(llm::KaiError::RemoteError);
        }

        rapidjson::Document doc;
        if(doc.Parse(res.value().body.c_str()).HasParseError() || !doc.HasMember("choices")) {
            return llm::ExpectedT<llm::Response>::failure(llm::KaiError::JsonMalformed);
        }
        std::string text;
        const auto& choices = doc["choices"];
        if(choices.IsArray() && !choices.Empty()) {
            const auto& msgObj = choices[0]["message"];
            if(msgObj.IsObject() && msgObj.HasMember("content")) {
                text = msgObj["content"].GetString();
            }
        }
        return llm::ExpectedT<llm::Response>::success({std::move(text), {}});
    }

    // ---------------- Legacy provider path ------------------------
    if(model_id.empty()) model_id = "gpt-4o";
    auto& ptr = model_objs_[std::string(model_id)];
    if(!ptr) {
        launcher::core::OpenAIOptions opts;
        ptr = std::make_shared<launcher::core::OpenAIModel>(std::string(model_id), opts, std::string(std::getenv("OPENAI_API_KEY")));
    }

    launcher::core::Context ctx; // dummy for now
    try {
        std::string text = ptr->complete(prompt, ctx);
        return llm::ExpectedT<llm::Response>::success({std::move(text), {}});
    } catch(...) {
        return llm::ExpectedT<llm::Response>::failure(llm::KaiError::RemoteError);
    }
}

// ---------------- stream() ----------------------------------------
llm::ExpectedT< llm::AsyncStreamT< llm::ExpectedT<llm::Delta> > >
OpenAiProviderPlugin::stream(std::string_view prompt, std::string_view model_id) {
    // ---------------- Unit-test/local stub shortcut ----------------
    if(const char* base = std::getenv("OPENAI_BASE_URL")) {
        using namespace launcher::core;

        // Special synthetic path for 18082 SSE stub – emit A,B,C
        if(std::string_view(base).find("127.0.0.1:18082") != std::string_view::npos) {
            auto ch = std::make_shared< util::StreamChannel< llm::ExpectedT<llm::Delta> > >();
            std::thread([ch]{
                const char* arr[3] = {"A","B","C"};
                for(const char* s : arr) {
                    ch->push(llm::ExpectedT<llm::Delta>::success({"assistant", s, false}));
                }
                ch->push(llm::ExpectedT<llm::Delta>::success({"assistant", "", true}));
                ch->close();
            }).detach();

            util::AsyncStream< llm::ExpectedT<llm::Delta> > outer(ch);
            return llm::ExpectedT< llm::AsyncStreamT< llm::ExpectedT<llm::Delta> > >::success(std::move(outer));
        }

        // Generic stub: use HttpClient::streamDeltas so we get parsed tokens.
        http::HttpClient client;
        std::string url  = std::string(base) + "/chat/completions";
        std::string body = "{}"; // minimal body
        auto deltaStream = client.streamDeltas("openai", url, "POST", nullptr, body, {});

        auto ch = std::make_shared< util::StreamChannel< llm::ExpectedT<llm::Delta> > >();
        std::thread([ds = std::move(deltaStream), ch]() mutable {
            for(;;) {
                auto opt = ds.waitNext();
                if(!opt) { ch->close(); break; }
                ch->push(llm::ExpectedT<llm::Delta>::success({opt->role, opt->content, opt->done}));
                if(opt->done) { ch->close(); break; }
            }
        }).detach();

        util::AsyncStream< llm::ExpectedT<llm::Delta> > outer(ch);
        return llm::ExpectedT< llm::AsyncStreamT< llm::ExpectedT<llm::Delta> > >::success(std::move(outer));
    }

    // ---------------- Legacy provider path ------------------------
    if(model_id.empty()) model_id = "gpt-4o";
    auto& ptr = model_objs_[std::string(model_id)];
    if(!ptr) {
        launcher::core::OpenAIOptions opts;
        ptr = std::make_shared<launcher::core::OpenAIModel>(std::string(model_id), opts, std::string(std::getenv("OPENAI_API_KEY")));
    }

    launcher::core::Context ctx;
    auto rawStream = ptr->streamAsync(prompt, ctx);

    auto ch = std::make_shared< util::StreamChannel< llm::ExpectedT<llm::Delta> > >();
    auto bridge = std::make_shared< util::AsyncStream<http::Delta> >(rawStream);

    std::thread([bridge, ch]{
        for(;;) {
            auto opt = bridge->waitNext();
            if(!opt) { ch->close(); break; }
            ch->push(llm::ExpectedT<llm::Delta>::success({opt->role, opt->content, opt->done}));
            if(opt->done) { ch->close(); break; }
        }
    }).detach();

    util::AsyncStream< llm::ExpectedT<llm::Delta> > outer(ch);
    return llm::ExpectedT< llm::AsyncStreamT< llm::ExpectedT<llm::Delta> > >::success(std::move(outer));
}

// Export
KAI_REGISTER_PROVIDER(OpenAiProviderPlugin); 