add_kai_plugin(sample_null_plugin sample_null.cpp)

# Ensure predictable file name (no lib prefix) so PluginManager svc finds it.
set_target_properties(sample_null_plugin PROPERTIES
    PREFIX ""
    OUTPUT_NAME "sample_null"
)

target_include_directories(sample_null_plugin PRIVATE
    ${CMAKE_SOURCE_DIR}/src
)

# Link allocator shim so operator new/delete are routed through Kai arena
target_link_libraries(sample_null_plugin PRIVATE kai_plugin_alloc_shim)

# Link to core library for allocator helpers
target_link_libraries(sample_null_plugin PRIVATE core)

# Copy into Plugins dir inside build output for manual runs
set(PLUGIN_DEST "${CMAKE_RUNTIME_OUTPUT_DIRECTORY}/Plugins")
add_custom_command(TARGET sample_null_plugin POST_BUILD
    COMMAND ${CMAKE_COMMAND} -E make_directory ${PLUGIN_DEST}
    COMMAND ${CMAKE_COMMAND} -E copy $<TARGET_FILE:sample_null_plugin> ${PLUGIN_DEST}
    COMMENT "Installing sample_null into ${PLUGIN_DEST}") 