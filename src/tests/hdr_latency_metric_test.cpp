#include "core/diagnostics/hdr_latency.hh"
#include "core/diagnostics/metrics_exporter.hh"
#include <catch2/catch_test_macros.hpp>
#include <tuple>

using namespace launcher::core;
using namespace launcher::core::diagnostics;
using namespace launcher::core::diagnostics::latency;

TEST_CASE("hdr_latency buckets populated", "[metrics][histogram]") {
    // 1. Record two verifier latencies and one runtime scan latency.
    recordVerifier(5.0);
    recordVerifier(8.0);
    recordRuntimeScan(100.0);

    // 2. Build tuple of histogram sources for exporter.
    auto tup = std::make_tuple(&g_verifier_metric_source, &g_scan_metric_source);

    int verifier_count = 0;
    int scan_count     = 0;

    export_metrics(tup, [&] <typename SrcT> (const auto& stats) {
        if constexpr (std::is_same_v<SrcT, VerifierMetricSource>) {
            REQUIRE(stats.buckets.size() > 0);
            uint64_t total = 0;
            for (auto& b : stats.buckets) total += b.count;
            verifier_count = static_cast<int>(total);
        } else if constexpr (std::is_same_v<SrcT, ScanMetricSource>) {
            uint64_t total = 0;
            for (auto& b : stats.buckets) total += b.count;
            scan_count = static_cast<int>(total);
        }
    });

    REQUIRE(verifier_count == 2);
    REQUIRE(scan_count == 1);
} 