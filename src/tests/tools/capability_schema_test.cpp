#include <catch2/catch_test_macros.hpp>
#include <filesystem>
#include <fstream>
#include <nlohmann/json.hpp>
#include <algorithm>

#include "tools/kai-capability-gen/capability_schema.hh"

namespace fs = std::filesystem;
using kai::capability_schema::extractCapabilities;

TEST_CASE("extractCapabilities parses canonical manifest.schema.json", "[capability-schema]") {
    const fs::path schema_path = fs::path{PROJECT_SOURCE_DIR} / "manifest.schema.json";
    REQUIRE(fs::exists(schema_path));

    nlohmann::json schema_json;
    {
        std::ifstream in(schema_path);
        REQUIRE(in);
        in >> schema_json;
    }

    auto caps = extractCapabilities(schema_json);

    REQUIRE(!caps.empty());
    // Ensure deterministic ordering (sorted) and presence of a known token.
    REQUIRE(std::is_sorted(caps.begin(), caps.end()));
    REQUIRE(std::find(caps.begin(), caps.end(), "read_fs") != caps.end());
}

TEST_CASE("extractCapabilities throws on malformed schema", "[capability-schema][negative]") {
    nlohmann::json bad_json = nlohmann::json::object(); // missing everything
    REQUIRE_THROWS_AS(extractCapabilities(bad_json), std::runtime_error);

    // Missing enum array
    nlohmann::json no_enum = {
        {"definitions", {
            {"capability", {}}}}
    };
    REQUIRE_THROWS_AS(extractCapabilities(no_enum), std::runtime_error);
} 