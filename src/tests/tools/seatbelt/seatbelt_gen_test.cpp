#include <catch2/catch_test_macros.hpp>
#include <filesystem>
#include <fstream>
#include <string>

#include "seatbelt_gen.h" // from tools include dir

namespace fs = std::filesystem;

TEST_CASE("Seatbelt generator matches golden", "[seatbelt-gen]") {
    const fs::path project_root = fs::path{PROJECT_SOURCE_DIR};
    const fs::path manifest_path = project_root / "src" / "tests" / "fixtures" / "sample_manifest.toml";
    const fs::path golden_path   = project_root / "src" / "tests" / "golden" / "sample_manifest.sb";

    REQUIRE(fs::exists(manifest_path));
    REQUIRE(fs::exists(golden_path));

    std::string error;
    std::string generated = kai::tools::generateSeatbeltProfile(manifest_path, error);
    REQUIRE(error.empty());

    std::ifstream in(golden_path);
    REQUIRE(in);
    std::string golden((std::istreambuf_iterator<char>(in)), {});

    // Trim trailing whitespace for robust comparison
    auto trim = [](std::string& s) {
        while (!s.empty() && (s.back() == ' ' || s.back() == '\n' || s.back() == '\r' || s.back() == '\t')) {
            s.pop_back();
        }
    };
    std::string gen_copy = generated;
    std::string gold_copy = golden;
    trim(gen_copy);
    trim(gold_copy);

    REQUIRE(gen_copy == gold_copy);
}

TEST_CASE("Seatbelt generator fails when sandbox table missing", "[seatbelt-gen][negative]") {
    const fs::path root = fs::path{PROJECT_SOURCE_DIR};
    const fs::path manifest = root / "src" / "tests" / "fixtures" / "invalid_no_sandbox.toml";
    std::string err;
    auto out = kai::tools::generateSeatbeltProfile(manifest, err);
    REQUIRE(out.empty());
    REQUIRE_FALSE(err.empty());
}

TEST_CASE("Seatbelt generator fails when sandbox empty", "[seatbelt-gen][negative]") {
    const fs::path root = fs::path{PROJECT_SOURCE_DIR};
    const fs::path manifest = root / "src" / "tests" / "fixtures" / "invalid_empty_sandbox.toml";
    std::string err;
    auto out = kai::tools::generateSeatbeltProfile(manifest, err);
    REQUIRE(out.empty());
    REQUIRE_FALSE(err.empty());
} 