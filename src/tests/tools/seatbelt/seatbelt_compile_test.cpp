#include <catch2/catch_test_macros.hpp>
#include <filesystem>
#include <string>

#if defined(__APPLE__)
#include <dlfcn.h>
#endif

namespace fs = std::filesystem;

#if defined(__APPLE__)
TEST_CASE("Seatbelt profile compiles via libsandbox", "[seatbelt-compile]") {
    const fs::path root = fs::path{PROJECT_SOURCE_DIR};
    const fs::path sb_path = root / "src" / "tests" / "golden" / "sample_manifest.sb";
    REQUIRE(fs::exists(sb_path));

    void* libSandbox = dlopen("/usr/lib/system/libsystem_sandbox.dylib", RTLD_LAZY);
    if (!libSandbox) {
        WARN("libsandbox not available; skipping compile test");
        SUCCEED();
        return;
    }

    using CompileFn = int (*)(const char*, uint64_t, char**, char**);
    auto* compile_fn = reinterpret_cast<CompileFn>(dlsym(libSandbox, "sandbox_compile_file"));
    if (!compile_fn) {
        WARN("sandbox_compile_file symbol missing; skipping compile test");
        dlclose(libSandbox);
        SUCCEED();
        return;
    }

    char* blob{nullptr};
    char* err{nullptr};
    int rc = compile_fn(sb_path.c_str(), /*flags=*/0, &blob, &err);

    if (err) {
        std::string msg(err);
        free(err);
        FAIL_CHECK("Seatbelt compile error: " + msg);
    }

    if (blob) free(blob);
    dlclose(libSandbox);

    REQUIRE(rc == 0);
}
#else
TEST_CASE("Seatbelt compile stub (non-Apple)", "[seatbelt-compile]") {
    SUCCEED();
}
#endif 