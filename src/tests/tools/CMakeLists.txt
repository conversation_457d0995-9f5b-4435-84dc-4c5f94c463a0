add_executable(seatbelt_gen_test
    seatbelt/seatbelt_gen_test.cpp
    seatbelt/seatbelt_compile_test.cpp
)

target_include_directories(seatbelt_gen_test PRIVATE
    ${CMAKE_SOURCE_DIR}/src
    ${CMAKE_SOURCE_DIR}/src/tools/kai-seatbelt-gen
)

target_link_libraries(seatbelt_gen_test PRIVATE seatbelt_gen_lib Catch2::Catch2WithMain)

target_compile_definitions(seatbelt_gen_test PRIVATE PROJECT_SOURCE_DIR="${CMAKE_SOURCE_DIR}")

add_test(NAME SeatbeltGenTest COMMAND seatbelt_gen_test)

# Add service_gen tests
add_subdirectory(service_gen)

add_executable(capability_schema_test
    capability_schema_test.cpp
)

target_include_directories(capability_schema_test PRIVATE
    ${CMAKE_SOURCE_DIR}/src
)

target_link_libraries(capability_schema_test PRIVATE Catch2::Catch2WithMain
    nlohmann_json::nlohmann_json
)

target_compile_definitions(capability_schema_test PRIVATE PROJECT_SOURCE_DIR="${CMAKE_SOURCE_DIR}")

add_test(NAME CapabilitySchemaTest COMMAND capability_schema_test) 
