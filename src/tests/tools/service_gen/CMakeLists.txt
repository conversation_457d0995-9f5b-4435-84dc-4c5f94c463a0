add_executable(service_gen_tests
    service_gen_test.cpp
)

# Dependencies ---------------------------------------------------
include(FetchContent)

# yaml-cpp (header + static lib)
FetchContent_Declare(
    yaml_cpp
    GIT_REPOSITORY https://github.com/jbeder/yaml-cpp.git
    GIT_TAG 0.8.0
)
FetchContent_MakeAvailable(yaml_cpp)

FetchContent_Declare(
    bbhash
    GIT_REPOSITORY https://github.com/rizkg/BBHash.git
    GIT_TAG master
)
FetchContent_MakeAvailable(bbhash)

target_include_directories(service_gen_tests PRIVATE
    ${CMAKE_SOURCE_DIR}/src
    ${CMAKE_SOURCE_DIR}/src/tools/kai-service-gen
    ${yaml_cpp_SOURCE_DIR}/include
    ${bbhash_SOURCE_DIR})

target_link_libraries(service_gen_tests PRIVATE Catch2::Catch2WithMain fmt::fmt yaml-cpp)

target_compile_definitions(service_gen_tests PRIVATE PROJECT_SOURCE_DIR="${CMAKE_SOURCE_DIR}")

add_test(NAME ServiceGenTest COMMAND service_gen_tests) 