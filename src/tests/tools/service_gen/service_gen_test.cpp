#include <catch2/catch_test_macros.hpp>
#include <filesystem>
#include <fstream>
#include <string>

#include "service_gen.h" // header from tool

namespace fs = std::filesystem;

static fs::path fixturesDir() {
    return fs::path{PROJECT_SOURCE_DIR} / "src" / "tests" / "fixtures";
}

TEST_CASE("Service generator detects cycles", "[service-gen][negative]") {
    const fs::path yamlPath = fixturesDir() / "services_cycle.yaml";
    std::string err;
    bool ok = kai::tools::generateServiceHeaders(yamlPath, fs::temp_directory_path(), err);
    REQUIRE_FALSE(ok);
    REQUIRE_FALSE(err.empty());
}

TEST_CASE("Service generator detects unknown dependency", "[service-gen][negative]") {
    const fs::path yamlPath = fixturesDir() / "services_unknown_dep.yaml";
    std::string err;
    bool ok = kai::tools::generateServiceHeaders(yamlPath, fs::temp_directory_path(), err);
    REQUIRE_FALSE(ok);
    REQUIRE(err.find("unknown") != std::string::npos);
}

TEST_CASE("Service generator produces headers for valid YAML", "[service-gen]") {
    const fs::path yamlPath = fixturesDir() / "services_valid.yaml";
    const fs::path outDir = fs::temp_directory_path() / "kai_service_gen_test";
    fs::remove_all(outDir);
    std::string err;
    bool ok = kai::tools::generateServiceHeaders(yamlPath, outDir, err);
    REQUIRE(ok);
    REQUIRE(err.empty());
    // Check that key header exists
    REQUIRE(fs::exists(outDir / "service_id.h"));
    REQUIRE(fs::exists(outDir / "service_topology.h"));
} 