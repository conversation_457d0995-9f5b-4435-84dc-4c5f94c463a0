#include "core/diagnostics/diagnostics_service.h"
#include "core/async/executor_service.h"
#include "core/foundation/registry.h"
#include "core/memory/arena_allocator_service.h"
#include "core/events/event_bus_service.h"
#include <iostream>
#include <thread>
#include <vector>

using namespace launcher::core;

int main() {
    foundation::ServiceRegistry registry;
    memory::ArenaAllocatorSvc    arena{registry};
    async::ExecutorService       executor{registry};
    events::EventBusService     bus{registry, nullptr}; // Pass nullptr for diagnostics if not used
    auto execRes = executor.start();
    if (!execRes) {
        std::cerr << "ExecutorService failed to start\n";
        return 1;
    }

    diagnostics::DiagnosticsService diag{registry};
    auto startRes = diag.start();
    if (!startRes) {
        std::cerr << "DiagnosticsService failed to start\n";
        return 1;
    }

    constexpr int kThreads = 16;
    constexpr int kLoops   = 100000;

    std::vector<std::thread> workers;
    workers.reserve(kThreads);

    for (int i = 0; i < kThreads; ++i) {
        workers.emplace_back([&]() {
            for (int j = 0; j < kLoops; ++j) {
                diag.incrementCounter("race", 1);
            }
        });
    }

    for (auto& t : workers) {
        t.join();
    }

    const auto snap   = diag.snapshot();
    const auto total  = snap["counters"]["race"].get<int64_t>();
    const int64_t exp = static_cast<int64_t>(kThreads) * kLoops;

    if (total != exp) {
        std::cerr << "Mismatch: " << total << " expected " << exp << "\n";
        return 1;
    }

    diag.stop();
    bus.stop();
    executor.stop();
    std::cout << "DiagnosticsService race test passed\n";
    return 0;
} 