#include "core/diagnostics/diagnostics_service.h"
#include "core/async/executor_service.h"
#include "core/events/event_bus_service.h"
#include "core/foundation/registry.h"
#include "core/memory/arena_allocator_service.h"

#include <iostream>
#include <thread>
#include <vector>

using namespace launcher::core;

struct DummyEvent {};

int main() {
    foundation::ServiceRegistry registry;
    memory::ArenaAllocatorSvc    arena{registry};
    async::ExecutorService       executor{registry};
    events::EventBusService     bus{registry, nullptr}; // Pass nullptr for diagnostics
    if (!executor.start()) {
        std::cerr << "ExecutorService failed to start\n";
        return 1;
    }

    // 2. Create & start EventBus ------------------------------------------
    if (!bus.start()) {
        std::cerr << "EventBusService failed to start\n";
        return 1;
    }

    // Subscribe a dummy handler so publishes enqueue tasks.
    auto handle = bus.subscribe<DummyEvent>([](const DummyEvent* /*unused*/) {});

    // Publish a batch of events to create queue backlog.
    constexpr int kPublishes = 500;
    for (int i = 0; i < kPublishes; ++i) {
        {
            auto evt = std::make_shared<DummyEvent>();
            (void)bus.publish(std::static_pointer_cast<const DummyEvent>(evt));
        }
    }

    // 3. Start Diagnostics (after backlog exists) -------------------------
    diagnostics::DiagnosticsService diag{registry};
    if (!diag.start()) {
        std::cerr << "DiagnosticsService failed to start\n";
        return 1;
    }

    kai::json::Document snap;
    bool gauges_ok = false;
    for (int i = 0; i < 100 && !gauges_ok; ++i) {
        diag.flushForTest();
        auto snap_tmp = diag.snapshot();
        if (snap_tmp.contains("gauges") && snap_tmp["gauges"].contains("executor.pending_tasks") &&
            snap_tmp["gauges"].contains("eventbus.queue_depth")) {
            snap = std::move(snap_tmp);
            gauges_ok = true;
            break;
        }
        std::this_thread::sleep_for(std::chrono::milliseconds(10));
    }

    if (!gauges_ok) {
        std::cerr << "Gauges did not appear within timeout\n";
        return 1;
    }

    if (!snap.contains("gauges")) {
        std::cerr << "Snapshot missing gauges object\n";
        return 1;
    }

    const auto& gauges = snap["gauges"];
    if (!gauges.contains("executor.pending_tasks")) {
        std::cerr << "Missing executor.pending_tasks gauge\n";
        return 1;
    }

    if (!gauges.contains("eventbus.queue_depth")) {
        std::cerr << "Missing eventbus.queue_depth gauge\n";
        return 1;
    }

    // Basic sanity: values are non-negative.
    if (gauges["executor.pending_tasks"].get<int64_t>() < 0) {
        std::cerr << "executor.pending_tasks negative\n";
        return 1;
    }
    if (gauges["eventbus.queue_depth"].get<int64_t>() < 0) {
        std::cerr << "eventbus.queue_depth negative\n";
        return 1;
    }

    // Clean shutdown
    diag.stop();
    bus.stop();
    executor.stop();

    std::cout << "Diagnostics gauges test passed\n";
    return 0;
} 