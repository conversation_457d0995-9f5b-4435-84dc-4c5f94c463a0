add_executable(openai_provider_unit_test
    openai_provider_unit_test.cpp
    openai_streaming_test.cpp
)

target_include_directories(openai_provider_unit_test PRIVATE
    ../../../core
    ../../../plugins/openai
    ${CMAKE_BINARY_DIR}/_deps/rpmalloc/rpmalloc
    ${CMAKE_BINARY_DIR}/_deps/json-src/include
)

target_link_libraries(openai_provider_unit_test PRIVATE
    Catch2::Catch2WithMain
    openai
)

add_test(NAME openai_provider_unit_test COMMAND openai_provider_unit_test)

add_executable(openai_plugin_runtime_test
    openai_plugin_runtime_test.cpp
)

target_include_directories(openai_plugin_runtime_test PRIVATE
    ../../../core
    ${CMAKE_SOURCE_DIR}/src
)

target_link_libraries(openai_plugin_runtime_test PRIVATE core snapshot_lib Catch2::Catch2WithMain)

target_compile_definitions(openai_plugin_runtime_test PRIVATE LLM_OPENAI_PLUGIN_PATH="$<TARGET_FILE:openai>" LAUNCHER_ASYNC_LOG=0 RPMALLOC_FIRST_CLASS_HEAPS=0)

add_test(NAME openai_plugin_runtime_test COMMAND openai_plugin_runtime_test) 