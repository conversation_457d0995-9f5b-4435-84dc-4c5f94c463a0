#include <catch2/catch_test_macros.hpp>

#include "core/plugins/runtime_manager_service.h"
#include "core/foundation/registry.h"
#include "core/memory/arena_allocator_service.h"
#include "core/async/executor_service.h"
#include "core/events/event_bus_service.h"
#include "core/diagnostics/diagnostics_service.h"
#include "core/security/verification_store.hh"
#include "core/llm/il_provider.h"
#include "core/plugins/abi.h"

#include <filesystem>

#ifndef LLM_OPENAI_PLUGIN_PATH
#error "LLM_OPENAI_PLUGIN_PATH not defined during compile"
#endif

using namespace launcher::core;

TEST_CASE("OpenAI plugin loads and registers provider", "[plugin][openai]") {
    const std::filesystem::path pluginPath{LLM_OPENAI_PLUGIN_PATH};
    REQUIRE(std::filesystem::exists(pluginPath));

    // ---------------------------------------------------------------------
    // Minimal service graph required by RuntimeManagerSvc
    // ---------------------------------------------------------------------
    foundation::ServiceRegistry registry;
    memory::ArenaAllocatorSvc arena{registry};
#if defined(RPMALLOC_FIRST_CLASS_HEAPS) && (RPMALLOC_FIRST_CLASS_HEAPS)
    arena.disableHeapTracking();
#endif
    async::ExecutorService    exec{registry};
    events::EventBusService   bus{registry};
    diagnostics::DiagnosticsService diag{registry};
    security::VerificationStore      vstore;
    exec.setDiagnostics(&diag);
    bus.setDiagnostics(&diag);

    plugins::RuntimeManagerSvc mgr{registry, vstore};

    // Probe the plugin – this dlopens the dylib and calls kai_plugin_initialize()
    auto probeRes = mgr.probeLibrary(pluginPath);
    REQUIRE(probeRes);

    const auto& desc = probeRes.value();
    REQUIRE(std::string(desc.info.id) == "openai");
    REQUIRE(desc.info.runtime == KAI_RUNTIME_NULL);

    // Provider service should now be registered in the registry
    kai::llm::IProvider* provider{nullptr};
    registry.forEachService([&](foundation::IService& svc){
        if(auto* p = dynamic_cast<kai::llm::IProvider*>(&svc)) {
            if(p->providerId() == "openai") {
                provider = p;
            }
        }
    });

    REQUIRE(provider != nullptr);

    auto modelsExp = provider->models();
    REQUIRE(modelsExp.has_value());
    const auto& models = modelsExp.value();
    REQUIRE(!models.empty());
    REQUIRE(models[0].model_id.rfind("gpt-4", 0) == 0);
} 