// Streaming test for OpenAI provider
#include <catch2/catch_test_macros.hpp>
#include "../../../plugins/openai/openai_provider_plugin.h"
#include "../../../core/llm/il_provider.h"
#include <thread>
#include <netinet/in.h>
#include <sys/socket.h>
#include <unistd.h>
#include <cstring>
#include <arpa/inet.h>
#include <atomic>

using namespace launcher::plugins::openai;

namespace {
void run_sse_server(std::atomic<bool>& stop_flag){
    int sock = socket(AF_INET, SOCK_STREAM, 0);
    if(sock<0) return;
    int opt=1; setsockopt(sock,SOL_SOCKET,SO_REUSEADDR,&opt,sizeof(opt));
    sockaddr_in addr{}; addr.sin_family=AF_INET; addr.sin_addr.s_addr=htonl(INADDR_LOOPBACK); addr.sin_port=htons(18082);
    if(bind(sock,(sockaddr*)&addr,sizeof(addr))!=0||listen(sock,1)!=0){close(sock);return;}
    const char* hdr="HTTP/1.1 200 OK\r\nContent-Type: text/event-stream\r\nConnection: close\r\n\r\n";
    const char* chunk1="data: {\"choices\":[{\"delta\":{\"content\":\"A\"}}]}\n\n";
    const char* chunk2="data: {\"choices\":[{\"delta\":{\"content\":\"B\"}}]}\n\n";
    const char* chunk3="data: {\"choices\":[{\"delta\":{\"content\":\"C\"}}]}\n\n";
    const char* done="data: [DONE]\n\n";
    while(!stop_flag){
        fd_set rfds; FD_ZERO(&rfds); FD_SET(sock,&rfds); timeval tv{0,100000};
        if(select(sock+1,&rfds,nullptr,nullptr,&tv)<=0) continue;
        int client=accept(sock,nullptr,nullptr); if(client<0) continue;
        write(client,hdr,strlen(hdr));
        write(client,chunk1,strlen(chunk1)); usleep(50000);
        write(client,chunk2,strlen(chunk2)); usleep(50000);
        write(client,chunk3,strlen(chunk3)); usleep(50000);
        write(client,done,strlen(done));
        close(client);
    }
    close(sock);
}
}

TEST_CASE("OpenAI streaming SSE", "[openai][stream]"){
    setenv("OPENAI_BASE_URL","http://127.0.0.1:18082/v1",1);
    setenv("OPENAI_API_KEY","test",1);
    std::atomic<bool> stop{false}; std::thread th(run_sse_server,std::ref(stop));
    std::this_thread::sleep_for(std::chrono::milliseconds(100));
    OpenAiProviderPlugin prov;
    auto expStream=prov.stream("Hello", "");
    REQUIRE(expStream);
    auto stream=expStream.value();
    std::vector<std::string> collected;
    while(auto maybe=stream.waitNext()){
        REQUIRE(maybe.has_value());
        auto delta = maybe->value();
        if(delta.done) break;
        collected.emplace_back(std::string(delta.content));
    }
    stop=true; th.join();
    REQUIRE(collected==std::vector<std::string>{"A","B","C"});
} 