#include <catch2/catch_test_macros.hpp>
#include "../../../plugins/openai/openai_provider_plugin.h"
#include "../../../core/llm/il_provider.h"
#include <thread>
#include <sys/socket.h>
#include <netinet/in.h>
#include <string>
#include <cstring> // For strerror
#include <sys/select.h>
#include <unistd.h>
#include <atomic>
#include <iostream> // For std::cerr and std::cout

using namespace launcher::plugins::openai;

namespace {
void run_stub_server(std::atomic<bool>& stop_flag) {
    int sock = socket(AF_INET, SOCK_STREAM, 0);
    if (sock < 0) {
        std::cerr << "Stub server: socket() failed: " << strerror(errno) << std::endl;
        return;
    }
    int opt = 1;
    setsockopt(sock, SOL_SOCKET, SO_REUSEADDR, &opt, sizeof(opt));
    sockaddr_in addr{};
    addr.sin_family      = AF_INET;
    addr.sin_addr.s_addr = htonl(INADDR_LOOPBACK);
    addr.sin_port        = htons(18083);
    if (bind(sock, (sockaddr*)&addr, sizeof(addr)) != 0) {
        std::cerr << "Stub server: bind() to 127.0.0.1:18083 failed: " << strerror(errno) << std::endl;
        close(sock);
        return;
    }
    if (listen(sock, 1) != 0) {
        std::cerr << "Stub server: listen() failed: " << strerror(errno) << std::endl;
        close(sock);
        return;
    }
    std::cout << "Stub server: Listening on 127.0.0.1:18083" << std::endl;

    const char* body = R"({"choices":[{"message":{"content":"Hello unit"}}]})";
    std::string hdr = "HTTP/1.1 200 OK\r\nContent-Type: application/json\r\nContent-Length: " +
                      std::to_string(strlen(body)) + "\r\n\r\n";
    std::string resp = hdr + body;

    while (!stop_flag) {
        fd_set rfds; FD_ZERO(&rfds); FD_SET(sock, &rfds);
        timeval tv{0, 100000}; // 100ms timeout for select
        int r = select(sock + 1, &rfds, nullptr, nullptr, &tv);
        if (r < 0) { // select error
            if (errno == EINTR) continue; // Interrupted by signal, retry
            std::cerr << "Stub server: select() error: " << strerror(errno) << std::endl;
            break;
        }
        if (r == 0) continue; // timeout, check stop_flag

        int client = accept(sock, nullptr, nullptr);
        if (client >= 0) {
            // Send stubbed response.
            send(client, resp.c_str(), resp.size(), 0);

            // Gracefully finish the connection: stop sending but keep reading to
            // consume the request body. This avoids the kernel sending an
            // RST if unread data remains, which would make the HTTP client
            // treat the request as failed and trigger long retries.
            shutdown(client, SHUT_WR);

            char buf[2048];
            while (recv(client, buf, sizeof(buf), 0) > 0) {}

            close(client);
        } else {
             if (errno != EAGAIN && errno != EWOULDBLOCK) { // Ignore non-blocking errors
                std::cerr << "Stub server: accept() error: " << strerror(errno) << std::endl;
             }
        }
    }
    close(sock);
    std::cout << "Stub server: Shut down." << std::endl;
}
}

TEST_CASE("OpenAI provider completes via local stub", "[openai][unit]") {
    // Point provider to local stub
    setenv("OPENAI_BASE_URL", "http://127.0.0.1:18083/v1", 1);
    setenv("OPENAI_API_KEY", "test", 1);

    std::atomic<bool> stop{false};
    std::thread th(run_stub_server, std::ref(stop));
    // Increased sleep duration to give server more time to start
    std::this_thread::sleep_for(std::chrono::milliseconds(500));

    launcher::plugins::openai::OpenAiProviderPlugin provider;

    auto result = provider.complete("Hi", "");

    // Retrieve model list while stub server is still alive to avoid retries
    auto modelsExp = provider.models();

    stop = true;
    th.join();

    REQUIRE(result.has_value());
    REQUIRE(result.value().text == "Hello unit");

    REQUIRE(modelsExp.has_value());
    const auto& list = modelsExp.value();
    REQUIRE(!list.empty());
    REQUIRE(list[0].model_id == "gpt-4o");
}