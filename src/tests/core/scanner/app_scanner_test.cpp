#define CATCH_CONFIG_MAIN
#include <catch2/catch_all.hpp>

#include "../../../core/index/app_index.h"
#include "../../../core/scanner/app_scanner_interface.h"

using namespace launcher::core;

TEST_CASE("AppScanner can be created", "[scanner]") {
    auto scanner = AppScannerInterface::create();

#ifdef __APPLE__
    REQUIRE(scanner != nullptr);
#elif defined(_WIN32)
    REQUIRE(scanner != nullptr);
#else
    // On other platforms, the scanner might not be implemented
    if (scanner == nullptr) {
        SUCCEED("Scanner is not implemented on this platform");
    }
#endif
}

TEST_CASE("AppScanner can scan applications", "[scanner]") {
    auto scanner = AppScannerInterface::create();

    // Skip test if scanner is not implemented for this platform
    if (scanner == nullptr) {
        SUCCEED("Scanner is not implemented on this platform");
        return;
    }

    AppIndex index;
    int added = scanner->scanApplications(index);

    // We should find at least some applications on any system
    REQUIRE(added > 0);
    REQUIRE(index.size() > 0);

    // Check that we can search for applications
    auto results = index.search("");  // Empty query returns all apps
    REQUIRE(!results.empty());

    // Check that the first result has valid data
    REQUIRE(!results[0].name.empty());
    REQUIRE(!results[0].path.empty());
}

TEST_CASE("AppScanner can update applications", "[scanner]") {
    auto scanner = AppScannerInterface::create();

    // Skip test if scanner is not implemented for this platform
    if (scanner == nullptr) {
        SUCCEED("Scanner is not implemented on this platform");
        return;
    }

    AppIndex index;

    // First scan
    int added = scanner->scanApplications(index);
    REQUIRE(added > 0);

    // Get the initial size
    size_t initial_size = index.size();

    // Update scan (might not add any new apps in a test environment)
    scanner->updateApplications(index);

    // The size should be at least the same as before
    REQUIRE(index.size() >= initial_size);

    // Check last scan time
    auto last_scan_time = scanner->getLastScanTime();
    REQUIRE(last_scan_time > std::chrono::system_clock::time_point());
}