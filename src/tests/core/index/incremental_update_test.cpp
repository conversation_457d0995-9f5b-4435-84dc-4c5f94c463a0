#include <catch2/catch_test_macros.hpp>
#include <chrono>
#include <filesystem>
#include <fstream>
#include <thread>

#include "../../../core/index/app_index.h"
#include "../../../core/scanner/app_scanner_interface.h"

using namespace launcher::core;
namespace fs = std::filesystem;

TEST_CASE("AppIndex incremental updates", "[app_index][incremental]") {
    // Create a temporary directory for testing
    fs::path tempDir = fs::temp_directory_path() / "app_index_test";
    fs::create_directories(tempDir);

    // Create a scanner
    auto scanner = AppScannerInterface::create();
    REQUIRE(scanner != nullptr);

    // Create an index
    AppIndex index;

    SECTION("Enable and disable incremental updates") {
        REQUIRE_FALSE(index.isIncrementalUpdatesEnabled());
        REQUIRE(index.enableIncrementalUpdates(scanner));
        REQUIRE(index.isIncrementalUpdatesEnabled());
        REQUIRE(index.disableIncrementalUpdates());
        REQUIRE_FALSE(index.isIncrementalUpdatesEnabled());
    }

    SECTION("Update applications") {
        REQUIRE(index.updateApplications(scanner));
        REQUIRE(index.size() > 0);
    }

    SECTION("Search applications") {
        REQUIRE(index.updateApplications(scanner));

        // Search for all applications
        auto allApps = index.getAllApplications();
        REQUIRE(allApps.size() > 0);

        // Search for a specific application
        if (!allApps.empty()) {
            std::string appName = allApps[0].name;
            auto results = index.search(appName);
            REQUIRE(results.size() > 0);
        }
    }

    // Clean up
    fs::remove_all(tempDir);
}