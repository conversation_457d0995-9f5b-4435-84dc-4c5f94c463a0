// New test: llm_factory_service_test.cpp
#include <catch2/catch_test_macros.hpp>

#include "core/llm/llm_factory_service.h"
#include "core/llm/llm_provider_adapter_base.h"
#include "core/foundation/registry.h"
#include <string>

using namespace launcher::core;
using namespace launcher::core::llm;

// Dummy provider for testing
class DummyProvider final : public LlmProviderAdapterBase<DummyProvider> {
 public:
    static constexpr char kProviderId[] = "dummy";
    static constexpr security::Mask128 kCaps{}; // no caps

    std::array<LlmModelInfo, 1> modelsImplArr{{{"model1", kProviderId}}};
    auto modelsImpl() const noexcept { return std::span<const LlmModelInfo>(modelsImplArr); }

    util::Expected<LlmResponse, foundation::KaiError> completeImpl(const LlmRequest&) noexcept {
        return util::Expected<LlmResponse, foundation::KaiError>::success(LlmResponse{.text = "ok"});
    }
};

constexpr char DummyProvider::kProviderId[];
constexpr security::Mask128 DummyProvider::kCaps;

TEST_CASE("LlmFactoryService registers providers", "[llm][factory]") {
    foundation::ServiceRegistry reg;
    LlmFactoryService factory{reg};

    std::vector<std::unique_ptr<DummyProvider>> vec;
    for(int i=0;i<10;++i){ vec.push_back(std::make_unique<DummyProvider>()); factory.registerProvider(*vec.back()); }

    REQUIRE(factory.providers().size() == 10);
    for(auto* prov: factory.providers()){ REQUIRE(prov->providerId()==std::string_view("dummy")); }
} 