#include "gtest/gtest.h"
#include "core/llm/model_factory.h"
#include "core/llm/model_registry.h"
#include "core/config/ai_provider_config.h"
#include "core/config/config_manager.h"

using namespace launcher::core;

TEST(EchoProvider, CompleteEcho) {
    AIProviderConfig cfg; cfg.provider_id="echo"; cfg.enabled=true; cfg.default_model="echo";
    ConfigManager dummyCfg; // assume default construct ok
    ModelRegistry& reg = ModelRegistry::global();
    ModelFactory factory(dummyCfg, std::shared_ptr<ModelRegistry>(&reg, [](ModelRegistry*){}));
    auto model = factory.create("echo", "echo");
    ASSERT_TRUE(model);
    Context ctx; std::string prompt="hello";
    auto out = model->complete(prompt, ctx);
    EXPECT_EQ(out, prompt);
} 