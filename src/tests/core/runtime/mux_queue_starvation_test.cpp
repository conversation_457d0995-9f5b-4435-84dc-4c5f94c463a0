#include "core/runtime/mux_queue.hh"
#include <catch2/catch_test_macros.hpp>
#include <atomic>
#include <chrono>
#include <thread>

using namespace launcher::core;
using launcher::core::runtime::MuxQueueBackend;

namespace {
struct Msg {
    int id;
    std::uint8_t priority;
};

constexpr std::size_t kCap = 64;
using QueueT = MuxQueueBackend<Msg, kCap, 3>;
}

TEST_CASE("MuxQueue ensures no starvation beyond 100us", "[mux_queue][starvation]") {
    QueueT q;
    std::atomic<bool> stop{false};
    std::atomic<bool> low_seen{false};
    std::atomic<long long> low_pop_us{0};

    // Consumer thread ----------------------------------------------------
    std::thread consumer([&]() {
        Msg m;
        while (!stop.load(std::memory_order_acquire)) {
            if (q.try_pop(m)) {
                if (m.priority == 2) {
                    low_seen.store(true, std::memory_order_release);
                    if (low_pop_us.load(std::memory_order_relaxed) == 0) {
                        auto tp = std::chrono::steady_clock::now();
                        low_pop_us.store(std::chrono::duration_cast<std::chrono::microseconds>(tp.time_since_epoch()).count(), std::memory_order_release);
                    }
                }
            } else {
                // Busy-spin instead of std::this_thread::yield(); the latter can
                // introduce 100–500 µs scheduler latency on macOS and break the
                // tight starvation SLA we are asserting here.
                continue; // cheap spin, queue is lock-free
            }
        }
        while (q.try_pop(m)) {
            if (m.priority == 2) {
                low_seen.store(true, std::memory_order_release);
                if (low_pop_us.load(std::memory_order_relaxed) == 0) {
                    auto tp = std::chrono::steady_clock::now();
                    low_pop_us.store(std::chrono::duration_cast<std::chrono::microseconds>(tp.time_since_epoch()).count(), std::memory_order_release);
                }
            }
        }
    });

    // Producer 1 – inject one low-priority message and timestamp first ----
    using clock = std::chrono::steady_clock;
    std::atomic<long long> low_push_us{0};
    {
        auto now_tp = clock::now();
        low_push_us.store(std::chrono::duration_cast<std::chrono::microseconds>(now_tp.time_since_epoch()).count(),
                          std::memory_order_relaxed);
        Msg low_msg{999, 2};
        q.try_push(low_msg);
    }

    // Producer 0 – floods high priority for 1 ms -------------------------
    std::thread prod_hi([&]() {
        using clock = std::chrono::steady_clock;
        auto start = clock::now();
        int id = 0;
        while (clock::now() - start < std::chrono::milliseconds(1)) {
            Msg m{ id++, 0 };
            if (!q.try_push(std::move(m))) {
                // Back-pressure: yield so the consumer can run, ensuring
                // more deterministic latency behaviour under CPU contention.
                std::this_thread::yield();
            }
        }
    });

    // Wait for low priority to be popped ---------------------------------
    using clock = std::chrono::steady_clock;
    auto deadline = clock::now() + std::chrono::milliseconds(5);
    while (!low_seen.load(std::memory_order_acquire) && clock::now() < deadline) {
        std::this_thread::sleep_for(std::chrono::microseconds(50));
    }

    stop.store(true, std::memory_order_release);
    prod_hi.join();
    consumer.join();

    REQUIRE(low_seen.load());
    REQUIRE(low_pop_us.load() > 0);
    const auto latency = low_pop_us.load() - low_push_us.load();
    REQUIRE(latency <= 200);
} 