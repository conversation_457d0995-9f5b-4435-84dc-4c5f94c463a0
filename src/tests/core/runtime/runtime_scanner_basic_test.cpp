#include "core/runtime/runtime_scanner.hh"
#include "core/async/executor_service.h"
#include "core/events/event_bus_service.h"
#include "core/foundation/registry.h"
#include "core/memory/arena_allocator_service.h"

#include <catch2/catch_test_macros.hpp>
#include <atomic>
#include <filesystem>
#include <fstream>
#include <random>
#include <thread>
#include <chrono>

using namespace launcher::core;

static void createDummyFiles(const std::filesystem::path& dir, int count) {
    std::error_code ec;
    std::filesystem::create_directories(dir, ec);
    std::mt19937 rng{42};
    std::uniform_int_distribution<uint8_t> dist(0, 255);
    for (int i = 0; i < count; ++i) {
        auto p = dir / ("rt_" + std::to_string(i));
        std::ofstream out(p, std::ios::binary);
        char buf[512];
        for (auto& b : buf) b = static_cast<char>(dist(rng));
        out.write(buf, sizeof(buf));
    }
}

TEST_CASE("RuntimeScanner discovers runtimes", "[runtime_scanner]") {
    constexpr int kNum = 10;
    auto tmp_dir = std::filesystem::temp_directory_path() / "rt_scanner_test";
    std::filesystem::remove_all(tmp_dir);
    createDummyFiles(tmp_dir, kNum);

    foundation::ServiceRegistry registry;
    memory::ArenaAllocatorSvc   arena{registry};
    async::ExecutorService      exec{registry};
    exec.start();
    events::EventBusService     bus{registry};
    bus.start();

    std::atomic<int> discovered{0};
    auto sub = bus.subscribe<runtime::RuntimeDiscoveredEvent>(
        [&](const runtime::RuntimeDiscoveredEvent* evt_ptr) {
            if (evt_ptr) {
                 discovered.fetch_add(1, std::memory_order_relaxed);
            }
        });

    runtime::RuntimeScanner scanner(exec, bus, {tmp_dir});
    REQUIRE(scanner.start());

    const char* env_budget = std::getenv("KAI_SCANNER_BUDGET_MS");
    const int budget_ms = env_budget ? std::atoi(env_budget) : 8000; // 8 s default
    auto deadline = std::chrono::steady_clock::now() + std::chrono::milliseconds(budget_ms);
    while (discovered.load(std::memory_order_relaxed) < kNum && std::chrono::steady_clock::now() < deadline) {
        std::this_thread::sleep_for(std::chrono::milliseconds(5));
    }

    REQUIRE(discovered.load(std::memory_order_relaxed) == kNum);
    auto st = scanner.stats();
    // Queue drops are acceptable under heavy load; we only require discovery.

    scanner.stop();
    bus.stop();
    exec.stop();
} 