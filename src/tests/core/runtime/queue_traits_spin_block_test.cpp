#include "../../../core/runtime/ring_queue_backend.hh"
#include <catch2/catch_test_macros.hpp>
#include <atomic>
#include <chrono>
#include <thread>
#include <vector>

using namespace launcher::core;

namespace {
using Clock = std::chrono::steady_clock;

// Alias for the queue under test.
using SpinQueue = runtime::RingQueueBackend<int, 8>; // small capacity emphasises contention
} // namespace

TEST_CASE("RingQueue wait_pop sleeps and wakes promptly", "[ring_queue][spin_block][sleep_wake]") {
    SpinQueue q;

    std::atomic<long long> pop_ts_us{0};
    std::atomic<long long> blocked_ms{0};

    // Consumer thread that will block in wait_pop() until producer pushes.
    std::thread consumer([&]() {
        int value = 0;
        const auto t0 = Clock::now();
        q.wait_pop(value); // should block here
        const auto t1 = Clock::now();
        blocked_ms.store(std::chrono::duration_cast<std::chrono::milliseconds>(t1 - t0).count(),
                         std::memory_order_release);
        pop_ts_us.store(std::chrono::duration_cast<std::chrono::microseconds>(t1.time_since_epoch()).count(),
                        std::memory_order_release);
        REQUIRE(value == 42);
    });

    // Ensure consumer is definitely waiting.
    std::this_thread::sleep_for(std::chrono::milliseconds(50));

    // Producer push after delay and record timestamp.
    const auto push_tp = Clock::now();
    const long long push_ts_us =
        std::chrono::duration_cast<std::chrono::microseconds>(push_tp.time_since_epoch()).count();
    REQUIRE(q.try_push(42));

    consumer.join();

    // Assertions ----------------------------------------------------------
    REQUIRE(blocked_ms.load() >= 45);               // consumer really slept
    const long long latency_us = pop_ts_us.load() - push_ts_us;
    REQUIRE(latency_us <= 2000);                    // woke within 2 ms
}

TEST_CASE("RingQueue producers spin and make progress under contention", "[ring_queue][spin_block][producer_spin]") {
    using ContendedQueue = runtime::RingQueueBackend<int, 4>; // tiny capacity to force drops
    ContendedQueue q;

    constexpr int kProducerThreads = 4;
    constexpr auto kRunDuration = std::chrono::milliseconds(5);

    std::atomic<bool> stop{false};
    std::atomic<size_t> attempts{0};
    std::atomic<size_t> hits{0};
    std::atomic<size_t> drops{0};

    // Consumer that drains queue to keep space appearing.
    std::thread consumer([&]() {
        int val;
        while (!stop.load(std::memory_order_acquire)) {
            if (!q.try_pop(val)) {
                std::this_thread::yield();
            }
        }
        // drain leftovers
        while (q.try_pop(val)) {}
    });

    // Producers -----------------------------------------------------------
    std::vector<std::thread> producers;
    producers.reserve(kProducerThreads);
    for (int t = 0; t < kProducerThreads; ++t) {
        producers.emplace_back([&, t]() {
            int value = t * 1000000; // unique base per thread
            const auto deadline = Clock::now() + kRunDuration;
            while (Clock::now() < deadline) {
                if (q.try_push(value)) {
                    hits.fetch_add(1, std::memory_order_relaxed);
                    ++value;
                } else {
                    drops.fetch_add(1, std::memory_order_relaxed);
                }
                attempts.fetch_add(1, std::memory_order_relaxed);
            }
        });
    }

    for (auto &th : producers) th.join();
    stop.store(true, std::memory_order_release);
    consumer.join();

    // Sanity & progress assertions ---------------------------------------
    const size_t total_attempts = attempts.load();
    REQUIRE(total_attempts > 1000);                 // producers made progress
    REQUIRE(hits.load() > 0);
    REQUIRE(drops.load() > 0);
    REQUIRE(hits.load() + drops.load() == total_attempts);
} 