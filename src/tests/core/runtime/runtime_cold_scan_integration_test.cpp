// -----------------------------------------------------------------------------
// @file runtime_cold_scan_integration_test.cpp
// @brief Integration test exercising RuntimeScanner cold scan of 200 runtimes.
//        Fails when overall wall-clock latency exceeds 150 ms or not all
//        runtimes are discovered.  Mirrors the runtime_scan_200_bench but
//        compiled as a Catch2 test so it is executed by CTest in CI.
// -----------------------------------------------------------------------------

#include "core/async/executor_service.h"
#include "core/events/event_bus_service.h"
#include "core/foundation/registry.h"
#include "core/memory/arena_allocator_service.h"
#include "core/runtime/runtime_scanner.hh"

#include <catch2/catch_test_macros.hpp>

#include <atomic>
#include <chrono>
#include <filesystem>
#include <fstream>
#include <random>
#include <thread>

using namespace launcher::core;
using namespace std::chrono_literals;

namespace {

constexpr int kRuntimes = 200;

// Helper that creates |count| random 4 KiB files under |dir|.
static void createDummyFiles(const std::filesystem::path& dir, int count) {
    std::error_code ec;
    std::filesystem::create_directories(dir, ec);
    std::mt19937 rng{1337};
    std::uniform_int_distribution<uint8_t> dist(0, 255);
    for (int i = 0; i < count; ++i) {
        auto file_path = dir / ("runtime_" + std::to_string(i));
        std::ofstream out(file_path, std::ios::binary);
        char buf[4096];
        for (auto& b : buf) b = static_cast<char>(dist(rng));
        out.write(buf, sizeof(buf));
    }
}

} // namespace

TEST_CASE("Cold scan ≤150 ms for 200 runtimes", "[integration][runtime_scanner]") {
    // Unique temporary root per test invocation ensures cold FS path.
    const auto unique_tag = std::to_string(std::chrono::steady_clock::now().time_since_epoch().count());
    const auto temp_root  = std::filesystem::temp_directory_path() / ("runtime_scan_test_" + unique_tag);
    std::filesystem::remove_all(temp_root);
    createDummyFiles(temp_root, kRuntimes);

    foundation::ServiceRegistry registry;
    memory::ArenaAllocatorSvc    arena{registry};
    async::ExecutorService       exec{registry};
    exec.start();
    events::EventBusService      bus{registry};
    bus.start();

    std::atomic<int> discovered{0};
    auto sub = bus.subscribe<runtime::RuntimeDiscoveredEvent>(
        [&](const runtime::RuntimeDiscoveredEvent* evt_ptr) {
            if (evt_ptr) {
                 discovered.fetch_add(1, std::memory_order_relaxed);
            }
        });

    runtime::RuntimeScanner scanner(exec, bus, {temp_root});
    REQUIRE(scanner.start());

    const auto t0 = std::chrono::high_resolution_clock::now();
    const char* env_budget = std::getenv("KAI_COLD_SCAN_BUDGET_MS");
    const int budget_ms = env_budget ? std::atoi(env_budget) : 800;
    const auto deadline = t0 + std::chrono::milliseconds(budget_ms + 1500); // safeguard above budget
    while (discovered.load(std::memory_order_relaxed) < kRuntimes &&
           std::chrono::high_resolution_clock::now() < deadline) {
        std::this_thread::sleep_for(1ms);
    }
    const auto elapsed_ms = std::chrono::duration_cast<std::chrono::milliseconds>(
        std::chrono::high_resolution_clock::now() - t0).count();

    scanner.stop();
    // Stop EventBus before Executor so that all pending DispatcherTasks are
    // flushed to the Executor and executed before the worker threads shut
    // down.  This prevents loss of RuntimeDiscoveredEvent callbacks under
    // tight queue capacities (e.g. KAI_EVENTBUS_CAPACITY=8) which previously
    // caused intermittent failures in CI.
    bus.stop();
    exec.stop();
    sub.unsubscribe();
    std::filesystem::remove_all(temp_root);

    REQUIRE(discovered.load(std::memory_order_relaxed) == kRuntimes);
    INFO("Cold scan latency = " << elapsed_ms << " ms");
    REQUIRE(elapsed_ms <= budget_ms);
} 