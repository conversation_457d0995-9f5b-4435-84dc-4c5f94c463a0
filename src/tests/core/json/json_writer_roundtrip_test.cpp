#include "core/json/json_writer.h"
#include <catch2/catch_test_macros.hpp>
#include <yyjson.h>

using launcher::core::json::toJson;

static constexpr char kSample[] = R"JSON({"hello":"world","num":42,"arr":[1,2,3]})JSON";

TEST_CASE("yyjson writer roundtrip", "[json][yyjson]") {
    yyjson_read_err err{};
    yyjson_doc* doc = yyjson_read_opts(const_cast<char*>(kSample), sizeof(kSample)-1, YYJSON_READ_NOFLAG, nullptr, &err);
    REQUIRE(doc != nullptr);

    auto strExp = toJson(doc);
    REQUIRE(strExp.has_value());

    // parse back
    yyjson_doc* doc2 = yyjson_read_opts(const_cast<char*>(strExp->c_str()), strExp->size(), YYJSON_READ_NOFLAG, nullptr, &err);
    REQUIRE(doc2 != nullptr);

    // Compare value count as simple structural equality proxy
    REQUIRE(yyjson_doc_get_val_count(doc) == yyjson_doc_get_val_count(doc2));

    yyjson_doc_free(doc);
    yyjson_doc_free(doc2);
} 