#include "core/diagnostics/metrics_exporter.hh"
#include "core/container/adaptive_cache.hh"
#include "core/runtime/ring_queue_backend.hh"
#include <catch2/catch_test_macros.hpp>
#include <tuple>

using namespace launcher::core;
using namespace launcher::core::diagnostics;

TEST_CASE("export_metrics enumerates all MetricSource instances", "[metrics][exporter]") {
    // 1. Prepare sample metric sources
    container::AdaptiveCache<int, int> cache(64);
    runtime::RingQueueBackend<int, 8>   queue;

    // 2. Make tuple of pointers (common pattern – avoids copying)
    auto tup = std::make_tuple(&cache, &queue);

    // 3. Counter to verify visitation
    int visited = 0;

    // 4. Call exporter with templated lambda (captures SrcT type)
    export_metrics(tup, [&visited]<typename SrcT>(const auto& s) {
        static_assert(util::MetricSource<SrcT>);
        // Simple sanity check on struct layout (all zero at init)
        REQUIRE(s.hit == 0);
        visited++;
    });

    REQUIRE(visited == 2);
} 