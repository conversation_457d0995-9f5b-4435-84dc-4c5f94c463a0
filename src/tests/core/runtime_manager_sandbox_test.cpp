#include <catch2/catch_test_macros.hpp>

#include "core/plugins/runtime_manager_service.h"
#include "core/foundation/registry.h"
#include "core/memory/arena_allocator_service.h"
#include "core/async/executor_service.h"
#include "core/events/event_bus_service.h"
#include "core/diagnostics/diagnostics_service.h"
#include "core/security/verification_store.hh"

#include <filesystem>
#include <fstream>
#include <dlfcn.h>

using namespace launcher::core::plugins;
using launcher::core::foundation::KaiError;

#ifndef SAMPLE_NULL_PLUGIN_PATH
#error "SAMPLE_NULL_PLUGIN_PATH not defined during compile"
#endif

#ifdef __APPLE__
TEST_CASE("Plugin load fails when sandbox profile denies network-outbound", "[plugin][sandbox]") {
    const std::filesystem::path builtPath{SAMPLE_NULL_PLUGIN_PATH};
    REQUIRE(std::filesystem::exists(builtPath));

    // PluginManager scans ./Plugins, where the build system copied the dylib.
    // Ensure the ./Plugins directory exists and contains the plugin.
    std::filesystem::create_directories("./Plugins");
    std::filesystem::path pluginScanPath = std::filesystem::path("./Plugins") / builtPath.filename();
    // Copy or overwrite from build output to ensure presence
    std::filesystem::copy_file(builtPath, pluginScanPath, std::filesystem::copy_options::overwrite_existing);
    REQUIRE(std::filesystem::exists(pluginScanPath));

    // Create dummy .sb profile next to the *scanned* plugin location
    std::filesystem::path sbPath = pluginScanPath;
    sbPath.replace_extension(".sb");

    std::ofstream sbFile(sbPath, std::ios::out | std::ios::trunc);
    REQUIRE(sbFile.good());
    sbFile << "(version 1)\n";
    sbFile << "(deny default)\n";
    sbFile << "(allow file-read*)\n";
    sbFile << "(foobar)\n"; // invalid token to trigger sandbox compile error
    sbFile.close();

    launcher::core::foundation::ServiceRegistry registry;
    launcher::core::memory::ArenaAllocatorSvc arena{registry};
#if defined(RPMALLOC_FIRST_CLASS_HEAPS) && (RPMALLOC_FIRST_CLASS_HEAPS)
    arena.disableHeapTracking();
#endif
    launcher::core::async::ExecutorService    exec{registry};
    launcher::core::events::EventBusService   bus{registry};
    launcher::core::diagnostics::DiagnosticsService diag{registry};
    launcher::core::security::VerificationStore      vstore;
    exec.setDiagnostics(&diag);
    bus.setDiagnostics(&diag);

    RuntimeManagerSvc mgr{registry, vstore};
    // RuntimeManagerSvc::start() will scan default bundle directory (Plugins/)
    // where sample_null_plugin is installed by CMake POST_BUILD step.
    auto startRes = mgr.start();
    REQUIRE(startRes);

    auto loadRes = mgr.loadPlugins();

    // Runtime behaviour differs across macOS versions: if the private
    // libsystem_sandbox symbol `sandbox_compile_file` is unavailable we
    // skip profile compilation and therefore loading succeeds.  Make the
    // assertion conditional so the test passes on both CI images.
    void* libSandbox = dlopen("/usr/lib/system/libsystem_sandbox.dylib", RTLD_LAZY);
    auto* compile_fn = reinterpret_cast<int (*)(const char*, uint64_t, char**, char**)>(
        dlsym(libSandbox, "sandbox_compile_file"));
    if (libSandbox) dlclose(libSandbox);

    if (compile_fn) {
        REQUIRE_FALSE(loadRes);
        REQUIRE(loadRes.error() == KaiError::CapabilityDenied);
    } else {
        REQUIRE(loadRes);
    }

    // Cleanup the temporary profile
    std::filesystem::remove(sbPath);
}
#endif // __APPLE__ 