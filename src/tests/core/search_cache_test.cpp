#include "../../core/search/search_cache.h"

#include <catch2/catch_test_macros.hpp>
#include <filesystem>
#include <memory>
#include <string>
#include <thread>
#include <vector>

#include "../../core/index/app_index.h"
#include "../../core/scanner/app_scanner_interface.h"

namespace fs = std::filesystem;
using namespace launcher::core;

TEST_CASE("SearchCache basic operations", "[search_cache]") {
    // Create a search cache with a short TTL to keep the test fast
    SearchCache cache(10, std::chrono::seconds(1));

    // Create some test results
    std::vector<AppResult> results;
    results.push_back(AppResult("Firefox", "/path/to/firefox", "", 0.9f));
    results.push_back(AppResult("Chrome", "/path/to/chrome", "", 0.7f));

    SECTION("Add and retrieve results") {
        // Add results to cache
        cache.addResults("fire", results, false);

        // Check if query is in cache
        REQUIRE(cache.hasQuery("fire", false));

        // Get results from cache
        auto cachedResults = cache.getResults("fire", false);

        // Check that results match
        REQUIRE(cachedResults.size() == 2);
        REQUIRE(cachedResults[0].name == "Firefox");
        REQUIRE(cachedResults[0].score == 0.9f);
        REQUIRE(cachedResults[1].name == "Chrome");
        REQUIRE(cachedResults[1].score == 0.7f);
    }

    SECTION("Different history flag creates different cache entries") {
        // Add results with and without history
        cache.addResults("fire", results, false);

        // Create different results for the same query with history
        std::vector<AppResult> resultsWithHistory;
        resultsWithHistory.push_back(
            AppResult("Firefox", "/path/to/firefox", "", 0.95f));  // Higher score with history
        resultsWithHistory.push_back(
            AppResult("Chrome", "/path/to/chrome", "", 0.8f));  // Higher score with history

        cache.addResults("fire", resultsWithHistory, true);

        // Both should be in cache
        REQUIRE(cache.hasQuery("fire", false));
        REQUIRE(cache.hasQuery("fire", true));

        // Get results without history
        auto cachedResults = cache.getResults("fire", false);
        REQUIRE(cachedResults.size() == 2);
        REQUIRE(cachedResults[0].score == 0.9f);

        // Get results with history
        auto cachedResultsWithHistory = cache.getResults("fire", true);
        REQUIRE(cachedResultsWithHistory.size() == 2);
        REQUIRE(cachedResultsWithHistory[0].score == 0.95f);
    }

    SECTION("Cache expiration") {
        // Add results to cache
        cache.addResults("fire", results, false);

        // Check that results are in cache
        REQUIRE(cache.hasQuery("fire", false));

        // Wait for cache to expire (2 seconds > 1-second TTL)
        std::this_thread::sleep_for(std::chrono::seconds(2));

        // Results should no longer be in cache
        REQUIRE_FALSE(cache.hasQuery("fire", false));

        // Getting expired results should return empty vector
        auto cachedResults = cache.getResults("fire", false);
        REQUIRE(cachedResults.empty());
    }

    SECTION("Cache size limit") {
        // Create a cache with size limit of 2
        SearchCache smallCache(2, std::chrono::seconds(60));

        // Add 3 different queries
        smallCache.addResults("fire", results, false);
        smallCache.addResults("chrome", results, false);
        smallCache.addResults("code", results, false);

        // The oldest query should be evicted
        REQUIRE_FALSE(smallCache.hasQuery("fire", false));
        REQUIRE(smallCache.hasQuery("chrome", false));
        REQUIRE(smallCache.hasQuery("code", false));
    }

    SECTION("Clear cache") {
        // Add results to cache
        cache.addResults("fire", results, false);
        cache.addResults("chrome", results, false);

        // Clear the cache
        cache.clear();

        // Cache should be empty
        REQUIRE_FALSE(cache.hasQuery("fire", false));
        REQUIRE_FALSE(cache.hasQuery("chrome", false));
    }

    SECTION("Remove expired entries") {
        // Add results to cache
        cache.addResults("fire", results, false);

        // Wait for cache to expire (2 seconds > 1-second TTL)
        std::this_thread::sleep_for(std::chrono::seconds(2));

        // Add another entry that won't expire
        cache.addResults("chrome", results, false);

        // Remove expired entries
        cache.removeExpiredEntries();

        // "fire" should be removed, "chrome" should remain
        REQUIRE_FALSE(cache.hasQuery("fire", false));
        REQUIRE(cache.hasQuery("chrome", false));
    }
}

TEST_CASE("AppIndex with caching", "[app_index][search_cache]") {
    // Create app index
    AppIndex index;

    // Add some test applications
    index.addApp(AppResult("Firefox", "/path/to/firefox", ""));
    index.addApp(AppResult("Chrome", "/path/to/chrome", ""));
    index.addApp(AppResult("Visual Studio Code", "/path/to/vscode", ""));

    SECTION("Caching disabled by default") {
        REQUIRE_FALSE(index.isCachingEnabled());

        // Search should work without caching
        auto results = index.search("fire");
        // The actual number of results may vary depending on the implementation
        // Just check that we get at least one result that matches our query
        REQUIRE(results.size() > 0);
        bool foundFirefox = false;
        for (const auto& result : results) {
            if (result.name == "Firefox") {
                foundFirefox = true;
                break;
            }
        }
        REQUIRE(foundFirefox);
    }

    SECTION("Enable caching") {
        // Enable caching
        index.setCaching(true);
        REQUIRE(index.isCachingEnabled());

        // First search should populate cache
        auto results1 = index.search("fire");
        // The actual number of results may vary depending on the implementation
        REQUIRE(results1.size() > 0);
        bool foundFirefox = false;
        for (const auto& result : results1) {
            if (result.name == "Firefox") {
                foundFirefox = true;
                break;
            }
        }
        REQUIRE(foundFirefox);

        // Second search should use cache
        auto results2 = index.search("fire");
        REQUIRE(results2.size() == results1.size());
    }

    SECTION("Cache invalidation on index change") {
        // Skip this test for now
        REQUIRE(true);
    }

    SECTION("Disable caching") {
        // Enable caching
        index.setCaching(true);

        // Search to populate cache
        index.search("fire");

        // Disable caching
        index.setCaching(false);
        REQUIRE_FALSE(index.isCachingEnabled());

        // Cache should be cleared
        // We can't test this directly, but we can verify that caching is disabled
    }

    SECTION("Clear cache") {
        // Enable caching
        index.setCaching(true);

        // Search to populate cache
        index.search("fire");

        // Clear cache
        index.clearCache();

        // Caching should still be enabled
        REQUIRE(index.isCachingEnabled());
    }
}