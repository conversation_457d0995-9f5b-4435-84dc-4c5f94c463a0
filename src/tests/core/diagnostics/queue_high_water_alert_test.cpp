#include "core/diagnostics/queue_alerts.hh"
#include <catch2/catch_test_macros.hpp>

using namespace launcher::core;
using launcher::core::diagnostics::queue_alerts::maybeAlert;
namespace queue_alerts = launcher::core::diagnostics::queue_alerts;

struct DummyQueue { // satisfies trait false -> generic threshold 60
    using value_type = int;
    static constexpr bool is_metric_source = true;
};

TEST_CASE("queue_alerts generic threshold and counter", "[queue_alerts]") {
    launcher::core::runtime::QueueStats s{};
    queue_alerts::resetAlertCount();

    // Below threshold – no alert
    s.high_water_pct = 50;
    bool fired = maybeAlert<DummyQueue>(s);
    REQUIRE_FALSE(fired);
    REQUIRE(queue_alerts::alertCount() == 0);

    // Above threshold – should fire
    s.high_water_pct = 70;
    fired = maybeAlert<DummyQueue>(s);
    REQUIRE(fired);
    REQUIRE(queue_alerts::alertCount() == 1);
}

// MuxQueue threshold path
#include "core/runtime/mux_queue.hh"
struct DummyMsg { int id; std::uint8_t priority; };
using MuxQ = launcher::core::runtime::MuxQueueBackend<DummyMsg, 8, 3>;

TEST_CASE("queue_alerts muxqueue threshold 85%", "[queue_alerts][mux]") {
    launcher::core::runtime::QueueStats s{};
    queue_alerts::resetAlertCount();

    s.high_water_pct = 80; // just below threshold
    bool fired = maybeAlert<MuxQ>(s);
    REQUIRE_FALSE(fired);

    s.high_water_pct = 90; // above 85
    fired = maybeAlert<MuxQ>(s);
    REQUIRE(fired);
    REQUIRE(queue_alerts::alertCount() == 1);
} 