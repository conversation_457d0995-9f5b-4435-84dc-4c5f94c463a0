#include "service_name_phf.h"
#include <catch2/catch_test_macros.hpp>

using namespace launcher::core::foundation;

TEST_CASE("Service name perfect hash lookup", "[service_registry][phf]") {
    REQUIRE(lookupService("ServiceRegistry") == ServiceId::kServiceRegistry);
    REQUIRE(lookupService("DiagnosticsService") == ServiceId::kDiagnosticsService);
    REQUIRE(!lookupService("NonExistentService"));
}

TEST_CASE("service_name_phf maps every service name", "[service_name_phf]") {
    for (std::size_t i = 0; i < kServiceIdNames.size(); ++i) {
        const std::string_view name = kServiceIdNames[i];
        auto id_opt = lookupService(name);
        REQUIRE(id_opt);
        REQUIRE(*id_opt == static_cast<ServiceId>(i));
    }
}

TEST_CASE("service_name_phf returns nullopt for unknown", "[service_name_phf]") {
    REQUIRE_FALSE(lookupService("NonExistentService"));
} 