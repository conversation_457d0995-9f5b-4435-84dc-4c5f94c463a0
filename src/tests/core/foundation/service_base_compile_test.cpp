#include "core/foundation/service_base.h"
#include "core/foundation/registry.h"
#include "core/async/executor_service.h"
#include "core/memory/arena_allocator_service.h"
#include "core/mcp/mcp_client_service.h"
#include "core/diagnostics/diagnostics_service.h"

using namespace launcher::core;

namespace {
struct DepSvc final : foundation::ServiceBase<DepSvc> {
    static constexpr foundation::ServiceId kId = foundation::ServiceId::kArenaAllocatorSvc; // reuse existing id for convenience
    explicit DepSvc(foundation::ServiceRegistry& reg) : ServiceBase<DepSvc>(reg) {}
    util::Result<void> start() override { return util::Result<void>::success(); }
    void             stop() noexcept override {}
};

struct FooSvc final : foundation::ServiceBase<FooSvc, async::ExecutorService, DepSvc> {
    static constexpr foundation::ServiceId kId = static_cast<foundation::ServiceId>(0xFE);
    explicit FooSvc(foundation::ServiceRegistry& reg) : ServiceBase<FooSvc, async::ExecutorService, DepSvc>(reg) {}
    util::Result<void> start() override { return util::Result<void>::success(); }
    void             stop() noexcept override {}
};

// Helper concept to detect whether get<U>() is well-formed.
template <typename Svc, typename Dep>
concept HasGet = requires(Svc& s) { s.template get<Dep>(); };

static_assert(HasGet<FooSvc, async::ExecutorService>, "Expected dependency missing");

// McpClientService should expose Diagnostics dependency only.
static_assert(HasGet<mcp::McpClientService, diagnostics::DiagnosticsService>, "Diagnostics dep missing");
} // namespace

int main() { return 0; } 