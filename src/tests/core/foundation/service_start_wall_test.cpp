#include "../../core/foundation/registry.h"
#include "../../core/memory/arena_allocator_service.h"
#include "../../core/async/executor_service.h"
#include "../../core/events/event_bus_service.h"
#include "../../core/diagnostics/diagnostics_service.h"

#include <catch2/catch_test_macros.hpp>
#include <chrono>

using namespace launcher::core;

TEST_CASE("ServiceRegistry start time < 50ms", "[service_registry][perf]") {
    foundation::ServiceRegistry registry;

    memory::ArenaAllocatorSvc arena{registry};
    async::ExecutorService    exec{registry};
    events::EventBusService   bus{registry};
    diagnostics::DiagnosticsService diag{registry};

    exec.setDiagnostics(&diag);
    bus.setDiagnostics(&diag);

    const auto t0 = std::chrono::steady_clock::now();
    REQUIRE(registry.startAll());
    const auto t1 = std::chrono::steady_clock::now();

    const auto us = std::chrono::duration_cast<std::chrono::microseconds>(t1 - t0).count();
    REQUIRE(us < 50000); // 50 ms budget

    registry.stopAll();
} 