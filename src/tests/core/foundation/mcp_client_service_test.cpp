#include <catch2/catch_test_macros.hpp>

#include "core/foundation/registry.h"
#include "core/memory/arena_allocator_service.h"
#include "core/async/executor_service.h"
#include "core/events/event_bus_service.h"
#include "core/diagnostics/diagnostics_service.h"
#include "core/mcp/mcp_client_service.h"

using namespace launcher::core;

TEST_CASE("McpClientService starts/stops with registry", "[services]") {
    foundation::ServiceRegistry registry;

    memory::ArenaAllocatorSvc arena{registry};
    async::ExecutorService    exec{registry};
    events::EventBusService   eventBus{registry};
    diagnostics::DiagnosticsService diag{registry};
    mcp::McpClientService     mcp{registry};

    auto startRes = registry.startAll();
    REQUIRE(startRes);

    registry.stopAll();
} 