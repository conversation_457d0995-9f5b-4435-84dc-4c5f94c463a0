#include <catch2/catch_test_macros.hpp>

#include <nlohmann/json.hpp>
#include <fstream>
#include <string>
#include <vector>
#include <algorithm>
#include <cctype>
#include <filesystem>

namespace fs = std::filesystem;
using json    = nlohmann::json;

namespace {
std::vector<std::string> loadCapabilitiesFromManifest(const fs::path& manifest_path) {
    std::ifstream in(manifest_path);
    REQUIRE(in);
    json root;
    in >> root;

    const auto& defs = root.at("definitions");
    const auto& cap_node = defs.at("capability");
    const auto& enum_node = cap_node.at("enum");
    std::vector<std::string> caps = enum_node.get<std::vector<std::string>>();
    std::sort(caps.begin(), caps.end());
    caps.erase(std::unique(caps.begin(), caps.end()), caps.end());
    return caps;
}

std::vector<std::string> loadCapabilitiesGolden(const fs::path& golden_path) {
    std::ifstream in(golden_path);
    REQUIRE(in);
    std::vector<std::string> caps;
    std::string line;
    while (std::getline(in, line)) {
        // Trim CR or trailing/leading whitespace
        while (!line.empty() && (line.back() == '\r' || std::isspace(static_cast<unsigned char>(line.back())))) {
            line.pop_back();
        }
        size_t front = 0;
        while (front < line.size() && std::isspace(static_cast<unsigned char>(line[front]))) {
            ++front;
        }
        if (front < line.size()) {
            caps.emplace_back(line.substr(front));
        }
    }
    std::sort(caps.begin(), caps.end());
    return caps;
}
}

TEST_CASE("Capability list matches golden", "[capability-gen]") {
    const fs::path project_root = fs::path{PROJECT_SOURCE_DIR};
    const fs::path manifest_path = project_root / "manifest.schema.json";
    const fs::path golden_path   = project_root / "src" / "tests" / "golden" / "capabilities.golden.txt";

    auto manifest_caps = loadCapabilitiesFromManifest(manifest_path);
    auto golden_caps   = loadCapabilitiesGolden(golden_path);

    REQUIRE(manifest_caps == golden_caps);
} 