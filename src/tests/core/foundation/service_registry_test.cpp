#include "../../core/foundation/registry.h"
#include <catch2/catch_test_macros.hpp>

using namespace launcher::core::foundation;

namespace {

class DummyArenaService : public IService {
 public:
    static constexpr ServiceId kId = ServiceId::kArenaAllocatorSvc;

    [[nodiscard]] ServiceId id() const noexcept override { return kId; }
    launcher::core::util::Result<void> start() override { started = true; return launcher::core::util::Result<void>::success(); }
    void stop() noexcept override { stopped = true; }

    bool started{false};
    bool stopped{false};
};

class DummyExecutorService : public IService {
 public:
    static constexpr ServiceId kId = ServiceId::kExecutorService;

    [[nodiscard]] ServiceId id() const noexcept override { return kId; }
    launcher::core::util::Result<void> start() override { started = true; return launcher::core::util::Result<void>::success(); }
    void stop() noexcept override { stopped = true; }

    bool started{false};
    bool stopped{false};
};

} // namespace

TEST_CASE("ServiceRegistry start/stop order", "[service_registry]") {
    ServiceRegistry registry;

    DummyArenaService arena;
    DummyExecutorService exec;

    REQUIRE(registry.registerService(arena));
    REQUIRE(registry.registerService(exec));

    // Start all – should succeed and call start on both services.
    auto start_res = registry.startAll();
    REQUIRE(start_res);

    registry.stopAll();
}

TEST_CASE("ServiceRegistry duplicate registration", "[service_registry]") {
    ServiceRegistry registry;
    DummyArenaService arena;

    REQUIRE(registry.registerService(arena));
    auto dup_res = registry.registerService(arena);
    REQUIRE_FALSE(dup_res);
    REQUIRE(dup_res.error() == KaiError::AlreadyRegistered);
} 