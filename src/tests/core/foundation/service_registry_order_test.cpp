#include "../../core/foundation/registry.h"
#include <catch2/catch_test_macros.hpp>
#include <vector>

using namespace launcher::core::foundation;

namespace {

static std::vector<ServiceId>* g_seq = nullptr;

class StubArenaSvc : public IService {
 public:
    static constexpr ServiceId kId = ServiceId::kArenaAllocatorSvc;
    [[nodiscard]] ServiceId id() const noexcept override { return kId; }
    launcher::core::util::Result<void> start() override {
        g_seq->push_back(kId);
        return launcher::core::util::Result<void>::success();
    }
    void stop() noexcept override {}
};

class StubExecutorSvc : public IService {
 public:
    static constexpr ServiceId kId = ServiceId::kExecutorService;
    [[nodiscard]] ServiceId id() const noexcept override { return kId; }
    launcher::core::util::Result<void> start() override {
        g_seq->push_back(kId);
        return launcher::core::util::Result<void>::success();
    }
    void stop() noexcept override {}
};

} // namespace

TEST_CASE("ServiceRegistry starts services in deterministic compile-time order", "[service_registry][order]") {
    std::vector<ServiceId> seq;
    g_seq = &seq;

    ServiceRegistry reg;
    StubArenaSvc arena;
    StubExecutorSvc exec;

    REQUIRE(reg.registerService(arena));
    REQUIRE(reg.registerService(exec));

    REQUIRE(reg.startAll());
    reg.stopAll();

    REQUIRE(seq.size() == 2);
    REQUIRE(seq[0] == ServiceId::kArenaAllocatorSvc);
    REQUIRE(seq[1] == ServiceId::kExecutorService);
} 