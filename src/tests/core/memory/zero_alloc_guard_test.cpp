#include "core/memory/zero_alloc_guard.h"
#include "core/memory/arena_allocator.h"
#include <catch2/catch_test_macros.hpp>

using namespace launcher::core::memory;

TEST_CASE("KAI_VERIFY_ZERO_ALLOC passes when no allocation", "[zero_alloc]") {
    bool threw = false;
    try {
        KAI_VERIFY_ZERO_ALLOC({
            int local = 42;
            (void)local;
        });
    } catch (...) {
        threw = true;
    }
    REQUIRE_FALSE(threw);
}

TEST_CASE("KAI_VERIFY_ZERO_ALLOC throws on allocation", "[zero_alloc]") {
    bool threw = false;
    try {
        KAI_VERIFY_ZERO_ALLOC({
            void* p = alloc(64);
            launcher::core::memory::free(p);
        });
    } catch (const std::runtime_error&) {
        threw = true;
    }
    REQUIRE(threw);
} 