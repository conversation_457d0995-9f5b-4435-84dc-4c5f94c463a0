#include "core/memory/arena_allocator.h"
#include <catch2/catch_test_macros.hpp>
#include <vector>

using namespace launcher::core::memory;

TEST_CASE("arena_allocator basic alloc/free", "[arena]") {
    constexpr std::size_t kCount = 1024;
    std::vector<void*> ptrs;
    ptrs.reserve(kCount);

    // allocate
    for (std::size_t i = 0; i < kCount; ++i) {
        void* p = alloc(64);
        REQUIRE(p != nullptr);
        ptrs.push_back(p);
    }

    // free
    for (void* p : ptrs) {
        launcher::core::memory::free(p);
    }
}

TEST_CASE("arena_allocator stats increase", "[arena]") {
    ArenaStats before = getStats();
    void* p = alloc(128);
    REQUIRE(p != nullptr);
    ArenaStats mid = getStats();
    launcher::core::memory::free(p);
    ArenaStats after = getStats();

    // We can't guarantee exact counts, but allocations should increase.
    REQUIRE(mid.allocs >= before.allocs + 1);
    REQUIRE(after.frees >= before.frees + 1);
} 