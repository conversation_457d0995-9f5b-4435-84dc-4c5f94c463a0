#include "core/memory/slab_arena.h"
#include <catch2/catch_test_macros.hpp>
#include <string_view>

using namespace launcher::core::memory;

TEST_CASE("SlabArena append produces stable views", "[slab_arena]") {
    SlabArena arena;
    std::string_view v1 = arena.append("hello", 5);
    std::string_view v2 = arena.append(", world", 7);

    REQUIRE(v1 == "hello");
    REQUIRE(v2 == ", world");

    // Subsequent appends must not invalidate earlier views
    std::string_view before = v1;
    for (int i = 0; i < 1000; ++i) {
        arena.append("x", 1);
    }
    REQUIRE(v1 == before);
}

TEST_CASE("SlabArena handles > block size", "[slab_arena]") {
    SlabArena arena;
    constexpr std::size_t big = SlabArena::kBlockSize + 1024;
    std::string huge(big, 'a');
    std::string_view v = arena.append(huge.data(), huge.size());
    REQUIRE(v.size() == big);
    REQUIRE(v.front() == 'a');
    REQUIRE(v.back() == 'a');
} 