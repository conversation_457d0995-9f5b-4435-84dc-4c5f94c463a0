#include "core/memory/zero_alloc_guard.h"
#include "core/container/adaptive_cache.hh"
#include "core/runtime/ring_queue_backend.hh"
#include "core/security/verification_store.hh"
#include <catch2/catch_test_macros.hpp>
#include <algorithm>

using namespace launcher;

TEST_CASE("Zero-alloc guard on mixed hot-path operations", "[allocator][zero_alloc][hotpath]") {
    // Warm-up phase – construct data structures and pre-populate them so that
    // the subsequent guarded block hits fully initialised, cache-hot paths.
    core::container::AdaptiveCache<uint64_t, uint64_t> cache{64, 4};
    cache.getOrInsert(42, [] { return 7ULL; }); // prime

    core::runtime::RingQueueBackend<int, 64> queue;
    queue.try_push(11);
    int tmp{};
    queue.try_pop(tmp);

    core::security::VerificationStore store{1024};
    core::security::CDHash cd{};
    std::fill(std::begin(cd.bytes), std::end(cd.bytes), 0xAB);
    core::security::Verdict allow{core::security::Verdict::Code::kAllowed};
    store.insert(cd, allow);

    bool threw = false;
    try {
        KAI_VERIFY_ZERO_ALLOC({
            // AdaptiveCache hit
            auto &v = cache.getOrInsert(42, [] { return 0ULL; });
            (void)v;
            // RingQueue push + pop on pre-allocated buffer
            queue.try_push(123);
            int out{};
            queue.try_pop(out);
            // VerificationStore lookup (hit)
            auto exp = store.find(cd);
            (void)exp;
        });
    } catch (...) {
        threw = true;
    }
    REQUIRE_FALSE(threw);
} 