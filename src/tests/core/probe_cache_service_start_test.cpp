#include "snapshot/probe_cache_service.hh"
#include "core/foundation/registry.h"
#include "core/memory/arena_allocator_service.h"
#include "core/diagnostics/diagnostics_service.h"
#include "core/async/executor_service.h"
#include "core/events/event_bus_service.h"
#include <catch2/catch_test_macros.hpp>

TEST_CASE("ProbeCacheSvc boot", "[probe_cache][service]") {
    using namespace launcher;
    core::foundation::ServiceRegistry reg;
    core::memory::ArenaAllocatorSvc arena{reg};
    core::async::ExecutorService    exec{reg};
    core::events::EventBusService   bus{reg, nullptr};
    core::diagnostics::DiagnosticsService diag{reg};
    bus.setDiagnostics(&diag);
    exec.setDiagnostics(&diag);
    snapshot::ProbeCacheSvc pcs{reg, diag};

    REQUIRE(reg.startAll());
    auto stats = pcs.stats();
    REQUIRE(stats.flush_count == 0);
    reg.stopAll();
} 