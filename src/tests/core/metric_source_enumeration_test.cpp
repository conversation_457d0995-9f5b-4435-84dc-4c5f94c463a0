#include "core/util/metrics.hh"
#include "core/security/verification_store.hh"
#include <catch2/catch_test_macros.hpp>
#include <tuple>
#include <type_traits>

using namespace launcher::core;

TEST_CASE("MetricSource enumeration captures VerificationStore", "[metrics][verification_store]") {
    security::VerificationStore vs;

    // Verify concept satisfaction at compile-time
    static_assert(util::MetricSource<security::VerificationStore>);

    util::for_each_type<std::tuple<security::VerificationStore>>([&vs]<typename T>() {
        if constexpr (std::is_same_v<T, security::VerificationStore>) {
            auto s = vs.stats();
            REQUIRE(s.hit == 0);
            REQUIRE(s.miss == 0);
        }
    });
} 