#include <catch2/catch_test_macros.hpp>

#include "core/http/sse_decoder.h"
#include "core/memory/slab_arena.h"

using http::SSEDecoder;
using http::Delta;
using launcher::core::memory::SlabArena;

static Delta feedLine(SSEDecoder& dec, std::string_view line) {
    auto maybe = dec.feed(line);
    REQUIRE(maybe.has_value());
    return *maybe;
}

TEST_CASE("OpenAI responses output_text delta", "[sse_decoder_responses]") {
    auto dec = SSEDecoder::create("openai");
    auto sink = std::make_shared<SlabArena>();
    dec->setSink(sink);

    Delta d = feedLine(*dec, "data: {\"type\":\"response.output_text.delta\",\"delta\":\"Hello\"}\n");
    REQUIRE(d.role == "assistant");
    REQUIRE(d.content == "Hello");
    REQUIRE_FALSE(d.done);
}

TEST_CASE("OpenAI responses reasoning delta", "[sse_decoder_responses]") {
    auto dec = SSEDecoder::create("openai");
    auto sink = std::make_shared<SlabArena>();
    dec->setSink(sink);

    Delta d = feedLine(*dec, "data: {\"type\":\"response.reasoning.delta\",\"delta\":\"Think\"}\n");
    REQUIRE(d.role == "thought");
    REQUIRE(d.content == "Think");
}

TEST_CASE("OpenAI responses done", "[sse_decoder_responses]") {
    auto dec = SSEDecoder::create("openai");
    auto sink = std::make_shared<SlabArena>();
    dec->setSink(sink);

    Delta d = feedLine(*dec, "data: {\"type\":\"response.completed\"}\n");
    REQUIRE(d.done);
} 