#include <catch2/catch_test_macros.hpp>

#include "core/http/responses_decoder.h"
#include "core/memory/slab_arena.h"

using http::ResponsesDecoder;
using http::ResponseEvent;
using http::ResponseKind;
using launcher::core::memory::SlabArena;

static ResponseEvent feedLine(ResponsesDecoder& dec, std::string_view line) {
    auto ev = dec.feed(line);
    REQUIRE(ev.has_value());
    return *ev;
}

TEST_CASE("Image partial chunk", "[responses_decoder]") {
    auto dec = http::createResponsesDecoder("openai");
    auto sink = std::make_shared<SlabArena>();
    dec->setSink(sink);
    auto ev = feedLine(*dec, "data: {\"type\":\"response.image_generation_call.partial_image\",\"partial_image\":\"QUFB\",\"partial_image_index\":1}\n");
    REQUIRE(ev.kind == ResponseKind::kImageChunk);
}

TEST_CASE("Tool call event", "[responses_decoder]") {
    auto dec = http::createResponsesDecoder("openai");
    dec->setSink(std::make_shared<SlabArena>());
    auto ev = feedLine(*dec, "data: {\"type\":\"response.tool_call\",\"name\":\"foo\"}\n");
    REQUIRE(ev.kind == ResponseKind::kToolCall);
}

TEST_CASE("MCP approval", "[responses_decoder]") {
    auto dec = http::createResponsesDecoder("openai");
    dec->setSink(std::make_shared<SlabArena>());
    auto ev = feedLine(*dec, "data: {\"type\":\"response.mcp_approval_request\",\"id\":\"x\"}\n");
    REQUIRE(ev.kind == ResponseKind::kMcpApproval);
}

TEST_CASE("Error event", "[responses_decoder]") {
    auto dec = http::createResponsesDecoder("openai");
    dec->setSink(std::make_shared<SlabArena>());
    auto ev = feedLine(*dec, "data: {\"type\":\"response.error\",\"code\":\"Foo\",\"message\":\"nope\"}\n");
    REQUIRE(ev.kind == ResponseKind::kError);
} 