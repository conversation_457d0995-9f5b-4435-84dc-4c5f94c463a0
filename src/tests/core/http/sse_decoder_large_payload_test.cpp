#include "core/http/sse_decoder.h"
#include "core/memory/slab_arena.h"
#include <catch2/catch_test_macros.hpp>
#include <string>
#include <vector>

TEST_CASE("OpenAI SSE decoder handles large payload crossing slab boundary", "[sse][large]") {
    constexpr size_t kPayloadSize = 70 * 1024; // 70 KiB > 2x slab block
    std::string long_content(kPayloadSize, 'x');

    std::string json = "data: {\"choices\":[{\"delta\":{\"content\":\"" + long_content + "\"}}]}\n";

    auto dec = http::SSEDecoder::create("openai");
    REQUIRE(dec);
    auto arena = std::make_shared<launcher::core::memory::SlabArena>();
    dec->setSink(arena);

    auto first_view = arena->append("init", 4);
    const char* base_ptr = first_view.data();

    auto tokOpt = dec->feed(json);
    REQUIRE(tokOpt.has_value());
    REQUIRE(tokOpt->content.size() == kPayloadSize);
    // arena grew
    REQUIRE(arena->size() > first_view.size());
    // initial pointer stable
    REQUIRE(first_view.data() == base_ptr);
} 