#include "core/http/sse_decoder.h"
#include <catch2/catch_test_macros.hpp>
#include <string>
#include "core/memory/slab_arena.h"

TEST_CASE("OpenAI SSE decoder handles error event", "[sse][error]") {
    auto dec = http::SSEDecoder::create("openai");
    REQUIRE(dec);
    auto arena = std::make_shared<launcher::core::memory::SlabArena>();
    dec->setSink(arena);
    std::string line = "data: {\"type\":\"error\",\"error\":{\"type\":\"rate_limit_error\",\"message\":\"Too fast\"}}\n";
    auto tokOpt = dec->feed(line);
    REQUIRE(tokOpt.has_value());
    REQUIRE(tokOpt->done == true);
} 