#include <catch2/catch_test_macros.hpp>

#include "core/http/sse_decoder.h"
#include "core/memory/slab_arena.h"

using http::SSEDecoder;
using http::Delta;
using launcher::core::memory::SlabArena;

static Delta feedLine(SSEDecoder& dec, std::string_view line) {
    auto maybe = dec.feed(line);
    REQUIRE(maybe.has_value());
    return *maybe;
}

TEST_CASE("OpenAI new delta content", "[sse_decoder_simd]") {
    auto dec = SSEDecoder::create("openai");
    auto sink = std::make_shared<SlabArena>();
    dec->setSink(sink);

    Delta d = feedLine(*dec, "data: {\"delta\":{\"content\":\"Hello\"}}\n");
    REQUIRE(d.role == "assistant");
    REQUIRE(d.content == "Hello");
    REQUIRE_FALSE(d.done);
}

TEST_CASE("OpenAI reasoning delta", "[sse_decoder_simd]") {
    auto dec = SSEDecoder::create("openai");
    auto sink = std::make_shared<SlabArena>();
    dec->setSink(sink);

    Delta d = feedLine(*dec, "data: {\"delta\":{\"reasoning_content\":\"Think\"}}\n");
    REQUIRE(d.role == "thought");
    REQUIRE(d.content == "Think");
    REQUIRE_FALSE(d.done);
}

TEST_CASE("OpenAI done token", "[sse_decoder_simd]") {
    auto dec = SSEDecoder::create("openai");
    auto sink = std::make_shared<SlabArena>();
    dec->setSink(sink);

    Delta d = feedLine(*dec, "data: {\"type\":\"response.completed\"}");
    REQUIRE(d.done);
}

TEST_CASE("Anthropic thinking delta", "[sse_decoder_simd]") {
    auto dec = SSEDecoder::create("anthropic");
    auto sink = std::make_shared<SlabArena>();
    dec->setSink(sink);

    Delta d = feedLine(*dec, "data: {\"delta\":{\"type\":\"thinking_delta\",\"thinking\":\"Hmm\"}}");
    REQUIRE(d.role == "thought");
    REQUIRE(d.content == "Hmm");
}

TEST_CASE("Anthropic text delta", "[sse_decoder_simd]") {
    auto dec = SSEDecoder::create("anthropic");
    auto sink = std::make_shared<SlabArena>();
    dec->setSink(sink);
    Delta d = feedLine(*dec, "data: {\"delta\":{\"text\":\"Hi\"}}");
    REQUIRE(d.role == "assistant");
    REQUIRE(d.content == "Hi");
} 