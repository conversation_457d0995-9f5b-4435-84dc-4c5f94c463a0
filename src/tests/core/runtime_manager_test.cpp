#include <catch2/catch_test_macros.hpp>

#include "core/plugins/runtime_manager_service.h"
#include "core/plugins/abi.h"
#include "core/foundation/registry.h"
#include "core/memory/arena_allocator_service.h"
#include "core/async/executor_service.h"
#include "core/events/event_bus_service.h"
#include "core/diagnostics/diagnostics_service.h"
#include "core/security/verification_store.hh"

#include <filesystem>

using namespace launcher::core::plugins;

#ifndef SAMPLE_NULL_PLUGIN_PATH
#error "SAMPLE_NULL_PLUGIN_PATH not defined during compile"
#endif

TEST_CASE("RuntimeManager loads/unloads null plugin 100x", "[plugin]") {
    const std::filesystem::path pluginPath{SAMPLE_NULL_PLUGIN_PATH};
    REQUIRE(std::filesystem::exists(pluginPath));

    launcher::core::foundation::ServiceRegistry registry;
    launcher::core::memory::ArenaAllocatorSvc arena{registry};
#if defined(RPMALLOC_FIRST_CLASS_HEAPS) && (RPMALLOC_FIRST_CLASS_HEAPS)
    arena.disableHeapTracking();
#endif
    launcher::core::async::ExecutorService    exec{registry};
    launcher::core::events::EventBusService   bus{registry};
    launcher::core::diagnostics::DiagnosticsService diag{registry};
    launcher::core::security::VerificationStore      vstore;
    exec.setDiagnostics(&diag);
    bus.setDiagnostics(&diag);

    RuntimeManagerSvc mgr{registry, vstore};

    for (int i = 0; i < 5; ++i) {
        auto result = mgr.probeLibrary(pluginPath);
        REQUIRE(result);
        REQUIRE(result.value().info.runtime == KAI_RUNTIME_NULL);
    }
} 