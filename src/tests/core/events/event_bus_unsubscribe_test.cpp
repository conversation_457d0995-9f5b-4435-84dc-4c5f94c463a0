#include "../../../core/async/executor_service.h"
#include "../../../core/foundation/registry.h"
#include "../../../core/memory/arena_allocator_service.h"
#include "../../../core/events/event_bus_service.h"
#include <catch2/catch_test_macros.hpp>
#include <thread>

using namespace launcher::core;

struct RaceEvt { int value; };

TEST_CASE("EventBus unsubscribe race", "[event_bus][unsubscribe]") {
    foundation::ServiceRegistry registry;
    memory::ArenaAllocatorSvc    arena{registry};
    async::ExecutorService       exec{registry};
    exec.start();
    events::EventBusService bus{registry};
    bus.start();

    std::atomic<int> received{0};
    auto handle = bus.subscribe<RaceEvt>([&](const RaceEvt* /*unused*/) { received.fetch_add(1, std::memory_order_relaxed); });

    constexpr int kMsgs = 1000;
    std::thread pub([&](){
        for (int i = 0; i < kMsgs; ++i) {
            auto evt = std::make_shared<RaceEvt>(RaceEvt{i});
            bus.publish(std::static_pointer_cast<const RaceEvt>(evt));
        }
    });

    // unsubscribe while publishing is ongoing
    handle.unsubscribe();

    pub.join();

    // Received count should be <= kMsgs (could be less due to race) but not crash.
    REQUIRE(received.load() <= kMsgs);

    bus.stop();
    exec.stop();
} 