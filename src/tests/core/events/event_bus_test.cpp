#include "../../../core/async/executor_service.h"
#include "../../../core/foundation/registry.h"
#include "../../../core/memory/arena_allocator_service.h"
#include "../../../core/events/event_bus_service.h"
#include <catch2/catch_test_macros.hpp>

using namespace launcher::core;

struct DummyEvent { int value; };

TEST_CASE("EventBus publish/subscribe", "[event_bus]") {
    foundation::ServiceRegistry registry;
    memory::ArenaAllocatorSvc    arena{registry};
    async::ExecutorService       exec{registry};
    exec.start();

    events::EventBusService bus{registry};
    bus.start();

    std::atomic<int> received{0};
    auto sub = bus.subscribe<DummyEvent>([&](const DummyEvent* evt_ptr) {
        if (evt_ptr) {
            received.store(evt_ptr->value, std::memory_order_release);
        }
    });

    {
        auto evt = std::make_shared<DummyEvent>(DummyEvent{42});
        bus.publish(std::static_pointer_cast<const DummyEvent>(evt));
    }

    // Wait up to 100ms for delivery
    for (int i = 0; i < 100; ++i) {
        if (received.load(std::memory_order_acquire) == 42) break;
        std::this_thread::sleep_for(std::chrono::milliseconds(1));
    }

    REQUIRE(received.load() == 42);

    bus.stop();
    exec.stop();
} 