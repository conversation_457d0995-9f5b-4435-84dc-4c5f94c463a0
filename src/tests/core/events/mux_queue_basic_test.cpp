#include "../../../core/runtime/mux_queue.hh"
#include <catch2/catch_test_macros.hpp>

using namespace launcher::core;

struct Task {
    int         id;
    std::uint8_t priority;          // 0 – high, 1 – medium, 2 – low
};

template <std::size_t CapPerLvl>
using MuxQ = runtime::MuxQueueBackend<Task, CapPerLvl, 3>;

TEST_CASE("MuxQueue respects priority then FIFO", "[mux_queue][basic]") {
    constexpr std::size_t kSubCap = 8;
    MuxQ<kSubCap> q;

    // Push low-priority tasks [id 0..7] (priority 2)
    for (int i = 0; i < 8; ++i) {
        REQUIRE(q.try_push(Task{i, 2}));
    }

    // Push high-priority tasks [id 100..101] (priority 0)
    REQUIRE(q.try_push(Task{100, 0}));
    REQUIRE(q.try_push(Task{101, 0}));

    // Pop: should get high-priority first in FIFO order.
    Task out{0, 0};
    REQUIRE(q.try_pop(out));
    REQUIRE(out.id == 100);
    REQUIRE(out.priority == 0);

    REQUIRE(q.try_pop(out));
    REQUIRE(out.id == 101);
    REQUIRE(out.priority == 0);

    // Remaining low-priority tasks should now come FIFO 0..7
    for (int i = 0; i < 8; ++i) {
        REQUIRE(q.try_pop(out));
        REQUIRE(out.id == i);
        REQUIRE(out.priority == 2);
    }

    // Queue empty
    REQUIRE_FALSE(q.try_pop(out));
} 