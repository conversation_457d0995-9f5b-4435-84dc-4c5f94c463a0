#include "../../../core/async/executor_service.h"
#include "../../../core/foundation/registry.h"
#include "../../../core/memory/arena_allocator_service.h"
#include "../../../core/events/event_bus_service.h"
#include "../../../core/diagnostics/diagnostics_service.h"

#include <catch2/catch_test_macros.hpp>
#include <thread>

using namespace launcher::core;

struct DummyEvt {};

TEST_CASE("EventBus idle CPU gauge", "[event_bus][idle]") {
    // 1. Bootstrap executor and services ---------------------------------
    foundation::ServiceRegistry registry;
    memory::ArenaAllocatorSvc    arena{registry};
    async::ExecutorService       exec{registry};
    REQUIRE(exec.start());

    events::EventBusService bus{registry};
    diagnostics::DiagnosticsService diag{registry};
    bus.setDiagnostics(&diag);

    REQUIRE(bus.start());
    REQUIRE(diag.start());

    // 2. Let the system idle for ~1.2 s so dispatcher<PERSON><PERSON> collects stats
    // Publish one event to ensure dispatcher loop runs at least once to update gauge.
    {
        auto evt = std::make_shared<DummyEvt>();
        bus.publish(std::static_pointer_cast<const DummyEvt>(evt));
    }
    std::this_thread::sleep_for(std::chrono::milliseconds(1200));

    // 3. Flush diagnostics and verify idle gauge -------------------------
    bool gauge_found = false;
    int64_t idle_pct = 100; // worst-case default

    for (int i = 0; i < 10 && !gauge_found; ++i) {
        diag.flushForTest();
        auto snap = diag.snapshot();
        if (snap.contains("gauges") && snap["gauges"].contains("eventbus.idle_cpu_pct")) {
            idle_pct = snap["gauges"]["eventbus.idle_cpu_pct"].get<int64_t>();
            gauge_found = true;
            break;
        }
        std::this_thread::sleep_for(std::chrono::milliseconds(50));
    }

    REQUIRE(gauge_found);
    REQUIRE(idle_pct >= 95); // For full idle, expect high idle percentage

    // 4. Clean shutdown ---------------------------------------------------
    diag.stop();
    bus.stop();
    exec.stop();
} 