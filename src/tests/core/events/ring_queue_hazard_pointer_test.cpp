#include "../../../core/runtime/ring_queue_backend.hh"
#include <catch2/catch_test_macros.hpp>
#include <atomic>
#include <thread>
#include <vector>

using namespace launcher::core;

TEST_CASE("RingQueue ABA hazard stress-test", "[ring_queue][hazard][stress]") {
    constexpr std::size_t kCap = 128;                 // small capacity to force wrap-around
    constexpr int kProducers   = 4;
    constexpr int kOpsPerProd  = 200'000;             // total events = 800k
    constexpr int kTotal       = kProducers * kOpsPerProd;

    using Q = runtime::RingQueueBackend<int, kCap>;
    Q queue;

    // Bitset to detect duplicate pops (false positives extremely unlikely)
    std::vector<std::atomic<uint8_t>> seen(kTotal);
    for (auto& b : seen) b.store(0, std::memory_order_relaxed);

    std::atomic<int> produced{0};
    std::atomic<int> consumed{0};
    std::atomic<bool> stop{false};

    // Consumer thread -----------------------------------------------------
    std::thread consumer([&]() {
        int val;
        while (!stop.load(std::memory_order_acquire)) {
            if (queue.try_pop(val)) {
                REQUIRE(val >= 0);
                REQUIRE(val < kTotal);
                // Expect unique value.
                REQUIRE(seen[val].exchange(1, std::memory_order_relaxed) == 0);
                ++consumed;
            } else {
                std::this_thread::yield();
            }
        }
        // Drain remaining items
        while (queue.try_pop(val)) {
            REQUIRE(val >= 0);
            REQUIRE(val < kTotal);
            REQUIRE(seen[val].exchange(1, std::memory_order_relaxed) == 0);
            ++consumed;
        }
    });

    // Producer threads ----------------------------------------------------
    std::vector<std::thread> producers;
    for (int p = 0; p < kProducers; ++p) {
        producers.emplace_back([&, p]() {
            const int base = p * kOpsPerProd;
            for (int i = 0; i < kOpsPerProd; ++i) {
                const int v = base + i;
                while (!queue.try_push(v)) {
                    std::this_thread::yield();
                }
                ++produced;
            }
        });
    }

    for (auto& t : producers) t.join();
    stop.store(true, std::memory_order_release);
    consumer.join();

    REQUIRE(produced.load() == kTotal);
    REQUIRE(consumed.load() == kTotal);

    // Verify each value popped exactly once.
    for (int i = 0; i < kTotal; ++i) {
        REQUIRE(seen[i].load(std::memory_order_relaxed) == 1);
    }

    // Queue should be empty.
    int dummy;
    REQUIRE_FALSE(queue.try_pop(dummy));
} 