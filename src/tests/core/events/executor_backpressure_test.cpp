#include "../../core/foundation/registry.h"
#include "../../core/async/executor_service.h"
#include "../../core/diagnostics/diagnostics_service.h"
#include "../../core/memory/arena_allocator_service.h"
#include "../../core/events/event_bus_service.h"

#include <catch2/catch_test_macros.hpp>

using namespace launcher::core;

#ifdef KAI_EXECUTOR_CAPACITY
#undef KAI_EXECUTOR_CAPACITY
#endif
#define KAI_EXECUTOR_CAPACITY 4

TEST_CASE("Executor back-pressure", "[executor][backpressure]") {
    foundation::ServiceRegistry registry;
    memory::ArenaAllocatorSvc arena{registry};
    async::ExecutorService   exec{registry};
    events::EventBusService  bus{registry};
    diagnostics::DiagnosticsService diag{registry};
    exec.setDiagnostics(&diag);

    // Saturate queue BEFORE starting threads to guarantee deterministic overflow.
    const int kCap = static_cast<int>(KAI_EXECUTOR_CAPACITY);
    bool backpressured = false;
    for (int i = 0; i < kCap * 4; ++i) {
        auto res = exec.submit([=]() { /* no-op */ });
        if (!res) {
            backpressured = true;
            REQUIRE(res.error() == foundation::KaiError::BackPressure);
            break;
        }
    }
    REQUIRE(backpressured);

    REQUIRE(registry.startAll());

    registry.stopAll();
} 