// Override any compile-time default so we get deterministic back-pressure.
#ifdef KAI_EVENTBUS_CAPACITY
#undef KAI_EVENTBUS_CAPACITY
#endif
#define KAI_EVENTBUS_CAPACITY 8
// Compile-time capacity for this test is provided via CMake.

#include "../../core/foundation/registry.h"
#include "../../core/events/event_bus_service.h"
#include "../../core/async/executor_service.h"
#include "../../core/diagnostics/diagnostics_service.h"
#include "../../core/memory/arena_allocator_service.h"

#include <catch2/catch_test_macros.hpp>

using namespace launcher::core;

TEST_CASE("EventBus back-pressure", "[event_bus][backpressure]") {
    foundation::ServiceRegistry registry;

    // Instantiate prerequisite services (auto-registered via ServiceBase)
    memory::ArenaAllocatorSvc arena{registry};
    async::ExecutorService    exec{registry};
    events::EventBusService   bus{registry};
    diagnostics::DiagnosticsService diag{registry};

    bus.setDiagnostics(&diag);

    // Subscribe dummy handler so publishing has at least one listener.
    auto h = bus.subscribe<int>([](const int* /*unused*/) {});

    // Publish up to capacity and ensure last one fails.
    // Intentionally publish BEFORE starting services so dispatcher thread is not
    // yet draining the queue, guaranteeing deterministic back-pressure.
    
    const int kCap = static_cast<int>(KAI_EVENTBUS_CAPACITY);
    bool backpressured = false;
    for (int i = 0; i < kCap * 4; ++i) {
        auto res = bus.publish(std::static_pointer_cast<const int>(std::make_shared<int>(i)));
        if (!res) {
            backpressured = true;
            REQUIRE(res.error() == foundation::KaiError::BackPressure);
            break;
        }
    }
    REQUIRE(backpressured);

    REQUIRE(registry.startAll());

    registry.stopAll();
} 