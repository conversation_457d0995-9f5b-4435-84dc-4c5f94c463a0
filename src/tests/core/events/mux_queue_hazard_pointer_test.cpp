#include "../../../core/runtime/mux_queue.hh"
#include <catch2/catch_test_macros.hpp>
#include <atomic>
#include <thread>
#include <vector>
#include <random>

using namespace launcher::core;

struct Task {
    int         id;
    std::uint8_t priority;
};

template <std::size_t CapPerLvl>
using MQ = runtime::MuxQueueBackend<Task, CapPerLvl, 3>;

TEST_CASE("MuxQueue concurrent stress ABA hazard", "[mux_queue][hazard][stress]") {
    constexpr std::size_t kSubCap   = 128;          // small capacity to force wrap
    constexpr int         kProducers   = 4;
    constexpr int         kOpsPerProd  = 200'000;
    constexpr int         kTotal       = kProducers * kOpsPerProd;

    MQ<kSubCap> queue;

    // bitset to detect duplicates
    std::vector<std::atomic<uint8_t>> seen(kTotal);
    for (auto& b : seen) b.store(0, std::memory_order_relaxed);

    std::atomic<int> produced{0};
    std::atomic<int> consumed{0};
    std::atomic<bool> stop{false};

    // consumer thread
    std::thread consumer([&]() {
        Task t;
        while (!stop.load(std::memory_order_acquire)) {
            if (queue.try_pop(t)) {
                REQUIRE(t.id >= 0);
                REQUIRE(t.id < kTotal);
                REQUIRE(seen[t.id].exchange(1, std::memory_order_relaxed) == 0);
                ++consumed;
            } else {
                std::this_thread::yield();
            }
        }
        // drain
        while (queue.try_pop(t)) {
            REQUIRE(t.id >= 0);
            REQUIRE(t.id < kTotal);
            REQUIRE(seen[t.id].exchange(1, std::memory_order_relaxed) == 0);
            ++consumed;
        }
    });

    // producer threads
    std::vector<std::thread> producers;
    for (int p = 0; p < kProducers; ++p) {
        producers.emplace_back([&, p]() {
            std::mt19937 rng(static_cast<uint32_t>(p + 9876));
            std::uniform_int_distribution<int> dist(0, 2);
            const int base = p * kOpsPerProd;
            for (int i = 0; i < kOpsPerProd; ++i) {
                const int id = base + i;
                Task task{id, static_cast<std::uint8_t>(dist(rng))};
                while (!queue.try_push(task)) {
                    std::this_thread::yield();
                }
                ++produced;
            }
        });
    }

    for (auto& t : producers) t.join();
    stop.store(true, std::memory_order_release);
    consumer.join();

    REQUIRE(produced.load() == kTotal);
    REQUIRE(consumed.load() == kTotal);
    for (int i = 0; i < kTotal; ++i) {
        REQUIRE(seen[i].load(std::memory_order_relaxed) == 1);
    }

    Task dummy{0,0};
    REQUIRE_FALSE(queue.try_pop(dummy));
} 