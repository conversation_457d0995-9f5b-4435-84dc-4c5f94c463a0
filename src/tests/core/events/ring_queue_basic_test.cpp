#include "../../../core/runtime/ring_queue_backend.hh"
#include <catch2/catch_test_macros.hpp>

using namespace launcher::core;

TEST_CASE("RingQueue basic push/pop", "[ring_queue][basic]") {
    using Q = runtime::RingQueueBackend<int, 8>;
    Q q;

    // push 8 elements
    for (int i = 0; i < 8; ++i) {
        REQUIRE(q.try_push(std::move(i)));
    }

    // queue should now be full
    REQUIRE_FALSE(q.try_push(42));

    // pop all elements in order
    int val;
    for (int i = 0; i < 8; ++i) {
        REQUIRE(q.try_pop(val));
        REQUIRE(val == i);
    }
    // queue empty now
    REQUIRE_FALSE(q.try_pop(val));
} 