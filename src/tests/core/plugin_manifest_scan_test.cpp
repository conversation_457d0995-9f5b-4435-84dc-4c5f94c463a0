#include "core/plugins/runtime_manager_service.h"
#include "core/foundation/registry.h"
#include "core/memory/arena_allocator_service.h"
#include "core/security/verification_store.hh"
#include "core/async/executor_service.h"
#include "core/events/event_bus_service.h"
#include "core/diagnostics/diagnostics_service.h"

#include <catch2/catch_test_macros.hpp>
#include <filesystem>
#include <fstream>
#include <algorithm>

using namespace launcher::core;
using namespace launcher::core::plugins;

TEST_CASE("manifest scan deduplicates by id", "[plugins][manifest]") {
    foundation::ServiceRegistry registry;
    memory::ArenaAllocatorSvc arena{registry};
    async::ExecutorService    exec{registry};
    events::EventBusService   bus{registry};
    diagnostics::DiagnosticsService diag{registry};
    security::VerificationStore store;

    exec.setDiagnostics(&diag);
    bus.setDiagnostics(&diag);

    exec.start();
    bus.start();
    diag.start();

    RuntimeManagerSvc rms{registry, store};

    // Create isolated temp directory with duplicate manifests
    const auto tmp_root = std::filesystem::temp_directory_path() / "manifest_scan_test";
    std::filesystem::remove_all(tmp_root);
    const auto plugins_dir = tmp_root / "Plugins";
    std::filesystem::create_directories(plugins_dir);

    // Two manifests declaring the same id
    {
        std::ofstream out1(plugins_dir / "a.toml");
        out1 << "id = \"dup\"\n";
        out1 << "runtime = \"null\"\n";
    }
    {
        std::ofstream out2(plugins_dir / "b.toml");
        out2 << "id = \"dup\"\n";
        out2 << "runtime = \"null\"\n";
    }

    // Change CWD so enumeratePluginRoots() discovers ./Plugins
    std::error_code ec;
    std::filesystem::current_path(tmp_root, ec);

    REQUIRE(rms.loadManifests());

    // Registry should contain exactly one meta entry for id="dup"
    const auto metas = rms.registry().list();
    auto dup_count = std::count_if(metas.begin(), metas.end(), [](const plugins::PluginMeta& m){
        return std::string_view(m.descriptor.info.id) == "dup";
    });
    REQUIRE(dup_count == 1);

    diag.stop();
    bus.stop();
    exec.stop();
} 