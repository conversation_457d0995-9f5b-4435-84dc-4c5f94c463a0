#include <catch2/catch_test_macros.hpp>
#include "core/container/adaptive_cache.hh"

using namespace launcher::core::container;

TEST_CASE("AdaptiveCache grows associativity", "[adaptive_cache][grow]") {
    // small cache: 4 sets, start at 2-way
    AdaptiveCache<uint64_t, uint64_t, NullPolicy, 4, 16> cache(/*capacity_sets*/4, /*initial_ways*/2);

    // insert many distinct keys mapping to same set to force evictions & growth
    const uint64_t base_key = 0; // all identical hashes when modulo set_count
    for (uint64_t i = 0; i < 1024; ++i) {
        cache.getOrInsert(base_key + (i << 4), [i] { return i; });
    }

    REQUIRE(cache.ways() >= 4); // expect at least one growth step (2 → 4)
} 