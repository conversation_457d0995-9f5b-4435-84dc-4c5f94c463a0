#include <catch2/catch_test_macros.hpp>
#include "core/container/adaptive_cache.hh"

using namespace launcher::core::container;

TEST_CASE("AdaptiveCache stats counters", "[adaptive_cache]") {
    AdaptiveCache<uint64_t, uint64_t> cache(256); // 256 sets total

    auto &v0 = cache.getOrInsert(1, [] { return 42ull; });
    REQUIRE(v0 == 42ull);

    // hit path
    auto &v1 = cache.getOrInsert(1, [] { return 84ull; });
    REQUIRE(v1 == 42ull);

    // miss path
    auto &v2 = cache.getOrInsert(2, [] { return 7ull; });
    REQUIRE(v2 == 7ull);

    auto st = cache.stats();
    REQUIRE(st.hit == 1);
    REQUIRE(st.miss == 2); // first and third inserts were misses
}

TEST_CASE("AdaptiveCache erase path", "[adaptive_cache][erase]") {
    AdaptiveCache<uint64_t, uint64_t> cache(128);

    cache.getOrInsert(99, [] { return 99ull; });
    auto st_before = cache.stats();
    REQUIRE(st_before.miss == 1);

    REQUIRE(cache.erase(99) == true);
    REQUIRE(cache.erase(99) == false); // already erased

    auto st_after = cache.stats();
    // No change to hit/miss counters on erase.
    REQUIRE(st_after.miss == st_before.miss);
}

TEST_CASE("AdaptiveCache heuristic growth & cap", "[adaptive_cache][grow][threshold]") {
    using namespace launcher::core::container;

    // Single shard cache with small set count to amplify conflict probability.
    constexpr std::size_t kSets = 4;
    AdaptiveCache<uint64_t, uint64_t, NullPolicy, 1, kMaxWays> cache(/*capacity_sets*/kSets,
                                                                     /*initial_ways*/2);

    // Hammer the cache with many distinct keys so miss-ratio stays high (>15 %)
    for (uint64_t i = 0; i < 4096; ++i) {
        cache.getOrInsert(i, [i] { return i; });
    }

    // Cache should have grown all the way up to the cap and stopped there.
    REQUIRE(cache.ways() == kMaxWays);

    // Verify the observed miss ratio actually exceeded the heuristic threshold.
    auto st = cache.stats();
    const double miss_ratio = static_cast<double>(st.miss) / (st.hit + st.miss);
    REQUIRE(miss_ratio > kMissGrowThreshold);
} 