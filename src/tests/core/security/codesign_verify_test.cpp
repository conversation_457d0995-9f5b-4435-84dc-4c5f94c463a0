#include "core/security/codesign_verify.h"
#include <catch2/catch_test_macros.hpp>
#include <filesystem>
#include <fstream>

using launcher::core::security::verifyCodeSignature;
using launcher::core::security::CodesignError;
using KaiVoidExpected = launcher::core::security::KaiVoidExpected;

TEST_CASE("codesign verify – happy & corrupted path") {
#if !defined(__APPLE__)
    // On non-Apple hosts the function is stubbed to success – nothing more to verify.
    REQUIRE(verifyCodeSignature("/"));
    return;
#else
    // Pick a ubiquitous signed binary – /bin/ls is safe on macOS.
    const std::filesystem::path good_path = "/bin/ls";
    REQUIRE(std::filesystem::exists(good_path));

    KaiVoidExpected res_good = verifyCodeSignature(good_path);
    REQUIRE(res_good);

    // Create a temporary copy and corrupt its contents to break the signature.
    const auto temp_dir = std::filesystem::temp_directory_path();
    const std::filesystem::path bad_path = temp_dir / "ls_corrupt_copy";
    std::filesystem::copy_file(good_path, bad_path, std::filesystem::copy_options::overwrite_existing);

    // Corrupt by appending a byte.
    std::ofstream ofs(bad_path, std::ios::app | std::ios::binary);
    ofs << '\n';
    ofs.close();

    KaiVoidExpected res_bad = verifyCodeSignature(bad_path);
    REQUIRE_FALSE(res_bad);
    REQUIRE(res_bad.error() == CodesignError::InvalidSignature);

    // Clean up.
    std::filesystem::remove(bad_path);
#endif
} 