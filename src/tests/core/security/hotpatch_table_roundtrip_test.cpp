#include <catch2/catch_test_macros.hpp>
#include <vector>
#include <filesystem>
#include <cstring>

#if !KAI_ENABLE_VPATCH
TEST_CASE("Hotpatch table feature flag off", "[vpatch]") {
    SUCCEED("Feature disabled – nothing to test");
}
#else

#include "core/security/hotpatch_verifier_table.hh"
#include "core/security/verification_store.hh"
#include "core/storage/flat_snapshot.hh"

using namespace launcher::core;
using namespace launcher::core::security;

static CDHash makeHash(uint8_t seed) {
    CDHash h{};
    for (int i = 0; i < 20; ++i) h.bytes[i] = seed + i;
    return h;
}

TEST_CASE("Hotpatch round-trip", "[vpatch]") {
    const CDHash allowed = makeHash(1);
    const CDHash denied  = makeHash(2);

    // Build payload: entry_cnt (uint32) + 2 entries sorted by CDHash
    std::vector<std::byte> payload;
    uint32_t count = 2;
    struct EntryP { CDHash cdhash; uint8_t verdict; uint8_t reserved[3]{}; };
    payload.resize(sizeof(uint32_t) + count * sizeof(EntryP));
    std::memcpy(payload.data(), &count, sizeof(uint32_t));
    auto* entries = reinterpret_cast<EntryP*>(payload.data() + sizeof(uint32_t));

    entries[0].cdhash = allowed;
    entries[0].verdict = static_cast<uint8_t>(Verdict::Code::kAllowed);
    entries[1].cdhash = denied;
    entries[1].verdict = static_cast<uint8_t>(Verdict::Code::kDenied);

    // Create snapshot file in tmp
    std::filesystem::path path = std::filesystem::temp_directory_path() / "unit_vpatch.kfsn";
    REQUIRE(storage::FlatSnapshot::create(payload, path));

    auto tbl_res = HotpatchVerifierTable::load(path);
    REQUIRE(tbl_res);
    HotpatchVerifierTable tbl = std::move(tbl_res.value());

    VerificationStore store;
    store.attachHotpatchTable(tbl);

    auto res1 = store.find(allowed);
    REQUIRE(res1);
    CHECK(res1.value().code == Verdict::Code::kAllowed);

    auto res2 = store.find(denied);
    REQUIRE(res2);
    CHECK(res2.value().code == Verdict::Code::kDenied);

    auto unknown = makeHash(42);
    auto res3 = store.find(unknown);
    CHECK_FALSE(res3);

    std::filesystem::remove(path);
}
#endif 