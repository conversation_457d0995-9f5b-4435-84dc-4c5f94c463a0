#include "core/security/mask128.hh"
#include <catch2/catch_test_macros.hpp>

using launcher::core::security::Mask128;

// Local dummy enum to test enum adapters without depending on generated headers.
enum class DummyCap : uint8_t {
    Read  = 5,
    Write = 31,
    Exec  = 70,
    Admin = 127,
};

TEST_CASE("Mask128 default-constructed is empty") {
    constexpr Mask128 m{};
    STATIC_REQUIRE(m.popcount() == 0);
    STATIC_REQUIRE_FALSE(m.has(0));
    STATIC_REQUIRE_FALSE(m.has(64));
}

TEST_CASE("Mask128 set and has for low bits") {
    Mask128 m{};
    m.set(5);
    REQUIRE(m.has(5));
    REQUIRE(m.popcount() == 1);
}

TEST_CASE("Mask128 high-bit manipulation") {
    Mask128 m{};
    m.set(70);
    REQUIRE(m.has(70));
    REQUIRE(m.popcount() == 1);

    m.clear(70);
    REQUIRE_FALSE(m.has(70));
    REQUIRE(m.popcount() == 0);
}

TEST_CASE("Mask128 enum adapter works") {
    Mask128 m{};
    m.set(DummyCap::Exec);
    m.set(DummyCap::Write);

    REQUIRE(m.has(DummyCap::Exec));
    REQUIRE(m.has(static_cast<uint8_t>(DummyCap::Write)));
    REQUIRE(m.popcount() == 2);
}

TEST_CASE("Mask128 to_string produces 32-char hex with leading zeros") {
    Mask128 m{};
    m.set(0);   // LSB
    m.set(127); // MSB
    const auto hex = m.to_string();
    REQUIRE(hex.size() == 32);
    // Convert to std::string for easier indexing/printing (allocates outside hot path)
    const std::string s(hex.data(), hex.size());

    // hi bit set ⇒ first char should be '8' (since bit 127 is highest)
    REQUIRE(s[0] == '8');
    // LSB set ⇒ last char should be '1'
    REQUIRE(s[31] == '1');
} 