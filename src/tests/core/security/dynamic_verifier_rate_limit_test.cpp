#include "core/security/dynamic_verifier_registry.hh"
#include <catch2/catch_test_macros.hpp>
#include <thread>
#include <chrono>

using namespace launcher::core;
using namespace launcher::core::security;

// Dummy fast verifier – always returns Unknown so chain continues.
static DynVerifierAdapter makeFastAdapter() {
    return DynVerifierAdapter{[](const CDHash&) {
        return Verdict{ Verdict::Code::kUnknown };
    }};
}

// Slow verifier – sleeps for >250 µs causing timeout
static DynVerifierAdapter makeSlowAdapter() {
    return DynVerifierAdapter{[](const CDHash&) {
        std::this_thread::sleep_for(std::chrono::microseconds(400));
        return Verdict{ Verdict::Code::kAllowed };
    }};
}

TEST_CASE("DynamicVerifierRegistry capacity saturation", "[dynamic_verifier_registry]") {
    DynamicVerifierRegistry reg(std::chrono::microseconds{250}, std::chrono::seconds{0});
    for (int i = 0; i < 8; ++i) {
        REQUIRE(reg.registerVerifier(makeFastAdapter()));
    }
    // 9th should fail
    auto res = reg.registerVerifier(makeFastAdapter());
    REQUIRE_FALSE(res);
    REQUIRE(res.error() == foundation::KaiError::TooManyVerifiers);
}

TEST_CASE("DynamicVerifierRegistry registration rate limit", "[dynamic_verifier_registry]") {
    DynamicVerifierRegistry reg;  // default 1s window
    REQUIRE(reg.registerVerifier(makeFastAdapter()));

    // Immediate second registration should fail due to 1s window
    auto res = reg.registerVerifier(makeFastAdapter());
    REQUIRE_FALSE(res);
    REQUIRE(res.error() == foundation::KaiError::TooManyVerifiers);

    // Wait slightly over 1 second then should succeed
    std::this_thread::sleep_for(std::chrono::milliseconds(1100));
    REQUIRE(reg.registerVerifier(makeFastAdapter()));
}

TEST_CASE("DynamicVerifierRegistry verify timeout", "[dynamic_verifier_registry]") {
    DynamicVerifierRegistry reg;
    // First register slow adapter so verify() hits timeout.
    REQUIRE(reg.registerVerifier(makeSlowAdapter()));

    CDHash h{}; // zero hash fine
    auto res = reg.verify(h);
    REQUIRE_FALSE(res);
    REQUIRE(res.error() == foundation::KaiError::VerifierTimeout);

    // Clear registry and register fast adapter; verify should succeed.
    // (Cannot clear, so create a fresh instance.)
    DynamicVerifierRegistry reg2;
    REQUIRE(reg2.registerVerifier(makeFastAdapter()));
    auto res2 = reg2.verify(h);
    REQUIRE(res2);
    REQUIRE(res2.value().code == Verdict::Code::kUnknown);
}

TEST_CASE("DynamicVerifierRegistry early verdict", "[dynamic_verifier_registry]") {
    DynamicVerifierRegistry reg(std::chrono::microseconds{250}, std::chrono::seconds{0});
    // First adapter returns Unknown, second Denied – verify should stop early.
    REQUIRE(reg.registerVerifier(makeFastAdapter()));
    REQUIRE(reg.registerVerifier(DynVerifierAdapter{[](const CDHash&) {
        return Verdict{ Verdict::Code::kDenied };
    }}));

    CDHash h{};
    auto res = reg.verify(h);
    REQUIRE(res);
    REQUIRE(res.value().code == Verdict::Code::kDenied);
} 