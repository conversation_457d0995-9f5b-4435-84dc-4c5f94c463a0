#include "core/security/verification_store.hh"
#include <catch2/catch_test_macros.hpp>
#include <thread>
#include <vector>

using namespace launcher::core::security;

TEST_CASE("VerificationStore concurrent stress", "[verification_store][threads]") {
    constexpr int kThreads   = 8;
    constexpr int kOpsPerThr = 10000;

    VerificationStore store{1024}; // 1024 sets – small for test

    auto worker = [&](int id) {
        CDHash h{};
        for (int i = 0; i < kOpsPerThr; ++i) {
            // Deterministic but distinct hashes per thread
            for (int j = 0; j < 20; ++j) h.bytes[j] = static_cast<uint8_t>((id * 13 + j + i) & 0xFF);

            auto res = store.find(h);
            if (!res) {
                Verdict v{static_cast<Verdict::Code>((i + id) % 3)}; // rotate Allowed/Denied/Unknown
                (void)store.insert(h, v);
            }
        }
    };

    std::vector<std::thread> threads;
    for (int t = 0; t < kThreads; ++t) threads.emplace_back(worker, t);
    for (auto &th : threads) th.join();

    auto st = store.stats();
    REQUIRE(st.hit + st.miss >= static_cast<uint64_t>(kThreads * kOpsPerThr));
} 