#include <catch2/catch_test_macros.hpp>

// Stub-out expensive runtime verifiers so the test focuses solely on PolicyMask
// logic and deterministic execution order.
#define KAI_DISABLE_VERIFIER_RUNTIME
#include <core/security/verifier_strategy.hh>

using namespace launcher::core::security;

TEST_CASE("VerifierStrategy respects PolicyMask skipping and order", "[verifier][policy]") {
    CDHash dummy{};

    // 1. Full policy – expect Allowed because first enabled verifier returns Allowed.
    {
        auto v = VerifierStrategy<kPolicyAll>::verify(dummy);
        REQUIRE(v.code == Verdict::Code::kAllowed);
    }

    // 2. CodeSign-only policy – still Allowed (CodeSignVerifier returns Allowed in stub mode).
    {
        auto v = VerifierStrategy<kPolicyCodeSignOnly>::verify(dummy);
        REQUIRE(v.code == Verdict::Code::kAllowed);
    }

    // 3. Seatbelt-only policy – Allowed because Seatbelt stub returns Allowed.
    constexpr PolicyMask kSeatOnly = makePolicy({VerifierBit::kSeatbelt});
    {
        auto v = VerifierStrategy<kSeatOnly>::verify(dummy);
        REQUIRE(v.code == Verdict::Code::kAllowed);
    }

    // 4. No verifiers enabled – result must remain Unknown.
    {
        auto v = VerifierStrategy<kPolicyNone>::verify(dummy);
        REQUIRE(v.code == Verdict::Code::kUnknown);
    }

    // 5. Compile-time order invariant – static_assert duplication (will trigger at build).
    STATIC_REQUIRE(CodeSignVerifier::kOrder < SeatbeltVerifier::kOrder);
    STATIC_REQUIRE(SeatbeltVerifier::kOrder < DynamicVerifierWrapper::kOrder);
} 