#include <catch2/catch_test_macros.hpp>
#include <cstdlib>

#include <core/security/seatbelt_verifier.hh>

using namespace launcher::core::security;

TEST_CASE("SeatbeltVerifier rejects unknown profile", "[seatbelt]") {
    // Ensure runtime toggle is OFF
    unsetenv("KAI_DISABLE_SEATBELT_VERIFIER");

    auto res = verifyProfileName("restricted");
    REQUIRE_FALSE(res);
    REQUIRE(res.error() == launcher::core::foundation::KaiError::SeatbeltInvalid);
} 