#include "core/security/verification_store.hh"
#include <catch2/catch_test_macros.hpp>

using namespace launcher::core::security;

TEST_CASE("VerificationStore miss then insert and hit", "[verification_store]") {
    VerificationStore store;
    CDHash h{};
    // simple deterministic bytes pattern
    for (int i = 0; i < 20; ++i) h.bytes[i] = static_cast<uint8_t>(i);

    // Initial miss
    auto miss = store.find(h);
    REQUIRE_FALSE(miss);
    REQUIRE(miss.error() == StoreError::kNotFound);

    // Insert Allowed verdict
    REQUIRE(store.insert(h, Verdict{ Verdict::Code::kAllowed }).has_value());

    // Hit path
    auto hit = store.find(h);
    REQUIRE(hit);
    REQUIRE(hit.value().code == Verdict::Code::kAllowed);

    // Stats reflect 1 hit + 1 miss
    auto st = store.stats();
    REQUIRE(st.hit  >= 1);
    REQUIRE(st.miss >= 1);
}

TEST_CASE("VerificationStore Deny cached", "[verification_store]") {
    VerificationStore store;
    CDHash h{};
    for (int i = 0; i < 20; ++i) h.bytes[i] = static_cast<uint8_t>(255 - i);

    // Insert Denied verdict
    REQUIRE(store.insert(h, Verdict{ Verdict::Code::kDenied }).has_value());

    auto res = store.find(h);
    REQUIRE(res);
    REQUIRE(res.value().code == Verdict::Code::kDenied);
} 