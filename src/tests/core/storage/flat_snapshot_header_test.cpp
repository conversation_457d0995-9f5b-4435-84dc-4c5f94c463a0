#include "core/storage/flat_snapshot.hh"
#include <catch2/catch_test_macros.hpp>
#include <filesystem>
#include <fstream>
#include <cstring>
#include <cstdint>

using launcher::core::storage::FlatSnapshot;
using launcher::core::storage::FlatSnapshotError;
using FlatExpectedVoid = launcher::core::storage::FlatExpectedVoid;
using FlatExpectedSnap = launcher::core::storage::FlatExpected<FlatSnapshot>;

static std::vector<std::byte> makePayload() {
    const char* txt = "hello world snapshot";
    std::vector<std::byte> v(strlen(txt));
    std::memcpy(v.data(), txt, v.size());
    return v;
}

TEST_CASE("FlatSnapshot happy path and corrupt header detection") {
    auto tmp = std::filesystem::temp_directory_path() / "flat_snapshot_test.bin";
    std::filesystem::remove(tmp); // ensure clean

    // Create snapshot
    auto payload = makePayload();
    REQUIRE(FlatSnapshot::create(payload, tmp));

    // mmap + verify
    auto snapExp = FlatSnapshot::mmapReadOnly(tmp);
    REQUIRE(snapExp);
    auto verifyRes = snapExp.value().verify();
    REQUIRE(verifyRes);

    // Corrupt magic byte
    {
        std::fstream fs(tmp, std::ios::in | std::ios::out | std::ios::binary);
        REQUIRE(fs.good());
        char bad = 'X';
        fs.write(&bad, 1);
        fs.close();
    }

    auto snapBad = FlatSnapshot::mmapReadOnly(tmp);
    REQUIRE_FALSE(snapBad);
    REQUIRE(snapBad.error() == FlatSnapshotError::InvalidMagic);

    std::filesystem::remove(tmp);
}

TEST_CASE("FlatSnapshot detects truncated header via excessive sig_len") {
    auto tmp = std::filesystem::temp_directory_path() / "flat_snapshot_siglen_test.bin";
    std::filesystem::remove(tmp);

    auto payload = makePayload();
    REQUIRE(FlatSnapshot::create(payload, tmp));

    // Manipulate sig_len field to be larger than file size
    {
        std::fstream fs(tmp, std::ios::in | std::ios::out | std::ios::binary);
        REQUIRE(fs.good());
        fs.seekp(30); // offset of sig_len field in header
        uint32_t huge = 0xFFFFFFFFu;
        fs.write(reinterpret_cast<const char*>(&huge), sizeof(huge));
        fs.close();
    }

    auto snapHuge = FlatSnapshot::mmapReadOnly(tmp);
    REQUIRE_FALSE(snapHuge);
    REQUIRE(snapHuge.error() == FlatSnapshotError::TruncatedHeader);

    std::filesystem::remove(tmp);
} 