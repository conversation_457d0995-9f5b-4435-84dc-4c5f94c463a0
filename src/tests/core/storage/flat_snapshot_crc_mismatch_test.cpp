#include "core/storage/flat_snapshot.hh"
#include <catch2/catch_test_macros.hpp>
#include <filesystem>
#include <fstream>
#include <vector>
#include <cstring>
#include <cstddef>

using launcher::core::storage::FlatSnapshot;
using launcher::core::storage::FlatSnapshotError;

static std::vector<std::byte> makePayload() {
    const char* txt = "hello snapshot crc";
    std::vector<std::byte> v(strlen(txt));
    std::memcpy(v.data(), txt, v.size());
    return v;
}

TEST_CASE("FlatSnapshot detects CRC mismatch") {
    auto tmp = std::filesystem::temp_directory_path() / "flat_snapshot_crc_test.bin";
    std::filesystem::remove(tmp);

    auto payload = makePayload();
    REQUIRE(FlatSnapshot::create(payload, tmp));

    // Flip first payload byte to break CRC
    {
        std::fstream fs(tmp, std::ios::in | std::ios::out | std::ios::binary);
        REQUIRE(fs.good());
        constexpr std::streamoff kSigLenFieldOffset = 30; // bytes from file start (magic+version+build_sha+crc32)
        fs.seekg(kSigLenFieldOffset);
        uint32_t sig_len_le = 0;
        fs.read(reinterpret_cast<char*>(&sig_len_le), sizeof(sig_len_le));
        const uint32_t sig_len = sig_len_le; // header is little-endian

        const std::streamoff payload_offset = static_cast<std::streamoff>(FlatSnapshot::kHeaderSize + sig_len);
        fs.seekg(payload_offset);
        char ch;
        fs.read(&ch, 1);
        fs.seekp(payload_offset);
        ch ^= 0xFF;
        fs.write(&ch, 1);
        fs.close();
    }

    auto snapExp = FlatSnapshot::mmapReadOnly(tmp);
    REQUIRE(snapExp);
    auto verifyRes = snapExp.value().verify();
    REQUIRE_FALSE(verifyRes);
    REQUIRE(verifyRes.error() == FlatSnapshotError::CrcMismatch);

    std::filesystem::remove(tmp);
} 