#include "core/storage/flat_snapshot.hh"
#include <catch2/catch_test_macros.hpp>
#include <filesystem>
#include <vector>
#include <cstring>
#include <cstddef>

using launcher::core::storage::FlatSnapshot;
using launcher::core::storage::FlatSnapshotError;

static std::vector<std::byte> makePayload() {
    const char* txt = "signed snapshot payload";
    std::vector<std::byte> v(strlen(txt));
    std::memcpy(v.data(), txt, v.size());
    return v;
}

#ifdef __APPLE__
TEST_CASE("FlatSnapshot invalid CMS signature rejects on macOS") {
    auto tmp = std::filesystem::temp_directory_path() / "flat_snapshot_sig_invalid.bin";
    std::filesystem::remove(tmp);

    auto payload = makePayload();

    // fabricate random bytes that are NOT a valid CMS detached signature
    std::vector<std::byte> bogus_sig(32);
    for (size_t i = 0; i < bogus_sig.size(); ++i) {
        bogus_sig[i] = static_cast<std::byte>(0xA5 + i);
    }

    REQUIRE(FlatSnapshot::create(payload, tmp, bogus_sig));

    auto snapExp = FlatSnapshot::mmapReadOnly(tmp);
    REQUIRE(snapExp);
    auto verifyRes = snapExp.value().verify();
    REQUIRE_FALSE(verifyRes);
    REQUIRE((verifyRes.error() == FlatSnapshotError::SignatureInvalid ||
             verifyRes.error() == FlatSnapshotError::AppleApiError));

    std::filesystem::remove(tmp);
}
#else
TEST_CASE("FlatSnapshot signature test skipped on non-Apple") {
    SUCCEED("Non-Apple platform – signature verify stubbed");
}
#endif 