#if KA<PERSON>_ENABLE_FLATSNAPSHOT_SIGN
// -----------------------------------------------------------------------------
// @file flat_snapshot_ed25519_roundtrip_test.cpp
// -----------------------------------------------------------------------------
#include "core/storage/flat_snapshot.hh"
#include <catch2/catch_test_macros.hpp>
#include <fstream>
#include <filesystem>

using namespace launcher::core::storage;

TEST_CASE("FlatSnapshot Ed25519 round-trip", "[snapshot][ed25519]") {
    // Payload 128 bytes of deterministic data
    std::vector<std::byte> payload(128);
    for (size_t i = 0; i < payload.size(); ++i) payload[i] = static_cast<std::byte>(i & 0xFF);

    const auto tmp = std::filesystem::temp_directory_path() / "ed25519_roundtrip.kfsn";
    std::filesystem::remove(tmp);

    REQUIRE(FlatSnapshot::create(payload, tmp));

    auto map_res = FlatSnapshot::mmapReadOnly(tmp);
    REQUIRE(map_res);

    FlatSnapshot snap = std::move(map_res.value());
    REQUIRE(snap.verify());
    REQUIRE(snap.payload().size() == payload.size());
}
#endif // KAI_ENABLE_FLATSNAPSHOT_SIGN 