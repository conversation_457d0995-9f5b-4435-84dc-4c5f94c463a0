#include "core/http/sse_decoder.h"
#include "core/util/stream_channel.h"
#include "core/util/stream_generator.h"
#include "core/memory/slab_arena.h"
#include "utilities/cancellation_token.h"
#include <catch2/catch_test_macros.hpp>
#include <string>
#include <coroutine>

using namespace launcher::core;

TEST_CASE("SSE decoder produces stable arena without realloc", "[sse][arena]") {
    auto decoder = http::SSEDecoder::create("openai");
    REQUIRE(decoder);
    launcher::core::memory::SlabArena slab;
    decoder->setSink(&slab);

    std::string chunk = "data: {\"choices\":[{\"delta\":{\"content\":\"x\"}}]}\n\n";
    const int iterations = 102400 / static_cast<int>(chunk.size()) + 1; // >100 KiB total

    // Capture pointer of first append to detect reallocations / moves.
    auto first_view = slab.append("init", 4);
    const char* base_ptr = first_view.data();

    for (int i = 0; i < iterations; ++i) {
        auto tok = decoder->feed(chunk);
        (void)tok;
    }

    REQUIRE(slab.size() > first_view.size());
    // Ensure the original pointer stayed stable (no reallocation / move).
    REQUIRE(first_view.data() == base_ptr);
}

TEST_CASE("StreamChannel PopAwaiter reacts to cancellation", "[async_stream]") {
    using T = std::string;
    util::StreamChannel<T> ch;
    auto token = std::make_shared<utilities::CancellationToken>();

    auto awaiter = ch.coPop(token);
    // Not cancelled; queue empty; expect not ready.
    REQUIRE(awaiter.await_ready() == false);

    // Cancel token; await_ready should now return true.
    token->cancel();
    REQUIRE(awaiter.await_ready() == true);
}

TEST_CASE("StreamChannel multiple waiters resume on cancellation", "[async_stream][multi_waiter]") {
    using T = std::string;
    launcher::core::util::StreamChannel<T> ch;
    auto token = std::make_shared<launcher::core::utilities::CancellationToken>();

    // Create two independent awaiters.
    auto a1 = ch.coPop(token);
    auto a2 = ch.coPop(token);

    REQUIRE_FALSE(a1.await_ready());
    REQUIRE_FALSE(a2.await_ready());

    // Cancel the token – both awaiters should become ready immediately.
    token->cancel();

    REQUIRE(a1.await_ready());
    REQUIRE(a2.await_ready());

    // They should both resume with empty optional (channel wasn't closed but cancelled).
    auto r1 = a1.await_resume();
    auto r2 = a2.await_resume();

    REQUIRE_FALSE(r1.has_value());
    REQUIRE_FALSE(r2.has_value());
} 