#include "../../../core/util/memory_pool.h"

#include <catch2/catch_test_macros.hpp>
#include <memory>
#include <string>
#include <vector>

using namespace launcher::core;

TEST_CASE("MemoryPool basic functionality", "[memory]") {
    MemoryPool<int> pool;

    SECTION("Allocation and deallocation") {
        // Allocate memory for an int
        void* p1 = pool.allocate();
        REQUIRE(p1 != nullptr);

        // Allocate another int
        void* p2 = pool.allocate();
        REQUIRE(p2 != nullptr);
        REQUIRE(p2 != p1);

        // Deallocate the first int
        pool.deallocate(p1);

        // Allocate again, should reuse the first allocation
        void* p3 = pool.allocate();
        REQUIRE(p3 == p1);

        // Deallocate both
        pool.deallocate(p2);
        pool.deallocate(p3);
    }

    SECTION("Multiple allocations") {
        std::vector<void*> pointers;

        // Allocate 100 ints
        for (int i = 0; i < 100; ++i) {
            void* p = pool.allocate();
            REQUIRE(p != nullptr);
            pointers.push_back(p);
        }

        // Verify all pointers are different
        for (size_t i = 0; i < pointers.size(); ++i) {
            for (size_t j = i + 1; j < pointers.size(); ++j) {
                REQUIRE(pointers[i] != pointers[j]);
            }
        }

        // Deallocate all
        for (void* p : pointers) {
            pool.deallocate(p);
        }
    }

    SECTION("Chunk allocation") {
        // Default chunk size is 64, so allocating 65 should create a new chunk
        for (int i = 0; i < 65; ++i) {
            void* p = pool.allocate();
            REQUIRE(p != nullptr);
        }

        // Verify that we have 2 chunks
        REQUIRE(pool.getChunkCount() == 2);
    }
}

TEST_CASE("PoolAllocator with STL containers", "[memory]") {
    SECTION("std::vector with PoolAllocator") {
        std::vector<int, PoolAllocator<int>> vec;

        // Add some elements
        for (int i = 0; i < 100; ++i) {
            vec.push_back(i);
        }

        // Verify the elements
        REQUIRE(vec.size() == 100);
        for (int i = 0; i < 100; ++i) {
            REQUIRE(vec[i] == i);
        }
    }

    SECTION("std::basic_string with PoolAllocator") {
        std::basic_string<char, std::char_traits<char>, PoolAllocator<char>> str;

        // Add some characters
        str = "Hello, world!";

        // Verify the string
        REQUIRE(str == "Hello, world!");
    }
}