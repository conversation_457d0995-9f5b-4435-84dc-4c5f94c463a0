#include "../../../core/util/memory_tracker.h"
#include "../../../core/util/memory_tracker_registry.h"

#include <catch2/catch_test_macros.hpp>
#include <new>
#include <string>

using namespace launcher::core;

TEST_CASE("MemoryTracker basic functionality", "[memory]") {
    // Access the injected/fallback tracker
    MemoryTracker& tracker = TRACKER();

    // Reset the tracker to start with a clean state
    tracker.reset();

    SECTION("Allocation and deallocation") {
        // Record initial state
        size_t initialUsage = tracker.getCurrentUsage();
        size_t initialCount = tracker.getAllocationCount();

        // Allocate memory
        void* p = tracker.recordAllocation(100);
        REQUIRE(p != nullptr);

        // Verify that the usage increased
        REQUIRE(tracker.getCurrentUsage() == initialUsage + 100);
        REQUIRE(tracker.getAllocationCount() == initialCount + 1);

        // Deallocate memory
        tracker.recordDeallocation(p);

        // Verify that the usage decreased
        REQUIRE(tracker.getCurrentUsage() == initialUsage);
        REQUIRE(tracker.getAllocationCount() == initialCount);
    }

    SECTION("Multiple allocations with tags") {
        // Reset the tracker
        tracker.reset();

        // Allocate memory with different tags
        void* p1 = tracker.recordAllocation(100, "tag1");
        void* p2 = tracker.recordAllocation(200, "tag2");
        void* p3 = tracker.recordAllocation(300, "tag1");

        // Verify the total usage
        REQUIRE(tracker.getCurrentUsage() == 600);
        REQUIRE(tracker.getAllocationCount() == 3);

        // Verify the tag usage
        REQUIRE(tracker.getTagUsage("tag1") == 400);
        REQUIRE(tracker.getTagUsage("tag2") == 200);

        // Deallocate memory
        tracker.recordDeallocation(p1);
        tracker.recordDeallocation(p2);
        tracker.recordDeallocation(p3);

        // Verify that all memory was deallocated
        REQUIRE(tracker.getCurrentUsage() == 0);
        REQUIRE(tracker.getAllocationCount() == 0);
    }

    SECTION("Peak usage") {
        // Reset the tracker
        tracker.reset();

        // Allocate memory
        void* p1 = tracker.recordAllocation(100);
        void* p2 = tracker.recordAllocation(200);

        // Verify the peak usage
        REQUIRE(tracker.getPeakUsage() == 300);

        // Deallocate memory
        tracker.recordDeallocation(p1);

        // Verify that the peak usage remains the same
        REQUIRE(tracker.getPeakUsage() == 300);

        // Allocate more memory to increase the peak
        void* p3 = tracker.recordAllocation(400);

        // Verify the new peak usage
        REQUIRE(tracker.getPeakUsage() == 600);

        // Deallocate memory
        tracker.recordDeallocation(p2);
        tracker.recordDeallocation(p3);
    }

    SECTION("Active allocations") {
        // Reset the tracker
        tracker.reset();

        // Allocate memory
        void* p1 = tracker.recordAllocation(100);
        void* p2 = tracker.recordAllocation(200);

        // Get the active allocations
        auto allocations = tracker.getActiveAllocations();

        // Verify the number of allocations
        REQUIRE(allocations.size() == 2);

        // Verify the allocations
        bool foundP1 = false;
        bool foundP2 = false;

        for (const auto& alloc : allocations) {
            if (alloc.first == p1) {
                REQUIRE(alloc.second == 100);
                foundP1 = true;
            } else if (alloc.first == p2) {
                REQUIRE(alloc.second == 200);
                foundP2 = true;
            }
        }

        REQUIRE(foundP1);
        REQUIRE(foundP2);

        // Deallocate memory
        tracker.recordDeallocation(p1);
        tracker.recordDeallocation(p2);
    }

    SECTION("Custom new and delete operators") {
        // Reset the tracker
        tracker.reset();

        // Record the initial state
        size_t initialUsage = tracker.getCurrentUsage();
        size_t initialCount = tracker.getAllocationCount();

        // Allocate memory using the custom new operator
        int* p = new ("custom") int(42);

        // Verify that the allocation was recorded
        REQUIRE(tracker.getCurrentUsage() > initialUsage);
        REQUIRE(tracker.getAllocationCount() > initialCount);
        REQUIRE(tracker.getTagUsage("custom") > 0);

        // Record the current state before deallocation
        size_t currentUsage = tracker.getCurrentUsage();
        size_t currentCount = tracker.getAllocationCount();

        // Manually record the deallocation
        tracker.recordDeallocation(p);

        // NOTE: No explicit `delete p` call here –\n+        // recordDeallocation already released the memory.\n+        // Verify that the deallocation was recorded\n+        REQUIRE(tracker.getCurrentUsage() < currentUsage);\n+        REQUIRE(tracker.getAllocationCount() < currentCount);
    }
}