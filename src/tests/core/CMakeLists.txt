# Add compile definitions for testing
add_compile_definitions(TESTING)

# Add test executables
add_executable(app_index_test app_index_test.cpp)
add_executable(app_scanner_test scanner/app_scanner_test.cpp)
add_executable(file_watcher_test util/file_watcher_test.cpp)
add_executable(incremental_update_test index/incremental_update_test.cpp)
add_executable(search_history_test search_history_test.cpp)
add_executable(search_cache_test search_cache_test.cpp)
add_executable(memory_pool_test util/memory_pool_test.cpp)
add_executable(memory_tracker_test util/memory_tracker_test.cpp)
add_executable(service_registry_test foundation/service_registry_test.cpp)
add_executable(service_registry_order_test foundation/service_registry_order_test.cpp)
add_executable(arena_allocator_test memory/arena_allocator_test.cpp)
add_executable(slab_arena_test memory/slab_arena_test.cpp)
add_executable(event_bus_test events/event_bus_test.cpp)
add_executable(capability_gen_test foundation/capability_gen_test.cpp)
add_executable(service_registry_cycle_test foundation/service_registry_cycle_test.cpp)
add_executable(event_bus_unsubscribe_test events/event_bus_unsubscribe_test.cpp)
add_executable(runtime_manager_test runtime_manager_test.cpp)
add_executable(runtime_manager_sandbox_test runtime_manager_sandbox_test.cpp)
add_executable(event_bus_idle_test events/event_bus_idle_test.cpp)
add_executable(codesign_verify_test security/codesign_verify_test.cpp)
add_executable(service_base_compile_test foundation/service_base_compile_test.cpp)
add_executable(mcp_client_service_test foundation/mcp_client_service_test.cpp)
add_executable(zero_alloc_guard_test memory/zero_alloc_guard_test.cpp)
add_executable(allocator_zero_alloc_guard_test memory/allocator_zero_alloc_guard_test.cpp)
add_executable(service_name_lookup_test foundation/service_name_lookup_test.cpp)
add_executable(event_bus_backpressure_test events/event_bus_backpressure_test.cpp)
add_executable(executor_backpressure_test events/executor_backpressure_test.cpp)
add_executable(service_start_wall_test foundation/service_start_wall_test.cpp)
add_executable(mask128_helper_test security/mask128_helper_test.cpp)
add_executable(adaptive_cache_stats_test container/adaptive_cache_stats_test.cpp)
add_executable(verification_store_test security/verification_store_test.cpp)
add_executable(flat_snapshot_header_test storage/flat_snapshot_header_test.cpp)
add_executable(adaptive_cache_grow_test container/adaptive_cache_grow_test.cpp)
add_executable(probe_cache_integration_test snapshot/probe_cache_integration_test.cpp)
add_executable(verdict_store_hit_miss_test snapshot/verdict_store_hit_miss_test.cpp)
add_executable(ring_queue_basic_test events/ring_queue_basic_test.cpp)
add_executable(verification_store_thread_test security/verification_store_thread_test.cpp)
add_executable(metric_source_enumeration_test metric_source_enumeration_test.cpp)
add_executable(flat_snapshot_crc_mismatch_test storage/flat_snapshot_crc_mismatch_test.cpp)
add_executable(flat_snapshot_signature_invalid_test storage/flat_snapshot_signature_invalid_test.cpp)
add_executable(probe_cache_service_start_test probe_cache_service_start_test.cpp)
add_executable(runtime_scanner_basic_test runtime/runtime_scanner_basic_test.cpp)
add_executable(metrics_exporter_iteration_test metrics_exporter_iteration_test.cpp)
add_executable(dynamic_verifier_rate_limit_test security/dynamic_verifier_rate_limit_test.cpp)
add_executable(seatbelt_verifier_profile_test security/seatbelt_verifier_profile_test.cpp)
add_executable(seatbelt_verifier_accept_test security/seatbelt_verifier_accept_test.cpp)
add_executable(hotpatch_table_roundtrip_test security/hotpatch_table_roundtrip_test.cpp)
add_executable(verification_pipeline_order_test security/verification_pipeline_order_test.cpp)
add_executable(mux_queue_starvation_test runtime/mux_queue_starvation_test.cpp)
add_executable(queue_high_water_alert_test diagnostics/queue_high_water_alert_test.cpp)
add_executable(queue_traits_spin_block_test runtime/queue_traits_spin_block_test.cpp)
add_executable(hdr_latency_metric_test ../hdr_latency_metric_test.cpp)
add_executable(runtime_cold_scan_integration_test runtime/runtime_cold_scan_integration_test.cpp)
add_executable(verifier_latency_test security/verifier_latency_test.cpp)
add_executable(async_stream_test util/async_stream_test.cpp)
add_executable(sse_decoder_simd_test http/sse_decoder_simd_test.cpp)
add_executable(sse_decoder_responses_test http/sse_decoder_responses_test.cpp)
add_executable(responses_decoder_test http/responses_decoder_test.cpp)
add_executable(sse_decoder_error_event_test http/sse_decoder_error_event_test.cpp)
add_executable(sse_decoder_large_payload_test http/sse_decoder_large_payload_test.cpp)
add_executable(json_writer_roundtrip_test json/json_writer_roundtrip_test.cpp)
add_executable(llm_factory_service_test llm/llm_factory_service_test.cpp)
add_executable(plugin_manifest_scan_test plugin_manifest_scan_test.cpp)
add_executable(plugin_capability_validation_test plugin_capability_validation_test.cpp)

# Link with core library and Catch2
target_link_libraries(app_index_test PRIVATE core Catch2::Catch2WithMain)
target_link_libraries(app_scanner_test PRIVATE core Catch2::Catch2WithMain)
target_link_libraries(file_watcher_test PRIVATE core Catch2::Catch2WithMain)
target_link_libraries(incremental_update_test PRIVATE core Catch2::Catch2WithMain)
target_link_libraries(search_history_test PRIVATE core Catch2::Catch2WithMain)
target_link_libraries(search_cache_test PRIVATE core Catch2::Catch2WithMain)
target_link_libraries(memory_pool_test PRIVATE core Catch2::Catch2WithMain)
target_link_libraries(memory_tracker_test PRIVATE core Catch2::Catch2WithMain)
target_link_libraries(service_registry_test PRIVATE core Catch2::Catch2WithMain)
target_link_libraries(service_registry_order_test PRIVATE core Catch2::Catch2WithMain)
target_link_libraries(arena_allocator_test PRIVATE core Catch2::Catch2WithMain)
target_link_libraries(slab_arena_test PRIVATE core Catch2::Catch2WithMain)
target_link_libraries(event_bus_test PRIVATE core Catch2::Catch2WithMain)
target_link_libraries(capability_gen_test PRIVATE core Catch2::Catch2WithMain)
target_link_libraries(service_registry_cycle_test PRIVATE core Catch2::Catch2WithMain)
target_link_libraries(event_bus_unsubscribe_test PRIVATE core Catch2::Catch2WithMain)
target_link_libraries(runtime_manager_test PRIVATE core Catch2::Catch2WithMain snapshot_lib)
target_compile_options(runtime_manager_test PRIVATE -O3 -DNDEBUG)
target_link_libraries(runtime_manager_sandbox_test PRIVATE core Catch2::Catch2WithMain snapshot_lib)
target_compile_options(runtime_manager_sandbox_test PRIVATE -O3 -DNDEBUG)
target_link_libraries(event_bus_idle_test PRIVATE core Catch2::Catch2WithMain)
target_link_libraries(codesign_verify_test PRIVATE core Catch2::Catch2WithMain)
target_link_libraries(service_base_compile_test PRIVATE core)
target_link_libraries(mcp_client_service_test PRIVATE core Catch2::Catch2WithMain)
target_link_libraries(zero_alloc_guard_test PRIVATE core Catch2::Catch2WithMain)
target_link_libraries(allocator_zero_alloc_guard_test PRIVATE core Catch2::Catch2WithMain snapshot_lib)
target_link_libraries(service_name_lookup_test PRIVATE core Catch2::Catch2WithMain)
target_link_libraries(event_bus_backpressure_test PRIVATE core Catch2::Catch2WithMain)
target_link_libraries(executor_backpressure_test PRIVATE core Catch2::Catch2WithMain)
target_link_libraries(service_start_wall_test PRIVATE core Catch2::Catch2WithMain)
target_link_libraries(mask128_helper_test PRIVATE core Catch2::Catch2WithMain)
target_link_libraries(adaptive_cache_stats_test PRIVATE core Catch2::Catch2WithMain)
target_link_libraries(verification_store_test PRIVATE core Catch2::Catch2WithMain snapshot_lib)
target_link_libraries(flat_snapshot_header_test PRIVATE core Catch2::Catch2WithMain)
target_link_libraries(adaptive_cache_grow_test PRIVATE core Catch2::Catch2WithMain)
target_link_libraries(probe_cache_integration_test PRIVATE core snapshot_lib Catch2::Catch2WithMain)
target_link_libraries(verdict_store_hit_miss_test PRIVATE core snapshot_lib Catch2::Catch2WithMain)
target_link_libraries(ring_queue_basic_test PRIVATE core Catch2::Catch2WithMain)
target_link_libraries(verification_store_thread_test PRIVATE core Catch2::Catch2WithMain snapshot_lib)
target_link_libraries(metric_source_enumeration_test PRIVATE core Catch2::Catch2WithMain)
target_link_libraries(flat_snapshot_crc_mismatch_test PRIVATE core Catch2::Catch2WithMain)
target_link_libraries(flat_snapshot_signature_invalid_test PRIVATE core Catch2::Catch2WithMain)
target_link_libraries(probe_cache_service_start_test PRIVATE core snapshot_lib Catch2::Catch2WithMain)
target_link_libraries(runtime_scanner_basic_test PRIVATE core Catch2::Catch2WithMain)
target_link_libraries(metrics_exporter_iteration_test PRIVATE core Catch2::Catch2WithMain)
target_link_libraries(dynamic_verifier_rate_limit_test PRIVATE core Catch2::Catch2WithMain)
target_link_libraries(seatbelt_verifier_profile_test PRIVATE core Catch2::Catch2WithMain)
target_link_libraries(seatbelt_verifier_accept_test PRIVATE core Catch2::Catch2WithMain)
target_link_libraries(hotpatch_table_roundtrip_test PRIVATE core Catch2::Catch2WithMain)
target_link_libraries(verification_pipeline_order_test PRIVATE core Catch2::Catch2WithMain)
target_link_libraries(mux_queue_starvation_test PRIVATE core Catch2::Catch2WithMain)
target_link_libraries(queue_high_water_alert_test PRIVATE core Catch2::Catch2WithMain)
target_link_libraries(queue_traits_spin_block_test PRIVATE core Catch2::Catch2WithMain)
target_link_libraries(hdr_latency_metric_test PRIVATE core Catch2::Catch2WithMain)
target_link_libraries(runtime_cold_scan_integration_test PRIVATE core Catch2::Catch2WithMain snapshot_lib)
target_link_libraries(verifier_latency_test PRIVATE core Catch2::Catch2WithMain snapshot_lib)
target_link_libraries(async_stream_test PRIVATE core Catch2::Catch2WithMain)
target_link_libraries(sse_decoder_simd_test PRIVATE http_client Catch2::Catch2WithMain)
target_link_libraries(sse_decoder_responses_test PRIVATE http_client Catch2::Catch2WithMain)
target_link_libraries(responses_decoder_test PRIVATE http_client Catch2::Catch2WithMain)
target_link_libraries(sse_decoder_error_event_test PRIVATE http_client Catch2::Catch2WithMain)
target_link_libraries(sse_decoder_large_payload_test PRIVATE http_client Catch2::Catch2WithMain)
target_link_libraries(json_writer_roundtrip_test PRIVATE json_util Catch2::Catch2WithMain)
target_link_libraries(llm_factory_service_test PRIVATE core_test_common)
target_link_libraries(plugin_manifest_scan_test PRIVATE core_test_common)
target_link_libraries(plugin_capability_validation_test PRIVATE core_test_common)

# Provide the project source root to the test via macro for locating files
target_compile_definitions(capability_gen_test PRIVATE PROJECT_SOURCE_DIR="${CMAKE_SOURCE_DIR}")
target_compile_definitions(runtime_manager_test PRIVATE SAMPLE_NULL_PLUGIN_PATH="$<TARGET_FILE:sample_null_plugin>" LAUNCHER_ASYNC_LOG=0 RPMALLOC_FIRST_CLASS_HEAPS=0)
target_compile_definitions(runtime_manager_sandbox_test PRIVATE SAMPLE_NULL_PLUGIN_PATH="$<TARGET_FILE:sample_null_plugin>" LAUNCHER_ASYNC_LOG=0 RPMALLOC_FIRST_CLASS_HEAPS=0)
target_compile_definitions(executor_backpressure_test PRIVATE KAI_EXECUTOR_CAPACITY=4)
target_compile_definitions(verifier_latency_test PRIVATE KAI_DISABLE_VERIFIER_RUNTIME=1 KAI_DISABLE_CODESIGN_VERIFY=1 KAI_DISABLE_SEATBELT_VERIFIER=1)

# Include directories
target_include_directories(app_index_test PRIVATE ${CMAKE_SOURCE_DIR}/src)
target_include_directories(app_scanner_test PRIVATE ${CMAKE_SOURCE_DIR}/src)
target_include_directories(file_watcher_test PRIVATE ${CMAKE_SOURCE_DIR}/src)
target_include_directories(incremental_update_test PRIVATE ${CMAKE_SOURCE_DIR}/src)
target_include_directories(search_history_test PRIVATE ${CMAKE_SOURCE_DIR}/src)
target_include_directories(search_cache_test PRIVATE ${CMAKE_SOURCE_DIR}/src)
target_include_directories(memory_pool_test PRIVATE ${CMAKE_SOURCE_DIR}/src)
target_include_directories(memory_tracker_test PRIVATE ${CMAKE_SOURCE_DIR}/src)
target_include_directories(service_registry_test PRIVATE ${CMAKE_SOURCE_DIR}/src)
target_include_directories(service_registry_order_test PRIVATE ${CMAKE_SOURCE_DIR}/src)
target_include_directories(arena_allocator_test PRIVATE ${CMAKE_SOURCE_DIR}/src)
target_include_directories(slab_arena_test PRIVATE ${CMAKE_SOURCE_DIR}/src)
target_include_directories(event_bus_test PRIVATE ${CMAKE_SOURCE_DIR}/src)
target_include_directories(capability_gen_test PRIVATE ${CMAKE_SOURCE_DIR}/src)
target_include_directories(service_registry_cycle_test PRIVATE ${CMAKE_SOURCE_DIR}/src)
target_include_directories(event_bus_unsubscribe_test PRIVATE ${CMAKE_SOURCE_DIR}/src)
target_include_directories(runtime_manager_test PRIVATE ${CMAKE_SOURCE_DIR}/src)
target_include_directories(runtime_manager_sandbox_test PRIVATE ${CMAKE_SOURCE_DIR}/src)
target_include_directories(event_bus_idle_test PRIVATE ${CMAKE_SOURCE_DIR}/src)
target_include_directories(codesign_verify_test PRIVATE ${CMAKE_SOURCE_DIR}/src)
target_include_directories(service_base_compile_test PRIVATE ${CMAKE_SOURCE_DIR}/src)
target_include_directories(mcp_client_service_test PRIVATE ${CMAKE_SOURCE_DIR}/src)
target_include_directories(zero_alloc_guard_test PRIVATE ${CMAKE_SOURCE_DIR}/src)
target_include_directories(allocator_zero_alloc_guard_test PRIVATE ${CMAKE_SOURCE_DIR}/src)
target_include_directories(service_name_lookup_test PRIVATE ${CMAKE_SOURCE_DIR}/src)
target_include_directories(event_bus_backpressure_test PRIVATE ${CMAKE_SOURCE_DIR}/src)
target_include_directories(executor_backpressure_test PRIVATE ${CMAKE_SOURCE_DIR}/src)
target_include_directories(service_start_wall_test PRIVATE ${CMAKE_SOURCE_DIR}/src)
target_include_directories(mask128_helper_test PRIVATE ${CMAKE_SOURCE_DIR}/src)
target_include_directories(adaptive_cache_stats_test PRIVATE ${CMAKE_SOURCE_DIR}/src)
target_include_directories(verification_store_test PRIVATE ${CMAKE_SOURCE_DIR}/src)
target_include_directories(flat_snapshot_header_test PRIVATE ${CMAKE_SOURCE_DIR}/src)
target_include_directories(adaptive_cache_grow_test PRIVATE ${CMAKE_SOURCE_DIR}/src)
target_include_directories(probe_cache_integration_test PRIVATE ${CMAKE_SOURCE_DIR}/src)
target_include_directories(verdict_store_hit_miss_test PRIVATE ${CMAKE_SOURCE_DIR}/src)
target_include_directories(ring_queue_basic_test PRIVATE ${CMAKE_SOURCE_DIR}/src)
target_include_directories(verification_store_thread_test PRIVATE ${CMAKE_SOURCE_DIR}/src)
target_include_directories(metric_source_enumeration_test PRIVATE ${CMAKE_SOURCE_DIR}/src)
target_include_directories(flat_snapshot_crc_mismatch_test PRIVATE ${CMAKE_SOURCE_DIR}/src)
target_include_directories(flat_snapshot_signature_invalid_test PRIVATE ${CMAKE_SOURCE_DIR}/src)
target_include_directories(probe_cache_service_start_test PRIVATE ${CMAKE_SOURCE_DIR}/src)
target_include_directories(runtime_scanner_basic_test PRIVATE ${CMAKE_SOURCE_DIR}/src)
target_include_directories(metrics_exporter_iteration_test PRIVATE ${CMAKE_SOURCE_DIR}/src)
target_include_directories(dynamic_verifier_rate_limit_test PRIVATE ${CMAKE_SOURCE_DIR}/src)
target_include_directories(seatbelt_verifier_profile_test PRIVATE ${CMAKE_SOURCE_DIR}/src ${CMAKE_SOURCE_DIR}/include)
target_include_directories(seatbelt_verifier_accept_test PRIVATE ${CMAKE_SOURCE_DIR}/src ${CMAKE_SOURCE_DIR}/include)
target_include_directories(seatbelt_verifier_profile_test PRIVATE $<TARGET_PROPERTY:Catch2::Catch2WithMain,INTERFACE_INCLUDE_DIRECTORIES>)
target_include_directories(seatbelt_verifier_accept_test PRIVATE $<TARGET_PROPERTY:Catch2::Catch2WithMain,INTERFACE_INCLUDE_DIRECTORIES>)
target_include_directories(hotpatch_table_roundtrip_test PRIVATE ${CMAKE_SOURCE_DIR}/src)
target_include_directories(verification_pipeline_order_test PRIVATE ${CMAKE_SOURCE_DIR}/src)
target_include_directories(mux_queue_starvation_test PRIVATE ${CMAKE_SOURCE_DIR}/src)
target_include_directories(queue_high_water_alert_test PRIVATE ${CMAKE_SOURCE_DIR}/src)
target_include_directories(queue_traits_spin_block_test PRIVATE ${CMAKE_SOURCE_DIR}/src)
target_include_directories(hdr_latency_metric_test PRIVATE ${CMAKE_SOURCE_DIR}/src)
target_include_directories(runtime_cold_scan_integration_test PRIVATE ${CMAKE_SOURCE_DIR}/src)
target_include_directories(verifier_latency_test PRIVATE ${CMAKE_SOURCE_DIR}/src)
target_include_directories(verifier_latency_test PRIVATE $<TARGET_PROPERTY:Catch2::Catch2WithMain,INTERFACE_INCLUDE_DIRECTORIES>)
target_include_directories(async_stream_test PRIVATE ${CMAKE_SOURCE_DIR}/src)
target_include_directories(sse_decoder_simd_test PRIVATE ${CMAKE_SOURCE_DIR}/src)
target_include_directories(sse_decoder_responses_test PRIVATE ${CMAKE_SOURCE_DIR}/src)
target_include_directories(responses_decoder_test PRIVATE ${CMAKE_SOURCE_DIR}/src)
target_include_directories(sse_decoder_error_event_test PRIVATE ${CMAKE_SOURCE_DIR}/src)
target_include_directories(sse_decoder_large_payload_test PRIVATE ${CMAKE_SOURCE_DIR}/src)
target_include_directories(json_writer_roundtrip_test PRIVATE ${CMAKE_SOURCE_DIR}/src)
target_include_directories(llm_factory_service_test PRIVATE ${CMAKE_SOURCE_DIR}/src)
target_include_directories(plugin_manifest_scan_test PRIVATE ${CMAKE_SOURCE_DIR}/src)
target_include_directories(plugin_capability_validation_test PRIVATE ${CMAKE_SOURCE_DIR}/src)

# Register tests with CTest
add_test(NAME AppIndexTest COMMAND app_index_test)
add_test(NAME AppScannerTest COMMAND app_scanner_test)
add_test(NAME FileWatcherTest COMMAND file_watcher_test)
add_test(NAME IncrementalUpdateTest COMMAND incremental_update_test)
add_test(NAME SearchHistoryTest COMMAND search_history_test)
add_test(NAME SearchCacheTest COMMAND search_cache_test)
add_test(NAME MemoryPoolTest COMMAND memory_pool_test)
add_test(NAME MemoryTrackerTest COMMAND memory_tracker_test)
add_test(NAME ServiceRegistryTest COMMAND service_registry_test)
add_test(NAME ServiceRegistryOrderTest COMMAND service_registry_order_test)
add_test(NAME ArenaAllocatorTest COMMAND arena_allocator_test)
add_test(NAME SlabArenaTest COMMAND slab_arena_test)
add_test(NAME EventBusTest COMMAND event_bus_test)
add_test(NAME CapabilityGenTest COMMAND capability_gen_test)
add_test(NAME ServiceRegistryCycleTest COMMAND service_registry_cycle_test)
add_test(NAME EventBusUnsubscribeTest COMMAND event_bus_unsubscribe_test)
add_test(NAME RuntimeManagerTest COMMAND runtime_manager_test)
add_test(NAME RuntimeManagerSandboxTest COMMAND runtime_manager_sandbox_test)
add_test(NAME EventBusIdleTest COMMAND event_bus_idle_test)
add_test(NAME CodesignVerifyTest COMMAND codesign_verify_test)
add_test(NAME McpClientServiceTest COMMAND mcp_client_service_test)
add_test(NAME ZeroAllocGuardTest COMMAND zero_alloc_guard_test)
add_test(NAME AllocatorZeroAllocGuardTest COMMAND allocator_zero_alloc_guard_test)
add_test(NAME ServiceNameLookupTest COMMAND service_name_lookup_test)
add_test(NAME EventBusBackPressureTest COMMAND event_bus_backpressure_test)
add_test(NAME ExecutorBackPressureTest COMMAND executor_backpressure_test)
add_test(NAME ServiceStartWallTest COMMAND service_start_wall_test)
add_test(NAME Mask128HelperTest COMMAND mask128_helper_test)
add_test(NAME AdaptiveCacheStatsTest COMMAND adaptive_cache_stats_test)
add_test(NAME VerificationStoreTest COMMAND verification_store_test)
add_test(NAME FlatSnapshotHeaderTest COMMAND flat_snapshot_header_test)
add_test(NAME AdaptiveCacheGrowTest COMMAND adaptive_cache_grow_test)
add_test(NAME ProbeCacheIntegrationTest COMMAND probe_cache_integration_test)
add_test(NAME VerdictStoreHitMissTest COMMAND verdict_store_hit_miss_test)
add_test(NAME RingQueueBasicTest COMMAND ring_queue_basic_test)
add_test(NAME VerificationStoreThreadTest COMMAND verification_store_thread_test)
add_test(NAME MetricSourceEnumerationTest COMMAND metric_source_enumeration_test)
add_test(NAME FlatSnapshotCrcMismatchTest COMMAND flat_snapshot_crc_mismatch_test)
add_test(NAME FlatSnapshotSignatureInvalidTest COMMAND flat_snapshot_signature_invalid_test)
add_test(NAME ProbeCacheServiceStartTest COMMAND probe_cache_service_start_test)
add_test(NAME RuntimeScannerBasicTest COMMAND runtime_scanner_basic_test)
add_test(NAME MetricsExporterIterationTest COMMAND metrics_exporter_iteration_test)
add_test(NAME DynamicVerifierRateLimitTest COMMAND dynamic_verifier_rate_limit_test)
add_test(NAME SeatbeltVerifierProfileTest COMMAND seatbelt_verifier_profile_test)
add_test(NAME SeatbeltVerifierAcceptTest COMMAND seatbelt_verifier_accept_test)
add_test(NAME HotpatchTableRoundtripTest COMMAND hotpatch_table_roundtrip_test)
add_test(NAME VerificationPipelineOrderTest COMMAND verification_pipeline_order_test)
add_test(NAME MuxQueueStarvationTest COMMAND mux_queue_starvation_test)
add_test(NAME QueueHighWaterAlertTest COMMAND queue_high_water_alert_test)
add_test(NAME QueueTraitsSpinBlockTest COMMAND queue_traits_spin_block_test)
add_test(NAME HdrLatencyMetricTest COMMAND hdr_latency_metric_test)
add_test(NAME RuntimeColdScanIntegrationTest COMMAND runtime_cold_scan_integration_test)
add_test(NAME VerifierLatencyTest COMMAND verifier_latency_test)
add_test(NAME AsyncStreamTest COMMAND async_stream_test)
add_test(NAME SseDecoderSimdTest COMMAND sse_decoder_simd_test)
add_test(NAME SseDecoderResponsesTest COMMAND sse_decoder_responses_test)
add_test(NAME ResponsesDecoderTest COMMAND responses_decoder_test)
add_test(NAME SseDecoderErrorEventTest COMMAND sse_decoder_error_event_test)
add_test(NAME SseDecoderLargePayloadTest COMMAND sse_decoder_large_payload_test)
add_test(NAME JsonWriterRoundtripTest COMMAND json_writer_roundtrip_test)
add_test(NAME LlmFactoryServiceTest COMMAND llm_factory_service_test)
add_test(NAME PluginManifestScanTest COMMAND plugin_manifest_scan_test)
add_test(NAME PluginCapabilityValidationTest COMMAND plugin_capability_validation_test)

# Ensure the core library is built with the same capacity so all translation
# units (including the EventBus service implementation) are consistent.
target_compile_definitions(core PUBLIC KAI_EVENTBUS_CAPACITY=8)

set_tests_properties(AdaptiveCacheStatsTest PROPERTIES LABELS bench)

#ifdef KAI_USE_LMDB_PROBECACHE
# add_executable(probe_cache_lmdb_hit_miss_test snapshot/probe_cache_lmdb_hit_miss_test.cpp)
# target_link_libraries(probe_cache_lmdb_hit_miss_test PRIVATE core snapshot_lib Catch2::Catch2WithMain)
# target_include_directories(probe_cache_lmdb_hit_miss_test PRIVATE ${CMAKE_SOURCE_DIR}/src)
# add_test(NAME ProbeCacheLmdbHitMissTest COMMAND probe_cache_lmdb_hit_miss_test)
#endif

if(KAI_USE_LMDB_PROBECACHE)
    add_executable(probe_cache_lmdb_hit_miss_test snapshot/probe_cache_lmdb_hit_miss_test.cpp)
    target_link_libraries(probe_cache_lmdb_hit_miss_test PRIVATE core snapshot_lib Catch2::Catch2WithMain)
    target_include_directories(probe_cache_lmdb_hit_miss_test PRIVATE ${CMAKE_SOURCE_DIR}/src)
    add_test(NAME ProbeCacheLmdbHitMissTest COMMAND probe_cache_lmdb_hit_miss_test)
endif()

# ---------------- MuxQueue tests (flag controlled) -------------------------
if(KAI_ENABLE_MUX_QUEUE)
    add_executable(mux_queue_basic_test events/mux_queue_basic_test.cpp)
    target_link_libraries(mux_queue_basic_test PRIVATE core Catch2::Catch2WithMain)
    target_include_directories(mux_queue_basic_test PRIVATE ${CMAKE_SOURCE_DIR}/src)
    add_test(NAME MuxQueueBasicTest COMMAND mux_queue_basic_test)

    add_executable(mux_queue_hazard_pointer_test events/mux_queue_hazard_pointer_test.cpp)
    target_link_libraries(mux_queue_hazard_pointer_test PRIVATE core Catch2::Catch2WithMain)
    target_include_directories(mux_queue_hazard_pointer_test PRIVATE ${CMAKE_SOURCE_DIR}/src)
    add_test(NAME MuxQueueHazardPointerTest COMMAND mux_queue_hazard_pointer_test)
endif()

# ---------------- ProbeCache hit-rate integration test ----------------
add_executable(probe_cache_hit_rate_test snapshot/probe_cache_hit_rate_integration_test.cpp)
target_link_libraries(probe_cache_hit_rate_test PRIVATE core snapshot_lib Catch2::Catch2WithMain)
target_include_directories(probe_cache_hit_rate_test PRIVATE ${CMAKE_SOURCE_DIR}/src)
add_test(NAME ProbeCacheHitRateTest COMMAND probe_cache_hit_rate_test)

if(KAI_ENABLE_FLATSNAPSHOT_SIGN)
    add_executable(flat_snapshot_ed25519_roundtrip_test storage/flat_snapshot_ed25519_roundtrip_test.cpp)
    target_link_libraries(flat_snapshot_ed25519_roundtrip_test PRIVATE core snapshot_lib Catch2::Catch2WithMain kai_crypto)
    target_include_directories(flat_snapshot_ed25519_roundtrip_test PRIVATE ${CMAKE_SOURCE_DIR}/src)
    add_test(NAME FlatSnapshotEd25519RoundtripTest COMMAND flat_snapshot_ed25519_roundtrip_test)
endif()

target_compile_definitions(runtime_scanner_basic_test PRIVATE KAI_EVENTBUS_CAPACITY=8)
target_compile_definitions(runtime_cold_scan_integration_test PRIVATE KAI_EVENTBUS_CAPACITY=64)

# -----------------------------------------------------------------------------
# Reusable interface target aggregating common link libs and include dirs
# -----------------------------------------------------------------------------
add_library(core_test_common INTERFACE)
target_link_libraries(core_test_common INTERFACE core snapshot_lib Catch2::Catch2WithMain)
target_include_directories(core_test_common INTERFACE ${CMAKE_SOURCE_DIR}/src)
