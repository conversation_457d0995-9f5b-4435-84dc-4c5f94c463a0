#ifdef KAI_USE_LMDB_PROBECACHE
#include "snapshot/probe_cache.hh"
#include <catch2/catch_test_macros.hpp>
#include <filesystem>
#include <random>

using launcher::snapshot::ProbeCache;
using launcher::core::security::Verdict;

static std::array<uint8_t, 32> makeSha256(uint64_t seed) {
    std::array<uint8_t, 32> out{};
    std::mt19937_64 rng(seed);
    for (auto &b : out) {
        b = static_cast<uint8_t>(rng() & 0xFFu);
    }
    return out;
}

TEST_CASE("LMDB ProbeCache hit/miss path", "[probe_cache][lmdb]") {
    auto dir = std::filesystem::temp_directory_path() / "probe_cache_lmdb_ut";
    std::filesystem::remove_all(dir);

    ProbeCache cache(dir, 1024);

    auto sha_known = makeSha256(1);
    auto sha_unknown = makeSha256(2);

    auto path_known = dir / "known";
    auto path_unknown = dir / "unknown";

    // update known verdict
    cache.update(path_known, sha_known, Verdict{Verdict::Code::kAllowed});
    cache.flush();

    // Should hit LMDB on first lookup after L1 miss, then L1
    auto v1 = cache.lookup(path_known, sha_known);
    REQUIRE(v1.code == Verdict::Code::kAllowed);

    // Unknown path should remain unknown
    auto v2 = cache.lookup(path_unknown, sha_unknown);
    REQUIRE(v2.code == Verdict::Code::kUnknown);

    auto st = cache.stats();
    REQUIRE(st.l1_hit + st.l2_hit + st.miss >= 2);
}
#endif 