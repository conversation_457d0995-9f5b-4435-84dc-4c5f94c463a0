#include "snapshot/verdict_store.hh"
#include <catch2/catch_test_macros.hpp>
#include <filesystem>
#include <random>

using launcher::snapshot::VerdictStore;
using launcher::core::security::Verdict;
using launcher::core::security::CDHash;

static CDHash makeHash(uint64_t seed) {
    CDHash h{};
    std::mt19937_64 rng(seed);
    for (auto &b : h.bytes) {
        b = static_cast<uint8_t>(rng() & 0xFFu);
    }
    return h;
}

TEST_CASE("VerdictStore cold / warm hit, ghost", "[verdict_store]") {
    auto tmp_dir = std::filesystem::temp_directory_path() / "verdict_store_it";
    std::filesystem::remove_all(tmp_dir);

    CDHash h1 = makeHash(1);
    CDHash h2 = makeHash(2);

    // Cold run – expect miss then persist Allowed
    {
        VerdictStore store(tmp_dir);
        auto v = store.lookup(h1);
        REQUIRE(v.code == Verdict::Code::kUnknown);

        store.persist(h1, Verdict{Verdict::Code::kAllowed});
        store.flush();
    }

    // Warm run – snapshot should load
    {
        VerdictStore store(tmp_dir);
        auto allowed = store.lookup(h1);
        REQUIRE(allowed.code == Verdict::Code::kAllowed);

        auto miss = store.lookup(h2);
        REQUIRE(miss.code == Verdict::Code::kUnknown);

        auto st = store.stats();
        REQUIRE(st.l1_hit + st.l2_hit >= 1);
        REQUIRE(st.miss >= 1);
    }
} 