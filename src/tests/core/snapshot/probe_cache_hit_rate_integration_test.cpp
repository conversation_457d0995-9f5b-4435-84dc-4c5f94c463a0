#include "snapshot/probe_cache.hh"
#include <catch2/catch_test_macros.hpp>
#include <array>
#include <filesystem>
#include <random>

using namespace launcher::snapshot;
using launcher::core::security::Verdict;

static std::array<uint8_t, 32> fakeSha256(uint64_t seed) {
    std::array<uint8_t, 32> out{};
    for (size_t i = 0; i < out.size(); ++i) out[i] = static_cast<uint8_t>((seed >> (i % 8)) & 0xFF);
    return out;
}

TEST_CASE("ProbeCache warm-start hit-rate ≥95%", "[probe_cache][integration]") {
    const std::size_t kRuntimes = 200;
    const auto tmp_dir = std::filesystem::temp_directory_path() / "probe_cache_hit_rate_test";
    std::filesystem::remove_all(tmp_dir);
    std::filesystem::create_directories(tmp_dir);

    {
        ProbeCache pc{tmp_dir, /*l1_sets_pow2*/ 256, /*flush_threshold*/ 16, /*prewarm*/ true};
        for (std::size_t i = 0; i < kRuntimes; ++i) {
            const auto path = tmp_dir / ("runtime_" + std::to_string(i));
            auto sha = fakeSha256(i);
            pc.update(path, sha, Verdict{Verdict::Code::kAllowed});
        }
        pc.flush();
    }

    {
        ProbeCache pc{tmp_dir};
        std::size_t hits = 0;
        for (std::size_t i = 0; i < kRuntimes; ++i) {
            const auto path = tmp_dir / ("runtime_" + std::to_string(i));
            auto sha = fakeSha256(i);
            auto v = pc.lookup(path, sha);
            if (v.code != Verdict::Code::kUnknown) ++hits;
        }
        const double hit_rate = static_cast<double>(hits) / static_cast<double>(kRuntimes);
        REQUIRE(hit_rate >= 0.95);
    }

    std::filesystem::remove_all(tmp_dir);
} 