#include "snapshot/probe_cache.hh"
#include <catch2/catch_test_macros.hpp>
#include <filesystem>
#include <random>
#include <cstring>

using launcher::snapshot::ProbeCache;
using launcher::core::security::Verdict;

static std::array<uint8_t, 32> makeSha256(uint64_t seed) {
    std::array<uint8_t, 32> out{};
    std::mt19937_64 rng(seed);
    for (auto &b : out) {
        b = static_cast<uint8_t>(rng() & 0xFFu);
    }
    return out;
}

TEST_CASE("ProbeCache cold / warm hit rate >=95%", "[probe_cache]") {
    auto tmp_dir = std::filesystem::temp_directory_path() / "probe_cache_it";
    std::filesystem::remove_all(tmp_dir);

    // First run – cold
    {
        ProbeCache cache(tmp_dir);
        for (int i = 0; i < 100; ++i) {
            auto path = tmp_dir / ("runtime_" + std::to_string(i));
            auto sha = makeSha256(i);
            cache.update(path, sha, Verdict{Verdict::Code::kAllowed});
        }
        cache.flush();
    }

    // Second run – warm (snapshot should be loaded)
    ProbeCache cache(tmp_dir);
    int hit = 0;
    for (int i = 0; i < 100; ++i) {
        auto path = tmp_dir / ("runtime_" + std::to_string(i));
        auto sha = makeSha256(i);
        auto v = cache.lookup(path, sha);
        if (v.code == Verdict::Code::kAllowed) ++hit;
    }

    // Perform second loop to exercise L1 hits
    for (int i = 0; i < 100; ++i) {
        auto path = tmp_dir / ("runtime_" + std::to_string(i));
        auto sha = makeSha256(i);
        cache.lookup(path, sha);
    }

    cache.flush();

    auto st = cache.stats();
    REQUIRE(hit == 100);
    REQUIRE((st.l1_hit + st.l2_hit) >= 95);
} 