#include "core/plugins/runtime_manager_service.h"
#include "core/foundation/registry.h"
#include "core/memory/arena_allocator_service.h"
#include "core/async/executor_service.h"
#include "core/events/event_bus_service.h"
#include "core/diagnostics/diagnostics_service.h"
#include "core/security/verification_store.hh"
#include <catch2/catch_test_macros.hpp>
#include <filesystem>
#include <fstream>

using namespace launcher::core;
using namespace launcher::core::plugins;

TEST_CASE("manifest with unsupported runtime triggers error", "[plugins][manifest]") {
    foundation::ServiceRegistry registry;
    memory::ArenaAllocatorSvc arena{registry};
    async::ExecutorService    exec{registry};
    events::EventBusService   bus{registry};
    diagnostics::DiagnosticsService diag{registry};
    security::VerificationStore store;

    exec.setDiagnostics(&diag);
    bus.setDiagnostics(&diag);
    exec.start();
    bus.start();
    // Diagnostics thread not required for this test – avoid sleep delays
    // diag.start();

    RuntimeManagerSvc rms{registry, store};

    // Temp manifest with unsupported runtime and zero capabilities
    const auto tmp_root = std::filesystem::temp_directory_path() / "cap_val_test";
    std::filesystem::remove_all(tmp_root);
    const auto plugins_dir = tmp_root / "Plugins";
    std::filesystem::create_directories(plugins_dir);

    std::ofstream out(plugins_dir / "u.toml");
    out << "id = \"cap_test\"\n";
    out << "runtime = \"javascript\"\n"; // unsupported runtime kind

    std::filesystem::current_path(tmp_root);

    REQUIRE(rms.loadManifests());

    // Activation should fail with UnsupportedRuntime
    auto res = rms.activatePlugin("cap_test");
    REQUIRE(!res);
    REQUIRE(res.error() == foundation::KaiError::UnsupportedRuntime);

    // diag.stop();
    bus.stop();
    exec.stop();
} 