#include "../../core/search/search_history.h"

#include <catch2/catch_test_macros.hpp>
#include <filesystem>
#include <fstream>
#include <memory>

namespace fs = std::filesystem;

TEST_CASE("SearchHistory basic operations", "[search_history]") {
    // Create a temporary file for testing
    fs::path tempFile = fs::temp_directory_path() / "search_history_test.json";

    // Remove the file if it exists
    if (fs::exists(tempFile)) {
        fs::remove(tempFile);
    }

    // Create a new search history
    launcher::core::SearchHistory history(tempFile.string());

    SECTION("New history is empty") {
        REQUIRE(history.getQueries().empty());
    }

    SECTION("Adding queries") {
        history.addQuery("firefox");
        history.addQuery("chrome");
        history.addQuery("vscode");

        auto queries = history.getQueries();
        REQUIRE(queries.size() == 3);
        REQUIRE(queries[0] == "vscode");  // Most recent first
        REQUIRE(queries[1] == "chrome");
        REQUIRE(queries[2] == "firefox");
    }

    SECTION("Adding duplicate queries") {
        history.addQuery("firefox");
        history.addQuery("chrome");
        history.addQuery("firefox");  // Duplicate

        auto queries = history.getQueries();
        REQUIRE(queries.size() == 2);
        REQUIRE(queries[0] == "firefox");  // Most recent first
        REQUIRE(queries[1] == "chrome");
    }

    SECTION("Limiting history size") {
        // Add more queries than the default limit
        for (int i = 0; i < 30; i++) {
            history.addQuery("query" + std::to_string(i));
        }

        auto queries = history.getQueries();
        REQUIRE(queries.size() == 20);     // Default limit is 20
        REQUIRE(queries[0] == "query29");  // Most recent first
    }

    SECTION("Persistence") {
        history.addQuery("firefox");
        history.addQuery("chrome");

        // Save to file
        history.saveToFile();

        // Create a new history from the same file
        launcher::core::SearchHistory newHistory(tempFile.string());
        newHistory.loadFromFile();

        auto queries = newHistory.getQueries();
        REQUIRE(queries.size() == 2);
        REQUIRE(queries[0] == "chrome");
        REQUIRE(queries[1] == "firefox");
    }

    // Clean up
    if (fs::exists(tempFile)) {
        fs::remove(tempFile);
    }
}

TEST_CASE("SearchHistory frequency tracking", "[search_history]") {
    // Create a temporary file for testing
    fs::path tempFile = fs::temp_directory_path() / "search_history_freq_test.json";

    // Remove the file if it exists
    if (fs::exists(tempFile)) {
        fs::remove(tempFile);
    }

    // Create a new search history
    launcher::core::SearchHistory history(tempFile.string());

    SECTION("Query frequency") {
        history.addQuery("firefox");
        history.addQuery("chrome");
        history.addQuery("firefox");  // Duplicate
        history.addQuery("firefox");  // Duplicate

        REQUIRE(history.getQueryFrequency("firefox") == 3);
        REQUIRE(history.getQueryFrequency("chrome") == 1);
        REQUIRE(history.getQueryFrequency("nonexistent") == 0);
    }

    SECTION("Most frequent queries") {
        history.addQuery("firefox");
        history.addQuery("chrome");
        history.addQuery("vscode");
        history.addQuery("firefox");
        history.addQuery("firefox");
        history.addQuery("chrome");

        auto frequentQueries = history.getMostFrequentQueries(2);
        REQUIRE(frequentQueries.size() == 2);
        REQUIRE(frequentQueries[0] == "firefox");  // 3 times
        REQUIRE(frequentQueries[1] == "chrome");   // 2 times
    }

    SECTION("Clear history") {
        history.addQuery("firefox");
        history.addQuery("chrome");

        history.clear();

        REQUIRE(history.getQueries().empty());
        REQUIRE(history.getQueryFrequency("firefox") == 0);
    }

    // Clean up
    if (fs::exists(tempFile)) {
        fs::remove(tempFile);
    }
}