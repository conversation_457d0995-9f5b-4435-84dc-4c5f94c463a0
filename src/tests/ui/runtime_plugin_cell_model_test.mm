#import <XCTest/XCTest.h>
#include "core/security/mask128.hh"
#import "ui/models/runtime_plugin_cell_model.h"

@interface RuntimePluginCellModelTest : XCTestCase
@end

@implementation RuntimePluginCellModelTest

- (void)testBasicProperties {
    launcher::core::security::Mask128 m; m.set(5);
    RuntimePluginCellModel *model = [[RuntimePluginCellModel alloc] initWithRuntimeId:std::string("test")
                                                                              version:std::string("1.0")
                                                                       capabilityMask:m
                                                                               enabled:YES];
    XCTAssertEqualObjects(model.runtimeId, @"test");
    XCTAssertTrue(model.enabled);
    XCTAssertEqual(model.capabilitiesHex.length, 32u);
}

@end 