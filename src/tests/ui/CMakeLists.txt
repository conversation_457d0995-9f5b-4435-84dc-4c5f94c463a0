if(APPLE)
    # Build UI tests as an XCTest bundle so the XCTest runner drives the
    # lifecycle instead of a custom NSApplicationMain that never terminates.
    add_library(ui_plugins_pane_test MODULE
        runtime_plugin_cell_model_test.mm
    )

    # Mark as .xctest bundle – <PERSON><PERSON><PERSON> will set the correct flags (linker -bundle)
    # and place the binary inside an .xctest directory that <PERSON><PERSON><PERSON> can load.
    set_target_properties(ui_plugins_pane_test PROPERTIES
        BUNDLE TRUE
        BUNDLE_EXTENSION xctest
        MACOSX_BUNDLE TRUE
    )

    # Required frameworks
    find_library(APPKIT_LIBRARY AppKit REQUIRED)
    find_library(XCTEST_LIBRARY XCTest REQUIRED)

    target_link_libraries(ui_plugins_pane_test PRIVATE
        ${APPKIT_LIBRARY}
        ${XCTEST_LIBRARY}
        ui_macos
        core
    )

    # Drive the test via xcrun xctest which discovers XCTestCase subclasses.
    add_test(NAME UiRuntimeTests
             COMMAND xcrun xctest $<TARGET_BUNDLE_DIR:ui_plugins_pane_test>)
endif() 