# Find Catch2 package for testing
enable_testing()

find_package(Catch2 QUIET)

if(NOT Catch2_FOUND)
    include(Fetch<PERSON>ontent)
    FetchContent_Declare(
        Catch2
        GIT_REPOSITORY https://github.com/catchorg/Catch2.git
        GIT_TAG v3.4.0
    )
    FetchContent_MakeAvailable(Catch2)
endif()

# Add test subdirectories
add_subdirectory(core)
add_subdirectory(tools)
add_subdirectory(plugins/openai)

# Integration tests are opt-in (external network) – lives under integration/
add_subdirectory(integration)

if(APPLE)
    add_subdirectory(ui)
endif()