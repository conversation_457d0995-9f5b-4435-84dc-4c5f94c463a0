// BEGIN FILE
#include <catch2/catch_test_macros.hpp>
#include "../../plugins/openai/openai_provider_plugin.h"
#include "../../core/llm/il_provider.h"
#include <thread>
#include <netinet/in.h>
#include <sys/socket.h>
#include <unistd.h>
#include <cstring>
#include <cstdlib>
#include <arpa/inet.h>

using namespace launcher::plugins::openai;

namespace {
void run_stub_server(bool& stop_flag) {
    int sock = socket(AF_INET, SOCK_STREAM, 0);
    if (sock < 0) return;

    int opt = 1;
    setsockopt(sock, SOL_SOCKET, SO_REUSEADDR, &opt, sizeof(opt));

    sockaddr_in addr{};
    addr.sin_family = AF_INET;
    addr.sin_addr.s_addr = htonl(INADDR_LOOPBACK); // 127.0.0.1
    addr.sin_port = htons(18081);
    if (bind(sock, (sockaddr*)&addr, sizeof(addr)) != 0) { close(sock); return; }
    if (listen(sock, 1) != 0) { close(sock); return; }

    while (!stop_flag) {
        fd_set rfds;
        FD_ZERO(&rfds);
        FD_SET(sock, &rfds);
        timeval tv{0, 100000}; // 100 ms
        int ret = select(sock+1, &rfds, nullptr, nullptr, &tv);
        if (ret <= 0) continue;
        int client = accept(sock, nullptr, nullptr);
        if (client < 0) continue;
        char buffer[1024];
        read(client, buffer, sizeof(buffer)); // ignore request content
        const char* body = "{\"choices\":[{\"message\":{\"content\":\"Hello stub\"}}]}";
        std::string response = "HTTP/1.1 200 OK\r\nContent-Type: application/json\r\nContent-Length: " + std::to_string(strlen(body)) + "\r\n\r\n" + body;
        send(client, response.c_str(), response.size(), 0);
        close(client);
    }
    close(sock);
}
}

TEST_CASE("OpenAI provider real HTTP call via stub", "[openai][integration]") {
    // Override env variables
    setenv("OPENAI_BASE_URL", "http://127.0.0.1:18081/v1", 1);
    setenv("OPENAI_API_KEY", "test-key", 1);

    bool stop_flag = false;
    std::thread server_thread(run_stub_server, std::ref(stop_flag));

    // Give server time to start
    std::this_thread::sleep_for(std::chrono::milliseconds(100));

    OpenAiProviderPlugin provider;
    auto result = provider.complete("Ping", "");

    stop_flag = true;
    server_thread.join();

    REQUIRE(result.has_value());
    REQUIRE(result.value().text == "Hello stub");
}
// END FILE 