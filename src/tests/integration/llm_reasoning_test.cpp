#include <catch2/catch_test_macros.hpp>
#include "core/providers/openai/openai_model.h"
#include "core/providers/anthropic/anthropic_model.h"
#include "core/context/context.h"
#include "core/llm/reasoning_control.h"
#include "utilities/cancellation_token.h"
#include "core/config/config_manager.h"
#include <string_view>

using namespace launcher::core;

static bool hasEnv(const char* name) {
    return std::getenv(name) != nullptr;
}

static bool containsThought(const std::vector<std::string>& chunks) {
    for (const auto& c : chunks) {
        if (c.rfind("THOUGHT:", 0) == 0) return true;
    }
    return false;
}

static std::string fetchApiKey(const std::string& provider) {
    // 1. Env var override (OPENAI_API_KEY / ANTHROPIC_API_KEY)
    std::string envKeyName = provider == "openai" ? "OPENAI_API_KEY" : "ANTHROPIC_API_KEY";
    const char* envVal = std::getenv(envKeyName.c_str());
    if (envVal && *envVal) return std::string(envVal);

    // 2. ConfigManager fallback
    static launcher::core::ConfigManager cfg;
    static bool cfgInit = false;
    if (!cfgInit) {
        cfg.initialize();
        cfgInit = true;
    }
    return cfg.getApiKeyFor(provider);
}

TEST_CASE("OpenAI reasoning stream returns CoT deltas", "[integration][openai]") {
    std::string apiKey = fetchApiKey("openai");
    if (apiKey.empty()) {
        WARN("OpenAI API key not configured – skipping test");
        return;
    }

    // Reasoning model already embeds reasoning control; we can still tweak options if desired.
    OpenAIOptions opts;
    opts.temperature = 0.0;
    ReasoningControl rc;
    rc.effort = "medium";
    opts.reasoning = rc;

    auto model = std::make_shared<launcher::core::OpenAIModel>("o4-mini", opts, apiKey);
    REQUIRE(model);

    Context ctx; // empty context
    std::vector<std::string> out;
    auto token = std::make_shared<utilities::CancellationToken>();

    try {
        model->stream("Explain why the sky is blue in one sentence.", ctx,
            [&](std::string_view chunk){ out.emplace_back(chunk); }, token);
    } catch (const std::exception& e) {
        WARN(std::string("OpenAI reasoning request failed: ") + e.what());
        return; // skip test
    }

    if (out.empty()) {
        WARN("OpenAI reasoning stream returned no data – skipping assertion");
        return;
    }
    std::cout << "OpenAI deltas:\n";
    for (const auto& part : out) std::cout << part;
    std::cout << std::endl;
    if (!containsThought(out)) {
        WARN("OpenAI reasoning stream produced no THOUGHT tokens – continuing");
    }
}

TEST_CASE("Anthropic reasoning stream returns CoT deltas", "[integration][anthropic]") {
    std::string apiKey = fetchApiKey("anthropic");
    if (apiKey.empty()) {
        WARN("Anthropic API key not configured – skipping test");
        return;
    }

    AnthropicOptions opts;
    opts.temperature = 0.0;
    ReasoningControl rc2;
    rc2.effort = "medium";
    rc2.budget = 16000;
    opts.reasoning = rc2;

    auto model = std::make_shared<launcher::core::AnthropicModel>("claude-opus-4-20250514", opts, apiKey);
    REQUIRE(model);

    Context ctx;
    std::vector<std::string> out;
    auto token = std::make_shared<utilities::CancellationToken>();

    try {
        model->stream("Explain gravity briefly.", ctx, [&](std::string_view c){ out.emplace_back(c); }, token);
    } catch (const std::exception& e) {
        WARN(std::string("Anthropic reasoning request failed: ") + e.what());
        return;
    }
    if (out.empty()) {
        WARN("Anthropic reasoning stream returned no data – skipping assertion");
        return;
    }
    std::cout << "Anthropic deltas:\n";
    for (const auto& part : out) std::cout << part;
    std::cout << std::endl;
    REQUIRE(containsThought(out));
} 