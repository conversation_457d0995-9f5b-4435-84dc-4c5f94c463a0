# Integration tests for external LLM providers (OpenAI, Anthropic)

add_compile_definitions(TESTING)

add_executable(llm_reasoning_test
    llm_reasoning_test.cpp
)

add_executable(openai_provider_integration_test
    openai_provider_integration_test.cpp
)

# Provides access to project headers
target_include_directories(llm_reasoning_test PRIVATE
    ${CMAKE_SOURCE_DIR}/src
)

target_include_directories(openai_provider_integration_test PRIVATE
    ${CMAKE_SOURCE_DIR}/src
)

# Link against core library and Catch2 test framework
find_package(Catch2 REQUIRED)

target_link_libraries(llm_reasoning_test PRIVATE core Catch2::Catch2WithMain)
find_package(Threads REQUIRED)
target_link_libraries(openai_provider_integration_test PRIVATE core Catch2::Catch2WithMain Threads::Threads openai)

# Register with CTest (disabled by default because it hits external network)
option(ENABLE_LLM_INTEGRATION_TESTS "Run integration tests that call external LLM endpoints" OFF)
if(ENABLE_LLM_INTEGRATION_TESTS)
    add_test(NAME LLMReasoningTest COMMAND llm_reasoning_test)
    add_test(NAME openai_provider_integration_test COMMAND openai_provider_integration_test)
endif() 