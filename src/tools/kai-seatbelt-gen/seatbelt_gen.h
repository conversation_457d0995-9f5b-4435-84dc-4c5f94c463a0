//------------------------------------------------------------------------------
// ⚙️ kai-seatbelt-gen – Header-only generator utilities
//------------------------------------------------------------------------------
#pragma once

#include <filesystem>
#include <string>
#include <string_view>
#include <vector>
#include <optional>

namespace kai::tools {

struct SandboxSpec {
    std::vector<std::string> network_patterns;   // remote tcp patterns
    std::vector<std::string> fs_paths;           // subpath patterns (read-only)
    bool exec_allowed{false};                    // process-exec
    bool jit_allowed{false};                     // jit-memory
};

// Parses the [sandbox] section of manifest.toml (very small subset of TOML).
// On failure, returns std::nullopt and sets human_readable_error.
std::optional<SandboxSpec> parseSandboxSpec(const std::filesystem::path& manifest_path,
                                           std::string&                  error) noexcept;

// Renders a seatbelt profile from the parsed spec.
std::string renderSeatbeltProfile(const SandboxSpec& spec);

// Convenience helper: load → parse → render. Returns empty string if error.
std::string generateSeatbeltProfile(const std::filesystem::path& manifest_path,
                                    std::string&                  error) noexcept;

} // namespace kai::tools 