// kai-seatbelt-gen – render macOS seatbelt sandbox profile from manifest.toml
// -----------------------------------------------------------------------------
#include "seatbelt_gen.h"

#include <filesystem>
#include <fstream>
#include <iostream>
#include <string>
#include <vector>

namespace fs = std::filesystem;

struct Options {
    fs::path manifest_path;
    fs::path output_path;
    bool dry_run{false};
};

static std::optional<Options> parseArgs(int argc, char** argv) {
    Options opt;
    for (int i = 1; i < argc; ++i) {
        std::string_view arg{argv[i]};
        if ((arg == "-m" || arg == "--manifest") && i + 1 < argc) {
            opt.manifest_path = argv[++i];
        } else if ((arg == "-o" || arg == "--out") && i + 1 < argc) {
            opt.output_path = argv[++i];
        } else if (arg == "--dry-run") {
            opt.dry_run = true;
        } else if (arg == "-h" || arg == "--help") {
            std::cout << "kai-seatbelt-gen\n"
                         "  -m, --manifest <file>   Plugin manifest.toml (required)\n"
                         "  -o, --out      <file>   Output .sb path       (required if not --dry-run)\n"
                         "      --dry-run          Print profile to stdout\n";
            return std::nullopt;
        } else {
            std::cerr << "Unknown argument: " << arg << "\n";
            return std::nullopt;
        }
    }
    if (opt.manifest_path.empty()) {
        std::cerr << "Error: --manifest is required.\n";
        return std::nullopt;
    }
    if (!opt.dry_run && opt.output_path.empty()) {
        std::cerr << "Error: --out is required when not using --dry-run.\n";
        return std::nullopt;
    }
    return opt;
}

int main(int argc, char** argv) {
    auto optsOpt = parseArgs(argc, argv);
    if (!optsOpt) {
        return 1; // help printed or error
    }
    const auto& opts = *optsOpt;

    std::string error;
    std::string profile = kai::tools::generateSeatbeltProfile(opts.manifest_path, error);
    if (!error.empty()) {
        std::cerr << "Error: " << error << "\n";
        return 2;
    }

    if (opts.dry_run || opts.output_path.empty()) {
        std::cout << profile;
    } else {
        std::ofstream out(opts.output_path);
        if (!out) {
            std::cerr << "Cannot open output file: " << opts.output_path << "\n";
            return 3;
        }
        out << profile;
    }
    return 0;
} 