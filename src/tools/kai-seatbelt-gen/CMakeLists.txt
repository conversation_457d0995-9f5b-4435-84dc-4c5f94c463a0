cmake_minimum_required(VERSION 3.25)

# kai-seatbelt-gen – emits .sb sandbox profile from manifest.toml
add_library(seatbelt_gen_lib STATIC
    seatbelt_gen.cpp
    seatbelt_gen.h
)

set_target_properties(seatbelt_gen_lib PROPERTIES
    CXX_STANDARD 23
    CXX_STANDARD_REQUIRED ON
    CXX_EXTENSIONS OFF
)

target_include_directories(seatbelt_gen_lib PUBLIC ${CMAKE_CURRENT_LIST_DIR})

add_executable(kai-seatbelt-gen
    main.cpp
)

target_link_libraries(kai-seatbelt-gen PRIVATE seatbelt_gen_lib)

# -----------------------------------------------------------------------------
# Dependency – cpptoml (header-only)
# -----------------------------------------------------------------------------
include(FetchContent)
FetchContent_Declare(
    cpptoml
    GIT_REPOSITORY https://github.com/skystrife/cpptoml.git
    GIT_TAG v0.1.1
)
FetchContent_MakeAvailable(cpptoml)

target_include_directories(seatbelt_gen_lib PUBLIC ${cpptoml_SOURCE_DIR}/include)
target_include_directories(kai-seatbelt-gen PRIVATE ${cpptoml_SOURCE_DIR}/include) 