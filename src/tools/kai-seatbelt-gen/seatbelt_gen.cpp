#include "seatbelt_gen.h"
#include <cpptoml.h>

namespace kai::tools {
namespace {
} // namespace

// ----------------------------------------------------------------------------
std::optional<SandboxSpec> parseSandboxSpec(const std::filesystem::path& manifest_path,
                                           std::string&                  error) noexcept {
    try {
        auto root = cpptoml::parse_file(manifest_path.string());
        auto sandbox_tbl = root->get_table("sandbox");
        if (!sandbox_tbl) {
            error = "[sandbox] table missing";
            return std::nullopt;
        }

        SandboxSpec spec;
        if (auto net_arr = sandbox_tbl->get_array_of<std::string>("network")) {
            spec.network_patterns = *net_arr;
        }
        if (auto fs_arr = sandbox_tbl->get_array_of<std::string>("filesystem")) {
            spec.fs_paths = *fs_arr;
        }
        if (auto exec_val = sandbox_tbl->get_as<bool>("exec")) {
            spec.exec_allowed = *exec_val;
        }
        if (auto jit_val = sandbox_tbl->get_as<bool>("jit")) {
            spec.jit_allowed = *jit_val;
        }

        // Validate not empty
        if (spec.network_patterns.empty() && spec.fs_paths.empty() && !spec.exec_allowed && !spec.jit_allowed) {
            error = "[sandbox] table empty";
            return std::nullopt;
        }
        return spec;
    } catch (const cpptoml::parse_exception& ex) {
        error = std::string{"TOML parse error: "} + ex.what();
        return std::nullopt;
    } catch (const std::exception& ex) {
        error = std::string{"Error: "} + ex.what();
        return std::nullopt;
    }
}

// ----------------------------------------------------------------------------
static void appendLine(std::string& out, std::string_view line) {
    out.append(line.data(), line.size());
    out.push_back('\n');
}

std::string renderSeatbeltProfile(const SandboxSpec& s) {
    std::string out;
    appendLine(out, "(version 1)");
    appendLine(out, "(deny default)");
    out.push_back('\n');

    // Network
    if (!s.network_patterns.empty()) {
        appendLine(out, ";; Network");
        appendLine(out, "(allow network-outbound");
        for (const auto& pat : s.network_patterns) {
            appendLine(out, "    (remote tcp \"" + pat + "\")");
        }
        appendLine(out, ")");
        out.push_back('\n');
    }

    // Filesystem
    if (!s.fs_paths.empty()) {
        appendLine(out, ";; Read-only filesystem");
        appendLine(out, "(allow file-read*");
        for (const auto& p : s.fs_paths) {
            appendLine(out, "    (subpath \"" + p + "\")");
        }
        appendLine(out, ")");
        out.push_back('\n');
    }

    // Exec
    if (s.exec_allowed) {
        appendLine(out, ";; Exec capability");
        appendLine(out, "(allow process-exec)");
        out.push_back('\n');
    }

    // JIT
    if (s.jit_allowed) {
        appendLine(out, ";; JIT capability");
        appendLine(out, "(allow jit-memory)");
        out.push_back('\n');
    }

    return out;
}

std::string generateSeatbeltProfile(const std::filesystem::path& manifest_path,
                                    std::string&                  error) noexcept {
    auto specOpt = parseSandboxSpec(manifest_path, error);
    if (!specOpt) return {};
    return renderSeatbeltProfile(*specOpt);
}

} // namespace kai::tools
