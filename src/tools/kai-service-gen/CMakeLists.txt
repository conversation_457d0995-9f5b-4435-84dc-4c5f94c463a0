add_executable(kai-service-gen
    main.cpp
)

# Dependencies ---------------------------------------------------------
include(FetchContent)

# yaml-cpp (header + static lib)
FetchContent_Declare(
    yaml_cpp
    GIT_REPOSITORY https://github.com/jbeder/yaml-cpp.git
    GIT_TAG 0.8.0
)
FetchContent_MakeAvailable(yaml_cpp)

# bbhash (header-only)
FetchContent_Declare(
    bbhash
    GIT_REPOSITORY https://github.com/rizkg/BBHash.git
    GIT_TAG master
)
FetchContent_MakeAvailable(bbhash)

# Include directories

target_include_directories(kai-service-gen PRIVATE ${yaml_cpp_SOURCE_DIR}/include ${bbhash_SOURCE_DIR})

# link fmt already provided globally

target_link_libraries(kai-service-gen PRIVATE yaml-cpp fmt::fmt)

# Enable C++23
set_target_properties(kai-service-gen PROPERTIES CXX_STANDARD 23 CXX_STANDARD_REQUIRED YES)

# Header-only interface target for other consumers
add_library(kai-service-gen-lib INTERFACE)
target_include_directories(kai-service-gen-lib INTERFACE ${CMAKE_CURRENT_SOURCE_DIR} ${yaml_cpp_SOURCE_DIR}/include ${bbhash_SOURCE_DIR}) 