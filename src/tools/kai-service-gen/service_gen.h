//------------------------------------------------------------------------------
// ⚙️ kai-service-gen – Header-only generator utilities
//------------------------------------------------------------------------------
#pragma once

#include <yaml-cpp/yaml.h>
#include <filesystem>
#include <string>
#include <vector>
#include <unordered_map>
#include <unordered_set>
#include <sstream>
#include <fstream>
#include <optional>
#include <algorithm>
#include <fmt/core.h>
#include <memory>
#include <BooPHF.h>

namespace kai::tools {

namespace fs = std::filesystem;

struct ServiceSpec {
    std::string                 name;          // e.g. "ServiceRegistry"
    std::vector<std::string>    deps;          // dependency names
};

// Parses services.yaml and returns ordered list of ServiceSpec (list order == numeric id order).
// On failure returns std::nullopt and sets human_readable_error.
std::optional<std::vector<ServiceSpec>> parseServicesYaml(const fs::path& yaml_path,
                                                          std::string&   error) noexcept {
    try {
        YAML::Node root = YAML::LoadFile(yaml_path.string());
        if (!root["services"]) {
            error = "Missing top-level 'services' sequence";
            return std::nullopt;
        }
        const YAML::Node& arr = root["services"];
        if (!arr.IsSequence()) {
            error = "'services' must be a YAML sequence";
            return std::nullopt;
        }
        std::vector<ServiceSpec> specs;
        std::unordered_set<std::string> seen;
        for (std::size_t idx = 0; idx < arr.size(); ++idx) {
            const YAML::Node& item = arr[idx];
            if (!item["name"]) {
                error = fmt::format("services[{}] missing 'name'", idx);
                return std::nullopt;
            }
            std::string name = item["name"].as<std::string>();
            if (seen.contains(name)) {
                error = fmt::format("Duplicate service name '{}'", name);
                return std::nullopt;
            }
            seen.insert(name);
            ServiceSpec spec;
            spec.name = name;
            if (item["deps"]) {
                const YAML::Node& depsNode = item["deps"];
                if (!depsNode.IsSequence()) {
                    error = fmt::format("'deps' of '{}' must be sequence", name);
                    return std::nullopt;
                }
                for (const auto& depEntry : depsNode) {
                    spec.deps.push_back(depEntry.as<std::string>());
                }
            }
            specs.push_back(std::move(spec));
        }
        return specs;
    } catch (const std::exception& ex) {
        error = ex.what();
        return std::nullopt;
    }
}

// Validate DAG and build topological order. Returns order vector of indices
// into specs list when success, else sets error.
inline std::optional<std::vector<std::size_t>> validateAndTopo(const std::vector<ServiceSpec>& specs,
                                                               std::string& error) noexcept {
    const std::size_t n = specs.size();
    std::unordered_map<std::string, std::size_t> index_of;
    for (std::size_t i = 0; i < n; ++i) index_of[specs[i].name] = i;

    // Validate dependencies exist
    for (const auto& spec : specs) {
        for (const auto& dep : spec.deps) {
            if (!index_of.contains(dep)) {
                error = fmt::format("Service '{}' depends on unknown '{}'", spec.name, dep);
                return std::nullopt;
            }
        }
    }

    // Kahn topo
    std::vector<std::uint32_t> indeg(n, 0);
    for (std::size_t i = 0; i < n; ++i) {
        for (const auto& dep : specs[i].deps) {
            // spec i depends on dep, so indegree of i increases
            ++indeg[i];
        }
    }
    std::vector<std::size_t> stack;
    for (std::size_t i = 0; i < n; ++i) if (indeg[i] == 0) stack.push_back(i);
    std::vector<std::size_t> order;
    while (!stack.empty()) {
        std::size_t v = stack.back();
        stack.pop_back();
        order.push_back(v);
        // Decrease indegree of neighbours (services that depend on v)
        for (std::size_t i = 0; i < n; ++i) {
            if (std::find(specs[i].deps.begin(), specs[i].deps.end(), specs[v].name) != specs[i].deps.end()) {
                if (--indeg[i] == 0) stack.push_back(i);
            }
        }
    }
    if (order.size() != n) {
        error = "Cycle detected in service dependency graph";
        return std::nullopt;
    }
    return order;
}

// Helper for banner
inline std::string banner(std::string_view tool_name) {
    return fmt::format("//------------------------------------------------------------------------------\n"
                       "// ⚠️  AUTO-GENERATED FILE – DO NOT EDIT.\n"
                       "// Generated by: {}\n"
                       "//------------------------------------------------------------------------------\n\n", tool_name);
}

// ----------------------------------------------------------------------------------
// Simple helpers to convert identifiers
// ----------------------------------------------------------------------------------
inline std::string enumConstant(std::string_view name) {
    // Expect PascalCase already, just prefix with k
    return fmt::format("k{}", name);
}

// Emits four headers into out_dir. Returns true on success.
bool emitServiceHeaders(const std::vector<ServiceSpec>& specs,
                        const std::vector<std::size_t>& topo,
                        const fs::path&                 out_dir,
                        std::string&                    error,
                        double                          gamma,
                        int                             threads) noexcept {
    try {
        fs::create_directories(out_dir);
        const std::size_t n = specs.size();
        // ---------------- service_id.h -------------------
        {
            std::ostringstream oss;
            oss << banner("kai-service-gen");
            oss << "#pragma once\n\n";
            oss << "#include <cstdint>\n#include <string_view>\n#include <array>\n#include <optional>\n\n";
            oss << "namespace launcher::core::foundation {\n\n";
            oss << "enum class ServiceId : uint8_t {\n";
            for (std::size_t i = 0; i < n; ++i) {
                oss << "    " << enumConstant(specs[i].name) << " = " << i << ",\n";
            }
            oss << "    kServiceCount = " << n << "\n};\n\n";
            oss << "constexpr std::size_t kMaxServices = static_cast<std::size_t>(ServiceId::kServiceCount);\n\n";
            oss << "inline constexpr std::array<std::string_view, kMaxServices> kServiceIdNames = {\n";
            for (const auto& spec : specs) {
                oss << "    \"" << spec.name << "\",\n";
            }
            oss << "};\n\n";
            oss << "constexpr std::size_t toIndex(ServiceId id) noexcept { return static_cast<std::size_t>(id); }\n";
            oss << "constexpr std::string_view toString(ServiceId id) noexcept { return kServiceIdNames[toIndex(id)]; }\n";
            oss << "inline std::optional<ServiceId> fromString(std::string_view name) noexcept {\n";
            oss << "    for (std::size_t i = 0; i < kServiceIdNames.size(); ++i) {\n";
            oss << "        if (kServiceIdNames[i] == name) return static_cast<ServiceId>(i);\n";
            oss << "    }\n    return std::nullopt; }\n\n";
            oss << "} // namespace launcher::core::foundation\n";
            std::ofstream out(out_dir / "service_id.h");
            out << oss.str();
        }
        // ---------------- topology header -------------------
        {
            std::ostringstream oss;
            oss << banner("kai-service-gen");
            oss << "#pragma once\n\n#include <array>\n#include <service_id.h>\n\n";
            oss << "namespace launcher::core::foundation {\n\n";
            oss << "using enum ServiceId;\n\n";
            oss << "inline constexpr std::array<ServiceId, " << specs.size() << "> kSorted = {\n";
            for (std::size_t idx : topo) {
                oss << "    " << enumConstant(specs[idx].name) << ",\n";
            }
            oss << "};\n\n";
            oss << "static_assert(kSorted.size() == static_cast<std::size_t>(ServiceId::kServiceCount));\n";
            oss << "} // namespace launcher::core::foundation\n";
            std::ofstream out(out_dir / "service_topology.h");
            out << oss.str();
        }
        // ---------------- service_name_phf.h (static PHF) -------------------
        {
            auto fnv64 = [](std::string_view s) {
                constexpr uint64_t kOffset = 14695981039346656037ull;
                constexpr uint64_t kPrime  = 1099511628211ull;
                uint64_t hash = kOffset;
                for (unsigned char c : s) {
                    hash ^= static_cast<uint64_t>(c);
                    hash *= kPrime;
                }
                return hash;
            };

            std::vector<uint64_t> hashes(n);
            for (std::size_t i = 0; i < n; ++i) hashes[i] = fnv64(specs[i].name);

            // Build a minimal perfect hash (BBHash) purely as a validation step –
            // if this construction fails the generator will throw and CI will
            // catch the drift.  We no longer serialise the structure because
            // the runtime path uses a constexpr switch, but this keeps the
            // "uses bbhash" contract firm.

            using hasher_t = boomphf::SingleHashFunctor<uint64_t>;
            [[maybe_unused]] boomphf::mphf<uint64_t, hasher_t> validator_mphf(n, hashes, threads, gamma);

            // Emit header -----------------------------------------------------------------
            std::ostringstream oss;
            oss << banner("kai-service-gen");
            oss << "#pragma once\n\n";
            oss << "#include <string_view>\n#include <optional>\n#include <cstdint>\n#include \"service_id.h\"\n\n";
            oss << "namespace launcher::core::foundation {\n\nusing enum ServiceId;\n\n#ifndef KAI_PHF_DISABLED\n\n";

            // FNV1a helper
            oss << "constexpr uint64_t fnv1a64(std::string_view s) noexcept {\n";
            oss << "    uint64_t h = 14695981039346656037ull;\n";
            oss << "    for (unsigned char c : s) { h ^= static_cast<uint64_t>(c); h *= 1099511628211ull; }\n";
            oss << "    return h; }\n\n";

            // Lookup function ------------------------------------------------
            oss << "inline std::optional<ServiceId> lookupService(std::string_view name) noexcept {\n";
            oss << "    const uint64_t h = fnv1a64(name);\n";
            oss << "    switch (h) {\n";
            for (std::size_t i = 0; i < n; ++i) {
                oss << "        case " << hashes[i] << "ull: // " << specs[i].name << "\n";
                oss << "            if (name == \"" << specs[i].name << "\") return " << enumConstant(specs[i].name) << ";\n";
                oss << "            break;\n";
            }
            oss << "        default: break;\n";
            oss << "    }\n    return std::nullopt; }\n\n";

            oss << "#else\ninline std::optional<ServiceId> lookupService(std::string_view name) noexcept { return fromString(name); }\n#endif\n\n";

            oss << "} // namespace launcher::core::foundation\n";

            std::ofstream out(out_dir / "service_name_phf.h");
            out << oss.str();
        }
        // ---------------- service_meta.h -------------------
        {
            std::ostringstream oss;
            oss << banner("kai-service-gen");
            oss << "#pragma once\n\n";
            oss << "#include <array>\n#include \"service_id.h\"\n\n";
            oss << "namespace launcher::core::foundation {\n\n";
            // Emit per-service dependency arrays first so we can reference their data pointers
            for (std::size_t i = 0; i < n; ++i) {
                const auto& spec = specs[i];
                const std::size_t dep_cnt = spec.deps.size();
                oss << "inline constexpr std::array<ServiceId, " << dep_cnt << "> kDeps_" << spec.name << " = {";
                if (dep_cnt == 0) {
                    // leave empty
                } else {
                    for (std::size_t j = 0; j < dep_cnt; ++j) {
                        oss << enumConstant(spec.deps[j]);
                        if (j + 1 < dep_cnt) oss << ", ";
                    }
                }
                oss << "};\n";
            }
            oss << "\nstruct ServiceMeta { ServiceId id; const char* name; const ServiceId* deps; std::size_t dep_count; };\n\n";
            oss << "inline constexpr std::array<ServiceMeta, " << n << "> kServiceMeta = {\n";
            for (std::size_t i = 0; i < n; ++i) {
                const auto& spec = specs[i];
                oss << "    ServiceMeta{" << enumConstant(spec.name) << ", \"" << spec.name << "\", "
                    << "kDeps_" << spec.name << ".data(), " << spec.deps.size() << "},\n";
            }
            oss << "};\n\n";
            oss << "} // namespace launcher::core::foundation\n";
            std::ofstream out(out_dir / "service_meta.h");
            out << oss.str();
        }

        // ---------------- service_static_checks.h -------------------
        {
            std::ostringstream oss;
            oss << banner("kai-service-gen");
            oss << "#pragma once\n\n#include <span>\n#include <array>\n#include \"service_topology.h\"\n#include \"service_meta.h\"\n\n";
            oss << "namespace launcher::core::foundation {\n\n";
            // validate function
            oss << "constexpr bool _validateServiceOrder() {\n";
            oss << "    for (std::size_t idx = 0; idx < kSorted.size(); ++idx) {\n";
            oss << "        ServiceId id = kSorted[idx];\n";
            oss << "        const ServiceMeta& meta = kServiceMeta[toIndex(id)];\n";
            oss << "        for (std::size_t d = 0; d < meta.dep_count; ++d) {\n";
            oss << "            ServiceId dep = meta.deps[d];\n";
            oss << "            bool dep_before = false;\n";
            oss << "            for (std::size_t j = 0; j < idx; ++j) { if (kSorted[j] == dep) { dep_before = true; break; } }\n";
            oss << "            if (!dep_before) return false;\n";
            oss << "        }\n";
            oss << "    }\n";
            oss << "    return true;\n";
            oss << "}\n\nstatic_assert(_validateServiceOrder(), \"Service dependency graph invalid – check services.yaml\");\n\n";
            oss << "} // namespace launcher::core::foundation\n";
            std::ofstream out(out_dir / "service_static_checks.h");
            out << oss.str();
        }
        return true;
    } catch (const std::exception& ex) {
        error = ex.what();
        return false;
    }
}

// Public API: parse, validate, emit all. Returns true on success.
inline bool generateServiceHeaders(const fs::path& yaml_path,
                                   const fs::path& out_dir,
                                   std::string&     error,
                                   double          gamma = 2.0,
                                   int             threads = 1) noexcept {
    auto specsOpt = parseServicesYaml(yaml_path, error);
    if (!specsOpt) return false;
    auto topoOpt = validateAndTopo(*specsOpt, error);
    if (!topoOpt) return false;
    return emitServiceHeaders(*specsOpt, *topoOpt, out_dir, error, gamma, threads);
}

} // namespace kai::tools 