// kai-service-gen – generate service headers from services.yaml
// -----------------------------------------------------------------------------
#include "service_gen.h"

#include <filesystem>
#include <iostream>
#include <string>
#include <vector>

namespace fs = std::filesystem;

struct Options {
    fs::path yaml_path;
    fs::path out_dir; // directory
    bool dry_run{false};
    double gamma{2.0};
    int threads{1};
};

static std::optional<Options> parseArgs(int argc, char** argv) {
    Options opt;
    for (int i = 1; i < argc; ++i) {
        std::string_view arg{argv[i]};
        if ((arg == "-y" || arg == "--yaml") && i + 1 < argc) {
            opt.yaml_path = argv[++i];
        } else if ((arg == "-o" || arg == "--out") && i + 1 < argc) {
            opt.out_dir = argv[++i];
        } else if (arg == "--dry-run") {
            opt.dry_run = true;
        } else if (arg == "--gamma" && i + 1 < argc) {
            opt.gamma = std::stod(argv[++i]);
        } else if (arg == "--threads" && i + 1 < argc) {
            opt.threads = std::stoi(argv[++i]);
        } else if (arg == "-h" || arg == "--help") {
            std::cout << "kai-service-gen\n"
                      << "  -y, --yaml <file>   services.yaml (required)\n"
                      << "  -o, --out  <dir>    output directory (default: ./gen)\n"
                      << "      --dry-run       parse & validate only, no output\n"
                      << "      --gamma <value>  gamma value (default: 2.0)\n"
                      << "      --threads <num>  number of threads (default: 1)\n";
            return std::nullopt; // show help
        } else {
            std::cerr << "Unknown argument: " << arg << "\n";
            return std::nullopt;
        }
    }
    if (opt.yaml_path.empty()) {
        std::cerr << "Error: --yaml is required.\n";
        return std::nullopt;
    }
    if (!opt.dry_run && opt.out_dir.empty()) {
        opt.out_dir = fs::current_path() / "gen";
    }
    return opt;
}

int main(int argc, char** argv) {
    auto optOpt = parseArgs(argc, argv);
    if (!optOpt) return 1;
    const auto& opts = *optOpt;

    std::string err;
    if (opts.dry_run) {
        auto specsOpt = kai::tools::parseServicesYaml(opts.yaml_path, err);
        if (!specsOpt) {
            std::cerr << "Error: " << err << "\n";
            return 2;
        }
        std::cout << "Parsed " << specsOpt->size() << " services successfully.\n";
        std::vector<std::size_t> topo;
        auto topoOpt = kai::tools::validateAndTopo(*specsOpt, err);
        if (!topoOpt) {
            std::cerr << "Error: " << err << "\n";
            return 2;
        }
        std::cout << "Dependency graph valid, topo length " << topoOpt->size() << "\n";
        return 0;
    }

    if (!kai::tools::generateServiceHeaders(opts.yaml_path, opts.out_dir, err, opts.gamma, opts.threads)) {
        std::cerr << "Error: " << err << "\n";
        return 2;
    }
    std::cout << "Headers generated at " << opts.out_dir << "\n";
    return 0;
} 