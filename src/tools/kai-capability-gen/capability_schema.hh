//------------------------------------------------------------------------------
// capability_schema.hh – shared helpers for manifest.schema.json processing
//------------------------------------------------------------------------------
#pragma once

#include <algorithm>
#include <stdexcept>
#include <string>
#include <vector>

#include <nlohmann/json.hpp>

namespace kai::capability_schema {

using json = nlohmann::json;

// Extracts the capability list (array of strings) from a manifest JSON Schema
// object. Performs structural validation, de-duplicates, sorts for deterministic
// output and enforces the 256-entry hard limit. Throws std::runtime_error when
// the schema is malformed or exceeds limits.
inline std::vector<std::string> extractCapabilities(const json& root) {
    // Expected path: definitions.capability.enum
    auto defs_it = root.find("definitions");
    if (defs_it == root.end()) {
        throw std::runtime_error("Missing 'definitions' in schema");
    }
    auto cap_it = defs_it->find("capability");
    if (cap_it == defs_it->end()) {
        throw std::runtime_error("Missing 'capability' definition in schema");
    }
    auto enum_it = cap_it->find("enum");
    if (enum_it == cap_it->end() || !enum_it->is_array()) {
        throw std::runtime_error("'capability.enum' not found or is not array");
    }

    std::vector<std::string> caps = enum_it->get<std::vector<std::string>>();

    // Remove duplicates & sort for deterministic output
    std::sort(caps.begin(), caps.end());
    caps.erase(std::unique(caps.begin(), caps.end()), caps.end());

    if (caps.size() > 256) {
        throw std::runtime_error("Capability list exceeds 256 entries");
    }
    return caps;
}

} // namespace kai::capability_schema 