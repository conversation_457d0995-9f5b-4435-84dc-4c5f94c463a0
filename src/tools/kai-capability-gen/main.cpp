// kai-capability-gen
// -----------------------------------------------------------------------------
// Build-time CLI tool that reads manifest.schema.json and emits capability256.h
// containing:
//   • enum class Capability : uint8_t (≤256 entries)
//   • CapabilityBitmap (std::array<uint64_t,4>)
//   • constexpr helpers: set(), has(), makeBitmap()
//
// The tool is intentionally minimal, uses only header-only deps: nlohmann/json
// and fmt for string formatting. It avoids any heap allocations after startup
// except those performed by nlohmann::json when parsing the schema.
// -----------------------------------------------------------------------------

#include <algorithm>
#include <array>
#include <cstdint>
#include <cctype>
#include <filesystem>
#include <fstream>
#include <chrono>
#include <ctime>
#include <optional>
#include <initializer_list>
#include <iostream>
#include <sstream>
#include <string>
#include <string_view>
#include <vector>

#include <fmt/format.h>
#include <nlohmann/json.hpp>
#include "capability_schema.hh"

namespace fs = std::filesystem;
using json     = nlohmann::json;

namespace {

// Simple FNV-1a 64-bit hash – enough to detect drift, not for crypto.
constexpr uint64_t fnv_offset_basis = 14695981039346656037ULL;
constexpr uint64_t fnv_prime        = 1099511628211ULL;

std::string fnv1a64_hex(std::string_view data) {
    uint64_t hash = fnv_offset_basis;
    for (unsigned char c : data) {
        hash ^= static_cast<uint64_t>(c);
        hash *= fnv_prime;
    }
    std::ostringstream oss;
    oss << std::hex << std::uppercase;
    oss.fill('0');
    oss.width(16);
    oss << hash;
    return oss.str();
}

// Converts snake-case / kebab-case identifier to PascalCase and prepends 'k'.
// E.g. "read_fs" → "kReadFs", "network-access" → "kNetworkAccess".
std::string toEnumConstant(std::string_view name) {
    std::string out;
    out.reserve(name.size() + 2);
    out.push_back('k');
    bool upper_next = true;
    for (char ch : name) {
        if (ch == '_' || ch == '-' || ch == ' ') {
            upper_next = true;
            continue;
        }
        if (upper_next) {
            out.push_back(static_cast<char>(std::toupper(static_cast<unsigned char>(ch))));
            upper_next = false;
        } else {
            out.push_back(ch);
        }
    }
    return out;
}

struct Options {
    fs::path schema_path;
    fs::path output_path;
    bool dry_run = false;
};

std::optional<Options> parseCommandLine(int argc, char** argv) {
    Options opt;

    for (int i = 1; i < argc; ++i) {
        std::string_view arg = argv[i];
        if ((arg == "-s" || arg == "--schema") && i + 1 < argc) {
            opt.schema_path = argv[++i];
        } else if ((arg == "-o" || arg == "--out") && i + 1 < argc) {
            opt.output_path = argv[++i];
        } else if (arg == "--dry-run") {
            opt.dry_run = true;
        } else if (arg == "-h" || arg == "--help") {
            std::cout << "kai-capability-gen\n"
                         "  -s, --schema  <file>   manifest.schema.json (required)\n"
                         "  -o, --out     <file>   Output header path      (required)\n"
                         "      --dry-run          Print to stdout only\n";
            return std::nullopt; // Signal help printed.
        } else {
            std::cerr << "Unknown argument: " << arg << '\n';
            return std::nullopt;
        }
    }

    if (opt.schema_path.empty() || opt.output_path.empty()) {
        std::cerr << "Error: --schema and --out are required.\n";
        return std::nullopt;
    }

    return opt;
}

// -----------------------------------------------------------------------------
// Code-generation helpers
// -----------------------------------------------------------------------------

std::string emitHeader(const std::vector<std::string>& caps, std::string_view schema_hash_hex) {
    const std::string timestamp = [] {
        auto      now   = std::chrono::system_clock::now();
        std::time_t tt  = std::chrono::system_clock::to_time_t(now);
        std::tm     tm  = *std::gmtime(&tt);
        char        buf[64];
        std::strftime(buf, sizeof(buf), "%Y-%m-%dT%H:%M:%SZ", &tm);
        return std::string{buf};
    }();

    std::string out;

    // Banner
    out += fmt::format("//------------------------------------------------------------------------------\n");
    out += fmt::format("// ⚠️  AUTO-GENERATED FILE – DO NOT EDIT.\n");
    out += fmt::format("// Generated by: kai-capability-gen\n");
    out += fmt::format("// Schema hash: {}\n", schema_hash_hex);
    out += fmt::format("//------------------------------------------------------------------------------\n\n");

    // Pragmas & includes
    out += "#pragma once\n\n";
    out += "#include <array>\n";
    out += "#include <cstdint>\n";
    out += "#include <initializer_list>\n";
    out += "#include <string_view>\n";
    out += "#include <optional>\n\n";

    out += "namespace kai {\n\n";

    // Enum
    out += "enum class Capability : uint8_t {\n";
    for (size_t i = 0; i < caps.size(); ++i) {
        out += fmt::format("    {} = {},\n", toEnumConstant(caps[i]), i);
    }
    // Pad up to 256 members with reserved entries to guarantee ABI stability
    for (size_t i = caps.size(); i < 256; ++i) {
        out += fmt::format("    _RESERVED_{} = {},\n", i, i);
    }
    out += "};\n\n";

    // Bitmap typedef
    out += "using CapabilityBitmap = std::array<uint64_t, 4>;\n\n";

    // set()
    out += "constexpr void set(CapabilityBitmap& bm, Capability cap) {\n";
    out += "    const auto idx = static_cast<uint8_t>(cap);\n";
    out += "    bm[idx >> 6] |= uint64_t{1} << (idx & 63);\n";
    out += "}\n\n";

    // has()
    out += "constexpr bool has(const CapabilityBitmap& bm, Capability cap) {\n";
    out += "    const auto idx = static_cast<uint8_t>(cap);\n";
    out += "    return (bm[idx >> 6] & (uint64_t{1} << (idx & 63))) != 0ULL;\n";
    out += "}\n\n";

    // makeBitmap()
    out += "constexpr CapabilityBitmap makeBitmap(std::initializer_list<Capability> caps_init) {\n";
    out += "    CapabilityBitmap bm{0, 0, 0, 0};\n";
    out += "    for (auto cap : caps_init) { set(bm, cap); }\n";
    out += "    return bm;\n";
    out += "}\n\n";

    // ---------------------------------------------------------------------
    // Capability ↔ string helpers
    // ---------------------------------------------------------------------
    out += "// Array mapping enum → string_view (size 256).\n";
    out += "inline constexpr std::array<std::string_view, 256> kCapabilityNames = {\n";
    for (size_t i = 0; i < caps.size(); ++i) {
        out += fmt::format("    \"{}\",\n", caps[i]);
    }
    for (size_t i = caps.size(); i < 256; ++i) {
        out += "    \"\",\n";
    }
    out += "};\n\n";

    out += "constexpr std::string_view toString(Capability cap) noexcept {\n";
    out += "    return kCapabilityNames[static_cast<uint8_t>(cap)];\n";
    out += "}\n\n";

    out += "constexpr std::optional<Capability> fromString(std::string_view name) noexcept {\n";
    out += "    for (uint16_t i = 0; i < kCapabilityNames.size(); ++i) {\n";
    out += "        if (kCapabilityNames[i] == name) return static_cast<Capability>(i);\n";
    out += "    }\n";
    out += "    return std::nullopt;\n";
    out += "}\n\n";

    out += "} // namespace kai\n";

    return out;
}

} // namespace

int main(int argc, char** argv) {
    try {
        auto opt = parseCommandLine(argc, argv);
        if (!opt) {
            // Help was printed or error.
            return 1;
        }

        // Read schema
        std::ifstream schema_file(opt->schema_path);
        if (!schema_file) {
            fmt::print(stderr, "Failed to open schema file: {}\n", opt->schema_path.string());
            return 1;
        }
        json schema_json;
        schema_file >> schema_json;

        // Extract capability list (validated & deterministic order)
        auto capabilities = kai::capability_schema::extractCapabilities(schema_json);

        // Compute simple hash of joined capability list for banner stability
        std::string joined;
        for (const auto& c : capabilities) {
            joined.append(c);
            joined.push_back('\n');
        }
        const std::string hash_hex = fnv1a64_hex(joined);

        // Emit header
        const std::string header_text = emitHeader(capabilities, hash_hex);

        if (opt->dry_run) {
            fmt::print("{}", header_text);
        } else {
            // Ensure directory exists
            fs::create_directories(opt->output_path.parent_path());
            std::ofstream out_file(opt->output_path);
            if (!out_file) {
                fmt::print(stderr, "Failed to open output path: {}\n", opt->output_path.string());
                return 1;
            }
            out_file << header_text;
            fmt::print("Generated {} ({} capabilities)\n", opt->output_path.string(), capabilities.size());
        }

        return 0;
    } catch (const std::exception& ex) {
        fmt::print(stderr, "kai-capability-gen error: {}\n", ex.what());
        return 1;
    }
} 