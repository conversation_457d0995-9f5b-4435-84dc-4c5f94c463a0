cmake_minimum_required(VERSION 3.25)

# kai-capability-gen – generates capability256.h from manifest.schema.json
add_executable(kai-capability-gen
    main.cpp
)

# Link dependencies available from the top-level FetchContent declarations.
# Root CMake builds fmt and nlohmann_json targets.
if(TARGET fmt::fmt-header-only)
    set(FMT_TARGET fmt::fmt-header-only)
elseif(TARGET fmt::fmt)
    set(FMT_TARGET fmt::fmt)
else()
    message(FATAL_ERROR "fmt target not found – ensure top-level CMake fetched fmt")
endif()

if(NOT TARGET nlohmann_json::nlohmann_json)
    message(FATAL_ERROR "nlohmann_json target not found – ensure top-level CMake fetched json")
endif()

# C++23 standard already enforced globally, but set again for clarity.
set_target_properties(kai-capability-gen PROPERTIES
    CXX_STANDARD 23
    CXX_STANDARD_REQUIRED ON
    CXX_EXTENSIONS OFF
)

target_link_libraries(kai-capability-gen
    PRIVATE
    nlohmann_json::nlohmann_json
    ${FMT_TARGET}
) 