#pragma once

#include <atomic>
#include <memory> // For std::shared_ptr
#include <vector>
#include <mutex>
#include <functional>

namespace launcher {
namespace core {
namespace utilities {

/**
 * @brief A simple cancellation token for signalling cancellation requests.
 *
 * This class uses an atomic boolean flag to allow thread-safe checking
 * and signalling of cancellation.
 */
class CancellationToken {
public:
    CancellationToken() : cancelled_(false) {}

    // Disable copy semantics
    CancellationToken(const CancellationToken&) = delete;
    CancellationToken& operator=(const CancellationToken&) = delete;

    // Allow move semantics (though typically used via shared_ptr)
    CancellationToken(CancellationToken&& other) noexcept : cancelled_(other.cancelled_.load()) {}
    CancellationToken& operator=(CancellationToken&& other) noexcept {
        if (this != &other) {
            cancelled_.store(other.cancelled_.load());
        }
        return *this;
    }

    /**
     * @brief Signals that the operation should be cancelled.
     *
     * Sets the internal cancellation flag to true.
     */
    void cancel() {
        cancelled_.store(true);
        // Notify registered callbacks exactly once
        std::vector<std::function<void()>> local;
        {
            std::lock_guard<std::mutex> lk(cb_mtx_);
            local.swap(callbacks_);
        }
        for (auto &cb : local) {
            if (cb) cb();
        }
    }

    /**
     * @brief Checks if cancellation has been requested.
     *
     * @return true if cancel() has been called, false otherwise.
     */
    bool isCancelled() const {
        return cancelled_.load();
    }

    /**
     * @brief Resets the cancellation flag.
     *
     * Sets the internal cancellation flag back to false.
     * Use with caution, generally tokens are single-use.
     */
    void reset() {
        cancelled_.store(false);
    }

    // Register a callback to be invoked when cancel() is called. If already cancelled, invokes immediately.
    void registerCallback(std::function<void()> cb) {
        if (!cb) return;
        if (isCancelled()) {
            cb();
            return;
        }
        std::lock_guard<std::mutex> lk(cb_mtx_);
        callbacks_.push_back(std::move(cb));
    }

private:
    std::atomic<bool> cancelled_;
    std::mutex cb_mtx_;
    std::vector<std::function<void()>> callbacks_;
};

} // namespace utilities
} // namespace core
} // namespace launcher 