#pragma once

#import <Cocoa/Cocoa.h>
#include <cmark-gfm.h>

/**
 * @brief Class responsible for formatting messages with Markdown support
 *
 * This class handles the formatting of messages in the Commander UI,
 * specifically handling Markdown syntax, code blocks, and other formatting
 * for chat messages.
 */
@interface MessageFormatter : NSObject

/**
 * Unified message formatting function that handles all formatting needs
 *
 * @param content The main message content with optional Markdown
 * @return The formatted NSAttributedString
 */
- (NSAttributedString* _Nonnull)formatMessage:(NSString* _Nullable)content;

/**
 * Process Markdown formatting in content text
 *
 * @param content The text content with Markdown syntax
 * @return NSMutableAttributedString with formatted content
 */
- (NSMutableAttributedString* _Nonnull)formatMarkdownContent:(NSString* _Nullable)content;

/**
 * Process Markdown formatting using CommonMark (cmark) library
 *
 * @param content The text content with Markdown syntax
 * @return NSMutableAttributedString with formatted content
 */
- (NSMutableAttributedString* _Nonnull)formatMarkdownContentWithCMark:(NSString* _Nullable)content;

/**
 * Format agent message with special handling
 *
 * @param content The text content with Markdown syntax
 * @return NSMutableAttributedString with formatted content
 */
- (NSMutableAttributedString* _Nullable)formatAgentMessageContent:(NSString* _Nullable)content;

/**
 * Get a friendly display name for an agent ID
 *
 * @param agentId The agent ID to format
 * @return Formatted agent name
 */
- (NSString* _Nonnull)agentNameForId:(NSString* _Nullable)agentId;

@end