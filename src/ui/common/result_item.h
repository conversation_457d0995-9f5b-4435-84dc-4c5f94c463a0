#pragma once

#include <string>
#include <vector>

namespace launcher {
namespace ui {

/**
 * \brief A single item shown in search results / history lists.
 */
struct ResultItem {
    std::string name;        ///< Display name of the item
    std::string path;        ///< File path or identifier
    std::string iconPath;    ///< Optional icon path
    double      score;       ///< Ranking score (higher is better)
    std::string type;        ///< Category ("app", "file", etc.)
    std::string identifier;  ///< Stable identifier for history merging

    ResultItem() : score(0.0) {}

    ResultItem(const std::string& name,
               const std::string& path,
               const std::string& iconPath,
               double score,
               const std::string& type = "app",
               const std::string& identifier = "")
        : name(name), path(path), iconPath(iconPath), score(score), type(type), identifier(identifier) {}
};

} // namespace ui
} // namespace launcher 