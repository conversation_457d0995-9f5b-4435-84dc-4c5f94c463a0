// icon_utils.mm
//------------------------------------------------------------------------------
// Shared utility for generating tinted SF-Symbol icons for chat windows.
//------------------------------------------------------------------------------

#ifdef __APPLE__

#import "icon_utils.h"

// Pull in metadata provider
#import "../chat/session_metadata_manager.h"
#include "../../core/util/debug.h"

using launcher::ui::SessionMetadataManager;

namespace {

// Convenience: global cache so we don't regenerate the same icon repeatedly.
static NSCache<NSString *, NSImage *> *IconCache() {
    static NSCache<NSString *, NSImage *> *cache = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        cache = [[NSCache alloc] init];
        cache.countLimit = 512; // Arbitrary reasonable cap
    });
    return cache;
}

static NSString *CacheKey(NSWindow *window, CGFloat size, BOOL tint) {
    return [NSString stringWithFormat:@"%p-%g-%d", window, size, tint];
}

static NSImage *ImageWithTint(NSImage *src, NSColor *tint) {
    if (!src || !tint) { return src; }

    // Always perform manual tinting to maximise SDK compatibility.  Calling
    // imageWithTintColor: directly is unsafe when building against older
    // SDKs where the selector may be absent at compile-time.

    NSImage *copy = [src copy];
    [copy setTemplate:YES];
    [copy lockFocus];
    [tint set];
    NSRectFillUsingOperation(NSMakeRect(0, 0, copy.size.width, copy.size.height),
                             NSCompositingOperationSourceAtop);
    [copy unlockFocus];
    [copy setTemplate:NO]; // Retain original colours – prevents system re-tint
    return copy;
}

} // anonymous namespace

NSImage * _Nullable LUGenerateWindowSymbolIcon(NSWindow *window,
                                              CGFloat   pointSize,
                                              BOOL      applyTint) {
    if (!window) { return nil; }

    NSString *key = CacheKey(window, pointSize, applyTint);
    if (NSImage *cached = [IconCache() objectForKey:key]) {
        return cached;
    }

    // --- Retrieve metadata --------------------------------------------------
    NSString *symbolName = SessionMetadataManager::shared().iconSymbolNameForWindow(window);
    if (!symbolName) { return nil; }

    // Build base symbol image
    NSImage *icon = nil;
    if (@available(macOS 11.0, *)) {
        icon = [NSImage imageWithSystemSymbolName:symbolName accessibilityDescription:nil];
        if (icon) {
            NSImageSymbolConfiguration *cfg = [NSImageSymbolConfiguration configurationWithPointSize:pointSize
                                                                                              weight:NSFontWeightRegular];
            icon = [icon imageWithSymbolConfiguration:cfg];
        }
    } else {
        // Fallback: try loading from asset catalog or named image
        icon = [NSImage imageNamed:symbolName];
    }

    if (!icon) { return nil; }

    if (applyTint) {
        NSColor *tint = SessionMetadataManager::shared().tintColorForWindow(window);
        if (tint) {
            icon = ImageWithTint(icon, tint);
        }
    }

    [IconCache() setObject:icon forKey:key];
    return icon;
}

#endif // __APPLE__ 