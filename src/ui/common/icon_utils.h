#ifndef ICON_UTILS_H
#define ICON_UTILS_H

#import <Cocoa/Cocoa.h>

NS_ASSUME_NONNULL_BEGIN

//------------------------------------------------------------------------------
// LUGenerateWindowSymbolIcon
// Returns an SF-Symbol image for the given window sized to `pointSize` and
// optionally tinted with the window's SessionMetadataManager tint colour.
// If the window has no metadata symbol, this function returns nil so callers
// can gracefully fall back to the default miniwindow image.
//------------------------------------------------------------------------------
NSImage * _Nullable LUGenerateWindowSymbolIcon(NSWindow *window,
                                              C<PERSON><PERSON><PERSON>   pointSize,
                                              BOOL      applyTint);

NS_ASSUME_NONNULL_END

#endif  // ICON_UTILS_H 