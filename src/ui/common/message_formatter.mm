#import "message_formatter.h"
#import <Foundation/Foundation.h> // Needed for NSString etc.
#include <cmark-gfm.h>

// Import debug.h to access DBM
#import "../../core/util/debug.h"

@interface MessageFormatter ()

// Properties for incremental Markdown parsing
@property (nonatomic, assign) NSUInteger lastParsedPosition;
@property (nonatomic, strong) NSMutableAttributedString *parsedContent;
@property (nonatomic, strong) NSCache *formattedChunksCache;
@property (nonatomic, strong) NSString *lastChunkSignature;

// Properties for improved Markdown state tracking
@property (nonatomic, assign) BOOL insideCodeBlock;
@property (nonatomic, strong) NSString *currentCodeBlockLanguage;
@property (nonatomic, strong) NSMutableArray<NSString *> *openMarkdownStructures;

// Regex pattern cache to improve performance
@property (nonatomic, strong) NSMutableDictionary<NSString *, NSRegularExpression *> *regexCache;
@property (nonatomic, strong) NSDictionary<NSString *, NSString *> *languageAliases;

@end

@implementation MessageFormatter

- (instancetype)init {
    self = [super init];
    if (self) {
        // Initialize collections
        self.formattedChunksCache = [[NSCache alloc] init];
        self.openMarkdownStructures = [NSMutableArray array];
        self.insideCodeBlock = NO;
        self.currentCodeBlockLanguage = nil;
        self.lastChunkSignature = @"";
        
        // Initialize regex cache for performance
        self.regexCache = [NSMutableDictionary dictionary];
        
        // Initialize comprehensive language alias mapping
        self.languageAliases = @{
            // JavaScript and related
            @"javascript": @"js",
            @"typescript": @"ts",
            @"jsx": @"js",
            @"tsx": @"ts",
            @"node": @"js",
            @"nodejs": @"js",
            
            // Python
            @"python": @"py",
            @"python3": @"py",
            @"py3": @"py",
            @"pypy": @"py",
            @"ipython": @"py",
            @"jupyter": @"py",
            
            // Ruby
            @"rb": @"ruby",
            
            // C/C++/C#
            @"c++": @"cpp",
            @"c": @"c",
            @"cc": @"cpp",
            @"cxx": @"cpp",
            @"h": @"cpp",
            @"hpp": @"cpp",
            @"csharp": @"cs",
            
            // Swift/Objective-C
            @"objc": @"objectivec",
            @"objective-c": @"objectivec",
            @"objectivec": @"objectivec",
            @"m": @"objectivec",
            @"mm": @"objectivec",
            
            // Web languages
            @"html": @"html",
            @"xml": @"xml",
            @"css": @"css",
            @"scss": @"css",
            @"less": @"css",
            @"json": @"json",
            @"yaml": @"yaml",
            @"yml": @"yaml",
            
            // Other common languages
            @"go": @"go",
            @"golang": @"go",
            @"rust": @"rust",
            @"rs": @"rust",
            @"java": @"java",
            @"kotlin": @"kotlin",
            @"kt": @"kotlin",
            @"php": @"php",
            @"scala": @"scala",
            @"shell": @"bash",
            @"bash": @"bash",
            @"sh": @"bash",
            @"zsh": @"bash",
            @"sql": @"sql",
            @"perl": @"perl",
            @"pl": @"perl",
            @"markdown": @"md",
            @"md": @"md"
        };
    }
    return self;
}

/**
 * Unified message formatting function that handles all formatting needs.
 * 
 * @param content The main message content with optional Markdown
 * @return The formatted NSAttributedString
 */
- (NSAttributedString*)formatMessage:(NSString*)content {
    
    NSMutableAttributedString* result = [[NSMutableAttributedString alloc] init];
    
    // Create paragraph style with text block for the whole message
    NSMutableParagraphStyle* messageParagraphStyle = [[NSMutableParagraphStyle alloc] init];
    messageParagraphStyle.lineSpacing = 4;
    messageParagraphStyle.paragraphSpacing = 8;
    
    // Create a text block for the message background
    NSTextBlock *messageBlock = [[NSTextBlock alloc] init];
    
    // Set message block to span full width
    [messageBlock setContentWidth:100 type:NSTextBlockPercentageValueType];
    
    // Set vertical alignment to middle
    [messageBlock setVerticalAlignment:NSTextBlockMiddleAlignment];
    
    // Set padding for better appearance
    CGFloat padding = 10.0;
    [messageBlock setWidth:padding type:NSTextBlockAbsoluteValueType forLayer:NSTextBlockPadding edge:NSRectEdgeMinX];
    [messageBlock setWidth:padding type:NSTextBlockAbsoluteValueType forLayer:NSTextBlockPadding edge:NSRectEdgeMaxX];
    [messageBlock setWidth:padding type:NSTextBlockAbsoluteValueType forLayer:NSTextBlockPadding edge:NSRectEdgeMinY];
    [messageBlock setWidth:padding type:NSTextBlockAbsoluteValueType forLayer:NSTextBlockPadding edge:NSRectEdgeMaxY];
    
    // Add margins for spacing between messages
    [messageBlock setWidth:5 type:NSTextBlockAbsoluteValueType forLayer:NSTextBlockMargin edge:NSRectEdgeMinY];
    [messageBlock setWidth:5 type:NSTextBlockAbsoluteValueType forLayer:NSTextBlockMargin edge:NSRectEdgeMaxY];
    
    // Add the text block to the paragraph style
    messageParagraphStyle.textBlocks = @[messageBlock];
    
    // Format content with Markdown processing
    NSMutableAttributedString* formattedContent = [self formatMarkdownContent:content];
    [result appendAttributedString:formattedContent];
    
    // Add line break after content (no timestamp handling)
    [result appendAttributedString:[[NSAttributedString alloc] initWithString:@"\n"]];
    
    // Find and exclude code block areas when applying message styles
    NSUInteger position = 0;
    NSMutableArray<NSValue *> *codeBlockRanges = [NSMutableArray array];
    
    while (position < result.length) {
        NSRange effectiveRange;
        id value = [result attribute:@"IsCodeBlock" atIndex:position longestEffectiveRange:&effectiveRange inRange:NSMakeRange(position, result.length - position)];
        
        if (value && [value isKindOfClass:[NSNumber class]] && [(NSNumber *)value boolValue]) {
            // Found a code block range
            [codeBlockRanges addObject:[NSValue valueWithRange:effectiveRange]];
            position = NSMaxRange(effectiveRange);
        } else {
            // Skip non-code block content
            position = effectiveRange.location + effectiveRange.length;
        }
    }
    
    if (codeBlockRanges.count > 0) {
        // Apply message styling to non-code-block ranges
        NSUInteger lastEnd = 0;
        
        for (NSValue *rangeValue in codeBlockRanges) {
            NSRange codeRange = [rangeValue rangeValue];
            
            // Apply message style to the range before this code block
            if (codeRange.location > lastEnd) {
                NSRange beforeRange = NSMakeRange(lastEnd, codeRange.location - lastEnd);
                [result addAttribute:NSParagraphStyleAttributeName
                               value:messageParagraphStyle
                               range:beforeRange];
            }
            
            lastEnd = NSMaxRange(codeRange);
        }
        
        // Apply message style to any remaining content after the last code block
        if (lastEnd < result.length) {
            NSRange afterRange = NSMakeRange(lastEnd, result.length - lastEnd);
            [result addAttribute:NSParagraphStyleAttributeName
                           value:messageParagraphStyle
                           range:afterRange];
        }
    } else {
        // No code blocks - apply message style to the entire content
        [result addAttribute:NSParagraphStyleAttributeName
                       value:messageParagraphStyle
                       range:NSMakeRange(0, result.length)];
    }
    
    return result;
}

/**
 * Process Markdown formatting in content text
 * This method now only uses CommonMark implementation for safety
 *
 * @param content The text content with Markdown syntax
 * @return NSMutableAttributedString with formatted content
 */
- (NSMutableAttributedString*)formatMarkdownContent:(NSString*)content {
    if (!content) {
        return [[NSMutableAttributedString alloc] init];
    }
    
    // Only use the cmark implementation - don't fall back to regex
    return [self formatMarkdownContentWithCMark:content];
}

/**
 * Process Markdown formatting using CommonMark (cmark) library
 *
 * @param content The text content with Markdown syntax
 * @return NSMutableAttributedString with formatted content
 */
- (NSMutableAttributedString*)formatMarkdownContentWithCMark:(NSString*)content {
    if (!content) {
        return [[NSMutableAttributedString alloc] initWithString:@""];
    }
    
    // Use streaming-aware parser for more robust handling
    return [self streamingAwareMarkdownParse:content];
}

/**
 * Format agent message with special handling
 */
- (NSMutableAttributedString*)formatAgentMessageContent:(NSString*)content {
    if (!content) {
        return nil;
    }
    
    // Format using the standard Markdown formatter
    return [self formatMarkdownContent:content];
}

/**
 * Get a friendly display name for an agent ID
 */
- (NSString*)agentNameForId:(NSString*)agentId {
    if (!agentId || [agentId length] == 0) {
        return @"Unknown";
    }
    
    if ([agentId isEqualToString:@"general"]) {
        return @"General Agent";
    } else if ([agentId hasPrefix:@"agent-"]) {
        // Format agent-some-name as "Some Name"
        NSString* nameWithoutPrefix = [agentId substringFromIndex:6]; // Skip "agent-"
        NSArray* parts = [nameWithoutPrefix componentsSeparatedByString:@"-"];
        NSMutableArray* capitalizedParts = [NSMutableArray array];
        
        for (NSString* part in parts) {
            if ([part length] > 0) {
                [capitalizedParts addObject:[part capitalizedString]];
            }
        }
        
        return [capitalizedParts componentsJoinedByString:@" "];
    }
    
    return agentId;
}

/**
 * Streaming-aware Markdown parser that handles incomplete structures gracefully
 *
 * @param content The text content with potentially incomplete Markdown syntax
 * @return NSMutableAttributedString with formatted content
 */
- (NSMutableAttributedString*)streamingAwareMarkdownParse:(NSString*)content {
    if (!content) {
        return [[NSMutableAttributedString alloc] init];
    }

    // Create result string to build up
    NSMutableAttributedString* result = [[NSMutableAttributedString alloc] init];
    
    // Set default attributes
    NSMutableParagraphStyle* paragraphStyle = [[NSMutableParagraphStyle alloc] init];
    paragraphStyle.lineSpacing = 4;
    paragraphStyle.paragraphSpacing = 8;
    
    NSDictionary* defaultAttributes = @{
        NSFontAttributeName: [NSFont systemFontOfSize:13.5],
        NSParagraphStyleAttributeName: paragraphStyle,
        NSForegroundColorAttributeName: [NSColor textColor]
    };

    // Guard against null string
    if (content == nil) {
        return result;
    }

    // Initialize state tracking array if needed
    if (!self.openMarkdownStructures) {
        self.openMarkdownStructures = [NSMutableArray array];
    }

    // IMPROVED: Save original state for recovery in case of parsing failures
    BOOL wasInsideCodeBlock = self.insideCodeBlock;
    NSString* savedCodeBlockLanguage = [self.currentCodeBlockLanguage copy];
    
    // Try to detect raw code blocks before any preprocessing
    NSArray<NSValue*>* rawCodeBlocks = [self findRawCodeBlocks:content];
    
    // Analyze for code blocks before preprocessing - enhanced with better context detection
    [self analyzeMarkdownStructures:content];
    
    // Pre-process content to handle incomplete Markdown structures
    NSString* processedContent = [self preprocessIncompleteMarkdown:content];

    // Convert NSString to C string
    const char* markdown = [processedContent UTF8String];
    if (markdown == NULL) {
        return result;
    }
    
    // Track if CommonMark parsing was successful
    BOOL parseSuccess = YES;
    
    // Parse markdown with cmark
    cmark_node* document = cmark_parse_document(markdown, strlen(markdown), CMARK_OPT_DEFAULT);
    if (!document) {
        // If parsing fails, we'll handle it below
        parseSuccess = NO;
        ERM(@"CommonMark parsing failed for content of length %lu", (unsigned long)content.length);
    }

    if (parseSuccess) {
        // Process the document tree
        cmark_node* node = cmark_node_first_child(document);
        BOOL foundCodeBlocks = NO;
        
        while (node) {
            switch (cmark_node_get_type(node)) {
                case CMARK_NODE_CODE_BLOCK:
                    // Mark that we found at least one code block during normal parsing
                    foundCodeBlocks = YES;
                    // Fall through to normal handling
                
                // Process all other node types normally
                default:
                    [self appendNode:node toString:result withDefaultAttributes:defaultAttributes];
                    break;
            }
            
            node = cmark_node_next(node);
        }

        // Free the document
        cmark_node_free(document);
        
        // If CommonMark couldn't identify code blocks but we found them, use fallback rendering
        if (!foundCodeBlocks && rawCodeBlocks.count > 0) {
            DBM(@"Using fallback rendering for %lu code blocks not detected by CommonMark", (unsigned long)rawCodeBlocks.count);
            [self renderRawCodeBlocksAsFallback:rawCodeBlocks 
                                   fromContent:content 
                                      toResult:result];
        }
    } else {
        // If parsing fails, return the plain content with default styling
        // but with special handling for code blocks we detected
        [result appendAttributedString:[[NSAttributedString alloc] initWithString:content 
                                                                   attributes:defaultAttributes]];
        
        // Try to render code blocks we detected manually
        if (rawCodeBlocks.count > 0) {
            DBM(@"Rendering %lu raw code blocks after parsing failure", (unsigned long)rawCodeBlocks.count);
            
            // Create a fresh result with the code blocks properly rendered
            NSMutableAttributedString* recoveredResult = [[NSMutableAttributedString alloc] init];
            [self renderRawCodeBlocksAsFallback:rawCodeBlocks 
                                   fromContent:content 
                                      toResult:recoveredResult];
            
            if (recoveredResult.length > 0) {
                result = recoveredResult;
            }
        }
    }
    
    // Process any remaining unhandled incomplete structures that weren't caught by CommonMark
    result = [self postProcessIncompleteStructures:result originalContent:content];
    
    // Restore original state if no code blocks were detected but we were inside one
    if (wasInsideCodeBlock && !self.insideCodeBlock && [self detectCodeBlockContinuation:content]) {
        self.insideCodeBlock = wasInsideCodeBlock;
        self.currentCodeBlockLanguage = savedCodeBlockLanguage;
        DBM(@"Restored code block state: inside=YES, language=%@", self.currentCodeBlockLanguage);
    }
    
    return result;
}

/**
 * Detect if this content appears to be a continuation of a code block
 * without any markdown fence markers
 */
- (BOOL)detectCodeBlockContinuation:(NSString*)content {
    // If this content has no fences at all and we were previously in a code block,
    // it's likely just continuing code content
    NSRange fenceRange = [content rangeOfString:@"```"];
    return (fenceRange.location == NSNotFound);
}

/**
 * Find raw code blocks in content by looking for ``` fence markers
 * Returns an array of NSValue objects containing NSRange values
 */
- (NSArray<NSValue*>*)findRawCodeBlocks:(NSString*)content {
    NSMutableArray<NSValue*>* blocks = [NSMutableArray array];
    
    // Early return for obvious cases
    if (!content || content.length < 6 || ![content containsString:@"```"]) {
        return blocks;
    }
    
    NSUInteger contentLength = content.length;
    NSUInteger searchStart = 0;
    
    while (searchStart < contentLength) {
        // Find opening fence
        NSRange openingRange = [content rangeOfString:@"```" 
                                             options:0 
                                               range:NSMakeRange(searchStart, contentLength - searchStart)];
        
        if (openingRange.location == NSNotFound) {
            break;
        }
        
        // Try to get the language
        NSUInteger languageStart = NSMaxRange(openingRange);
        NSRange newlineRange = [content rangeOfString:@"\n" 
                                             options:0 
                                               range:NSMakeRange(languageStart, contentLength - languageStart)];
        
        NSString* language = @"";
        NSUInteger codeStart;
        
        if (newlineRange.location != NSNotFound) {
            language = [content substringWithRange:NSMakeRange(languageStart, newlineRange.location - languageStart)];
            codeStart = NSMaxRange(newlineRange);
        } else {
            // No language specified or no newline after opening fence
            codeStart = NSMaxRange(openingRange);
        }
        
        // Find closing fence
        NSRange closingRange = [content rangeOfString:@"```" 
                                             options:0 
                                               range:NSMakeRange(codeStart, contentLength - codeStart)];
        
        // Calculate the code block range
        NSRange blockRange;
        if (closingRange.location != NSNotFound) {
            // Complete code block
            blockRange = NSMakeRange(openingRange.location, 
                                     NSMaxRange(closingRange) - openingRange.location);
            // Also include language info
            NSDictionary* blockInfo = @{
                @"range": [NSValue valueWithRange:blockRange],
                @"language": language,
                @"content": [content substringWithRange:NSMakeRange(codeStart, closingRange.location - codeStart)]
            };
            [blocks addObject:[NSValue valueWithNonretainedObject:blockInfo]];
            
            // Move search start past this block
            searchStart = NSMaxRange(closingRange);
        } else {
            // Incomplete code block - goes to end of content
            blockRange = NSMakeRange(openingRange.location, contentLength - openingRange.location);
            // Include language info
            NSDictionary* blockInfo = @{
                @"range": [NSValue valueWithRange:blockRange],
                @"language": language,
                @"content": [content substringWithRange:NSMakeRange(codeStart, contentLength - codeStart)]
            };
            [blocks addObject:[NSValue valueWithNonretainedObject:blockInfo]];
            
            // Nothing more to search
            break;
        }
    }
    
    return blocks;
}

/**
 * Render raw code blocks as a fallback when CommonMark fails to detect them
 */
- (void)renderRawCodeBlocksAsFallback:(NSArray<NSValue*>*)blocks 
                         fromContent:(NSString*)content 
                            toResult:(NSMutableAttributedString*)result {
    
    if (blocks.count == 0 || !content || !result) {
        return;
    }
    
    // Set default attributes for non-code text
    NSMutableParagraphStyle* paragraphStyle = [[NSMutableParagraphStyle alloc] init];
    paragraphStyle.lineSpacing = 4;
    paragraphStyle.paragraphSpacing = 8;
    
    NSDictionary* defaultAttributes = @{
        NSFontAttributeName: [NSFont systemFontOfSize:13.5],
        NSParagraphStyleAttributeName: paragraphStyle,
        NSForegroundColorAttributeName: [NSColor textColor]
    };
    
    // Process each block and the text between blocks
    NSUInteger currentPosition = 0;
    
    for (NSValue* blockValue in blocks) {
        NSDictionary* blockInfo = [blockValue nonretainedObjectValue];
        NSRange blockRange = [blockInfo[@"range"] rangeValue];
        NSString* language = blockInfo[@"language"];
        NSString* codeContent = blockInfo[@"content"];
        
        // Add text before this block
        if (blockRange.location > currentPosition) {
            NSString* textBefore = [content substringWithRange:
                                   NSMakeRange(currentPosition, blockRange.location - currentPosition)];
            [result appendAttributedString:[[NSAttributedString alloc] initWithString:textBefore
                                                                          attributes:defaultAttributes]];
        }
        
        // Add the formatted code block
        NSMutableAttributedString* formattedCode = [self formatCodeBlock:codeContent withLanguage:language];
        [result appendAttributedString:formattedCode];
        
        // Update current position
        currentPosition = NSMaxRange(blockRange);
    }
    
    // Add any remaining text after the last block
    if (currentPosition < content.length) {
        NSString* textAfter = [content substringFromIndex:currentPosition];
        [result appendAttributedString:[[NSAttributedString alloc] initWithString:textAfter
                                                                      attributes:defaultAttributes]];
    }
}

/**
 * Format a code block with syntax highlighting for common languages
 */
- (NSMutableAttributedString*)formatCodeBlock:(NSString*)code withLanguage:(NSString*)language {
    // Validate input
    if (!code) {
        ERM(@"Null code passed to formatCodeBlock");
        return [[NSMutableAttributedString alloc] initWithString:@""];
    }
    
    // Create basic code block styling
    NSMutableAttributedString* codeString = [[NSMutableAttributedString alloc] 
                                           initWithString:code];
    
    // Base styling with monospaced font
    [codeString addAttribute:NSFontAttributeName
                       value:[NSFont monospacedSystemFontOfSize:12.5 weight:NSFontWeightRegular]
                       range:NSMakeRange(0, codeString.length)];
    
    // Create a paragraph style that works with text blocks
    NSMutableParagraphStyle* codeParaStyle = [[NSMutableParagraphStyle alloc] init];
    codeParaStyle.lineSpacing = 1;
    codeParaStyle.paragraphSpacing = 1;
    // Make sure we're using the full width available
    codeParaStyle.headIndent = 0;
    codeParaStyle.firstLineHeadIndent = 0;
    codeParaStyle.tailIndent = 0;
    
    // Create a text block for perfect rectangular background
    NSTextBlock *codeBlock = [[NSTextBlock alloc] init];
    
    // Determine background color based on appearance
    NSColor* backgroundColor;
    if (@available(macOS 10.14, *)) {
        if ([NSApp.effectiveAppearance.name isEqualToString:NSAppearanceNameDarkAqua]) {
            backgroundColor = [NSColor colorWithRed:0.15 green:0.15 blue:0.15 alpha:1.0];
        } else {
            backgroundColor = [NSColor colorWithRed:0.95 green:0.95 blue:0.95 alpha:1.0];
        }
    } else {
        backgroundColor = [NSColor colorWithWhite:0.95 alpha:1.0];
    }
    
    // Configure the text block
    [codeBlock setBackgroundColor:backgroundColor];
    
    // Set width to 100% to make it span the full width
    [codeBlock setContentWidth:100 type:NSTextBlockPercentageValueType];
    
    // Set minimal margins to ensure the block extends to edges
    [codeBlock setWidth:0 type:NSTextBlockAbsoluteValueType forLayer:NSTextBlockMargin edge:NSRectEdgeMinX];
    [codeBlock setWidth:0 type:NSTextBlockAbsoluteValueType forLayer:NSTextBlockMargin edge:NSRectEdgeMaxX];
    
    // Set padding on all sides for better appearance
    CGFloat padding = 10.0;
    [codeBlock setWidth:padding type:NSTextBlockAbsoluteValueType forLayer:NSTextBlockPadding edge:NSRectEdgeMinX];
    [codeBlock setWidth:padding type:NSTextBlockAbsoluteValueType forLayer:NSTextBlockPadding edge:NSRectEdgeMaxX];
    [codeBlock setWidth:padding type:NSTextBlockAbsoluteValueType forLayer:NSTextBlockPadding edge:NSRectEdgeMinY];
    [codeBlock setWidth:padding type:NSTextBlockAbsoluteValueType forLayer:NSTextBlockPadding edge:NSRectEdgeMaxY];
    
    // Add borders to the code block
    CGFloat borderWidth = 1.0;
    [codeBlock setWidth:borderWidth type:NSTextBlockAbsoluteValueType forLayer:NSTextBlockBorder edge:NSRectEdgeMinX];
    [codeBlock setWidth:borderWidth type:NSTextBlockAbsoluteValueType forLayer:NSTextBlockBorder edge:NSRectEdgeMaxX];
    [codeBlock setWidth:borderWidth type:NSTextBlockAbsoluteValueType forLayer:NSTextBlockBorder edge:NSRectEdgeMinY];
    [codeBlock setWidth:borderWidth type:NSTextBlockAbsoluteValueType forLayer:NSTextBlockBorder edge:NSRectEdgeMaxY];
    
    // Set border color - slightly darker than the background
    NSColor *borderColor;
    if (@available(macOS 10.14, *)) {
        if ([NSApp.effectiveAppearance.name isEqualToString:NSAppearanceNameDarkAqua]) {
            borderColor = [NSColor colorWithRed:0.25 green:0.25 blue:0.25 alpha:1.0];
        } else {
            borderColor = [NSColor colorWithRed:0.8 green:0.8 blue:0.8 alpha:1.0];
        }
    } else {
        borderColor = [NSColor colorWithWhite:0.8 alpha:1.0];
    }
    
    // Apply border color to all edges
    [codeBlock setBorderColor:borderColor];
    
    // Mark this code block with a special attribute to identify it later
    // This will allow us to avoid overriding its formatting when applying message blocks
    [codeString addAttribute:@"IsCodeBlock" value:@YES range:NSMakeRange(0, codeString.length)];
    
    // Add the text block to the paragraph style
    codeParaStyle.textBlocks = @[codeBlock];
    
    // Apply paragraph style with text block to the entire string
    [codeString addAttribute:NSParagraphStyleAttributeName
                       value:codeParaStyle
                       range:NSMakeRange(0, codeString.length)];
    
    // Calculate the exact content range - account for the newlines we added
    NSRange contentRange;
    if (codeString.length >= 3) {
        contentRange = NSMakeRange(1, codeString.length - 2);
    } else {
        // Handle edge case of very short content - just use the whole string
        contentRange = NSMakeRange(0, codeString.length);
        DBM(@"Code string too short for normal range calculation, using full range: (%lu, %lu)", (unsigned long)contentRange.location, (unsigned long)contentRange.length);
    }
    
    // Validate the range before applying highlighting
    if (contentRange.location + contentRange.length <= codeString.length) {
        // Apply syntax highlighting if there's a valid content range
        [self applyHighlightingForLanguage:language toCodeString:codeString inRange:contentRange];
    } else {
        ERM(@"Invalid content range calculated: (%lu, %lu) for string length %lu", 
              (unsigned long)contentRange.location,
              (unsigned long)contentRange.length,
              (unsigned long)codeString.length);
    }
    
    return codeString;
}

/**
 * Get a compiled regex pattern from cache or create it if not found
 */
- (NSRegularExpression *)cachedRegexForPattern:(NSString *)pattern {
    // Try to get from cache first
    NSRegularExpression *regex = self.regexCache[pattern];
    
    if (!regex) {
        // Not in cache, create and store it
        NSError *error = nil;
        regex = [NSRegularExpression regularExpressionWithPattern:pattern
                                                         options:NSRegularExpressionAnchorsMatchLines
                                                           error:&error];
        if (error) {
            ERM(@"Failed to create regex for pattern %@: %@", pattern, error.localizedDescription);
            return nil;
        }
        
        // Store in cache for future use
        self.regexCache[pattern] = regex;
    }
    
    return regex;
}

/**
 * Apply syntax highlighting for a specific language using regex patterns
 */
- (void)applyHighlightingForLanguage:(NSString*)language 
                        toCodeString:(NSMutableAttributedString*)codeString
                            inRange:(NSRange)contentRange {
    // Normalize and map language identifier
    NSString* normalizedLanguage = [language lowercaseString];
    
    // Apply language alias mapping if available
    NSString* mappedLanguage = self.languageAliases[normalizedLanguage];
    if (mappedLanguage) {
        normalizedLanguage = mappedLanguage;
    }
    
    // Default colors based on current appearance
    NSColor* keywordColor;
    NSColor* stringColor;
    NSColor* commentColor;
    NSColor* functionColor;
    NSColor* numberColor;
    
    if (@available(macOS 10.14, *)) {
        if ([NSApp.effectiveAppearance.name isEqualToString:NSAppearanceNameDarkAqua]) {
            // Dark mode colors
            keywordColor = [NSColor colorWithRed:0.8 green:0.47 blue:0.8 alpha:1.0];
            stringColor = [NSColor colorWithRed:0.6 green:0.8 blue:0.47 alpha:1.0];
            commentColor = [NSColor colorWithRed:0.5 green:0.5 blue:0.5 alpha:1.0];
            functionColor = [NSColor colorWithRed:0.47 green:0.6 blue:0.8 alpha:1.0];
            numberColor = [NSColor colorWithRed:0.8 green:0.6 blue:0.47 alpha:1.0];
        } else {
            // Light mode colors
            keywordColor = [NSColor colorWithRed:0.7 green:0.0 blue:0.7 alpha:1.0];
            stringColor = [NSColor colorWithRed:0.0 green:0.6 blue:0.0 alpha:1.0];
            commentColor = [NSColor colorWithRed:0.4 green:0.4 blue:0.4 alpha:1.0];
            functionColor = [NSColor colorWithRed:0.0 green:0.0 blue:0.7 alpha:1.0];
            numberColor = [NSColor colorWithRed:0.7 green:0.4 blue:0.0 alpha:1.0];
        }
    } else {
        // Fallback for older macOS versions
        keywordColor = [NSColor purpleColor];
        stringColor = [NSColor greenColor];
        commentColor = [NSColor grayColor];
        functionColor = [NSColor blueColor];
        numberColor = [NSColor orangeColor];
    }
    
    // Number pattern for all languages
    [self highlightRegex:@"\\b\\d+(\\.\\d+)?([eE][+-]?\\d+)?\\b" 
               inString:codeString 
              withColor:numberColor 
               inRange:contentRange];
    
    // Apply language-specific highlighting
    if ([normalizedLanguage isEqualToString:@"js"] || 
        [normalizedLanguage isEqualToString:@"ts"]) {
        
        DBM(@"Applying JavaScript/TypeScript syntax highlighting");
        
        // Keywords
        [self highlightRegex:@"\\b(const|let|var|function|class|extends|implements|import|export|from|return|if|else|for|while|do|switch|case|break|continue|try|catch|finally|throw|new|this|super|typeof|instanceof|void|delete|default|async|await|yield|of|in)\\b" 
                   inString:codeString 
                  withColor:keywordColor 
                   inRange:contentRange];
        
        // Strings - double quotes, single quotes, and template literals
        [self highlightRegex:@"\"[^\"\\\\]*(\\\\.[^\"\\\\]*)*\"" 
                   inString:codeString 
                  withColor:stringColor 
                   inRange:contentRange];
        [self highlightRegex:@"'[^'\\\\]*(\\\\.[^'\\\\]*)*'" 
                   inString:codeString 
                  withColor:stringColor 
                   inRange:contentRange];
        [self highlightRegex:@"`[^`\\\\]*(\\\\.[^`\\\\]*)*`" 
                   inString:codeString 
                  withColor:stringColor 
                   inRange:contentRange];
        
        // Line comments
        [self highlightRegex:@"//.*$" 
                   inString:codeString 
                  withColor:commentColor 
                   inRange:contentRange];
        
        // Block comments
        [self highlightRegex:@"/\\*[^*]*\\*+(?:[^/*][^*]*\\*+)*/" 
                   inString:codeString 
                  withColor:commentColor 
                   inRange:contentRange];
        
        // Function declarations
        [self highlightRegex:@"\\b([a-zA-Z_$][a-zA-Z0-9_$]*)\\s*\\(" 
                   inString:codeString 
                  withColor:functionColor 
                   inRange:contentRange];
        
    } else if ([normalizedLanguage isEqualToString:@"py"]) {
        
        DBM(@"Applying Python syntax highlighting");
        
        // Keywords
        [self highlightRegex:@"\\b(def|class|if|elif|else|for|while|try|except|finally|with|as|import|from|return|raise|assert|pass|break|continue|and|or|not|is|in|lambda|None|True|False|nonlocal|global|del|yield)\\b" 
                   inString:codeString 
                  withColor:keywordColor 
                   inRange:contentRange];
        
        // Strings - triple quotes and regular quotes
        [self highlightRegex:@"\"\"\"[^\"]*\"\"\"" 
                   inString:codeString 
                  withColor:stringColor 
                   inRange:contentRange];
        [self highlightRegex:@"'''[^']*'''" 
                   inString:codeString 
                  withColor:stringColor 
                   inRange:contentRange];
        [self highlightRegex:@"\"[^\"\\\\]*(\\\\.[^\"\\\\]*)*\"" 
                   inString:codeString 
                  withColor:stringColor 
                   inRange:contentRange];
        [self highlightRegex:@"'[^'\\\\]*(\\\\.[^'\\\\]*)*'" 
                   inString:codeString 
                  withColor:stringColor 
                   inRange:contentRange];
        
        // Comments
        [self highlightRegex:@"#.*$" 
                   inString:codeString 
                  withColor:commentColor 
                   inRange:contentRange];
        
        // Function declarations
        [self highlightRegex:@"\\bdef\\s+([a-zA-Z_][a-zA-Z0-9_]*)\\s*\\(" 
                   inString:codeString 
                  withColor:functionColor 
                   inRange:contentRange];
        
        // Decorators
        [self highlightRegex:@"@[a-zA-Z_][a-zA-Z0-9_.]*" 
                   inString:codeString 
                  withColor:keywordColor 
                   inRange:contentRange];
        
    } else if ([normalizedLanguage isEqualToString:@"cpp"] ||
               [normalizedLanguage isEqualToString:@"c"]) {
        
        DBM(@"Applying C/C++ syntax highlighting");
        
        // Keywords
        [self highlightRegex:@"\\b(if|else|for|while|do|switch|case|break|continue|return|try|catch|throw|new|delete|class|struct|enum|union|typedef|template|virtual|public|private|protected|inline|static|extern|const|auto|register|volatile|operator|sizeof|namespace|using|default|void|int|char|float|double|bool|true|false)\\b" 
                   inString:codeString 
                  withColor:keywordColor 
                   inRange:contentRange];
        
        // Preprocessor directives
        [self highlightRegex:@"#\\s*(include|define|ifdef|ifndef|endif|if|else|elif|pragma|undef|line|error|warning)\\b" 
                   inString:codeString 
                  withColor:keywordColor 
                   inRange:contentRange];
        
        // Strings
        [self highlightRegex:@"\"[^\"\\\\]*(\\\\.[^\"\\\\]*)*\"" 
                   inString:codeString 
                  withColor:stringColor 
                   inRange:contentRange];
        [self highlightRegex:@"'[^'\\\\]*(\\\\.[^'\\\\]*)*'" 
                   inString:codeString 
                  withColor:stringColor 
                   inRange:contentRange];
        
        // Line comments
        [self highlightRegex:@"//.*$" 
                   inString:codeString 
                  withColor:commentColor 
                   inRange:contentRange];
        
        // Block comments
        [self highlightRegex:@"/\\*[^*]*\\*+(?:[^/*][^*]*\\*+)*/" 
                   inString:codeString 
                  withColor:commentColor 
                   inRange:contentRange];
        
        // Function declarations - match function name before parentheses
        [self highlightRegex:@"\\b([a-zA-Z_][a-zA-Z0-9_]*)\\s*\\(" 
                   inString:codeString 
                  withColor:functionColor 
                   inRange:contentRange];
        
    } else if ([normalizedLanguage isEqualToString:@"swift"]) {
        
        DBM(@"Applying Swift syntax highlighting");
        
        // Keywords
        [self highlightRegex:@"\\b(class|struct|enum|protocol|extension|func|var|let|if|else|guard|switch|case|default|for|while|do|break|continue|return|throw|try|catch|import|typealias|associatedtype|init|deinit|self|super|override|subscript|true|false|nil|as|is|where|in|static|final|open|public|internal|private|fileprivate|weak|unowned|mutating)\\b" 
                   inString:codeString 
                  withColor:keywordColor 
                   inRange:contentRange];
        
        // Strings
        [self highlightRegex:@"\"[^\"\\\\]*(\\\\.[^\"\\\\]*)*\"" 
                   inString:codeString 
                  withColor:stringColor 
                   inRange:contentRange];
        
        // Comments
        [self highlightRegex:@"//.*$" 
                   inString:codeString 
                  withColor:commentColor 
                   inRange:contentRange];
        
        // Block comments
        [self highlightRegex:@"/\\*[^*]*\\*+(?:[^/*][^*]*\\*+)*/" 
                   inString:codeString 
                  withColor:commentColor 
                   inRange:contentRange];
        
        // Function declarations
        [self highlightRegex:@"\\bfunc\\s+([a-zA-Z_][a-zA-Z0-9_]*)\\s*\\(" 
                   inString:codeString 
                  withColor:functionColor 
                   inRange:contentRange];
                   
    } else if ([normalizedLanguage isEqualToString:@"objectivec"]) {
        
        DBM(@"Applying Objective-C syntax highlighting");
        
        // Keywords
        [self highlightRegex:@"\\b(if|else|for|while|do|switch|case|break|continue|return|goto|@interface|@implementation|@protocol|@end|@property|@synthesize|@dynamic|@class|@public|@protected|@private|@try|@catch|@finally|@throw|@synchronized|self|super|nil|YES|NO|TRUE|FALSE|id|instancetype|BOOL|NSInteger|CGFloat|IBOutlet|IBAction|strong|weak|assign|unsafe_unretained|copy|readonly|readwrite|nonatomic|atomic)\\b" 
                   inString:codeString 
                  withColor:keywordColor 
                   inRange:contentRange];
        
        // Method declarations
        [self highlightRegex:@"[+-]\\s*\\([^)]*\\)\\s*([a-zA-Z_][a-zA-Z0-9_:]*)+" 
                   inString:codeString 
                  withColor:functionColor 
                   inRange:contentRange];
        
        // Strings
        [self highlightRegex:@"@\"[^\"\\\\]*(\\\\.[^\"\\\\]*)*\"" 
                   inString:codeString 
                  withColor:stringColor 
                   inRange:contentRange];
        [self highlightRegex:@"\"[^\"\\\\]*(\\\\.[^\"\\\\]*)*\"" 
                   inString:codeString 
                  withColor:stringColor 
                   inRange:contentRange];
        
        // Comments
        [self highlightRegex:@"//.*$" 
                   inString:codeString 
                  withColor:commentColor 
                   inRange:contentRange];
        [self highlightRegex:@"/\\*[^*]*\\*+(?:[^/*][^*]*\\*+)*/" 
                   inString:codeString 
                  withColor:commentColor 
                   inRange:contentRange];
                   
    } else if ([normalizedLanguage isEqualToString:@"ruby"]) {
        
        DBM(@"Applying Ruby syntax highlighting");
        
        // Keywords
        [self highlightRegex:@"\\b(alias|and|BEGIN|begin|break|case|class|def|defined|do|else|elsif|END|end|ensure|false|for|if|in|module|next|nil|not|or|redo|rescue|retry|return|self|super|then|true|undef|unless|until|when|while|yield)\\b" 
                   inString:codeString 
                  withColor:keywordColor 
                   inRange:contentRange];
        
        // Strings
        [self highlightRegex:@"\"[^\"\\\\]*(\\\\.[^\"\\\\]*)*\"" 
                   inString:codeString 
                  withColor:stringColor 
                   inRange:contentRange];
        [self highlightRegex:@"'[^'\\\\]*(\\\\.[^'\\\\]*)*'" 
                   inString:codeString 
                  withColor:stringColor 
                   inRange:contentRange];
        
        // Comments
        [self highlightRegex:@"#.*$" 
                   inString:codeString 
                  withColor:commentColor 
                   inRange:contentRange];
        
        // Function declarations
        [self highlightRegex:@"\\bdef\\s+([a-zA-Z_][a-zA-Z0-9_?!]*)\\s*" 
                   inString:codeString 
                  withColor:functionColor 
                   inRange:contentRange];
                  
    } else if ([normalizedLanguage isEqualToString:@"json"]) {
        
        DBM(@"Applying JSON syntax highlighting");
        
        // Strings (property names and values)
        [self highlightRegex:@"\"[^\"\\\\]*(\\\\.[^\"\\\\]*)*\"\\s*:" 
                   inString:codeString 
                  withColor:keywordColor 
                   inRange:contentRange];
        [self highlightRegex:@":\\s*\"[^\"\\\\]*(\\\\.[^\"\\\\]*)*\"" 
                   inString:codeString 
                  withColor:stringColor 
                   inRange:contentRange];
        
        // Numbers
        [self highlightRegex:@":\\s*[-+]?\\b\\d+(\\.\\d+)?([eE][+-]?\\d+)?\\b" 
                   inString:codeString 
                  withColor:numberColor 
                   inRange:contentRange];
        
        // Boolean values
        [self highlightRegex:@":\\s*\\b(true|false|null)\\b" 
                   inString:codeString 
                  withColor:keywordColor 
                   inRange:contentRange];
                  
    } else {
        // For unknown languages, apply basic highlighting
        // DBM(@"Applying generic syntax highlighting for unknown language: %@", normalizedLanguage);
        
        // Simple keyword detection for multiple languages
        [self highlightRegex:@"\\b(function|class|if|else|for|while|return|import|export|var|let|const)\\b" 
                   inString:codeString 
                  withColor:keywordColor 
                   inRange:contentRange];
        
        // Common string patterns
        [self highlightRegex:@"\"[^\"\\\\]*(\\\\.[^\"\\\\]*)*\"" 
                   inString:codeString 
                  withColor:stringColor 
                   inRange:contentRange];
        [self highlightRegex:@"'[^'\\\\]*(\\\\.[^'\\\\]*)*'" 
                   inString:codeString 
                  withColor:stringColor 
                   inRange:contentRange];
        
        // Common comment patterns
        [self highlightRegex:@"//.*$|#.*$" 
                   inString:codeString 
                  withColor:commentColor 
                   inRange:contentRange];
        [self highlightRegex:@"/\\*[^*]*\\*+(?:[^/*][^*]*\\*+)*/" 
                   inString:codeString 
                  withColor:commentColor 
                   inRange:contentRange];
        
        // Functions
        [self highlightRegex:@"\\b([a-zA-Z_][a-zA-Z0-9_]*)\\s*\\(" 
                   inString:codeString 
                  withColor:functionColor 
                   inRange:contentRange];
    }
}

/**
 * Apply highlighting for matched regex patterns
 */
- (void)highlightRegex:(NSString*)pattern 
             inString:(NSMutableAttributedString*)string 
            withColor:(NSColor*)color 
             inRange:(NSRange)searchRange {
    
    // Get or create regex from cache
    NSRegularExpression* regex = [self cachedRegexForPattern:pattern];
    if (!regex) {
        // If there was an error creating the regex, log has already been emitted
        return;
    }
    
    // Get the string we're working with
    NSString* plainString = string.string;
    
    // Make sure search range is valid
    if (searchRange.location + searchRange.length > plainString.length) {
        ERM(@"Invalid search range: (%lu, %lu) for string length %lu", 
               (unsigned long)searchRange.location,
               (unsigned long)searchRange.length,
               (unsigned long)plainString.length);
        return;
    }
    
    // Find matches and apply color
    NSArray<NSTextCheckingResult*>* matches = [regex matchesInString:plainString
                                                             options:0
                                                               range:searchRange];
    
    for (NSTextCheckingResult* match in matches) {
        [string addAttribute:NSForegroundColorAttributeName value:color range:match.range];
        
        // For function name highlighting, we only want to highlight the function name, not the parentheses
        if ([pattern hasSuffix:@"\\s*\\("]) {
            // If this is a function match pattern and we have a capture group
            if (match.numberOfRanges > 1) {
                NSRange functionNameRange = [match rangeAtIndex:1];
                [string addAttribute:NSForegroundColorAttributeName value:color range:functionNameRange];
            }
        }
    }
}

/**
 * Helper method to restore incomplete code blocks, especially for streaming markdown
 */
- (NSString*)restoreIncompleteCodeBlock:(NSString*)code withLanguage:(NSString*)language {
    // If the code was artificially closed (e.g., during streaming)
    if ([code hasSuffix:@"```"]) {
        // Remove the closing backticks
        code = [code substringToIndex:code.length - 3];
        DBM(@"Removed closing backticks from incomplete code block");
    }
    
    return code;
}

/**
 * Analyze markdown content to detect state like incomplete code blocks
 */
- (void)analyzeMarkdownStructures:(NSString*)content {
    // Reset any existing state if we are given an empty string
    if (!content || content.length == 0) {
        [self.openMarkdownStructures removeAllObjects];
        self.insideCodeBlock = NO;
        self.currentCodeBlockLanguage = nil;
        return;
    }
    
    // First check if we have a code block with language specified
    NSError* error = nil;
    NSRegularExpression* codeBlockStartRegex = [NSRegularExpression 
        regularExpressionWithPattern:@"```([^\\n`]*)\n" 
                             options:0 
                               error:&error];
                               
    if (!error) {
        NSArray* matches = [codeBlockStartRegex matchesInString:content 
                                                       options:0 
                                                         range:NSMakeRange(0, content.length)];
        
        // If we found opening code blocks with language specifiers
        if (matches.count > 0) {
            // Get the last one (most likely to be incomplete in streaming)
            NSTextCheckingResult* lastMatch = [matches lastObject];
            if (lastMatch.numberOfRanges > 1) {
                NSRange languageRange = [lastMatch rangeAtIndex:1];
                self.currentCodeBlockLanguage = [content substringWithRange:languageRange];
                // DBM(@"Found code block with language: %@", self.currentCodeBlockLanguage);
            }
        }
    }
    
    // Count code fences to determine if we're inside a code block
    // This is now a more sophisticated approach that considers context
    [self detectCodeBlockState:content];
    
    // Also check for other common structures like blockquotes and lists
    [self.openMarkdownStructures removeAllObjects];
    
    if ([self hasIncompleteBlockquote:content]) {
        [self.openMarkdownStructures addObject:@"blockquote"];
    }
    
    if ([self hasIncompleteListItems:content]) {
        [self.openMarkdownStructures addObject:@"list"];
    }
    
    // DBM(@"Inside code block: %@, Language: %@, Open structures: %@", 
    //       self.insideCodeBlock ? @"YES" : @"NO", 
    //       self.currentCodeBlockLanguage ?: @"none",
    //       self.openMarkdownStructures);
}

/**
 * Improved detection of code block state that considers the context
 */
- (void)detectCodeBlockState:(NSString*)content {
    // Use a state machine approach to properly track code fence transitions
    BOOL wasInsideCodeBlock = self.insideCodeBlock;
    NSMutableArray* fencePositions = [NSMutableArray array];
    
    // Find all fence positions
    NSRange searchRange = NSMakeRange(0, [content length]);
    NSRange range;
    
    while ((range = [content rangeOfString:@"```" options:0 range:searchRange]).location != NSNotFound) {
        [fencePositions addObject:@(range.location)];
        searchRange.location = NSMaxRange(range);
        searchRange.length = [content length] - searchRange.location;
        
        if (searchRange.length == 0) {
            break;
        }
    }
    
    // Easy case: no fences at all
    if (fencePositions.count == 0) {
        // State remains unchanged
        return;
    }
    
    // Sort fence positions (should already be in order, but just to be safe)
    [fencePositions sortUsingSelector:@selector(compare:)];
    
    // Now determine if we're inside a code block by simulating the fence transitions
    BOOL insideBlock = wasInsideCodeBlock;
    for (NSNumber* position in fencePositions) {
        // Check if this is a real fence or part of inline code
        NSUInteger pos = [position unsignedIntegerValue];
        
        // Only toggle state for valid fence transitions (simple heuristic)
        // A valid fence should be at the start of a line or have only whitespace before it
        BOOL isValidFence = YES;
        if (pos > 0) {
            NSRange lineStartRange = [content rangeOfString:@"\n" 
                                                    options:NSBackwardsSearch 
                                                      range:NSMakeRange(0, pos)];
            NSUInteger lineStart = (lineStartRange.location == NSNotFound) ? 0 : NSMaxRange(lineStartRange);
            
            // Check if there's only whitespace between line start and fence
            NSString* prefixText = [content substringWithRange:NSMakeRange(lineStart, pos - lineStart)];
            NSCharacterSet* nonWhitespace = [[NSCharacterSet whitespaceCharacterSet] invertedSet];
            isValidFence = ([prefixText rangeOfCharacterFromSet:nonWhitespace].location == NSNotFound);
        }
        
        // Toggle block state only for valid fences
        if (isValidFence) {
            insideBlock = !insideBlock;
            // DBM(@"Code block state toggled at position %lu to %@", 
            //          (unsigned long)pos, 
            //          insideBlock ? @"inside" : @"outside");
        } else {
            // DBM(@"Ignoring potential code fence at position %lu (not at line start)", 
            //          (unsigned long)pos);
        }
    }
    
    // Update state
    self.insideCodeBlock = insideBlock;
    // DBM(@"Final code block state: %@", self.insideCodeBlock ? @"inside" : @"outside");
    
    // If we've exited a code block, clear the language
    if (wasInsideCodeBlock && !self.insideCodeBlock) {
        self.currentCodeBlockLanguage = nil;
    }
}

/**
 * Checks if content has an incomplete code block
 */
- (BOOL)hasIncompleteCodeBlock:(NSString*)content {
    // First, check our state tracking (if we already know we're inside a code block)
    if (self.insideCodeBlock) {
        return YES;
    }
    
    // Use the more sophisticated detection
    BOOL priorState = self.insideCodeBlock;
    [self detectCodeBlockState:content];
    BOOL result = self.insideCodeBlock;
    
    // If we're just checking but don't want to update state, restore it
    self.insideCodeBlock = priorState;
    
    return result;
}

/**
 * Checks if content has incomplete list items
 */
- (BOOL)hasIncompleteListItems:(NSString*)content {
    // Check for list item at the end without a following newline
    NSError* error = nil;
    NSRegularExpression* listEndingRegex = [NSRegularExpression 
        regularExpressionWithPattern:@"(^|\\n)(\\s*[-*+]\\s+|\\s*\\d+\\.\\s+)[^\\n]+$" 
                             options:0 
                               error:&error];
    
    if (error) {
        return NO;
    }
    
    return [listEndingRegex firstMatchInString:content 
                                       options:0 
                                         range:NSMakeRange(0, content.length)] != nil;
}

/**
 * Checks if content has an incomplete blockquote
 */
- (BOOL)hasIncompleteBlockquote:(NSString*)content {
    // Check for blockquote at the end without a following newline
    NSError* error = nil;
    NSRegularExpression* blockquoteEndingRegex = [NSRegularExpression 
        regularExpressionWithPattern:@"(^|\\n)>\\s+[^\\n]+$" 
                             options:0 
                               error:&error];
    
    if (error) {
        return NO;
    }
    
    return [blockquoteEndingRegex firstMatchInString:content 
                                            options:0 
                                              range:NSMakeRange(0, content.length)] != nil;
}

/**
 * Checks if content has an incomplete table
 */
- (BOOL)hasIncompleteTable:(NSString*)content {
    // Check for a table row without a final newline or with missing cells
    NSError* error = nil;
    NSRegularExpression* tableRowRegex = [NSRegularExpression 
        regularExpressionWithPattern:@"(^|\\n)\\|[^\\n]+$" 
                             options:0 
                               error:&error];
    
    if (error) {
        return NO;
    }
    
    return [tableRowRegex firstMatchInString:content 
                                    options:0 
                                      range:NSMakeRange(0, content.length)] != nil;
}

/**
 * Checks if content has incomplete emphasis markers (* or _)
 */
- (BOOL)hasIncompleteEmphasis:(NSString*)content {
    // Count asterisks and underscores to detect unpaired ones
    NSUInteger asteriskCount = 0;
    NSUInteger underscoreCount = 0;
    
    for (NSUInteger i = 0; i < content.length; i++) {
        unichar c = [content characterAtIndex:i];
        if (c == '*') {
            asteriskCount++;
        } else if (c == '_') {
            underscoreCount++;
        }
    }
    
    // If we have an odd count of either marker, we have unmatched emphasis
    return (asteriskCount % 2) == 1 || (underscoreCount % 2) == 1;
}

/**
 * Close any incomplete emphasis markers
 */
- (NSMutableString*)closeIncompleteEmphasis:(NSString*)content {
    NSMutableString* result = [content mutableCopy];
    
    // Count asterisks and underscores to detect unpaired ones
    NSUInteger asteriskCount = 0;
    NSUInteger underscoreCount = 0;
    
    for (NSUInteger i = 0; i < result.length; i++) {
        unichar c = [result characterAtIndex:i];
        if (c == '*') {
            asteriskCount++;
        } else if (c == '_') {
            underscoreCount++;
        }
    }
    
    // Add closing markers as needed
    if ((asteriskCount % 2) == 1) {
        [result appendString:@"*"];
    }
    
    if ((underscoreCount % 2) == 1) {
        [result appendString:@"_"];
    }
    
    return result;
}

/**
 * Perform post-processing on the result to handle any incomplete structures
 * that weren't fully addressed by the CommonMark parser
 */
- (NSMutableAttributedString*)postProcessIncompleteStructures:(NSMutableAttributedString*)result 
                                             originalContent:(NSString*)originalContent {
    // Check if we need to apply any post-processing
    if (!self.insideCodeBlock && self.openMarkdownStructures.count == 0) {
        return result;
    }
    
    // Special handling for content inside code blocks
    if (self.insideCodeBlock) {
        // Apply code block formatting to trailing content if needed
        NSString* trailingContent = [originalContent stringByTrimmingCharactersInSet:[NSCharacterSet whitespaceAndNewlineCharacterSet]];
        
        // Find the last code fence in the original content
        NSRange lastFenceRange = [originalContent rangeOfString:@"```" options:NSBackwardsSearch];
        
        // If we have content after the last code fence, format it as continuing the code block
        if (lastFenceRange.location != NSNotFound && lastFenceRange.location + 3 < originalContent.length) {
            NSString* contentAfterFence = [originalContent substringFromIndex:lastFenceRange.location + 3];
            contentAfterFence = [contentAfterFence stringByTrimmingCharactersInSet:[NSCharacterSet whitespaceAndNewlineCharacterSet]];
            
            if (contentAfterFence.length > 0) {
                // Format this content as part of the code block
                NSMutableAttributedString* codeString = [self formatCodeBlock:contentAfterFence 
                                                                 withLanguage:self.currentCodeBlockLanguage];
                [result appendAttributedString:codeString];
            }
        }
    }
    
    // Special handling for other incomplete structures could be added here
    
    return result;
}

/**
 * Determine if incremental parsing is appropriate for the current content
 */
- (BOOL)shouldUseIncrementalParse:(NSString*)newContent originalLength:(NSUInteger)originalLength {
    // Always use full parse for small total content
    if (originalLength < 500) {
        return NO;
    }
    
    // Check if new content might break existing Markdown structures
    NSString *chunkSignature = [self computeMarkdownSignatureForChunk:newContent];
    BOOL structureBreaking = [self isStructureBreakingChunk:chunkSignature previousSignature:self.lastChunkSignature];
    self.lastChunkSignature = chunkSignature;
    
    // If it potentially breaks structures, perform full parse
    if (structureBreaking) {
        return NO;
    }
    
    // Otherwise, use incremental parse for better performance
    return YES;
}

/**
 * Compute a signature for a chunk that represents its Markdown structure
 */
- (NSString*)computeMarkdownSignatureForChunk:(NSString*)chunk {
    NSMutableString *signature = [NSMutableString string];
    
    // Check for key Markdown elements
    if ([chunk containsString:@"```"]) {
        [signature appendString:@"C"]; // Code block
    }
    
    if ([chunk containsString:@"*"] || [chunk containsString:@"_"]) {
        [signature appendString:@"E"]; // Emphasis
    }
    
    if ([chunk containsString:@"["] || [chunk containsString:@"]"]) {
        [signature appendString:@"L"]; // Link
    }
    
    if ([chunk rangeOfString:@"^\\s*[-*+]\\s" options:NSRegularExpressionSearch].location != NSNotFound) {
        [signature appendString:@"U"]; // Unordered list
    }
    
    if ([chunk rangeOfString:@"^\\s*\\d+\\.\\s" options:NSRegularExpressionSearch].location != NSNotFound) {
        [signature appendString:@"O"]; // Ordered list
    }
    
    if ([chunk containsString:@"|"]) {
        [signature appendString:@"T"]; // Table
    }
    
    if ([chunk containsString:@">"]) {
        [signature appendString:@"B"]; // Blockquote
    }
    
    if ([signature length] == 0) {
        [signature appendString:@"P"]; // Plain text
    }
    
    return signature;
}

/**
 * Check if the new chunk might break existing Markdown structures
 */
- (BOOL)isStructureBreakingChunk:(NSString*)newSignature previousSignature:(NSString*)prevSignature {
    // Different structure types completely -> potential break
    if (![newSignature isEqualToString:prevSignature] && [prevSignature length] > 0) {
        // Special case: If we're just adding plain text to existing structures, that's safe
        if ([newSignature isEqualToString:@"P"]) {
            return NO;
        }
        return YES;
    }
    
    // Code blocks are especially prone to structure issues
    if ([newSignature containsString:@"C"]) {
        return YES;
    }
    
    // By default, assume it's safe
    return NO;
}

/**
 * Recursively append a cmark node and its children to an NSMutableAttributedString
 * Enhanced with better error handling and defensive coding
 */
- (void)appendNode:(cmark_node*)node 
         toString:(NSMutableAttributedString*)string 
withDefaultAttributes:(NSDictionary*)attributes {
    
    if (!node || !string) {
        return;
    }
    
    switch (cmark_node_get_type(node)) {
        case CMARK_NODE_DOCUMENT:
            // Process all children of the document
            {
                cmark_node* child = cmark_node_first_child(node);
                while (child) {
                    [self appendNode:child toString:string withDefaultAttributes:attributes];
                    child = cmark_node_next(child);
                }
            }
            break;
            
        case CMARK_NODE_BLOCK_QUOTE: {
            // Create padding for the blockquote
            NSMutableParagraphStyle* quoteStyle = [[NSMutableParagraphStyle alloc] init];
            quoteStyle.lineSpacing = 4;
            quoteStyle.paragraphSpacing = 8;
            quoteStyle.headIndent = 20.0;
            quoteStyle.firstLineHeadIndent = 20.0;
            
            // Process all children recursively
            cmark_node* child = cmark_node_first_child(node);
            NSMutableAttributedString* quoteContent = [[NSMutableAttributedString alloc] init];
            
            while (child) {
                [self appendNode:child toString:quoteContent withDefaultAttributes:attributes];
                child = cmark_node_next(child);
            }
            
            // Apply blockquote styling
            [quoteContent addAttribute:NSParagraphStyleAttributeName 
                                 value:quoteStyle 
                                 range:NSMakeRange(0, quoteContent.length)];
            [quoteContent addAttribute:NSForegroundColorAttributeName
                                 value:[NSColor secondaryLabelColor]
                                 range:NSMakeRange(0, quoteContent.length)];
            
            [string appendAttributedString:quoteContent];
            [string appendAttributedString:[[NSAttributedString alloc] initWithString:@"\n"]];
            break;
        }
            
        case CMARK_NODE_LIST: {
            // Process the list by handling each item
            cmark_node* item = cmark_node_first_child(node);
            int listIndex = 1;
            BOOL isOrdered = (cmark_node_get_list_type(node) == CMARK_ORDERED_LIST);
            
            while (item) {
                // Create bullet or number prefix based on list type
                NSString* prefix;
                if (isOrdered) {
                    prefix = [NSString stringWithFormat:@"%d. ", listIndex++];
                } else {
                    prefix = @"• ";
                }
                
                // Add the prefix
                NSAttributedString* prefixAttr = [[NSAttributedString alloc] 
                                                initWithString:prefix 
                                                attributes:attributes];
                [string appendAttributedString:prefixAttr];
                
                // Process item content
                cmark_node* child = cmark_node_first_child(item);
                while (child) {
                    [self appendNode:child toString:string withDefaultAttributes:attributes];
                    child = cmark_node_next(child);
                }
                
                // Move to next item
                item = cmark_node_next(item);
                if (item) {
                    [string appendAttributedString:[[NSAttributedString alloc] initWithString:@"\n"]];
                }
            }
            
            // Add extra space after list
            [string appendAttributedString:[[NSAttributedString alloc] initWithString:@"\n"]];
            break;
        }
            
        case CMARK_NODE_CODE: {
            // Inline code
            const char* content = cmark_node_get_literal(node);
            if (content) {
                NSString* code = [NSString stringWithUTF8String:content];
                
                // Early return for empty code
                if (!code || code.length == 0) {
                    break;
                }
                
                NSFont* codeFont = [NSFont monospacedSystemFontOfSize:12.5 weight:NSFontWeightRegular];
                
                // Get the text color with slight adjustment for inline code
                NSColor* codeColor;
                if (@available(macOS 10.14, *)) {
                    if ([NSApp.effectiveAppearance.name isEqualToString:NSAppearanceNameDarkAqua]) {
                        codeColor = [NSColor colorWithRed:0.8 green:0.8 blue:0.6 alpha:1.0];
                    } else {
                        codeColor = [NSColor colorWithRed:0.2 green:0.4 blue:0.6 alpha:1.0];
                    }
                } else {
                    codeColor = [NSColor colorWithRed:0.2 green:0.4 blue:0.6 alpha:1.0];
                }
                
                // Get a background color for inline code
                NSColor* codeBgColor;
                if (@available(macOS 10.14, *)) {
                    if ([NSApp.effectiveAppearance.name isEqualToString:NSAppearanceNameDarkAqua]) {
                        codeBgColor = [NSColor colorWithWhite:0.2 alpha:1.0];
                    } else {
                        codeBgColor = [NSColor colorWithWhite:0.92 alpha:1.0];
                    }
                } else {
                    codeBgColor = [NSColor colorWithWhite:0.92 alpha:1.0];
                }
                
                NSMutableAttributedString* codeString = [[NSMutableAttributedString alloc] 
                                                      initWithString:code];
                
                // Set attributes with defensive range check
                NSRange fullRange = NSMakeRange(0, codeString.length);
                [codeString addAttribute:NSFontAttributeName value:codeFont range:fullRange];
                [codeString addAttribute:NSForegroundColorAttributeName value:codeColor range:fullRange];
                [codeString addAttribute:NSBackgroundColorAttributeName value:codeBgColor range:fullRange];
                
                [string appendAttributedString:codeString];
            }
            break;
        }
            
        case CMARK_NODE_CODE_BLOCK: {
            // Get the literal text from the code block
            const char* codeText = cmark_node_get_literal(node);
            if (codeText) {
                NSString* code = [NSString stringWithUTF8String:codeText];
                
                // Get the language info if available
                const char* info = cmark_node_get_fence_info(node);
                NSString* language = info ? [NSString stringWithUTF8String:info] : @"";
                
                // Process the code block
                NSString* finalCode = [self restoreIncompleteCodeBlock:code withLanguage:language];
                
                // Format the code block with syntax highlighting
                NSMutableAttributedString* formattedCode = [self formatCodeBlock:finalCode withLanguage:language];
                [string appendAttributedString:formattedCode];
                
                // Update state for incremental parsing
                if ([self hasIncompleteCodeBlock:code]) {
                    self.insideCodeBlock = YES;
                    self.currentCodeBlockLanguage = language;
                    DBM(@"Still inside an incomplete code block with language: %@", language);
                } else {
                    self.insideCodeBlock = NO;
                    self.currentCodeBlockLanguage = nil;
                    // DBM(@"Completed code block");
                }
            } else {
                // Empty code block case
                DBM(@"Empty code block detected, applying fallback rendering");
                NSMutableAttributedString* emptyBlock = [self formatCodeBlock:@"" withLanguage:@""];
                [string appendAttributedString:emptyBlock];
            }
            break;
        }
            
        case CMARK_NODE_PARAGRAPH: {
            // Process paragraph with all its inline elements
            NSMutableAttributedString* paragraph = [[NSMutableAttributedString alloc] init];
            cmark_node* child = cmark_node_first_child(node);
            
            while (child) {
                [self appendNode:child toString:paragraph withDefaultAttributes:attributes];
                child = cmark_node_next(child);
            }
            
            [string appendAttributedString:paragraph];
            [string appendAttributedString:[[NSAttributedString alloc] initWithString:@"\n"]];
            break;
        }
            
        case CMARK_NODE_HEADING: {
            // Get heading level and content
            int level = cmark_node_get_heading_level(node);
            cmark_node* child = cmark_node_first_child(node);
            
            // Create the heading text
            NSMutableAttributedString* heading = [[NSMutableAttributedString alloc] init];
            while (child) {
                [self appendNode:child toString:heading withDefaultAttributes:attributes];
                child = cmark_node_next(child);
            }
            
            // Calculate font size based on heading level (h1 is biggest)
            CGFloat fontSize = 18.0 - ((level - 1) * 2.0);
            
            // Apply heading styling
            [heading addAttribute:NSFontAttributeName
                           value:[NSFont boldSystemFontOfSize:fontSize]
                           range:NSMakeRange(0, heading.length)];
            
            [string appendAttributedString:heading];
            [string appendAttributedString:[[NSAttributedString alloc] initWithString:@"\n"]];
            break;
        }
            
        case CMARK_NODE_THEMATIC_BREAK: {
            // Add a horizontal rule (simple line)
            NSMutableAttributedString* hr = [[NSMutableAttributedString alloc] 
                                          initWithString:@"----------------------------------------\n"];
            [hr addAttribute:NSForegroundColorAttributeName
                       value:[NSColor separatorColor]
                       range:NSMakeRange(0, hr.length)];
            [string appendAttributedString:hr];
            break;
        }
            
        case CMARK_NODE_TEXT: {
            const char* content = cmark_node_get_literal(node);
            if (content) {
                NSString* text = [NSString stringWithUTF8String:content];
                NSAttributedString* attrText = [[NSAttributedString alloc] 
                                              initWithString:text 
                                              attributes:attributes];
                [string appendAttributedString:attrText];
            }
            break;
        }
            
        case CMARK_NODE_SOFTBREAK: {
            // Soft break is typically a newline in the source that shouldn't create a new paragraph
            [string appendAttributedString:[[NSAttributedString alloc] 
                                         initWithString:@" " 
                                         attributes:attributes]];
            break;
        }
            
        case CMARK_NODE_LINEBREAK: {
            // Hard break should create a new line
            [string appendAttributedString:[[NSAttributedString alloc] 
                                         initWithString:@"\n" 
                                         attributes:attributes]];
            break;
        }
            
        case CMARK_NODE_EMPH: {
            // Process emphasized text (italic)
            NSMutableAttributedString* emphText = [[NSMutableAttributedString alloc] init];
            cmark_node* child = cmark_node_first_child(node);
            
            while (child) {
                [self appendNode:child toString:emphText withDefaultAttributes:attributes];
                child = cmark_node_next(child);
            }
            
            // Create italic font
            NSFont* currentFont = attributes[NSFontAttributeName] ?: [NSFont systemFontOfSize:13.5];
            NSFont* italicFont = [[NSFontManager sharedFontManager] convertFont:currentFont 
                                                                    toHaveTrait:NSItalicFontMask];
            
            // Apply italic styling
            [emphText addAttribute:NSFontAttributeName value:italicFont range:NSMakeRange(0, emphText.length)];
            [string appendAttributedString:emphText];
            break;
        }
            
        case CMARK_NODE_STRONG: {
            // Process strong text (bold)
            NSMutableAttributedString* strongText = [[NSMutableAttributedString alloc] init];
            cmark_node* child = cmark_node_first_child(node);
            
            while (child) {
                [self appendNode:child toString:strongText withDefaultAttributes:attributes];
                child = cmark_node_next(child);
            }
            
            // Create bold font
            NSFont* currentFont = attributes[NSFontAttributeName] ?: [NSFont systemFontOfSize:13.5];
            NSFont* boldFont = [[NSFontManager sharedFontManager] convertFont:currentFont 
                                                                  toHaveTrait:NSBoldFontMask];
            
            // Apply bold styling
            [strongText addAttribute:NSFontAttributeName value:boldFont range:NSMakeRange(0, strongText.length)];
            [string appendAttributedString:strongText];
            break;
        }
            
        case CMARK_NODE_LINK: {
            // Process links
            NSMutableAttributedString* linkText = [[NSMutableAttributedString alloc] init];
            cmark_node* child = cmark_node_first_child(node);
            
            while (child) {
                [self appendNode:child toString:linkText withDefaultAttributes:attributes];
                child = cmark_node_next(child);
            }
            
            // Get the URL
            const char* url = cmark_node_get_url(node);
            if (url) {
                NSURL* linkURL = [NSURL URLWithString:[NSString stringWithUTF8String:url]];
                if (linkURL) {
                    // Apply link styling
                    [linkText addAttribute:NSLinkAttributeName value:linkURL range:NSMakeRange(0, linkText.length)];
                    [linkText addAttribute:NSForegroundColorAttributeName 
                                     value:[NSColor linkColor] 
                                     range:NSMakeRange(0, linkText.length)];
                    [linkText addAttribute:NSUnderlineStyleAttributeName 
                                     value:@(NSUnderlineStyleSingle) 
                                     range:NSMakeRange(0, linkText.length)];
                }
            }
            
            [string appendAttributedString:linkText];
            break;
        }
            
        case CMARK_NODE_IMAGE: {
            // For simplicity, we'll just show the alt text for images
            const char* alt = cmark_node_get_title(node);
            NSString* altText = alt ? [NSString stringWithFormat:@"[Image: %s]", alt] : @"[Image]";
            
            NSMutableAttributedString* imageText = [[NSMutableAttributedString alloc] 
                                                 initWithString:altText 
                                                 attributes:attributes];
            
            // Apply styling to indicate it's an image
            [imageText addAttribute:NSForegroundColorAttributeName 
                              value:[NSColor secondaryLabelColor] 
                              range:NSMakeRange(0, imageText.length)];
            
            [string appendAttributedString:imageText];
            break;
        }
            
        default:
            // For any other node types, process their children
            cmark_node* child = cmark_node_first_child(node);
            while (child) {
                [self appendNode:child toString:string withDefaultAttributes:attributes];
                child = cmark_node_next(child);
            }
            break;
    }
}

/**
 * Preprocesses content to handle incomplete Markdown structures
 * by temporarily closing them to make the CommonMark parser happy
 *
 * @param content The potentially incomplete Markdown content
 * @return NSString with preprocessed content
 */
- (NSString*)preprocessIncompleteMarkdown:(NSString*)content {
    if (!content) {
        return @"";
    }
    
    NSMutableString* processed = [content mutableCopy];
    
    // Track modifications made
    BOOL modified = NO;
    
    // Handle incomplete code blocks using our tracked state
    if (self.insideCodeBlock) {
        // Ensure there's at least one newline before the closing fence
        unichar lastChar = [processed characterAtIndex:(processed.length - 1)];
        if (lastChar != '\n') {
            [processed appendString:@"\n"];
        }
        
        [processed appendString:@"```"];
        modified = YES;
        DBM(@"Added closing fence for code block (state-tracked)");
    } 
    // As a fallback, also check using the improved detection method
    else if ([self hasIncompleteCodeBlock:processed]) {
        unichar lastChar = [processed characterAtIndex:(processed.length - 1)];
        if (lastChar != '\n') {
            [processed appendString:@"\n"];
        }
        
        [processed appendString:@"```"];
        modified = YES;
        DBM(@"Added closing fence for code block (sophisticated detection)");
    }
    
    // Handle incomplete links [text](url
    NSError* error = nil;
    NSRegularExpression* incompleteLinkRegex = [NSRegularExpression 
        regularExpressionWithPattern:@"\\[([^\\]]+)\\]\\([^\\)]*$" 
                             options:0 
                               error:&error];
    
    if (!error) {
        NSArray* linkMatches = [incompleteLinkRegex matchesInString:processed 
                                                           options:0 
                                                             range:NSMakeRange(0, processed.length)];
        if (linkMatches.count > 0) {
            // Close incomplete links with a placeholder
            for (NSTextCheckingResult* match in [linkMatches reverseObjectEnumerator]) {
                [processed replaceCharactersInRange:match.range 
                                         withString:[NSString stringWithFormat:@"[%@](placeholder)", 
                                                    [processed substringWithRange:[match rangeAtIndex:1]]]];
            }
            modified = YES;
            DBM(@"Closed %lu incomplete links", (unsigned long)linkMatches.count);
        }
    }
    
    // Handle incomplete list items
    if ([self hasIncompleteListItems:processed]) {
        // Append an extra newline to properly terminate any list
        [processed appendString:@"\n\n"];
        modified = YES;
        DBM(@"Added newlines to terminate list");
    }
    
    // Handle incomplete blockquotes (lines starting with >)
    if ([self hasIncompleteBlockquote:processed]) {
        [processed appendString:@"\n\n"];
        modified = YES;
        DBM(@"Added newlines to terminate blockquote");
    }
    
    // Handle incomplete tables
    if ([self hasIncompleteTable:processed]) {
        // Add a closing row to incomplete tables
        [processed appendString:@"\n| | |"];
        modified = YES;
        DBM(@"Added closing row to incomplete table");
    }
    
    // Handle incomplete emphasis or strong text
    if ([self hasIncompleteEmphasis:processed]) {
        processed = [self closeIncompleteEmphasis:processed];
        modified = YES;
        DBM(@"Closed incomplete emphasis");
    }
    
    if (modified) {
        DBM(@"Made modifications to handle incomplete structures");
    }
    
    return processed;
}

- (void)dealloc {
    // Clear the regex cache to free memory
    [self.regexCache removeAllObjects];
    self.regexCache = nil;
    
    // Clear other resources as needed
    self.formattedChunksCache = nil;
    self.openMarkdownStructures = nil;
    self.currentCodeBlockLanguage = nil;
    self.lastChunkSignature = nil;
    
    // No need for explicit dealloc in ARC
}

@end 
