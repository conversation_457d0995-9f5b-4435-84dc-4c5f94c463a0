#import <Foundation/Foundation.h>
#include "../../core/include/chat_backend_interface.h" // To get BackendErrorCode

NS_ASSUME_NONNULL_BEGIN

/**
 * @brief Represents an error originating from the backend C++ layer.
 */
@interface BackendError : NSObject

@property (nonatomic, assign, readonly) launcher::core::BackendErrorCode code;
@property (nonatomic, strong, readonly) NSString *message;
@property (nonatomic, strong, nullable, readonly) NSError *underlyingError;

+ (instancetype)errorWithCode:(launcher::core::BackendErrorCode)code
                      message:(NSString *)message
              underlyingError:(nullable NSError *)underlyingError;

- (instancetype)initWithCode:(launcher::core::BackendErrorCode)code
                     message:(NSString *)message
             underlyingError:(nullable NSError *)underlyingError NS_DESIGNATED_INITIALIZER;

- (instancetype)init NS_UNAVAILABLE;
+ (instancetype)new NS_UNAVAILABLE;

@end

NS_ASSUME_NONNULL_END 