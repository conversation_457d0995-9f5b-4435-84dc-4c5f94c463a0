#import "runtime_plugin_cell_model.h"
#import <AppKit/AppKit.h>
#include "../../core/plugins/runtime_manager_service.h"
#import "../macos/injected_services.h"

#ifdef __OBJC__

using launcher::core::security::Mask128;
using launcher::core::plugins::PluginDescriptor;

@implementation RuntimePluginCellModel {
    NSString *_runtimeId;
    NSString *_version;
    NSString *_capabilitiesHex;
    BOOL _enabled;
}

- (instancetype)initWithRuntimeId:(const std::string &)rid
                           version:(const std::string &)ver
                    capabilityMask:(const Mask128 &)mask
                            enabled:(BOOL)enabled {
    self = [super init];
    if (self) {
        _runtimeId = [NSString stringWithUTF8String:rid.c_str()];
        _version   = [NSString stringWithUTF8String:ver.c_str()];
        _capabilitiesHex = [NSString stringWithUTF8String:mask.toStdString().c_str()];
        _enabled = enabled;
    }
    return self;
}

- (NSString *)runtimeId { return _runtimeId; }
- (NSString *)version   { return _version; }
- (NSString *)capabilitiesHex { return _capabilitiesHex; }

@end

// ------------------------ Factory helpers ----------------------------------

@implementation RuntimePluginCellModel (Factory)

+ (NSArray<RuntimePluginCellModel *> *)modelsFromDescriptors:
    (const std::vector<PluginDescriptor> &)descs {
    NSMutableArray<RuntimePluginCellModel *> *out = [NSMutableArray arrayWithCapacity:descs.size()];
    for (const auto &d : descs) {
        Mask128 m;
        static_assert(sizeof(m) <= sizeof(d.info.capabilities), "Capability mask copy overflow");
        std::memcpy(&m, d.info.capabilities, sizeof(m));

        std::string idStr(d.info.id);
        std::string ver = std::to_string(d.info.abi_major) + "." + std::to_string(d.info.abi_minor);

        // Determine enabled state via ConfigService key plugins.disabled.<id>
        BOOL enabled = YES;
        #ifdef __OBJC__
        {
            auto& cfg = launcher::ui::ConfigService();
            std::string key = std::string("plugins.disabled.") + idStr;
            bool disabled = cfg.getBool(key, false);
            enabled = disabled ? NO : YES;
        }
        #endif

        RuntimePluginCellModel *model = [[RuntimePluginCellModel alloc] initWithRuntimeId:idStr
                                                                                version:ver
                                                                         capabilityMask:m
                                                                                enabled:enabled];
        [out addObject:model];
    }
    return out;
}

@end

#endif /* __OBJC__ */ 