#import "BackendError.h"

@implementation BackendError

+ (instancetype)errorWithCode:(launcher::core::BackendErrorCode)code
                      message:(NSString *)message
              underlyingError:(nullable NSError *)underlyingError {
    return [[self alloc] initWithCode:code message:message underlyingError:underlyingError];
}

- (instancetype)initWithCode:(launcher::core::BackendErrorCode)code
                     message:(NSString *)message
             underlyingError:(nullable NSError *)underlyingError {
    self = [super init];
    if (self) {
        _code = code;
        _message = [message copy];
        _underlyingError = underlyingError;
    }
    return self;
}

@end 