#pragma once

#ifdef __OBJC__

#import <Foundation/Foundation.h>
#include <string>
#include "../../core/security/mask128.hh"

NS_ASSUME_NONNULL_BEGIN

/**
 * RuntimePluginCellModel bridges a core Slice-2 PluginDescriptor snapshot into
 * an immutable, ARC-managed object that is ready for AppKit consumption.  All
 * expensive formatting work (std::string → NSString, capability hex) happens
 * exactly once during initialisation so NSTableView can reuse the instance
 * without further allocations.
 */
@interface RuntimePluginCellModel : NSObject

@property(nonatomic, readonly) NSString *runtimeId;        ///< Plugin identifier
@property(nonatomic, readonly) NSString *version;          ///< "major.minor" ABI string
@property(nonatomic, readonly) NSString *capabilitiesHex;  ///< 32-char lowercase hex mask
@property(nonatomic, readonly) BOOL enabled; ///< YES if plugin enabled in config

- (instancetype)initWithRuntimeId:(const std::string &)rid
                           version:(const std::string &)ver
                    capabilityMask:(const launcher::core::security::Mask128 &)mask
                            enabled:(BOOL)enabled NS_DESIGNATED_INITIALIZER;
- (instancetype)init NS_UNAVAILABLE;

@end

// ---------------------------------------------------------------------------
// Factory helpers (Objective-C category) to convert a vector of descriptors
// into an NSArray for bulk table reloads.  Lives in the .mm implementation.
// ---------------------------------------------------------------------------

namespace launcher {
namespace core {
namespace plugins { struct PluginDescriptor; }
} // namespace core
} // namespace launcher

@interface RuntimePluginCellModel (Factory)
+ (NSArray<RuntimePluginCellModel *> *)modelsFromDescriptors:
    (const std::vector<launcher::core::plugins::PluginDescriptor> &)descs;
@end

NS_ASSUME_NONNULL_END

#endif /* __OBJC__ */ 