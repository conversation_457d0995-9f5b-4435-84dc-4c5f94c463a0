#import "MessageView.h"
#import <QuartzCore/QuartzCore.h>
#import "ModelAppearanceFactory.h"
#include "../../core/util/debug.h"

// Constants for layout
static const CGFloat kAvatarSize = 36.0;
static const CGFloat kHorizontalPadding = 12.0;
static const CGFloat kVerticalPadding = 8.0;
static const CGFloat kAvatarMargin = 10.0;
static const CGFloat kNameHeight = 20.0;
static const CGFloat kTimestampHeight = 16.0;
static const CGFloat kContentTopMargin = 4.0;
static const CGFloat kContentPadding = 10.0; // Add consistent padding definition

// Notification for propagating model accent color to window chrome
static NSString* const kModelAccentColorNotification = @"ChatModelAccentColorChanged";

// Text for typing indicator
static NSString* const kTypingIndicatorText = @"Thinking...";

@implementation MessageView {
    NSTrackingArea* _trackingArea;
    CGFloat _cachedHeight;
    BOOL _needsLayout;
    NSLayoutConstraint* _containerTopConstraint; // Track container top constraint
    NSTimer* _animationTimer; // For typing indicator animation
    NSMutableArray* _animatedDots; // To track animated dots
    CALayer* _indicatorContainerLayer; // Container for animated dots
    NSUInteger _animationFrame; // Current animation frame
}

#pragma mark - Initialization & Setup

- (instancetype)initWithMessage:(Message*)message frame:(NSRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        _message = message;
        _preferredWidth = frame.size.width > 0 ? frame.size.width : 300.0; // Safe default
        _needsLayout = YES;
        _cachedHeight = 0.0;
        
        [self setupViews];
        [self setupConstraints];
        [self updateContent];
        [self setupTrackingArea];
    }
    return self;
}

- (void)setupViews {
    // DBM("setupViews", @"setupViews", @"Setting up message view");
    
    // Create container view with rounded corners
    self.containerView = [[NSView alloc] initWithFrame:NSZeroRect];
    self.containerView.wantsLayer = YES;
    self.containerView.layer.cornerRadius = 8.0;
    self.containerView.layer.masksToBounds = NO;
    [self addSubview:self.containerView];
    
    // Set up Auto Layout
    self.containerView.translatesAutoresizingMaskIntoConstraints = NO;
    
    // Avatar view
    self.avatarView = [[NSImageView alloc] initWithFrame:NSZeroRect];
    self.avatarView.wantsLayer = YES;
    self.avatarView.layer.cornerRadius = kAvatarSize / 2;
    self.avatarView.layer.masksToBounds = YES;
    self.avatarView.imageScaling = NSImageScaleProportionallyUpOrDown;
    [self addSubview:self.avatarView];
    self.avatarView.translatesAutoresizingMaskIntoConstraints = NO;
    
    // Name label
    self.nameLabel = [[NSTextField alloc] initWithFrame:NSZeroRect];
    self.nameLabel.bezeled = NO;
    self.nameLabel.drawsBackground = NO;
    self.nameLabel.editable = NO;
    self.nameLabel.selectable = NO;
    self.nameLabel.font = [NSFont boldSystemFontOfSize:13.0];
    [self addSubview:self.nameLabel];
    self.nameLabel.translatesAutoresizingMaskIntoConstraints = NO;
    
    // Timestamp label
    self.timestampLabel = [[NSTextField alloc] initWithFrame:NSZeroRect];
    self.timestampLabel.bezeled = NO;
    self.timestampLabel.drawsBackground = NO;
    self.timestampLabel.editable = NO;
    self.timestampLabel.selectable = NO;
    self.timestampLabel.font = [NSFont systemFontOfSize:11.0];
    self.timestampLabel.textColor = [NSColor grayColor];
    [self addSubview:self.timestampLabel];
    self.timestampLabel.translatesAutoresizingMaskIntoConstraints = NO;
    
    // Text view for rendering message content with enhanced capabilities
    self.textView = [[NSTextView alloc] initWithFrame:NSZeroRect];
    self.textView.editable = NO;
    self.textView.selectable = YES;
    self.textView.drawsBackground = NO;
    self.textView.textContainerInset = NSMakeSize(0, 0);
    self.textView.alignment = NSTextAlignmentLeft;
    self.textView.verticallyResizable = YES;
    self.textView.horizontallyResizable = NO;
    self.textView.autoresizingMask = NSViewWidthSizable;
    
    // Ensure the text view can become first responder to handle cmd+c/x
    self.textView.allowsUndo = YES;
    self.textView.richText = YES;
    self.textView.importsGraphics = YES;
    self.textView.allowsDocumentBackgroundColorChange = NO;
    
    // Configure better selection behavior
    self.textView.usesFindBar = YES;
    self.textView.usesInspectorBar = NO;
    self.textView.usesRuler = NO;
    
    // Configure copy/paste menu behavior
    self.textView.enabledTextCheckingTypes = 0; // Disable spelling, etc. for better performance
    self.textView.automaticQuoteSubstitutionEnabled = NO; // Disable smart quotes for copied code
    
    // Enable rich link handling
    self.textView.automaticLinkDetectionEnabled = YES;
    self.textView.linkTextAttributes = @{
        NSForegroundColorAttributeName: [NSColor blueColor],
        NSUnderlineStyleAttributeName: @(NSUnderlineStyleSingle)
    };
    
    // Enable smart quote and dash substitution
    self.textView.smartInsertDeleteEnabled = YES;
    self.textView.automaticDashSubstitutionEnabled = YES;
    
    // Enable handling of attachments for images and rendered items
    self.textView.allowsImageEditing = NO;
    
    [self.containerView addSubview:self.textView];
    self.textView.translatesAutoresizingMaskIntoConstraints = NO;
    
    // Initialize animation-related members
    _animatedDots = [NSMutableArray array];
    _animationFrame = 0;
}

- (void)setupConstraints {
    // Safety check – avoid adding constraints more than once
    if (_containerTopConstraint) return;
    
    // Avatar constraints
    [NSLayoutConstraint activateConstraints:@[
        [self.avatarView.topAnchor constraintEqualToAnchor:self.topAnchor constant:kVerticalPadding],
        [self.avatarView.leadingAnchor constraintEqualToAnchor:self.leadingAnchor constant:kHorizontalPadding],
        [self.avatarView.widthAnchor constraintEqualToConstant:kAvatarSize],
        [self.avatarView.heightAnchor constraintEqualToConstant:kAvatarSize]
    ]];
    
    // Name label constraints - ensure it doesn't have zero width
    [NSLayoutConstraint activateConstraints:@[
        [self.nameLabel.topAnchor constraintEqualToAnchor:self.topAnchor constant:kVerticalPadding],
        [self.nameLabel.leadingAnchor constraintEqualToAnchor:self.avatarView.trailingAnchor constant:kAvatarMargin],
        [self.nameLabel.heightAnchor constraintEqualToConstant:kNameHeight]
    ]];
    
    // Timestamp label constraints
    [NSLayoutConstraint activateConstraints:@[
        [self.timestampLabel.topAnchor constraintEqualToAnchor:self.topAnchor constant:kVerticalPadding],
        [self.timestampLabel.trailingAnchor constraintEqualToAnchor:self.trailingAnchor constant:-kHorizontalPadding],
        [self.timestampLabel.heightAnchor constraintEqualToConstant:kTimestampHeight],
        [self.timestampLabel.leadingAnchor constraintEqualToAnchor:self.nameLabel.trailingAnchor constant:8.0]
    ]];
    
    // Container view constraints - store the top constraint for stable positioning
    _containerTopConstraint = [self.containerView.topAnchor constraintEqualToAnchor:self.nameLabel.bottomAnchor constant:kContentTopMargin];
    _containerTopConstraint.active = YES;
    
    [NSLayoutConstraint activateConstraints:@[
        [self.containerView.leadingAnchor constraintEqualToAnchor:self.avatarView.trailingAnchor constant:kAvatarMargin],
        [self.containerView.trailingAnchor constraintEqualToAnchor:self.trailingAnchor constant:-kHorizontalPadding],
        // Use a fixed bottom padding for stability during streaming
        [self.bottomAnchor constraintEqualToAnchor:self.containerView.bottomAnchor constant:kVerticalPadding]
    ]];
    
    // Text view constraints (inside container)
    [NSLayoutConstraint activateConstraints:@[
        [self.textView.topAnchor constraintEqualToAnchor:self.containerView.topAnchor constant:kContentPadding],
        [self.textView.leadingAnchor constraintEqualToAnchor:self.containerView.leadingAnchor constant:kContentPadding],
        [self.textView.trailingAnchor constraintEqualToAnchor:self.containerView.trailingAnchor constant:-kContentPadding],
        [self.textView.bottomAnchor constraintEqualToAnchor:self.containerView.bottomAnchor constant:-kContentPadding]
    ]];
    
    // Add a minimum height to the text view to avoid zero height issues
    NSLayoutConstraint *minHeightConstraint = [self.textView.heightAnchor constraintGreaterThanOrEqualToConstant:20.0];
    minHeightConstraint.active = YES;
    
    // Width of containerView is now resolved automatically by its leading/trailing anchors – no fixed width constraint required.
}

- (void)setupTrackingArea {
    if (_trackingArea) {
        [self removeTrackingArea:_trackingArea];
    }
    
    _trackingArea = [[NSTrackingArea alloc] initWithRect:self.bounds
                                                 options:(NSTrackingMouseEnteredAndExited | 
                                                         NSTrackingActiveInKeyWindow | 
                                                         NSTrackingInVisibleRect)
                                                   owner:self
                                                userInfo:nil];
    [self addTrackingArea:_trackingArea];
}

#pragma mark - Content and Layout

- (void)updateContent {
    if (!self.message) return;
    
    // DBM("updateContent", @"Updating content for message: %@", self.message.messageId);
    
    // Obtain deterministic model colour & symbol
    NSColor *modelColor = [ModelAppearanceFactory colorForModel:self.message.senderName];
    NSString *symbolName = [ModelAppearanceFactory symbolForModel:self.message.senderName];

    NSImage *symbolImage = [NSImage imageWithSystemSymbolName:symbolName accessibilityDescription:nil];
    [symbolImage setTemplate:YES]; // enable tinting for tint color rendering
    self.avatarView.image = symbolImage;
    // Tint avatar symbol
    if (@available(macOS 10.14, *)) {
        self.avatarView.contentTintColor = modelColor;
    }

    // Configure avatar background subtle colored circle
    self.avatarView.wantsLayer = YES;
    self.avatarView.layer.backgroundColor = [modelColor colorWithAlphaComponent:0.15].CGColor;

    // Apply border to container view for ambient styling
    self.containerView.layer.borderWidth = 1.0;
    self.containerView.layer.borderColor = [modelColor colorWithAlphaComponent:0.6].CGColor;
    
    // Broadcast accent color for window-level theming when message is from a non-user sender
    NSString *senderLower = self.message.senderId ? [self.message.senderId lowercaseString] : @"user";
    if (![senderLower isEqualToString:@"user"]) {
        [[NSNotificationCenter defaultCenter] postNotificationName:kModelAccentColorNotification
                                                            object:self
                                                          userInfo:@{ @"color": modelColor }];
    }
    
    // Update name and timestamp
    self.nameLabel.stringValue = self.message.senderName ? self.message.senderName : @"User";
    // Apply tint colour to sender name for visual consistency with avatar & borders
    self.nameLabel.textColor = modelColor;
    
    // Format timestamp
    NSDateFormatter *formatter = [[NSDateFormatter alloc] init];
    formatter.timeStyle = NSDateFormatterShortStyle;
    self.timestampLabel.stringValue = [formatter stringFromDate:self.message.timestamp ? self.message.timestamp : [NSDate date]];
    
    // Clean up any existing animation timer
    if (_animationTimer) {
        [_animationTimer invalidate];
        _animationTimer = nil;
    }
    
    // Remove any existing animated dots
    for (CALayer *dotLayer in _animatedDots) {
        [dotLayer removeFromSuperlayer];
    }
    [_animatedDots removeAllObjects];
    
    if (_indicatorContainerLayer) {
        [_indicatorContainerLayer removeFromSuperlayer];
        _indicatorContainerLayer = nil;
    }
    
    // Handle different message types
    switch (self.message.type) {
        case MessageType::TYPING_INDICATOR: {
            // Create a modern animated typing indicator
            
            // Clear any existing content
            self.textView.string = @"";
            
            // Set up container for dots with proper positioning - directly under sender name
            _indicatorContainerLayer = [CALayer layer];
            _indicatorContainerLayer.frame = CGRectMake(0, 0, 60, 20);
            [self.containerView.layer addSublayer:_indicatorContainerLayer];
            
            // Create the animated dots
            CGFloat dotSize = 8.0;
            CGFloat dotSpacing = 6.0;
            
            // Generate three brightness variants of the model color for dots
            NSArray *dotColors = @[
                modelColor,
                [[self class] color:modelColor byAdjustingBrightness:0.15],
                [[self class] color:modelColor byAdjustingBrightness:-0.15]
            ];
            
            for (int i = 0; i < 3; i++) {
                CALayer *dotLayer = [CALayer layer];
                dotLayer.frame = CGRectMake(i * (dotSize + dotSpacing), 5, dotSize, dotSize);
                dotLayer.cornerRadius = dotSize / 2.0;
                dotLayer.backgroundColor = [dotColors[i] CGColor];
                dotLayer.opacity = 0.4;
                
                // Add subtle shadow for depth
                dotLayer.shadowColor = [NSColor blackColor].CGColor;
                dotLayer.shadowOffset = CGSizeMake(0, 1);
                dotLayer.shadowOpacity = 0.2;
                dotLayer.shadowRadius = 1.0;
                
                [_indicatorContainerLayer addSublayer:dotLayer];
                [_animatedDots addObject:dotLayer];
            }
            
            // Start the animation timer
            _animationTimer = [NSTimer scheduledTimerWithTimeInterval:0.3
                                                              target:self
                                                            selector:@selector(updateDotAnimation:)
                                                            userInfo:nil
                                                             repeats:YES];
            [[NSRunLoop currentRunLoop] addTimer:_animationTimer forMode:NSRunLoopCommonModes];
            
            // Set a subtle background color for typing indicator
            self.containerView.layer.backgroundColor = [[NSColor controlBackgroundColor] colorWithAlphaComponent:0.7].CGColor;
            
            // Force stable height for typing indicator
            _cachedHeight = 60.0; // Reduced height since we removed the text
            break;
        }
            
        case MessageType::ERROR: {
            // Create an error message with red styling
            NSString *errorText = self.message.rawContent;
            if (errorText.length == 0) {
                errorText = @"An error occurred while generating the response.";
            }
            
            NSMutableAttributedString *errorAttrString = [[NSMutableAttributedString alloc] 
                                                        initWithString:errorText];
            
            // Apply error styling
            [errorAttrString addAttribute:NSForegroundColorAttributeName 
                                    value:[NSColor systemRedColor] 
                                    range:NSMakeRange(0, errorAttrString.length)];
            [errorAttrString addAttribute:NSFontAttributeName 
                                    value:[NSFont systemFontOfSize:13.0] 
                                    range:NSMakeRange(0, errorAttrString.length)];
            
            // Set the styled text
            self.textView.textStorage.attributedString = errorAttrString;
            
            // Set custom background color for error messages
            self.containerView.layer.backgroundColor = [[NSColor systemRedColor] colorWithAlphaComponent:0.1].CGColor;
            break;
        }
            
        default: {
            // Regular message content (TEXT, IMAGE, SYSTEM)
            NSAttributedString* attributedContent = [self.message getAttributedContent];
            if (attributedContent) {
                // DBM("updateContent", @"Setting attributed content with length: %lu", (unsigned long)attributedContent.length);
                self.textView.textStorage.attributedString = attributedContent;
                
                // Configure text view for proper sizing during streaming
                [self.textView.layoutManager ensureLayoutForTextContainer:self.textView.textContainer];
                
                // Force immediate layout if streaming
                if (self.message.type == MessageType::TEXT) {
                    [self.textView.layoutManager glyphRangeForTextContainer:self.textView.textContainer];
                }
            } else {
                // Safety fallback
                // DBM("updateContent", @"No attributed content available, setting empty string");
                self.textView.string = @"";
            }
            
            // Set default background color
            self.containerView.layer.backgroundColor = [NSColor controlBackgroundColor].CGColor;
            break;
        }
    }
    
    // Setup delegate for handling link clicks
    self.textView.delegate = self;
    
    // Width handled by Auto Layout – no manual constraint update necessary.
    
    // Ensure container top constraint remains stable
    _containerTopConstraint.constant = kContentTopMargin;
    
    _needsLayout = YES;
    [self setNeedsLayout:YES];
}

- (void)updateLayout {
    if (!_needsLayout) return;
    
    // Update container background color based on message state
    if (self.isSelected) {
        self.containerView.layer.backgroundColor = [NSColor selectedControlColor].CGColor;
        
        // Find and remove gradient layer if exists
        [self removeGradientBackgroundLayer];
    } else if (self.isHighlighted) {
        self.containerView.layer.backgroundColor = [NSColor unemphasizedSelectedContentBackgroundColor].CGColor;
        
        // Find and remove gradient layer if exists
        [self removeGradientBackgroundLayer];
    } else if (self.message && self.message.type == MessageType::TYPING_INDICATOR) {
        // Don't change the gradient background for typing indicator
        // Update the background gradient size if exists
        [self updateGradientBackgroundLayer];
    } else {
        self.containerView.layer.backgroundColor = [NSColor controlBackgroundColor].CGColor;
        
        // Find and remove gradient layer if exists
        [self removeGradientBackgroundLayer];
    }
    
    // If we have a typing indicator, update its position
    if (self.message && self.message.type == MessageType::TYPING_INDICATOR && _indicatorContainerLayer) {
        // Position indicator dots directly under the sender name
        CGFloat nameX = NSMinX(self.nameLabel.frame) - NSMinX(self.containerView.frame);
        
        // Position just below the name label
        [CATransaction begin];
        [CATransaction setDisableActions:YES]; // Prevent animation during layout changes
        _indicatorContainerLayer.position = CGPointMake(nameX + 48, 18); // Adjust position to align with name
        [CATransaction commit];
    }
    
    _needsLayout = NO;
}

// Helper method to remove gradient background layer
- (void)removeGradientBackgroundLayer {
    // Look for gradient layers in the container's sublayers
    for (CALayer *layer in self.containerView.layer.sublayers) {
        if ([layer isKindOfClass:[CAGradientLayer class]] && layer != _indicatorContainerLayer) {
            [layer removeFromSuperlayer];
            break;
        }
    }
}

// Helper method to update gradient background layer dimensions
- (void)updateGradientBackgroundLayer {
    // Look for gradient layers in the container's sublayers
    for (CALayer *layer in self.containerView.layer.sublayers) {
        if ([layer isKindOfClass:[CAGradientLayer class]] && layer != _indicatorContainerLayer) {
            // Update frame to match container bounds
            [CATransaction begin];
            [CATransaction setDisableActions:YES]; // Prevent implicit animation
            layer.frame = self.containerView.bounds;
            layer.cornerRadius = 8.0;
            [CATransaction commit];
            break;
        }
    }
}

- (CGFloat)heightForWidth:(CGFloat)width {
    // Only recalculate if width changed or we need layout
    if (width != self.preferredWidth || _needsLayout || _cachedHeight == 0) {
        // Use a minimum width to avoid constraints issues
        self.preferredWidth = MAX(100, width);
        
        // Force layout to calculate the proper height
        [self layoutSubtreeIfNeeded];
        
        // Special handling for typing indicator (stable height)
        if (self.message && self.message.type == MessageType::TYPING_INDICATOR) {
            // Use a fixed height for typing indicator to avoid jumps
            CGFloat textViewHeight = 32.0; // Height for single line of text
            CGFloat contentHeight = kVerticalPadding + kNameHeight + kContentTopMargin + textViewHeight + kVerticalPadding;
            _cachedHeight = MAX(contentHeight, kVerticalPadding + kAvatarSize + kVerticalPadding);
            return _cachedHeight;
        }
        
        // Calculate the height based on constraints
        CGFloat textViewHeight = [self.message heightForWidth:MAX(100, width - (kHorizontalPadding * 2 + kAvatarSize + kAvatarMargin))]; // Account for text padding
        
        // Calculate total height with fixed top and bottom padding
        // Top: vertical padding + name height + content top margin
        // Bottom: vertical padding (fixed)
        CGFloat topSectionHeight = kVerticalPadding + kNameHeight + kContentTopMargin;
        CGFloat bottomPadding = kVerticalPadding; // Keep bottom padding constant
        
        // Calculate content area with stable padding 
        CGFloat contentHeight = topSectionHeight + 
                              MAX(20, textViewHeight + (kContentPadding * 2)) + 
                              bottomPadding;
        
        // Ensure minimum height for avatar
        _cachedHeight = MAX(contentHeight, kVerticalPadding + kAvatarSize + kVerticalPadding);
    }
    
    return _cachedHeight;
}

#pragma mark - NSView Overrides

- (void)layout {
    [super layout];
    [self updateLayout];
    
    // Special handling for typing indicator during layout
    if (self.message && self.message.type == MessageType::TYPING_INDICATOR) {
        if (_indicatorContainerLayer) {
            // Position indicator dots directly under the sender name
            CGFloat nameX = NSMinX(self.nameLabel.frame) - NSMinX(self.containerView.frame);
            
            // Position just below the name label
            [CATransaction begin];
            [CATransaction setDisableActions:YES]; // Prevent animation during layout changes
            _indicatorContainerLayer.position = CGPointMake(nameX + 48, 18); // Adjust position to align with name
            [CATransaction commit];
        }
    }
}

- (BOOL)acceptsFirstResponder {
    return YES;
}

- (BOOL)performKeyEquivalent:(NSEvent *)event {
    // Forward cmd+c/x to the text view if it has selection
    if (self.textView && self.textView.selectedRange.length > 0 && 
        (event.modifierFlags & NSEventModifierFlagCommand)) {
        
        NSString *key = [event charactersIgnoringModifiers];
        if ([key isEqualToString:@"c"] || [key isEqualToString:@"x"]) {
            // Even though we're making the text view first responder, also explicitly
            // forward the command for robustness
            if ([key isEqualToString:@"c"]) {
                [self.textView copy:self];
                return YES;
            } else if ([key isEqualToString:@"x"]) {
                // Since textView is non-editable, cut acts as copy
                [self.textView copy:self];
                return YES;
            }
        }
    }
    
    return [super performKeyEquivalent:event];
}

- (BOOL)isFlipped {
    return YES; // Use flipped coordinates for easier positioning
}

- (void)drawRect:(NSRect)dirtyRect {
    [super drawRect:dirtyRect];
    // Additional custom drawing if needed
}

- (void)mouseDown:(NSEvent *)event {
    NSPoint localPoint = [self convertPoint:event.locationInWindow fromView:nil];
    
    // Check if the click is within the textView's frame in the container
    NSPoint containerPoint = [self.containerView convertPoint:localPoint fromView:self];
    if (NSPointInRect(containerPoint, self.textView.frame)) {
        // Make the textView the first responder to handle cmd+c/x
        [self.window makeFirstResponder:self.textView];
    }
    
    [super mouseDown:event];
}

- (void)mouseEntered:(NSEvent *)event {
    [super mouseEntered:event];
    [self setHighlighted:YES];
}

- (void)mouseExited:(NSEvent *)event {
    [super mouseExited:event];
    [self setHighlighted:NO];
}

#pragma mark - State Management

- (void)setHighlighted:(BOOL)highlighted {
    if (_isHighlighted != highlighted) {
        _isHighlighted = highlighted;
        [self updateLayout];
        [self setNeedsDisplay:YES];
    }
}

- (void)setSelected:(BOOL)selected {
    if (_isSelected != selected) {
        _isSelected = selected;
        [self updateLayout];
        [self setNeedsDisplay:YES];
    }
}

- (void)setMessage:(Message *)message {
    _message = message;
    // Reset cached height when message changes
    _cachedHeight = 0.0;
    // Update content with the new message
    [self updateContent];
}

- (void)setFrame:(NSRect)frame {
    if (!NSEqualRects(self.frame, frame)) {
        [super setFrame:frame];
        [self setupTrackingArea];
        _needsLayout = YES;
        
        // Ensure gradient layer gets updated
        [self updateGradientBackgroundLayer];
    }
}

#pragma mark - NSTextViewDelegate Methods

- (BOOL)textView:(NSTextView *)textView clickedOnLink:(id)link atIndex:(NSUInteger)charIndex {
    if ([link isKindOfClass:[NSURL class]]) {
        NSURL *url = (NSURL *)link;
        [[NSWorkspace sharedWorkspace] openURL:url];
        return YES;
    }
    return NO;
}

#pragma mark - Menu and Responder Chain Methods

- (BOOL)validateMenuItem:(NSMenuItem *)menuItem {
    SEL action = [menuItem action];
    
    if (action == @selector(cut:) || action == @selector(copy:) || action == @selector(selectAll:)) {
        // Enable copy and selectAll if we have text
        if (self.textView && self.textView.string.length > 0) {
            if (action == @selector(copy:)) {
                // Enable copy only if there's a selection
                return (self.textView.selectedRange.length > 0);
            } else if (action == @selector(cut:)) {
                // Since our textView is non-editable, cut behaves like copy
                return (self.textView.selectedRange.length > 0);
            } else if (action == @selector(selectAll:)) {
                return YES;
            }
        }
        return NO;
    }
    
    // For other menu items, use the default validation
    return [super validateMenuItem:menuItem];
}

// Forward common edit actions to the text view
- (void)copy:(id)sender {
    [self.textView copy:sender];
}

- (void)cut:(id)sender {
    // Since our text view is non-editable, cut behaves like copy
    [self.textView copy:sender];
}

- (void)selectAll:(id)sender {
    [self.textView selectAll:sender];
}

- (void)dealloc {
    // Clean up animation timer if active
    if (_animationTimer) {
        [_animationTimer invalidate];
        _animationTimer = nil;
    }
}

// Animation method for typing indicator dots
- (void)updateDotAnimation:(NSTimer *)timer {
    if (_animatedDots.count == 0) return;
    
    // Reset all dots
    for (CALayer *dotLayer in _animatedDots) {
        [CATransaction begin];
        [CATransaction setDisableActions:YES];
        dotLayer.opacity = 0.4;
        dotLayer.transform = CATransform3DIdentity;
        [CATransaction commit];
    }
    
    // Animate the current dot
    CALayer *currentDot = _animatedDots[_animationFrame % _animatedDots.count];
    
    [CATransaction begin];
    [CATransaction setAnimationDuration:0.25];
    
    // Enhanced animation: Scale up, increase opacity, and add pulse effect
    currentDot.opacity = 1.0;
    
    // Create a more interesting transform with slight y-axis movement for a pulse effect
    CATransform3D pulseTransform = CATransform3DMakeScale(1.4, 1.4, 1.0);
    pulseTransform = CATransform3DTranslate(pulseTransform, 0, -1.0, 0);
    currentDot.transform = pulseTransform;
    
    // Add a subtle pulse animation
    CABasicAnimation *pulseAnimation = [CABasicAnimation animationWithKeyPath:@"shadowRadius"];
    pulseAnimation.fromValue = @(1.0);
    pulseAnimation.toValue = @(3.0);
    pulseAnimation.duration = 0.25;
    pulseAnimation.autoreverses = YES;
    [currentDot addAnimation:pulseAnimation forKey:@"pulseAnimation"];
    
    [CATransaction commit];
    
    // Move to next frame
    _animationFrame++;
}

// Deprecated – kept for backward compatibility. Uses ModelAppearanceFactory under the hood.
+ (NSColor *)colorForSenderName:(NSString *)senderName {
    return [ModelAppearanceFactory colorForModel:senderName];
}

+ (NSColor *)color:(NSColor *)color byAdjustingBrightness:(CGFloat)delta {
    CGFloat hue, sat, bri, alpha;
    [[color colorUsingColorSpace:[NSColorSpace deviceRGBColorSpace]] getHue:&hue saturation:&sat brightness:&bri alpha:&alpha];
    bri = MIN(MAX(bri + delta, 0.0), 1.0);
    return [NSColor colorWithHue:hue saturation:sat brightness:bri alpha:alpha];
}

@end
