#import "chat_content_view_controller.h"
#import "ModelAppearanceFactory.h"

// Include required headers
#import "ChatViewController.h"
#import "ComposerView.h"
#import "mention/MentionPopoverController.h"
#import "model/BackendError.h"
#import "model/MentionSuggestion.h"
#import "core/model/Message.h"
#import "session_autosave_manager.mm"
#import "session_metadata_manager.h"
#import "MessageView.h"
#import "CapabilityIconProvider.h"

// C++ includes
#include "../../core/util/debug.h"
#include "macos_chat_ui.h" // For MacOSChatUI pointer
#include "../../core/llm/capability_heuristics.h"

// --- Constants for layout --- 
static const CGFloat kMinInputHeight = 36.0; // Approx height for single line + padding
static const CGFloat kDefaultInputHeight = kMinInputHeight;
static const CGFloat kInputSidePadding = 10.0;
static const CGFloat kInputBottomPadding = 10.0;
static const CGFloat kInputTopPadding = 5.0;
static const CGFloat kSendButtonWidth = 60.0;
static const CGFloat kSendButtonPadding = 5.0;
static NSString* const kModelAccentColorNotification = @"ChatModelAccentColorChanged";

@interface ChatContentViewController () <ComposerViewDelegate, MentionPopoverDelegate>

// Private properties, hidden from interface
@property (nonatomic, strong, readwrite) NSView *mainContainerView;
@property (nonatomic, strong, readwrite) ChatViewController *chatViewController;
@property (nonatomic, strong, readwrite) ComposerView *composerView;
@property (nonatomic, strong, readwrite) MentionPopoverController *mentionPopoverController;

// Store weak reference to C++ UI instance
@property (nonatomic, assign) launcher::ui::MacOSChatUI* uiInstance;

@end

@implementation ChatContentViewController

// Synthesize properties if not automatically done
@synthesize mainContainerView = _mainContainerView;
@synthesize chatViewController = _chatViewController;
@synthesize composerView = _composerView;
@synthesize mentionPopoverController = _mentionPopoverController;
@synthesize uiInstance = _uiInstance;

- (instancetype)init {
    self = [super init];
    if (self) {
        _uiInstance = nullptr;
    }
    return self;
}

- (void)loadView {
    DBM(@"Loading ChatContentViewController view");
    
    // Create main container view
    _mainContainerView = [[NSView alloc] initWithFrame:NSMakeRect(0, 0, 900, 700)];
    _mainContainerView.translatesAutoresizingMaskIntoConstraints = NO;
    self.view = _mainContainerView;
}

- (void)viewDidLoad {
    [super viewDidLoad];
    
    DBM(@"Setting up chat and composer views");
    
    // --- Create Chat View Controller --- //
    _chatViewController = [[ChatViewController alloc] init];
    _chatViewController.view.translatesAutoresizingMaskIntoConstraints = NO;
    [_mainContainerView addSubview:_chatViewController.view];
    [self addChildViewController:_chatViewController];
    
    // --- Create ComposerView --- //
    _composerView = [[ComposerView alloc] initWithFrame:NSMakeRect(0, 0, 600, 60)];
    _composerView.delegate = self; // Set self as delegate
    _composerView.translatesAutoresizingMaskIntoConstraints = NO;
    [_mainContainerView addSubview:_composerView];

    // --- Create Mention Popover Controller --- //
    _mentionPopoverController = [[MentionPopoverController alloc] init];
    _mentionPopoverController.delegate = self; // Set self as delegate

    // --- Setup Auto Layout Constraints --- //
    NSDictionary *views = @{
        @"chatView": _chatViewController.view,
        @"composerView": _composerView
    };
    NSDictionary *metrics = @{
        @"topPad": @(kInputTopPadding),
        @"bottomPad": @(kInputBottomPadding),
        @"sidePad": @(kInputSidePadding)
    };

    // Horizontal constraints for chat view and composer view
    [_mainContainerView addConstraints:[NSLayoutConstraint 
        constraintsWithVisualFormat:@"H:|[chatView]|" 
        options:0 metrics:metrics views:views]];
        
    [_mainContainerView addConstraints:[NSLayoutConstraint 
        constraintsWithVisualFormat:@"H:|-(sidePad)-[composerView]-(sidePad)-|" 
        options:0 metrics:metrics views:views]];

    // Vertical constraints: chat view above composer view
    [_mainContainerView addConstraints:[NSLayoutConstraint 
        constraintsWithVisualFormat:@"V:|[chatView]-(topPad)-[composerView]-(bottomPad)-|" 
        options:0 metrics:metrics views:views]];
}

- (void)viewDidAppear {
    [super viewDidAppear];
    [self focusOnChatInput];
}

#pragma mark - UI Instance Setter

- (void)setUiInstance:(launcher::ui::MacOSChatUI*)uiInstance {
    _uiInstance = uiInstance;
}

#pragma mark - UI Actions

- (void)focusOnChatInput {
    if (self.view.window && _composerView && _composerView.textView) {
        // DBM(@"Focusing on chat input.");
        [self.view.window makeFirstResponder:_composerView.textView];
    } else {
        ERM(@"Cannot focus chat input: window or composer view/textView is nil.");
    }
}

- (void)sendMessage {
    if (!_uiInstance) {
        ERM(@"UI instance is null, cannot send message.");
        return;
    }
    
    NSString* message = [_composerView.textView string];
    // Trimming logic might be inside ComposerView or handled before calling this
    
    if (!message || message.length == 0) {
        DBM(@"Attempted to send empty message.");
        return;
    }
    
    DBM(@"Sending message via UI instance: %@", message);

    // Call the C++ method to initiate the stream
    _uiInstance->initiateStreamForMessage(message);
    
    // Clear the input field - handled by composerView
    [_composerView clear];
    
    // Focus on the input field again
    [self focusOnChatInput];

    // Inform metadata manager for automatic naming / icon handling
    launcher::ui::SessionMetadataManager::shared().userSentMessage(self.view.window, message);
}

- (void)addTestMessages {
    if (!_uiInstance) {
        ERM(@"UI instance is null, cannot add test messages.");
        return;
    }
    DBM(@"Forwarding addTestMessages call to UI instance.");
    // Let the C++ class handle adding test messages to the UI
    _uiInstance->addTestMessages();
}

#pragma mark - ComposerViewDelegate Methods

- (void)composerViewDidSubmitMessage:(NSString *)message {
    DBM(@"composerViewDidSubmitMessage called, forwarding to internal sendMessage.");
    // This delegate method now just calls our internal sendMessage method
    [self sendMessage];
}

#pragma mark - ComposerViewDelegate Methods for Mentions

- (void)composerView:(ComposerView *)composerView
didDetectMentionTriggerInRange:(NSRange)triggerRange
                 query:(NSString *)query
        fullQueryRange:(NSRange)fullQueryRange
{
    if (!_mentionPopoverController) {
        ERM(@"Mention popover controller is nil, cannot show.");
        return;
    }
    DBM(@"Mention triggered: Query='%@', Range=%@", query, NSStringFromRange(fullQueryRange));
    [_mentionPopoverController showAndSearchWithQuery:query
                                   anchoredToTextView:composerView.textView
                                                range:fullQueryRange];
}

- (void)composerView:(ComposerView *)composerView
mentionQueryDidChange:(NSString *)query
               range:(NSRange)range
{
     if (!_mentionPopoverController) {
        ERM(@"Mention popover controller is nil, cannot update query.");
        return;
    }
    DBM(@"Mention query changed: Query='%@', Range=%@", query, NSStringFromRange(range));
    [_mentionPopoverController updateSearchQuery:query range:range];
}

- (void)composerViewDidCancelMention:(ComposerView *)composerView {
    if (!_mentionPopoverController) {
        ERM(@"Mention popover controller is nil, cannot hide.");
        return;
    }
    DBM(@"Mention cancelled by composer view, hiding popover.");
    [_mentionPopoverController hide];
}

// --- Delegate Methods FOR Mention Popover Interaction (called by ComposerView) ---

- (BOOL)mentionPopoverIsVisible {
    if (!_mentionPopoverController) {
        return NO;
    }
    return _mentionPopoverController.popover.isShown;
}

- (BOOL)mentionPopoverMoveSelectionUp {
    if (![self mentionPopoverIsVisible]) {
        return NO;
    }
    DBM(@"Forwarding moveSelectionUp to MentionPopoverController");
    return [_mentionPopoverController moveSelectionUp]; // Call the controller method
}

- (BOOL)mentionPopoverMoveSelectionDown {
     if (![self mentionPopoverIsVisible]) {
        return NO;
    }
    DBM(@"Forwarding moveSelectionDown to MentionPopoverController");
    return [_mentionPopoverController moveSelectionDown]; // Call the controller method
}

- (BOOL)mentionPopoverConfirmSelection {
    if (![self mentionPopoverIsVisible]) {
        return NO;
    }
    DBM(@"Forwarding confirmSelection to MentionPopoverController");
    return [_mentionPopoverController confirmSelection]; // Call the controller method
}

#pragma mark - MentionPopoverDelegate Methods (Implementation)

// This method is called BY the MentionPopoverController when it needs data
- (void)mentionController:(MentionPopoverController *)controller
           searchForQuery:(NSString *)query
               identifier:(NSNumber *)requestIdentifier
          completionBlock:(void (^)(NSArray<MentionSuggestion *> * _Nullable, BackendError * _Nullable))completionBlock
{
    if (!_uiInstance) {
         ERM(@"UI instance is null, cannot forward mention search.");
         if (completionBlock) {
             BackendError *error = [BackendError errorWithCode:launcher::core::BackendErrorCode::INTERNAL_ERROR
                                                     message:@"Internal UI component missing."
                                             underlyingError:nil];
             completionBlock(nil, error);
         }
         return;
    }
    
    DBM(@"Forwarding mention search to MacOSChatUI. Query='%@', ID=%@", query, requestIdentifier);
    
    // Forward the call to the C++ MacOSChatUI instance
    _uiInstance->searchMentionableItemsWithQuery(query, requestIdentifier, completionBlock);
}

// This method is called BY the MentionPopoverController when a suggestion is selected
- (void)mentionController:(MentionPopoverController *)controller
      didSelectSuggestion:(MentionSuggestion *)suggestion
                  atRange:(NSRange)replacementRange
{
    DBM(@"Suggestion selected: %@, replacing range %@", suggestion.displayText, NSStringFromRange(replacementRange));
    
    NSTextView *textView = _composerView.textView;
    if (!textView) {
        ERM(@"Composer text view is nil, cannot insert suggestion.");
        return;
    }
    
    // Format the replacement string (e.g., "@DisplayName ")
    NSString *replacementString = [NSString stringWithFormat:@"@%@ ", suggestion.displayText]; 
    
    // Safely insert the text
    if ([textView shouldChangeTextInRange:replacementRange replacementString:replacementString]) {
        // Perform the insertion
        [textView insertText:replacementString replacementRange:replacementRange];
        
        // Move the cursor to the end of the inserted text
        NSRange newSelectedRange = NSMakeRange(replacementRange.location + [replacementString length], 0);
        [textView setSelectedRange:newSelectedRange affinity:NSSelectionAffinityDownstream stillSelecting:NO];
        
        // Ensure the text view regains focus
        [self focusOnChatInput]; 
    } else {
         ERM(@"Text view rejected proposed change for mention insertion.");
    }
}

// This method is called BY the MentionPopoverController when it cancels an operation
- (void)mentionController:(MentionPopoverController *)controller
didCancelOperationWithIdentifier:(NSNumber *)requestIdentifier
{
    if (!_uiInstance || !requestIdentifier) {
        ERM(@"UI instance or identifier is null, cannot forward cancellation.");
        return;
    }
    DBM(@"Mention operation cancelled by popover. Forwarding to MacOSChatUI (ID: %@)", requestIdentifier);
    
    // Forward the cancellation to the C++ MacOSChatUI instance
    _uiInstance->cancelRequestWithIdentifier(requestIdentifier);
}

#pragma mark - Model Picker Delegate

- (void)composerView:(ComposerView *)composerView
 didChangeModelProvider:(NSString *)provider
                model:(NSString *)model
{
    if (!_uiInstance) {
        ERM(@"UI instance is null, cannot forward model change.");
        return;
    }
    std::string provCpp = [provider UTF8String];
    std::string modelCpp = [model UTF8String];
    _uiInstance->setActiveModel(provCpp, modelCpp);

    // --- Notify window of accent color change based on currently selected model ---
    NSColor *accentColor = [ModelAppearanceFactory colorForModel:(model ?: provider)];
    [[NSNotificationCenter defaultCenter] postNotificationName:kModelAccentColorNotification
                                                        object:self
                                                      userInfo:@{ @"color": accentColor }];

    // --- Update window metadata (icon, tint, title) so tab bar & sidebar reflect model ---
    if (self.view.window) {
        NSString *symbol = [ModelAppearanceFactory symbolForProvider:provider model:model];
        launcher::ui::SessionMetadataManager::shared().applyMetadataForWindow(self.view.window,
                                                                              symbol,
                                                                              accentColor,
                                                                              nil);
    }
}

- (void)dealloc {
    DBM(@"Deallocating ChatContentViewController");
    _uiInstance = nullptr; // Clear C++ pointer
}

@end 