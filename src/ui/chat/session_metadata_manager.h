#pragma once

#include <Foundation/Foundation.h>

@class NSWindow;
@class NSColor;

// Notification posted when a window's chat title was updated by the metadata manager.
extern NSString * const kSessionMetadataManagerTitleUpdatedNotification;

namespace launcher {
namespace ui {

/**
 * SessionMetadataManager – keeps per-window chat metadata such as
 * generated short title, chosen SF-Symbol icon and accent colour.  It is a
 * lightweight singleton that lives entirely on the ObjC side and is safe to
 * call from ObjC++ sources.
 */
class SessionMetadataManager {
public:
    // Returns the shared singleton instance.
    static SessionMetadataManager &shared();

    // Registers a new chat window.  Must be called once when the window is
    // created (e.g. in TopChatWindowController::setupWindow).
    void registerWindow(NSWindow *window);

    // Call whenever the user sends a message.  After a few messages this will
    // trigger automatic summarisation + title/icon update.
    void userSentMessage(NSWindow *window, NSString *message);

    // Accessors used by the sidebar.
    NSString *iconSymbolNameForWindow(NSWindow *window) const;
    NSColor  *tintColorForWindow(NSWindow *window) const;
    NSString *generatedTitleForWindow(NSWindow *window) const;

    // NEW: Persist current metadata to the underlying conversation file
    void persistMetadataForWindow(NSWindow *window);

    // NEW: Apply metadata loaded from disk to the window/session manager
    void applyMetadataForWindow(NSWindow *window,
                                NSString *iconSymbol,
                                NSColor *tint,
                                NSString *title);

    // Legacy purely random picker retained as fallback when requested symbol
    // is unavailable on current macOS release.
    NSString *pickRandomIcon() const;

private:
    SessionMetadataManager();
    ~SessionMetadataManager();

    SessionMetadataManager(const SessionMetadataManager &) = delete;
    SessionMetadataManager &operator=(const SessionMetadataManager &) = delete;

    struct Metadata;
    NSMapTable<NSWindow *, id> *table_; // weak-key table storing Metadata objects

    // Helpers
    id ensureMetadata(NSWindow *window);
    // Picks a symbol name from the curated pool in a way that is stable for a
    // given window but may refresh daily.  Implemented in the .mm file.
    NSString *pickRandomIconForWindow(NSWindow *window) const;
    NSColor *pickRandomTint() const;
    NSString *generateTitleFromFirstMessage(NSString *message) const;
};

} // namespace ui
} // namespace launcher 