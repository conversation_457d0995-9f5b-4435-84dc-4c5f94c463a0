#import <Cocoa/Cocoa.h>
#include <dispatch/dispatch.h>
#include <objc/message.h>
#include <cmath>
#include <algorithm>
#include <sstream>
#include <string>
#include <unordered_map>
#include <unordered_set>

#include "session_autosave_manager.h"

#include "top_chat_window_controller.h"
#include "macos_chat_ui.h"
#include "../../core/persistence/session_serializer.h"
#include "../../core/persistence/conversation_store.h"
#include "../../core/util/debug.h"
#include "tabs/tab_group_manager.h"
#import "session_metadata_manager.h"

using launcher::core::persistence::SessionMeta;
using launcher::core::persistence::WindowSnapshot;
using launcher::core::persistence::SessionSerializer;
using launcher::core::persistence::GroupSnapshot;

namespace launcher::ui {

static inline std::filesystem::path autosavePath() {
#ifdef __APPLE__
    const char *home = getenv("HOME");
    std::filesystem::path base = std::filesystem::path(home ? home : ".") / "Library" / "Application Support" / "MicroLauncher";
    return base / "Sessions" / "session-autosave.json";
#else
    return std::filesystem::path("session-autosave.json");
#endif
}

class SessionAutosaveManagerImpl {
public:
    dispatch_source_t timer{nullptr};
    std::vector<std::shared_ptr<launcher::ui::MacOSChatUI>> restored;
    std::vector<WindowSnapshot> closedStack; // LIFO of recently closed windows
    uint64_t lastHash{0}; // Hash of last-written session state
};

SessionAutosaveManager &SessionAutosaveManager::shared() {
    static SessionAutosaveManager instance;
    return instance;
}

SessionAutosaveManager::SessionAutosaveManager() : pImpl_(std::make_unique<SessionAutosaveManagerImpl>()) {}
SessionAutosaveManager::~SessionAutosaveManager() = default;

void SessionAutosaveManager::start() {
    if (pImpl_->timer) return;
    // 3-second interval
    pImpl_->timer = dispatch_source_create(DISPATCH_SOURCE_TYPE_TIMER, 0, 0, dispatch_get_main_queue());
    dispatch_source_set_timer(pImpl_->timer, dispatch_time(DISPATCH_TIME_NOW, 3 * NSEC_PER_SEC), 3 * NSEC_PER_SEC, 1 * NSEC_PER_SEC);
    __weak SessionAutosaveManager *weakSelf = this;
    dispatch_source_set_event_handler(pImpl_->timer, ^{
        if (auto self = weakSelf) {
            self->performAutosave();
        }
    });
    dispatch_resume(pImpl_->timer);
}

void SessionAutosaveManager::stop() {
    if (!pImpl_->timer) return;
    dispatch_source_cancel(pImpl_->timer);
    pImpl_->timer = nullptr;
}

static WindowSnapshot snapshotForWindow(NSWindow *window) {
    WindowSnapshot ws;
    ws.uuid = [[[NSUUID UUID] UUIDString] UTF8String];
    ws.title = window.title.UTF8String ?: "";
    // Always persist the logical TabGroupManager identifier when available so
    // single-tab groups are preserved across restarts.  This avoids the edge
    // case where a group temporarily shrinks to one tab right before the
    // autosave cycle, which previously caused the group_id to be dropped.
    NSString *gid = [[TabGroupManager sharedManager] groupIdForWindow:window];
    if (gid) {
        ws.group_id = gid.UTF8String;
    }
    NSRect f = window.frame;
    ws.frame_x = f.origin.x; ws.frame_y = f.origin.y; ws.frame_w = f.size.width; ws.frame_h = f.size.height;

    // Obtain conversation id via controller -> uiInstance
    TopChatWindowController *controller = (TopChatWindowController *)window.windowController;
    if (controller && [controller respondsToSelector:@selector(uiInstance)]) {
        launcher::ui::MacOSChatUI *ui = nil;
        // Use objc_msgSend to avoid ARC issues
        typedef launcher::ui::MacOSChatUI *(*GetterFunc)(id, SEL);
        GetterFunc getter = (GetterFunc)objc_msgSend;
        ui = getter(controller, @selector(uiInstance));
        if (ui) {
            ws.conversation_id = ui->conversationId();
        }
    }
    return ws;
}

void SessionAutosaveManager::performAutosave() {
    @autoreleasepool {
        // Purge any UI instances whose windows have been closed so we do not
        // retain "zombie" NSWindow objects indefinitely.
        {
            auto &restored = pImpl_->restored;
            restored.erase(std::remove_if(restored.begin(), restored.end(), [](const std::shared_ptr<launcher::ui::MacOSChatUI> &ui) {
                if (!ui) { return true; }
                NSWindow *w = ui->getNativeWindow();
                // Remove if the window is gone or no longer visible / miniaturised.
                return !(w && ([w isVisible] || [w isMiniaturized]));
            }), restored.end());
        }

        SessionMeta meta;
        meta.timestamp = std::chrono::duration_cast<std::chrono::seconds>(std::chrono::system_clock::now().time_since_epoch()).count();

        NSArray<NSWindow *> *windows = [NSApp windows];
        for (NSWindow *win in windows) {
            // For standalone windows (pre-macOS 10.12) we keep the original
            // visibility check so we do not persist hidden/zombie objects.
            // For tab-group based chat windows (tabbingIdentifier == "ChatWindow")
            // we deliberately *include* invisible background tabs so that the
            // full set of conversations is restored on next launch.
#if __MAC_OS_X_VERSION_MAX_ALLOWED >= 101200
            BOOL isChatTab = NO;
            if (@available(macOS 10.12, *)) {
                isChatTab = [win.tabbingIdentifier isEqualToString:@"ChatWindow"];
            }
            if (!isChatTab && ![win isVisible] && ![win isMiniaturized]) {
                continue;
            }
#else
            if (![win isVisible] && ![win isMiniaturized]) {
                continue;
            }
#endif
            // Skip autosave for brand-new tabs with no messages (no conversation_id).
            WindowSnapshot ws = snapshotForWindow(win);
            if (ws.conversation_id.empty()) {
                // Window has no conversation yet – do not include in session autosave.
                continue;
            }
            meta.windows.push_back(std::move(ws));
        }
        // Persist closed-tab stack as well so it survives restarts.
        meta.closed_stack = pImpl_->closedStack;

        // ------------------------------------------------------------------
        // Capture TabGroupManager metadata (titles, tints)
        // ------------------------------------------------------------------
        {
            TabGroupManager *mgr = [TabGroupManager sharedManager];
            NSArray<NSString *> *gids = [mgr allGroupIds];

            auto colorToHex = ^std::string(NSColor *c) {
                if (!c) { return std::string("#000000FF"); }
                NSColor *rgb = [c colorUsingColorSpace:[NSColorSpace sRGBColorSpace]];
                CGFloat r,g,b,a; [rgb getRed:&r green:&g blue:&b alpha:&a];
                uint8_t ri = (uint8_t)lround(r * 255.0);
                uint8_t gi = (uint8_t)lround(g * 255.0);
                uint8_t bi = (uint8_t)lround(b * 255.0);
                uint8_t ai = (uint8_t)lround(a * 255.0);
                char buf[10]; snprintf(buf, sizeof(buf), "#%02X%02X%02X%02X", ri, gi, bi, ai);
                return std::string(buf);
            };

            for (NSString *gid in gids) {
                GroupSnapshot gs;
                gs.group_id = gid.UTF8String;
                gs.title = ([mgr groupTitleForId:gid] ?: @"").UTF8String;
                gs.tint_hex = colorToHex([mgr groupTintForId:gid]);
                gs.auto_title = NO; // manager knows this but not exposed; best-effort

                // NEW: serialise closed tabs for this group
                NSArray<id> *closed = [mgr closedTabsInGroup:gid];
                for (id info in closed) {
                    WindowSnapshot w;
                    w.uuid = "";
                    NSString *title = [info valueForKey:@"title"] ?: @"";
                    w.title = title.UTF8String;
                    w.group_id = gid.UTF8String;
                    NSRect frame = [[info valueForKey:@"frame"] rectValue];
                    w.frame_x = frame.origin.x;
                    w.frame_y = frame.origin.y;
                    w.frame_w = frame.size.width;
                    w.frame_h = frame.size.height;
                    NSString *cid = [info valueForKey:@"conversationId"] ?: @"";
                    w.conversation_id = cid.UTF8String;

                    nlohmann::json meta;
                    NSString *iconSym = [info valueForKey:@"iconSymbolName"];
                    if (iconSym) { meta["icon_symbol"] = std::string(iconSym.UTF8String); }
                    NSColor *tintColor = [info valueForKey:@"tintColor"];
                    if (tintColor) { meta["tint_hex"] = colorToHex(tintColor); }
                    w.ui_state = meta;
                    gs.closed_tabs.push_back(std::move(w));
                }

                meta.groups.push_back(std::move(gs));
            }
        }

        // ---------- Change detection ----------
        auto computeMetaHash = [](const SessionMeta &m) -> uint64_t {
            std::ostringstream oss;
            oss << m.windows.size() << '|';
            for (const auto &w : m.windows) {
                // NOTE: Intentionally omit w.uuid from the signature because snapshotForWindow
                // generates a fresh random UUID on every call, which would falsely mark the
                // session as changed each autosave cycle.  The combination of conversation_id,
                // group_id and rounded frame geometry is sufficient to detect user-visible
                // changes while remaining stable across invocations.
                oss << w.conversation_id << ';' << w.group_id << ';'
                    << static_cast<int>(std::round(w.frame_x)) << ','
                    << static_cast<int>(std::round(w.frame_y)) << ','
                    << static_cast<int>(std::round(w.frame_w)) << ','
                    << static_cast<int>(std::round(w.frame_h)) << '|';
            }
            oss << "groups:" << m.groups.size() << '|';
            for (const auto &g : m.groups) {
                // Include stable group-specific fields to detect renames or tint changes.
                oss << g.group_id << ';' << g.title << ';' << g.tint_hex << '|';
            }
            oss << m.closed_stack.size() << '|';
            if (!m.closed_stack.empty()) {
                const auto &cs = m.closed_stack.back();
                oss << cs.conversation_id; // also omit cs.uuid for same reason
            }
            std::string sig = oss.str();
            return std::hash<std::string>{}(sig);
        };

        uint64_t newHash = computeMetaHash(meta);
        if (newHash == pImpl_->lastHash) {
            // No meaningful change since last write; skip disk I/O.
            return;
        }
        // Trim the serialised closed stack to a sensible size (e.g. 10) so we
        // do not write unbounded data to disk over time.
        const size_t kMaxClosed = 10;
        if (meta.closed_stack.size() > kMaxClosed) {
            meta.closed_stack.erase(meta.closed_stack.begin(),
                                    meta.closed_stack.end() - kMaxClosed);
        }

        SessionSerializer::write(meta, autosavePath());
        pImpl_->lastHash = newHash;
        DBM(@"Autosaved session with %zu windows", meta.windows.size());
    }
}

bool SessionAutosaveManager::restorePreviousSession() {
    using namespace std::chrono;
    auto path = autosavePath();
    auto opt = SessionSerializer::read(path);
    if (!opt) { return false; }
    SessionMeta meta = *opt;
    // Allow restore to proceed as long as there is useful state (groups or closed-tab history)
    bool no_windows  = meta.windows.empty();
    bool no_groups   = meta.groups.empty();
    bool no_history  = meta.closed_stack.empty();
    if (no_windows && no_groups && no_history) {
        return false; // Nothing meaningful to restore
    }

    DBM(@"Restoring %zu windows from previous session", meta.windows.size());

    TabGroupManager *mgr = [TabGroupManager sharedManager];

    // ------------------------------------------------------------------
    // Determine which stored groups are actually referenced by at least
    // one WindowSnapshot so we avoid resurrecting empty groups.
    // ------------------------------------------------------------------
    std::unordered_set<std::string> used_group_ids;
    for (const auto &ws : meta.windows) {
        if (!ws.group_id.empty()) {
            used_group_ids.insert(ws.group_id);
        }
    }

    // NEW: Also mark groups with closed tabs as used
    for (const auto &gs : meta.groups) {
        if (!gs.closed_tabs.empty()) {
            used_group_ids.insert(gs.group_id);
        }
    }

    // ------------------------------------------------------------------
    // Recreate logical tab groups with their titles / tints before adding
    // windows so sidebar reflects accurate metadata immediately.
    // Only groups present in |used_group_ids| are materialised.
    // ------------------------------------------------------------------
    auto hexToColor = ^NSColor *(const std::string &hex) {
        if (hex.size() != 9 || hex[0] != '#') { return [NSColor labelColor]; }
        auto parse = [](char c) -> int { return c >= '0' && c <= '9' ? c - '0' : (toupper(c) - 'A' + 10); };
        int r = parse(hex[1])*16 + parse(hex[2]);
        int g = parse(hex[3])*16 + parse(hex[4]);
        int b = parse(hex[5])*16 + parse(hex[6]);
        int a = parse(hex[7])*16 + parse(hex[8]);
        return [NSColor colorWithRed:r/255.0 green:g/255.0 blue:b/255.0 alpha:a/255.0];
    };

    std::unordered_set<std::string> precreated;
    for (const auto &gs : meta.groups) {
        if (!used_group_ids.count(gs.group_id)) { continue; }
        NSString *gidStr = [NSString stringWithUTF8String:gs.group_id.c_str()];
        if (![mgr groupTitleForId:gidStr]) {
            NSColor *tint = hexToColor(gs.tint_hex);
            [mgr createGroupWithId:gidStr title:[NSString stringWithUTF8String:gs.title.c_str()] tint:tint];
            precreated.insert(gs.group_id);
        }
        // Register closed tabs into manager
        for (const auto &ws : gs.closed_tabs) {
            [mgr registerClosedTabSnapshot:ws];
        }
    }

    // Map group_id -> anchor NSWindow*
    std::unordered_map<std::string, NSWindow *> anchor_for_group;

    // Keep pointer to the window that was most recently active (first entry).
    NSWindow *lastActiveWindow = nil;

    // Iterate in order – meta.windows[0] is considered last active by capture logic.
    for (size_t idx = 0; idx < meta.windows.size(); ++idx) {
        const WindowSnapshot &ws = meta.windows[idx];

        // 1) Build UI & load conversation
        auto ui = std::make_shared<launcher::ui::MacOSChatUI>();
        ui->initialize();
        if (!ws.conversation_id.empty()) {
            auto convJsonOpt = launcher::core::persistence::ConversationStore::instance().loadConversation(ws.conversation_id);
            if (convJsonOpt) { ui->loadConversationFromJson(*convJsonOpt); }
        }
        ui->show();

        // 2) Native window setup
        NSWindow *newWin = ui->getNativeWindow();
        if (!newWin) { continue; }

        // Apply geometry – this may be ignored later if window gets tab-grouped.
        NSRect frame = NSMakeRect(ws.frame_x, ws.frame_y, ws.frame_w, ws.frame_h);
        [newWin setFrame:frame display:NO];

        // 3) Determine logical tab-group key (empty string ⇒ standalone window)
        std::string groupKey = ws.group_id;
        if (groupKey.empty()) {
            // Use the window uuid to guarantee uniqueness so different standalone
            // windows do not collide in the map.
            groupKey = ws.uuid;
        }

        // ------------------------------------------------------------------
        // Assign a *temporary* unique tabbingIdentifier per logical group so
        // macOS automatic window-tabbing cannot merge unrelated groups while we
        // are still reconstructing the session.  All identifiers are
        // normalised back to "ChatWindow" once the restore is complete.
        // ------------------------------------------------------------------
        if (@available(macOS 10.12, *)) {
            NSString *tempId = [NSString stringWithFormat:@"ChatWindow-%s", groupKey.c_str()];
            newWin.tabbingIdentifier = tempId;
        }

        auto it = anchor_for_group.find(groupKey);
        NSString *targetGroupId = nil;

        if (it == anchor_for_group.end()) {
            // First window of this group – make it the anchor
            anchor_for_group[groupKey] = newWin;

            // Register with TabGroupManager (preserve title tint later if required)
            if (!ws.group_id.empty()) {
                targetGroupId = [NSString stringWithUTF8String:ws.group_id.c_str()];
            }
            [mgr addWindow:newWin toGroup:targetGroupId];

        } else {
            // Subsequent window – tab it onto anchor
            NSWindow *anchorWin = it->second;

            // Ensure destination group exists
            targetGroupId = [mgr groupIdForWindow:anchorWin];
            if (!targetGroupId) {
                targetGroupId = [mgr createGroupWithTitle:nil tint:[NSColor systemTealColor]];
                [mgr addWindow:anchorWin toGroup:targetGroupId];
            }

            [mgr addWindow:newWin toGroup:targetGroupId];

            if (@available(macOS 10.12, *)) {
                [anchorWin addTabbedWindow:newWin ordered:NSWindowAbove];
            }
        }

        // 4) Track last active pointer
        if (idx == 0) { lastActiveWindow = newWin; }

        // 5) Keep UI alive
        pImpl_->restored.push_back(ui);
    }

    if (lastActiveWindow) {
        [mgr setSelectedWindow:lastActiveWindow];
        [lastActiveWindow makeKeyAndOrderFront:nil];
    }

    // ------------------------------------------------------------------
    // Phase-2: After all windows are correctly grouped, restore the generic
    // identifier so future windows can again participate in automatic tabbing.
    // ------------------------------------------------------------------
    if (@available(macOS 10.12, *)) {
        for (NSWindow *win in [NSApp windows]) {
            if ([win.tabbingIdentifier hasPrefix:@"ChatWindow-"]) {
                win.tabbingIdentifier = @"ChatWindow";
            }
        }
    }

    // Restore closed-window history so user can still reopen earlier tabs.
    pImpl_->closedStack = std::move(meta.closed_stack);

    return true;
}

// Adds a window snapshot to the closed-tab history so that it can be reopened.
void SessionAutosaveManager::recordClosedWindow(NSWindow *window) {
    if (!window) { return; }
    WindowSnapshot ws = snapshotForWindow(window);

    // Prevent duplicates for same conversation_id + frame combo in quick succession.
    if (!pImpl_->closedStack.empty()) {
        const auto &last = pImpl_->closedStack.back();
        if (last.conversation_id == ws.conversation_id &&
            fabs(last.frame_x - ws.frame_x) < 1e-3 &&
            fabs(last.frame_y - ws.frame_y) < 1e-3) {
            return; // Skip duplicate entry
        }
    }

    pImpl_->closedStack.push_back(std::move(ws));

    // Enforce maximum history length
    const size_t kMaxClosed = 10;
    if (pImpl_->closedStack.size() > kMaxClosed) {
        // Remove oldest entries at the beginning
        pImpl_->closedStack.erase(pImpl_->closedStack.begin(),
                                  pImpl_->closedStack.end() - kMaxClosed);
    }
}

// Pops last closed window snapshot and restores it. Returns true if restored.
bool SessionAutosaveManager::reopenLastClosedWindow(NSWindow *explicitAnchorWindow) {
    if (pImpl_->closedStack.empty()) { return false; }

    WindowSnapshot ws = pImpl_->closedStack.back();
    pImpl_->closedStack.pop_back();

    // Create UI and restore conversation
    auto ui = std::make_shared<launcher::ui::MacOSChatUI>();
    ui->initialize();
    if (!ws.conversation_id.empty()) {
        auto convJsonOpt = launcher::core::persistence::ConversationStore::instance().loadConversation(ws.conversation_id);
        if (convJsonOpt) {
            ui->loadConversationFromJson(*convJsonOpt);
        }
    }

    ui->show();

    // Integrate the new window into the existing tab group (if any)
    NSWindow *newWindow = ui->getNativeWindow();
    // Restore saved window frame immediately (may later be ignored if we end up tabbing)
    if (newWindow) {
        NSRect savedFrame = NSMakeRect(ws.frame_x, ws.frame_y, ws.frame_w, ws.frame_h);
        [newWindow setFrame:savedFrame display:NO];
    }

    if (newWindow && @available(macOS 10.12, *)) {
        // Ensure the window carries our identifier so macOS allows tabbing
        newWindow.tabbingIdentifier = @"ChatWindow";

        TabGroupManager *mgr = [TabGroupManager sharedManager];

        // --------------------------------------------------------------
        // Robust anchor-window resolution (Explicit anchor → Hybrid)
        // --------------------------------------------------------------

        // 0) If the caller supplied an explicit anchor window (i.e. the
        //     window that received the Cmd+Shift+T key-press), try to use it
        //     first.  This matches the user's expectation that the closed
        //     tab reopens in the same tab group they are currently looking
        //     at.  Fallback to the previous heuristic only when the explicit
        //     anchor is not suitable (e.g. detached during meantime).

        NSWindow *anchorWindow = explicitAnchorWindow;

        // Helper lambda to verify this is a valid chat window and not the one we just created
        auto IsEligible = ^BOOL(NSWindow *w) {
            if (!w || w == newWindow) { return NO; }
            if (@available(macOS 10.12, *)) {
                return [w.tabbingIdentifier isEqualToString:@"ChatWindow"];
            }
            return YES; // Pre-10.12: all windows are considered chat windows
        };

        // 0.5) Validate explicit anchor – discard if not eligible.
        if (!IsEligible(anchorWindow)) {
            anchorWindow = nil;
        }

        // 1) App-level selection (if still not set)
        if (!anchorWindow) {
            anchorWindow = [mgr selectedWindow];
        }

        // 2) Fall back to key window if needed
        if (!IsEligible(anchorWindow)) {
            NSWindow *keyWin = [NSApp keyWindow];
            anchorWindow = IsEligible(keyWin) ? keyWin : nil;
        }

        // 3) Try any window that belongs to the snapshot's original logical group
        if (!anchorWindow && !ws.group_id.empty()) {
            NSString *gid = [NSString stringWithUTF8String:ws.group_id.c_str()];
            NSArray<NSWindow *> *candidates = [mgr windowsInGroup:gid];
            for (NSWindow *w in candidates) {
                if (IsEligible(w)) { anchorWindow = w; break; }
            }
        }

        // 3.5) Validate anchor belongs to same logical group; otherwise discard.
        if (anchorWindow && !ws.group_id.empty()) {
            NSString *anchorGid = [mgr groupIdForWindow:anchorWindow];
            NSString *snapshotGid = [NSString stringWithUTF8String:ws.group_id.c_str()];
            if (anchorGid == nil || ![anchorGid isEqualToString:snapshotGid]) {
                anchorWindow = nil; // Force standalone because groups differ
            }
        }

        // 4) As a final fallback pick the first visible chat window BUT only when
        //    the snapshot is not associated with a specific tab group (group_id empty).
        if (!anchorWindow && ws.group_id.empty()) {
            for (NSWindow *w in [NSApp windows]) {
                if (!IsEligible(w)) { continue; }
                if ([w isVisible] || [w isMiniaturized]) { anchorWindow = w; break; }
            }
        }

        if (anchorWindow) {
            // Determine destination group id (create one if necessary)
            NSString *destGroupId = [mgr groupIdForWindow:anchorWindow];
            if (!destGroupId) {
                destGroupId = [mgr createGroupWithTitle:nil tint:[NSColor systemTealColor]];
                [mgr addWindow:anchorWindow toGroup:destGroupId];
            }

            // Pre-register the new window so observers see it in the correct group immediately
            [mgr addWindow:newWindow toGroup:destGroupId];

            // Add as a tab to the right-most position of the anchor window's group
            NSArray<NSWindow *> *tabWindows = anchorWindow.tabGroup.windows;
            NSWindow *anchor = tabWindows.lastObject ?: anchorWindow;
            [anchor addTabbedWindow:newWindow ordered:NSWindowAbove];

            // Select the newly reopened tab
            [mgr setSelectedWindow:newWindow];

            // Ensure the tab becomes active and window key.
            [newWindow makeKeyAndOrderFront:nil];

            // The window is now part of a tab group, so we skip manual frame placement.
        } else {
            // No suitable anchor – leave reopened window as standalone.
            if (newWindow) { [newWindow makeKeyAndOrderFront:nil]; }
        }
    }

    // Keep UI alive for the duration of the application session
    pImpl_->restored.push_back(ui);
    return true;
}

bool SessionAutosaveManager::reopenClosedWindowFromSnapshot(const launcher::core::persistence::WindowSnapshot &ws,
                                                            NSWindow *explicitAnchorWindow) {
    // Leverage the existing reopen logic by temporarily pushing snapshot onto
    // the stack so we can reuse reopenLastClosedWindow.  However we want to
    // preserve icon/tint metadata immediately after creation, so we apply it
    // manually below.

    // Insert snapshot
    pImpl_->closedStack.push_back(ws);
    bool ok = reopenLastClosedWindow(explicitAnchorWindow);
    if (!ok) {
        pImpl_->closedStack.pop_back();
        return false;
    }

    // If reopen succeeded we need to apply icon/tint because reopenLastClosedWindow
    // will not parse ui_state.
    NSWindow *win = [NSApp keyWindow];
    if (win) {
        try {
            const auto &state = ws.ui_state;
            if (!state.is_null()) {
                NSString *iconSymbol = nil;
                NSColor *tint = nil;
                if (state.contains("icon_symbol")) {
                    std::string sym = state["icon_symbol"].get<std::string>();
                    iconSymbol = [NSString stringWithUTF8String:sym.c_str()];
                }
                if (state.contains("tint_hex")) {
                    std::string hex = state["tint_hex"].get<std::string>();
                    // Simple hex → color parser (expects #RRGGBBAA or #RRGGBB)
                    auto ParseHexColor = ^NSColor *(const std::string &hx) {
                        if (hx.size() < 7) return (NSColor *)[NSColor labelColor];
                        unsigned int r=0,g=0,b=0,a=255;
                        if (hx.size() >= 9) {
                            sscanf(hx.c_str(), "#%02X%02X%02X%02X", &r,&g,&b,&a);
                        } else {
                            sscanf(hx.c_str(), "#%02X%02X%02X", &r,&g,&b);
                        }
                        return [NSColor colorWithRed:r/255.0 green:g/255.0 blue:b/255.0 alpha:a/255.0];
                    };
                    tint = ParseHexColor(hex);
                }
                launcher::ui::SessionMetadataManager::shared().applyMetadataForWindow(win, iconSymbol, tint, nil);
            }
        } catch (...) {
            // ignore
        }
    }

    return true;
}

} // namespace launcher::ui 