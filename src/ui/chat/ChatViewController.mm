#import "ChatViewController.h"
#import "MessageView.h"
#import <QuartzCore/QuartzCore.h> // Import QuartzCore for CADisplayLink
#include "../../core/util/debug.h"

// Custom collection view item for message display
@interface MessageItem : NSCollectionViewItem
@property (nonatomic, strong) MessageView* messageView;
@end

@implementation MessageItem

- (void)loadView {
    self.view = [[NSView alloc] initWithFrame:NSZeroRect];
}

- (void)viewDidLoad {
    [super viewDidLoad];
    
    // DBM("viewDidLoad", @"Setting up chat view");
    
    // Initialize message view
    self.messageView = [[MessageView alloc] initWithMessage:nil frame:self.view.bounds];
    self.messageView.autoresizingMask = NSViewWidthSizable | NSViewHeightSizable;
    [self.view addSubview:self.messageView];
}

- (void)setSelected:(BOOL)selected {
    [super setSelected:selected];
    [self.messageView setSelected:selected];
}

- (void)setHighlightState:(NSCollectionViewItemHighlightState)highlightState {
    [super setHighlightState:highlightState];
    [self.messageView setHighlighted:(highlightState != NSCollectionViewItemHighlightNone)];
}

@end

// Message collection view flow layout (to handle variable height cells)
@interface MessageFlowLayout : NSCollectionViewFlowLayout
@property (nonatomic, strong) NSMutableDictionary<NSIndexPath*, NSValue*>* itemSizeCache;
@property (nonatomic, strong) MessageView* sizingView; // For offscreen size calculation
@end

@implementation MessageFlowLayout

- (instancetype)init {
    self = [super init];
    if (self) {
        self.itemSizeCache = [NSMutableDictionary dictionary];
        self.minimumLineSpacing = 2.0;
        self.minimumInteritemSpacing = 0.0;
        self.sectionInset = NSEdgeInsetsMake(10, 0, 10, 0);
        
        // Create a reusable sizing view for calculating heights
        self.sizingView = [[MessageView alloc] initWithMessage:nil frame:NSMakeRect(0, 0, 300, 100)];
    }
    return self;
}

- (void)prepareLayout {
    [super prepareLayout];
    
    // This is intentionally left minimal for performance.
    // We'll calculate item sizes on demand in collectionView:layout:sizeForItemAtIndexPath:
}

- (void)invalidateLayout {
    [super invalidateLayout];
    [self.itemSizeCache removeAllObjects];
}

- (NSArray<NSCollectionViewLayoutAttributes *> *)layoutAttributesForElementsInRect:(NSRect)rect {
    NSArray<NSCollectionViewLayoutAttributes *> *layoutAttrs = [super layoutAttributesForElementsInRect:rect];
    
    // For performance, we are not modifying the layout attributes here
    return layoutAttrs;
}

- (BOOL)shouldInvalidateLayoutForBoundsChange:(NSRect)newBounds {
    // Only invalidate if width changes
    CGFloat oldWidth = self.collectionView.bounds.size.width;
    CGFloat newWidth = newBounds.size.width;
    
    // Use a very small delta to catch live-resize changes promptly
    return (fabs(oldWidth - newWidth) > 0.1);
}

@end

// Main implementation of ChatViewController
@implementation ChatViewController {
    BOOL _needsReload;
    NSOperationQueue* _backgroundQueue;
    CADisplayLink* _displayLink;
    NSMutableSet<NSIndexPath*>* _pendingInsertIndexPaths;
    dispatch_queue_t _sizingQueue;
    NSLock* _sizingLock;
    MessageView* _mainThreadSizingView; // For main-thread sizing operations
    NSMutableDictionary<NSString*, NSString*>* _targetMessageContent;
    NSMutableDictionary<NSString*, NSString*>* _displayedMessageContent;
    NSMutableDictionary<NSString*, NSNumber*>* _wordIndex;
    BOOL _currentlyStreaming;
    
    // Frame-rate synchronized streaming updates
    NSTimer* _streamingTimer;
    NSMutableSet<NSString*>* _pendingUpdateMessageIds;
    BOOL _isStreamingTimerActive;
    
    NSTextField *_thoughtOverlayLabel;
    NSTimer *_thoughtFadeTimer;
}

static NSString* const kMessageCellIdentifier = @"MessageCell";

#pragma mark - Initialization & Setup

- (instancetype)init {
    self = [super initWithNibName:nil bundle:nil];
    if (self) {
        _messages = [NSMutableArray array];
        _autoScrollToBottom = YES;
        _needsReload = NO;
        _backgroundQueue = [[NSOperationQueue alloc] init];
        _backgroundQueue.qualityOfService = NSQualityOfServiceUserInteractive;
        _backgroundQueue.maxConcurrentOperationCount = 1;
        _pendingInsertIndexPaths = [NSMutableSet set];
        _sizingQueue = dispatch_queue_create("com.chat.sizingQueue", DISPATCH_QUEUE_SERIAL);
        _sizingLock = [[NSLock alloc] init];
        _mainThreadSizingView = [[MessageView alloc] initWithMessage:nil frame:NSMakeRect(0, 0, 300, 100)]; 
        
        // Initialize animation dictionaries
        _targetMessageContent = [NSMutableDictionary dictionary];
        _displayedMessageContent = [NSMutableDictionary dictionary];
        _wordIndex = [NSMutableDictionary dictionary];
        
        // Initialize frame-synchronized streaming state
        _pendingUpdateMessageIds = [NSMutableSet set];
        _isStreamingTimerActive = NO;
    }
    return self;
}

- (void)loadView {
    // Create main view
    NSView* mainView = [[NSView alloc] initWithFrame:NSMakeRect(0, 0, 800, 600)];
    mainView.autoresizingMask = NSViewWidthSizable | NSViewHeightSizable;
    self.view = mainView;
}

- (void)viewDidLoad {
    [super viewDidLoad];
    
    // DBM("viewDidLoad", @"Setting up chat view");
    
    // Create flow layout
    MessageFlowLayout* flowLayout = [[MessageFlowLayout alloc] init];
    
    // Create collection view
    NSCollectionView* collectionView = [[NSCollectionView alloc] initWithFrame:NSZeroRect];
    collectionView.dataSource = self;
    collectionView.delegate = self;
    collectionView.collectionViewLayout = flowLayout;
    collectionView.selectable = YES;
    collectionView.allowsMultipleSelection = NO;
    collectionView.backgroundColors = @[[NSColor clearColor]];
    [collectionView registerClass:[MessageItem class] forItemWithIdentifier:kMessageCellIdentifier];
    self.collectionView = collectionView;
    
    // Create scroll view
    NSScrollView* scrollView = [[NSScrollView alloc] initWithFrame:self.view.bounds];
    scrollView.autoresizingMask = NSViewWidthSizable | NSViewHeightSizable;
    scrollView.hasVerticalScroller = YES;
    scrollView.hasHorizontalScroller = NO;
    scrollView.autohidesScrollers = YES;
    scrollView.documentView = collectionView;
    self.scrollView = scrollView;
    
    [self.view addSubview:scrollView];
    
    // Setup display link for smooth scrolling
    [self setupDisplayLink];
    
    // Setup streaming timer for frame-synchronized content updates
    [self setupStreamingTimer];
    
    // Observe scroll view for auto-scrolling
    [[NSNotificationCenter defaultCenter] addObserver:self 
                                             selector:@selector(scrollViewDidScroll:) 
                                                 name:NSScrollViewDidLiveScrollNotification 
                                               object:scrollView];
    
    // Setup thought overlay UI
    [self setupThoughtOverlay];
    
    // Listen for generic capability events (e.g., reasoning delta)
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(handleCapabilityEvent:)
                                                 name:@"KaiCapabilityEvent"
                                               object:nil];
}

- (void)viewWillAppear {
    [super viewWillAppear];
    [self updateLayout];
}

- (void)viewDidLayout {
    [super viewDidLayout];
    // Ensure collection view layout reflects the new bounds during live resize
    [self updateLayout];
}

- (void)setupDisplayLink {
    // Use CVDisplayLink for high-performance animation synchronization
    // For simplicity, we're using a timer in this example
    NSTimer* timer = [NSTimer timerWithTimeInterval:1.0/60.0
                                             target:self
                                           selector:@selector(displayLinkFired:)
                                           userInfo:nil
                                            repeats:YES];
    [[NSRunLoop currentRunLoop] addTimer:timer forMode:NSRunLoopCommonModes];
}

- (void)displayLinkFired:(id)sender {
    if (_needsReload) {
        _needsReload = NO;
        
        // If we have pending insertions, use batch updates
        NSMutableSet *pendingIndexPaths;
        
        // Get a copy to work with inside the block
        @synchronized(_pendingInsertIndexPaths) {
            if (_pendingInsertIndexPaths.count > 0) {
                pendingIndexPaths = [_pendingInsertIndexPaths mutableCopy];
                [_pendingInsertIndexPaths removeAllObjects];
            }
        }
        
        if (pendingIndexPaths && pendingIndexPaths.count > 0) {
            // Verify all index paths are valid
            BOOL allValid = YES;
            for (NSIndexPath *indexPath in pendingIndexPaths) {
                if ((NSUInteger)indexPath.item >= self.messages.count) {
                    allValid = NO;
                    break;
                }
            }
            
            if (allValid) {
                [self.collectionView performBatchUpdates:^{
                    [self.collectionView insertItemsAtIndexPaths:pendingIndexPaths];
                } completionHandler:^(BOOL __unused _finished) {
                    if (self.autoScrollToBottom) {
                        [self scrollToBottom:YES];
                    }
                }];
            } else {
                // Fall back to full reload
                [self.collectionView reloadData];
                
                if (self.autoScrollToBottom) {
                    [self scrollToBottom:YES];
                }
            }
        } else {
            // Fall back to full reload if needed
            [self.collectionView reloadData];
            
            if (self.autoScrollToBottom) {
                [self scrollToBottom:YES];
            }
        }
    }
}

- (void)dealloc {
     // Invalidate streaming timer
     [_streamingTimer invalidate];
     _streamingTimer = nil;
     
     // Clear all update sets
     [_pendingUpdateMessageIds removeAllObjects];
     
     // Clean up state dictionaries
     [self.targetMessageContent removeAllObjects];
     [self.displayedMessageContent removeAllObjects];
     [self.wordIndex removeAllObjects];
     
     // Remove observers
     [[NSNotificationCenter defaultCenter] removeObserver:self];
     [_thoughtFadeTimer invalidate];
}

#pragma mark - Public Methods

- (void)updateLayout {
    // Update layout on bounds change
    MessageFlowLayout* layout = (MessageFlowLayout*)self.collectionView.collectionViewLayout;
    [layout invalidateLayout];

    // Preserve current height so scrolling range remains intact; only adjust width.
    NSRect frame = self.collectionView.frame;
    frame.size.width = self.scrollView.contentSize.width;
    frame.origin.x = 0; // Ensure aligned to left
    self.collectionView.frame = frame;
}

- (void)reloadData {
    _needsReload = YES;
}

- (void)addMessage:(Message*)message {
    if (!message) return;
    
    // Use GCD for thread safety
    dispatch_async(dispatch_get_main_queue(), ^{
        NSInteger newIndex = self.messages.count;
        [self.messages addObject:message];
        
        // Add to pending insertions 
        NSIndexPath* indexPath = [NSIndexPath indexPathForItem:newIndex inSection:0];
        @synchronized(self->_pendingInsertIndexPaths) {
            [self->_pendingInsertIndexPaths addObject:indexPath];
        }
        
        // For performance, we batch UI updates
        self->_needsReload = YES;
        
        // Precompute size on background thread for better performance
        [self precomputeSizeForMessageAtIndexPath:indexPath];
    });
}

- (void)precomputeSizeForMessageAtIndexPath:(NSIndexPath*)indexPath {
    if ((NSUInteger)indexPath.item >= self.messages.count) return;
    
    // Get the message on the main thread before passing to background
    Message* message = self.messages[indexPath.item];
    
    // Get current collection view width for sizing
    CGFloat collectionViewWidth = self.collectionView.bounds.size.width;
    
    // Offload some work to background, but do actual UI layout on main thread
    dispatch_async(_sizingQueue, ^{
        // Any non-UI background work could go here
        
        // Actual sizing done on main thread
        dispatch_async(dispatch_get_main_queue(), ^{
            // Skip if already cached
            MessageFlowLayout* flowLayout = (MessageFlowLayout*)self.collectionView.collectionViewLayout;
            if ([flowLayout.itemSizeCache objectForKey:indexPath]) {
                return;
            }
            
            // Use main thread sizing view for Auto Layout
            self->_mainThreadSizingView.message = message;
            self->_mainThreadSizingView.preferredWidth = collectionViewWidth;
            
            // Trigger layout and get the computed height
            CGFloat height = [self->_mainThreadSizingView heightForWidth:collectionViewWidth];
            
            // Store size in cache
            NSSize size = NSMakeSize(collectionViewWidth, height);
            [flowLayout.itemSizeCache setObject:[NSValue valueWithSize:size] forKey:indexPath];
        });
    });
}

- (void)addMessages:(NSArray<Message*>*)messages {
    if (!messages || messages.count == 0) return;
    
    dispatch_async(dispatch_get_main_queue(), ^{
        NSInteger startIndex = self.messages.count;
        [self.messages addObjectsFromArray:messages];
        
        // Add all new messages to pending insertions
        @synchronized(self->_pendingInsertIndexPaths) {
            for (NSUInteger i = 0; i < messages.count; i++) {
                NSIndexPath* indexPath = [NSIndexPath indexPathForItem:(startIndex + i) inSection:0];
                [self->_pendingInsertIndexPaths addObject:indexPath];
                
                // Precompute size for better performance
                [self precomputeSizeForMessageAtIndexPath:indexPath];
            }
        }
        
        self->_needsReload = YES;
    });
}

- (void)clearMessages {
    dispatch_async(dispatch_get_main_queue(), ^{
        // Clean up all streaming state
        @synchronized(self->_pendingUpdateMessageIds) {
            [self->_pendingUpdateMessageIds removeAllObjects];
        }
        [self.targetMessageContent removeAllObjects];
        [self.displayedMessageContent removeAllObjects];
        [self.wordIndex removeAllObjects];
        
        // Pause the streaming timer
        [self _pauseStreamingTimerIfIdle];
        
        // Clear messages and pending insertions
        [self.messages removeAllObjects];
        @synchronized(self->_pendingInsertIndexPaths) {
            [self->_pendingInsertIndexPaths removeAllObjects];
        }
        
        // For clearing, it's better to do a full reload
        self->_needsReload = YES;
    });
}

- (void)scrollToBottom:(BOOL)animated {
    if (self.messages.count == 0) return;
    
    NSInteger lastSection = [self.collectionView numberOfSections] - 1;
    NSInteger lastItem = [self.collectionView numberOfItemsInSection:lastSection] - 1;
    
    if (lastItem >= 0) {
        NSIndexPath* lastIndexPath = [NSIndexPath indexPathForItem:lastItem inSection:lastSection];
        [self.collectionView scrollToItemsAtIndexPaths:[NSSet setWithObject:lastIndexPath] 
                                      scrollPosition:NSCollectionViewScrollPositionBottom];
    }
}

#pragma mark - NSScrollViewDelegate

- (void)scrollViewDidScroll:(NSNotification*)notification {
    NSScrollView* scrollView = notification.object;
    
    // Check if we're at the bottom using actual content height from layout
    CGFloat contentHeight = self.collectionView.collectionViewLayout.collectionViewContentSize.height;
    CGFloat scrollPosition = scrollView.contentView.bounds.origin.y + scrollView.bounds.size.height;
    CGFloat threshold = 20.0; // Threshold to consider "at bottom"
    
    self.autoScrollToBottom = (contentHeight - scrollPosition) <= threshold;
}

#pragma mark - Message Streaming Updates

// Helper method to find message index (can be private)
- (NSUInteger)_findMessageIndexById:(NSString *)messageId {
    for (NSUInteger i = 0; i < self.messages.count; i++) {
        Message* message = self.messages[i];
        if ([message.messageId isEqualToString:messageId]) {
            return i;
        }
    }
    return NSNotFound;
}

- (void)replaceMessageContent:(NSString*)messageId
               withNewContent:(NSString*)newContent
                      newType:(MessageType)newType {
    if (!messageId || newContent == nil) {
        DBM(@"Invalid parameters for replaceMessageContent");
        return;
    }

    // +++ Add logging here +++
    DBM(@"replaceMessageContent: Received chunk for ID %@: '%@'", messageId, newContent);
    // +++ End logging +++

    // Find the message index
    NSUInteger messageIndex = [self _findMessageIndexById:messageId];

    if (messageIndex == NSNotFound) {
        ERM(@"Message with ID %@ not found for content replacement", messageId);
        return;
    }

    // Get the message object
    Message* message = self.messages[messageIndex];

    // --- Frame-Rate Synchronized Streaming Logic ---
    
    // Track that we're streaming content to prevent excessive layout invalidation
    _currentlyStreaming = YES;

    if (newType == MessageType::TEXT) {
        // Update the target content with the latest full content received
        self.targetMessageContent[messageId] = newContent;

        // Initialize display state if this is the first text chunk
        if (!self.displayedMessageContent[messageId]) {
             // Start displayed content as empty
             self.displayedMessageContent[messageId] = @"";
             // Start word index at 0
             self.wordIndex[messageId] = @0;
             // Update the underlying message immediately to empty text for initial layout
             message.rawContent = @"";
             message.type = MessageType::TEXT;
             [message clearCache];
             
             // Ensure complete layout refresh when transitioning from typing indicator
             // Force MessageFlowLayout to recalculate size
             NSIndexPath* indexPath = [NSIndexPath indexPathForItem:messageIndex inSection:0];
             MessageFlowLayout* flowLayout = (MessageFlowLayout*)self.collectionView.collectionViewLayout;
             [flowLayout.itemSizeCache removeObjectForKey:indexPath];
             
             // Reload item to reflect initial empty state
             [self.collectionView reloadItemsAtIndexPaths:[NSSet setWithObject:indexPath]];
        } else {
            // If already displaying, just ensure the message type is TEXT
            if (message.type != MessageType::TEXT) {
                message.type = MessageType::TEXT;
            }
        }

        // Add to pending updates for the next timer frame
        @synchronized(_pendingUpdateMessageIds) {
            [_pendingUpdateMessageIds addObject:messageId];
        }
        
        // Ensure the timer is active
        [self _ensureStreamingTimerIsActive];

    } else {
        // Handle non-TEXT types (e.g., ERROR, TYPING_INDICATOR finalization)
        // Remove from pending updates
        @synchronized(_pendingUpdateMessageIds) {
            [_pendingUpdateMessageIds removeObject:messageId];
        }
        
        // Clean up animation state
        [self.targetMessageContent removeObjectForKey:messageId];
        [self.displayedMessageContent removeObjectForKey:messageId];
        [self.wordIndex removeObjectForKey:messageId];

        // Update the message model directly
        BOOL contentChanged = ![message.rawContent isEqualToString:newContent] || message.type != newType;
        if (contentChanged) {
            message.rawContent = newContent;
            message.type = newType;
            [message clearCache];

            // Reload the item
            NSIndexPath* indexPath = [NSIndexPath indexPathForItem:messageIndex inSection:0];
            MessageFlowLayout* flowLayout = (MessageFlowLayout*)self.collectionView.collectionViewLayout;
            [flowLayout.itemSizeCache removeObjectForKey:indexPath];
            [self.collectionView reloadItemsAtIndexPaths:[NSSet setWithObject:indexPath]];

            // Auto-scroll if needed
            if (self.autoScrollToBottom) {
                [self scrollToBottom:NO];
            }
        }
        
        // Check if timer should be paused
        [self _pauseStreamingTimerIfIdle];
    }
    
    _currentlyStreaming = NO;
    // --- End Frame-Rate Synchronized Streaming Logic ---
}
- (void)finalizeMessage:(NSString*)messageId {

    if (!messageId) {
        DBM(@"Invalid messageId for finalizeMessage");
        return;
    }

    // Find the message index
    NSUInteger messageIndex = [self _findMessageIndexById:messageId];
    if (messageIndex == NSNotFound) {
        // Message might have been cleared before finalization callback arrived
        // Still ensure cleanup happens
        ERM(@"Message with ID %@ not found for finalization, attempting cleanup.", messageId);
    }

    // --- Finalize Frame-Synchronized Streaming ---
    
    // 1. Remove from pending updates to stop further processing
    @synchronized(_pendingUpdateMessageIds) {
        [_pendingUpdateMessageIds removeObject:messageId];
    }

    // 2. Ensure the message model has the absolute final content
    NSString *finalContent = self.targetMessageContent[messageId];
    BOOL contentChanged = NO;
    if (messageIndex != NSNotFound) {
        Message* message = self.messages[messageIndex];
        // Use targetContent as the source of truth for the final state
        if (finalContent && ![message.rawContent isEqualToString:finalContent]) {
            message.rawContent = finalContent;
            // Ensure final type is TEXT (unless error occurred)
            if (message.type != MessageType::ERROR) { // Avoid overwriting error state
                 message.type = MessageType::TEXT;
            }
            [message clearCache];
            contentChanged = YES;
        } else if (!finalContent && message.rawContent.length > 0) {
            // If target somehow nil, but we displayed something, ensure type is TEXT
            if (message.type != MessageType::ERROR && message.type != MessageType::TEXT) {
                message.type = MessageType::TEXT;
                contentChanged = YES; // Type change might affect rendering/layout
            }
        }
    }

    // 3. Clean up all animation state dictionaries for this message
    [self.targetMessageContent removeObjectForKey:messageId];
    [self.displayedMessageContent removeObjectForKey:messageId];
    [self.wordIndex removeObjectForKey:messageId];

    // Prevent excessive layout invalidations during finalization
    _currentlyStreaming = YES;
    
    // 4. Reload the item one last time if content changed or index exists
    if (messageIndex != NSNotFound && contentChanged) {
        NSIndexPath* indexPath = [NSIndexPath indexPathForItem:messageIndex inSection:0];
        MessageFlowLayout* flowLayout = (MessageFlowLayout*)self.collectionView.collectionViewLayout;
        [flowLayout.itemSizeCache removeObjectForKey:indexPath]; // Ensure final size calculation

        [self.collectionView reloadItemsAtIndexPaths:[NSSet setWithObject:indexPath]];

        // Animated scroll to bottom after final update
        if (self.autoScrollToBottom) {
            [self scrollToBottom:YES]; // Use animated scroll for final state
        }
    } else if (messageIndex != NSNotFound && self.autoScrollToBottom) {
         // Scroll even if content didn't change, ensures view is correct
         [self scrollToBottom:YES];
    }
    
    _currentlyStreaming = NO;
    
    // 5. Check if timer should be paused
    [self _pauseStreamingTimerIfIdle];
    // --- End Finalize Frame-Synchronized Streaming ---
}

#pragma mark - NSCollectionViewDataSource

- (NSInteger)numberOfSectionsInCollectionView:(NSCollectionView *)collectionView {
    return 1;
}

- (NSInteger)collectionView:(NSCollectionView *)collectionView numberOfItemsInSection:(NSInteger)section {
    return self.messages.count;
}

- (NSCollectionViewItem *)collectionView:(NSCollectionView *)collectionView 
     itemForRepresentedObjectAtIndexPath:(NSIndexPath *)indexPath {
    
    MessageItem* item = [collectionView makeItemWithIdentifier:kMessageCellIdentifier forIndexPath:indexPath];
    Message* message = self.messages[indexPath.item];
    
    item.messageView.message = message;
    item.messageView.preferredWidth = collectionView.bounds.size.width;
    
    return item;
}

#pragma mark - NSCollectionViewDelegateFlowLayout

- (NSSize)collectionView:(NSCollectionView *)collectionView 
                  layout:(NSCollectionViewLayout *)collectionViewLayout 
  sizeForItemAtIndexPath:(NSIndexPath *)indexPath {
    
    if ((NSUInteger)indexPath.item >= self.messages.count) {
        return NSMakeSize(collectionView.bounds.size.width, 50); // Default fallback
    }
    
    // Get cached size from layout if available
    MessageFlowLayout* flowLayout = (MessageFlowLayout*)collectionViewLayout;
    NSValue* cachedSizeValue = [flowLayout.itemSizeCache objectForKey:indexPath];
    
    // During streaming updates, prefer using cached sizes if available to prevent flickering
    if (_currentlyStreaming && cachedSizeValue) {
        return [cachedSizeValue sizeValue];
    }
    
    if (cachedSizeValue) {
        return [cachedSizeValue sizeValue];
    }
    
    // Calculate height for this message using main thread sizing view
    Message* message = self.messages[indexPath.item];
    
    _mainThreadSizingView.message = message;
    _mainThreadSizingView.preferredWidth = collectionView.bounds.size.width;
    
    CGFloat height = [_mainThreadSizingView heightForWidth:collectionView.bounds.size.width];
    
    NSSize size = NSMakeSize(collectionView.bounds.size.width, height);
    
    // Cache the size
    [flowLayout.itemSizeCache setObject:[NSValue valueWithSize:size] forKey:indexPath];
    
    return size;
}

- (void)collectionView:(NSCollectionView *)collectionView didSelectItemsAtIndexPaths:(NSSet<NSIndexPath *> *)indexPaths {
    // Handle selection
}

// Setup frame-rate synchronized streaming timer
- (void)setupStreamingTimer {
    // Create but don't schedule the timer yet
    _streamingTimer = [NSTimer timerWithTimeInterval:1.0/60.0
                                             target:self
                                           selector:@selector(_updateStreamingFrame:)
                                           userInfo:nil
                                            repeats:YES];
    // Timer is initially inactive
    _isStreamingTimerActive = NO;
}

#pragma mark - Frame-Rate Synchronized Streaming

// Ensure streaming timer is active
- (void)_ensureStreamingTimerIsActive {
    if (!_isStreamingTimerActive) {
        // Invalidate any existing timer
        [_streamingTimer invalidate];
        
        // Create and schedule a new timer
        _streamingTimer = [NSTimer timerWithTimeInterval:1.0/60.0
                                                 target:self
                                               selector:@selector(_updateStreamingFrame:)
                                               userInfo:nil
                                                repeats:YES];
        [[NSRunLoop currentRunLoop] addTimer:_streamingTimer forMode:NSRunLoopCommonModes];
        
        _isStreamingTimerActive = YES;
        // DBM(@"Activated streaming timer");
    }
}

// Check if there are pending updates and pause if idle
- (void)_pauseStreamingTimerIfIdle {
    @synchronized(_pendingUpdateMessageIds) {
        if (_pendingUpdateMessageIds.count == 0 && _isStreamingTimerActive) {
            [_streamingTimer invalidate];
            _streamingTimer = nil;
            _isStreamingTimerActive = NO;
            // DBM(@"Paused streaming timer - no pending updates");
        }
    }
}

// Process the next content chunk for a specific message ID
- (BOOL)_processNextChunkForMessageId:(NSString *)messageId {
    if (!messageId) {
        return NO;
    }

    // Safely get state
    NSString *targetContent = self.targetMessageContent[messageId];
    NSString *displayedContent = self.displayedMessageContent[messageId];
    NSNumber *currentIndexNum = self.wordIndex[messageId];

    // If state is inconsistent, stop processing
    if (!targetContent || !displayedContent || !currentIndexNum) {
        ERM(@"Animation state inconsistent for message ID %@", messageId);
        return NO;
    }

    NSUInteger currentIndex = [currentIndexNum unsignedIntegerValue];

    // Check if we have displayed all the *currently available* target content
    if (currentIndex >= targetContent.length) {
        // No more text to display *for now*
        return NO;
    }

    // --- Simplified Chunk Determination ---
    // Animate by a fixed number of characters or up to the next word.
    const NSUInteger kCharsPerFrame = 5; // Number of characters to reveal per frame (tuneable)
    NSUInteger nextChunkEndIndex = MIN(currentIndex + kCharsPerFrame, targetContent.length);
    
    // Optional: Try to break at a word boundary if within the next `kCharsPerFrame` characters
    // to make animation feel more natural.
    if (nextChunkEndIndex < targetContent.length) {
        NSRange searchRange = NSMakeRange(nextChunkEndIndex, MIN(kCharsPerFrame * 2, targetContent.length - nextChunkEndIndex));
        NSRange spaceRange = [targetContent rangeOfCharacterFromSet:[NSCharacterSet whitespaceAndNewlineCharacterSet]
                                                          options:0
                                                            range:searchRange];
        if (spaceRange.location != NSNotFound && spaceRange.location > currentIndex) {
            // If a space is found soon after the kCharsPerFrame limit, extend to include it.
            // This helps break on word boundaries more often.
            if (spaceRange.location < currentIndex + kCharsPerFrame + (kCharsPerFrame / 2) ) { // Heuristic: if space is close
                 nextChunkEndIndex = spaceRange.location + 1; // Include the space
            }
        }
    }
    // --- End Simplified Chunk Determination ---


    // Get the chunk to append
    NSString *nextChunk = [targetContent substringWithRange:NSMakeRange(currentIndex, nextChunkEndIndex - currentIndex)];

    // Update displayed content state
    NSString *newDisplayedContent = [displayedContent stringByAppendingString:nextChunk];
    self.displayedMessageContent[messageId] = newDisplayedContent;
    self.wordIndex[messageId] = @(nextChunkEndIndex);

    // Update message model with the new text content
    NSUInteger messageIndex = [self _findMessageIndexById:messageId];
    if (messageIndex != NSNotFound) {
        Message* message = self.messages[messageIndex];
        message.rawContent = newDisplayedContent;
        // Ensure type is TEXT during animation
        if (message.type != MessageType::TEXT) { message.type = MessageType::TEXT; }
        [message clearCache]; // Clear layout cache
    }
    
    // Return true if there's more content to process
    return nextChunkEndIndex < targetContent.length;
}

// Main timer callback - process all pending updates in a batch
- (void)_updateStreamingFrame:(NSTimer *)timer {
    // Early exit if no pending updates (shouldn't happen due to pause/unpause logic)
    @synchronized(_pendingUpdateMessageIds) {
        if (_pendingUpdateMessageIds.count == 0) {
            [self _pauseStreamingTimerIfIdle];
            return;
        }
        
        // Make a local copy of pending updates and clear the set
        NSSet<NSString *> *messageIdsToUpdate = [_pendingUpdateMessageIds copy];
        [_pendingUpdateMessageIds removeAllObjects];
        
        // Track message IDs that need another update in the next frame
        NSMutableSet<NSString *> *messageIdsNeedingMoreUpdates = [NSMutableSet set];
        
        // Track which items in the collection view need UI updates
        NSMutableSet<NSIndexPath *> *indexPathsToUpdate = [NSMutableSet set];
        
        // Process a chunk for each message ID
        for (NSString *messageId in messageIdsToUpdate) {
            // Process the next chunk and find out if more updates are needed
            BOOL needsMoreUpdates = [self _processNextChunkForMessageId:messageId];
            
            // Find the index path for this message
            NSUInteger messageIndex = [self _findMessageIndexById:messageId];
            if (messageIndex != NSNotFound) {
                NSIndexPath *indexPath = [NSIndexPath indexPathForItem:messageIndex inSection:0];
                [indexPathsToUpdate addObject:indexPath];
            }
            
            // If more content remains, re-add to pending updates for next frame
            if (needsMoreUpdates) {
                [messageIdsNeedingMoreUpdates addObject:messageId];
            }
        }
        
        // Re-add messages that need more updates
        if (messageIdsNeedingMoreUpdates.count > 0) {
            [_pendingUpdateMessageIds unionSet:messageIdsNeedingMoreUpdates];
        } else {
            // If no more updates, pause the timer
            [self _pauseStreamingTimerIfIdle];
        }
        
        // Update the UI in a batch if we have items to update
        if (indexPathsToUpdate.count > 0) {
            _currentlyStreaming = YES; // Prevent excessive layout recalculations
            
            // Get the current scroll position before update
            CGFloat currentScrollOffset = self.scrollView.contentView.bounds.origin.y;
            BOOL wasAtBottom = self.autoScrollToBottom;
            
            // First, update all MessageItems directly without full reload
            for (NSIndexPath *indexPath in indexPathsToUpdate) {
                if ((NSUInteger)indexPath.item < self.messages.count) {
                    MessageItem *item = (MessageItem *)[self.collectionView itemAtIndexPath:indexPath];
                    if (item && item.messageView) {
                        // Update message content directly
                        item.messageView.message = self.messages[indexPath.item];
                        
                        // Mark as needing layout, but don't force it immediately
                        [item.messageView setNeedsLayout:YES];
                    }
                }
            }
            
            // Clear cache and force layout update, but in a single batch
            MessageFlowLayout* flowLayout = (MessageFlowLayout*)self.collectionView.collectionViewLayout;
            for (NSIndexPath *indexPath in indexPathsToUpdate) {
                [flowLayout.itemSizeCache removeObjectForKey:indexPath];
            }
            
            // Use performBatchUpdates for smoother animation
            [self.collectionView performBatchUpdates:^{
                // During update phase, no explicit calls needed - layout system will handle it
            } completionHandler:^(BOOL finished) {
                // If was previously at bottom, maintain position
                if (wasAtBottom) {
                    [self scrollToBottom:NO];
                } else {
                    // Otherwise try to maintain scroll position
                    // This helps prevent jarring movement during streaming
                    [self.scrollView.contentView scrollToPoint:NSMakePoint(0, currentScrollOffset)];
                    [self.scrollView reflectScrolledClipView:self.scrollView.contentView];
                }
            }];
            
            _currentlyStreaming = NO;
        }
    }
}

#pragma mark - Thought overlay

- (void)setupThoughtOverlay {
    // Create label lazily
    _thoughtOverlayLabel = [[NSTextField alloc] initWithFrame:NSZeroRect];
    _thoughtOverlayLabel.bezeled = NO;
    _thoughtOverlayLabel.drawsBackground = YES;
    _thoughtOverlayLabel.backgroundColor = [[NSColor windowBackgroundColor] colorWithAlphaComponent:0.9];
    _thoughtOverlayLabel.textColor = [NSColor secondaryLabelColor];
    _thoughtOverlayLabel.font = [NSFont systemFontOfSize:11 weight:NSFontWeightMedium];
    _thoughtOverlayLabel.editable = NO;
    _thoughtOverlayLabel.selectable = NO;
    _thoughtOverlayLabel.wantsLayer = YES;
    _thoughtOverlayLabel.alphaValue = 0.0; // initially hidden
    _thoughtOverlayLabel.layer.cornerRadius = 6.0;
    _thoughtOverlayLabel.layer.masksToBounds = YES;
    _thoughtOverlayLabel.lineBreakMode = NSLineBreakByTruncatingTail;

    // Add to main view covering collectionView area (top right corner)
    [self.view addSubview:_thoughtOverlayLabel positioned:NSWindowAbove relativeTo:self.collectionView];
    _thoughtOverlayLabel.translatesAutoresizingMaskIntoConstraints = NO;

    const CGFloat margin = 12.0;
    [NSLayoutConstraint activateConstraints:@[
        [_thoughtOverlayLabel.trailingAnchor constraintEqualToAnchor:self.view.trailingAnchor constant:-margin],
        [_thoughtOverlayLabel.topAnchor constraintEqualToAnchor:self.view.topAnchor constant:margin]
    ]];
}

// Generic capability event handler
- (void)handleCapabilityEvent:(NSNotification *)note {
    NSString *kind = note.userInfo[@"kind"];
    if (![kind isEqualToString:@"ReasoningDelta"]) { return; }

    NSString *delta = note.userInfo[@"text"];
    if (!delta || delta.length == 0) { return; }

    _thoughtOverlayLabel.stringValue = delta;
    _thoughtOverlayLabel.alphaValue = 1.0;
    [_thoughtOverlayLabel invalidateIntrinsicContentSize];

    if (_thoughtFadeTimer) { [_thoughtFadeTimer invalidate]; }
    __weak __typeof(self) weakSelf = self;
    _thoughtFadeTimer = [NSTimer scheduledTimerWithTimeInterval:1.2 repeats:NO block:^(NSTimer * _Nonnull timer) {
        __strong __typeof(weakSelf) strongSelf = weakSelf;
        if (!strongSelf) { return; }
        [NSAnimationContext runAnimationGroup:^(NSAnimationContext * _Nonnull context) {
            context.duration = 0.4;
            strongSelf->_thoughtOverlayLabel.animator.alphaValue = 0.0;
        } completionHandler:nil];
    }];
}

@end