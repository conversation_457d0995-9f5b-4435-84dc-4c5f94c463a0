#import <Cocoa/Cocoa.h>

// Forward declarations
@class ChatRootViewController;
@class ChatContentViewController;

// Forward declare C++ class
namespace launcher { namespace ui { class MacOSChatUI; } }

NS_ASSUME_NONNULL_BEGIN

/**
 * TopChatWindowController manages the window, toolbar, and tabbed window behavior for the chat UI.
 * It creates a window with a ChatRootViewController as its content view controller.
 */
@interface TopChatWindowController : NSWindowController <NSToolbarDelegate, NSWindowDelegate>

// Set the C++ UI instance (weak reference)
- (void)setUiInstance:(launcher::ui::MacOSChatUI*)uiInstance;

// Access to child view controllers for MacOSChatUI to use
@property (nonatomic, readonly) ChatRootViewController *chatRootVC;
@property (nonatomic, readonly) ChatContentViewController *contentVC;

// UI Actions
- (void)focusOnChatInput;
- (void)addTestMessages;
- (void)toggleSidebar:(nullable id)sender;

// Toolbar-related methods
- (void)setupToolbar;
- (void)setupSidebarToggleButton;

// Sets the logical TabGroupManager id that the *next* window created by a
// TopChatWindowController instance should join.  Used by ChatWindowFactory so
// the controller can pick up the desired group during its init sequence.
+ (void)setNextWindowInitialGroupId:(nullable NSString *)groupId;

@end

NS_ASSUME_NONNULL_END 