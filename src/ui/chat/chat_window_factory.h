#pragma once

#include <memory>

#ifdef __OBJC__
@class NSWindow;
#else
class NSWindow;
#endif

namespace launcher {
namespace ui {

class MacOSChatUI;  // Forward declaration

/**
 * ChatWindowFactory centralises the logic for creating new chat windows or tabs.
 * Every call path (status-bar, menu, hot-keys) should delegate here to guarantee
 * consistent grouping, autosave behaviour and lifecycle management.
 */
class ChatWindowFactory {
 public:
    /**
     * Creates a new top-level chat window (⌘N equivalent).
     * Returns a fully initialised and visible MacOSChatUI instance or nullptr
     * on failure.
     */
    static std::shared_ptr<MacOSChatUI> createNewWindow();

    /**
     * Creates a new tab in the same tab-group as |anchorWindow| (⌘T equivalent).
     * If |anchorWindow| is nil the call falls back to createNewWindow().
     */
    static std::shared_ptr<MacOSChatUI> createNewTab(NSWindow *anchorWindow);
};

}  // namespace ui
}  // namespace launcher 