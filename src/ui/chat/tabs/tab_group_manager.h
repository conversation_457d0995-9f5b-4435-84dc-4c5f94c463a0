// Header for TabGroupManager
#ifndef TAB_GROUP_MANAGER_H
#define TAB_GROUP_MANAGER_H

#import <Cocoa/Cocoa.h>
#include "../../../core/persistence/session_serializer.h"

NS_ASSUME_NONNULL_BEGIN

// Notification names for observers (posted on main thread)
extern NSString * const kTabGroupManagerTabAddedNotification;       // userInfo: { "window": NSWindow, "groupId": NSString }
extern NSString * const kTabGroupManagerTabRemovedNotification;     // userInfo: { "window": NSWindow, "groupId": NSString }
extern NSString * const kTabGroupManagerSelectionChangedNotification; // userInfo: { "window": NSWindow, "groupId": NSString }
extern NSString * const kTabGroupManagerGroupAddedNotification;     // userInfo: { "groupId": NSString }
extern NSString * const kTabGroupManagerGroupTitleChangedNotification; // userInfo: { "groupId": NSString, "title": NSString }
extern NSString * const kTabGroupManagerTabClosedNotification; // userInfo: { "groupId": NSString, "conversationId": NSString }

// Forward declaration for closed-tab info model.
@class ClosedTabInfo;

@interface TabGroupManager : NSObject

+ (instancetype)sharedManager;

// Group management
- (NSString *)createGroupWithTitle:(NSString *)title tint:(NSColor *)tint; // returns groupId
- (NSString *)createGroupWithId:(NSString *)groupId title:(NSString *)title tint:(NSColor *)tint;
- (NSArray<NSString *> *)allGroupIds;
- (nullable NSString *)groupTitleForId:(NSString *)groupId;
- (nullable NSColor *)groupTintForId:(NSString *)groupId;
- (void)setTitle:(NSString *)title forGroupId:(NSString *)groupId;

// Tab management
- (void)addWindow:(NSWindow *)window toGroup:(nullable NSString *)groupId; // if nil, puts in default group
- (void)removeWindow:(NSWindow *)window;
// Detach a window from its current logical group without considering it a real
// closed tab.  Unlike removeWindow:, this method does NOT add the tab to the
// closed-tab list and therefore does not emit the TabClosed notification.
- (void)detachWindow:(NSWindow *)window;

- (NSArray<NSWindow *> *)windowsInGroup:(NSString *)groupId;
- (nullable NSString *)groupIdForWindow:(NSWindow *)window;

// Selection
- (void)setSelectedWindow:(NSWindow *)window;
- (nullable NSWindow *)selectedWindow;

// Convenience ‑ returns an already-open window that shows the given conversation id, or nil.
// Does a linear scan over all groups and their windows.  Callers can then simply bring the
// returned window to front instead of creating a duplicate tab.
- (nullable NSWindow *)windowForConversationId:(NSString *)conversationId;

// MARK: ‑ Closed-tab accessors
- (NSArray<ClosedTabInfo *> *)closedTabsInGroup:(NSString *)groupId;
// Reopen the given closed tab (typically triggered by sidebar click).  Returns YES on success.
- (BOOL)reopenClosedTab:(ClosedTabInfo *)info anchorWindow:(nullable NSWindow *)anchorWindow;

// Convenience – searches the closed-tab lists for the given conversation id and
// reopens it (delegating to reopenClosedTab:).  Returns YES if a closed entry was
// found and successfully restored.
- (BOOL)reopenClosedConversationId:(NSString *)conversationId
                        anchorWindow:(nullable NSWindow *)anchorWindow;

// Register a closed tab snapshot (used during session restore)
- (void)registerClosedTabSnapshot:(const launcher::core::persistence::WindowSnapshot &)snapshot;

// Permanently forget a closed tab so it is no longer restored on launch.
- (void)removeClosedTab:(ClosedTabInfo *)info;

@end

NS_ASSUME_NONNULL_END

#endif // TAB_GROUP_MANAGER_H 