// Objective-C++ implementation of TabGroupManager

#import "tab_group_manager.h"
#include "../../../core/util/debug.h"
#import "../session_metadata_manager.h"
#import "../session_autosave_manager.h"
#include <objc/message.h>
#include <nlohmann/json.hpp>
#import "../macos_chat_ui.h"

// MARK: ‑ Notification strings
NSString * const kTabGroupManagerTabAddedNotification         = @"TabGroupManagerTabAddedNotification";
NSString * const kTabGroupManagerTabRemovedNotification       = @"TabGroupManagerTabRemovedNotification";
NSString * const kTabGroupManagerSelectionChangedNotification = @"TabGroupManagerSelectionChangedNotification";
NSString * const kTabGroupManagerGroupAddedNotification       = @"TabGroupManagerGroupAddedNotification";
NSString * const kTabGroupManagerGroupTitleChangedNotification = @"TabGroupManagerGroupTitleChangedNotification";
NSString * const kTabGroupManagerTabClosedNotification         = @"TabGroupManagerTabClosedNotification";

// MARK: ‑ Internal helper classes
@interface ClosedTabInfo : NSObject
@property (nonatomic, copy)   NSString *conversationId;
@property (nonatomic, copy)   NSString *title;
@property (nonatomic, copy)   NSString *iconSymbolName;
@property (nonatomic, strong) NSColor  *tintColor;
@property (nonatomic, assign) NSRect    frame;
@end

@implementation ClosedTabInfo
@end

@interface TabGroup : NSObject
@property (nonatomic, copy)   NSString      *groupId;
@property (nonatomic, copy)   NSString      *title;
@property (nonatomic, strong) NSColor       *tint;
@property (nonatomic, strong) NSPointerArray *windows; // Ordered weak references
@property (nonatomic, strong) NSMutableArray<ClosedTabInfo *> *closedTabs; // NEW
@property (nonatomic, assign) BOOL           autoTitle; // YES ⇒ title is managed automatically ("N Tabs")
@end

@implementation TabGroup
@end

// MARK: ‑ TabGroupManager private interface
@interface TabGroupManager ()
@property (nonatomic, strong) NSMutableArray<TabGroup *> *groups; // Preserves ordering
@property (nonatomic, weak)   NSWindow *currentSelectedWindow;

// Maps a window that was just detached (via KVO removal) to the group id it came from.
// This allows removeWindow: (invoked later during the actual close) to recover the
// correct logical group even though the weak pointer was already removed.
@property (nonatomic, strong) NSMapTable<NSWindow *, NSString *> *recentlyDetachedMap;

// Updates an auto-titled group's dynamic title ("N Tabs") when needed.
- (void)updateAutoTitleForGroupIfNeeded:(TabGroup *)group;
@end

// Forward declaration for helper used inside implementation
static BOOL PointerArrayContainsWindow(NSPointerArray *array, NSWindow *window);

// Forward declaration so we can use the helper before its definition.
static NSColor *RandomGroupTint(void);

// Provide forward declaration of helper for colour hex conversion
static NSString *HexFromColor(NSColor *c);
static NSColor *ColorFromHex(NSString *hex);

// Forward declaration so we can extract conversation id from a window
static NSString *ConversationIdForWindow(NSWindow *window);

// Definition placed before @implementation so it is visible to all methods.
static BOOL PointerArrayContainsWindow(NSPointerArray *array, NSWindow *window) {
    if (!window) { return NO; }
    for (NSWindow *w in array.allObjects) {
        if (w == window) { return YES; }
    }
    return NO;
}

@implementation TabGroupManager

+ (instancetype)sharedManager {
    static TabGroupManager *instance = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        instance = [[TabGroupManager alloc] initPrivate];
    });
    return instance;
}

// Private designated initialiser
- (instancetype)initPrivate {
    self = [super init];
    if (self) {
        _groups = [NSMutableArray array];
        _recentlyDetachedMap = [NSMapTable weakToStrongObjectsMapTable];
        // Create a default group so there is always at least one
        [self createGroupWithTitle:@"Default" tint:nil];
    }
    return self;
}

// Prevent accidental use of plain init
- (instancetype)init {
    @throw [NSException exceptionWithName:@"Singleton" reason:@"Use +sharedManager" userInfo:nil];
    return nil;
}

#pragma mark ‑ Group API

- (NSString *)createGroupWithTitle:(NSString *)title tint:(NSColor *)tint {
    // Generate unique id
    NSString *groupId = [[NSUUID UUID] UUIDString];
    TabGroup *group = [[TabGroup alloc] init];
    group.groupId   = groupId;
    // Use supplied tint unless it is nil or the previous default systemTealColor sentinel.
    if (tint && ![tint isEqual:[NSColor systemTealColor]]) {
        group.tint = tint;
    } else {
        group.tint = RandomGroupTint();
    }
    group.windows   = [NSPointerArray weakObjectsPointerArray];
    group.closedTabs = [NSMutableArray array];

    // Decide whether this group should maintain a dynamic "N Tabs" title.
    BOOL shouldAutoTitle = (_groups.count == 0) || (title == nil) || ([title length] == 0);
    group.autoTitle = shouldAutoTitle;

    // Initial title
    if (shouldAutoTitle) {
        // Use dynamic scheme (may be updated again when tabs are added/removed).
        group.title = @"0 Tabs";
    } else {
        group.title = title;
    }

    // Insert at front so newest groups appear first in UI
    [_groups insertObject:group atIndex:0];

    // Keep initial title accurate for auto-titled group if it already contains windows (edge-case).
    if (shouldAutoTitle) {
        [self updateAutoTitleForGroupIfNeeded:group];
    }

    [[NSNotificationCenter defaultCenter] postNotificationName:kTabGroupManagerGroupAddedNotification
                                                        object:self
                                                      userInfo:@{ @"groupId" : groupId }];
    DBM(@"Created tab group %@ (%@)", title, groupId);
    return groupId;
}

- (NSString *)createGroupWithId:(NSString *)groupId title:(NSString *)title tint:(NSColor *)tint {
    if (!groupId || groupId.length == 0) {
        // Fall back to normal generator if caller passed empty string
        return [self createGroupWithTitle:title tint:tint];
    }

    // If a group with that id already exists, just update its metadata
    for (TabGroup *g in _groups) {
        if ([g.groupId isEqualToString:groupId]) {
            g.title = title ?: g.title;
            // Use supplied tint unless it is nil or the previous default systemTealColor sentinel.
            if (tint && ![tint isEqual:[NSColor systemTealColor]]) {
                g.tint = tint;
            } else {
                g.tint = RandomGroupTint();
            }
            g.autoTitle = (title == nil || title.length == 0);
            return groupId;
        }
    }

    // Otherwise create a new group object with the provided id
    TabGroup *group = [[TabGroup alloc] init];
    group.groupId   = [groupId copy];
    // Use supplied tint unless it is nil or the previous default systemTealColor sentinel.
    if (tint && ![tint isEqual:[NSColor systemTealColor]]) {
        group.tint = tint;
    } else {
        group.tint = RandomGroupTint();
    }
    group.windows   = [NSPointerArray weakObjectsPointerArray];
    group.closedTabs = [NSMutableArray array];

    BOOL shouldAutoTitle = (title == nil) || ([title length] == 0);
    group.autoTitle = shouldAutoTitle;
    group.title = shouldAutoTitle ? @"0 Tabs" : title;

    // Insert at front so newest groups appear first in UI
    [_groups insertObject:group atIndex:0];

    [[NSNotificationCenter defaultCenter] postNotificationName:kTabGroupManagerGroupAddedNotification
                                                        object:self
                                                      userInfo:@{ @"groupId" : groupId }];
    DBM(@"Created fixed-id tab group %@ (%@)", title, groupId);
    return groupId;
}

- (NSArray<NSString *> *)allGroupIds {
    NSMutableArray<NSString *> *ids = [NSMutableArray arrayWithCapacity:_groups.count];
    for (TabGroup *g in _groups) {
        [ids addObject:g.groupId];
    }
    return ids;
}

- (NSString *)ensureDefaultGroup {
    if (_groups.count == 0) {
        return [self createGroupWithTitle:@"Default" tint:nil];
    }
    return _groups.lastObject.groupId;
}

- (nullable NSString *)groupTitleForId:(NSString *)groupId {
    for (TabGroup *g in _groups) {
        if ([g.groupId isEqualToString:groupId]) { return g.title; }
    }
    return nil;
}

- (nullable NSColor *)groupTintForId:(NSString *)groupId {
    for (TabGroup *g in _groups) {
        if ([g.groupId isEqualToString:groupId]) { return g.tint; }
    }
    return nil;
}

- (void)setTitle:(NSString *)title forGroupId:(NSString *)groupId {
    if (!groupId) { return; }
    for (TabGroup *g in _groups) {
        if ([g.groupId isEqualToString:groupId]) {
            NSString *trimmed = [title stringByTrimmingCharactersInSet:[NSCharacterSet whitespaceAndNewlineCharacterSet]];
            if (trimmed.length == 0) { trimmed = @"Untitled"; }
            if ([g.title isEqualToString:trimmed]) { return; } // No change
            g.title = trimmed;
            g.autoTitle = NO; // Disable automatic title so manual name persists
            [[NSNotificationCenter defaultCenter] postNotificationName:kTabGroupManagerGroupTitleChangedNotification
                                                                object:self
                                                              userInfo:@{ @"groupId" : g.groupId,
                                                                          @"title"   : trimmed }];
            DBM(@"Group %@ renamed to %@", g.groupId, trimmed);
            return;
        }
    }
}

#pragma mark ‑ Tab API

- (void)addWindow:(NSWindow *)window toGroup:(nullable NSString *)groupId {
    if (!window) { return; }
    // If this window was recently detached, clear the marker – it's not closing.
    [self.recentlyDetachedMap removeObjectForKey:window];
    // Remove from any existing group first without marking as closed
    [self detachWindow:window];

    NSString *targetGroupId = groupId ?: [self ensureDefaultGroup];
    TabGroup *targetGroup = nil;
    for (TabGroup *g in _groups) {
        if ([g.groupId isEqualToString:targetGroupId]) { targetGroup = g; break; }
    }
    if (!targetGroup) {
        // Fallback: create new group with given id as title
        targetGroupId = [self createGroupWithTitle:groupId ?: @"Group" tint:nil];
        for (TabGroup *g in _groups) {
            if ([g.groupId isEqualToString:targetGroupId]) { targetGroup = g; break; }
        }
    }

    // ----------------------------------------------
    // If this window's conversation already exists in the closed list, remove it.
    // ----------------------------------------------
    launcher::ui::MacOSChatUI *uiInstance = nil;
    if (window.windowController && [window.windowController respondsToSelector:@selector(uiInstance)]) {
        typedef launcher::ui::MacOSChatUI *(*GetterFunc)(id, SEL);
        GetterFunc getter = (GetterFunc)objc_msgSend;
        uiInstance = getter(window.windowController, @selector(uiInstance));
    }
    std::string convId;
    if (uiInstance) { convId = uiInstance->conversationId(); }

    if (!convId.empty()) {
        for (NSUInteger i = 0; i < targetGroup.closedTabs.count; ++i) {
            ClosedTabInfo *info = targetGroup.closedTabs[i];
            if ([info.conversationId isEqualToString:[NSString stringWithUTF8String:convId.c_str()]]) {
                [targetGroup.closedTabs removeObjectAtIndex:i];
                break;
            }
        }
    }

    // Append at end to keep Safari-style ordering
    [targetGroup.windows addPointer:(__bridge void *)window];
    [targetGroup.windows compact]; // Clean cleared pointers

    // Update dynamic title (if applicable) before notifying observers so UI sees new value early.
    [self updateAutoTitleForGroupIfNeeded:targetGroup];

    [[NSNotificationCenter defaultCenter] postNotificationName:kTabGroupManagerTabAddedNotification
                                                        object:self
                                                      userInfo:@{ @"window" : window, @"groupId" : targetGroupId }];
    DBM(@"Added window %p to group %@", window, targetGroupId);
}

- (void)removeWindow:(NSWindow *)window {
    if (!window) { return; }

    BOOL handled = NO;

    for (NSUInteger groupIdx = 0; groupIdx < _groups.count; ++groupIdx) {
        TabGroup *g = _groups[groupIdx];
        if (!PointerArrayContainsWindow(g.windows, window)) { continue; }

        // ----------------------------------------------
        // Build ClosedTabInfo before removing pointer
        // ----------------------------------------------
        ClosedTabInfo *cinfo = [[ClosedTabInfo alloc] init];

        // Conversation id extraction
        launcher::ui::MacOSChatUI *uiInstance = nil;
        if (window.windowController && [window.windowController respondsToSelector:@selector(uiInstance)]) {
            typedef launcher::ui::MacOSChatUI *(*GetterFunc)(id, SEL);
            GetterFunc getter = (GetterFunc)objc_msgSend;
            uiInstance = getter(window.windowController, @selector(uiInstance));
        }
        if (uiInstance) {
            std::string cid = uiInstance->conversationId();
            cinfo.conversationId = [NSString stringWithUTF8String:cid.c_str()];
        }
        cinfo.title = window.title ?: @"Untitled";
        cinfo.iconSymbolName = launcher::ui::SessionMetadataManager::shared().iconSymbolNameForWindow(window) ?: @"bubble.left";
        cinfo.tintColor = launcher::ui::SessionMetadataManager::shared().tintColorForWindow(window) ?: [NSColor labelColor];
        cinfo.frame = window.frame;

        // Remove all occurrences inside the group's window list.
        for (NSUInteger idx = 0; idx < g.windows.count; ++idx) {
            void *rawPtr = [g.windows pointerAtIndex:idx];
            NSWindow *ptrWindow = (__bridge NSWindow *)rawPtr;
            if (ptrWindow == window) {
                [g.windows removePointerAtIndex:idx];
                break;
            }
        }
        [g.windows compact];

        // Persist closed entry only if the tab has a valid conversation id
        if (cinfo.conversationId.length > 0) {
            [g.closedTabs addObject:cinfo];
        }

        BOOL groupNowEmpty = (g.windows.count == 0 && g.closedTabs.count == 0);
        NSString *groupId = g.groupId; // capture before potential deletion.

        // Update auto-title counts before potential deletion
        [self updateAutoTitleForGroupIfNeeded:g];

        if (groupNowEmpty) {
            [_groups removeObjectAtIndex:groupIdx];
            if (_groups.count > 0) {
                [self updateAutoTitleForGroupIfNeeded:_groups.lastObject];
            }
        }

        // Notifications
        [[NSNotificationCenter defaultCenter] postNotificationName:kTabGroupManagerTabRemovedNotification
                                                            object:self
                                                          userInfo:@{ @"window" : window,
                                                                      @"groupId" : groupId }];

        [[NSNotificationCenter defaultCenter] postNotificationName:kTabGroupManagerTabClosedNotification
                                                            object:self
                                                          userInfo:@{ @"groupId" : groupId,
                                                                      @"conversationId" : (cinfo.conversationId ?: @"") }];
        DBM(@"Closed window %p from group %@", window, groupId);
        handled = YES;
        break; // Only one group can contain the window.
    }

    // ------------------------------------------------------------------
    // Fallback: If the window was already detached (pointer not found), but
    //           we have a record in recentlyDetachedMap, create a closedTab
    //           entry using that group id.
    // ------------------------------------------------------------------
    if (!handled) {
        NSString *gid = [self.recentlyDetachedMap objectForKey:window];
        if (!gid) { return; }

        // Ensure we no longer keep the stale mapping.
        [self.recentlyDetachedMap removeObjectForKey:window];

        TabGroup *targetGroup = nil;
        for (TabGroup *g in _groups) {
            if ([g.groupId isEqualToString:gid]) { targetGroup = g; break; }
        }

        if (!targetGroup) {
            // Group may have been deleted earlier; recreate a placeholder one.
            [self createGroupWithId:gid title:nil tint:[NSColor systemTealColor]];
            for (TabGroup *g in _groups) {
                if ([g.groupId isEqualToString:gid]) { targetGroup = g; break; }
            }
            if (!targetGroup) { return; }
        }

        // Build ClosedTabInfo similar to main path
        ClosedTabInfo *cinfo = [[ClosedTabInfo alloc] init];

        launcher::ui::MacOSChatUI *uiInstance = nil;
        if (window.windowController && [window.windowController respondsToSelector:@selector(uiInstance)]) {
            typedef launcher::ui::MacOSChatUI *(*GetterFunc)(id, SEL);
            GetterFunc getter = (GetterFunc)objc_msgSend;
            uiInstance = getter(window.windowController, @selector(uiInstance));
        }
        if (uiInstance) {
            std::string cid = uiInstance->conversationId();
            cinfo.conversationId = [NSString stringWithUTF8String:cid.c_str()];
        }
        cinfo.title = window.title ?: @"Untitled";
        cinfo.iconSymbolName = launcher::ui::SessionMetadataManager::shared().iconSymbolNameForWindow(window) ?: @"bubble.left";
        cinfo.tintColor = launcher::ui::SessionMetadataManager::shared().tintColorForWindow(window) ?: [NSColor labelColor];
        cinfo.frame = window.frame;

        if (cinfo.conversationId.length > 0) {
            [targetGroup.closedTabs addObject:cinfo];
        }

        // Update title counts
        [self updateAutoTitleForGroupIfNeeded:targetGroup];

        // Emit TabClosed notification (TabRemoved already sent during detach)
        [[NSNotificationCenter defaultCenter] postNotificationName:kTabGroupManagerTabClosedNotification
                                                            object:self
                                                          userInfo:@{ @"groupId" : gid,
                                                                      @"conversationId" : (cinfo.conversationId ?: @"") }];

        DBM(@"Closed window (fallback) %p from previously-detached group %@", window, gid);
    }
}

- (void)detachWindow:(NSWindow *)window {
    if (!window) { return; }

    for (NSUInteger groupIdx = 0; groupIdx < _groups.count; ++groupIdx) {
        TabGroup *g = _groups[groupIdx];
        if (!PointerArrayContainsWindow(g.windows, window)) { continue; }

        // Remove the weak pointer
        for (NSUInteger idx = 0; idx < g.windows.count; ++idx) {
            void *rawPtr = [g.windows pointerAtIndex:idx];
            NSWindow *ptrWindow = (__bridge NSWindow *)rawPtr;
            if (ptrWindow == window) {
                [g.windows removePointerAtIndex:idx];
                break;
            }
        }
        [g.windows compact];

        // Update automatic title counts BEFORE possible group deletion
        [self updateAutoTitleForGroupIfNeeded:g];

        BOOL groupNowEmpty = (g.windows.count == 0 && g.closedTabs.count == 0);
        NSString *groupId = g.groupId;

        // Mark this window as detached from its group so that if it later closes
        // without being re-added, we can still record a ClosedTabInfo.
        if (g.groupId) {
            [self.recentlyDetachedMap setObject:g.groupId forKey:window];
        }

        if (groupNowEmpty) {
            [_groups removeObjectAtIndex:groupIdx];
            if (_groups.count > 0) {
                [self updateAutoTitleForGroupIfNeeded:_groups.lastObject];
            }
        }

        // Emit only the TabRemoved notification so UI can update its model.
        [[NSNotificationCenter defaultCenter] postNotificationName:kTabGroupManagerTabRemovedNotification
                                                            object:self
                                                          userInfo:@{ @"window" : window,
                                                                      @"groupId" : groupId }];

        DBM(@"Detached window %p from group %@ (no closed snapshot)", window, groupId);
        break; // only found in one group
    }
}

- (NSArray<NSWindow *> *)windowsInGroup:(NSString *)groupId {
    for (TabGroup *g in _groups) {
        if ([g.groupId isEqualToString:groupId]) {
            [g.windows compact];
            return g.windows.allObjects;
        }
    }
    return @[];
}

- (nullable NSString *)groupIdForWindow:(NSWindow *)window {
    for (TabGroup *g in _groups) {
        if (PointerArrayContainsWindow(g.windows, window)) { return g.groupId; }
    }
    return nil;
}

#pragma mark ‑ Selection

- (void)setSelectedWindow:(NSWindow *)window {
    if (_currentSelectedWindow == window) { return; }
    _currentSelectedWindow = window;
    NSString *gid = [self groupIdForWindow:window] ?: @"";
    [[NSNotificationCenter defaultCenter] postNotificationName:kTabGroupManagerSelectionChangedNotification
                                                        object:self
                                                      userInfo:@{ @"window" : (window ?: [NSNull null]),
                                                                  @"groupId": gid }];
    DBM(@"Selected window changed to %p (gid=%@)", window, gid);
}

- (nullable NSWindow *)selectedWindow {
    return _currentSelectedWindow;
}

#pragma mark - Helper

- (void)updateAutoTitleForGroupIfNeeded:(TabGroup *)group {
    if (!group || !group.autoTitle) { return; }

    [group.windows compact];
    NSUInteger count = group.windows.count + group.closedTabs.count; // include closed tabs
    NSString *newTitle = [NSString stringWithFormat:@"%lu %@", (unsigned long)count, (count == 1 ? @"Tab" : @"Tabs")];

    if (![group.title isEqualToString:newTitle]) {
        group.title = newTitle;
        [[NSNotificationCenter defaultCenter] postNotificationName:kTabGroupManagerGroupTitleChangedNotification
                                                            object:self
                                                          userInfo:@{ @"groupId" : group.groupId,
                                                                      @"title"   : newTitle }];
        DBM(@"Updated auto-titled group %@ to %@", group.groupId, newTitle);
    }
}

#pragma mark - Random Tint Helper

// Returns a random colour from a fixed Safari-like palette for tab-group icons.
static NSColor *RandomGroupTint() {
    static NSArray<NSColor *> *palette = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        palette = @[ [NSColor systemRedColor],
                     [NSColor systemOrangeColor],
                     [NSColor systemYellowColor],
                     [NSColor systemGreenColor],
                     [NSColor systemBlueColor],
                     [NSColor systemPurpleColor],
                     [NSColor systemPinkColor] ];
    });
    uint32_t idx = arc4random_uniform((uint32_t)palette.count);
    return palette[idx];
}

- (NSArray<ClosedTabInfo *> *)closedTabsInGroup:(NSString *)groupId {
    for (TabGroup *g in _groups) {
        if ([g.groupId isEqualToString:groupId]) {
            return [g.closedTabs copy];
        }
    }
    return @[];
}

- (BOOL)reopenClosedTab:(ClosedTabInfo *)info anchorWindow:(NSWindow *)anchorWindow {
    if (!info) { return NO; }
    NSString *gid = nil;
    TabGroup *targetGroup = nil;
    for (TabGroup *g in _groups) {
        if ([g.closedTabs containsObject:info]) {
            gid = g.groupId;
            targetGroup = g;
            break;
        }
    }
    if (!gid) { return NO; }

    // ------------------------------------------------------------------
    // 1) Duplicate-conversation guard – if a window already shows the same
    //    conversation, just activate/raise it instead of opening another.
    // ------------------------------------------------------------------
    NSString *targetConversationId = info.conversationId;
    if (targetConversationId.length > 0) {
        for (TabGroup *g in _groups) {
            [g.windows compact];
            for (NSWindow *w in g.windows.allObjects) {
                NSString *cid = ConversationIdForWindow(w);
                if (cid && [cid isEqualToString:targetConversationId]) {
                    // Found existing window → make it active & ensure selection
                    [w makeKeyAndOrderFront:nil];
                    [self setSelectedWindow:w];
                    // Remove the closedTab entry now that it is open again
                    [targetGroup.closedTabs removeObject:info];
                    return YES;
                }
            }
        }
    }

    // ------------------------------------------------------------------
    // Remove the closedTab entry *before* we restore so observers rebuild
    // against the latest model and we do not duplicate rows.
    // ------------------------------------------------------------------
    [targetGroup.closedTabs removeObject:info];
    [self updateAutoTitleForGroupIfNeeded:targetGroup];

    // ------------------------------------------------------------------
    // 2) Build snapshot and reopen WITHOUT an anchor so the new window is
    //    not forced into the wrong group.  We will attach it ourselves.
    // ------------------------------------------------------------------
    launcher::core::persistence::WindowSnapshot ws;
    ws.uuid = "";
    ws.title = [info.title UTF8String];
    ws.group_id = [gid UTF8String];
    ws.frame_x = info.frame.origin.x;
    ws.frame_y = info.frame.origin.y;
    ws.frame_w = info.frame.size.width;
    ws.frame_h = info.frame.size.height;
    ws.conversation_id = [info.conversationId UTF8String];
    nlohmann::json meta;
    meta["icon_symbol"] = std::string([info.iconSymbolName UTF8String]);
    meta["tint_hex"]   = std::string([HexFromColor(info.tintColor) UTF8String]);
    ws.ui_state = meta;

    BOOL restored = launcher::ui::SessionAutosaveManager::shared()
                        .reopenClosedWindowFromSnapshot(ws, /*anchorWindow*/ nil);

    if (restored) {
        // The newly created window should be the key window right after restore.
        NSWindow *newWindow = [NSApp keyWindow];
        if (newWindow) {
            // Ensure it is registered in the correct logical group.
            [self addWindow:newWindow toGroup:gid];
            [self setSelectedWindow:newWindow];
            // Bring the restored window (or tab) to the front so it gains focus.
            [newWindow makeKeyAndOrderFront:nil];
        }
        return YES;
    }

    // Restore failed – re-insert the closedTab entry we removed above and
    // bring the counts back in sync.
    [targetGroup.closedTabs addObject:info];
    [self updateAutoTitleForGroupIfNeeded:targetGroup];
    return NO;
}

- (void)registerClosedTabSnapshot:(const launcher::core::persistence::WindowSnapshot &)snapshot {
    NSString *gid = [NSString stringWithUTF8String:snapshot.group_id.c_str()];
    if (!gid) { return; }
    TabGroup *target = nil;
    for (TabGroup *g in _groups) { if ([g.groupId isEqualToString:gid]) { target = g; break; } }
    if (!target) {
        // Create group if missing
        gid = [self createGroupWithId:gid title:nil tint:[NSColor systemTealColor]];
        for (TabGroup *g in _groups) { if ([g.groupId isEqualToString:gid]) { target = g; break; } }
    }
    if (!target) { return; }

    ClosedTabInfo *info = [[ClosedTabInfo alloc] init];
    info.conversationId = [NSString stringWithUTF8String:snapshot.conversation_id.c_str()];
    info.title = [NSString stringWithUTF8String:snapshot.title.c_str()];
    info.frame = NSMakeRect(snapshot.frame_x, snapshot.frame_y, snapshot.frame_w, snapshot.frame_h);

    // Extract icon/tint from ui_state
    try {
        const auto &state = snapshot.ui_state;
        if (!state.is_null()) {
            if (state.contains("icon_symbol")) {
                std::string sym = state["icon_symbol"].get<std::string>();
                info.iconSymbolName = [NSString stringWithUTF8String:sym.c_str()];
            }
            if (state.contains("tint_hex")) {
                std::string hex = state["tint_hex"].get<std::string>();
                info.tintColor = ColorFromHex([NSString stringWithUTF8String:hex.c_str()]);
            }
        }
    } catch (...) {
    }

    if (!info.iconSymbolName) { info.iconSymbolName = @"bubble.left"; }
    if (!info.tintColor) { info.tintColor = [NSColor labelColor]; }

    // Avoid duplicates
    for (ClosedTabInfo *existing in target.closedTabs) {
        if ([existing.conversationId isEqualToString:info.conversationId]) { return; }
    }

    if (info.conversationId.length > 0) {
        [target.closedTabs addObject:info];
    }
    [self updateAutoTitleForGroupIfNeeded:target];
}

#pragma mark - Remove closed tab

- (void)removeClosedTab:(ClosedTabInfo *)info {
    if (!info) { return; }
    for (NSUInteger idx = 0; idx < _groups.count; ++idx) {
        TabGroup *g = _groups[idx];
        if (![g.closedTabs containsObject:info]) { continue; }

        NSString *gid = g.groupId ?: @"";

        [g.closedTabs removeObject:info];
        [self updateAutoTitleForGroupIfNeeded:g];

        // If group becomes empty, delete it safely after the loop ends.
        BOOL groupEmpty = (g.windows.count == 0 && g.closedTabs.count == 0);
        if (groupEmpty) {
            [_groups removeObjectAtIndex:idx];
            if (_groups.count > 0) {
                [self updateAutoTitleForGroupIfNeeded:_groups.lastObject];
            }
        }

        [[NSNotificationCenter defaultCenter] postNotificationName:kTabGroupManagerTabClosedNotification
                                                            object:self
                                                          userInfo:@{ @"groupId" : gid,
                                                                      @"conversationId" : (info.conversationId ?: @"") }];
        DBM(@"Removed closed tab %@ from group %@", info.title, gid);
        return;
    }
}

#pragma mark ‑ Conversation lookup helper

// Returns a window that is already showing the specified conversation id, or nil if not found.
- (nullable NSWindow *)windowForConversationId:(NSString *)conversationId {
    if (!conversationId || conversationId.length == 0) { return nil; }

    for (TabGroup *g in _groups) {
        // Clean up stray nil pointers to avoid retain cycles.
        [g.windows compact];
        for (NSWindow *w in g.windows.allObjects) {
            NSString *cid = ConversationIdForWindow(w);
            if (cid && [cid isEqualToString:conversationId]) {
                return w;
            }
        }
    }
    return nil;
}

#pragma mark - Public helper to reopen via conversation id

- (BOOL)reopenClosedConversationId:(NSString *)conversationId
                        anchorWindow:(nullable NSWindow *)anchorWindow {
    if (!conversationId || conversationId.length == 0) { return NO; }

    for (TabGroup *g in _groups) {
        for (ClosedTabInfo *info in g.closedTabs) {
            if ([info.conversationId isEqualToString:conversationId]) {
                return [self reopenClosedTab:info anchorWindow:anchorWindow];
            }
        }
    }
    return NO; // not found
}

@end

// BEGIN EDIT: helper functions HexFromColor/ColorFromHex at bottom before @end
static NSString *HexFromColor(NSColor *c) {
    if (!c) { return @"#000000FF"; }
    NSColor *rgb = [c colorUsingColorSpace:[NSColorSpace sRGBColorSpace]];
    CGFloat r,g,b,a; [rgb getRed:&r green:&g blue:&b alpha:&a];
    uint8_t ri = (uint8_t)lround(r * 255.0);
    uint8_t gi = (uint8_t)lround(g * 255.0);
    uint8_t bi = (uint8_t)lround(b * 255.0);
    uint8_t ai = (uint8_t)lround(a * 255.0);
    char buf[10]; snprintf(buf, sizeof(buf), "#%02X%02X%02X%02X", ri, gi, bi, ai);
    return [NSString stringWithUTF8String:buf];
}

static NSColor *ColorFromHex(NSString *hex) {
    if (!hex || hex.length < 7) { return [NSColor labelColor]; }
    unsigned int r, g, b, a = 255;
    if (hex.length == 9) {
        sscanf(hex.UTF8String, "#%02X%02X%02X%02X", &r, &g, &b, &a);
    } else {
        sscanf(hex.UTF8String, "#%02X%02X%02X", &r, &g, &b);
    }
    return [NSColor colorWithRed:r/255.0 green:g/255.0 blue:b/255.0 alpha:a/255.0];
}

// Forward declaration so we can extract conversation id from a window
static NSString *ConversationIdForWindow(NSWindow *window) {
    if (!window) { return nil; }
    launcher::ui::MacOSChatUI *uiInstance = nil;
    if (window.windowController && [window.windowController respondsToSelector:@selector(uiInstance)]) {
        typedef launcher::ui::MacOSChatUI *(*GetterFunc)(id, SEL);
        GetterFunc getter = (GetterFunc)objc_msgSend;
        uiInstance = getter(window.windowController, @selector(uiInstance));
    }
    if (uiInstance) {
        std::string cid = uiInstance->conversationId();
        return [NSString stringWithUTF8String:cid.c_str()];
    }
    return nil;
}
// END EDIT 