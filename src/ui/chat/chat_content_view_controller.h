#import <Cocoa/Cocoa.h>

// Forward declarations
@class ChatViewController;
@class ComposerView;
@class MentionPopoverController;
@class BackendError;
@class MentionSuggestion;

// Forward declare C++ class (we'll just store the pointer)
namespace launcher { namespace ui { class MacOSChatUI; } }

NS_ASSUME_NONNULL_BEGIN

// Blocks used for async callbacks
typedef void (^MentionCompletionBlock)(NSArray<MentionSuggestion *> * _Nullable, BackendError * _Nullable);
typedef void (^HistoryCompletionBlock)(NSArray<NSString *> * _Nullable, BackendError * _Nullable);
typedef void (^LLMSuggestionCompletionBlock)(NSArray<NSString *> * _Nullable, BackendError * _Nullable);

/**
 * ChatContentViewController manages the chat interface content - the chat transcript view and composer.
 * This class does NOT manage the window chrome or split views - it's strictly the UI content.
 */
@interface ChatContentViewController : NSViewController <NSTextViewDelegate, NSTextFieldDelegate>

// Core UI components
@property (nonatomic, readonly) NSView *mainContainerView;
@property (nonatomic, readonly) ChatViewController *chatViewController;
@property (nonatomic, readonly) ComposerView *composerView;
@property (nonatomic, readonly) MentionPopoverController *mentionPopoverController;

// Set the C++ UI instance (weak reference)
- (void)setUiInstance:(launcher::ui::MacOSChatUI*)uiInstance;

// UI actions
- (void)focusOnChatInput;
- (void)addTestMessages;
- (void)sendMessage;

// Methods for handling mentions and suggestions
- (BOOL)mentionPopoverIsVisible;
- (BOOL)mentionPopoverMoveSelectionUp;
- (BOOL)mentionPopoverMoveSelectionDown;
- (BOOL)mentionPopoverConfirmSelection;

@end

NS_ASSUME_NONNULL_END 