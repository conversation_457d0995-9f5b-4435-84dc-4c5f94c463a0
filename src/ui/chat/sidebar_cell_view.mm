#import "sidebar_cell_view.h"
#include "../../core/util/debug.h"

@implementation SidebarCellView {
    NSImageView *badgeView_; // Optional overlay badge for closed tabs
    NSButton    *closeButton_; // Trailing close ‑ removes closed tab from history
    NSTrackingArea *trackingArea_;
    BOOL isClosedRow_;
}

- (instancetype)initWithFrame:(NSRect)frameRect {
    self = [super initWithFrame:frameRect];
    if (self) {
        self.wantsLayer = YES;
        self.translatesAutoresizingMaskIntoConstraints = NO;
        [self setupViews];
    }
    return self;
}

- (void)setupViews {
    // Ensure built-in imageView & textField exist.
    if (!self.imageView) {
        NSImageView *iconView = [[NSImageView alloc] initWithFrame:NSZeroRect];
        iconView.translatesAutoresizingMaskIntoConstraints = NO;
        iconView.symbolConfiguration = [NSImageSymbolConfiguration configurationWithPointSize:13 weight:NSFontWeightRegular];
        iconView.contentTintColor = [NSColor labelColor];
        self.imageView = iconView;
        [self addSubview:iconView];
    }
    if (!self.textField) {
        NSTextField *label = [NSTextField labelWithString:@""];
        label.translatesAutoresizingMaskIntoConstraints = NO;
        self.textField = label;
        [self addSubview:label];
    }

    // Constraints: icon 4pt leading, label after 4pt.
    [NSLayoutConstraint activateConstraints:@[
        [self.imageView.leadingAnchor constraintEqualToAnchor:self.leadingAnchor constant:8],
        [self.imageView.centerYAnchor constraintEqualToAnchor:self.centerYAnchor],
        [self.imageView.widthAnchor constraintEqualToConstant:16],
        [self.imageView.heightAnchor constraintEqualToConstant:16],

        [self.textField.leadingAnchor constraintEqualToAnchor:self.imageView.trailingAnchor constant:6],
        [self.textField.centerYAnchor constraintEqualToAnchor:self.centerYAnchor],
        [self.trailingAnchor constraintGreaterThanOrEqualToAnchor:self.textField.trailingAnchor constant:4]
    ]];

    // Close button (initially hidden). Only revealed for closed tabs on hover.
    NSImage *closeImg = nil;
    if (@available(macOS 11.0, *)) {
        closeImg = [NSImage imageWithSystemSymbolName:@"xmark.circle.fill" accessibilityDescription:@"Remove"];
        NSImageSymbolConfiguration *closeConfig = [NSImageSymbolConfiguration configurationWithPointSize:12 weight:NSFontWeightRegular];
        closeImg = [closeImg imageWithSymbolConfiguration:closeConfig];
    } else {
        closeImg = [NSImage imageNamed:NSImageNameStopProgressTemplate];
    }

    closeButton_ = [NSButton buttonWithImage:closeImg target:nil action:nil];
    closeButton_.translatesAutoresizingMaskIntoConstraints = NO;
    closeButton_.bordered = NO;
    closeButton_.bezelStyle = NSBezelStyleRegularSquare;
    closeButton_.hidden = YES;
    [self addSubview:closeButton_];

    [NSLayoutConstraint activateConstraints:@[
        [closeButton_.trailingAnchor constraintEqualToAnchor:self.trailingAnchor constant:-6],
        [closeButton_.centerYAnchor constraintEqualToAnchor:self.centerYAnchor],
        [closeButton_.widthAnchor constraintEqualToConstant:13],
        [closeButton_.heightAnchor constraintEqualToConstant:13]
    ]];
}

+ (NSImage *)closedTabBadgeImage {
    static NSImage *badge = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        if (@available(macOS 11.0, *)) {
            badge = [NSImage imageWithSystemSymbolName:@"zzz" accessibilityDescription:nil];
            NSImageSymbolConfiguration *config = [NSImageSymbolConfiguration configurationWithPointSize:9 weight:NSFontWeightRegular];
            badge = [badge imageWithSymbolConfiguration:config];
        } else {
            badge = [NSImage imageNamed:NSImageNameStopProgressTemplate]; // Fallback generic icon
        }
    });
    return badge;
}

- (void)setClosedAppearance:(BOOL)isClosed {
    isClosedRow_ = isClosed;

    // Lazily create badge overlay pinned to bottom-right of iconView
    if (isClosed) {
        if (!badgeView_) {
            badgeView_ = [[NSImageView alloc] initWithFrame:NSZeroRect];
            badgeView_.translatesAutoresizingMaskIntoConstraints = NO;
            badgeView_.imageScaling = NSImageScaleProportionallyUpOrDown;
            badgeView_.contentTintColor = [NSColor tertiaryLabelColor];
            [self addSubview:badgeView_];
            [NSLayoutConstraint activateConstraints:@[
                [badgeView_.widthAnchor constraintEqualToConstant:9],
                [badgeView_.heightAnchor constraintEqualToConstant:9],
                [badgeView_.bottomAnchor constraintEqualToAnchor:self.imageView.bottomAnchor constant:2],
                [badgeView_.trailingAnchor constraintEqualToAnchor:self.imageView.trailingAnchor constant:2]
            ]];
        }
        badgeView_.image = [SidebarCellView closedTabBadgeImage];
        badgeView_.hidden = NO;
        self.imageView.alphaValue = 0.5;
        self.textField.textColor = [NSColor secondaryLabelColor];

        // Keep close button hidden until hover
        closeButton_.hidden = YES;
    } else {
        if (badgeView_) { badgeView_.hidden = YES; }
        self.imageView.alphaValue = 1.0;
        self.textField.textColor = [NSColor labelColor];
        closeButton_.hidden = YES;
    }
}

#pragma mark - Hover Tracking

- (void)updateTrackingAreas {
    [super updateTrackingAreas];
    if (trackingArea_) { [self removeTrackingArea:trackingArea_]; trackingArea_ = nil; }

    NSTrackingAreaOptions opts = NSTrackingMouseEnteredAndExited | NSTrackingActiveAlways | NSTrackingInVisibleRect;
    trackingArea_ = [[NSTrackingArea alloc] initWithRect:NSZeroRect
                                                 options:opts
                                                   owner:self
                                                userInfo:nil];
    [self addTrackingArea:trackingArea_];
}

- (void)mouseEntered:(NSEvent *)event {
    if (isClosedRow_) {
        closeButton_.hidden = NO;
    }
}

- (void)mouseExited:(NSEvent *)event {
    if (isClosedRow_) {
        closeButton_.hidden = YES;
    }
}

#pragma mark - Accessor

- (NSButton *)closeButton { return closeButton_; }

@end 