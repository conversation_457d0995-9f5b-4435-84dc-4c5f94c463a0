// chat_window_factory.mm
// Centralised creation logic for chat windows/tabs.
// Follows Google C++ style and uses Objective-C++ to talk to AppKit.

#import <Cocoa/Cocoa.h>

#include "chat_window_factory.h"

#include "macos_chat_ui.h"
#include "tabs/tab_group_manager.h"
#include "top_chat_window_controller.h"
#include "../../core/util/debug.h"

#include <vector>
#include <algorithm>

// -----------------------------------------------------------------------------
// Globals defined in top_chat_window_controller.mm used for lifecycle and KVO
// protection.  They live at the *global* namespace scope.
// -----------------------------------------------------------------------------
extern NSMutableSet<NSWindow *> *g_pending_tabs;
extern std::vector<std::shared_ptr<launcher::ui::MacOSChatUI>> g_tab_ui_instances;

namespace launcher {
namespace ui {

// Small helper to push UI instance into the shared vector whilst keeping the
// pointer alive for the lifetime of the window.
static void keepAlive(const std::shared_ptr<MacOSChatUI> &ui) {
    if (!ui) { return; }
    ::g_tab_ui_instances.push_back(ui);
}

// -----------------------------------------------------------------------------
// Public API
// -----------------------------------------------------------------------------
std::shared_ptr<MacOSChatUI> ChatWindowFactory::createNewWindow() {
    // 1) Create a brand-new logical group first so the upcoming window enters it directly.
    NSString *newGroupId = [[TabGroupManager sharedManager] createGroupWithTitle:nil
                                                                            tint:[NSColor systemTealColor]];
    // 2) Inform the next TopChatWindowController to use this id.
    [TopChatWindowController setNextWindowInitialGroupId:newGroupId];

    // 3) Instantiate the UI – its controller will consume the id and register the window once.
    auto newUI = std::make_shared<MacOSChatUI>();
    if (!newUI) {
        ERM(@"ChatWindowFactory: Failed to allocate MacOSChatUI for new window");
        NSBeep();
        return nullptr;
    }

    newUI->initialize();
    newUI->show();

    keepAlive(newUI);

    return newUI;
}

std::shared_ptr<MacOSChatUI> ChatWindowFactory::createNewTab(NSWindow *anchorWindow) {
    if (!anchorWindow) {
        // Graceful fallback – just open a new window.
        return createNewWindow();
    }

    // IMPORTANT: Get the existing group ID before we cause any tabGroup KVO events.
    NSString *destGroupId = [[TabGroupManager sharedManager] groupIdForWindow:anchorWindow];
    if (!destGroupId) {
        // Request an auto-titled group ("N Tabs") by passing nil for title.
        destGroupId = [[TabGroupManager sharedManager] createGroupWithTitle:nil
                                                                      tint:[NSColor systemTealColor]];
        [[TabGroupManager sharedManager] addWindow:anchorWindow toGroup:destGroupId];
    }

    // Create and initialise a new UI.
    auto newUI = std::make_shared<MacOSChatUI>();
    if (!newUI) {
        ERM(@"ChatWindowFactory: Failed to allocate MacOSChatUI for new tab");
        NSBeep();
        return nullptr;
    }
    newUI->initialize();

    // Obtain its native window.
    NSWindow *newWindow = newUI->getNativeWindow();
    if (!newWindow) {
        ERM(@"ChatWindowFactory: MacOSChatUI returned nil window for new tab");
        return nullptr;
    }

    // Mark this window as "pending addition" to protect it during tab group changes.
    @synchronized(::g_pending_tabs) {
        [::g_pending_tabs addObject:newWindow];
    }

    // Pre-register the new window with the same group BEFORE adding it as a tabbed window.
    [[TabGroupManager sharedManager] addWindow:newWindow toGroup:destGroupId];

    // Now safe to add the window as a tab.
    if (@available(macOS 10.12, *)) {
        // Append as the right-most tab similar to Safari.
        NSArray<NSWindow *> *tabWindows = anchorWindow.tabGroup.windows;
        NSWindow *anchor = tabWindows.lastObject ?: anchorWindow;
        [anchor addTabbedWindow:newWindow ordered:NSWindowAbove];
    } else {
        [anchorWindow addTabbedWindow:newWindow ordered:NSWindowAbove];
    }

    newUI->show();

    // Tab has been added, no longer pending.
    @synchronized(::g_pending_tabs) {
        [::g_pending_tabs removeObject:newWindow];
    }

    // Make the new tab the selected one.
    [[TabGroupManager sharedManager] setSelectedWindow:newWindow];

    keepAlive(newUI);

    return newUI;
}

}  // namespace ui
}  // namespace launcher 