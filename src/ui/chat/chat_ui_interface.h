#pragma once

#include <functional>
#include <memory>
#include <string>
#include <vector>

namespace launcher {
namespace ui {

/**
 * @brief Interface for Chat UI implementations
 */
class ChatUIInterface {
 public:
    /**
     * @brief Callback for when a message is sent
     */
    using SendMessageCallback = std::function<void(const std::string& message)>;

    /**
     * @brief Callback for when a message is received
     */
    using ReceiveMessageCallback =
        std::function<void(const std::string& senderId, const std::string& message)>;

    /**
     * @brief Destructor
     */
    virtual ~ChatUIInterface() = default;

    /**
     * @brief Initialize the UI
     * @param sendCallback Callback for when a message is sent
     * @param receiveCallback Callback for when a message is received
     * @return True if initialization was successful, false otherwise
     */
    virtual bool initialize(SendMessageCallback sendCallback = nullptr,
                            ReceiveMessageCallback receiveCallback = nullptr) = 0;

    /**
     * @brief Show the Chat UI
     */
    virtual void show() = 0;

    /**
     * @brief Hide the Chat UI
     */
    virtual void hide() = 0;

    /**
     * @brief Add a new message to the chat
     * @param senderId The ID of the sender
     * @param senderName The name of the sender
     * @param content The content of the message
     * @return True if the message was added successfully, false otherwise
     */
    virtual bool addMessage(const std::string& senderId, const std::string& senderName,
                            const std::string& content) = 0;

    /**
     * @brief Clear all messages from the chat
     * @return True if messages were cleared successfully, false otherwise
     */
    virtual bool clearMessages() = 0;

    /**
     * @brief Run the UI event loop
     * @return Exit code
     */
    virtual int run() = 0;
};

/**
 * @brief Create a platform-specific Chat UI implementation
 * @return Shared pointer to a ChatUIInterface implementation
 */
std::shared_ptr<ChatUIInterface> createChatUI();

}  // namespace ui
}  // namespace launcher