#import "ComposerView.h"
#import "ComposerViewDelegate.h" // Ensure delegate protocol is imported
#include "../../core/util/debug.h"
#import <AppKit/AppKit.h>
#import <Carbon/Carbon.h> // For kVK_ constants
#include "../../core/interfaces/iconfig_manager.h"  // @add_include
#include "../../core/llm/model_id_parser.h"   // Shared model ID parser
#import "ModelAppearanceFactory.h"
#include <cassert>
#include "../macos/injected_services.h"  // shared helpers (relative path from chat)
#import "CapabilityIconProvider.h"
#include "../../core/llm/capability_heuristics.h"
#include <vector>
#include <map>

@class AppDelegate;

using launcher::core::ParsedModelId;
using launcher::core::parseModelId;
using launcher::ui::ConfigService;

// Characters that typically terminate a mention query
// Use escape sequences for newline and tab
static NSString * const kMentionTerminationCharacters = @" .,:;!?()[]{}<>\n\t";

// Helper: returns an SF Symbol tinted with deterministic model colour (macOS 11+). Older systems return nil.
static NSImage *CreateTintedSymbolImageForModel(NSString *modelName) {
    if (!modelName) { return nil; }
    if (@available(macOS 11.0, *)) {
        NSString *symbol = [ModelAppearanceFactory symbolForModel:modelName];
        if (!symbol) { return nil; }
        NSImage *base = [NSImage imageWithSystemSymbolName:symbol accessibilityDescription:modelName];
        if (!base) { return nil; }

        // Apply desired point-size/weight
        NSImageSymbolConfiguration *cfg = [NSImageSymbolConfiguration configurationWithPointSize:14 weight:NSFontWeightRegular];
        base = [base imageWithSymbolConfiguration:cfg];

        // Manual tint because imageWithTintColor may not exist on all SDKs
        NSColor *tint = [ModelAppearanceFactory colorForModel:modelName];

        NSImage *tinted = [[NSImage alloc] initWithSize:base.size];
        [tinted lockFocus];
        // Draw original template image
        [base drawAtPoint:NSZeroPoint fromRect:NSMakeRect(0, 0, base.size.width, base.size.height) operation:NSCompositingOperationSourceOver fraction:1.0];
        // Overlay colour using SourceIn so only non-transparent pixels get tinted
        [tint set];
        NSRectFillUsingOperation(NSMakeRect(0, 0, base.size.width, base.size.height), NSCompositingOperationSourceIn);
        [tinted unlockFocus];
        [tinted setTemplate:NO]; // Keep colour (avoid C++ keyword conflict)
        return tinted;
    }
    return nil;
}

// New helper: returns tinted SF Symbol based on provider+model capabilities
static NSImage *CreateTintedCapabilitySymbolImage(NSString *providerId,
                                                  NSString *modelId,
                                                  NSString *displayName) {
    if (!providerId || !modelId) { return nil; }
    if (@available(macOS 11.0, *)) {
        NSString *symbol = [ModelAppearanceFactory symbolForProvider:providerId model:modelId];
        if (!symbol) { return nil; }
        NSImage *base = [NSImage imageWithSystemSymbolName:symbol accessibilityDescription:modelId];
        if (!base) { return nil; }

        NSImageSymbolConfiguration *cfg = [NSImageSymbolConfiguration configurationWithPointSize:14 weight:NSFontWeightRegular];
        base = [base imageWithSymbolConfiguration:cfg];

        NSColor *tint = [ModelAppearanceFactory colorForModel:(displayName ?: modelId)];

        NSImage *tinted = [[NSImage alloc] initWithSize:base.size];
        [tinted lockFocus];
        [base drawAtPoint:NSZeroPoint fromRect:NSMakeRect(0, 0, base.size.width, base.size.height) operation:NSCompositingOperationSourceOver fraction:1.0];
        [tint set];
        NSRectFillUsingOperation(NSMakeRect(0, 0, base.size.width, base.size.height), NSCompositingOperationSourceIn);
        [tinted unlockFocus];
        [tinted setTemplate:NO];
        return tinted;
    }
    return nil;
}

// Helper: decorate a menu item title with capability icons inferred from provider/model
static void ApplyCapabilitiesToMenuItem(NSMenuItem *item,
                                        const launcher::core::CapabilitySet &caps) {
    using launcher::core::Capability;

    if (!item) return;

    // Invariant ordering for visual consistency
    static const std::vector<Capability> kOrder = {
        Capability::HighReasoning,
        Capability::ImageInput,
        Capability::AudioInput,
        Capability::ImageGeneration,
        Capability::SpeechGeneration,
        Capability::LargeContext,
        Capability::MultimodalRealtime,
        Capability::ToolUse,
        Capability::ComputerUse
    };

    auto TintForCapability = ^NSColor *(Capability cap) {
        switch (cap) {
            case Capability::HighReasoning:      return [NSColor systemPurpleColor];
            case Capability::ImageInput:         return [NSColor systemBlueColor];
            case Capability::AudioInput:         return [NSColor systemOrangeColor];
            case Capability::ImageGeneration:    return [NSColor systemGreenColor];
            case Capability::SpeechGeneration:   return [NSColor systemPinkColor];
            case Capability::LargeContext:       return [NSColor secondaryLabelColor];
            case Capability::MultimodalRealtime: return [NSColor systemYellowColor];
            case Capability::ToolUse:            return [NSColor systemBrownColor];
            case Capability::ComputerUse:        return [NSColor systemGreenColor];
            default:                             return [NSColor labelColor];
        }
    };

    NSFont *font = [NSFont menuFontOfSize:0];
    NSDictionary *baseAttrs = @{ NSFontAttributeName : font };

    NSMutableAttributedString *attr = [[NSMutableAttributedString alloc] initWithString:item.title ?: @"" attributes:baseAttrs];

    for (Capability cap : kOrder) {
        if (!caps.test(static_cast<size_t>(cap))) continue;

        NSImage *base = [CapabilityIconProvider iconForCapability:cap];
        if (!base) continue;

        // Tinted 11×11 icon
        NSImage *tinted = [[NSImage alloc] initWithSize:NSMakeSize(12, 12)];
        [tinted lockFocus];
        [base setTemplate:YES];
        [base drawInRect:NSMakeRect(0, 0, 12, 12) fromRect:NSZeroRect operation:NSCompositingOperationSourceOver fraction:1.0];
        [TintForCapability(cap) set];
        NSRectFillUsingOperation(NSMakeRect(0, 0, 12, 12), NSCompositingOperationSourceIn);
        [tinted unlockFocus];
        [tinted setTemplate:NO];

        // Append hair-space + attachment
        [attr appendAttributedString:[[NSAttributedString alloc] initWithString:@"\u2006" attributes:baseAttrs]];
        NSTextAttachment *att = [[NSTextAttachment alloc] init];
        att.image = tinted;
        [attr appendAttributedString:[NSAttributedString attributedStringWithAttachment:att]];
    }

    item.attributedTitle = attr;
}

@implementation ComposerView {
    // --- Mention Tracking State ---
    BOOL _isTrackingMention;          // Are we currently inside a potential mention?
    NSRange _currentMentionTriggerRange; // Range of the '@' symbol
    NSRange _currentMentionQueryRange;   // Range of the query text after '@'

    // --- Auto-Completion State ---
    NSTimer *_completionTriggerTimer;       // Timer to trigger completion after pause
    NSTimeInterval _completionTriggerDelay; // Delay for the timer (e.g., 0.4 seconds)
    BOOL _isProgrammaticallyCompleting;     // Flag to prevent re-entrant calls
    NSNumber *_currentCompletionRequestIdentifier; // Tracks the *latest* request
    NSArray<NSString *> *_cachedHistorySuggestions; // Most recent valid history results
    NSArray<NSString *> *_cachedLLMSuggestions;     // Most recent valid LLM results
    NSRange _lastCompletionWordRange;       // Range provided by the last completion call

    // --- Local/Static Completion Suggestions ---
    NSArray<NSString *> *_localCompletionSuggestions;

    // --- UI Elements ---
    NSStackView *_actionBar;           // Holds the segmented control
    NSSegmentedControl *_sendControl;  // Combined control (provider + model + send)
    NSMenu *_providerMenu;             // Provider dropdown menu
    NSMenu *_modelMenu;                // Model dropdown menu (current provider)
}

- (instancetype)initWithFrame:(NSRect)frameRect {
    self = [super initWithFrame:frameRect];
    if (self) {
        // DBM("initWithFrame", @"Initializing composer view");
        
        // Configure properties with default values
        _minHeight = 36.0;
        _maxHeight = 150.0;
        _isTrackingMention = NO; // Start not tracking mention
        _currentMentionTriggerRange = NSMakeRange(NSNotFound, 0);
        _currentMentionQueryRange = NSMakeRange(NSNotFound, 0);

        // Auto-completion defaults
        _completionTriggerDelay = 0.5; // 400ms delay
        _isProgrammaticallyCompleting = NO;
        _currentCompletionRequestIdentifier = nil;
        _cachedHistorySuggestions = @[];
        _cachedLLMSuggestions = @[];
        _lastCompletionWordRange = NSMakeRange(NSNotFound, 0);
        [self setupLocalCompletionSuggestions]; // Initialize local suggestions

        // Setup UI
        [self setupTextView];
        [self setupActionBar];
        [self setupConstraints];

        // Observe text view selection changes for mention tracking
        [[NSNotificationCenter defaultCenter] addObserver:self
                                                 selector:@selector(textViewDidChangeSelection:)
                                                     name:NSTextViewDidChangeSelectionNotification
                                                   object:_textView];
    }
    return self;
}

// Adds a coloured glow to the send segment (index 2) of the segmented control.
- (void)applySendButtonGlowWithColor:(NSColor *)color {
    if (!_sendControl) { return; }
    _sendControl.wantsLayer = YES;
    if (!_sendControl.layer) { return; }
    _sendControl.layer.masksToBounds = NO;
    _sendControl.layer.shadowColor = color.CGColor;
    _sendControl.layer.shadowOpacity = 0.8;
    _sendControl.layer.shadowRadius = 6.0;
    _sendControl.layer.shadowOffset = CGSizeZero;
}

// Initialize the static list of suggestions (Reference Trick #2)
- (void)setupLocalCompletionSuggestions {
    // Example list - can be expanded or configured
    _localCompletionSuggestions = @[
        @"<EMAIL>",
        // Common greetings
        @"Hello", @"Hi there", @"Hey", @"Good morning", @"Good afternoon", @"Good evening",
        
        // Common phrases
        @"How are you?", @"What's up?", @"Thanks", @"Thank you", @"You're welcome",
        @"Great job", @"Nice work", @"Sounds good", @"I agree", @"I disagree",
        @"Let me think about it", @"Can you explain?", @"Please elaborate",
        
        // Questions
        @"What do you think?", @"When will it be ready?", @"How does this work?",
        
        // Time-related
        @"Today", @"Tomorrow", @"Yesterday", @"Next week", @"Later today",
        
        // Common emoji
        @"��", @"👋", @"😊", @"🙌", @"⭐", @"🎉", @"👀", @"💯", @"🔥",
        
        // Chat shortcuts
        @"BRB", @"LOL", @"OMG", @"BTW", @"AFAIK", @"IMHO", @"FYI",
        @"Thanks", @"Thank you",
        @"Sounds good", @"Okay", @"Got it",
        @"👍", @"😊", @"🎉",
        // Add more relevant static suggestions
    ];
}

- (void)setupTextView {
    // Create scroll view for text view
    NSRect scrollViewFrame = NSMakeRect(0, 0, self.bounds.size.width - 80, _minHeight);
    _scrollView = [[NSScrollView alloc] initWithFrame:scrollViewFrame];
    _scrollView.borderType = NSNoBorder;
    _scrollView.hasVerticalScroller = YES;
    _scrollView.hasHorizontalScroller = NO;
    _scrollView.autohidesScrollers = YES;
    _scrollView.wantsLayer = YES; // Ensure layer for potential background/border styling
    _scrollView.layer.cornerRadius = 8.0; // Round corners of the scroll view
    
    // Create text view
    NSRect textViewFrame = NSMakeRect(0, 0, scrollViewFrame.size.width, _minHeight);
    _textView = [[NSTextView alloc] initWithFrame:textViewFrame];
    _textView.minSize = NSMakeSize(0, _minHeight);
    _textView.maxSize = NSMakeSize(FLT_MAX, FLT_MAX);
    _textView.verticallyResizable = YES;
    _textView.horizontallyResizable = NO;
    _textView.autoresizingMask = NSViewWidthSizable;
    _textView.delegate = self;
    _textView.richText = NO;
    _textView.font = [NSFont systemFontOfSize:14.0];
    _textView.textContainerInset = NSMakeSize(5, 8);
    _textView.textContainer.lineFragmentPadding = 0;
    _textView.drawsBackground = YES;
    _textView.backgroundColor = [NSColor textBackgroundColor];
    _textView.allowsUndo = YES;
    _textView.undoManager.groupsByEvent = YES; // Enable standard event-based coalescing
    
    // Text editing behaviors
    _textView.automaticQuoteSubstitutionEnabled = YES;
    _textView.automaticLinkDetectionEnabled = YES;
    _textView.smartInsertDeleteEnabled = YES;
    _textView.continuousSpellCheckingEnabled = YES;
    _textView.automaticSpellingCorrectionEnabled = YES;
    _textView.automaticTextCompletionEnabled = YES;
    _textView.enabledTextCheckingTypes = NSTextCheckingTypeSpelling | NSTextCheckingTypeReplacement;
    
    // Ensure text container width matches scroll view content size width
    _textView.textContainer.containerSize = NSMakeSize(scrollViewFrame.size.width, FLT_MAX);
    _textView.textContainer.widthTracksTextView = YES;

    // --- Placeholder Text --- //
    // TODO: Implement placeholder text when targeting macOS 10.14+
    // For now, commenting out due to compatibility issues
    /*
    if (@available(macOS 10.14, *)) {
        NSDictionary *placeholderAttributes = @{
            NSFontAttributeName: [NSFont systemFontOfSize:14.0],
            NSForegroundColorAttributeName: [NSColor placeholderTextColor]
        };
        _textView.placeholderAttributedString = [[NSAttributedString alloc] initWithString:@"Type a message..."
                                                                             attributes:placeholderAttributes];
    }
    */
    
    // Observe text changes for undo coalescing and mention detection
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(textDidChange:)
                                                 name:NSTextDidChangeNotification
                                               object:_textView];
    
    // DBM("setupTextView", @"Undo/Redo enabled: allowsUndo = %s", _textView.allowsUndo ? "YES" : "NO");
    
    // Set the text view to the scroll view
    _scrollView.documentView = _textView;
    [self addSubview:_scrollView];
}

- (void)setupActionBar {
    using launcher::core::AIProviderConfig;
    using launcher::core::ModelConfig;

    // ---------------- Build provider menu -----------------
    _providerMenu = [[NSMenu alloc] initWithTitle:@"Providers"];

    launcher::core::IConfigManager* cfgPtr = nullptr;
    AppDelegate *appDelegateCfg = (AppDelegate *)[[NSApplication sharedApplication] delegate];
    if (appDelegateCfg && appDelegateCfg.ctx && appDelegateCfg.ctx->configManager) {
        cfgPtr = appDelegateCfg.ctx->configManager.get();
    } else {
        cfgPtr = &ConfigService();
    }
    auto &cfg = *cfgPtr;

    std::vector<AIProviderConfig> providers = cfg.getAIProviders();
    std::sort(providers.begin(), providers.end(), [](const auto &a, const auto &b){ return a.name < b.name; });

    std::string defaultProviderId = cfg.getString("default_ai_provider", "openai");
    std::string defaultModelId    = cfg.getProviderDefaultModel(defaultProviderId);

    NSString *defaultProviderDisplay = nil;

    for (const auto &p : providers) {
        if (!p.enabled) continue;

        NSString *title = [NSString stringWithUTF8String:p.name.c_str()];
        NSMenuItem *item = [[NSMenuItem alloc] initWithTitle:title action:@selector(providerMenuItemSelected:) keyEquivalent:@""];
        item.representedObject = [NSString stringWithUTF8String:p.provider_id.c_str()];
        [item setTarget:self];
        [_providerMenu addItem:item];

        if (p.provider_id == defaultProviderId) {
            defaultProviderDisplay = title;
        }
    }

    // ---------------- Build initial model menu for default provider -----------------
    _modelMenu = [[NSMenu alloc] initWithTitle:@"Models"];

    auto buildModelMenu = ^(const AIProviderConfig &prov) {
        [_modelMenu removeAllItems];

        // ----- Grouping like models_pane_controller -----
        struct ItemPair { const ModelConfig *cfg; ParsedModelId parsed; };
        std::map<std::string, std::vector<ItemPair>> grouped;
        for (const auto &m : prov.models) {
            ParsedModelId pm = parseModelId(m.id, prov.provider_id);
            grouped[pm.group_key].push_back({&m, pm});
        }

        struct GroupMeta { std::string key; std::string title; double major; int variant_rank; };
        std::vector<GroupMeta> metas;

        // Determine the *best* representative inside each group:
        //   – Highest numeric major version first
        //   – Lowest variant_rank as tie-breaker (e.g. 4o before 4)
        for (const auto &kv : grouped) {
            if (kv.second.empty()) continue;

            const auto &vecPairs = kv.second;
            double bestMajor = -1.0;
            int bestVariant = 99;
            std::string bestTitle;

            for (const auto &p : vecPairs) {
                const auto &pm = p.parsed;
                if (pm.major > bestMajor || (pm.major == bestMajor && pm.group_variant_rank < bestVariant)) {
                    bestMajor = pm.major;
                    bestVariant = pm.group_variant_rank;
                    bestTitle = pm.group_title;
                }
            }

            if (bestMajor < 0) bestMajor = 0.0; // Fallback safety
            metas.push_back({kv.first, bestTitle, bestMajor, bestVariant});
        }
        std::sort(metas.begin(), metas.end(), [](const GroupMeta &a, const GroupMeta &b){
            if (a.major != b.major) return a.major > b.major;
            if (a.variant_rank != b.variant_rank) return a.variant_rank < b.variant_rank;
            return a.key < b.key;
        });

        NSMenuItem *firstSelectable = nil;
        NSMenuItem *providerDefaultItem = nil;
        for (size_t gi = 0; gi < metas.size(); ++gi) {
            const auto &meta = metas[gi];
            NSString *headerTitle = [NSString stringWithUTF8String:meta.title.c_str()];
            NSMenuItem *header = [[NSMenuItem alloc] initWithTitle:headerTitle action:nil keyEquivalent:@""];
            header.enabled = NO;
            // --- Compute union of capabilities in this group ---
            launcher::core::CapabilitySet groupCaps;
            for (const auto &p : grouped[meta.key]) {
                groupCaps |= launcher::core::inferCapabilities(prov.provider_id, p.cfg->id);
            }
            ApplyCapabilitiesToMenuItem(header, groupCaps);

            [_modelMenu addItem:header];

            // Sort items within group
            auto vecPairs = grouped[meta.key];
            std::sort(vecPairs.begin(), vecPairs.end(), [](const ItemPair &a, const ItemPair &b){
                if (a.parsed.variant_rank != b.parsed.variant_rank) return a.parsed.variant_rank < b.parsed.variant_rank;
                if (a.parsed.context_k != b.parsed.context_k) return a.parsed.context_k > b.parsed.context_k;
                return a.cfg->display_name < b.cfg->display_name;
            });

            for (const auto &pair : vecPairs) {
                const ModelConfig *m = pair.cfg;
                std::string disp = m->display_name.empty() ? m->name : m->display_name;
                NSString *t = [NSString stringWithUTF8String:disp.c_str()];
                NSMenuItem *mi = [[NSMenuItem alloc] initWithTitle:t action:@selector(modelMenuItemSelected:) keyEquivalent:@""];
                std::string idToken = prov.provider_id + "::" + m->id;
                NSString *token = [NSString stringWithUTF8String:idToken.c_str()];
                mi.representedObject = token;
                [mi setTarget:self];
                [_modelMenu addItem:mi];

                // Attach tinted SF symbol icon based on capabilities
                NSString *provIdStr = [NSString stringWithUTF8String:prov.provider_id.c_str()];
                NSString *modelIdStr = [NSString stringWithUTF8String:m->id.c_str()];
                NSImage *iconImg = CreateTintedCapabilitySymbolImage(provIdStr, modelIdStr, t);
                if (iconImg) { [mi setImage:iconImg]; }

                // No capability icons on individual model rows (only group header)
                if (!firstSelectable) firstSelectable = mi;
                if (!providerDefaultItem && prov.default_model == m->id) {
                    providerDefaultItem = mi;
                }
            }

            if (gi + 1 < metas.size()) {
                [_modelMenu addItem:[NSMenuItem separatorItem]];
            }
        }
    };

    // Locate default provider object
    auto itDef = std::find_if(providers.begin(), providers.end(), [&](const AIProviderConfig &p){ return p.provider_id == defaultProviderId; });
    if (itDef == providers.end() && !providers.empty()) itDef = providers.begin();

    if (itDef != providers.end()) {
        buildModelMenu(*itDef);
    }

    // ---------------- Combined segmented control -----------------
    _sendControl = [[NSSegmentedControl alloc] initWithFrame:NSZeroRect];
    _sendControl.segmentCount = 3;
    _sendControl.translatesAutoresizingMaskIntoConstraints = NO;
    _sendControl.segmentStyle = NSSegmentStyleTexturedRounded;

    // Segment 0: provider
    NSString *provLabel = defaultProviderDisplay ?: ([_providerMenu itemArray].count > 0 ? [_providerMenu itemAtIndex:0].title : @"Provider");
    [_sendControl setLabel:provLabel forSegment:0];
    [_sendControl setWidth:60 forSegment:0];

    // Segment 1: model
    NSString *modelDisplay = @"Model";
    if (itDef != providers.end()) {
        // try find display of defaultModelId
        auto &prov = *itDef;
        auto mit = std::find_if(prov.models.begin(), prov.models.end(), [&](const ModelConfig &m){ return m.id == defaultModelId; });
        if (mit == prov.models.end() && !prov.models.empty()) mit = prov.models.begin();
        if (mit != prov.models.end()) {
            std::string disp = mit->display_name.empty() ? mit->name : mit->display_name;
            modelDisplay = [NSString stringWithUTF8String:disp.c_str()];
            // Store for delegate notification after init
            if (_delegate && [_delegate respondsToSelector:@selector(composerView:didChangeModelProvider:model:)]) {
                [_delegate composerView:self didChangeModelProvider:[NSString stringWithUTF8String:prov.provider_id.c_str()] model:[NSString stringWithUTF8String:mit->id.c_str()]];
            }
        }
    }
    [_sendControl setLabel:modelDisplay forSegment:1];
    [_sendControl setWidth:100 forSegment:1];

    // Apply glow to send button based on current model colour
    NSColor *initialAccent = [ModelAppearanceFactory colorForModel:modelDisplay];
    [self applySendButtonGlowWithColor:initialAccent];

    // Segment 2: send icon
    if (@available(macOS 11.0, *)) {
        NSImage *plane = [NSImage imageWithSystemSymbolName:@"paperplane.fill" accessibilityDescription:@"Send"];
        [_sendControl setImage:plane forSegment:2];
    } else {
        [_sendControl setLabel:@"➤" forSegment:2];
    }
    [_sendControl setWidth:24 forSegment:2];

    // Use smaller font to reduce overall button height/width
    [_sendControl setFont:[NSFont systemFontOfSize:11.0]];

    _sendControl.target = self;
    _sendControl.action = @selector(segmentedClicked:);

    // Fixed min height auto set by control.

    // ---------------- Stack (action bar) ---------
    _actionBar = [[NSStackView alloc] initWithFrame:NSZeroRect];
    _actionBar.translatesAutoresizingMaskIntoConstraints = NO;
    _actionBar.orientation = NSUserInterfaceLayoutOrientationHorizontal;
    _actionBar.spacing = 4.0;
    // Center vertically by default; explicit alignment not required for horizontal stack

    [_actionBar addArrangedSubview:_sendControl];

    [self addSubview:_actionBar];

    // Create stop button via existing helper
    [self setupStopButton];

    // Increase overall height to better match text field height
    [NSLayoutConstraint activateConstraints:@[
        [_sendControl.heightAnchor constraintEqualToConstant:32.0]
    ]];
}

- (void)setupStopButton {
    // Create stop button with same frame as send button
    _stopButton = [[NSButton alloc] initWithFrame:NSMakeRect(self.bounds.size.width - 70, 0, 60, 30)];
    _stopButton.bezelStyle = NSBezelStyleRounded;
    _stopButton.title = @"Stop";
    
    // Set button style to indicate "negative" action
    _stopButton.contentTintColor = [NSColor systemRedColor]; // Tint text red on macOS 10.14+
    
    // Set action to call delegate's cancelCurrentStream method
    _stopButton.action = @selector(cancelCurrentStream);
    _stopButton.target = _delegate; // Delegate must implement cancelCurrentStream
    
    // Hide by default
    _stopButton.hidden = YES;
    
    [self addSubview:_stopButton];
}

- (void)setupConstraints {
    // Make view use Auto Layout
    self.translatesAutoresizingMaskIntoConstraints = NO;
    _scrollView.translatesAutoresizingMaskIntoConstraints = NO;
    _actionBar.translatesAutoresizingMaskIntoConstraints = NO;
    _stopButton.translatesAutoresizingMaskIntoConstraints = NO; // Add for stop button
    
    // Add constraints to scroll view
    [NSLayoutConstraint activateConstraints:@[
        [_scrollView.leadingAnchor constraintEqualToAnchor:self.leadingAnchor constant:8.0],
        [_scrollView.trailingAnchor constraintEqualToAnchor:_actionBar.leadingAnchor constant:-8.0],
        [_scrollView.topAnchor constraintEqualToAnchor:self.topAnchor constant:8.0],
        [_scrollView.bottomAnchor constraintEqualToAnchor:self.bottomAnchor constant:-8.0],
        [_scrollView.heightAnchor constraintGreaterThanOrEqualToConstant:_minHeight]
    ]];
    
    // Add constraints to action bar (model popup + send)
    [NSLayoutConstraint activateConstraints:@[
        [_actionBar.trailingAnchor constraintEqualToAnchor:self.trailingAnchor constant:-8.0],
        [_actionBar.centerYAnchor constraintEqualToAnchor:self.centerYAnchor]
    ]];
    
    // Add constraints to stop button (same position as action bar)
    [NSLayoutConstraint activateConstraints:@[
        [_stopButton.trailingAnchor constraintEqualToAnchor:self.trailingAnchor constant:-8.0],
        [_stopButton.centerYAnchor constraintEqualToAnchor:self.centerYAnchor],
        [_stopButton.widthAnchor constraintEqualToConstant:60.0]
    ]];
}

#pragma mark - Show/Hide Stop Button

- (void)showStopButton {
    dispatch_async(dispatch_get_main_queue(), ^{
        // Hide action bar (model popup + send), show stop button
        self->_actionBar.hidden = YES;
        self.stopButton.hidden = NO;
    });
}

- (void)hideStopButton {
    dispatch_async(dispatch_get_main_queue(), ^{
        // Hide stop button, show action bar
        self.stopButton.hidden = YES;
        self->_actionBar.hidden = NO;
    });
}

#pragma mark - Public Methods

- (void)clear {
    [self cancelMentionTracking]; // Cancel mention tracking when clearing
    [self cancelCompletionTriggerTimer]; // Cancel completion timer
    _currentCompletionRequestIdentifier = nil; // Invalidate current completion request
    _cachedHistorySuggestions = @[];
    _cachedLLMSuggestions = @[];

    [_textView setString:@""];
    // [_textView.undoManager removeAllActions]; // Clearing string also clears undo
    [self invalidateIntrinsicContentSize]; // Trigger height update
}

- (void)sendMessage {
    [self cancelMentionTracking]; // Cancel mention tracking on send
    [self cancelCompletionTriggerTimer]; // Cancel completion timer
    _currentCompletionRequestIdentifier = nil; // Invalidate current completion request
    _cachedHistorySuggestions = @[];
    _cachedLLMSuggestions = @[];

    NSString *content = [_textView.string stringByTrimmingCharactersInSet:[NSCharacterSet whitespaceAndNewlineCharacterSet]];
    if (content.length > 0) {
        // DBM(@"Sending message with content length: %lu", (unsigned long)content.length);
        
        // Call delegate method with the content
        if (_delegate && [_delegate respondsToSelector:@selector(composerViewDidSubmitMessage:)]) {
            [_delegate composerViewDidSubmitMessage:content];
        } else {
            DBM(@"Delegate not set or does not respond to composerViewDidSubmitMessage:");
        }
        
        // Clear the text view
        [self clear];
    }
}

#pragma mark - Mention Tracking Logic

// Called on text change and selection change
- (void)checkForMentionTrigger {
    NSRange selectedRange = _textView.selectedRange;
    // Only track if selection is a cursor (length 0)
    if (selectedRange.length > 0) {
        [self cancelMentionTrackingIfNeeded];
        return;
    }

    NSUInteger cursorLocation = selectedRange.location;
    NSString *text = _textView.string;

    // Scan backwards from cursor to find '@' that's preceded by whitespace or start of string
    NSRange searchRange = NSMakeRange(0, cursorLocation);
    NSRange triggerRange = [text rangeOfString:@"@"
                                       options:NSBackwardsSearch
                                         range:searchRange];

    if (triggerRange.location == NSNotFound) {
        [self cancelMentionTrackingIfNeeded];
        return; // No '@' found before cursor
    }

    // Check character before '@'
    if (triggerRange.location > 0) {
        unichar charBefore = [text characterAtIndex:triggerRange.location - 1];
        if (![[NSCharacterSet whitespaceAndNewlineCharacterSet] characterIsMember:charBefore]) {
            [self cancelMentionTrackingIfNeeded];
            return; // '@' is not preceded by whitespace/newline or start of string
        }
    }

    // Check if cursor is immediately after '@' or within a valid query
    NSUInteger queryStartLocation = triggerRange.location + triggerRange.length;
    if (cursorLocation < queryStartLocation) {
         [self cancelMentionTrackingIfNeeded];
         return; // Cursor is before the potential query start
    }

    // Extract potential query
    NSRange potentialQueryRange = NSMakeRange(queryStartLocation, cursorLocation - queryStartLocation);
    NSString *query = [text substringWithRange:potentialQueryRange];

    // Check if query contains mention termination characters
    NSCharacterSet *terminationSet = [NSCharacterSet characterSetWithCharactersInString:kMentionTerminationCharacters];
    if ([query rangeOfCharacterFromSet:terminationSet].location != NSNotFound) {
        [self cancelMentionTrackingIfNeeded];
        return; // Query contains invalid characters, terminate mention tracking
    }

    // --- Conditions met for active mention tracking ---
    NSRange fullMentionRange = NSMakeRange(triggerRange.location, (potentialQueryRange.location + potentialQueryRange.length) - triggerRange.location);

    if (!_isTrackingMention) {
        // --- Start Tracking ---
        _isTrackingMention = YES;
        _currentMentionTriggerRange = triggerRange;
        _currentMentionQueryRange = potentialQueryRange;
        DBM(@"Starting mention tracking. Query: '%@', Trigger: %@, Query Range: %@", query, NSStringFromRange(triggerRange), NSStringFromRange(potentialQueryRange));

        // Notify Delegate: Did Detect Mention
        if (_delegate && [_delegate respondsToSelector:@selector(composerView:didDetectMentionTriggerInRange:query:fullQueryRange:)]) {
            [_delegate composerView:self
 didDetectMentionTriggerInRange:_currentMentionTriggerRange
                          query:query
                 fullQueryRange:fullMentionRange];
        }

    } else {
        // --- Continue Tracking (Query Changed) ---
        // Only notify if the query or range actually changed
        if (!NSEqualRanges(_currentMentionQueryRange, potentialQueryRange) ||
            !NSEqualRanges(_currentMentionTriggerRange, triggerRange)) {

            _currentMentionTriggerRange = triggerRange;
            _currentMentionQueryRange = potentialQueryRange;
            DBM(@"Mention query updated. Query: '%@', Full Range: %@", query, NSStringFromRange(fullMentionRange));

            // Notify Delegate: Query Did Change
            if (_delegate && [_delegate respondsToSelector:@selector(composerView:mentionQueryDidChange:range:)]) {
                [_delegate composerView:self mentionQueryDidChange:query range:fullMentionRange];
            }
        }
    }
}

// Call this when conditions for mention tracking are no longer met
- (void)cancelMentionTrackingIfNeeded {
    if (_isTrackingMention) {
        [self cancelMentionTracking];
    }
}

// Forcefully cancels mention tracking and notifies delegate
- (void)cancelMentionTracking {
    if (!_isTrackingMention) return; // Already cancelled

    DBM(@"Cancelling mention tracking.");
    _isTrackingMention = NO;
    _currentMentionTriggerRange = NSMakeRange(NSNotFound, 0);
    _currentMentionQueryRange = NSMakeRange(NSNotFound, 0);

    // Notify Delegate: Did Cancel
    if (_delegate && [_delegate respondsToSelector:@selector(composerViewDidCancelMention:)]) {
        [_delegate composerViewDidCancelMention:self];
    }
}

#pragma mark - Auto-Completion Logic

// Call this when typing stops to potentially trigger completion
- (void)scheduleCompletionTrigger {
    [self cancelCompletionTriggerTimer]; // Cancel any existing timer

    _completionTriggerTimer = [NSTimer scheduledTimerWithTimeInterval:_completionTriggerDelay
                                                             target:self
                                                           selector:@selector(completionTimerFired:)
                                                           userInfo:nil
                                                            repeats:NO];
    // DBM(@"Scheduled completion timer with delay: %.2fs", _completionTriggerDelay);
}

- (void)cancelCompletionTriggerTimer {
    if (_completionTriggerTimer) {
        [_completionTriggerTimer invalidate];
        _completionTriggerTimer = nil;
        // DBM(@"Cancelled completion timer");
    }
}

// Called when the idle timer fires
- (void)completionTimerFired:(NSTimer *)timer {
    // DBM(@"Completion timer fired.");
    _completionTriggerTimer = nil; // Timer has fired

    // Prevent triggering if a mention is active or selection is not a cursor
    if (_isTrackingMention || _textView.selectedRange.length > 0) {
        DBM(@"Skipping completion trigger (mention active or selection exists).");
        return;
    }

    // Perform context extraction and checks (example - refine as needed)
    NSString *text = _textView.string;
    NSRange selectedRange = _textView.selectedRange;
    if (selectedRange.location == 0) {
        DBM(@"Skipping completion trigger (cursor at start).");
        return; // Don't trigger at the very beginning
    }
    
    // Basic check: don't trigger if line starts with '/' (potential command)
    NSRange lineRange = [text lineRangeForRange:selectedRange];
    NSString *currentLine = [text substringWithRange:lineRange];
    if ([currentLine hasPrefix:@"/"]) {
        DBM(@"Skipping completion trigger (likely slash command).");
        return;
    }

    // TODO: Implement more sophisticated "Smart Context Extraction" here
    // - Analyze text before cursor
    // - Check for minimum characters typed since last space/trigger point?
    // - Limit context size for LLM

    // If conditions are met, trigger the built-in completion mechanism
    // DBM(@"Triggering programmatic completion.");
    _isProgrammaticallyCompleting = YES; // Set flag before calling complete:
    [_textView complete:nil];
    _isProgrammaticallyCompleting = NO; // Reset flag after call returns
}

// Helper to generate a new request identifier
- (NSNumber *)generateCompletionRequestIdentifier {
    // Simple incrementing counter for now, wrap in NSNumber
    static uint64_t counter = 0;
    @synchronized(self) {
        counter++;
    }
    return @(counter);
}

// Called by completion blocks when backend results arrive
- (void)handleCompletionResults:(NSArray<NSString *> *)suggestions
                      forType:(CompletionType)type
           withIdentifier:(NSNumber *)requestIdentifier
                    error:(BackendError *)error
{
    DBM(@"Received results for type %d, identifier %@. Error: %@", type, requestIdentifier, error);

    // --- Check Relevance --- 
    if (![_currentCompletionRequestIdentifier isEqualToNumber:requestIdentifier]) {
        DBM(@"Ignoring stale results (current identifier: %@).", _currentCompletionRequestIdentifier);
        return; // Stale results, a newer request is active
    }

    // --- Handle Error --- 
    if (error) {
        // Log the error, potentially show feedback to user?
        NSLog(@"Error fetching %s suggestions: %@", type == CompletionTypeHistory ? "history" : "LLM", error);
        // Depending on strategy, might want to clear cached results for this type
        if (type == CompletionTypeHistory) {
            _cachedHistorySuggestions = @[];
        } else {
            _cachedLLMSuggestions = @[];
        }
        // Optionally re-trigger completion to show remaining/empty results?
        // For now, just log and do nothing further.
        return;
    }

    // --- Store Results --- 
    if (type == CompletionTypeHistory) {
        _cachedHistorySuggestions = suggestions ?: @[];
        DBM(@"Stored %lu history suggestions.", (unsigned long)_cachedHistorySuggestions.count);
    } else { // LLM
        _cachedLLMSuggestions = suggestions ?: @[];
        DBM(@"Stored %lu LLM suggestions.", (unsigned long)_cachedLLMSuggestions.count);
    }

    // --- Re-trigger Completion UI --- 
    // Only re-trigger if the completion panel *might* still be relevant 
    // (i.e., the request identifier hasn't changed) 
    // and the text view still has focus.
    if ([_currentCompletionRequestIdentifier isEqualToNumber:requestIdentifier] && self.window.firstResponder == _textView) {
        DBM(@"Re-triggering completion UI after receiving results.");
        _isProgrammaticallyCompleting = YES;
        [_textView complete:nil];
        _isProgrammaticallyCompleting = NO;
    } else {
        DBM(@"Skipping UI re-trigger (request changed or view lost focus).");
    }
}

#pragma mark - NSTextViewDelegate Methods

- (BOOL)textView:(NSTextView *)textView doCommandBySelector:(SEL)selector {
    // End any text coalescing when a command is triggered
    [self endTextChangeCoalescing];
    
    // --- Mention Popover Navigation/Selection --- 
    if (_isTrackingMention) {
        // Check if the delegate responds to the necessary popover interaction methods
        BOOL delegateCanHandleMentions = (_delegate && 
                                          [_delegate respondsToSelector:@selector(mentionPopoverIsVisible)] && 
                                          [_delegate respondsToSelector:@selector(mentionPopoverMoveSelectionUp)] && 
                                          [_delegate respondsToSelector:@selector(mentionPopoverMoveSelectionDown)] && 
                                          [_delegate respondsToSelector:@selector(mentionPopoverConfirmSelection)]);

        if (delegateCanHandleMentions && [(id)_delegate mentionPopoverIsVisible]) {
            if (selector == @selector(moveUp:)) {
                 DBM(@"Forwarding moveUp: to mention delegate");
                 BOOL handled = [(id)_delegate mentionPopoverMoveSelectionUp];
                 return handled; // Return YES if the popover handled it
            }
            if (selector == @selector(moveDown:)) {
                DBM(@"Forwarding moveDown: to mention delegate");
                BOOL handled = [(id)_delegate mentionPopoverMoveSelectionDown];
                return handled; // Return YES if the popover handled it
            }
             // Intercept Enter (insertNewline:) to confirm mention selection
             if (selector == @selector(insertNewline:)) {
                 DBM(@"Forwarding insertNewline: (Enter) to mention delegate for confirmation");
                 BOOL handled = [(id)_delegate mentionPopoverConfirmSelection];
                 if (handled) {
                     return YES; // Popover handled Enter, don't insert newline or send message
                 } else {
                     // If popover didn't handle Enter (e.g., no selection), 
                     // fall through to default Enter/Shift+Enter behavior below.
                 }
             }
             // Intercept Escape (cancelOperation:) to hide the popover
             if (selector == @selector(cancelOperation:)) {
                  DBM(@"Forwarding cancelOperation: (Escape) to mention delegate");
                  [self cancelMentionTracking]; // This will notify delegate to hide popover
                  return YES; // Handled Escape
             }
        }
    }

    // --- Auto-Completion Popover Navigation/Selection --- 
    // Standard NSTextView completion handles Enter/Escape/Arrows automatically
    // when its completion list is visible. We don't need to intercept them here
    // unless we want *very* custom behavior beyond what the delegate provides.
    // However, pressing Escape should potentially cancel our *backend* requests.
    if (selector == @selector(cancelOperation:)) { // Escape key
        if (_currentCompletionRequestIdentifier) {
            DBM(@"Escape pressed, cancelling current completion backend requests (ID: %@)", _currentCompletionRequestIdentifier);
            if (_delegate && [_delegate respondsToSelector:@selector(composerView:didCancelCompletionRequestWithIdentifier:)]) {
                [_delegate composerView:self didCancelCompletionRequestWithIdentifier:_currentCompletionRequestIdentifier];
            }
            _currentCompletionRequestIdentifier = nil; // Invalidate the request
             _cachedHistorySuggestions = @[]; // Clear caches on manual cancel
             _cachedLLMSuggestions = @[];
             // Let NSTextView handle dismissing its own completion window
             return NO; 
        }
    }

    // --- Standard Enter/Shift+Enter Handling (if not handled by mention popover) ---
    if (selector == @selector(insertNewline:)) {
        NSEvent *currentEvent = NSApp.currentEvent;
        if (currentEvent.modifierFlags & NSEventModifierFlagShift) {
             [self cancelMentionTrackingIfNeeded]; // Cancel mention on Shift+Enter
             return NO; // Let NSTextView handle Shift+Enter (insert newline)
        } else {
             // Enter without Shift (and not handled by mention confirmation)
             [self cancelMentionTrackingIfNeeded]; // Ensure mention is cancelled
            [self sendMessage];
            return YES; // We handled the command (sent message)
        }
    }

    // --- Undo/Redo --- 
     if (selector == @selector(undo:) || selector == @selector(redo:)) {
          DBM(@"Allowing undo:/redo: to be handled by text view / responder chain");
         return NO; // Let default handling occur via responder chain
     }

    // --- Deletion Commands ---
    if (selector == @selector(deleteBackward:) || selector == @selector(deleteForward:) ||
        selector == @selector(deleteToBeginningOfLine:) || selector == @selector(deleteToEndOfLine:) ||
        selector == @selector(deleteToBeginningOfParagraph:) || selector == @selector(deleteToEndOfParagraph:) ||
        selector == @selector(deleteWordBackward:) || selector == @selector(deleteWordForward:)) {
         // Undo coalescing is handled by groupsByEvent = YES and textDidChange:
         return NO; // Let NSTextView handle the actual deletion
    }

    // --- Other Edit Commands (Copy/Cut/Paste/SelectAll) ---
    if (selector == @selector(copy:) || selector == @selector(cut:) ||
        selector == @selector(paste:) || selector == @selector(selectAll:)) {
         if (selector == @selector(paste:) || selector == @selector(cut:) || selector == @selector(selectAll:)) {
             [self cancelMentionTrackingIfNeeded];
         }
         return NO; // Let responder chain handle standard edits
    }

    // Default: command not handled by us
    return NO;
}

- (BOOL)performKeyEquivalent:(NSEvent *)event {
    // If our text view is NOT the current first responder, don't intercept shortcuts.
    // This prevents ComposerView from stealing key equivalents (e.g., Cmd+C) when the user
    // is editing another control such as the sidebar inline-editor.
    if (self.window.firstResponder != _textView) {
        return [super performKeyEquivalent:event];
    }

    // End coalescing for any keyboard shortcuts
    [self endTextChangeCoalescing];
    
    // For Command+Shift+Z, let's detect it directly by keycode for more reliability
    if (event.type == NSEventTypeKeyDown && 
        (event.modifierFlags & NSEventModifierFlagCommand) &&
        (event.modifierFlags & NSEventModifierFlagShift) &&
        [event keyCode] == kVK_ANSI_Z) {
        
        // This is the most direct detection of Command+Shift+Z possible
        DBM(@"Command+Shift+Z detected, performing redo");
        
        if (_textView.undoManager && [_textView.undoManager canRedo]) {
            [_textView.undoManager redo];
            return YES;
        }
    }
    
    // Handle the rest of command key shortcuts
    if (event.type == NSEventTypeKeyDown && (event.modifierFlags & NSEventModifierFlagCommand)) {
        // Get the lowercase character
        NSString *characters = [event charactersIgnoringModifiers];
        if (characters.length == 1) {
            unichar character = [characters characterAtIndex:0];
            
            // Check for common edit commands
            if (character == 'c') {
                // Command+C - Copy
                DBM(@"Command+C detected, copying text");
                
                // Only attempt to copy if there's a selection
                if (_textView.selectedRange.length > 0) {
                    [_textView copy:nil];
                    return YES;
                }
                // Otherwise, let the system handle it through responder chain
                return NO;
            } else if (character == 'v') {
                // Command+V - Paste
                DBM(@"Command+V detected, pasting text");
                
                // Only attempt to paste if pasteboard has content
                NSPasteboard *pasteboard = [NSPasteboard generalPasteboard];
                if ([pasteboard.types containsObject:NSPasteboardTypeString]) {
                    [_textView paste:nil];
                    return YES;
                }
                // Otherwise, let the system handle it through responder chain
                return NO;
            } else if (character == 'x') {
                // Command+X - Cut
                DBM(@"Command+X detected, cutting text");
                
                // Only attempt to cut if there's a selection
                if (_textView.selectedRange.length > 0) {
                    [_textView cut:nil];
                    return YES;
                }
                // Otherwise, let the system handle it through responder chain
                return NO;
            } else if (character == 'a') {
                // Command+A - Select All
                DBM(@"Command+A detected, selecting all text");
                [_textView selectAll:nil];
                return YES;
            } else if (character == 'z' && !(event.modifierFlags & NSEventModifierFlagShift)) {
                // Command+Z - Undo (explicitly checking NOT shift)
                DBM(@"Command+Z detected, undoing");
                
                if (_textView.undoManager && [_textView.undoManager canUndo]) {
                    [_textView.undoManager undo];
                    return YES;
                }
            }
        }
    }
    
    return [super performKeyEquivalent:event];
}

- (void)textDidChange:(NSNotification *)notification {
    // Invalidate intrinsic content size to trigger height adjustment
    [self invalidateIntrinsicContentSize];

    // Ensure the undo manager registers this change, but with coalescing
    if (_textView && _textView.undoManager) {
        if (![_textView.undoManager isUndoRegistrationEnabled]) {
            [_textView.undoManager enableUndoRegistration];
        }
    }

    // Check mention status AFTER text changes
    [self checkForMentionTrigger];

    // Schedule completion trigger check AFTER text changes
    // Don't schedule if mention is active
    if (!_isTrackingMention) {
        [self scheduleCompletionTrigger];
    }
}

- (void)viewDidMoveToWindow {
    [super viewDidMoveToWindow];
    if (self.window) {
        // DBM("viewDidMoveToWindow", @"Window Undo Manager: %@", self.window.undoManager);
        
        // Ensure the text view has undoManager capabilities
        if (_textView) {
            // The undoManager property is automatically tied to the window's undo manager in AppKit
            // But we need to make sure it's enabled and functioning
            _textView.allowsUndo = YES;
            
            // In AppKit, the text view automatically uses the window's undo manager
            // Make sure undo registration is enabled
            if (_textView.undoManager) {
                // Clear any pending undo operations when moved to a new window
                [_textView.undoManager removeAllActions];
                [_textView.undoManager setLevelsOfUndo:50]; // Set a reasonable number of undo levels
                
                // Make sure undo registration is enabled
                if (![_textView.undoManager isUndoRegistrationEnabled]) {
                    [_textView.undoManager enableUndoRegistration];
                }
            }
            
            // Register for both key notifications for better undo/redo support
            NSWindow *window = self.window;
            if (window) {
                [[NSNotificationCenter defaultCenter] addObserver:self
                                                         selector:@selector(windowDidBecomeKey:)
                                                             name:NSWindowDidBecomeKeyNotification
                                                           object:window];
                
                [[NSNotificationCenter defaultCenter] addObserver:self
                                                         selector:@selector(windowDidResignKey:)
                                                             name:NSWindowDidResignKeyNotification
                                                           object:window];
            }
            
            // DBM("viewDidMoveToWindow", @"Configured text view with undo manager");
        }
    } else {
        DBM(@"Removed from window.");
        
        // Remove notifications when removed from window
        [[NSNotificationCenter defaultCenter] removeObserver:self name:NSWindowDidBecomeKeyNotification object:nil];
        [[NSNotificationCenter defaultCenter] removeObserver:self name:NSWindowDidResignKeyNotification object:nil];

        [self cancelMentionTracking]; // Cancel tracking if removed
    }
}

// Window notification handlers
- (void)windowDidBecomeKey:(NSNotification *)notification {
    // When window becomes key, we want to ensure the undo manager is properly initialized
    if (_textView) {
        // DBM("windowDidBecomeKey", @"Window became key, ensuring undo manager is ready");
        [_textView.undoManager setLevelsOfUndo:100]; // More generous undo levels
    }
}

- (void)windowDidResignKey:(NSNotification *)notification {
    // End any text change coalescing when the window loses focus
    [self endTextChangeCoalescing];
    
    // DBM("windowDidResignKey", @"Window resigned key");

    [self cancelMentionTrackingIfNeeded]; // Cancel mention if window loses focus
}

- (void)dealloc {
    // Clean up notification observers
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

- (BOOL)validateMenuItem:(NSMenuItem *)menuItem {
    // Special handling for Undo/Redo menu items
    if ([menuItem action] == @selector(undo:)) {
        return (_textView.undoManager && [_textView.undoManager canUndo]);
    } else if ([menuItem action] == @selector(redo:)) {
        return (_textView.undoManager && [_textView.undoManager canRedo]);
    }
    return YES;
}

// Helper method to end coalescing
- (void)endTextChangeCoalescing {
    if (_textView && _textView.undoManager) {
        if (![_textView.undoManager isUndoRegistrationEnabled]) {
            [_textView.undoManager enableUndoRegistration];
        }
    }
}

// Called AFTER selection has changed
- (void)textViewDidChangeSelection:(NSNotification *)notification {
    // Check mention status AFTER selection changes
    [self checkForMentionTrigger];

    // Cancel completion timer if user moves cursor manually
    [self cancelCompletionTriggerTimer];
}

// --- Core Auto-Completion Delegate Method --- 
- (NSArray<NSString *> *)textView:(NSTextView *)textView
                     completions:(NSArray<NSString *> *)words
            forPartialWordRange:(NSRange)charRange
            indexOfSelectedItem:(NSInteger *)index
{
    // DBM(@"Delegate called. Range: %@, Programmatic: %s", NSStringFromRange(charRange), _isProgrammaticallyCompleting ? "YES" : "NO");

    if (charRange.location == NSNotFound) {
        DBM(@"Ignoring call with invalid range.");
        return @[];
    }

    // Store the range provided by the system for potential use in rangeForUserCompletion
    // Note: We re-calculate the range in rangeForUserCompletion now, but store this system-provided one anyway.
    _lastCompletionWordRange = charRange;
    NSString *partialWord = [_textView.string substringWithRange:charRange];
    NSString *partialWordLower = [partialWord lowercaseString]; // For case-insensitive matching
    
    // --- Filter Local Suggestions (Reference Trick #2) ---
    NSMutableArray<NSString *> *filteredLocalSuggestions = [NSMutableArray array];
    if (partialWord.length > 0) { // Only suggest if there's a partial word
        for (NSString *suggestion in _localCompletionSuggestions) {
            if ([[suggestion lowercaseString] hasPrefix:partialWordLower]) {
                [filteredLocalSuggestions addObject:suggestion];
            }
        }
    }

    // --- Combine Cached Backend Results --- 
    NSMutableArray<NSString *> *combinedBackendSuggestions = [NSMutableArray array];
    NSMutableSet *addedBackendSuggestions = [NSMutableSet set];
    // Add unique history suggestions first
    for (NSString *histSugg in _cachedHistorySuggestions) {
        if (![addedBackendSuggestions containsObject:histSugg]) {
            [combinedBackendSuggestions addObject:histSugg];
            [addedBackendSuggestions addObject:histSugg];
        }
    }
    // Add unique LLM suggestions
    for (NSString *llmSugg in _cachedLLMSuggestions) {
        if (![addedBackendSuggestions containsObject:llmSugg]) {
            [combinedBackendSuggestions addObject:llmSugg];
            [addedBackendSuggestions addObject:llmSugg];
        }
    }

    BOOL shouldRequestBackend = NO;
    NSNumber *newRequestIdentifier = nil;

    // --- Decide if Backend Request is Needed --- 
    if (!_currentCompletionRequestIdentifier) { 
        shouldRequestBackend = YES;
        newRequestIdentifier = [self generateCompletionRequestIdentifier];
        _currentCompletionRequestIdentifier = newRequestIdentifier; // Mark this sequence as active
        _cachedHistorySuggestions = @[]; // Clear cache for new request sequence
        _cachedLLMSuggestions = @[];
        DBM(@"Initiating NEW backend requests (ID: %@) for partial: '%@'", _currentCompletionRequestIdentifier, partialWord);
    } else {
        DBM(@"Re-displaying cached results for active request (ID: %@)", _currentCompletionRequestIdentifier);
    }

    // --- Initiate Backend Requests (if needed) --- 
    if (shouldRequestBackend && newRequestIdentifier) {
        // 1. Request History
        if (_delegate && [_delegate respondsToSelector:@selector(composerView:requestHistorySuggestionsForPrefix:identifier:completion:)]) {
             __weak ComposerView *weakSelf = self;
            [_delegate composerView:self
 requestHistorySuggestionsForPrefix:partialWord
                         identifier:newRequestIdentifier
                         completion:^(NSArray<NSString *> * _Nullable suggestions, BackendError * _Nullable error) {
                [weakSelf handleCompletionResults:suggestions
                                          forType:CompletionTypeHistory
                                   withIdentifier:newRequestIdentifier
                                            error:error];
            }];
        } else {
            DBM(@"Delegate doesn't respond to history request method.");
        }

        // 2. Request LLM
        //    Requires context extraction first!
        NSString *llmContext = [self extractLLMContextAroundRange:charRange]; // Implement this helper
        if (llmContext && _delegate && [_delegate respondsToSelector:@selector(composerView:requestLLMSuggestionsForContext:identifier:completion:)]) {
             __weak ComposerView *weakSelf = self;
            [_delegate composerView:self
 requestLLMSuggestionsForContext:llmContext
                         identifier:newRequestIdentifier
                         completion:^(NSArray<NSString *> * _Nullable suggestions, BackendError * _Nullable error) {
                [weakSelf handleCompletionResults:suggestions
                                          forType:CompletionTypeLLM
                                   withIdentifier:newRequestIdentifier
                                            error:error];
            }];
        } else {
            DBM(@"Skipping LLM request (no context or delegate doesn't respond).");
        }
    }

    // --- Combine ALL Suggestions --- 
    // Start with filtered local suggestions, then add unique backend suggestions.
    NSMutableArray<NSString *> *finalSuggestions = [NSMutableArray arrayWithArray:filteredLocalSuggestions];
    NSMutableSet *addedFinalSuggestions = [NSMutableSet setWithArray:filteredLocalSuggestions];

    for (NSString *backendSugg in combinedBackendSuggestions) {
        if (![addedFinalSuggestions containsObject:backendSugg]) {
            [finalSuggestions addObject:backendSugg];
            [addedFinalSuggestions addObject:backendSugg];
        }
    }
    
    // --- Add System Suggestions (words) if needed --- 
    const NSUInteger kMaxSuggestionsBeforeSystem = 5;
    if (finalSuggestions.count < kMaxSuggestionsBeforeSystem && words.count > 0) {
        DBM(@"Adding up to %lu system suggestions.", (unsigned long)(kMaxSuggestionsBeforeSystem - finalSuggestions.count));
        for (NSString *systemWord in words) {
            if (![addedFinalSuggestions containsObject:systemWord]) {
                [finalSuggestions addObject:systemWord];
                [addedFinalSuggestions addObject:systemWord]; // Keep track to avoid duplicates from system list itself
                // Optional: Break if we reach a total limit?
            }
        }
    }

    // --- Return Combined Suggestions --- 
    if (index) {
        *index = 0; // Default to selecting the first item
    }
    DBM(@"Returning %lu final combined suggestions.", (unsigned long)finalSuggestions.count);
    return finalSuggestions;
}

// Define the exact range to be replaced by the completion.
// Uses logic adapted from reference (Reference Trick #1)
- (NSRange)textView:(NSTextView *)textView rangeForUserCompletion:(NSRangePointer)charRange {
    NSRange selectedRange = [textView selectedRange];

    // If text is selected, use that as the completion range (standard behavior)
    if (selectedRange.length > 0) {
        DBM(@"Using selected text range: %@", NSStringFromRange(selectedRange));
        return selectedRange;
    }

    // Get the string up to the insertion point
    NSString *text = [textView string];
    NSUInteger cursorLocation = selectedRange.location;

    // Don't try to autocomplete if at the start of text
    if (cursorLocation == 0) {
        DBM(@"Cursor at start, returning empty range at cursor.");
        return NSMakeRange(0, 0);
    }

    // Define word boundaries (spaces, punctuation, etc.)
    // Includes standard whitespace/newlines and common punctuation that might end a word.
    NSMutableCharacterSet *wordBoundaries = [NSMutableCharacterSet whitespaceAndNewlineCharacterSet];
    [wordBoundaries addCharactersInString:@",.;:!?()[]{}<>" ];

    // Scan backwards to find the start of the current word
    NSUInteger wordStart = cursorLocation;
    while (wordStart > 0) {
        unichar character = [text characterAtIndex:wordStart - 1];
        // Stop if we hit a boundary character
        if ([wordBoundaries characterIsMember:character]) {
            break;
        }
        wordStart--;
    }
    
    // Ensure wordStart doesn't go past the beginning
    wordStart = MAX(0, wordStart);

    NSRange completionRange = NSMakeRange(wordStart, cursorLocation - wordStart);
    DBM(@"Calculated word completion range: %@ (Word: '%@')", NSStringFromRange(completionRange), [text substringWithRange:completionRange]);
    return completionRange;
}


// --- Helper for LLM Context Extraction --- 
- (NSString *)extractLLMContextAroundRange:(NSRange)completionRange {
    NSString *fullText = _textView.string;
    if (!fullText || fullText.length == 0) {
        return nil;
    }

    // Define max context length (e.g., 500 characters before the completion point)
    const NSUInteger kMaxContextLength = 500;
    
    // Start from the beginning of the word being completed
    NSUInteger contextEndLocation = completionRange.location;
    NSUInteger contextStartLocation = 0;

    if (contextEndLocation > kMaxContextLength) {
        contextStartLocation = contextEndLocation - kMaxContextLength;
    }

    NSRange contextRange = NSMakeRange(contextStartLocation, contextEndLocation - contextStartLocation);
    
    // TODO: Add more sophisticated logic?
    // - Look for paragraph breaks (double newlines)?
    // - Exclude leading whitespace?
    
    NSString *context = [fullText substringWithRange:contextRange];
    DBM(@"Extracted context (len %lu): '...%@'", (unsigned long)context.length, context.length > 50 ? [context substringFromIndex:context.length - 50] : context);
    return context;
}

// Override intrinsicContentSize to provide dynamic height
- (NSSize)intrinsicContentSize {
    // Calculate the ideal height based on text content
    if (!_textView || !_textView.layoutManager || !_textView.textContainer) {
        return NSMakeSize(NSViewNoIntrinsicMetric, _minHeight + 16.0); // Default size if not ready
    }
    
    [_textView.layoutManager ensureLayoutForTextContainer:_textView.textContainer];
    NSRect contentRect = [_textView.layoutManager usedRectForTextContainer:_textView.textContainer];
    
    // Calculate height including vertical insets (top + bottom)
    // Correct calculation using NSSize height (textContainerInset is an NSSize)
    CGFloat contentHeight = ceil(contentRect.size.height) + 2 * _textView.textContainerInset.height;
    
    // Ensure the height is within min/max constraints
    CGFloat clampedHeight = MAX(_minHeight, MIN(_maxHeight, contentHeight));
    
    // Return intrinsic size (width is flexible, height is calculated)
    // Add top/bottom padding for the ComposerView itself (around the scroll view)
    return NSMakeSize(NSViewNoIntrinsicMetric, clampedHeight + 16.0); 
}

// Called when model menu item clicked
- (void)modelMenuItemSelected:(id)sender {
    NSMenuItem *sel = (NSMenuItem *)sender;
    NSString *token = (NSString *)sel.representedObject;
    NSArray<NSString *> *parts = [token componentsSeparatedByString:@"::"];
    if (parts.count == 2) {
        NSString *provider = parts[0];
        NSString *model = parts[1];
        if (self.delegate && [self.delegate respondsToSelector:@selector(composerView:didChangeModelProvider:model:)]) {
            [self.delegate composerView:self didChangeModelProvider:provider model:model];
        }
        // Always show text (no capability strip) so the selection visibly changes
        NSString *trimmed = [sel.title stringByTrimmingCharactersInSet:[NSCharacterSet whitespaceCharacterSet]];
        [_sendControl setImage:nil forSegment:1];
        [_sendControl setLabel:trimmed forSegment:1];
        [_sendControl setWidth:100 forSegment:1];

        // Persist the new default model for this provider
        launcher::core::IConfigManager* cfgPtr = nullptr;
        AppDelegate *appDelegateCfg = (AppDelegate *)[[NSApplication sharedApplication] delegate];
        if (appDelegateCfg && appDelegateCfg.ctx && appDelegateCfg.ctx->configManager) {
            cfgPtr = appDelegateCfg.ctx->configManager.get();
        } else {
            cfgPtr = &ConfigService();
        }
        if (cfgPtr) {
            cfgPtr->setProviderDefaultModel([provider UTF8String], [model UTF8String]);
            cfgPtr->requestSave();
        }
        // Update glow to reflect newly selected model colour
        NSColor *selAccent = [ModelAppearanceFactory colorForModel:sel.title];
        [self applySendButtonGlowWithColor:selAccent];

        // Persist global default provider choice
        cfgPtr->setString("default_ai_provider", std::string([provider UTF8String]));
        cfgPtr->requestSave();
    }
}

#pragma mark - Segmented Control Handling

// Segmented control click handler
- (void)segmentedClicked:(NSSegmentedControl *)sender {
    NSInteger idx = sender.selectedSegment;
    if (idx == 0) { // Provider segment
        NSPoint pt = NSMakePoint(0, sender.bounds.size.height + 2);
        [_providerMenu popUpMenuPositioningItem:nil atLocation:pt inView:sender];
    } else if (idx == 1) { // Model segment
        NSPoint pt = NSMakePoint(0, sender.bounds.size.height + 2);
        [_modelMenu popUpMenuPositioningItem:nil atLocation:pt inView:sender];
    } else if (idx == 2) { // Send icon
        [self sendMessage];
    }
}

#pragma mark - Provider / Model menu actions

- (void)providerMenuItemSelected:(id)sender {
    NSMenuItem *sel = (NSMenuItem *)sender;
    NSString *providerId = (NSString *)sel.representedObject;

    if (!providerId) return;

    // Update provider segment label
    [_sendControl setLabel:sel.title forSegment:0];

    using launcher::core::AIProviderConfig;
    using launcher::core::ModelConfig;

    launcher::core::IConfigManager* cfgPtr = nullptr;
    AppDelegate *appDelegateCfg = (AppDelegate *)[[NSApplication sharedApplication] delegate];
    if (appDelegateCfg && appDelegateCfg.ctx && appDelegateCfg.ctx->configManager) {
        cfgPtr = appDelegateCfg.ctx->configManager.get();
    } else {
        cfgPtr = &ConfigService();
    }
    auto &cfg = *cfgPtr;

    std::vector<AIProviderConfig> providers = cfg.getAIProviders();
    auto itProv = std::find_if(providers.begin(), providers.end(), [&](const AIProviderConfig &p){ return p.provider_id == std::string([providerId UTF8String]); });
    if (itProv == providers.end()) return;

    const AIProviderConfig &prov = *itProv;

    // -------- Rebuild model menu for this provider (grouped) --------
    [_modelMenu removeAllItems];

    struct ItemPair { const ModelConfig *cfg; ParsedModelId parsed; };
    std::map<std::string, std::vector<ItemPair>> grouped;
    for (const auto &m : prov.models) {
        ParsedModelId pm = parseModelId(m.id, prov.provider_id);
        grouped[pm.group_key].push_back({&m, pm});
    }

    struct GroupMeta { std::string key; std::string title; double major; int variant_rank; };
    std::vector<GroupMeta> metas;

    // Determine the *best* representative inside each group:
    //   – Highest numeric major version first
    //   – Lowest variant_rank as tie-breaker (e.g. 4o before 4)
    for (const auto &kv : grouped) {
        if (kv.second.empty()) continue;

        const auto &vecPairs = kv.second;
        double bestMajor = -1.0;
        int bestVariant = 99;
        std::string bestTitle;

        for (const auto &p : vecPairs) {
            const auto &pm = p.parsed;
            if (pm.major > bestMajor || (pm.major == bestMajor && pm.group_variant_rank < bestVariant)) {
                bestMajor = pm.major;
                bestVariant = pm.group_variant_rank;
                bestTitle = pm.group_title;
            }
        }

        if (bestMajor < 0) bestMajor = 0.0; // Fallback safety
        metas.push_back({kv.first, bestTitle, bestMajor, bestVariant});
    }
    std::sort(metas.begin(), metas.end(), [](const GroupMeta &a, const GroupMeta &b){
        if (a.major != b.major) return a.major > b.major;
        if (a.variant_rank != b.variant_rank) return a.variant_rank < b.variant_rank;
        return a.key < b.key;
    });

    NSMenuItem *firstSelectable = nil;
    NSMenuItem *providerDefaultItem = nil;
    for (size_t gi = 0; gi < metas.size(); ++gi) {
        const auto &meta = metas[gi];
        NSString *headerTitle = [NSString stringWithUTF8String:meta.title.c_str()];
        NSMenuItem *header = [[NSMenuItem alloc] initWithTitle:headerTitle action:nil keyEquivalent:@""];
        header.enabled = NO;
        // --- Compute union of capabilities in this group ---
        launcher::core::CapabilitySet groupCaps;
        for (const auto &p : grouped[meta.key]) {
            groupCaps |= launcher::core::inferCapabilities(prov.provider_id, p.cfg->id);
        }
        ApplyCapabilitiesToMenuItem(header, groupCaps);

        [_modelMenu addItem:header];

        // Sort items within group
        auto vecPairs = grouped[meta.key];
        std::sort(vecPairs.begin(), vecPairs.end(), [](const ItemPair &a, const ItemPair &b){
            if (a.parsed.variant_rank != b.parsed.variant_rank) return a.parsed.variant_rank < b.parsed.variant_rank;
            if (a.parsed.context_k != b.parsed.context_k) return a.parsed.context_k > b.parsed.context_k;
            return a.cfg->display_name < b.cfg->display_name;
        });

        for (const auto &pair : vecPairs) {
            const ModelConfig *m = pair.cfg;
            std::string disp = m->display_name.empty() ? m->name : m->display_name;
            NSString *t = [NSString stringWithUTF8String:disp.c_str()];
            NSMenuItem *mi = [[NSMenuItem alloc] initWithTitle:t action:@selector(modelMenuItemSelected:) keyEquivalent:@""];
            std::string idToken = prov.provider_id + "::" + m->id;
            NSString *token = [NSString stringWithUTF8String:idToken.c_str()];
            mi.representedObject = token;
            [mi setTarget:self];
            [_modelMenu addItem:mi];

            // Attach tinted SF symbol icon based on capabilities
            NSString *provIdStr = [NSString stringWithUTF8String:prov.provider_id.c_str()];
            NSString *modelIdStr = [NSString stringWithUTF8String:m->id.c_str()];
            NSImage *iconImg = CreateTintedCapabilitySymbolImage(provIdStr, modelIdStr, t);
            if (iconImg) { [mi setImage:iconImg]; }

            // No capability icons on individual model rows (only group header)
            if (!firstSelectable) firstSelectable = mi;
            if (!providerDefaultItem && prov.default_model == m->id) {
                providerDefaultItem = mi;
            }
        }

        if (gi + 1 < metas.size()) {
            [_modelMenu addItem:[NSMenuItem separatorItem]];
        }
    }

    // Select provider default model if present, else first selectable
    NSMenuItem *initialItem = providerDefaultItem ?: firstSelectable;
    if (initialItem) {
        NSString *trimmed = [initialItem.title stringByTrimmingCharactersInSet:[NSCharacterSet whitespaceCharacterSet]];
        // Clear any previous image so label is always visible
        [_sendControl setImage:nil forSegment:1];
        [_sendControl setLabel:trimmed forSegment:1];
        [_sendControl setWidth:100 forSegment:1];

        // Notify delegate
        if (_delegate && [_delegate respondsToSelector:@selector(composerView:didChangeModelProvider:model:)]) {
            NSString *modelId = [[initialItem.representedObject componentsSeparatedByString:@"::"] lastObject];
            [_delegate composerView:self didChangeModelProvider:providerId model:modelId];
        }

        // Update glow based on selected model in new provider
        NSColor *provAccent = [ModelAppearanceFactory colorForModel:trimmed];
        [self applySendButtonGlowWithColor:provAccent];

        // Persist global default provider choice
        cfg.setString("default_ai_provider", std::string([providerId UTF8String]));
        cfg.requestSave();
    }
}

@end 