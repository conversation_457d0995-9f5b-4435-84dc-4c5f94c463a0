#import <Cocoa/Cocoa.h>

NS_ASSUME_NONNULL_BEGIN

// Represents an item shown in the chat sidebar (either a section header or a selectable row).
@interface SidebarItem : NSObject

@property (nonatomic, copy) NSString *title;               // Display text
@property (nonatomic, strong, nullable) NSImage *icon;     // Optional leading icon (template image)
@property (nonatomic, strong) NSArray<SidebarItem *> *children; // Child items (empty for leaf)
@property (nonatomic, assign) BOOL groupHeader;            // YES if this is a group/section header
@property (nonatomic, strong, nullable) id userInfo;       // Optional user data (like colors)

// Identifier for tab group rows; nil for non-group items. Used to preserve disclosure state.
@property (nonatomic, copy, nullable) NSString *groupId;

// Convenience factory helpers
+ (instancetype)groupWithTitle:(NSString *)title children:(NSArray<SidebarItem *> *)children;
+ (instancetype)leafWithTitle:(NSString *)title icon:(nullable NSImage *)icon;

@end

NS_ASSUME_NONNULL_END 