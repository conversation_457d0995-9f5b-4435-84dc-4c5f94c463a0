#import <Foundation/Foundation.h>
#import <AppKit/AppKit.h>
#import "model/MentionSuggestion.h" // Use correct path
#import "model/BackendError.h" // Use correct path

@class ComposerView;

// Enum to differentiate completion types in callbacks
typedef NS_ENUM(NSInteger, CompletionType) {
    CompletionTypeHistory,
    CompletionTypeLLM
};

/**
 * @protocol ComposerViewDelegate
 * @brief Delegate protocol for the ComposerView to communicate events back to its owner (e.g., ChatWindowController).
 */
@protocol ComposerViewDelegate <NSObject>

/**
 * @brief Called when the user submits a message (e.g., presses Enter without Shift).
 * @param content The text content entered by the user.
 */
- (void)composerViewDidSubmitMessage:(NSString *)content;

@optional

// --- Mention Delegate Methods --- 

/**
 * @brief Called when the composer detects a potential mention trigger ('@').
 * The delegate should typically display a popover for suggestions.
 * @param composerView The composer view instance.
 * @param triggerRange The range of the '@' symbol in the text view's string.
 * @param query The current text following the '@' (might be empty).
 * @param fullQueryRange The full range including the '@' and the query.
 */
- (void)composerView:(ComposerView *)composerView
    didDetectMentionTriggerInRange:(NSRange)triggerRange
                             query:(NSString *)query
                    fullQueryRange:(NSRange)fullQueryRange;

/**
 * @brief Called when the text following the mention trigger changes while tracking.
 * The delegate should update the suggestions based on the new query.
 * @param composerView The composer view instance.
 * @param query The updated query string.
 * @param range The full range of the mention ('@' + query).
 */
- (void)composerView:(ComposerView *)composerView
    mentionQueryDidChange:(NSString *)query
                    range:(NSRange)range;

/**
 * @brief Called when mention tracking is cancelled (e.g., user types a space, deletes '@', moves cursor).
 * The delegate should hide the mention suggestion popover.
 * @param composerView The composer view instance.
 */
- (void)composerViewDidCancelMention:(ComposerView *)composerView;

/**
 * @brief Allows the ComposerView to query if the mention popover is currently visible.
 * Used to determine if arrow keys/Enter/Escape should be forwarded.
 * @return YES if the mention popover is visible, NO otherwise.
 */
- (BOOL)mentionPopoverIsVisible;

/**
 * @brief Requests the delegate (owning the popover) to move the selection up.
 * @return YES if the action was handled (popover was visible and moved selection), NO otherwise.
 */
- (BOOL)mentionPopoverMoveSelectionUp;

/**
 * @brief Requests the delegate (owning the popover) to move the selection down.
 * @return YES if the action was handled (popover was visible and moved selection), NO otherwise.
 */
- (BOOL)mentionPopoverMoveSelectionDown;

/**
 * @brief Requests the delegate (owning the popover) to confirm the current selection.
 * The delegate should insert the selected mention into the text view.
 * @return YES if the action was handled (popover was visible and confirmed selection), NO otherwise.
 */
- (BOOL)mentionPopoverConfirmSelection;


// --- Auto-Completion Delegate Methods --- 

/**
 * @brief Requests history suggestions from the backend.
 * @param composerView The composer view instance.
 * @param prefix The partial word or text fragment to get suggestions for.
 * @param requestIdentifier A unique identifier for this specific request instance.
 * @param completion Block to call with results or error. MUST be called on the main thread.
 */
- (void)composerView:(ComposerView *)composerView
    requestHistorySuggestionsForPrefix:(NSString *)prefix
                            identifier:(NSNumber *)requestIdentifier
                            completion:(void (^)(NSArray<NSString *> * _Nullable suggestions, BackendError * _Nullable error))completion;

/**
 * @brief Requests LLM (Large Language Model) suggestions from the backend.
 * @param composerView The composer view instance.
 * @param context The relevant text context preceding the completion point.
 * @param requestIdentifier A unique identifier for this specific request instance.
 * @param completion Block to call with results or error. MUST be called on the main thread.
 */
- (void)composerView:(ComposerView *)composerView
    requestLLMSuggestionsForContext:(NSString *)context
                           identifier:(NSNumber *)requestIdentifier
                           completion:(void (^)(NSArray<NSString *> * _Nullable suggestions, BackendError * _Nullable error))completion;

/**
 * @brief Notifies the delegate that an ongoing auto-completion backend request
 *        (identified by the provided identifier) should be cancelled.
 *        This is typically called when the user presses Escape while the completion
 *        list might be showing pending results, or a new completion is triggered.
 * @param composerView The composer view instance.
 * @param requestIdentifier The identifier of the backend request(s) to cancel.
 */
- (void)composerView:(ComposerView *)composerView
    didCancelCompletionRequestWithIdentifier:(NSNumber *)requestIdentifier;

/**
 * @brief Notifies the delegate that the active model selection changed.
 *
 * @param composerView The composer view instance.
 * @param provider The provider identifier (e.g., "openai").
 * @param model The model identifier (e.g., "gpt-4o").
 */
- (void)composerView:(ComposerView *)composerView
 didChangeModelProvider:(NSString *)provider
                model:(NSString *)model;

@end 