#import "sidebar_view_controller.h"
#import "sidebar_item.h"
#import "sidebar_cell_view.h"
#import <AppKit/AppKit.h>
#include "../../core/util/debug.h"
#import "tabs/tab_group_manager.h"
#import "session_metadata_manager.h"
// Ensure ClosedTabInfo known
@class ClosedTabInfo;

static NSString *const kSidebarColumnIdentifier = @"sidebar_column";
static NSString *const kSidebarCellIdentifier   = @"sidebar_cell";

@interface SidebarViewController ()
@property (nonatomic, strong) NSOutlineView *outlineView;
@property (nonatomic, strong) NSArray<SidebarItem *> *rootItems;
@property (nonatomic, strong) TabGroupManager *tabManager;
@end

@implementation SidebarViewController

#pragma mark - View lifecycle

- (void)loadView {
    DBM(@"Loading SidebarViewController view");
    // Visual effect container for modern macOS sidebar appearance
    NSVisualEffectView *visualEffectView = [[NSVisualEffectView alloc] init];
    visualEffectView.material = NSVisualEffectMaterialSidebar;
    visualEffectView.blendingMode = NSVisualEffectBlendingModeBehindWindow;
    visualEffectView.state = NSVisualEffectStateActive;
    self.view = visualEffectView;

    // Configure outline view inside a scroll view
    NSScrollView *scrollView = [[NSScrollView alloc] init];
    scrollView.translatesAutoresizingMaskIntoConstraints = NO;
    scrollView.hasVerticalScroller = YES;
    scrollView.drawsBackground = NO; // Inherit material

    self.outlineView = [[NSOutlineView alloc] init];
    self.outlineView.translatesAutoresizingMaskIntoConstraints = NO;
    self.outlineView.headerView = nil; // No table header
    self.outlineView.selectionHighlightStyle = NSTableViewSelectionHighlightStyleSourceList;
    self.outlineView.rowSizeStyle = NSTableViewRowSizeStyleDefault;
    self.outlineView.floatsGroupRows = YES; // Enable floating group rows for visual separation
    self.outlineView.style = NSTableViewStyleSourceList; // Modern source list style

    // Create single column
    NSTableColumn *column = [[NSTableColumn alloc] initWithIdentifier:kSidebarColumnIdentifier];
    column.resizingMask = NSTableColumnAutoresizingMask;
    [self.outlineView addTableColumn:column];
    self.outlineView.outlineTableColumn = column;

    // Set datasource / delegate
    [self.outlineView setDelegate:self];
    [self.outlineView setDataSource:self];

    scrollView.documentView = self.outlineView;
    [self.view addSubview:scrollView];

    // Layout constraints
    [NSLayoutConstraint activateConstraints:@[
        [scrollView.topAnchor constraintEqualToAnchor:self.view.topAnchor],
        [scrollView.bottomAnchor constraintEqualToAnchor:self.view.bottomAnchor],
        [scrollView.leadingAnchor constraintEqualToAnchor:self.view.leadingAnchor],
        [scrollView.trailingAnchor constraintEqualToAnchor:self.view.trailingAnchor],
        [self.view.widthAnchor constraintGreaterThanOrEqualToConstant:180]
    ]];
}

- (void)viewDidLoad {
    [super viewDidLoad];
    DBM(@"SidebarViewController view did load");
    self.tabManager = [TabGroupManager sharedManager];
    [self registerForTabManagerNotifications];
    [self rebuildDataFromTabManager];
    [self.outlineView reloadData];
    if (self.rootItems.count > 0) {
        [self.outlineView expandItem:self.rootItems.firstObject];
    }

    // NEW: Ensure freshly created windows are fully initialised in the sidebar.
    // When a brand-new window is opened via Cmd+N, this SidebarViewController is
    // created *after* the TabGroupManager registration, but before any further
    // structure/selection notifications are emitted.  Therefore run the same
    // helpers that our notification handlers use so the UI starts in a correct
    // state without waiting for external events.
    NSWindow *activeWindow = [[TabGroupManager sharedManager] selectedWindow];
    if (activeWindow) {
        // Expand the corresponding group and highlight its row.
        [self highlightRowForWindow:activeWindow];
        // Apply icon + tint to the tab itself.
        [self applyIconToWindowTab:activeWindow];
    }
    // Refresh all tab icons to ensure correct tinting across the sidebar.
    [self updateAllWindowTabIcons];
}

- (void)dealloc {
    DBM(@"Deallocating SidebarViewController");
    // Ensure we deregister from all NSNotificationCenter observers to avoid leaks.
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

#pragma mark - Data Construction

- (void)buildSampleData {
    // 1) Create Tab Groups parent as a normal expandable row (not a group header)
    NSImage *tabGroupsIcon = [NSImage imageWithSystemSymbolName:@"square.stack.3d.up" accessibilityDescription:nil];
    SidebarItem *tabGroups = [SidebarItem leafWithTitle:@"Tab Groups" icon:tabGroupsIcon];

    // Define palette similar to Safari tag colours
    NSArray<NSColor *> *palette = @[
        [NSColor systemRedColor],
        [NSColor systemOrangeColor],
        [NSColor systemYellowColor],
        [NSColor systemGreenColor],
        [NSColor systemBlueColor],
        [NSColor systemPurpleColor]
    ];

    NSArray *tabGroupNames = @[ @"computer use", @"cursor", @"cool", @"kai", @"bolt", @"objc github" ];

    // Use "macwindow" symbol when available; fall back to the classic folder symbol on older macOS.
    NSImage *groupIcon = nil;
    if (@available(macOS 11.0, *)) {
        groupIcon = [NSImage imageWithSystemSymbolName:@"macwindow" accessibilityDescription:nil];
    }
    if (!groupIcon) {
        groupIcon = [NSImage imageWithSystemSymbolName:@"folder" accessibilityDescription:nil];
    }

    NSMutableArray *tabGroupItems = [NSMutableArray arrayWithCapacity:tabGroupNames.count];
    for (NSUInteger idx = 0; idx < tabGroupNames.count; ++idx) {
        NSString *name = tabGroupNames[idx];
        NSColor *tint = palette[idx % palette.count];
        SidebarItem *item = [SidebarItem leafWithTitle:name icon:groupIcon];
        item.userInfo = tint; // store tint colour
        [tabGroupItems addObject:item];
    }
    tabGroups.children = tabGroupItems;
    tabGroups.userInfo = [NSColor systemTealColor]; // tint for parent icon

    // Spacer as empty group header to push following items to bottom visually
    SidebarItem *spacer = [SidebarItem groupWithTitle:@"" children:@[]];

    // Bottom utility sections with unique tint colours
    SidebarItem *bookmarks = [SidebarItem leafWithTitle:@"Bookmarks" 
                                                   icon:[NSImage imageWithSystemSymbolName:@"bookmark" accessibilityDescription:nil]];
    bookmarks.userInfo = [NSColor systemOrangeColor];

    SidebarItem *readingList = [SidebarItem leafWithTitle:@"Reading List" 
                                                    icon:[NSImage imageWithSystemSymbolName:@"book" accessibilityDescription:nil]];
    readingList.userInfo = [NSColor systemGreenColor];

    SidebarItem *sharedWithYou = [SidebarItem leafWithTitle:@"Shared with You" 
                                                      icon:[NSImage imageWithSystemSymbolName:@"person.2" accessibilityDescription:nil]];
    sharedWithYou.userInfo = [NSColor systemPurpleColor];

    // Root ordering: Tab groups first, spacer header, then bottom items
    self.rootItems = @[tabGroups, spacer, bookmarks, readingList, sharedWithYou];
}

#pragma mark - Window Tab Icon Integration

- (void)applyIconToWindowTab:(NSWindow *)window {
    if (@available(macOS 10.13, *)) {
        NSWindowTab *tab = window.tab;
        if (!tab) return;
        
        // Get the window title
        NSString *title = window.title ?: @"Untitled";
        
        // Retrieve icon symbol name from metadata manager
        NSString *symbolName = launcher::ui::SessionMetadataManager::shared().iconSymbolNameForWindow(window);
        if (!symbolName) {
            // If no symbol, just set the regular title
            tab.title = title;
            tab.accessoryView = nil;
            return;
        }
        
        // Create and configure the SF Symbol image
        NSImage *iconImage = nil;
        if (@available(macOS 11.0, *)) {
            iconImage = [NSImage imageWithSystemSymbolName:symbolName accessibilityDescription:nil];
            
            // Configure symbol appearance for tab bar
            NSImageSymbolConfiguration *config = [NSImageSymbolConfiguration 
                configurationWithPointSize:13 weight:NSFontWeightRegular];
            iconImage = [iconImage imageWithSymbolConfiguration:config];
        } else {
            // Fallback for older macOS - could use asset catalog images
            iconImage = [NSImage imageNamed:symbolName];
        }
        
        if (!iconImage) {
            tab.title = title;
            tab.accessoryView = nil;
            return;
        }
        
        // Apply tint color if available
        NSColor *tint = launcher::ui::SessionMetadataManager::shared().tintColorForWindow(window);
        if (tint) {
            // Use template image with tint color instead of imageWithTintColor: which doesn't exist
            [iconImage setTemplate:YES];
            NSImage *tintedImage = [iconImage copy];
            [tintedImage lockFocus];
            [tint set];
            NSRect imageRect = {NSZeroPoint, [tintedImage size]};
            NSRectFillUsingOperation(imageRect, NSCompositingOperationSourceAtop);
            [tintedImage unlockFocus];
            iconImage = tintedImage;
        }
        
        // Create attributed string with the icon at the start
        NSTextAttachment *attachment = [[NSTextAttachment alloc] init];
        attachment.image = iconImage;
        
        // Adjust the attachment bounds to align vertically with text
        CGFloat imageSize = 13.0;
        attachment.bounds = NSMakeRect(0, -1, imageSize, imageSize); // -3 to vertically align with text
        
        NSAttributedString *imageString = [NSAttributedString attributedStringWithAttachment:attachment];
        
        // Create the full attributed string with icon + title
        NSMutableAttributedString *attributedTitle = [[NSMutableAttributedString alloc] initWithAttributedString:imageString];
        
        // Add a space between icon and text
        [attributedTitle appendAttributedString:[[NSAttributedString alloc] initWithString:@" "]];
        
        // Add the title text
        [attributedTitle appendAttributedString:[[NSAttributedString alloc] initWithString:title]];
        
        // ---------------- Add glow around tab title ----------------
        NSColor *shadowColor = tint ?: [NSColor controlAccentColor];
        NSShadow *titleGlow = [[NSShadow alloc] init];
        titleGlow.shadowColor = [shadowColor colorWithAlphaComponent:0.9];
        titleGlow.shadowBlurRadius = 6.0;
        titleGlow.shadowOffset = NSZeroSize;

        // Apply the shadow to the full range (icon + text) so both elements glow
        [attributedTitle addAttribute:NSShadowAttributeName
                                value:titleGlow
                                range:NSMakeRange(0, attributedTitle.length)];
        
        // Set the attributed title on the tab
        tab.attributedTitle = attributedTitle;
        
        // Clear any existing accessory view
        tab.accessoryView = nil;
    }
}

- (void)updateAllWindowTabIcons {
    for (NSString *groupId in [self.tabManager allGroupIds]) {
        for (NSWindow *window in [self.tabManager windowsInGroup:groupId]) {
            [self applyIconToWindowTab:window];
        }
    }
}

- (void)ensureTabBarVisibleForWindow:(NSWindow *)window {
    if (@available(macOS 10.13, *)) {
        window.tabbingMode = NSWindowTabbingModePreferred;
    }
}

#pragma mark - Tab Group Manager Integration

- (void)registerForTabManagerNotifications {
    NSNotificationCenter *nc = [NSNotificationCenter defaultCenter];
    [nc addObserver:self selector:@selector(tabManagerStructureChanged:) name:kTabGroupManagerTabAddedNotification object:nil];
    [nc addObserver:self selector:@selector(tabManagerStructureChanged:) name:kTabGroupManagerTabRemovedNotification object:nil];
    [nc addObserver:self selector:@selector(tabManagerStructureChanged:) name:kTabGroupManagerTabClosedNotification object:nil];
    [nc addObserver:self selector:@selector(tabManagerStructureChanged:) name:kTabGroupManagerGroupAddedNotification object:nil];
    [nc addObserver:self selector:@selector(tabManagerStructureChanged:) name:kTabGroupManagerGroupTitleChangedNotification object:nil];
    [nc addObserver:self selector:@selector(tabManagerSelectionChanged:) name:kTabGroupManagerSelectionChangedNotification object:nil];
    [nc addObserver:self selector:@selector(metadataTitleUpdated:) name:kSessionMetadataManagerTitleUpdatedNotification object:nil];
}

- (void)tabManagerStructureChanged:(NSNotification *)note {
    // Capture currently expanded groups by their identifier
    NSMutableSet<NSString *> *expandedIds = [NSMutableSet set];
    for (SidebarItem *item in self.rootItems) {
        if (item.groupId && [self.outlineView isItemExpanded:item]) {
            [expandedIds addObject:item.groupId];
        }
    }

    [self rebuildDataFromTabManager];
    [self.outlineView reloadData];

    // Restore disclosure state
    for (SidebarItem *item in self.rootItems) {
        if (item.groupId && [expandedIds containsObject:item.groupId]) {
            [self.outlineView expandItem:item];
        }
    }

    // NEW: Ensure the group containing the currently-selected window is visible.
    // When a second tab is inserted into a freshly torn-out window, the logical
    // group now has >1 child but may still be collapsed.  Expanding here makes
    // the new tab immediately discoverable without waiting for a user click.
    NSWindow *activeWindow = [[TabGroupManager sharedManager] selectedWindow];
    if (activeWindow) {
        [self highlightRowForWindow:activeWindow]; // this also expands its group
        [self applyIconToWindowTab:activeWindow]; // Apply icon to the active window tab
    }
    
    // Update all window tab icons after structure changes
    [self updateAllWindowTabIcons];
}

- (void)tabManagerSelectionChanged:(NSNotification *)note {
    NSWindow *selectedWindow = note.userInfo[@"window"];
    if (!selectedWindow || (id)selectedWindow == [NSNull null]) return;
    [self highlightRowForWindow:selectedWindow];
    
    // Apply icon to the newly selected window tab
    [self applyIconToWindowTab:selectedWindow];
    
    // Ensure tab bar is visible for this window
    [self ensureTabBarVisibleForWindow:selectedWindow];
}

- (void)rebuildDataFromTabManager {
    NSMutableArray<SidebarItem *> *groupsArray = [NSMutableArray array];

    // Use "macwindow" symbol for tab groups where available, otherwise fall back.
    NSImage *groupIcon = nil;
    if (@available(macOS 11.0, *)) {
        groupIcon = [NSImage imageWithSystemSymbolName:@"macwindow" accessibilityDescription:nil];
    }
    if (!groupIcon) {
        groupIcon = [NSImage imageWithSystemSymbolName:@"folder" accessibilityDescription:nil];
    }

    for (NSString *groupId in [self.tabManager allGroupIds]) {
        NSString *title = [self.tabManager groupTitleForId:groupId] ?: @"Group";
        NSColor *tint = [self.tabManager groupTintForId:groupId] ?: [NSColor labelColor];

        SidebarItem *groupItem = [SidebarItem leafWithTitle:title icon:groupIcon];
        groupItem.userInfo = tint;
        groupItem.groupId = groupId;

        NSMutableArray<SidebarItem *> *tabItems = [NSMutableArray array];
        // ------------------ Open Tabs ------------------
        for (NSWindow *w in [self.tabManager windowsInGroup:groupId]) {
            NSString *tabTitle = w.title ?: @"Untitled";
            // Retrieve icon from SessionMetadataManager
            NSString *symbolName = launcher::ui::SessionMetadataManager::shared().iconSymbolNameForWindow(w);
            NSImage *tabIcon = nil;
            if (symbolName) {
                tabIcon = [NSImage imageWithSystemSymbolName:symbolName accessibilityDescription:nil];
            }
            SidebarItem *tabItem = [SidebarItem leafWithTitle:tabTitle icon:tabIcon];
            // Keep window reference in userInfo
            tabItem.userInfo = w;
            [tabItems addObject:tabItem];
        }
        // ------------------ Closed Tabs ------------------
        NSArray<id> *closed = [self.tabManager closedTabsInGroup:groupId];
        for (id info in closed) {
            NSString *tabTitle = [info valueForKey:@"title"] ?: @"Untitled";
            NSString *iconSym = [info valueForKey:@"iconSymbolName"];
            NSImage *tabIcon = nil;
            if (iconSym) {
                tabIcon = [NSImage imageWithSystemSymbolName:iconSym accessibilityDescription:nil];
            }
            SidebarItem *tabItem = [SidebarItem leafWithTitle:tabTitle icon:tabIcon];
            tabItem.userInfo = info; // opaque closedTabInfo pointer
            [tabItems addObject:tabItem];
        }
        // Skip rendering empty tab groups (no open or closed tabs)
        if (tabItems.count == 0) {
            continue; // Do not add this group to the sidebar.
        }
        groupItem.children = tabItems;
        [groupsArray addObject:groupItem];
    }

    self.rootItems = groupsArray;
}

- (void)highlightRowForWindow:(NSWindow *)window {
    if (!window) return;

    for (SidebarItem *group in self.rootItems) {
        for (SidebarItem *tabItem in group.children) {
            if (tabItem.userInfo == window) {
                [self.outlineView expandItem:group];
                NSInteger row = [self.outlineView rowForItem:tabItem];
                if (row >= 0) {
                    [self.outlineView selectRowIndexes:[NSIndexSet indexSetWithIndex:row] byExtendingSelection:NO];

                    // Ensure the outline view is first-responder when we select programmatically
                    // so the row is highlighted using the emphasised (blue) style immediately.
                    if (self.view.window.firstResponder != self.outlineView) {
                        [self.view.window makeFirstResponder:self.outlineView];
                    }
                }
                return;
            }
        }
    }
}

#pragma mark - Metadata Notifications

- (void)metadataTitleUpdated:(NSNotification *)note {
    // Preserve current expanded group ids so UI state remains stable after reload.
    NSMutableSet<NSString *> *expandedIds = [NSMutableSet set];
    for (SidebarItem *item in self.rootItems) {
        if (item.groupId && [self.outlineView isItemExpanded:item]) {
            [expandedIds addObject:item.groupId];
        }
    }

    [self rebuildDataFromTabManager];
    [self.outlineView reloadData];

    // Restore expansion state for previously expanded groups.
    for (SidebarItem *item in self.rootItems) {
        if (item.groupId && [expandedIds containsObject:item.groupId]) {
            [self.outlineView expandItem:item];
        }
    }

    // Highlight the row of the window whose title changed (if provided).
    if ([note.object isKindOfClass:[NSWindow class]]) {
        NSWindow *window = (NSWindow *)note.object;
        [self highlightRowForWindow:window];
        
        // Update the window tab icon when metadata changes
        [self applyIconToWindowTab:window];
    }
    
    // Update all window tab icons after metadata changes
    [self updateAllWindowTabIcons];
}

#pragma mark - NSOutlineViewDataSource

- (NSInteger)outlineView:(NSOutlineView *)outlineView numberOfChildrenOfItem:(id)item {
    SidebarItem *sidebarItem = item ?: nil;
    if (!sidebarItem) {
        return self.rootItems.count;
    }
    return sidebarItem.children.count;
}

- (id)outlineView:(NSOutlineView *)outlineView child:(NSInteger)index ofItem:(id)item {
    SidebarItem *sidebarItem = item ?: nil;
    if (!sidebarItem) {
        return self.rootItems[index];
    }
    return sidebarItem.children[index];
}

- (BOOL)outlineView:(NSOutlineView *)outlineView isItemExpandable:(id)item {
    SidebarItem *sidebarItem = (SidebarItem *)item;
    return sidebarItem.children.count > 0;
}

#pragma mark - NSOutlineViewDelegate

- (nullable NSView *)outlineView:(NSOutlineView *)outlineView viewForTableColumn:(NSTableColumn *)tableColumn item:(id)item {
    SidebarItem *sidebarItem = (SidebarItem *)item;

    if (sidebarItem.groupHeader) {
        // Use default group row drawing (return nil) so AppKit renders embossed header.
        return nil;
    }

    SidebarCellView *cell = [outlineView makeViewWithIdentifier:kSidebarCellIdentifier owner:self];
    if (!cell) {
        cell = [[SidebarCellView alloc] initWithFrame:NSZeroRect];
        cell.identifier = kSidebarCellIdentifier;
    }

    cell.textField.stringValue = sidebarItem.title ?: @"";
    
    // Allow inline editing only for tab rows (represented by NSWindow in userInfo).
    BOOL isTabRow = [sidebarItem.userInfo isKindOfClass:[NSWindow class]];
    BOOL isGroupRow = (sidebarItem.groupId != nil);
    BOOL editable = isTabRow || isGroupRow;
    cell.textField.editable = editable;
    cell.textField.selectable = editable;
    cell.textField.delegate = self; // Monitor editing end
    
    if (sidebarItem.icon) {
        cell.imageView.image = sidebarItem.icon;
        NSColor *tint = [NSColor labelColor];
        if ([sidebarItem.userInfo isKindOfClass:[NSColor class]]) {
            tint = (NSColor *)sidebarItem.userInfo;
        } else if ([sidebarItem.userInfo isKindOfClass:[NSWindow class]]) {
            tint = launcher::ui::SessionMetadataManager::shared().tintColorForWindow((NSWindow *)sidebarItem.userInfo);
        } else if ([sidebarItem.userInfo isKindOfClass:NSClassFromString(@"ClosedTabInfo")]) {
            id info = sidebarItem.userInfo;
            NSColor *tc = [info valueForKey:@"tintColor"];
            tint = tc ?: [NSColor labelColor];
        }
        cell.imageView.contentTintColor = tint;
    } else {
        cell.imageView.image = nil;
    }
    // NEW: adjust appearance for closed tab rows & set up close button
    BOOL isClosedTab = [sidebarItem.userInfo isKindOfClass:NSClassFromString(@"ClosedTabInfo")];
    [cell setClosedAppearance:isClosedTab];

    if (isClosedTab) {
        NSButton *closeBtn = cell.closeButton;
        closeBtn.target = self;
        closeBtn.action = @selector(removeClosedTab:);
    } else {
        cell.closeButton.hidden = YES;
    }

    return cell;
}

- (BOOL)outlineView:(NSOutlineView *)outlineView isGroupItem:(id)item {
    SidebarItem *sidebarItem = (SidebarItem *)item;
    return sidebarItem.groupHeader;
}

- (CGFloat)outlineView:(NSOutlineView *)outlineView heightOfRowByItem:(id)item {
    if ([item isKindOfClass:[SidebarItem class]] && ((SidebarItem *)item).groupHeader) {
        return 24.0; // Group header height
    }
    return 28.0; // Regular item height
}

- (void)outlineViewSelectionDidChange:(NSNotification *)notification {
    NSInteger selectedRow = self.outlineView.selectedRow;
    if (selectedRow < 0) return;
    SidebarItem *item = [self.outlineView itemAtRow:selectedRow];
    if (item.groupHeader) return; // Ignore header clicks

    // Ensure the outline view becomes first-responder so the blue accent
    // selection highlight is shown on the first click even when focus
    // switches between different windows/tabs.
    if (self.view.window.firstResponder != self.outlineView) {
        [self.view.window makeFirstResponder:self.outlineView];
    }

    // When the user selects a row that represents a window, broadcast that as the
    // new active selection via the TabGroupManager.  This decouples the sidebar
    // from any particular window controller implementation.
    if ([item.userInfo isKindOfClass:[NSWindow class]]) {
        NSWindow *win = (NSWindow *)item.userInfo;
        [[TabGroupManager sharedManager] setSelectedWindow:win];
    } else if ([item.userInfo isKindOfClass:NSClassFromString(@"ClosedTabInfo")]) {
        id info = item.userInfo;
        // Attempt to reopen
        BOOL ok = [[TabGroupManager sharedManager] reopenClosedTab:info anchorWindow:nil];
        if (!ok) {
            NSSound *beep = [NSSound soundNamed:@"Basso"]; [beep play];
        }
    }
}

#pragma mark - Inline Editing Support

- (BOOL)outlineView:(NSOutlineView *)outlineView
 shouldEditTableColumn:(NSTableColumn *)tableColumn
                 item:(id)item {
    SidebarItem *sItem = (SidebarItem *)item;
    BOOL isTabRow = [sItem.userInfo isKindOfClass:[NSWindow class]];
    BOOL isGroupRow = (sItem.groupId != nil);
    return isTabRow || isGroupRow;
}

- (void)outlineView:(NSOutlineView *)outlineView
    setObjectValue:(id)object
    forTableColumn:(NSTableColumn *)tableColumn
            byItem:(id)item {
    SidebarItem *sItem = (SidebarItem *)item;
    NSString *newTitle = [(NSString *)object stringByTrimmingCharactersInSet:[NSCharacterSet whitespaceAndNewlineCharacterSet]];
    if (newTitle.length == 0) {
        newTitle = @"Untitled";
    }

    // Update model immediately so UI reflects change while editing.
    sItem.title = newTitle;

    if ([sItem.userInfo isKindOfClass:[NSWindow class]]) {
        // existing window rename logic unchanged
        NSWindow *win = (NSWindow *)sItem.userInfo;
        win.title = newTitle;
        launcher::ui::SessionMetadataManager::shared().applyMetadataForWindow(win, nil, nil, newTitle);
        
        // Update the window tab icon after title change
        [self applyIconToWindowTab:win];
    } else if (sItem.groupId) {
        // Group row rename – propagate to TabGroupManager
        [[TabGroupManager sharedManager] setTitle:newTitle forGroupId:sItem.groupId];
    }

    // Reload row (same as before)
    NSInteger row = [outlineView rowForItem:item];
    if (row >= 0) {
        NSIndexSet *rows = [NSIndexSet indexSetWithIndex:row];
        NSIndexSet *cols = [NSIndexSet indexSetWithIndex:[outlineView columnWithIdentifier:kSidebarColumnIdentifier]];
        [outlineView reloadDataForRowIndexes:rows columnIndexes:cols];
    }
}

// NSTextFieldDelegate – commit edits when user finishes editing
- (void)controlTextDidEndEditing:(NSNotification *)obj {
    NSTextField *field = obj.object;
    // Determine which row/view this text field belongs to
    NSInteger row = [self.outlineView rowForView:field];
    if (row < 0) { return; }

    SidebarItem *sItem = [self.outlineView itemAtRow:row];
    if (sItem.groupId) {
        NSString *newTitle = [field.stringValue stringByTrimmingCharactersInSet:[NSCharacterSet whitespaceAndNewlineCharacterSet]];
        if (newTitle.length == 0) { newTitle = @"Untitled"; }
        if (![newTitle isEqualToString:sItem.title]) {
            sItem.title = newTitle;
            [[TabGroupManager sharedManager] setTitle:newTitle forGroupId:sItem.groupId];

            NSIndexSet *rows = [NSIndexSet indexSetWithIndex:row];
            NSIndexSet *cols = [NSIndexSet indexSetWithIndexesInRange:NSMakeRange(0, self.outlineView.tableColumns.count)];
            [self.outlineView reloadDataForRowIndexes:rows columnIndexes:cols];
        }
        return;
    }

    if (![sItem.userInfo isKindOfClass:[NSWindow class]]) { return; }

    NSString *newTitle = [field.stringValue stringByTrimmingCharactersInSet:[NSCharacterSet whitespaceAndNewlineCharacterSet]];
    if (newTitle.length == 0) { newTitle = @"Untitled"; }

    if ([newTitle isEqualToString:sItem.title]) { return; } // No change

    sItem.title = newTitle;

    NSWindow *win = (NSWindow *)sItem.userInfo;
    win.title = newTitle;
    launcher::ui::SessionMetadataManager::shared().applyMetadataForWindow(win, nil, nil, newTitle);

    // Refresh this row for updated display
    NSIndexSet *rows = [NSIndexSet indexSetWithIndex:row];
    NSIndexSet *cols = [NSIndexSet indexSetWithIndexesInRange:NSMakeRange(0, self.outlineView.tableColumns.count)];
    [self.outlineView reloadDataForRowIndexes:rows columnIndexes:cols];
}

#pragma mark - Closed-tab removal

- (void)removeClosedTab:(id)sender {
    NSButton *btn = (NSButton *)sender;
    if (!btn) { return; }
    // Determine which row this button lives in
    SidebarCellView *cell = (SidebarCellView *)btn.superview;
    if (!cell || ![cell isKindOfClass:[SidebarCellView class]]) { return; }
    NSInteger row = [self.outlineView rowForView:cell];
    if (row < 0) { return; }
    SidebarItem *item = [self.outlineView itemAtRow:row];
    id info = item.userInfo;
    if (!info || ![info isKindOfClass:NSClassFromString(@"ClosedTabInfo")]) { return; }
    [[TabGroupManager sharedManager] removeClosedTab:info];
}

@end 