#import "sidebar_manager.h"
#import "sidebar_view_controller.h"
#include "../../core/util/debug.h"

/// Identifier string used by TopChatWindowController when enabling system tabbing.  Keep in sync!
static NSString *const kChatTabbingIdentifier = @"ChatWindow";

// Helper container storing sidebar components per window
@interface SidebarBundle : NSObject
@property (nonatomic, strong) SidebarViewController *sidebarVC;
@property (nonatomic, strong) NSSplitViewItem *sidebarItem;
@property (nonatomic, assign) BOOL outlineHadFocus;
@end

@implementation SidebarBundle
@end

@interface SidebarManager ()

// Keyed by NSValue(pointer) to NSSplitViewController* → SidebarBundle*
@property (nonatomic, strong) NSMutableDictionary<NSValue *, SidebarBundle *> *bundles_;

@end

@implementation SidebarManager

#pragma mark - Singleton

+ (instancetype)sharedManager {
    static SidebarManager *instance = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        instance = [[SidebarManager alloc] initPrivate];
    });
    return instance;
}

// Prevent use of plain init
- (instancetype)init {
    @throw [NSException exceptionWithName:@"Singleton"
                                   reason:@"Use +sharedManager"
                                 userInfo:nil];
    return nil;
}

- (instancetype)initPrivate {
    self = [super init];
    if (self) {
        DBM(@"Initializing SidebarManager");

        // Prepare bundle storage
        _bundles_ = [[NSMutableDictionary alloc] init];

        // Observe when a different window becomes key/main so we can ensure a sidebar bundle is attached.
        [[NSNotificationCenter defaultCenter] addObserver:self
                                                 selector:@selector(windowDidBecomeMain:)
                                                     name:NSWindowDidBecomeMainNotification
                                                   object:nil];

        // Cleanup bundles when their window is about to close
        [[NSNotificationCenter defaultCenter] addObserver:self
                                                 selector:@selector(windowWillClose:)
                                                     name:NSWindowWillCloseNotification
                                                   object:nil];
    }
    return self;
}

#pragma mark - Public surface

- (NSSplitViewItem *)sidebarItem {
    // Legacy: return the first item's sidebar if available
    SidebarBundle *bundle = self.bundles_.allValues.firstObject;
    return bundle.sidebarItem;
}

- (SidebarViewController *)sidebarVC {
    SidebarBundle *bundle = self.bundles_.allValues.firstObject;
    return bundle.sidebarVC;
}

- (void)attachSidebarToSplitViewController:(NSSplitViewController *)svc {
    if (!svc) { return; }

    NSValue *key = [NSValue valueWithPointer:(__bridge const void *)(svc)];
    SidebarBundle *bundle = self.bundles_[key];

    if (!bundle) {
        // --- Create a fresh sidebar bundle for this split view controller ---
        DBM(@"Creating sidebar for split view controller %p", svc);

        SidebarViewController *vc = [[SidebarViewController alloc] init];
        NSSplitViewItem *item = [NSSplitViewItem sidebarWithViewController:vc];
        item.allowsFullHeightLayout = YES;
        item.minimumThickness = 150;
        item.canCollapse = YES;

        bundle = [[SidebarBundle alloc] init];
        bundle.sidebarVC = vc;
        bundle.sidebarItem = item;
        bundle.outlineHadFocus = NO;
        self.bundles_[key] = bundle;
    }

    // Ensure the sidebar item is hosted by the requested split view.
    if (![svc.splitViewItems containsObject:bundle.sidebarItem]) {
        if ([svc respondsToSelector:@selector(attachSidebarItem:)]) {
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Warc-performSelector-leaks"
            [svc performSelector:@selector(attachSidebarItem:) withObject:bundle.sidebarItem];
#pragma clang diagnostic pop
        } else {
            [svc insertSplitViewItem:bundle.sidebarItem atIndex:0];
        }
    }
}

#pragma mark - Notification callbacks

- (void)windowDidBecomeMain:(NSNotification *)note {
    NSWindow *win = note.object;
    if (!win) { return; }

    if (![win.tabbingIdentifier isEqualToString:kChatTabbingIdentifier]) {
        // Ignore unrelated windows
        return;
    }

    NSSplitViewController *svc = (NSSplitViewController *)win.contentViewController;
    if (!svc || ![svc isKindOfClass:[NSSplitViewController class]]) { return; }

    [self attachSidebarToSplitViewController:svc];
}

- (void)windowWillClose:(NSNotification *)note {
    NSWindow *win = note.object;
    NSSplitViewController *svc = (NSSplitViewController *)win.contentViewController;
    if (!svc) { return; }

    NSValue *key = [NSValue valueWithPointer:(__bridge const void *)(svc)];
    if (self.bundles_[key]) {
        DBM(@"Removing sidebar bundle for split view controller %p", svc);
        [self.bundles_ removeObjectForKey:key];
    }
}

#pragma mark - Cleanup

- (void)dealloc {
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

@end 