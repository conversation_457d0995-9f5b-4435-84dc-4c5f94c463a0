#import "chat_root_view_controller.h"
#import "sidebar_view_controller.h"
#import "chat_content_view_controller.h"
#include "../../core/util/debug.h"
#import "tabs/tab_group_manager.h"

@interface ChatRootViewController () <SidebarViewControllerDelegate>
@property (nonatomic, strong, readwrite) SidebarViewController *sidebarVC;
@property (nonatomic, strong, readwrite) ChatContentViewController *contentVC;
@property (nonatomic, strong, readwrite) NSSplitViewItem *sidebarSplitViewItem;
@end

@implementation ChatRootViewController

- (instancetype)init {
    self = [super init];
    if (self) {
        DBM(@"Initializing ChatRootViewController");

        // NOTE: The sidebar is now managed by SidebarManager and will be attached later.

        // Create the content view controller (remains per-tab as before)
        _contentVC = [[ChatContentViewController alloc] init];

        // Create the content split view item with the content view controller
        NSSplitViewItem *contentSplitViewItem = [NSSplitViewItem contentListWithViewController:_contentVC];

        // Add only the content item for now.  The sidebar will be inserted at index 0 later.
        [self addSplitViewItem:contentSplitViewItem];

        // Split-view appearance
        self.splitView.vertical = YES; // Arrange horizontally
        self.splitView.dividerStyle = NSSplitViewDividerStyleThin;
    }
    return self;
}

- (void)viewDidLoad {
    [super viewDidLoad];
    DBM(@"ChatRootViewController view did load");
}

- (void)toggleSidebar:(nullable id)sender {
    if (!self.sidebarSplitViewItem) {
        ERM(@"Sidebar split view item is nil, cannot toggle.");
        return;
    }

    BOOL isCollapsed = self.sidebarSplitViewItem.isCollapsed;
    DBM(@"Toggling sidebar, current collapsed state: %d", isCollapsed);

    // Use the animator proxy for smooth animation
    [[self.sidebarSplitViewItem animator] setCollapsed:!isCollapsed];
    
    // Focus on the chat input after toggling
    [self.contentVC focusOnChatInput];
}

- (void)dealloc {
    DBM(@"Deallocating ChatRootViewController");
}

#pragma mark - SidebarManager hook

- (void)attachSidebarItem:(NSSplitViewItem *)item {
    if (!item) { return; }

    // Remove any existing sidebar item first (should not generally happen)
    if (self.sidebarSplitViewItem && self.sidebarSplitViewItem != item) {
        [self removeSplitViewItem:self.sidebarSplitViewItem];
    }

    // Determine the current host of the item via parentViewController.
    NSSplitViewController *currentHost = nil;
    if (item.viewController.parentViewController &&
        [item.viewController.parentViewController isKindOfClass:[NSSplitViewController class]]) {
        currentHost = (NSSplitViewController *)item.viewController.parentViewController;
    }

    // Insert only if not already hosted here.
    if (currentHost != self) {
        [self insertSplitViewItem:item atIndex:0];
    }

    // Cache for later toggling
    self.sidebarSplitViewItem = item;

    // Update convenience pointer and delegate so selection callbacks reach this root controller.
    if ([item.viewController isKindOfClass:[SidebarViewController class]]) {
        self.sidebarVC = (SidebarViewController *)item.viewController;
        self.sidebarVC.delegate = self;
    }
}

#pragma mark - SidebarViewControllerDelegate

- (void)sidebarViewController:(id)controller didSelectWindow:(NSWindow *)window {
    if (!window) return;

    // Update the tab manager first
    [[TabGroupManager sharedManager] setSelectedWindow:window];

    // Switch to the corresponding window/tab in the tab bar
    if (@available(macOS 10.12, *)) {
        NSWindow *curWin = self.view.window;
        if (curWin && curWin.tabGroup) {
            // This automatically brings the target tab forward
            curWin.tabGroup.selectedWindow = window;
        } else {
            [window makeKeyAndOrderFront:nil];
        }
    } else {
        [window makeKeyAndOrderFront:nil];
    }
}

@end 