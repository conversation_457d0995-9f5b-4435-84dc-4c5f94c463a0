#import "CacheManager.h"

/**
 * Category extension for CacheManager providing convenience methods for working with 
 * auto-completion suggestions in the cache system.
 */
@interface CacheManager (Suggestions)

/**
 * Retrieves suggestion strings from the cache for the specified key.
 *
 * @param key The cache key to retrieve suggestions for.
 * @return An array of NSString suggestions, or nil if none found in the cache.
 */
- (nullable NSArray<NSString *> *)suggestionsForKey:(NSString *)key;

/**
 * Stores suggestion strings in the cache with a default cost and TTL.
 *
 * @param suggestions The array of NSString suggestions to cache.
 * @param key The cache key to store them under.
 */
- (void)setSuggestions:(NSArray<NSString *> *)suggestions forKey:(NSString *)key;

/**
 * Stores suggestion strings in the cache with a specified TTL.
 *
 * @param suggestions The array of NSString suggestions to cache.
 * @param key The cache key to store them under.
 * @param ttlSeconds The time-to-live for these suggestions, in seconds.
 */
- (void)setSuggestions:(NSArray<NSString *> *)suggestions forKey:(NSString *)key ttl:(NSTimeInterval)ttlSeconds;

@end 