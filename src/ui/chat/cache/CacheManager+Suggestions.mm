#import "CacheManager+Suggestions.h"
#include "../../core/util/debug.h" // For DBM macro

// Constants for default caching behavior
static const NSUInteger kDefaultSuggestionCost = 10; // Cost per suggestion item
static const NSTimeInterval kDefaultSuggestionTTL = 300.0; // 5 minutes

@implementation CacheManager (Suggestions)

- (nullable NSArray<NSString *> *)suggestionsForKey:(NSString *)key {
    // Use the base CacheManager's objectForKey: method
    NSArray<NSString *> *suggestions = [self objectForKey:key];
    
    // Log cache hit/miss for debugging
    if (suggestions) {
        DBM("CacheManager+Suggestions.mm", "suggestionsForKey:", @"Cache HIT for key: %@, Found %lu suggestions", key, (unsigned long)suggestions.count);
    } else {
        DBM("CacheManager+Suggestions.mm", "suggestionsForKey:", @"Cache MISS for key: %@", key);
    }
    
    return suggestions;
}

- (void)setSuggestions:(NSArray<NSString *> *)suggestions forKey:(NSString *)key {
    // Use the default TTL
    [self setSuggestions:suggestions forKey:key ttl:kDefaultSuggestionTTL];
}

- (void)setSuggestions:(NSArray<NSString *> *)suggestions forKey:(NSString *)key ttl:(NSTimeInterval)ttlSeconds {
    if (!suggestions || suggestions.count == 0 || !key || key.length == 0) {
        DBM("CacheManager+Suggestions.mm", "setSuggestions:forKey:ttl:", @"Not caching empty suggestions or invalid key");
        return;
    }
    
    // Calculate cost based on the number of suggestions
    NSUInteger cost = suggestions.count * kDefaultSuggestionCost;
    
    // Call the base CacheManager's setObject:forKey:cost:ttl: method
    [self setObject:suggestions forKey:key cost:cost ttl:ttlSeconds];
    
    DBM("CacheManager+Suggestions.mm", "setSuggestions:forKey:ttl:", @"Cached %lu suggestions with key: %@, ttl: %.1fs", (unsigned long)suggestions.count, key, ttlSeconds);
}

@end 