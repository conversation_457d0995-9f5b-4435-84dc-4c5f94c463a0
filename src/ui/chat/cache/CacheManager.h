#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

/**
 * Manages caching for chat UI components including mentions, history, and LLM suggestions.
 * Implements thread-safe caching with TTL (time-to-live) support.
 */
@interface CacheManager : NSObject

/** Singleton instance */
+ (instancetype)sharedInstance;

/**
 * Sets a value in the cache with a cost and TTL.
 *
 * @param object The object to cache. Must be NSCoding compliant.
 * @param key The key to store it under.
 * @param cost The relative cost of storing the object (used for cache eviction decisions).
 * @param ttlSeconds Time-to-live in seconds. If 0, the object won't expire based on time.
 */
- (void)setObject:(id<NSCoding>)object forKey:(NSString *)key cost:(NSUInteger)cost ttl:(NSTimeInterval)ttlSeconds;

/**
 * Retrieves an object from the cache.
 *
 * @param key The key for the object.
 * @return The cached object, or nil if not found, invalid, or expired.
 */
- (nullable id)objectForKey:(NSString *)key;

/**
 * Removes an object from the cache.
 *
 * @param key The key for the object to remove.
 */
- (void)removeObjectForKey:(NSString *)key;

/**
 * Clears all objects from the cache.
 */
- (void)removeAllObjects;

/**
 * Generates a cache key for a mention query.
 *
 * @param query The mention query string.
 * @return A unique cache key for the query.
 */
- (NSString *)cacheKeyForMentionQuery:(NSString *)query;

/**
 * Generates a cache key for a history prefix.
 *
 * @param prefix The history prefix string.
 * @return A unique cache key for the prefix.
 */
- (NSString *)cacheKeyForHistoryPrefix:(NSString *)prefix;

/**
 * Generates a cache key for an LLM context using the V2 strategy.
 * This assumes the context has already been extracted by the caller to 
 * represent the relevant text for the suggestion.
 *
 * @param context The context string to generate a key for.
 * @return A unique cache key for the context.
 */
- (NSString *)cacheKeyForLLMContext:(NSString *)context;

@end

NS_ASSUME_NONNULL_END 