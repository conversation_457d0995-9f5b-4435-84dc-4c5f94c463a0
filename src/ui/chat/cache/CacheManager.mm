#import "CacheManager.h"
#import <CommonCrypto/CommonDigest.h> // For SHA256 hashing
#include "../../../core/util/debug.h"

// Internal CachedObject class to store objects with expiration
@interface CachedObject : NSObject <NSCoding>
@property (nonatomic, strong) id<NSCoding> object;
@property (nonatomic, assign) NSTimeInterval expirationDate; // NSTimeInterval as seconds since reference date

- (instancetype)initWithObject:(id<NSCoding>)object ttl:(NSTimeInterval)ttlSeconds;
- (BOOL)isExpired;
@end

@implementation CachedObject

- (instancetype)initWithObject:(id<NSCoding>)object ttl:(NSTimeInterval)ttlSeconds {
    self = [super init];
    if (self) {
        _object = object;
        
        if (ttlSeconds > 0) {
            _expirationDate = [NSDate timeIntervalSinceReferenceDate] + ttlSeconds;
        } else {
            _expirationDate = 0; // No expiration
        }
    }
    return self;
}

- (BOOL)isExpired {
    if (_expirationDate == 0) {
        return NO; // No expiration
    }
    return [NSDate timeIntervalSinceReferenceDate] > _expirationDate;
}

#pragma mark - NSCoding

- (instancetype)initWithCoder:(NSCoder *)coder {
    self = [super init];
    if (self) {
        _object = [coder decodeObjectForKey:@"object"];
        _expirationDate = [coder decodeDoubleForKey:@"expirationDate"];
    }
    return self;
}

- (void)encodeWithCoder:(NSCoder *)coder {
    [coder encodeObject:_object forKey:@"object"];
    [coder encodeDouble:_expirationDate forKey:@"expirationDate"];
}

@end

@interface CacheManager ()
@property (nonatomic, strong) NSCache<NSString*, CachedObject*> *cache;
@property (nonatomic, strong) dispatch_queue_t cacheQueue; // For thread-safe access
@end

@implementation CacheManager

#pragma mark - Singleton

+ (instancetype)sharedInstance {
    static CacheManager *sharedInstance = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        sharedInstance = [[self alloc] init];
    });
    return sharedInstance;
}

#pragma mark - Initialization

- (instancetype)init {
    self = [super init];
    if (self) {
        _cache = [[NSCache alloc] init];
        _cache.name = @"com.launcher.ChatUICache";
        _cache.countLimit = 1000; // Adjust based on expected usage
        _cache.totalCostLimit = 50 * 1024 * 1024; // 50MB limit
        
        _cacheQueue = dispatch_queue_create("com.launcher.ChatUICache.queue", DISPATCH_QUEUE_CONCURRENT);
        
        // Register for memory warning notification to clear cache
        // On macOS, use NSWorkspaceMemoryWarningNotification
        [[NSNotificationCenter defaultCenter] addObserver:self
                                                 selector:@selector(handleMemoryWarning:)
                                                     name:@"NSWorkspaceMemoryWarningNotification"
                                                   object:nil];
    }
    return self;
}

- (void)dealloc {
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

#pragma mark - Cache Operations

- (void)setObject:(id<NSCoding>)object forKey:(NSString *)key cost:(NSUInteger)cost ttl:(NSTimeInterval)ttlSeconds {
    if (!object || !key) return;
    
    DBM(@"Caching object for key: %@, TTL: %.1f sec", key, ttlSeconds);
    
    CachedObject *cachedObj = [[CachedObject alloc] initWithObject:object ttl:ttlSeconds];
    
    dispatch_barrier_async(_cacheQueue, ^{
        [self.cache setObject:cachedObj forKey:key cost:cost];
    });
}

- (nullable id)objectForKey:(NSString *)key {
    if (!key) return nil;
    
    __block id result = nil;
    
    dispatch_sync(_cacheQueue, ^{
        CachedObject *cachedObj = [self.cache objectForKey:key];
        
        if (cachedObj) {
            if ([cachedObj isExpired]) {
                DBM(@"Cache miss (expired) for key: %@", key);
                [self.cache removeObjectForKey:key];
            } else {
                DBM(@"Cache hit for key: %@", key);
                result = cachedObj.object;
            }
        } else {
            DBM(@"Cache miss (not found) for key: %@", key);
        }
    });
    
    return result;
}

- (void)removeObjectForKey:(NSString *)key {
    if (!key) return;
    
    dispatch_barrier_async(_cacheQueue, ^{
        [self.cache removeObjectForKey:key];
    });
}

- (void)removeAllObjects {
    dispatch_barrier_async(_cacheQueue, ^{
        [self.cache removeAllObjects];
    });
}

#pragma mark - Memory Warning Handling

- (void)handleMemoryWarning:(NSNotification *)notification {
    DBM(@"Received memory warning, clearing cache");
    [self removeAllObjects];
}

#pragma mark - Key Generation

- (NSString *)cacheKeyForMentionQuery:(NSString *)query {
    NSString * const kMentionCachePrefix = @"mention_";
    
    if (!query || query.length == 0) {
        return [kMentionCachePrefix stringByAppendingString:@"empty"];
    }
    
    // Normalize the query - lowercase and trim whitespace
    NSString *normalizedQuery = [query.lowercaseString stringByTrimmingCharactersInSet:[NSCharacterSet whitespaceAndNewlineCharacterSet]];
    
    // For mentions, we can use a simple prefix-based key
    return [kMentionCachePrefix stringByAppendingString:normalizedQuery];
}

- (NSString *)cacheKeyForHistoryPrefix:(NSString *)prefix {
    NSString * const kHistoryCachePrefix = @"history_";
    
    if (!prefix || prefix.length == 0) {
        return [kHistoryCachePrefix stringByAppendingString:@"empty"];
    }
    
    // Normalize the prefix - lowercase and trim whitespace
    NSString *normalizedPrefix = [prefix.lowercaseString stringByTrimmingCharactersInSet:[NSCharacterSet whitespaceAndNewlineCharacterSet]];
    
    // For history, we can use a simple prefix-based key
    return [kHistoryCachePrefix stringByAppendingString:normalizedPrefix];
}

- (NSString *)cacheKeyForLLMContext:(NSString *)context {
    // --- CONCRETE LLM CACHE KEY STRATEGY (V2) ---
    // The 'context' NSString passed here has ALREADY been extracted by the caller
    // to represent the relevant text for the suggestion based on cursor position, etc.
    
    // --- Define Cache Type Prefix ---
    NSString * const kLLMCachePrefix = @"llm_"; // Namespace cache keys

    // --- Define Max Length ---
    const NSUInteger kMaxContextLength = 1000; // Max chars to consider for the key
    NSString *relevantContext = context;
    if (context.length > kMaxContextLength) {
        // Trim from the beginning if too long, keeping the end (closer to cursor)
        relevantContext = [context substringFromIndex:(context.length - kMaxContextLength)];
    }

    if (!relevantContext || relevantContext.length == 0) {
        return [kLLMCachePrefix stringByAppendingString:@"ctx_empty"];
    }

    // --- Step 1: Normalize ---
    // Replace sequences of whitespace with single space, trim leading/trailing.
    NSCharacterSet *whitespaceAndNewlines = [NSCharacterSet whitespaceAndNewlineCharacterSet];
    NSString *trimmed = [relevantContext stringByTrimmingCharactersInSet:whitespaceAndNewlines];
    
    // This regex replaces multiple whitespace characters with a single space
    NSError *error = nil;
    NSRegularExpression *regex = [NSRegularExpression regularExpressionWithPattern:@"\\s+" 
                                                                          options:NSRegularExpressionCaseInsensitive
                                                                            error:&error];
    if (error) {
        ERM(@"Regex error: %@", error);
        return [kLLMCachePrefix stringByAppendingString:@"regex_error"];
    }
    
    NSString *normalized = [regex stringByReplacingMatchesInString:trimmed 
                                                           options:0 
                                                             range:NSMakeRange(0, [trimmed length]) 
                                                      withTemplate:@" "];

    if (!normalized || normalized.length == 0) {
        return [kLLMCachePrefix stringByAppendingString:@"ctx_empty_normalized"];
    }

    // --- Step 2: Hash the normalized context ---
    NSData *data = [normalized dataUsingEncoding:NSUTF8StringEncoding];
    unsigned char hash[CC_SHA256_DIGEST_LENGTH];
    CC_SHA256(data.bytes, (CC_LONG)data.length, hash);

    NSMutableString *output = [NSMutableString stringWithCapacity:CC_SHA256_DIGEST_LENGTH * 2];
    for (int i = 0; i < CC_SHA256_DIGEST_LENGTH; i++) {
        [output appendFormat:@"%02x", hash[i]];
    }

    // --- Step 3: Combine Prefix, Strategy Info, and Hash ---
    return [NSString stringWithFormat:@"%@ctx_stratV2_len%lu_normHash_%@", 
                    kLLMCachePrefix, 
                    (unsigned long)normalized.length, 
                    output];
}

@end 