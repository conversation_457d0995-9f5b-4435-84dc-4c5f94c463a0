#pragma once

#import <Cocoa/Cocoa.h>
#import "ComposerViewDelegate.h" // Ensure delegate protocol is imported

@class ComposerView; // Forward declaration

NS_ASSUME_NONNULL_BEGIN

/**
 * Delegate protocol for the ComposerView to communicate events like
 * message submission and mention detection back to its owner (e.g., ChatWindowController).
 */
@protocol ComposerViewDelegate <NSObject>

@required
/**
 * Called when the user submits a message (e.g., by pressing Enter).
 * @param message The non-empty, trimmed message string.
 */
- (void)composerViewDidSubmitMessage:(NSString *)message;

@optional
/**
 * Called when a potential mention trigger ('@') is detected and conditions are met.
 * This signals the start of a mention suggestion sequence.
 * @param composerView The instance sending the message.
 * @param triggerRange The range of the '@' character in the text view's string.
 * @param query The initial query string (text immediately following '@'). May be empty.
 * @param fullQueryRange The combined range of the trigger and the initial query.
 */
- (void)composerView:(ComposerView *)composerView
didDetectMentionTriggerInRange:(NSRange)triggerRange
                 query:(NSString *)query
        fullQueryRange:(NSRange)fullQueryRange;

/**
 * Called when the query text for an active mention tracking sequence changes.
 * @param composerView The instance sending the message.
 * @param query The updated query string.
 * @param range The updated full range of the mention trigger and query.
 */
- (void)composerView:(ComposerView *)composerView
mentionQueryDidChange:(NSString *)query
               range:(NSRange)range;

/**
 * Called when mention tracking is cancelled (e.g., user types a space,
 * deletes the '@', moves cursor away, sends message, etc.).
 * The delegate should use this to hide any mention suggestion UI.
 * @param composerView The instance sending the message.
 */
- (void)composerViewDidCancelMention:(ComposerView *)composerView;

/**
 * Optional: Called when the required height of the composer view changes.
 * @param composerView The instance sending the message.
 * @param newHeight The new intrinsic content height required by the text view.
 */
// - (void)composerViewDidChangeHeight:(CGFloat)newHeight; // Consider adding if needed

// --- NEW: Methods for Keyboard Navigation/Interaction with Mention Popover ---

// Asks the delegate if the mention popover is currently visible.
// Used by ComposerView to decide whether to intercept Up/Down/Enter/Escape.
- (BOOL)mentionPopoverIsVisible;

// Tells the delegate to move the selection in the popover up.
// Returns YES if the action was handled (e.g., selection moved).
- (BOOL)mentionPopoverMoveSelectionUp;

// Tells the delegate to move the selection in the popover down.
// Returns YES if the action was handled (e.g., selection moved).
- (BOOL)mentionPopoverMoveSelectionDown;

// Tells the delegate to confirm the current selection in the popover.
// Returns YES if the action was handled (e.g., a selection was confirmed).
- (BOOL)mentionPopoverConfirmSelection;

/**
 * @brief Called when the user clicks the 'Stop' button during generation.
 * The delegate should trigger the cancellation of the ongoing backend stream.
 */
- (void)cancelCurrentStream;

/**
 * @brief Notifies the delegate that the active model selection changed in the composer picker.
 * @param composerView The composer view instance.
 * @param provider The provider identifier (e.g., "openai").
 * @param model The model identifier (e.g., "gpt-4o").
 */
- (void)composerView:(ComposerView *)composerView
 didChangeModelProvider:(NSString *)provider
                model:(NSString *)model;

@end

/**
 * @class ComposerView
 * @brief A custom NSView containing an NSTextView for multiline text input and a send button.
 *        Handles dynamic height adjustment, placeholder text (requires macOS 10.14+),
 *        Enter/Shift+Enter behavior, mention detection, and optional delegate communication.
 */
@interface ComposerView : NSView <NSTextViewDelegate>

// --- UI Elements ---
@property (nonatomic, strong) NSScrollView *scrollView;
@property (nonatomic, strong) NSTextView *textView;
@property (nonatomic, strong) NSButton *sendButton;
@property (nonatomic, strong) NSButton *stopButton; // Stop button for cancelling streaming

// --- Configuration ---
@property (nonatomic, assign) CGFloat minHeight; // Minimum height of the text view/scroll view
@property (nonatomic, assign) CGFloat maxHeight; // Maximum height before scrolling occurs

// --- Delegate ---
@property (nonatomic, weak) id<ComposerViewDelegate> delegate;

/**
 * Clears the text in the composer view
 */
- (void)clear;

/**
 * Adjusts the height of the composer view based on its content
 */
- (void)adjustHeightToFitContent;

/**
 * Sends the current message in the composer view
 */
- (void)sendMessage;

/**
 * Shows the stop button (for cancelling streaming responses)
 * The stop button will be shown and the send button hidden
 */
- (void)showStopButton;

/**
 * Hides the stop button and shows the send button again
 */
- (void)hideStopButton;

/** Explicitly cancels any ongoing mention tracking. */
- (void)cancelMentionTracking;

@end

NS_ASSUME_NONNULL_END 