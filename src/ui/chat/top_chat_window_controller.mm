#import "top_chat_window_controller.h"
#import "chat_root_view_controller.h"
#import "chat_content_view_controller.h"
#import "sidebar_manager.h"
#include "../../core/util/debug.h"
#include "macos_chat_ui.h"
#import "session_autosave_manager.h"
#import "tabs/tab_group_manager.h"
#import "session_metadata_manager.h"
#include <memory>
#include <vector>
#include <algorithm>
#include "../macos/application_visibility_manager.h"
#import "chat_window_factory.h"
#include "../../core/config/config_manager.h"
#include <QuartzCore/QuartzCore.h>

// Define system identifiers for Big Sur and later if not available
#if !defined(NSToolbarItemIdentifierToggleSidebar)
#define NSToolbarItemIdentifierToggleSidebar @"com.apple.NSToolbarToggleSidebarItemIdentifier"
#endif

#if !defined(NSToolbarItemIdentifierSidebarTrackingSeparator)
#define NSToolbarItemIdentifierSidebarTrackingSeparator @"com.apple.NSToolbarSidebarTrackingSeparatorItemIdentifier"
#endif

// Define our own identifier constants
static const NSToolbarItemIdentifier kSidebarToggleToolbarItemIdentifier = NSToolbarItemIdentifierToggleSidebar;
static const NSToolbarItemIdentifier kSidebarTrackingSeparatorToolbarItemIdentifier = NSToolbarItemIdentifierSidebarTrackingSeparator;

static const NSToolbarItemIdentifier kFlexibleSpaceToolbarItemIdentifier = NSToolbarFlexibleSpaceItemIdentifier;
static const NSToolbarItemIdentifier kSpaceToolbarItemIdentifier = NSToolbarSpaceItemIdentifier;
static const NSToolbarItemIdentifier kBackToolbarItemIdentifier = @"BackItem";
static const NSToolbarItemIdentifier kForwardToolbarItemIdentifier = @"ForwardItem";
static const NSToolbarItemIdentifier kSearchToolbarItemIdentifier = @"SearchItem";
static const NSToolbarItemIdentifier kShareToolbarItemIdentifier = @"ShareItem";
static const NSToolbarItemIdentifier kNewTabToolbarItemIdentifier = @"NewTabItem";

// NEW: Keep strong references to spawned tab UI instances to ensure they live
std::vector<std::shared_ptr<launcher::ui::MacOSChatUI>> g_tab_ui_instances;

static void *kTabGroupKVOContext = &kTabGroupKVOContext;

// Global set tracking tabs waiting to be added to groups (used by ChatWindowFactory as well)
NSMutableSet<NSWindow *> *g_pending_tabs = nil;

// Declare static placeholder for upcoming controller initial group (thread-confined via main thread usage)
static NSString *g_next_initial_group_id = nil;

// After includes and static constants, add notification constant
static NSString* const kModelAccentColorNotification = @"ChatModelAccentColorChanged";

@interface TopChatWindowController ()
@property (nonatomic, strong, readwrite) ChatRootViewController *chatRootVC;
@property (nonatomic, strong) NSToolbar *toolbar;
@property (nonatomic, assign) launcher::ui::MacOSChatUI* uiInstance;
@property (nonatomic, strong) NSWindowTabGroup *observedTabGroup API_AVAILABLE(macos(10.12));
@property (nonatomic, assign) BOOL observingTabGroup;
@property (nonatomic, strong) NSString *lastCreatedGroupId;
@property (nonatomic, strong) NSDate *lastGroupCreationTime;
@property (nonatomic, copy) NSString *initialGroupId; // NEW: desired group for first registration
@end

@implementation TopChatWindowController

+ (void)setNextWindowInitialGroupId:(nullable NSString *)groupId {
    // Store a copy that will be consumed by the next init call on the main thread
    g_next_initial_group_id = [groupId copy];
}

- (instancetype)init {
    self = [super init];
    if (self) {
        // --- NEW: consume pending group id (if any) *before* we create/register the window ---
        _initialGroupId = g_next_initial_group_id;
        g_next_initial_group_id = nil; // Reset for safety

        DBM(@"Initializing TopChatWindowController (pendingGroup=%@)", _initialGroupId ?: @"<nil>");
        _uiInstance = nullptr;
        
        // Initialize the pending tabs set if needed (thread-safe)
        static dispatch_once_t onceToken;
        dispatch_once(&onceToken, ^{
            g_pending_tabs = [NSMutableSet set];
        });
        
        // Create the window
        [self setupWindow];
        
        // Register early with SessionMetadataManager so icon/tint are available before any UI components (sidebar, toolbar, etc.) query for them.
        launcher::ui::SessionMetadataManager::shared().registerWindow(self.window);
        
        // Create and setup the root view controller
        _chatRootVC = [[ChatRootViewController alloc] init];

        // Ensure the shared full-height sidebar is attached before the window appears to avoid
        // any visual flash where the content area spans the whole width.
        [[SidebarManager sharedManager] attachSidebarToSplitViewController:_chatRootVC];
        
        // Ensure Cocoa does not shrink the window to the child controller's default fitting size.
        // By telling the root controller what size we really want, the initial 70-%-of-screen
        // width is preserved and no flicker occurs.
        // _chatRootVC.preferredContentSize = self.window.frame.size;
        self.window.contentViewController = _chatRootVC;
        
        // Setup toolbar and sidebar toggle
        [self setupToolbar];
        [self setupSidebarToggleButton];

        // Listen for selection changes so every window can respond appropriately.
        [[NSNotificationCenter defaultCenter] addObserver:self
                                                 selector:@selector(handleAccentColorChange:)
                                                     name:kModelAccentColorNotification
                                                   object:nil];
        [self registerForTabSelectionNotifications];

        // Begin observing tab group changes to detect tab tear-outs / merges.
        [self startObservingTabGroup];

        // Observe the window's tabGroup property itself so we can switch observation when the window joins a new group.
        if (@available(macOS 10.12, *)) {
            [self.window addObserver:self
                           forKeyPath:@"tabGroup"
                              options:NSKeyValueObservingOptionNew
                              context:&kTabGroupKVOContext];
        }
    }
    return self;
}

- (void)setupWindow {
    // Create window - match Safari-like appearance
    NSRect screenRect = [[NSScreen mainScreen] frame];
    NSRect windowRect = NSMakeRect(screenRect.size.width / 4, 
                                 screenRect.size.height / 4, 
                                 screenRect.size.width / 2, 
                                 screenRect.size.height / 2);
    
    self.window = [[NSWindow alloc] initWithContentRect:windowRect
                                             styleMask:NSWindowStyleMaskTitled | 
                                                     NSWindowStyleMaskClosable |
                                                     NSWindowStyleMaskMiniaturizable |
                                                     NSWindowStyleMaskResizable |
                                                     NSWindowStyleMaskFullSizeContentView // For unified toolbar
                                               backing:NSBackingStoreBuffered
                                                 defer:NO];
    self.window.title = @"New Chat";
    self.window.releasedWhenClosed = NO;
    self.window.delegate = self;
    
    // Add titlebar appearance for modern look
    self.window.titleVisibility = NSWindowTitleHidden;
    self.window.titlebarAppearsTransparent = NO; // Make titlebar non-transparent
    
    // Apply vibrancy effect to make it look modern like Safari
    NSVisualEffectView *vibrantView = [[NSVisualEffectView alloc] initWithFrame:windowRect];
    vibrantView.material = NSVisualEffectMaterialWindowBackground;
    vibrantView.blendingMode = NSVisualEffectBlendingModeBehindWindow;
    vibrantView.state = NSVisualEffectStateActive;
    vibrantView.autoresizingMask = NSViewWidthSizable | NSViewHeightSizable;
    
    // Set window appearance to match system
    if (@available(macOS 10.14, *)) {
        // Use appropriate appearance API based on macOS version
        if (@available(macOS 12.0, *)) {
            [self.window setAppearance:[NSAppearance currentDrawingAppearance]];
        } else {
            #pragma clang diagnostic push
            #pragma clang diagnostic ignored "-Wdeprecated-declarations"
            [self.window setAppearance:[NSAppearance currentAppearance]];
            #pragma clang diagnostic pop
        }
    }
    
    // Enable native tabbing support (macOS 10.12+)
    if (@available(macOS 10.12, *)) {
        self.window.tabbingIdentifier = @"ChatWindow";
        self.window.tabbingMode = NSWindowTabbingModePreferred;
        
        // Force the tab bar to always be visible if needed
        if (@available(macOS 10.13, *)) {
            // Call the system menu item that enables tabs
            [NSApp sendAction:@selector(toggleTabBar:) to:nil from:self.window];
        }
    }

    // Register this window with the TabGroupManager – use caller-supplied group if available
    [[TabGroupManager sharedManager] addWindow:self.window toGroup:self.initialGroupId];
    [[TabGroupManager sharedManager] setSelectedWindow:self.window];
}

- (void)setupToolbar {
    // Create a new toolbar
    self.toolbar = [[NSToolbar alloc] initWithIdentifier:@"ChatToolbar"];
    self.toolbar.delegate = self;
    self.toolbar.allowsUserCustomization = NO;
    self.toolbar.displayMode = NSToolbarDisplayModeIconOnly;
    
    // Handle deprecated showsBaselineSeparator property
    #pragma clang diagnostic push
    #pragma clang diagnostic ignored "-Wdeprecated-declarations"
    if (@available(macOS 15.0, *)) {
        // Property is removed in macOS 15+, so we don't need to set it
    } else {
        self.toolbar.showsBaselineSeparator = NO; // Safari-like style without separator
    }
    #pragma clang diagnostic pop

    // Set the toolbar for the window
    self.window.toolbar = self.toolbar;
    
    // Set a non-transparent toolbar style
    if (@available(macOS 11.0, *)) {
        self.window.toolbarStyle = NSWindowToolbarStyleUnifiedCompact; // Use compact style for non-transparent appearance
    }
}

- (void)setupSidebarToggleButton {
    // Add a sidebar toggle button in the titlebar (similar to Safari)
    NSTitlebarAccessoryViewController *accessoryViewController = [[NSTitlebarAccessoryViewController alloc] init];
    
    // Create the sidebar toggle button
    NSButton *sidebarToggleButton = [NSButton buttonWithImage:[NSImage imageWithSystemSymbolName:@"sidebar.leading" accessibilityDescription:@"Toggle Sidebar"]
                                                      target:self
                                                      action:@selector(toggleSidebar:)];
    sidebarToggleButton.bezelStyle = NSBezelStyleRounded;
    sidebarToggleButton.bordered = NO;
    sidebarToggleButton.toolTip = @"Show or hide the sidebar";
    
    // Size the button appropriately
    CGFloat buttonSize = 25.0;
    sidebarToggleButton.frame = NSMakeRect(0, 0, buttonSize, buttonSize);
    
    // Setup the accessory view controller
    accessoryViewController.view = sidebarToggleButton;
    accessoryViewController.layoutAttribute = NSLayoutAttributeLeading; // Position at leading edge of window
    
    // Add the accessory view to the window's titlebar
    if (self.window != nil) {
        [self.window addTitlebarAccessoryViewController:accessoryViewController];
    }
}

#pragma mark - Property accessors

- (ChatContentViewController *)contentVC {
    return self.chatRootVC.contentVC;
}

#pragma mark - UI Instance Setter

- (void)setUiInstance:(launcher::ui::MacOSChatUI*)uiInstance {
    _uiInstance = uiInstance;
    
    // Forward the UI instance to the content view controller
    [self.contentVC setUiInstance:uiInstance];
}

#pragma mark - UI Actions

- (void)focusOnChatInput {
    [self.contentVC focusOnChatInput];
}

- (void)addTestMessages {
    [self.contentVC addTestMessages];
}

- (void)toggleSidebar:(nullable id)sender {
    DBM(@"Toggle Sidebar button clicked");
    [self.chatRootVC toggleSidebar:sender];
}

#pragma mark - NSToolbarDelegate Methods

- (NSArray<NSToolbarItemIdentifier> *)toolbarDefaultItemIdentifiers:(NSToolbar *)toolbar {
    return @[ 
              kSidebarTrackingSeparatorToolbarItemIdentifier,
              kBackToolbarItemIdentifier, 
              kForwardToolbarItemIdentifier,
              kFlexibleSpaceToolbarItemIdentifier,
              kSearchToolbarItemIdentifier,
              kFlexibleSpaceToolbarItemIdentifier,
              kNewTabToolbarItemIdentifier,
              kShareToolbarItemIdentifier
            ];
}

- (NSArray<NSToolbarItemIdentifier> *)toolbarAllowedItemIdentifiers:(NSToolbar *)toolbar {
    // Return identifiers of all items that can potentially be shown
    return @[ 
              kSidebarTrackingSeparatorToolbarItemIdentifier,
              kFlexibleSpaceToolbarItemIdentifier,
              kSpaceToolbarItemIdentifier,
              kBackToolbarItemIdentifier,
              kForwardToolbarItemIdentifier,
              kSearchToolbarItemIdentifier,
              kNewTabToolbarItemIdentifier,
              kShareToolbarItemIdentifier
            ];
}

- (nullable NSToolbarItem *)toolbar:(NSToolbar *)toolbar
              itemForItemIdentifier:(NSToolbarItemIdentifier)itemIdentifier
          willBeInsertedIntoToolbar:(BOOL)flag {

    NSToolbarItem *toolbarItem = nil;

    if ([itemIdentifier isEqualToString:kSidebarToggleToolbarItemIdentifier]) {
        // Create sidebar toggle item
        toolbarItem = [[NSToolbarItem alloc] initWithItemIdentifier:itemIdentifier];
        toolbarItem.label = @"Toggle Sidebar";
        toolbarItem.paletteLabel = @"Toggle Sidebar";
        toolbarItem.toolTip = @"Show/Hide Sidebar";
        toolbarItem.target = self;
        toolbarItem.action = @selector(toggleSidebar:);
        
        // Use a standard sidebar icon
        NSImage* sidebarImage = [NSImage imageWithSystemSymbolName:@"sidebar.leading" accessibilityDescription:@"Toggle Sidebar"];
        toolbarItem.image = sidebarImage;
    }
    else if ([itemIdentifier isEqualToString:kSidebarTrackingSeparatorToolbarItemIdentifier]) {
        // Create regular separator for now
        toolbarItem = [[NSToolbarItem alloc] initWithItemIdentifier:itemIdentifier];
        toolbarItem.label = @"Separator";
        toolbarItem.paletteLabel = @"Separator";
    }
    else if ([itemIdentifier isEqualToString:kBackToolbarItemIdentifier]) {
        toolbarItem = [[NSToolbarItem alloc] initWithItemIdentifier:itemIdentifier];
        toolbarItem.label = @"Back";
        toolbarItem.paletteLabel = @"Back";
        toolbarItem.toolTip = @"Go Back";
        toolbarItem.target = self;
        toolbarItem.action = @selector(goBack:);

        NSImage* backImage = [NSImage imageWithSystemSymbolName:@"chevron.left" accessibilityDescription:@"Go Back"];
        toolbarItem.image = backImage;

        NSButton *button = [NSButton buttonWithImage:backImage target:self action:@selector(goBack:)];
        button.bezelStyle = NSBezelStyleTexturedRounded;
        toolbarItem.view = button;
    }
    else if ([itemIdentifier isEqualToString:kForwardToolbarItemIdentifier]) {
        toolbarItem = [[NSToolbarItem alloc] initWithItemIdentifier:itemIdentifier];
        toolbarItem.label = @"Forward";
        toolbarItem.paletteLabel = @"Forward";
        toolbarItem.toolTip = @"Go Forward";
        toolbarItem.target = self;
        toolbarItem.action = @selector(goForward:);

        NSImage* forwardImage = [NSImage imageWithSystemSymbolName:@"chevron.right" accessibilityDescription:@"Go Forward"];
        toolbarItem.image = forwardImage;

        NSButton *button = [NSButton buttonWithImage:forwardImage target:self action:@selector(goForward:)];
        button.bezelStyle = NSBezelStyleTexturedRounded;
        toolbarItem.view = button;
    }
    else if ([itemIdentifier isEqualToString:kSearchToolbarItemIdentifier]) {
        toolbarItem = [[NSToolbarItem alloc] initWithItemIdentifier:itemIdentifier];
        toolbarItem.label = @"Search";
        toolbarItem.paletteLabel = @"Search";
        
        // Create the search field with a Safari-like style
        NSSearchField *searchField = [[NSSearchField alloc] initWithFrame:NSMakeRect(0, 0, 400, 28)];
        searchField.placeholderString = @"Search conversations";
        [searchField setContentHuggingPriority:NSLayoutPriorityDefaultLow forOrientation:NSLayoutConstraintOrientationHorizontal];
        
        // Customize the search field to look more like Safari
        searchField.wantsLayer = YES;
        searchField.layer.cornerRadius = 10.0;
        
        // Set search field as the item's view
        toolbarItem.view = searchField;
    }
    else if ([itemIdentifier isEqualToString:kNewTabToolbarItemIdentifier]) {
        toolbarItem = [[NSToolbarItem alloc] initWithItemIdentifier:itemIdentifier];
        toolbarItem.label = @"New Tab";
        toolbarItem.paletteLabel = @"New Tab";
        toolbarItem.toolTip = @"Create New Tab";
        toolbarItem.target = self;
        toolbarItem.action = @selector(newWindowForTab:);

        NSImage* newTabImage = [NSImage imageWithSystemSymbolName:@"plus" accessibilityDescription:@"New Tab"];
        toolbarItem.image = newTabImage;

        NSButton *button = [NSButton buttonWithImage:newTabImage target:self action:@selector(newWindowForTab:)];
        button.bezelStyle = NSBezelStyleTexturedRounded;
        toolbarItem.view = button;
    }
    else if ([itemIdentifier isEqualToString:kShareToolbarItemIdentifier]) {
        toolbarItem = [[NSToolbarItem alloc] initWithItemIdentifier:itemIdentifier];
        toolbarItem.label = @"Share";
        toolbarItem.paletteLabel = @"Share";
        toolbarItem.toolTip = @"Share This Conversation";
        toolbarItem.target = self;
        toolbarItem.action = @selector(sharePage:);

        NSImage* shareImage = [NSImage imageWithSystemSymbolName:@"square.and.arrow.up" accessibilityDescription:@"Share"];
        toolbarItem.image = shareImage;

        NSButton *button = [NSButton buttonWithImage:shareImage target:self action:@selector(sharePage:)];
        button.bezelStyle = NSBezelStyleTexturedRounded;
        toolbarItem.view = button;
    }
    
    return toolbarItem;
}

#pragma mark - Toolbar Actions

- (IBAction)goBack:(nullable id)sender {
    DBM(@"Back button clicked");
    // Implementation will go here
}

- (IBAction)goForward:(nullable id)sender {
    DBM(@"Forward button clicked");
    // Implementation will go here
}

- (IBAction)sharePage:(nullable id)sender {
    DBM(@"Share button clicked");
    // Implementation will go here
}

- (IBAction)newWindowForTab:(nullable id)sender {
    DBM(@"Creating new tab via ChatWindowFactory");

    launcher::ui::ChatWindowFactory::createNewTab(self.window);
}

- (IBAction)newWindow:(nullable id)sender {
    DBM(@"New Window hot-key invoked");

    launcher::ui::ChatWindowFactory::createNewWindow();
}

- (IBAction)newTab:(nullable id)sender {
    DBM(@"New Tab hot-key invoked");
    [self newWindowForTab:sender];
}

- (IBAction)closeTabOrWindow:(nullable id)sender {
    DBM(@"Close Tab/Window hot-key invoked");
    [self.window performClose:sender];
}

- (IBAction)reopenLastClosedTab:(nullable id)sender {
    if (launcher::ui::SessionAutosaveManager::shared().reopenLastClosedWindow(self.window)) {
        DBM(@"Reopened last closed chat window");
    } else {
        NSBeep();
    }
}

- (IBAction)selectNextTab:(nullable id)sender {
    if (@available(macOS 10.12, *)) {
        [self.window selectNextTab:sender];
    } else {
        NSBeep();
    }
}

- (IBAction)selectPreviousTab:(nullable id)sender {
    if (@available(macOS 10.12, *)) {
        [self.window selectPreviousTab:sender];
    } else {
        NSBeep();
    }
}

- (IBAction)showAllTabs:(nullable id)sender {
    if (@available(macOS 10.13, *)) {
        [self.window toggleTabOverview:sender];
    } else {
        NSBeep();
    }
}

- (IBAction)closeAllWindows:(nullable id)sender {
    DBM(@"Close All Windows hot-key invoked");
    // Iterate over all app windows and close them.
    // Make a snapshot first to avoid mutation during enumeration.
    NSArray<NSWindow *> *windowsSnapshot = [[NSApp windows] copy];
    for (NSWindow *win in windowsSnapshot) {
        // Only attempt to close windows that are chat windows (identified by our tabbingIdentifier)
        if (@available(macOS 10.12, *)) {
            if (![win.tabbingIdentifier isEqualToString:@"ChatWindow"]) { continue; }
        }
        [win performClose:sender];
    }
}

#pragma mark - NSWindowDelegate Methods

- (void)windowWillClose:(NSNotification *)notification {
    DBM(@"Window will close");

    // Snapshot must be recorded first so reopen-closed works. Then update the
    // TabGroupManager **while the UI instance is still alive** so it can
    // extract the conversationId and create a ClosedTabInfo entry for the
    // sidebar.
    launcher::ui::SessionAutosaveManager::shared().recordClosedWindow(self.window);

    // Inform TabGroupManager **before** nulling the C++ UI pointer so it can
    // query metadata from uiInstance.
    [[TabGroupManager sharedManager] removeWindow:self.window];

    // Now it is safe to clear the C++ UI pointer.
    _uiInstance = nullptr;

    // Stop tab-group observation **before** removing from managers
    [self stopObservingTabGroup];
    if (@available(macOS 10.12, *)) {
        @try {
            [self.window removeObserver:self forKeyPath:@"tabGroup" context:&kTabGroupKVOContext];
        } @catch (__unused NSException *ex) {}
    }

    // Remove any stored tab UI that matches the closing window to release resources
    NSWindow *closingWindow = (NSWindow *)notification.object;
    g_tab_ui_instances.erase(std::remove_if(g_tab_ui_instances.begin(), g_tab_ui_instances.end(),
                                            [&](const std::shared_ptr<launcher::ui::MacOSChatUI> &ui) {
                                                return ui && ui->getNativeWindow() == closingWindow;
                                            }),
                              g_tab_ui_instances.end());

    // Notify ApplicationVisibilityManager that a window is closing
    launcher::ui::ApplicationVisibilityManager::shared().closeChatWindow();
}

- (void)windowDidResize:(NSNotification *)notification {
    // Layout is handled by Auto Layout
}

- (void)windowDidBecomeKey:(NSNotification *)notification {
    // Always shift focus to the chat input after the window is key.
    dispatch_async(dispatch_get_main_queue(), ^{
        [self focusOnChatInput];
    });

    // Inform tab manager of selection change
    [[TabGroupManager sharedManager] setSelectedWindow:self.window];
}

- (void)dealloc {
    DBM(@"Deallocating TopChatWindowController");
    [[NSNotificationCenter defaultCenter] removeObserver:self name:kTabGroupManagerSelectionChangedNotification object:nil];

    // Ensure KVO observers are removed if the window somehow bypassed windowWillClose.
    [self stopObservingTabGroup];
    if (@available(macOS 10.12, *)) {
        @try {
            [self.window removeObserver:self forKeyPath:@"tabGroup" context:&kTabGroupKVOContext];
        } @catch (__unused NSException *ex) {}
    }

    _uiInstance = nullptr;

    // Remove accent color observer
    [[NSNotificationCenter defaultCenter] removeObserver:self name:kModelAccentColorNotification object:nil];
}

- (void)registerForTabSelectionNotifications {
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(tabManagerSelectionChanged:)
                                                 name:kTabGroupManagerSelectionChangedNotification
                                               object:nil];
}

- (void)tabManagerSelectionChanged:(NSNotification *)note {
    NSWindow *targetWindow = note.userInfo[@"window"];
    if (!targetWindow || (id)targetWindow == [NSNull null]) { return; }

    // If this controller owns the target window, simply bring it front.
    if (targetWindow == self.window) {
        [self.window makeKeyAndOrderFront:nil];
        return;
    }

    // If both windows are in the same tab group, change the selected tab.
    if (@available(macOS 10.12, *)) {
        if (self.window.tabGroup && [self.window.tabGroup.windows containsObject:targetWindow]) {
            self.window.tabGroup.selectedWindow = targetWindow;
        }
    }
}

#pragma mark - Tab Group Observation Helpers

- (void)startObservingTabGroup {
    if (@available(macOS 10.12, *)) {
        // Avoid double-registration
        if (self.observingTabGroup) { return; }
        NSWindowTabGroup *group = self.window.tabGroup;
        if (!group) { return; }

        self.observedTabGroup = group;
        @try {
            [group addObserver:self
                      forKeyPath:@"windows"
                         options:NSKeyValueObservingOptionOld | NSKeyValueObservingOptionNew
                         context:&kTabGroupKVOContext];
            // NEW: Observe selectedWindow so we know when this tab becomes active within the group
            [group addObserver:self
                      forKeyPath:@"selectedWindow"
                         options:0
                         context:&kTabGroupKVOContext];
            self.observingTabGroup = YES;
        } @catch (NSException *ex) {
            ERM(@"Failed to add KVO observer for tab group: %@", ex);
        }
    }
}

- (void)stopObservingTabGroup {
    if (@available(macOS 10.12, *)) {
        if (!self.observingTabGroup || !self.observedTabGroup) { return; }
        @try {
            [self.observedTabGroup removeObserver:self forKeyPath:@"windows" context:&kTabGroupKVOContext];
            // NEW: remove selectedWindow observer as well
            [self.observedTabGroup removeObserver:self forKeyPath:@"selectedWindow" context:&kTabGroupKVOContext];
        } @catch (__unused NSException *ex) {
            // Ignore attempts to remove if not registered
        }
        self.observedTabGroup = nil;
        self.observingTabGroup = NO;
    }
}

// KVO callback
- (void)observeValueForKeyPath:(NSString *)keyPath
                      ofObject:(id)object
                        change:(NSDictionary<NSKeyValueChangeKey,id> *)change
                       context:(void *)context {
    if (context != &kTabGroupKVOContext) {
        [super observeValueForKeyPath:keyPath ofObject:object change:change context:context];
        return;
    }

    if (@available(macOS 10.12, *)) {
        if ([keyPath isEqualToString:@"windows"]) {
            NSKeyValueChange kind = (NSKeyValueChange)[change[NSKeyValueChangeKindKey] unsignedIntegerValue];
            NSArray<NSWindow *> *affected = nil;

            if (kind == NSKeyValueChangeInsertion) {
                affected = change[NSKeyValueChangeNewKey];
                for (NSWindow *w in affected) {
                    if (!w) { continue; }
                    // Skip if already registered
                    if ([[TabGroupManager sharedManager] groupIdForWindow:w]) { continue; }

                    // Ensure the window truly belongs to the same tab group (prevents false insertion
                    // when a tab was just detached and now lives in a different NSWindowTabGroup).
                    if (@available(macOS 10.12, *)) {
                        if (w.tabGroup != self.window.tabGroup) { continue; }
                    }

                    NSString *gid = [[TabGroupManager sharedManager] groupIdForWindow:self.window];

                    // If this host window is not yet part of tab groups, create a new one and add host first.
                    if (!gid) {
                        gid = [[TabGroupManager sharedManager] createGroupWithTitle:nil
                                                                              tint:[NSColor systemTealColor]];
                        [[TabGroupManager sharedManager] addWindow:self.window toGroup:gid];
                    }

                    [[TabGroupManager sharedManager] addWindow:w toGroup:gid];
                }
            } else if (kind == NSKeyValueChangeRemoval) {
                affected = change[NSKeyValueChangeOldKey];
                for (NSWindow *w in affected) {
                    if (!w) { continue; }
                    // Only attempt removal if it was tracked
                    if ([[TabGroupManager sharedManager] groupIdForWindow:w]) {
                        [[TabGroupManager sharedManager] detachWindow:w];
                    }
                }
            }
        } else if ([keyPath isEqualToString:@"selectedWindow"]) {
            // NEW: When this window becomes the selected tab within the group, focus the input field.
            if (@available(macOS 10.12, *)) {
                if (object == self.observedTabGroup && self.observedTabGroup.selectedWindow == self.window) {
                    dispatch_async(dispatch_get_main_queue(), ^{
                        [self focusOnChatInput];
                    });
                }
            }
        } else if ([keyPath isEqualToString:@"tabGroup"]) {
            // Check if this window is pending tab addition - if so, don't interfere with group membership
            BOOL isPendingTab = NO;
            @synchronized(g_pending_tabs) {
                isPendingTab = [g_pending_tabs containsObject:self.window];
            }
            
            if (isPendingTab) {
                DBM(@"Skipping tabGroup change processing for window in pending tab state");
                // Still need to update our observers
                [self stopObservingTabGroup];
                [self startObservingTabGroup];
                return;
            }
            
            // Check if we're within the cooldown period for group creation
            // This prevents multiple group creations during tear-out operations
            BOOL withinCooldown = NO;
            if (self.lastCreatedGroupId && self.lastGroupCreationTime) {
                // Use a 500ms cooldown window for tab group operations
                NSTimeInterval elapsed = [[NSDate date] timeIntervalSinceDate:self.lastGroupCreationTime];
                if (elapsed < 0.5) {
                    // If we're in cooldown, only continue if this window doesn't have that group anymore
                    NSString *currentGroupId = [[TabGroupManager sharedManager] groupIdForWindow:self.window];
                    if ([currentGroupId isEqualToString:self.lastCreatedGroupId]) {
                        withinCooldown = YES;
                    }
                }
            }
            
            if (withinCooldown) {
                DBM(@"Ignoring redundant tabGroup change during cooldown period");
                // Still need to update our observers
                [self stopObservingTabGroup];
                [self startObservingTabGroup];
                return;
            }
            
            // Regular tab group change processing for non-pending windows
            [self stopObservingTabGroup];

            // Eagerly purge any stale registration of this window in the previous
            // TabGroupManager group *before* we begin working with the new group.
            // This prevents a race where the old \"windows\" KVO removal event is
            // missed (due to the stopObserving call above) and the window would be
            // erroneously re-added to the former group when the insertion event for
            // the new tab group arrives.
            NSString *prevGroupId = [[TabGroupManager sharedManager] groupIdForWindow:self.window];
            if (prevGroupId) {
                DBM(@"Removing stale window→group mapping during tabGroup switch (old group: %@)", prevGroupId);
                [[TabGroupManager sharedManager] detachWindow:self.window];
            }

            [self startObservingTabGroup];

            // EARLY-EXIT: If the window is no longer part of any NSWindowTabGroup (i.e. tabGroup == nil)
            // we are most likely in the middle of a close operation or between detach/attach events.
            // At this point we have already removed any stale mapping from the previous group, so the
            // correct behaviour is to *not* create or adopt a new logical group.  Doing so would leave
            // an orphaned group behind when the window ultimately closes.  Real detach operations will
            // trigger a subsequent KVO event with a non-nil tabGroup, which will follow the normal path
            // below.
            if (self.window.tabGroup == nil) {
                return;
            }

            // Ensure the TabGroupManager reflects the new group membership.
            // Step 1: check if *this* window is already tracked (unlikely after purge).
            if (![[TabGroupManager sharedManager] groupIdForWindow:self.window]) {
                // Step 2: attempt to adopt an existing group from any peer window
                // within the same NSWindowTabGroup.  This avoids creating transient
                // groups when a tab is merely added to an existing window.
                NSString *peerGid = nil;
                for (NSWindow *peer in self.window.tabGroup.windows) {
                    if (peer == self.window) { continue; }
                    peerGid = [[TabGroupManager sharedManager] groupIdForWindow:peer];
                    if (peerGid) { break; }
                }

                if (peerGid) {
                    DBM(@"Adopting existing TabGroupManager group %@ from peer window", peerGid);
                    [[TabGroupManager sharedManager] addWindow:self.window toGroup:peerGid];
                } else {
                    // No peers tracked – create a brand-new logical group.
                    NSString *destGroupId = [[TabGroupManager sharedManager] createGroupWithTitle:nil
                                                                                              tint:[NSColor systemTealColor]];
                    [[TabGroupManager sharedManager] addWindow:self.window toGroup:destGroupId];
                    
                    // Record this group creation to avoid redundant groups during tear-out
                    self.lastCreatedGroupId = destGroupId;
                    self.lastGroupCreationTime = [NSDate date];
                    DBM(@"Started group creation cooldown for group %@", destGroupId);
                }
            }
        }
    }
}

#pragma mark - Accent Color Handling

- (void)handleAccentColorChange:(NSNotification *)notification {
    NSColor *color = notification.userInfo[@"color"];
    if (!color) { return; }

    dispatch_async(dispatch_get_main_queue(), ^{
        [self applyAccentColorToWindow:color];
    });
}

- (void)applyAccentColorToWindow:(NSColor *)color {
    if (!self.window) { return; }

    // Ensure layer-backed content view
    NSView *contentView = self.window.contentView;
    contentView.wantsLayer = YES;

    // Apply border
    contentView.layer.borderWidth = 2.0;
    contentView.layer.borderColor = color.CGColor;

    // Optional glow via shadow (subtle)
    contentView.layer.shadowColor = color.CGColor;
    contentView.layer.shadowRadius = 6.0;
    contentView.layer.shadowOpacity = 0.3;
    contentView.layer.shadowOffset = CGSizeZero;
}

@end 