#import "sidebar_symbol_pool.h"
#import <AppKit/AppKit.h>

NSArray<NSString *> *SidebarSymbolPool() {
    static NSArray<NSString *> *symbols = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        // Curated list of symbol names that look crisp at small sizes and are
        // available on macOS 13+.  This list can be extended over time.
        symbols = @[ 
            @"bubble.left",
            @"scribble",
            @"sparkles",
            @"lightbulb",
            @"bolt",
            @"globe",
            @"brain.head.profile",
            @"terminal",
            @"paperplane",
            @"quote.bubble",
            @"pencil",
            @"bookmark",
            @"briefcase",
            @"calendar",
            @"camera",
            @"cloud",
            @"folder",
            @"gamecontroller",
            @"gauge",
            @"gift",
            @"graduationcap",
            @"hammer",
            @"headphones",
            @"heart",
            @"hourglass",
            @"key",
            @"leaf",
            @"link",
            @"location",
            @"lock",
            @"mic",
            @"moon",
            @"music.note",
            @"paintbrush",
            @"paperclip",
            @"pawprint",
            @"puzzlepiece",
            @"rosette",
            @"scissors",
            @"shield",
            @"star",
            @"suit.heart",
            @"sun.max",
            @"tag",
            @"timer",
            @"trophy",
            @"video",
            @"wand.and.stars",
            @"wifi",
            @"wrench",
            // --- Emoji-like symbols ---
            @"face.smiling",
            @"face.dashed",
            @"faceid",
            @"eyes",
            @"tortoise",
            @"hare",
            @"ant",
            @"ladybug",
            @"leaf",
            @"flame",
            @"drop",
            @"snow",
            @"wind",
            @"sparkle.magnifyingglass",
            @"circle.grid.cross",
            // TODO(assistant): expand further with script-generated list.
        ];
    });
    return symbols;
} 