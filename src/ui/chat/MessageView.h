#ifndef MINICHAT_MESSAGE_VIEW_H
#define MINICHAT_MESSAGE_VIEW_H

#import <Cocoa/Cocoa.h>
#import "../../core/model/Message.h"

/**
 * @class MessageView
 * @brief Custom view for displaying a chat message with optimized rendering
 *
 * This view handles efficient rendering of messages with markdown content,
 * including text formatting, code blocks, and embedded images.
 */
@interface MessageView : NSView<NSTextViewDelegate>

// Properties
@property(nonatomic, strong) Message *message;
@property(nonatomic, strong) NSTextView *textView;
@property(nonatomic, strong) NSView *containerView;
@property(nonatomic, strong) NSImageView *avatarView;
@property(nonatomic, strong) NSTextField *nameLabel;
@property(nonatomic, strong) NSTextField *timestampLabel;
@property(nonatomic, assign) BOOL isSelected;
@property(nonatomic, assign) BOOL isHighlighted;
@property(nonatomic, assign) CGFloat preferredWidth;

// Initialization
- (instancetype)initWithMessage:(Message *)message frame:(NSRect)frame;

// Methods
- (void)setupViews;
- (void)updateContent;
- (void)updateLayout;
- (CGFloat)heightForWidth:(CGFloat)width;
- (void)setHighlighted:(BOOL)highlighted;
- (void)setSelected:(BOOL)selected;

@end

#endif  // MINICHAT_MESSAGE_VIEW_H
