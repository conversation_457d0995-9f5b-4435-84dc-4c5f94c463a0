#import <Cocoa/Cocoa.h>

NS_ASSUME_NONNULL_BEGIN

// Custom cell view to display an icon + text in the sidebar.
@interface SidebarCellView : NSTableCellView
+ (NSImage *)closedTabBadgeImage; // Returns lazily initialised badge symbol image
- (void)setClosedAppearance:(BOOL)isClosed; // Toggle dimming & badge
@property (nonatomic, readonly) NSButton *closeButton; // Trailing close button shown on hover for closed tabs
@end

NS_ASSUME_NONNULL_END 