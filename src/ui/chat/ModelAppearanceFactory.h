#ifndef MODEL_APPEARANCE_FACTORY_H_
#define MODEL_APPEARANCE_FACTORY_H_

#import <Cocoa/Cocoa.h>

// ModelAppearanceFactory
// -----------------------
// Deterministically generates a unique tint colour and SF Symbol name for a given
// model (sender) name.  The mapping is guaranteed to be stable across sessions
// by deriving both artefacts from the SHA-256 hash of the input string.
//
// Usage:
//   NSColor *accent = [ModelAppearanceFactory colorForModel:@"gpt-4"];
//   NSString *symbol = [ModelAppearanceFactory symbolForModel:@"gpt-4"];
//
// The helper is thread-safe and caches computed results for optimal performance.
@interface ModelAppearanceFactory : NSObject

+ (NSColor *)colorForModel:(NSString *)modelName;
+ (NSString *)symbolForModel:(NSString *)modelName;

// Exposed for convenience by MessageView typing indicator, not intended for
// general public use.
+ (NSColor *)color:(NSColor *)color byAdjustingBrightness:(CGFloat)delta;

// Capability-aware icon helper.  Chooses an SF-Symbol that best represents the
// model based on its primary capability (vision, audio, image-gen, etc.).
// Falls back to the hash-based variant when no distinctive capability is found.
+ (NSString *)symbolForProvider:(NSString *)provider model:(NSString *)modelName;

@end

#endif /* MODEL_APPEARANCE_FACTORY_H_ */ 