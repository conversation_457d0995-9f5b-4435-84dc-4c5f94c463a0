#include "chat_ui_interface.h"

#ifdef __APPLE__
#include "macos_chat_ui.h"
#elif defined(_WIN32)
// Include Windows implementation when available
// #include "windows_chat_ui.h"
#endif

namespace launcher {
namespace ui {

std::shared_ptr<ChatUIInterface> createChatUI() {
#ifdef __APPLE__
    return std::make_shared<MacOSChatUI>();
#elif defined(_WIN32)
    // Return Windows implementation when available
    // return std::make_shared<WindowsChatUI>();
    return nullptr;
#else
    // Unsupported platform
    return nullptr;
#endif
}

}  // namespace ui
}  // namespace launcher