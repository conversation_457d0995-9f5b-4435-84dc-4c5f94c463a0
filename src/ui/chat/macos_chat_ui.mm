// --- Ensure ObjC Frameworks are imported FIRST ---
#import <Cocoa/Cocoa.h>      // Includes Foundation, AppKit, etc.
// #import <Foundation/Foundation.h> // Included by Cocoa
// #import <AppKit/AppKit.h>       // Included by Cocoa
// --- End Framework Imports ---

#include "macos_chat_ui.h" // Now include our header AFTER ObjC types are known

#include <iostream>
#include <memory>
#include <string>
#include <sstream> // For std::stringstream
#include <variant> // For std::variant
#include <vector>  // For std::vector

// Include core headers FIRST to ensure namespace is defined
#include "../../core/config/config_manager.h"
#include "../../core/util/debug.h"
#include "../../core/include/chat_backend_interface.h" // For BackendErrorCode, MentionResult etc.
#include "../../core/chat/unified_chat_backend.h" // Unified backend
#include "../../core/chat/conversation_manager.h" // Corrected path
#include "../../utilities/cancellation_token.h" // Include CancellationToken
#include "../../core/persistence/conversation_store.h"

// Include project Objective-C headers
#import "top_chat_window_controller.h" // *** USE THE NEW CONTROLLER ***
#import "chat_content_view_controller.h" // *** ADDED: Include the full class definition ***
#import "ComposerView.h" // *** ADDED: Include full definition for ComposerView methods ***
#import "model/BackendError.h"      // For BackendError class (still needed for conversion)
#import "model/MentionSuggestion.h" // For MentionSuggestion class (still needed for conversion)
#import "mention/MentionPopoverController.h" // Still needed? Maybe not directly. Keep for now.
#import "cache/CacheManager.h"
#import "ChatViewController.h" // Still needed by MacOSChatUI
#import "core/model/Message.h" // Still needed by MacOSChatUI
#import "session_metadata_manager.h"

// Add this include near the top, after the other includes
#include "../macos/application_visibility_manager.h" // For ApplicationVisibilityManager

#include "../../core/interfaces/iconfig_manager.h"  // @add_include
#include "../macos/injected_services.h"  // shared helpers

// At top of file after includes...
#include "core/llm/model_factory.h"

using namespace launcher::ui;
using namespace launcher::core;

// Constants for layout (REMOVED - Moved to chat_window_controller.mm)
// static const CGFloat kMinInputHeight = 36.0; 
// ... and others ...

// Forward declare the C++ class owning ChatWindowController (REMOVED - Already in header)
// namespace launcher { namespace ui { class MacOSChatUI; } }

// --- Convenience method for converting C++ error to Objective-C error ---
static BackendError* convertCppErrorToObjc(const launcher::core::BackendErrorInfo& errorInfo) {
    return [BackendError errorWithCode:errorInfo.code 
                              message:[NSString stringWithUTF8String:errorInfo.message.c_str()]
                      underlyingError:nil];
}

// --- Convenience method for converting C++ MentionableItems to Objective-C MentionSuggestions ---
static NSArray<MentionSuggestion*>* convertCppMentionItemsToObjc(const std::vector<launcher::core::MentionableItem>& items) {
    NSMutableArray<MentionSuggestion*>* result = [NSMutableArray arrayWithCapacity:items.size()];
    
    for (const auto& item : items) {
        NSString* identifier = [NSString stringWithUTF8String:item.id.c_str()];
        NSString* displayText = [NSString stringWithUTF8String:item.display_name.c_str()];
        
        MentionSuggestion* suggestion = [[MentionSuggestion alloc] initWithIdentifier:identifier
                                                                         displayText:displayText
                                                                       secondaryText:nil // TODO: Add secondary text if available in MentionableItem
                                                                           avatarURL:nil]; // TODO: Add avatar URL if available
        [result addObject:suggestion];
    }
    
    return result;
}

namespace launcher {
namespace ui {

// Template helper to wrap a C++ shared_ptr in NSValue
template<typename T>
NSValue* wrapSharedPtrInNSValue(const std::shared_ptr<T>& ptr) {
    return [NSValue valueWithBytes:&ptr objCType:@encode(void*)];
}

// Template helper to extract a C++ shared_ptr from NSValue
template<typename T>
std::shared_ptr<T> unwrapSharedPtrFromNSValue(NSValue* value) {
    std::shared_ptr<T> result;
    [value getValue:&result];
    return result;
}

// -----------------------------------------------------------------------------
// Implementation (PIMPL) – hides Objective-C objects and dispatch types
// -----------------------------------------------------------------------------

struct MacOSChatUI::Impl {
    TopChatWindowController* windowController{nil};
    NSWindow* window{nil};
    ChatViewController* chatViewController{nil};

    NSMapTable<NSNumber*, NSValue*>* activeRequestTokens{nil};
    dispatch_queue_t tokensQueue{nullptr};

    NSLock* streamStateLock{nil};
    NSString* activeAssistantMessageId{nil};

    // Rate-limiting state for flushing
    dispatch_time_t lastFlushTime{0};
    bool flushPending{false};

    bool visibilityRegistered{false};
};

MacOSChatUI::MacOSChatUI()
    : impl_(std::make_unique<Impl>()),
      sendCallback_(nullptr),
      receiveCallback_(nullptr)
{
    // Initialize C++ members
    try {
        // Retrieve dependency-injected ModelFactory from AppContext (via AppDelegate)
        std::shared_ptr<core::ModelFactory> factoryPtr = nullptr;
        AppDelegate *appDel = (AppDelegate *)[[NSApplication sharedApplication] delegate];
        if (appDel && appDel.ctx && appDel.ctx->modelFactory) {
            factoryPtr = appDel.ctx->modelFactory;
        }

        // Create backend instance with injected factory
        backend_ = std::make_unique<core::UnifiedChatBackend>(factoryPtr);

        if (!backend_) {
            ERM(@"Critical: Failed to create UnifiedChatBackend.");
            // Handle fatal error appropriately - maybe throw?
            throw std::runtime_error("Failed to initialize backend.");
        }

        conversationManager_ = std::make_unique<core::ConversationManager>();
        // Set default system message if desired
        // conversationManager_->setSystemMessage("You are a helpful assistant.");

        DBM(@"Initialized with UnifiedChatBackend and ConversationManager.");

        // Load default provider/model via injected config manager if available
        try {
            launcher::core::IConfigManager *cfgPtr = nullptr;
            AppDelegate *appDel = (AppDelegate *)[[NSApplication sharedApplication] delegate];
            if (appDel && appDel.ctx && appDel.ctx->configManager) {
                cfgPtr = appDel.ctx->configManager.get();
            } else {
                cfgPtr = &ConfigService();
            }

            if (cfgPtr) {
                currentProvider_ = cfgPtr->getString("default_ai_provider", "openai");
                currentModel_    = cfgPtr->getProviderDefaultModel(currentProvider_);
            }
        } catch (...) {
            // Config may not be initialised yet; keep built-in defaults
        }

    } catch (const std::exception& e) {
        ERM(@"Exception during MacOSChatUI C++ member initialization: %s", e.what());
        throw;
    }

    // Initialize ObjC token management map and queue later in initialize() or here if preferred
    @autoreleasepool {
        // Moved token map/queue initialization here for clarity
        impl_->activeRequestTokens = [NSMapTable strongToStrongObjectsMapTable];
        impl_->tokensQueue = dispatch_queue_create("com.launcher.MacOSChatUI.tokensQueue", DISPATCH_QUEUE_SERIAL);
        
        // --- Initialize Lock ---
        impl_->streamStateLock = [[NSLock alloc] init];
        // --- End Initialize Lock ---
        
        impl_->activeAssistantMessageId = nil; // Ensure it's nil initially

        // Create window controller - now using TopChatWindowController
        impl_->windowController = [[TopChatWindowController alloc] init];
        
        // Pass the 'this' pointer to the controller
        [(TopChatWindowController*)impl_->windowController setUiInstance:this];

        // Get the window from the window controller
        impl_->window = [(TopChatWindowController*)impl_->windowController window];
        
        // Get the chat view controller from the content controller in the hierarchy
        impl_->chatViewController = [(TopChatWindowController*)impl_->windowController contentVC].chatViewController;

        // Check if pointers were set correctly
        if (!impl_->window || !impl_->chatViewController) {
            ERM(@"Critical: Failed to get window or chatViewController from TopChatWindowController.");
            // Throw or handle fatal error
            throw std::runtime_error("Failed to get window/chatViewController from controller hierarchy.");
        }
        
        // Add test messages *after* controllers are confirmed to exist
        // [(TopChatWindowController*)impl_->windowController addTestMessages];
    }
}

MacOSChatUI::~MacOSChatUI() {
    DBG("Destroying UI");
    
    @autoreleasepool {
        // Cancel any remaining tokens
        cancelAllPendingRequests();
        
        if (impl_->window) {
            // Ensure delegate is nilled to prevent crashes during close
            [((NSWindow*)impl_->window) setDelegate:nil];
            // Release window controller reference (which owns the window)
            impl_->windowController = nil;
            impl_->window = nil;
            impl_->chatViewController = nil;
        }
    }
}

void MacOSChatUI::cancelAllPendingRequests() {
    DBG("Cancelling all pending requests");
    
    @autoreleasepool {
        dispatch_sync(impl_->tokensQueue, ^{
            NSDictionary<NSNumber*, NSValue*>* snapshot = [impl_->activeRequestTokens dictionaryRepresentation];
            for (NSNumber* identifier in snapshot) {
                NSValue* tokenValue = snapshot[identifier];
                if (tokenValue) {
                    auto token = unwrapSharedPtrFromNSValue<utilities::CancellationToken>(tokenValue);
                    if (token) {
                        DBG("Cancelling token for identifier: " + std::to_string([identifier unsignedLongLongValue]));
                        token->cancel();
                    }
                }
            }
            [impl_->activeRequestTokens removeAllObjects];
        });
    }
}

void MacOSChatUI::cancelRequestWithIdentifier(NSNumber* requestIdentifier) {
    if (!requestIdentifier) return;
    
    DBG("Cancelling request with identifier: " + std::to_string([requestIdentifier unsignedLongLongValue]));
    
    @autoreleasepool {
        dispatch_sync(impl_->tokensQueue, ^{
            NSValue* tokenValue = [impl_->activeRequestTokens objectForKey:requestIdentifier];
            if (tokenValue) {
                auto token = unwrapSharedPtrFromNSValue<utilities::CancellationToken>(tokenValue);
                if (token) {
                    DBG("Found token, calling cancel()");
                    token->cancel();
                    // Note: We don't remove the token here - removal happens in the callback
                } else {
                    DBG("Error: Token value was nil or invalid");
                }
            } else {
                DBG("No token found for identifier");
            }
        });
    }
}

// Helper function to add test messages to the chat
void MacOSChatUI::addTestMessages() {
    if (!impl_->chatViewController) {
         ERR(@"Cannot add test messages, chatViewController is null.");
        return;
    }
    
    @autoreleasepool {
        // Create a reduced set of test messages for faster loading and debugging
        NSArray* senderNames = @[@"Assistant", @"System", @"User"];
        NSArray* senderIds = @[@"assistant", @"system", @"user"];
        
        // Simplified test messages
        NSArray* messageContents = @[
            @"Hello everyone! How are you doing today?",
            
            @"# Markdown Support\n\nThis message demonstrates **markdown** formatting support with:\n\n- Bullet points\n- *Italic text*\n- **Bold text**\n\n## Code Blocks\n\n```swift\nfunc testFunction() {\n    print(\"Hello, world!\")\n}\n```",
            
            @"Here's a normal message.",
            
            @"## Large Message Example\n\nThis is an example of a very long message with multiple paragraphs and formatting to test the performance of the chat UI.\n\n### Features\n\n1. Headings\n2. Lists\n3. Code blocks\n4. Tables\n\n| Column 1 | Column 2 | Column 3 |\n|----------|----------|----------|\n| Data 1   | Data 2   | Data 3   |\n| Test A   | Test B   | Test C   |\n\n```objc\n@implementation Example\n- (void)testMethod {\n    NSLog(@\"Testing code blocks\");\n    for (int i = 0; i < 10; i++) {\n        // Do something\n        [self processItem:i];\n    }\n}\n@end\n```\n\nAnd here's more text after the code block.",
            
            @"I have a quick question about the project.",
            
            @"## Nested Lists\n\n* Top level item 1\n  * Nested item 1.1\n  * Nested item 1.2\n* Top level item 2\n  * Nested item 2.1\n    * Deeply nested item\n    * Another deeply nested item\n* Top level item 3",
            
            @"Has anyone seen the latest performance improvements?",
            
            @"# Very Large Message with Lots of Content\n\nLorem ipsum dolor sit amet, consectetur adipiscing elit. Nulla facilisi. Maecenas vel fermentum nunc. Suspendisse potenti. Curabitur dignissim libero vitae fermentum ultrices. Proin feugiat nibh at nisi cursus, ac mattis quam tincidunt. Nam fermentum, quam quis fermentum efficitur, magna orci tincidunt velit, id varius ante massa sed velit. Vestibulum ante ipsum primis in faucibus orci luctus et ultrices posuere cubilia curae; Donec varius nibh vel enim iaculis luctus.\n\n## Section 1\n\nPellentesque habitant morbi tristique senectus et netus et malesuada fames ac turpis egestas. Sed euismod, velit vel dictum volutpat, velit velit finibus magna, vel finibus velit magna vel velit. Vestibulum vel magna vel velit finibus magna.\n\n### Subsection 1.1\n\n* Item 1\n* Item 2\n* Item 3\n\n### Subsection 1.2\n\n1. First item\n2. Second item\n3. Third item\n\n## Section 2\n\n```cpp\n#include <iostream>\n\nint main() {\n    std::cout << \"Hello, World!\" << std::endl;\n    \n    for (int i = 0; i < 10; i++) {\n        std::cout << \"Count: \" << i << std::endl;\n    }\n    \n    return 0;\n}\n```\n\n## Section 3\n\n| Name | Age | Role |\n|------|-----|------|\n| John | 30  | Developer |\n| Jane | 28  | Designer |\n| Bob  | 35  | Manager |\n| Alice | 32 | Product Owner |\n\n## Section 4\n\n> This is a blockquote with *emphasized* text and **strong** text.\n> It can span multiple lines and include other markdown elements.\n\n## Conclusion\n\nThis was a test of a very large message with lots of different markdown elements to test the performance of our chat UI renderer.",

            @"# Extra-Large Table Test\n\n| ID | Name | Department | Position | Salary | Years | Location | Rating | Status | Projects |\n|---|---|---|---|---|---|---|---|---|---|\n| 001 | John Smith | Engineering | Senior Developer | $120,000 | 5 | New York | 4.8 | Active | Project A, Project B, Project C |\n| 002 | Mary Johnson | Marketing | Director | $135,000 | 7 | Chicago | 4.9 | Active | Campaign X, Launch Y |\n| 003 | David Williams | Finance | Analyst | $95,000 | 3 | Boston | 4.5 | Active | Budget 2023, Forecasting |\n| 004 | Sarah Brown | HR | Manager | $110,000 | 6 | San Francisco | 4.7 | Active | Recruitment, Training |\n| 005 | Michael Davis | Sales | Executive | $150,000 | 8 | Los Angeles | 4.9 | Active | Enterprise, SMB |\n| 006 | Jennifer Miller | Product | Designer | $105,000 | 4 | Seattle | 4.6 | Active | UI Redesign, Mobile App |\n| 007 | Robert Wilson | Engineering | Developer | $100,000 | 2 | Austin | 4.3 | Active | Backend API, Database |\n| 008 | Lisa Moore | Marketing | Specialist | $85,000 | 3 | Denver | 4.4 | Active | Social Media, Content |\n| 009 | James Taylor | Operations | Director | $130,000 | 9 | Miami | 4.8 | Inactive | Logistics, Supply Chain |\n| 010 | Patricia Anderson | Legal | Counsel | $140,000 | 7 | Washington DC | 4.7 | Active | Compliance, Contracts |\n| 011 | Thomas Jackson | IT | Support | $75,000 | 2 | Philadelphia | 4.2 | Active | Help Desk, Infrastructure |\n| 012 | Barbara White | Customer Service | Manager | $90,000 | 5 | Phoenix | 4.5 | Active | Call Center, CRM |\n| 013 | Christopher Harris | Research | Scientist | $115,000 | 6 | San Diego | 4.8 | Active | AI Development, Data Analysis |\n| 014 | Nancy Martin | Finance | Controller | $125,000 | 8 | Dallas | 4.6 | Active | Auditing, Tax Planning |\n| 015 | Daniel Thompson | Engineering | Architect | $140,000 | 10 | Portland | 4.9 | Active | System Design, Cloud Migration |",
            
            @"# Massive Code Block Test\n\n```cpp\n#include <iostream>\n#include <vector>\n#include <string>\n#include <algorithm>\n#include <map>\n#include <set>\n#include <cmath>\n#include <chrono>\n#include <thread>\n#include <mutex>\n#include <memory>\n\nclass ComplexDataProcessor {\nprivate:\n    std::vector<std::string> data_entries_;\n    std::map<int, std::vector<double>> processed_values_;\n    std::mutex mutex_;\n    bool initialized_;\n\npublic:\n    ComplexDataProcessor() : initialized_(false) {\n        std::cout << \"Creating new data processor instance\" << std::endl;\n    }\n\n    ~ComplexDataProcessor() {\n        std::cout << \"Destroying data processor instance\" << std::endl;\n        clearAllData();\n    }\n\n    bool initialize() {\n        std::lock_guard<std::mutex> lock(mutex_);\n        if (initialized_) {\n            std::cerr << \"Warning: Processor already initialized\" << std::endl;\n            return false;\n        }\n\n        try {\n            // Perform complex initialization\n            for (int i = 0; i < 10; ++i) {\n                std::vector<double> initial_values;\n                for (int j = 0; j < 5; ++j) {\n                    initial_values.push_back(std::sqrt(i * 10.0 + j));\n                }\n                processed_values_[i] = initial_values;\n            }\n\n            initialized_ = true;\n            std::cout << \"Processor initialized successfully\" << std::endl;\n            return true;\n        } catch (const std::exception& e) {\n            std::cerr << \"Initialization error: \" << e.what() << std::endl;\n            return false;\n        }\n    }\n\n    bool addDataEntry(const std::string& entry) {\n        std::lock_guard<std::mutex> lock(mutex_);\n        if (!initialized_) {\n            std::cerr << \"Error: Cannot add data before initialization\" << std::endl;\n            return false;\n        }\n\n        data_entries_.push_back(entry);\n        std::cout << \"Added new data entry: \" << entry << std::endl;\n        return true;\n    }\n\n    bool processDataBatch(int batch_id, const std::vector<double>& input_values) {\n        std::lock_guard<std::mutex> lock(mutex_);\n        if (!initialized_) {\n            std::cerr << \"Error: Cannot process data before initialization\" << std::endl;\n            return false;\n        }\n\n        if (input_values.empty()) {\n            std::cerr << \"Warning: Empty input batch\" << std::endl;\n            return false;\n        }\n\n        std::vector<double> processed;\n        for (const auto& value : input_values) {\n            // Complex processing simulation\n            double result = 0.0;\n            for (int i = 0; i < 100; ++i) {\n                result += std::sin(value * i / 10.0) * std::cos(i / 5.0);\n            }\n            processed.push_back(result);\n        }\n\n        processed_values_[batch_id] = processed;\n        std::cout << \"Processed batch \" << batch_id << \" with \" << processed.size() << \" values\" << std::endl;\n        return true;\n    }\n\n    std::vector<double> getProcessedBatch(int batch_id) {\n        std::lock_guard<std::mutex> lock(mutex_);\n        auto it = processed_values_.find(batch_id);\n        if (it == processed_values_.end()) {\n            std::cerr << \"Error: Batch \" << batch_id << \" not found\" << std::endl;\n            return std::vector<double>();\n        }\n        return it->second;\n    }\n\n    void clearAllData() {\n        std::lock_guard<std::mutex> lock(mutex_);\n        data_entries_.clear();\n        processed_values_.clear();\n        std::cout << \"All data cleared\" << std::endl;\n    }\n\n    // Multi-threaded processing\n    bool parallelProcess(int num_threads) {\n        if (!initialized_) {\n            std::cerr << \"Error: Cannot start parallel processing before initialization\" << std::endl;\n            return false;\n        }\n\n        std::vector<std::thread> threads;\n        for (int t = 0; t < num_threads; ++t) {\n            threads.push_back(std::thread([this, t]() {\n                std::vector<double> input_data;\n                for (int i = 0; i < 100; ++i) {\n                    input_data.push_back(t * 100.0 + i);\n                }\n                this->processDataBatch(t + 100, input_data);\n                std::cout << \"Thread \" << t << \" completed processing\" << std::endl;\n            }));\n        }\n\n        // Wait for all threads to complete\n        for (auto& thread : threads) {\n            thread.join();\n        }\n\n        std::cout << \"All parallel processing completed\" << std::endl;\n        return true;\n    }\n};\n\nint main() {\n    std::cout << \"Starting complex data processing application\" << std::endl;\n    \n    auto processor = std::make_unique<ComplexDataProcessor>();\n    if (!processor->initialize()) {\n        std::cerr << \"Failed to initialize processor\" << std::endl;\n        return 1;\n    }\n    \n    // Add some test data\n    processor->addDataEntry(\"Test entry 1\");\n    processor->addDataEntry(\"Test entry 2\");\n    processor->addDataEntry(\"Test entry 3\");\n    \n    // Process a batch\n    std::vector<double> test_batch = {1.0, 2.0, 3.0, 4.0, 5.0};\n    processor->processDataBatch(1, test_batch);\n    \n    // Start parallel processing\n    processor->parallelProcess(4);\n    \n    // Retrieve and display results\n    auto results = processor->getProcessedBatch(1);\n    std::cout << \"Results from batch 1:\" << std::endl;\n    for (const auto& val : results) {\n        std::cout << val << \" \";\n    }\n    std::cout << std::endl;\n    \n    std::cout << \"Application completed successfully\" << std::endl;\n    return 0;\n}\n```",
            
            @"# Multi-Dimensional Table Test\n\n## Performance Comparison Across Multiple Dimensions\n\n| Algorithm | Dataset Size | Time (ms) | Memory (MB) | Accuracy | Precision | Recall | F1 Score | Hardware | Iterations |\n|-----------|-------------|-----------|-------------|----------|-----------|--------|----------|----------|------------|\n| Algorithm A | Small | 25 | 45 | 0.92 | 0.91 | 0.93 | 0.92 | CPU | 100 |\n| Algorithm A | Medium | 145 | 180 | 0.90 | 0.89 | 0.91 | 0.90 | CPU | 100 |\n| Algorithm A | Large | 580 | 350 | 0.88 | 0.87 | 0.89 | 0.88 | CPU | 100 |\n| Algorithm A | Small | 15 | 65 | 0.93 | 0.92 | 0.94 | 0.93 | GPU | 100 |\n| Algorithm A | Medium | 85 | 220 | 0.91 | 0.90 | 0.92 | 0.91 | GPU | 100 |\n| Algorithm A | Large | 320 | 460 | 0.89 | 0.88 | 0.90 | 0.89 | GPU | 100 |\n| Algorithm B | Small | 30 | 40 | 0.94 | 0.93 | 0.95 | 0.94 | CPU | 100 |\n| Algorithm B | Medium | 160 | 165 | 0.92 | 0.91 | 0.93 | 0.92 | CPU | 100 |\n| Algorithm B | Large | 610 | 320 | 0.90 | 0.89 | 0.91 | 0.90 | CPU | 100 |\n| Algorithm B | Small | 18 | 55 | 0.95 | 0.94 | 0.96 | 0.95 | GPU | 100 |\n| Algorithm B | Medium | 95 | 195 | 0.93 | 0.92 | 0.94 | 0.93 | GPU | 100 |\n| Algorithm B | Large | 340 | 420 | 0.91 | 0.90 | 0.92 | 0.91 | GPU | 100 |\n| Algorithm C | Small | 20 | 50 | 0.96 | 0.95 | 0.97 | 0.96 | CPU | 100 |\n| Algorithm C | Medium | 130 | 190 | 0.94 | 0.93 | 0.95 | 0.94 | CPU | 100 |\n| Algorithm C | Large | 520 | 370 | 0.92 | 0.91 | 0.93 | 0.92 | CPU | 100 |\n| Algorithm C | Small | 12 | 70 | 0.97 | 0.96 | 0.98 | 0.97 | GPU | 100 |\n| Algorithm C | Medium | 75 | 240 | 0.95 | 0.94 | 0.96 | 0.95 | GPU | 100 |\n| Algorithm C | Large | 280 | 490 | 0.93 | 0.92 | 0.94 | 0.93 | GPU | 100 |"
        ];
        
        // Create and immediately add messages one by one on the main thread
        for (NSUInteger i = 0; i < messageContents.count; i++) {
            NSString* content = messageContents[i];
            NSString* senderId = senderIds[i % senderIds.count];
            NSString* senderName = senderNames[i % senderNames.count];
            
            Message* message = [[Message alloc] initWithContent:content
                                                      senderId:senderId
                                                    senderName:senderName
                                                          type:MessageType::TEXT];
            
            // Add each message directly on the main thread
            if ([NSThread isMainThread]) {
                ChatViewController* cvc = (ChatViewController*)impl_->chatViewController;
                if (cvc) {
                    [cvc addMessage:message];
                    // DBG("addTestMessages", "Added test message directly");
                }
            } else {
                dispatch_sync(dispatch_get_main_queue(), ^{
                    ChatViewController* cvc = (ChatViewController*)impl_->chatViewController;
                    if (cvc) {
                        [cvc addMessage:message];
                        DBG("Added test message via dispatch_sync");
                    }
                });
            }
        }
    }
}

// Initialize: Now simpler, controllers are created in constructor
bool MacOSChatUI::initialize(SendMessageCallback sendCallback, 
                             ReceiveMessageCallback receiveCallback) {
    @autoreleasepool {
        // DBG("initialize", "Initializing with callbacks");
        
        // Store callbacks
        sendCallback_ = sendCallback;
        receiveCallback_ = receiveCallback;
        
        if (!impl_->windowController || !impl_->window || !impl_->chatViewController) {
             ERR(@"Initialization failed: Controllers not created properly.");
             return false;
        }
        
        // Focus on the input field explicitly
        // DBG("initialize", "Focusing on chat input in initialize()");
        [(TopChatWindowController*)impl_->windowController focusOnChatInput]; // Now using TopChatWindowController
        
        // DBG("initialize", "Initialization complete.");
        return true;
    }
}

void MacOSChatUI::show() {
    @autoreleasepool {
        if (impl_->window) {
            //  DBG("show", "Showing window.");
            
            // Ensure app is visible in Dock and Command-Tab switcher exactly once
            if (!impl_->visibilityRegistered) {
                launcher::ui::ApplicationVisibilityManager::shared().openChatWindow();
                impl_->visibilityRegistered = true;
            }
            
            // Cast opaque pointer back to NSWindow*
            NSWindow *nsWindow = (NSWindow *)impl_->window;

            BOOL handledVisibility = NO;

            if (@available(macOS 10.12, *)) {
                if (nsWindow.tabGroup && nsWindow.tabGroup.windows.count > 1) {
                    // The window is already part of a tab group that has more than one tab.
                    // Merely select this tab without altering the group anchor to avoid
                    //   inadvertently closing the whole window when this tab later closes.
                    nsWindow.tabGroup.selectedWindow = nsWindow;

                    // Bring the *anchor* window (first element) to the front so the entire
                    //   tab-group becomes key without promoting the new tab to anchor status.
                    NSWindow *anchorWindow = nsWindow.tabGroup.windows.firstObject ?: nsWindow;
                    [anchorWindow makeKeyAndOrderFront:nil];

                    handledVisibility = YES;
                }
            }

            if (!handledVisibility) {
                // Stand-alone window (or pre-10.12 macOS). Default behaviour.
                [nsWindow makeKeyAndOrderFront:nil];
            }

            [NSApp activateIgnoringOtherApps:YES];

            // Focus on the input field when window is shown
            [(TopChatWindowController*)impl_->windowController focusOnChatInput];
        } else {
            ERR(@"Cannot show window, it's null.");
        }
    }
}

void MacOSChatUI::hide() {
    @autoreleasepool {
        if (impl_->window) {
             DBG("Hiding window.");
             // Cast opaque pointer back to NSWindow*
            [((NSWindow*)impl_->window) orderOut:nil];
        } else {
             ERR(@"Cannot hide window, it's null.");
        }
    }
}

bool MacOSChatUI::addMessage(const std::string& senderId, 
                              const std::string& senderName, 
                              const std::string& content) {
    @autoreleasepool {
        if (!impl_->chatViewController) {
            ERR(@"Cannot add message, chatViewController is null.");
            return false;
        }
        
        // Create a new Message object
        Message* message = [[Message alloc] initWithContent:[NSString stringWithUTF8String:content.c_str()]
                                                   senderId:[NSString stringWithUTF8String:senderId.c_str()]
                                                 senderName:[NSString stringWithUTF8String:senderName.c_str()]
                                                       type:MessageType::TEXT];
        
        // Check if we are on the main thread
        if ([NSThread isMainThread]) {
            //  DBG("Adding message on main thread");
             ChatViewController* cvc = (ChatViewController*)impl_->chatViewController;
             if (cvc) {
                [cvc addMessage:message];
             }
        } else {
             DBG("Adding message from background thread");
            // Dispatch to the main thread synchronously (or asynchronously if appropriate)
            dispatch_sync(dispatch_get_main_queue(), ^{
                 ChatViewController* cvc = (ChatViewController*)impl_->chatViewController;
                 if (cvc) {
                    [cvc addMessage:message];
                 }
            });
        }
        
        // Add to conversation history
        if (conversationManager_) {
            std::string role = senderId;
            if (role != "assistant" && role != "system") { role = "user"; }
            conversationManager_->addMessageShared(role, std::make_shared<std::string>(content));
        }
        
        // Persistence now handled automatically by ConversationManager::addMessage()
        return true;
    }
}

bool MacOSChatUI::clearMessages() {
    DBG("clearMessages called");
    
    @autoreleasepool {
        if (!impl_->chatViewController) {
            ERR(@"Cannot clear messages, chatViewController is null.");
            return false;
        }
        
        // Call clearAllMessages on the ChatViewController
        // Since ChatViewController is Objective-C, need to cast back
        if ([NSThread isMainThread]) {
            ChatViewController* cvc = (ChatViewController*)impl_->chatViewController;
            [cvc clearMessages];
        } else {
            dispatch_async(dispatch_get_main_queue(), ^{
                ChatViewController* cvc = (ChatViewController*)impl_->chatViewController;
                [cvc clearMessages];
            });
        }
        
        // Persist cleared state
        if (conversationManager_) {
            conversationManager_->clear();
            auto json = conversationManager_->exportToJson();
            launcher::core::persistence::ConversationStore::instance()
                .saveConversation(conversationManager_->getConversationId(), json);
        }
        return true;
    }
}

int MacOSChatUI::run() {
    DBG("run called (delegates to AppKit)");
    // The main event loop is typically run by NSApplication's run method.
    // This function might not be strictly necessary for a Cocoa app unless
    // it's intended for a non-standard run loop or blocking behavior.
    // Returning 0 to satisfy linker.
    return 0;
}

// Factory function to create Chat UI
std::shared_ptr<ChatUIInterface> createChatUI() {
    return std::make_shared<MacOSChatUI>();
}

// --- Add the real implementation for searchMentionableItemsWithQuery ---
void MacOSChatUI::searchMentionableItemsWithQuery(NSString* query, 
                                               NSNumber* requestIdentifier,
                                               MentionCompletionBlock completion) {
    if (!completion || !requestIdentifier) {
        DBG("Invalid parameters in searchMentionableItemsWithQuery");
        return;
    }
    
    if (!backend_) {
        DBG("Backend not initialized");
        dispatch_async(dispatch_get_main_queue(), ^{
            completion(nil, [BackendError errorWithCode:BackendErrorCode::INTERNAL_ERROR 
                                               message:@"Backend not initialized" 
                                       underlyingError:nil]);
        });
        return;
    }
    
    std::string_view cppQuery_sv = [query UTF8String] ?: "";
    DBG("Searching for mentions with query: " << cppQuery_sv <<
              ", identifier: " << std::to_string([requestIdentifier unsignedLongLongValue]));
    
    @autoreleasepool {
        // Check cache first
        NSString* cacheKey = [[CacheManager sharedInstance] cacheKeyForMentionQuery:query];
        NSArray<MentionSuggestion*>* cachedSuggestions = [[CacheManager sharedInstance] objectForKey:cacheKey];
        
        if (cachedSuggestions) {
            DBG("Cache hit for mention query: " << std::string([query UTF8String]));
            dispatch_async(dispatch_get_main_queue(), ^{
                completion(cachedSuggestions, nil);
            });
            return;
        }
        
        // Create a cancellation token
        auto cancellationToken = std::make_shared<utilities::CancellationToken>();
        
        // Box the shared_ptr into NSValue
        NSValue* tokenValue = wrapSharedPtrInNSValue(cancellationToken);
        
        // Store the token in the map (thread-safely)
        __block bool storeSucceeded = false;
        
        dispatch_sync(impl_->tokensQueue, ^{
            // Check for existing token with same ID
            NSValue* existingTokenValue = [impl_->activeRequestTokens objectForKey:requestIdentifier];
            if (existingTokenValue) {
                DBG("Warning: Request identifier already active, cancelling previous and overwriting");
                // Cancel the old token before overwriting
                auto oldToken = unwrapSharedPtrFromNSValue<utilities::CancellationToken>(existingTokenValue);
                if (oldToken) {
                    oldToken->cancel();
                }
            }

            // Store the new token
            [impl_->activeRequestTokens setObject:tokenValue forKey:requestIdentifier];
            storeSucceeded = true;
        });
        
        if (!storeSucceeded) {
            DBG("Failed to store token");
            dispatch_async(dispatch_get_main_queue(), ^{
                completion(nil, [BackendError errorWithCode:BackendErrorCode::INTERNAL_ERROR 
                                                   message:@"Failed to store cancellation token" 
                                           underlyingError:nil]);
            });
            return;
        }

        // Convert query to std::string
        std::string cppQuery = [query UTF8String];
        
        // Define C++ callback lambda (with proper captures)
        __weak MacOSChatUI* weakSelf = this;
        
        auto cppCallback = [completion, requestIdentifier, weakSelf, cancellationToken, tokenValue, query, cacheKey]
            (core::MentionResult result) {
            // This executes on a GCD background thread
            
            // Dispatch back to main thread for UI work and map access
            dispatch_async(dispatch_get_main_queue(), ^{
                MacOSChatUI* strongSelf = weakSelf;
                if (!strongSelf) {
                    DBG("Self deallocated during callback");
                    return;
                }
                
                // Clean up token from map
                __block bool removed = false;
                __block bool wasCancelled = false;
                
                dispatch_sync(strongSelf->impl_->tokensQueue, ^{
                    // Check if the token was cancelled
                    wasCancelled = cancellationToken->isCancelled();
                    
                    // Remove from map (only if it's the same token)
                    NSValue* currentValue = [strongSelf->impl_->activeRequestTokens objectForKey:requestIdentifier];
                    if (currentValue && [currentValue isEqual:tokenValue]) {
                        [strongSelf->impl_->activeRequestTokens removeObjectForKey:requestIdentifier];
                        removed = true;
                        DBG("Removed token from map during callback");
                    }
                });
                
                // Process result
                @try {
                    // Check for cancellation first
                    if (std::holds_alternative<core::BackendErrorInfo>(result)) {
                        auto errorInfo = std::get<core::BackendErrorInfo>(result);
                        if (errorInfo.code == core::BackendErrorCode::CANCELLED || wasCancelled) {
                            DBG("Mention search was cancelled");
                            completion(nil, [BackendError errorWithCode:core::BackendErrorCode::CANCELLED 
                                                               message:@"Operation cancelled" 
                                                       underlyingError:nil]);
                            return;
                        }
                        
                        // Otherwise it's a genuine error
                        DBG("Mention search failed with error: " + errorInfo.message);
                        completion(nil, convertCppErrorToObjc(errorInfo));
                        return;
                    }
                    
                    // We have results
                    auto items = std::get<std::vector<core::MentionableItem>>(result);
                    DBG("Mention search succeeded with " + std::to_string(items.size()) + " items");
                    
                    // Convert C++ items to Objective-C suggestions
                    NSArray<MentionSuggestion*>* suggestions = convertCppMentionItemsToObjc(items);
                    
                    // Cache the results if we have any
                    if (suggestions.count > 0) {
                        // Determine a cost based on the number of items (adjust as needed)
                        NSUInteger cost = suggestions.count * 500; // Rough estimate
                        
                        // Cache with a TTL of 5 minutes (300 seconds)
                        [[CacheManager sharedInstance] setObject:suggestions forKey:cacheKey cost:cost ttl:300];
                    }
                    
                 completion(suggestions, nil); 
                    
                } @catch (NSException* exception) {
                    ERR("Exception during result processing: " + std::string([[exception description] UTF8String]));
                    completion(nil, [BackendError errorWithCode:core::BackendErrorCode::INTERNAL_ERROR 
                                                       message:[NSString stringWithFormat:@"Internal exception: %@", exception] 
                                               underlyingError:nil]);
                } @catch (...) {
                    ERR("Unknown exception during result processing");
                    completion(nil, [BackendError errorWithCode:core::BackendErrorCode::INTERNAL_ERROR 
                                                       message:@"Unknown internal exception" 
                                               underlyingError:nil]);
                }
            });
        };
        
        // Call the backend with the token and std::string_view
        backend_->searchMentionableItems(std::string(cppQuery_sv), cancellationToken, std::move(cppCallback)); // Pass std::string temp for now if backend expects it
    }
}

// --- Add the searchHistoryWithPrefix method ---
void MacOSChatUI::searchHistoryWithPrefix(NSString* prefix, 
                                        NSNumber* requestIdentifier,
                                        HistoryCompletionBlock completion) {
    if (!completion || !requestIdentifier) {
        DBG("Invalid parameters in searchHistoryWithPrefix");
        return;
    }
    
    if (!backend_) {
        DBG("Backend not initialized");
        dispatch_async(dispatch_get_main_queue(), ^{
            completion(nil, [BackendError errorWithCode:BackendErrorCode::INTERNAL_ERROR 
                                               message:@"Backend not initialized" 
                                       underlyingError:nil]);
        });
        return;
    }
    
    DBG("Searching history with prefix: " + std::string([prefix UTF8String]) + 
              ", identifier: " + std::to_string([requestIdentifier unsignedLongLongValue]));
    
    @autoreleasepool {
        // Check cache first
        NSString* cacheKey = [[CacheManager sharedInstance] cacheKeyForHistoryPrefix:prefix];
        NSArray<NSString*>* cachedHistory = [[CacheManager sharedInstance] objectForKey:cacheKey];
        
        if (cachedHistory) {
            DBG("Cache hit for history prefix: " + std::string([prefix UTF8String]));
            dispatch_async(dispatch_get_main_queue(), ^{
                completion(cachedHistory, nil);
            });
            return;
        }
        
        // Create a cancellation token
        auto cancellationToken = std::make_shared<utilities::CancellationToken>();
        
        // Box the shared_ptr into NSValue
        NSValue* tokenValue = wrapSharedPtrInNSValue(cancellationToken);
        
        // Store the token in the map (thread-safely)
        __block bool storeSucceeded = false;
        
        dispatch_sync(impl_->tokensQueue, ^{
            // Check for existing token with same ID
            NSValue* existingTokenValue = [impl_->activeRequestTokens objectForKey:requestIdentifier];
            if (existingTokenValue) {
                DBG("Warning: Request identifier already active, cancelling previous and overwriting");
                // Cancel the old token before overwriting
                auto oldToken = unwrapSharedPtrFromNSValue<utilities::CancellationToken>(existingTokenValue);
                if (oldToken) {
                    oldToken->cancel();
                }
            }

            // Store the new token
            [impl_->activeRequestTokens setObject:tokenValue forKey:requestIdentifier];
            storeSucceeded = true;
        });
        
        if (!storeSucceeded) {
            DBG("Failed to store token");
            dispatch_async(dispatch_get_main_queue(), ^{
                completion(nil, [BackendError errorWithCode:BackendErrorCode::INTERNAL_ERROR 
                                                   message:@"Failed to store cancellation token" 
                                           underlyingError:nil]);
            });
            return;
        }

    // Convert prefix to std::string_view
    std::string_view cppPrefix_sv = [prefix UTF8String] ?: "";
        
        // Define C++ callback lambda (with proper captures)
        __weak MacOSChatUI* weakSelf = this;
        
        auto cppCallback = [completion, requestIdentifier, weakSelf, cancellationToken, tokenValue, prefix, cacheKey]
            (core::HistoryResult result) {
            // This executes on a GCD background thread
            
            // Dispatch back to main thread for UI work and map access
            dispatch_async(dispatch_get_main_queue(), ^{
                MacOSChatUI* strongSelf = weakSelf;
                if (!strongSelf) {
                    DBG("Self deallocated during callback");
                    return;
                }
                
                // Clean up token from map
                __block bool removed = false;
                __block bool wasCancelled = false;
                
                dispatch_sync(strongSelf->impl_->tokensQueue, ^{
                    // Check if the token was cancelled
                    wasCancelled = cancellationToken->isCancelled();
                    
                    // Remove from map (only if it's the same token)
                    NSValue* currentValue = [strongSelf->impl_->activeRequestTokens objectForKey:requestIdentifier];
                    if (currentValue && [currentValue isEqual:tokenValue]) {
                        [strongSelf->impl_->activeRequestTokens removeObjectForKey:requestIdentifier];
                        removed = true;
                        DBG("Removed token from map during callback");
                    }
                });
                
                // Process result
                @try {
                    // Check for cancellation first
                    if (std::holds_alternative<core::BackendErrorInfo>(result)) {
                        auto errorInfo = std::get<core::BackendErrorInfo>(result);
                        if (errorInfo.code == core::BackendErrorCode::CANCELLED || wasCancelled) {
                            DBG("History search was cancelled");
                            completion(nil, [BackendError errorWithCode:core::BackendErrorCode::CANCELLED 
                                                               message:@"Operation cancelled" 
                                                       underlyingError:nil]);
                            return;
                        }
                        
                        // Otherwise it's a genuine error
                        DBG("History search failed with error: " + errorInfo.message);
                        completion(nil, convertCppErrorToObjc(errorInfo));
                        return;
                    }
                    
                    // We have results - convert from std::vector<std::string> to NSArray<NSString*>
                    auto historyItems = std::get<std::vector<std::string>>(result);
                    DBG("History search succeeded with " + std::to_string(historyItems.size()) + " items");
                    
                    NSMutableArray<NSString*>* historyArray = [NSMutableArray arrayWithCapacity:historyItems.size()];
                    for (const auto& item : historyItems) {
                        [historyArray addObject:[NSString stringWithUTF8String:item.c_str()]];
                    }
                    
                    // Cache the results if we have any
                    if (historyArray.count > 0) {
                        // Determine a cost based on the number of items and average string length
                        NSUInteger totalLength = 0;
                        for (NSString* item in historyArray) {
                            totalLength += item.length;
                        }
                        NSUInteger cost = totalLength * 2; // Rough estimate (2 bytes per char)
                        
                        // Cache with a TTL of 10 minutes (600 seconds)
                        [[CacheManager sharedInstance] setObject:historyArray forKey:cacheKey cost:cost ttl:600];
                    }
                    
                    completion(historyArray, nil);
                    
                } @catch (NSException* exception) {
                    ERR("Exception during result processing: " + std::string([[exception description] UTF8String]));
                    completion(nil, [BackendError errorWithCode:core::BackendErrorCode::INTERNAL_ERROR 
                                                       message:[NSString stringWithFormat:@"Internal exception: %@", exception] 
                                               underlyingError:nil]);
                } @catch (...) {
                    ERR("Unknown exception during result processing");
                    completion(nil, [BackendError errorWithCode:core::BackendErrorCode::INTERNAL_ERROR 
                                                       message:@"Unknown internal exception" 
                                               underlyingError:nil]);
                }
            });
        };
        
        // Call the backend with the token and std::string_view
        backend_->searchHistory(std::string(cppPrefix_sv), cancellationToken, std::move(cppCallback)); // Pass std::string temp for now
    }
}

// --- Add the requestLLMSuggestionsForContext method ---
void MacOSChatUI::requestLLMSuggestionsForContext(NSString* context, 
                                                NSNumber* requestIdentifier,
                                                LLMSuggestionCompletionBlock completion) {
    if (!completion || !requestIdentifier) {
        DBG("Invalid parameters in requestLLMSuggestionsForContext");
        return;
    }
    
    if (!backend_) {
        DBG("Backend not initialized");
        dispatch_async(dispatch_get_main_queue(), ^{
            completion(nil, [BackendError errorWithCode:BackendErrorCode::INTERNAL_ERROR 
                                               message:@"Backend not initialized" 
                                       underlyingError:nil]);
        });
        return;
    }
    
    DBG("Requesting LLM suggestions with context length: " + std::to_string([context length]) + 
              ", identifier: " + std::to_string([requestIdentifier unsignedLongLongValue]));
    
    @autoreleasepool {
        // Check cache first using the V2 context key strategy
        NSString* cacheKey = [[CacheManager sharedInstance] cacheKeyForLLMContext:context];
        NSArray<NSString*>* cachedSuggestions = [[CacheManager sharedInstance] objectForKey:cacheKey];
        
        if (cachedSuggestions) {
            DBG("Cache hit for LLM context with key: " + std::string([cacheKey UTF8String]));
            dispatch_async(dispatch_get_main_queue(), ^{
                completion(cachedSuggestions, nil);
            });
            return;
        }
        
        // Create a cancellation token
        auto cancellationToken = std::make_shared<utilities::CancellationToken>();
        
        // Box the shared_ptr into NSValue
        NSValue* tokenValue = wrapSharedPtrInNSValue(cancellationToken);
        
        // Store the token in the map (thread-safely)
        __block bool storeSucceeded = false;
        
        dispatch_sync(impl_->tokensQueue, ^{
            // Check for existing token with same ID
            NSValue* existingTokenValue = [impl_->activeRequestTokens objectForKey:requestIdentifier];
            if (existingTokenValue) {
                DBG("Warning: Request identifier already active, cancelling previous and overwriting");
                // Cancel the old token before overwriting
                auto oldToken = unwrapSharedPtrFromNSValue<utilities::CancellationToken>(existingTokenValue);
                if (oldToken) {
                    oldToken->cancel();
                }
            }

            // Store the new token
            [impl_->activeRequestTokens setObject:tokenValue forKey:requestIdentifier];
            storeSucceeded = true;
        });
        
        if (!storeSucceeded) {
            DBG("Failed to store token");
            dispatch_async(dispatch_get_main_queue(), ^{
                completion(nil, [BackendError errorWithCode:BackendErrorCode::INTERNAL_ERROR 
                                                   message:@"Failed to store cancellation token" 
                                           underlyingError:nil]);
            });
            return;
        }

        // Convert context to std::string_view
        std::string_view cppContext_sv = [context UTF8String] ?: "";
        
        // Define C++ callback lambda (with proper captures)
        __weak MacOSChatUI* weakSelf = this;
        
        auto cppCallback = [completion, requestIdentifier, weakSelf, cancellationToken, tokenValue, cacheKey]
            (core::LLMSuggestionResult result) {
            // This executes on a GCD background thread
            
            // Dispatch back to main thread for UI work and map access
            dispatch_async(dispatch_get_main_queue(), ^{
                MacOSChatUI* strongSelf = weakSelf;
                if (!strongSelf) {
                    DBG("Self deallocated during callback");
                    return;
                }
                
                // Clean up token from map
                __block bool removed = false;
                __block bool wasCancelled = false;
                
                dispatch_sync(strongSelf->impl_->tokensQueue, ^{
                    // Check if the token was cancelled
                    wasCancelled = cancellationToken->isCancelled();
                    
                    // Remove from map (only if it's the same token)
                    NSValue* currentValue = [strongSelf->impl_->activeRequestTokens objectForKey:requestIdentifier];
                    if (currentValue && [currentValue isEqual:tokenValue]) {
                        [strongSelf->impl_->activeRequestTokens removeObjectForKey:requestIdentifier];
                        removed = true;
                        DBG("Removed token from map during callback");
                    }
                });
                
                // Process result
                @try {
                    // Check for cancellation first
                    if (std::holds_alternative<core::BackendErrorInfo>(result)) {
                        auto errorInfo = std::get<core::BackendErrorInfo>(result);
                        if (errorInfo.code == core::BackendErrorCode::CANCELLED || wasCancelled) {
                            DBG("LLM request was cancelled");
                            completion(nil, [BackendError errorWithCode:core::BackendErrorCode::CANCELLED 
                                                               message:@"Operation cancelled" 
                                                       underlyingError:nil]);
                            return;
                        }
                        
                        // Otherwise it's a genuine error
                        DBG("LLM request failed with error: " + errorInfo.message);
                        completion(nil, convertCppErrorToObjc(errorInfo));
                        return;
                    }
                    
                    // We have results - convert from std::vector<std::string> to NSArray<NSString*>
                    auto suggestions = std::get<std::vector<std::string>>(result);
                    DBG("LLM request succeeded with " + std::to_string(suggestions.size()) + " suggestions");
                    
                    NSMutableArray<NSString*>* suggestionsArray = [NSMutableArray arrayWithCapacity:suggestions.size()];
                    for (const auto& suggestion : suggestions) {
                        [suggestionsArray addObject:[NSString stringWithUTF8String:suggestion.c_str()]];
                    }
                    
                    // Cache the results if we have any (LLM results tend to be larger)
                    if (suggestionsArray.count > 0) {
                        // Determine a cost based on the number of items and average string length
                        NSUInteger totalLength = 0;
                        for (NSString* item in suggestionsArray) {
                            totalLength += item.length;
                        }
                        NSUInteger cost = totalLength * 2; // Rough estimate (2 bytes per char)
                        
                        // Cache with a TTL of 30 minutes (1800 seconds) - LLM results can be cached longer
                        [[CacheManager sharedInstance] setObject:suggestionsArray forKey:cacheKey cost:cost ttl:1800];
                    }
                    
                    completion(suggestionsArray, nil);
                    
                } @catch (NSException* exception) {
                    ERR("Exception during result processing: " + std::string([[exception description] UTF8String]));
                    completion(nil, [BackendError errorWithCode:core::BackendErrorCode::INTERNAL_ERROR 
                                                       message:[NSString stringWithFormat:@"Internal exception: %@", exception] 
                                               underlyingError:nil]);
                } @catch (...) {
                    ERR("Unknown exception during result processing");
                    completion(nil, [BackendError errorWithCode:core::BackendErrorCode::INTERNAL_ERROR 
                                                       message:@"Unknown internal exception" 
                                               underlyingError:nil]);
                }
            });
        };
        
        // Call the backend with the token and std::string_view
        backend_->requestLLMSuggestions(std::string(cppContext_sv), cancellationToken, std::move(cppCallback)); // Pass std::string temp for now
    }
}

// Define a reasonable token limit for context
const size_t kMaxContextTokens = 4000; // Example: Adjust based on model/needs


// --- Add the initiateStreamForMessage helper method ---
void MacOSChatUI::initiateStreamForMessage(NSString *userMessageText) {
    if (!backend_) {
        ERM(@"Backend is not initialized, cannot send message.");
        // Optionally display an error message in the UI
        return;
    }
    if (!impl_->chatViewController) {
        ERM(@"ChatViewController is nil, cannot add placeholder.");
        return;
    }
    if (!conversationManager_) {
        ERM(@"ConversationManager is nil, cannot manage history or context.");
        return;
    }

    // Quick command: "/model provider:model" switches the active model without contacting backend.
    if ([userMessageText hasPrefix:@"/model "]) {
        NSString *param = [userMessageText substringFromIndex:7];
        NSArray<NSString *> *parts = [param componentsSeparatedByString:@":"];
        if (parts.count == 2) {
            std::string providerCpp = [parts[0] UTF8String];
            std::string modelCpp    = [parts[1] UTF8String];

            setActiveModel(providerCpp, modelCpp);

            // Inform user via system message (no backend round-trip)
            std::string confirm = "Switched model to " + providerCpp + ":" + modelCpp;
            this->addMessage("system", "System", confirm);
        }
        return;
    }

    // DBM(@"Initiating stream for message: %@", userMessageText);

    // --- Step 0: Add user message to history *and* UI BEFORE assembling context ---
    std::string_view cppUserMessage_sv = [userMessageText UTF8String] ?: "";

    // Persist to history *and* update UI in a single operation. The call below
    // records the message in ConversationManager internally, avoiding the
    // previous double-add bug that caused duplicated user messages after a
    // session restore.
    this->addMessage("user", "User", std::string(cppUserMessage_sv)); // addMessage takes std::string

    // NEW: Inform SessionMetadataManager so the window title updates based on
    //      the very first visible user message (fixes "New Chat" not changing
    //      for programmatically-created windows).
    if (impl_->window) {
        launcher::ui::SessionMetadataManager::shared().userSentMessage((NSWindow *)impl_->window, userMessageText);
    }

    @autoreleasepool {
        // 1. Create and Add Placeholder Message to UI
        NSString* assistantMessageId = [[NSUUID UUID] UUIDString];
        // Display the concrete model (e.g. "gpt-4o" or "claude-3-sonnet") as sender
        NSString *senderDisplayName = [NSString stringWithUTF8String:currentModel_.c_str()];
        if (senderDisplayName.length == 0) senderDisplayName = @"Assistant";

        Message* placeholderMessage = [[Message alloc] initWithId:assistantMessageId
                                                        senderId:@"assistant"
                                                      senderName:senderDisplayName
                                                            type:MessageType::TYPING_INDICATOR // Use typing indicator
                                                         content:@""];  // Empty content, typing indicator will show "Generating response..."

        // Add placeholder to UI
        if ([NSThread isMainThread]) {
            [(ChatViewController*)impl_->chatViewController addMessage:placeholderMessage];
        } else {
            dispatch_sync(dispatch_get_main_queue(), ^{
                 [(ChatViewController*)impl_->chatViewController addMessage:placeholderMessage];
            });
        }
        
        // UI updates for generation start
        __weak MacOSChatUI* weakSelf = this; // Add weak self capture
        if (impl_->windowController) {
            dispatch_async(dispatch_get_main_queue(), ^{
                MacOSChatUI* strongSelf = weakSelf; // Add strong self reference
                if (!strongSelf || !strongSelf->impl_->windowController) return; // Check validity
                TopChatWindowController* controller = (TopChatWindowController*)strongSelf->impl_->windowController; // Cast needed
                [controller.contentVC.composerView showStopButton];
            });
        }

        // Convert assistant ID to std::string for capture
        std::string cppAssistantMessageId = [assistantMessageId UTF8String];

        // 2. Cancel Previous Stream & Manage State
        [impl_->streamStateLock lock]; // Lock before accessing stream state
        if (activeStreamToken_) {
            DBM(@"Cancelling previous active stream.");
            activeStreamToken_->cancel();
            activeStreamToken_.reset();
        }
        impl_->activeAssistantMessageId = assistantMessageId; // Assign new ID
        activeStreamToken_ = std::make_shared<utilities::CancellationToken>();
        [impl_->streamStateLock unlock]; // Unlock after accessing stream state


        // --- Step 3: Assemble Context (MODIFIED) ---
        core::Context context;
        try {
            // Corrected type based on ConversationManager definition
            std::vector<core::ApiChatMessage> history = conversationManager_->getFormattedHistoryForContext(kMaxContextTokens);
            context.setChatHistory(history);
            DBM(@"Assembled context with %zu history messages (limit: %zu tokens).", history.size(), kMaxContextTokens);

            // Removed: System message is handled by ConversationManager or the backend itself
            // std::string systemMessage = conversationManager_->getSystemMessage();
            // if (!systemMessage.empty()) {
            //     context.setValue("system_message", systemMessage); // Incorrect Context method
            //     DBM(@"Added system message to context.");
            // }
        } catch (const std::exception& e) {
            ERM(@"Error assembling context: %s", e.what());
            // Update placeholder with context error
             [(ChatViewController*)impl_->chatViewController replaceMessageContent:impl_->activeAssistantMessageId
                                                               withNewContent:[NSString stringWithFormat:@"Context Error: %s", e.what()]
                                                                      newType:MessageType::ERROR];
            // Clean up UI state
            __weak MacOSChatUI* weakSelfForContextError = this; // Add weak self capture
            if (impl_->windowController) {
                dispatch_async(dispatch_get_main_queue(), ^{
                    MacOSChatUI* strongSelfForContextError = weakSelfForContextError; // Add strong self reference
                    if (!strongSelfForContextError || !strongSelfForContextError->impl_->windowController) return; // Check validity
                    TopChatWindowController* controller = (TopChatWindowController*)strongSelfForContextError->impl_->windowController;
                    // controller.composerView.textView.editable = YES; // REMOVE THIS LINE
                    [controller.contentVC.composerView hideStopButton];
                 });
            }
            // Reset stream state
            activeStreamToken_.reset();
            impl_->activeAssistantMessageId = nil;
            return; // Stop processing
        }
        // --- End Assemble Context ---


        // 4. Define Callbacks
        __weak MacOSChatUI* weakSelfForCallbacks = this; // Renamed to avoid conflict with earlier declaration
        __weak TopChatWindowController* weakController = (TopChatWindowController*)impl_->windowController; // Capture weak controller

        // ---> Reset rate-limiting state for new stream <--- 
        impl_->flushPending = false;
        impl_->lastFlushTime = 0;
        // ---> End Reset <--- 

        // Clear the C++ buffer for the new stream
        streaming_content_buffer_.str("");

        // Define dataCallback with proper weak/strong self pattern
        auto dataCallback = [weakSelfForCallbacks, cppAssistantMessageId]
                           (const std::string& chunk) {
            // Processing chunks can happen on a background thread
            
            // Create strong self for initial processing
            MacOSChatUI* strongSelf = weakSelfForCallbacks;
            if (!strongSelf) {
                DBG("dataCallback: self deallocated, cannot process chunk");
                return;
            }
            
            // Append to buffer or process thought
            const std::string kThoughtPrefix = "THOUGHT:";
            if (chunk.rfind(kThoughtPrefix, 0) == 0) {
                std::string thoughtText = chunk.substr(kThoughtPrefix.size());
                // Post notification with thoughtText for interested views (overlay etc.)
                NSDictionary *userInfo = @{ @"kind": @"ReasoningDelta",
                                            @"text": [NSString stringWithUTF8String:thoughtText.c_str()] };
                [[NSNotificationCenter defaultCenter] postNotificationName:@"KaiCapabilityEvent" object:nil userInfo:userInfo];
            } else {
                strongSelf->streaming_content_buffer_ << chunk;
            }
            
            // Create weak reference for inner dispatch
            __weak MacOSChatUI* weakSelfForDispatch = weakSelfForCallbacks;
            
            // Schedule a flush operation with rate limiting
            dispatch_async(dispatch_get_main_queue(), ^{
                // Re-create strong reference on main thread
                MacOSChatUI* strongSelfForDispatch = weakSelfForDispatch;
                if (!strongSelfForDispatch) {
                    DBG("Inner dispatch: self deallocated, cannot flush");
                    return;
                }
                
                // Schedule flush with strong reference
                strongSelfForDispatch->maybeScheduleFlush();
            });
        };

        auto completionCallback = [weakSelfForCallbacks, cppAssistantMessageId] // Capture C++ string
                                  (const core::BackendErrorInfo& error) {
             // Create local copies of all data before dispatch
             std::string msgIdCopy = cppAssistantMessageId; // Copy the string
             core::BackendErrorInfo errorCopy = error;      // Copy the error info
             
             // Use ObjC block syntax instead of C++ lambda capture for better memory management
             dispatch_async(dispatch_get_main_queue(), ^{
                 // Recreate the strong reference inside main thread block
                 MacOSChatUI* strongSelf = weakSelfForCallbacks;
                 if (!strongSelf) {
                     DBG("Completion callback cannot proceed - self deallocated");
                     return;
                 }
 
                  // --- Check if this callback corresponds to the active stream (under lock) ---
                  BOOL isActiveStream = NO;
                  NSString* currentActiveId = nil;
                  [strongSelf->impl_->streamStateLock lock];
                  currentActiveId = strongSelf->impl_->activeAssistantMessageId; // Get current ObjC ID
                  // Safely convert current ObjC ID to C++ string for comparison
                  std::string currentActiveCppId = currentActiveId ? [currentActiveId UTF8String] : ""; 
                  isActiveStream = (msgIdCopy == currentActiveCppId);
                  [strongSelf->impl_->streamStateLock unlock];
                  
                  if (!isActiveStream) {
                      DBM(@"Completion callback for C++ ID %s ignored; current active stream is different.", msgIdCopy.c_str());
                      return; // Not the active stream anymore
                  }
                  // --- End check ---
 
                  // ---> Reset pending flush flag before final flush <--- 
                  strongSelf->impl_->flushPending = false;
                  // --- End Reset <--- 
 
                  // --- Force a final flush for any remaining content ---
                  strongSelf->flushStreamingBuffer();
                  // --- End final flush ---
 
                  // UI updates for generation end
                  if (strongSelf->impl_->windowController) {
                      TopChatWindowController* controller = (TopChatWindowController*)strongSelf->impl_->windowController; // Cast needed
                      [controller.contentVC.composerView hideStopButton];
                  }
 
                  if (errorCopy.code != core::BackendErrorCode::NONE) {
                      ERM(@"Stream completed with error: %s (Code: %d)",
                           errorCopy.message.c_str(), static_cast<int>(errorCopy.code));
                      NSString* errorStr = [NSString stringWithFormat:@"Error: %s", errorCopy.message.c_str()];
                      if (strongSelf->impl_->chatViewController) {
                          // Convert C++ ID back to NSString* for UI update on error
                          NSString* objcAssistantMessageId = [NSString stringWithUTF8String:msgIdCopy.c_str()];
                          [(ChatViewController*)strongSelf->impl_->chatViewController
                              replaceMessageContent:objcAssistantMessageId // Use converted ID
                                     withNewContent:errorStr
                                            newType:MessageType::ERROR];
                      }
                      // Do NOT add assistant message to history on error
                  } else {
                     //  DBM(@"Stream completed successfully.");
                      // --- Add final assistant message to history ---
                      if (strongSelf->conversationManager_) {
                          // Use the final content from the C++ buffer
                          std::string finalCppContent = strongSelf->streaming_content_buffer_.str();
                          strongSelf->conversationManager_->addMessageShared(
                              "assistant",
                              std::make_shared<std::string>(finalCppContent),
                              strongSelf->currentProvider_,
                              strongSelf->currentModel_
                          );
                         //   DBM(@"Added successful assistant response to history.");
                      }
                      // --- End Add to history ---
 
                      if (strongSelf->impl_->chatViewController) {
                          // Convert C++ ID back to NSString* for UI update
                          NSString* objcAssistantMessageId = [NSString stringWithUTF8String:msgIdCopy.c_str()];
                          [(ChatViewController*)strongSelf->impl_->chatViewController
                              finalizeMessage:objcAssistantMessageId];
                      }
                  }
 
                  // Reset stream state (protected by lock)
                  [strongSelf->impl_->streamStateLock lock];
                  strongSelf->activeStreamToken_.reset();
                  strongSelf->impl_->activeAssistantMessageId = nil;
                  [strongSelf->impl_->streamStateLock unlock];
                  
                  // Clear C++ buffer
                  strongSelf->streaming_content_buffer_.str("");
                  strongSelf->streaming_content_buffer_.clear();
             });
        };

        // 5. Call Backend
        try {
            // Pass the assembled context
            backend_->sendMessageStream(std::string(cppUserMessage_sv), context, activeStreamToken_,
                                       currentProvider_,
                                       currentModel_,
                                       std::move(dataCallback),
                                       std::move(completionCallback));  // Pass std::string temp for now
        } catch (const std::exception& e) {
              ERM(@"Exception calling backend_->sendMessageStream: %s", e.what());
              __weak MacOSChatUI* weakSelfForCatch = this; // Add weak self for catch block
               NSString* errorStr = [NSString stringWithFormat:@"Send Error: %s", e.what()];
               if (impl_->chatViewController) {
                    [(ChatViewController*)impl_->chatViewController replaceMessageContent:impl_->activeAssistantMessageId
                                                                   withNewContent:errorStr
                                                                          newType:MessageType::ERROR];
               }
               if (impl_->windowController) {
                   dispatch_async(dispatch_get_main_queue(), ^{
                       MacOSChatUI* strongSelfForCatch = weakSelfForCatch; // Add strong self
                       if (!strongSelfForCatch || !strongSelfForCatch->impl_->windowController) return; // Check validity
                       TopChatWindowController* controller = (TopChatWindowController*)strongSelfForCatch->impl_->windowController; // Cast needed
                       [controller.contentVC.composerView hideStopButton];
                   });
               }
               // Reset stream state on immediate error
               activeStreamToken_.reset();
               impl_->activeAssistantMessageId = nil;
         }
    }
}

// --- Add cancelCurrentStream Implementation ---
void MacOSChatUI::cancelCurrentStream() {
    DBG("Cancelling current stream"); // Adjusted log format

    @autoreleasepool {
        __weak TopChatWindowController* weakController = (TopChatWindowController*)impl_->windowController; // Capture weak controller // Cast needed
        bool hasCancelled = false;
        NSString* messageIdToUpdate = nil;

        // Check if we have an active stream token (protected by lock)
        [impl_->streamStateLock lock];
        if (activeStreamToken_) {
            // Request cancellation
            DBG("Requesting cancellation via token");
            activeStreamToken_->cancel();
            hasCancelled = true;
        }
        // Capture the message ID before potentially resetting it
        messageIdToUpdate = impl_->activeAssistantMessageId;
        [impl_->streamStateLock unlock];
        
        // Convert ID to C++ string for capture if it exists
        std::string cppMessageIdToUpdate = messageIdToUpdate ? [messageIdToUpdate UTF8String] : "";

        // --- Flush any pending content --- 
        __weak MacOSChatUI* weakSelfForCancel = this; // Add weak self capture
        dispatch_async(dispatch_get_main_queue(), [weakSelfForCancel, hasCancelled, cppMessageIdToUpdate](){
            MacOSChatUI* strongSelfForCancel = weakSelfForCancel; // Add strong self reference
            if (!strongSelfForCancel) return; // Check validity

            // ---> Reset pending flush flag before final flush <--- 
            strongSelfForCancel->impl_->flushPending = false;
            // --- End Reset <--- 
            
            // Perform a final flush
            strongSelfForCancel->flushStreamingBuffer();
            
            // Hide the stop button and re-enable the text view
            if (strongSelfForCancel->impl_->windowController) {
                TopChatWindowController* controller = (TopChatWindowController*)strongSelfForCancel->impl_->windowController; // Cast needed
                [controller.contentVC.composerView hideStopButton]; // Hide stop button
            }
            
            // Check if we have an active message ID and update its UI immediately
            if (!cppMessageIdToUpdate.empty() && strongSelfForCancel->impl_->chatViewController) {
                // Convert C++ ID back to NSString* for UI update
                NSString* objcMessageIdToUpdate = [NSString stringWithUTF8String:cppMessageIdToUpdate.c_str()];
                if (hasCancelled) {
                    NSString* cancelledMessage = @"Generation stopped.";
                    ChatViewController* cvc = (ChatViewController*)strongSelfForCancel->impl_->chatViewController;
                    [cvc replaceMessageContent:objcMessageIdToUpdate 
                             withNewContent:cancelledMessage
                                    newType:MessageType::TEXT];
                    [cvc finalizeMessage:objcMessageIdToUpdate];
                }
            }
            
            // Reset stream state (protected by lock)
            [strongSelfForCancel->impl_->streamStateLock lock];
            strongSelfForCancel->activeStreamToken_.reset();
            strongSelfForCancel->impl_->activeAssistantMessageId = nil;
            [strongSelfForCancel->impl_->streamStateLock unlock];
        });
    }
}

// --- Implementation of maybeScheduleFlush method ---
void MacOSChatUI::maybeScheduleFlush() {
    // Ensure we're on the main thread for UI updates
    if (![NSThread isMainThread]) {
        __weak MacOSChatUI* weakSelfForDispatch = this; // Add weak self capture
        dispatch_async(dispatch_get_main_queue(), ^{
            MacOSChatUI* strongSelfForDispatch = weakSelfForDispatch; // Add strong self reference
            if (strongSelfForDispatch) { // Check validity
                strongSelfForDispatch->maybeScheduleFlush();
            }
        });
        return;
    }

    // If a flush is already pending, don't schedule another one
    if (impl_->flushPending) {
        return;
    }
    
    // Get current time
    dispatch_time_t now = dispatch_time(DISPATCH_TIME_NOW, 0);
    
    // Calculate time since last flush
    uint64_t time_since_last_flush = (impl_->lastFlushTime == 0) ? 
                                     kFlushIntervalNanos + 1 : // Force immediate flush if first time
                                     now - impl_->lastFlushTime;
    
    // If enough time has passed, flush immediately
    if (time_since_last_flush >= kFlushIntervalNanos) {
        flushStreamingBuffer();
        // Update last flush time
        impl_->lastFlushTime = dispatch_time(DISPATCH_TIME_NOW, 0);
    }
    // Otherwise, schedule a deferred flush
    else {
        // Calculate time to wait
        uint64_t wait_time = kFlushIntervalNanos - time_since_last_flush;
        
        // Mark flush as pending
        impl_->flushPending = true;
        
        // Schedule the flush after wait_time
        __weak MacOSChatUI* weakSelfForAfter = this; // Renamed to avoid potential conflicts
        
        // Use ObjC block syntax instead of C++ lambda capture for better memory management
        dispatch_block_t flushBlock = ^{
             MacOSChatUI* strongSelf = weakSelfForAfter;
             if (!strongSelf) {
                 DBG("dispatch_after: strongSelf is nil, cannot flush.");
                 return;
             }
             
             // ---> RE-CHECK if flush is still pending <--- 
             if (!strongSelf->impl_->flushPending) {
                 DBG("dispatch_after: Flush no longer pending. Exiting.");
                 return; // Exit if flag was reset by completion/cancellation/new stream
             }
             
             // DBG("dispatch_after: strongSelf is valid ("<< strongSelf << "), proceeding with flush.");
             // Reset pending flag *before* flushing
             strongSelf->impl_->flushPending = false;
             // Update last flush time
             strongSelf->impl_->lastFlushTime = dispatch_time(DISPATCH_TIME_NOW, 0);
             // Do the flush
             strongSelf->flushStreamingBuffer();
         };
         
         // Schedule the block to run after wait_time
         dispatch_after(dispatch_time(DISPATCH_TIME_NOW, wait_time), dispatch_get_main_queue(), flushBlock);
    }
}

// --- New flushStreamingBuffer Implementation ---
void MacOSChatUI::flushStreamingBuffer() {
    // Ensure we're on the main thread
    if (![NSThread isMainThread]) {
        __weak MacOSChatUI* weakSelfForFlush = this; // Add weak self capture
        // DBG("flushStreamingBuffer: Dispatching to main thread.");
        
        // Create a block variable for better memory management
        dispatch_block_t flushBlock = ^{
            // Create strong reference inside the block
            MacOSChatUI* strongSelfForFlush = weakSelfForFlush;
            if (!strongSelfForFlush) {
                DBG("flushStreamingBuffer: self deallocated, cannot flush on main thread");
                return;
            }
            
            // Call the method on the main thread
            strongSelfForFlush->flushStreamingBuffer();
        };
        
        // Dispatch the block to the main queue
        dispatch_async(dispatch_get_main_queue(), flushBlock);
        return;
    }
    
    // DBG("flushStreamingBuffer: Executing on main thread.");
    
    // Create local copies of pointers with safety checks
    ChatViewController* chatVC = (ChatViewController*)impl_->chatViewController;
    
    // --- Get messageId under lock ---
    NSString* messageId = nil;
    [impl_->streamStateLock lock];
    messageId = impl_->activeAssistantMessageId;
    [impl_->streamStateLock unlock];
    // --- End get messageId under lock ---
    
    // --- Add detailed validity checks ---
    if (!chatVC) {
        DBG("flushStreamingBuffer: chatVC is nil. Aborting flush.");
        return;
    }
    // Check messageId AFTER getting it from lock
    if (!messageId) {
        DBG("flushStreamingBuffer: messageId is nil (likely stream ended). Aborting flush.");
        return;
    }
    // --- End detailed validity checks ---
    
    // DBG("flushStreamingBuffer: chatVC (%p) and messageId (%@) are valid.", (void*)chatVC, messageId);
    
    // Get content from buffer
    std::string content_str;
    try {
        content_str = streaming_content_buffer_.str();
        // DBG("flushStreamingBuffer: Successfully read buffer content (length: %zu)", content_str.length());
    } catch (const std::exception& e) {
        ERR("flushStreamingBuffer: Exception reading buffer: " << e.what());
        return; // Don't proceed if buffer read failed
    } catch (...) {
        ERR("flushStreamingBuffer: Unknown exception reading buffer.");
        return; // Don't proceed if buffer read failed
    }
    
    if (!content_str.empty()) {
        // Convert to NSString
        NSString* contentToUpdate = nil;
        try {
            contentToUpdate = [NSString stringWithUTF8String:content_str.c_str()];
            if (!contentToUpdate) {
                ERR("flushStreamingBuffer: Failed to convert buffer content to NSString (returned nil).");
                return; // Don't proceed if conversion failed
            }
            // DBG("flushStreamingBuffer: Successfully converted buffer to NSString: %@", contentToUpdate);
        } catch (NSException* exception) {
            ERM(@"flushStreamingBuffer: NSException converting buffer to NSString: %@", exception);
            return; // Don't proceed on exception
        }
        
        // DBG("flushStreamingBuffer: Calling replaceMessageContent...");
        // Update the UI
        @try {
            [chatVC replaceMessageContent:messageId
                           withNewContent:contentToUpdate
                                  newType:MessageType::TEXT];
            // DBG("flushStreamingBuffer: replaceMessageContent call completed.");
        } @catch (NSException* exception) {
            ERM(@"flushStreamingBuffer: NSException during replaceMessageContent: %@", exception);
            // Potentially log more details or rethrow depending on desired behavior
        }
    }
}

NSWindow* MacOSChatUI::getNativeWindow() {
    return (NSWindow*)impl_->window;
}

void MacOSChatUI::loadConversationFromJson(const nlohmann::json &j) {
    if (!conversationManager_) { return; }

    // 1. Load into the ConversationManager (history only in C++ world).
    conversationManager_->loadFromJson(j);

    // 2. Reflect the loaded history into the visible Objective-C chat view.
    if (!impl_->chatViewController) { return; }

    const std::vector<core::ApiChatMessage> history = conversationManager_->getHistory();

    if (history.empty()) { return; }

    // Build Message* objects for UI – we avoid addMessage() to prevent re-persisting.
    NSMutableArray<Message*> *uiMessages = [NSMutableArray arrayWithCapacity:history.size()];

    for (const auto &msg : history) {
        // Map role ➜ friendly name via central helper, and set type
        MessageType type = (msg.role == "system") ? MessageType::SYSTEM : MessageType::TEXT;

        // Use the canonical helper from ApiChatMessage for consistent naming
        NSString *senderName = [NSString stringWithUTF8String:msg.displayName().c_str()];

        Message *m = [[Message alloc] initWithContent:[NSString stringWithUTF8String:msg.contentRef().c_str()]
                                              senderId:[NSString stringWithUTF8String:msg.role.c_str()]
                                            senderName:senderName
                                                  type:type];
        [uiMessages addObject:m];
    }

    // Ensure UI work happens on main thread.
    dispatch_async(dispatch_get_main_queue(), ^{
        ChatViewController *cvc = (ChatViewController*)impl_->chatViewController;
        [cvc addMessages:uiMessages];
    });

    // -------------------- Metadata restore --------------------
    if (j.contains("meta")) {
        const auto &meta = j["meta"];

        NSString *iconSym = nil;
        NSColor  *tintClr = nil;
        NSString *genTitle = nil;

        if (meta.contains("icon_symbol") && meta["icon_symbol"].is_string()) {
            std::string sym = meta["icon_symbol"].get<std::string>();
            iconSym = [NSString stringWithUTF8String:sym.c_str()];
        }

        if (meta.contains("tint") && meta["tint"].is_array() && meta["tint"].size() == 4) {
            double r = meta["tint"][0].get<double>();
            double g = meta["tint"][1].get<double>();
            double b = meta["tint"][2].get<double>();
            double a = meta["tint"][3].get<double>();
            tintClr = [NSColor colorWithSRGBRed:r green:g blue:b alpha:a];
        }

        if (meta.contains("title") && meta["title"].is_string()) {
            std::string t = meta["title"].get<std::string>();
            genTitle = [NSString stringWithUTF8String:t.c_str()];
        }

        // Apply to SessionMetadataManager
        if (impl_->window) {
            launcher::ui::SessionMetadataManager::shared().applyMetadataForWindow((NSWindow *)impl_->window, iconSym, tintClr, genTitle);
        }
    }
    // -----------------------------------------------------------
 }

// -----------------------------------------------------------
// Active model setter
void MacOSChatUI::setActiveModel(const std::string &provider, const std::string &model) {
    currentProvider_ = provider;
    currentModel_    = model;

    // Persist as new defaults via injected configuration service (debounced save)
    try {
        launcher::core::IConfigManager *cfgPtr = nullptr;
        AppDelegate *appDel = (AppDelegate *)[[NSApplication sharedApplication] delegate];
        if (appDel && appDel.ctx && appDel.ctx->configManager) {
            cfgPtr = appDel.ctx->configManager.get();
        } else {
            cfgPtr = &ConfigService();
        }
        if (cfgPtr) {
            cfgPtr->setProviderDefaultModel(provider, model);
            cfgPtr->setString("default_ai_provider", provider);
            cfgPtr->requestSave();
        }
    } catch (...) {
        // Config not ready; ignore
    }
}
} // namespace ui
} // namespace launcher