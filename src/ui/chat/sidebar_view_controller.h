#import <Cocoa/Cocoa.h>

NS_ASSUME_NONNULL_BEGIN

@class SidebarItem;

@protocol SidebarViewControllerDelegate <NSObject>
- (void)sidebarViewController:(id)controller didSelectWindow:(NSWindow *)window;
@end

@interface SidebarViewController : NSViewController <NSOutlineViewDataSource, NSOutlineViewDelegate, NSTextFieldDelegate>

@property (nonatomic, weak) id<SidebarViewControllerDelegate> delegate;

/// Read-only access to the underlying outline view so external code can, for example, make it first-responder.
@property (nonatomic, readonly) NSOutlineView *outlineView;

@end

NS_ASSUME_NONNULL_END 