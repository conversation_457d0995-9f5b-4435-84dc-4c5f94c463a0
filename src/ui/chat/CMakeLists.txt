# UI chat library
add_library(ui_chat STATIC
    chat_ui_interface.cpp
    macos_chat_ui.mm
    sidebar_view_controller.mm
    sidebar_item.mm
    sidebar_cell_view.mm
    chat_content_view_controller.mm
    chat_root_view_controller.mm
    sidebar_manager.mm
    top_chat_window_controller.mm
    tabs/tab_group_manager.mm
    mention/MentionPopoverController.mm
    mention/MentionViewController.mm
    mention/MentionSuggestionCellView.mm
    model/MentionSuggestion.mm
    model/BackendError.mm
    cache/CacheManager.mm
    session_autosave_manager.mm
    session_metadata_manager.mm
    sidebar_symbol_pool.mm
    chat_window_factory.mm
    ../common/icon_utils.mm
    ModelAppearanceFactory.mm
    ComposerView.mm
    CapabilityIconProvider.mm
)

# Include directories
target_include_directories(ui_chat
    PUBLIC
        ${CMAKE_CURRENT_SOURCE_DIR}
)

# Link with dependencies
target_link_libraries(ui_chat
    PRIVATE
        core
)

# Platform-specific configurations
if(APPLE)
    find_library(COCOA_FRAMEWORK Cocoa REQUIRED)
    find_library(FOUNDATION_FRAMEWORK Foundation REQUIRED)
    find_library(APPKIT_FRAMEWORK AppKit REQUIRED)
    
    target_link_libraries(ui_chat
        PRIVATE
            ${COCOA_FRAMEWORK}
            ${FOUNDATION_FRAMEWORK}
            ${APPKIT_FRAMEWORK}
    )
endif()

# Add platform-specific sources
target_sources(ui_chat
    PRIVATE
        ${PLATFORM_SOURCES}
) 