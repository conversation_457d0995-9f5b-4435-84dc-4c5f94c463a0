#pragma once

#include <memory>
#include <string>
#include <vector>
#include <sstream> // Added for ostringstream
#include <dispatch/dispatch.h> // Added for dispatch_source_t

// --- Removed direct Foundation import ---
// #import <Foundation/Foundation.h> // Removed: Avoid ObjC headers in C++ headers

#include "chat_ui_interface.h"
#include "../../core/include/chat_backend_interface.h" // Needed for IChatBackend
#include "../../core/chat/conversation_manager.h" // Include definition for unique_ptr

namespace core {
class ConversationManager;
}

// Forward declarations for Objective-C classes & types
#ifdef __OBJC__
@class TopChatWindowController;
@class NSWindow;
@class ChatViewController;
@class MentionSuggestion;
@class BackendError;
@class NSString;
@class NSNumber;
@class NSArray; // Needed for the block definition
@class NSMapTable; // For token management
@class NSValue; // For storing C++ shared_ptr

// Define the completion block type only for Objective-C++ context
typedef void (^MentionCompletionBlock)(NSArray<MentionSuggestion *> * _Nullable suggestions, BackendError * _Nullable error);
typedef void (^HistoryCompletionBlock)(NSArray<NSString *> * _Nullable history, BackendError * _Nullable error);
typedef void (^LLMSuggestionCompletionBlock)(NSArray<NSString *> * _Nullable suggestions, BackendError * _Nullable error);

#else
// C++ forward declarations/opaque pointers for Objective-C classes
class TopChatWindowController;
class NSWindow;
class ChatViewController;
class MentionSuggestion;
class BackendError;
class NSString;
class NSNumber;
class NSMapTable;
class NSValue;

// In pure C++ context we don't evaluate Objective-C blocks. Create
// forward-declared placeholder classes so pointer types stay strongly
// typed without resorting to raw void*.
namespace launcher { namespace ui {
class MentionCompletionBlockHolder;   // opaque
class HistoryCompletionBlockHolder;   // opaque
class LLMSuggestionCompletionBlockHolder; // opaque
} }  // namespace launcher::ui

using MentionCompletionBlock = launcher::ui::MentionCompletionBlockHolder*;
using HistoryCompletionBlock = launcher::ui::HistoryCompletionBlockHolder*;
using LLMSuggestionCompletionBlock = launcher::ui::LLMSuggestionCompletionBlockHolder*;

#endif

// --- End type definition changes ---

namespace launcher {
namespace ui {

/**
 * @brief macOS implementation of the Chat UI
 */
class MacOSChatUI : public ChatUIInterface {
 public:
    MacOSChatUI();
    ~MacOSChatUI() override;

    // Initialize the UI
    bool initialize(SendMessageCallback sendCallback = nullptr,
                    ReceiveMessageCallback receiveCallback = nullptr) override;

    // Show the UI
    void show() override;

    // Hide the UI
    void hide() override;

    // Add a new message to the chat
    bool addMessage(const std::string& senderId, const std::string& senderName,
                    const std::string& content) override;

    // Clear all messages from the chat
    bool clearMessages() override;

    // Run the UI
    int run() override;

    // Helper to add test messages
    void addTestMessages();

    // --- Method declarations using forward-declared types ---
    /**
     * @brief Initiates an asynchronous search for mentionable items.
     *
     * Called from Objective-C++ layer (ChatWindowController) to trigger a search.
     * This implementation forwards to a core C++ backend component, using a 
     * CancellationToken mapped to the provided request identifier.
     *
     * @param query The search query string (passed as NSString*).
     * @param requestIdentifier A unique identifier for this specific search request (passed as NSNumber*).
     * @param completion The Objective-C block to call when search completes or fails.
     */
    void searchMentionableItemsWithQuery(NSString *query, // Use forward-declared type
                                       NSNumber *requestIdentifier, // Use forward-declared type
                                       MentionCompletionBlock completion);
    
    /**
     * @brief Searches through message history for items matching the prefix.
     *
     * @param prefix The prefix to search for (NSString*).
     * @param requestIdentifier A unique identifier for this specific request (NSNumber*).
     * @param completion The block to call when the search completes or fails.
     */
    void searchHistoryWithPrefix(NSString *prefix,
                                NSNumber *requestIdentifier,
                                HistoryCompletionBlock completion);
    
    /**
     * @brief Requests LLM-based suggestions based on the provided context.
     *
     * @param context The context text to generate suggestions for (NSString*).
     * @param requestIdentifier A unique identifier for this specific request (NSNumber*).
     * @param completion The block to call when suggestions are ready or on failure.
     */
    void requestLLMSuggestionsForContext(NSString *context,
                                        NSNumber *requestIdentifier,
                                        LLMSuggestionCompletionBlock completion);

    /**
     * @brief Cancels an ongoing mention search request.
     *
     * Called from Objective-C++ layer (ChatWindowController) if the mention operation
     * is cancelled before the search completes (e.g., user deletes trigger character).
     *
     * @param requestIdentifier The unique identifier of the request to cancel (passed as NSNumber*).
     */
    void cancelRequestWithIdentifier(NSNumber *requestIdentifier); // Use forward-declared type
    
    /**
     * @brief Cancels all pending requests and cleans up tokens.
     * This is called during shutdown or when cleaning up resources.
     */
    void cancelAllPendingRequests();
    // --- End Method declarations ---

    /**
     * @brief Initiates streaming for a user message.
     *
     * Creates a placeholder message in the UI, starts a backend stream request,
     * and sets up the appropriate callbacks to update the UI as content streams in.
     * 
     * @param userMessageText The user message text to send to the backend.
     */
    void initiateStreamForMessage(NSString* userMessageText);
    
    /**
     * @brief Cancels the current streaming response if one is in progress.
     * 
     * This will cancel the active stream token and update the UI to indicate
     * that generation was cancelled.
     */
    void cancelCurrentStream();

    // --- NEW: Native window accessor ---
#ifdef __OBJC__
    /**
     * @brief Returns the underlying NSWindow for embedding or tabbing.
     *        In pure C++ translation units NSWindow is forward-declared;
     *        the pointer remains opaque there but has the correct type
     *        for Objective-C++ implementation files.
     */
    NSWindow *getNativeWindow();
#else
    void *getNativeWindow();
#endif
    // --- END accessor ---

    // --- NEW: Returns conversation id ---
    const std::string &conversationId() const {
        return conversationManager_ ? conversationManager_->getConversationId() : kEmptyId_;
    }

    void loadConversationFromJson(const nlohmann::json &j);

    // --- NEW: Returns raw pointer to conversation manager ---
    launcher::core::ConversationManager *conversationManager() const {
        return conversationManager_.get();
    }

    // --- Active LLM selection (defaults come from ConfigManager) ---
    std::string currentProvider_ = "openai";
    std::string currentModel_    = "gpt-3.5-turbo";

    void setActiveModel(const std::string &provider, const std::string &model);
    // --- End Active LLM selection ---

 private:
    // ---------------------------------------------------------------------
    // Platform/UI-specific state is hidden behind a PIMPL to avoid including
    // Objective-C types in the public header and to remove the previous
    // `void*` placeholders.  Business-logic members (backend, conversation
    // manager, streaming buffer) remain directly in the class for now.
    // ---------------------------------------------------------------------

    struct Impl;
    std::unique_ptr<Impl> impl_;

    // ---------------- Core-agnostic members ----------------

    // C++ backend instance
    std::unique_ptr<core::IChatBackend> backend_;

    // Streaming state
    std::shared_ptr<core::utilities::CancellationToken> activeStreamToken_ = nullptr;

    // Callbacks
    SendMessageCallback sendCallback_;
    ReceiveMessageCallback receiveCallback_;

    // Conversation manager
    std::unique_ptr<core::ConversationManager> conversationManager_;

    // Streaming buffer + basic rate-limiting (non-platform specific)
    std::ostringstream streaming_content_buffer_;
    void flushStreamingBuffer();
    void maybeScheduleFlush();

    // Flush interval (nanoseconds) – 150 ms
    static constexpr uint64_t kFlushIntervalNanos = 150ULL * 1000000ULL;

    static inline const std::string kEmptyId_{};
};

// Factory function to create Chat UI
std::shared_ptr<ChatUIInterface> createChatUI();

}  // namespace ui
}  // namespace launcher