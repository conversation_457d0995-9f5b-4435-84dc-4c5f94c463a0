#import "ModelAppearanceFactory.h"
#import <CommonCrypto/CommonCrypto.h>
#include "../../core/llm/capability_heuristics.h"
#include "../../core/llm/capability.h"
#include <vector>

using launcher::core::Capability;
using launcher::core::CapabilitySet;

// Deferred construction of heavy objects using dispatch_once ensures
// low-cost startup while remaining thread-safe.
@implementation ModelAppearanceFactory

#pragma mark - Public API

+ (NSColor *)colorForModel:(NSString *)modelName {
    if (modelName.length == 0) {
        // Fallback to system accent colour when name is missing.
        return [NSColor controlAccentColor];
    }

    static NSCache<NSString *, NSColor *> *colorCache = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        colorCache = [[NSCache alloc] init];
        colorCache.countLimit = 512; // Arbitrary bound – colours are cheap.
    });

    NSColor *cached = [colorCache objectForKey:modelName];
    if (cached) return cached;

    unsigned char digest[CC_SHA256_DIGEST_LENGTH];
    [self.class computeDigestForString:modelName output:digest];

    // Map first four bytes to H, S, B within visually pleasant ranges.
    uint16_t hue16 = ((uint16_t)digest[0] << 8) | digest[1];
    double hue = (double)hue16 / 65535.0; // 0.0–1.0

    double sat = 0.55 + ((double)digest[2] / 255.0) * 0.20; // 0.55 – 0.75
    double bri = 0.65 + ((double)digest[3] / 255.0) * 0.20; // 0.65 – 0.85

    NSColor *colour = [NSColor colorWithHue:hue
                                  saturation:sat
                                  brightness:bri
                                       alpha:1.0];
    [colorCache setObject:colour forKey:modelName];
    return colour;
}

+ (NSString *)symbolForModel:(NSString *)modelName {
    if (modelName.length == 0) {
        return @"person.circle"; // sensible default
    }

    static NSArray<NSString *> *symbolPool = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        symbolPool = @[
            // Curated list of SF Symbols that render well at avatar size.
            // macOS 11+ availability assumed.
            @"bolt.circle", @"brain.head.profile", @"globe", @"hare", @"tortoise",
            @"ant", @"ladybug", @"leaf", @"cloud", @"sun.max", @"moon", @"star",
            @"heart", @"paperplane", @"pencil", @"book", @"bookmark", @"flag",
            @"flame", @"bolt", @"snow", @"drop", @"wave.3.forward", @"music.note",
            @"film", @"camera", @"paintbrush", @"scissors", @"wand.and.stars",
            @"gamecontroller", @"headphones", @"mic", @"speaker.wave.2", @"keyboard",
            @"laptopcomputer", @"desktopcomputer", @"terminal", @"tv", @"iphone",
            @"ipad", @"watch", @"airpods", @"car", @"bicycle", @"tram", @"bus",
            @"airplane", @"figure.walk", @"figure.wave", @"figure.stand", @"bed.double",
            @"cup.and.saucer", @"takeoutbag.and.cup.and.straw", @"cart", @"creditcard",
            @"gift", @"gamecontroller", @"dice", @"sparkles", @"balloon", @"graduationcap",
            @"globe.asia.australia", @"hammer", @"wrench", @"screwdriver", @"gearshape",
            @"shield", @"lock.shield", @"key", @"lightbulb", @"magnet", @"scope",
            @"speedometer", @"timer", @"trophy", @"medal", @"rosette", @"crown",
            @"wifi", @"antenna.radiowaves.left.and.right", @"map", @"paperclip", @"doc",
            @"folder", @"trash", @"archivebox", @"shippingbox", @"cube", @"giftcard",
            @"sparkler", @"megaphone", @"bell", @"pin", @"message", @"bubble.left",
            @"scribble", @"signature", @"questionmark.circle", @"exclamationmark.triangle"
        ];
    });

    unsigned char digest[CC_SHA256_DIGEST_LENGTH];
    [self.class computeDigestForString:modelName output:digest];

    uint16_t idx = ((uint16_t)digest[4] << 8) | digest[5];
    NSString *symbol = symbolPool[idx % symbolPool.count];
    return symbol;
}

+ (NSColor *)color:(NSColor *)color byAdjustingBrightness:(CGFloat)delta {
    CGFloat hue, sat, bri, alpha;
    [[color colorUsingColorSpace:[NSColorSpace deviceRGBColorSpace]] getHue:&hue
                                                               saturation:&sat
                                                               brightness:&bri
                                                                    alpha:&alpha];
    bri = MIN(MAX(bri + delta, 0.0), 1.0);
    return [NSColor colorWithHue:hue saturation:sat brightness:bri alpha:alpha];
}

#pragma mark - Helpers

+ (void)computeDigestForString:(NSString *)string output:(unsigned char[CC_SHA256_DIGEST_LENGTH])digest {
    NSData *data = [string dataUsingEncoding:NSUTF8StringEncoding];
    CC_SHA256(data.bytes, (CC_LONG)data.length, digest);
}

@end

#pragma mark - Capability-aware symbol helper

static NSString *SymbolNameForCapability(Capability cap) {
    switch (cap) {
        case Capability::ToolUse:           return @"wrench";            // 🛠️
        case Capability::ImageInput:        return @"photo";             // 🖼️
        case Capability::AudioInput:        return @"mic";               // 🎙️
        case Capability::ImageGeneration:   return @"paintbrush";        // 🎨
        case Capability::SpeechGeneration:  return @"speaker.wave.2";    // 🔊
        case Capability::LargeContext:      return @"book";              // 📖
        case Capability::MultimodalRealtime:return @"bolt.circle";       // ⚡️
        case Capability::HighReasoning:     return @"sparkles";          // ✨
        case Capability::ComputerUse:       return @"desktopcomputer";     // 🖥️
        default:                            return nil;
    }
}

// Ordered priority list matching capability bar order (most distinctive first)
static const std::vector<Capability> kPriority = {
    Capability::HighReasoning,
    Capability::MultimodalRealtime,
    Capability::ImageGeneration,
    Capability::SpeechGeneration,
    Capability::ImageInput,
    Capability::AudioInput,
    Capability::ToolUse,
    Capability::ComputerUse,
    Capability::LargeContext,
};

@implementation ModelAppearanceFactory (CapabilityAware)

+ (NSString *)symbolForProvider:(NSString *)provider model:(NSString *)modelName {
    if (modelName.length == 0) {
        // Fallback to provider name if model missing – keeps previous behaviour.
        return [self symbolForModel:provider];
    }

    // Build cache key to avoid repeated heuristic work.
    static NSCache<NSString *, NSString *> *cache = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        cache = [[NSCache alloc] init];
        cache.countLimit = 1024; // arbitrary – symbols are tiny strings
    });

    NSString *key = [NSString stringWithFormat:@"%@|%@", provider ?: @"", modelName];
    NSString *cached = [cache objectForKey:key];
    if (cached) return cached;

    // Infer capabilities (C++ helper).
    std::string provCpp = provider ? [provider UTF8String] : std::string();
    std::string modelCpp = [modelName UTF8String];
    CapabilitySet caps = launcher::core::inferCapabilities(provCpp, modelCpp);

    NSString *symbol = nil;
    for (Capability cap : kPriority) {
        if (caps.test(static_cast<size_t>(cap))) {
            // Skip baseline capabilities (text/json) – handled by priority list.
            symbol = SymbolNameForCapability(cap);
            if (symbol) break;
        }
    }

    if (!symbol) {
        // No distinctive capability – reuse hash-based method for variety.
        symbol = [self symbolForModel:modelName];
    }

    [cache setObject:symbol forKey:key];
    return symbol;
}

@end 