#import "MentionSuggestion.h"

@implementation MentionSuggestion

- (instancetype)initWithIdentifier:(NSString *)identifier
                       displayText:(NSString *)displayText
                     secondaryText:(nullable NSString *)secondaryText
                         avatarURL:(nullable NSURL *)avatarURL
{
    self = [super init];
    if (self) {
        // Use non-nullable parameters directly, copy string properties
        _identifier = [identifier copy];
        _displayText = [displayText copy];
        // Handle nullable properties
        _secondaryText = [secondaryText copy]; // Copy even if nil
        _avatarURL = avatarURL; // Strong reference for URL
    }
    return self;
}

// Optional: Implement description for easier debugging
- (NSString *)description {
    return [NSString stringWithFormat:@"<MentionSuggestion: %p; id='%@', text='%@', secondary='%@'>",
            self,
            self.identifier,
            self.displayText,
            self.secondaryText ?: @"(null)"];
}

// Optional: Implement isEqual: and hash if needed for collection management
- (BOOL)isEqual:(id)object {
    if (self == object) {
        return YES;
    }
    if (![object isKindOfClass:[MentionSuggestion class]]) {
        return NO;
    }
    MentionSuggestion *other = (MentionSuggestion *)object;
    // Primarily compare by identifier, but consider other fields if needed
    return [self.identifier isEqualToString:other.identifier];
}

- (NSUInteger)hash {
    // Primarily hash based on identifier
    return [self.identifier hash];
}

@end 