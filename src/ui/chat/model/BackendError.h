// src/ui/chat/model/BackendError.h

#import <Foundation/Foundation.h>
#include "../../../core/include/chat_backend_interface.h" // Include C++ enum definition

NS_ASSUME_NONNULL_BEGIN

/**
 * Represents an error returned from the backend operations (mentions, history, LLM).
 * Wraps the C++ BackendErrorInfo struct.
 */
@interface BackendError : NSError

/** The specific error code from the backend. */
@property (nonatomic, assign, readonly) launcher::core::BackendErrorCode cppErrorCode;

/** A descriptive error message. */
@property (nonatomic, copy, readonly) NSString *displayMessage;

/**
 * Creates a BackendError instance.
 *
 * @param code The C++ BackendErrorCode enum value.
 * @param message The user-facing error message.
 * @param underlyingError Optional underlying NSError that caused this error.
 * @return An initialized BackendError object.
 */
+ (instancetype)errorWithCode:(launcher::core::BackendErrorCode)code
                      message:(NSString *)message
              underlyingError:(nullable NSError *)underlyingError;

/**
 * Initializes a BackendError instance.
 *
 * @param code The C++ BackendErrorCode enum value.
 * @param message The user-facing error message.
 * @param underlyingError Optional underlying NSError that caused this error.
 * @return An initialized BackendError object.
 */
- (instancetype)initWithCode:(launcher::core::BackendErrorCode)code
                     message:(NSString *)message
             underlyingError:(nullable NSError *)underlyingError NS_DESIGNATED_INITIALIZER;

// Disable default initializers
- (instancetype)initWithDomain:(NSErrorDomain)domain code:(NSInteger)code userInfo:(nullable NSDictionary<NSErrorUserInfoKey, id> *)dict NS_UNAVAILABLE;
- (instancetype)init NS_UNAVAILABLE;
+ (instancetype)new NS_UNAVAILABLE;

@end

NS_ASSUME_NONNULL_END 