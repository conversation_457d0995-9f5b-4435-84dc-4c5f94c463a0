// src/ui/chat/model/MentionSuggestion.h

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

/**
 * Represents a suggestion item for @mentions.
 */
@interface MentionSuggestion : NSObject

/** A unique identifier for the mentionable item (e.g., user ID, group ID). */
@property (nonatomic, copy, readonly) NSString *identifier;

/** The primary text to display for the suggestion (e.g., username, full name). */
@property (nonatomic, copy, readonly) NSString *displayText;

/** Optional: Secondary text or description (e.g., full name if displayText is username, role). */
@property (nonatomic, copy, nullable, readonly) NSString *secondaryText;

/** Optional: URL to an avatar image for the suggestion. */
@property (nonatomic, strong, nullable, readonly) NSURL *avatarURL;

/**
 * Initializes a new mention suggestion.
 *
 * @param identifier The unique identifier.
 * @param displayText The primary display text.
 * @param secondaryText Optional secondary text.
 * @param avatarURL Optional URL for an avatar image.
 * @return An initialized MentionSuggestion object.
 */
- (instancetype)initWithIdentifier:(NSString *)identifier
                       displayText:(NSString *)displayText
                     secondaryText:(nullable NSString *)secondaryText
                         avatarURL:(nullable NSURL *)avatarURL NS_DESIGNATED_INITIALIZER;

// Disable default initializer
- (instancetype)init NS_UNAVAILABLE;

@end

NS_ASSUME_NONNULL_END 