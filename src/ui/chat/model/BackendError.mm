#import "BackendError.h"

// Define a custom error domain
static NSErrorDomain const kLauncherBackendErrorDomain = @"com.launcher.backend.error";

@implementation BackendError {
    launcher::core::BackendErrorCode _cppErrorCode;
}

+ (instancetype)errorWithCode:(launcher::core::BackendErrorCode)code
                      message:(NSString *)message
              underlyingError:(nullable NSError *)underlyingError {
    return [[self alloc] initWithCode:code message:message underlyingError:underlyingError];
}

- (instancetype)initWithCode:(launcher::core::BackendErrorCode)code
                     message:(NSString *)message
             underlyingError:(nullable NSError *)underlyingError {
    
    NSMutableDictionary<NSErrorUserInfoKey, id> *userInfo = [NSMutableDictionary dictionary];
    if (message) {
        userInfo[NSLocalizedDescriptionKey] = message;
    }
    if (underlyingError) {
        userInfo[NSUnderlyingErrorKey] = underlyingError;
    }
    
    // Use the C++ enum value directly as the integer code for the NSError
    self = [super initWithDomain:kLauncherBackendErrorDomain code:(NSInteger)code userInfo:[userInfo copy]];
    if (self) {
        _cppErrorCode = code; // Store the C++ enum value
        _displayMessage = [message copy]; // Store a separate copy if direct access is preferred
    }
    return self;
}

// Getter for the C++ error code
- (launcher::core::BackendErrorCode)cppErrorCode {
    return _cppErrorCode;
}

// Override localizedDescription to ensure it returns our message
- (NSString *)localizedDescription {
    return self.displayMessage ?: [super localizedDescription];
}

// Optional: Custom description for logging
- (NSString *)description {
    return [NSString stringWithFormat:@"<BackendError: %p; code=%ld (%@); message=\"%@\"; underlyingError=%@>",
            self,
            (long)self.code,
            [self stringFromBackendErrorCode:self.cppErrorCode], // Helper to convert enum to string
            self.displayMessage,
            self.userInfo[NSUnderlyingErrorKey]];
}

// Helper method to convert C++ enum to string (for description)
- (NSString *)stringFromBackendErrorCode:(launcher::core::BackendErrorCode)code {
    switch (code) {
        case launcher::core::BackendErrorCode::NONE:
            return @"NONE";
        case launcher::core::BackendErrorCode::CANCELLED:
            return @"CANCELLED";
        case launcher::core::BackendErrorCode::NETWORK_ERROR:
            return @"NETWORK_ERROR";
        case launcher::core::BackendErrorCode::INTERNAL_ERROR:
            return @"INTERNAL_ERROR";
        case launcher::core::BackendErrorCode::INVALID_ARGUMENT:
            return @"INVALID_ARGUMENT";
        default:
            return @"UNKNOWN";
    }
}

@end 