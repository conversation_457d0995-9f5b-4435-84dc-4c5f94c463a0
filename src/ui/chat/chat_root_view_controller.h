#import <Cocoa/Cocoa.h>

// Forward declarations
@class SidebarViewController;
@class ChatContentViewController;

NS_ASSUME_NONNULL_BEGIN

/**
 * ChatRootViewController is an NSSplitViewController that manages the sidebar and main content area.
 * It provides the ability to toggle the sidebar and tracks the split view items.
 */
@interface ChatRootViewController : NSSplitViewController

// Key components of the split view
@property (nonatomic, readonly) SidebarViewController *sidebarVC;
@property (nonatomic, readonly) ChatContentViewController *contentVC;
@property (nonatomic, readonly) NSSplitViewItem *sidebarSplitViewItem;

// Toggles the sidebar visibility
- (void)toggleSidebar:(nullable id)sender;

// NEW: Called by SidebarManager to insert the shared full-height sidebar item.
- (void)attachSidebarItem:(NSSplitViewItem *)item;

@end

NS_ASSUME_NONNULL_END 