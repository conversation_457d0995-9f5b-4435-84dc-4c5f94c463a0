#import "session_metadata_manager.h"
#import <AppKit/AppKit.h>
#import "sidebar_symbol_pool.h"
#include "../../core/util/debug.h"
#include "../../core/persistence/conversation_store.h"
#include "../../core/chat/conversation_manager.h"
#include <objc/message.h>
#include "macos_chat_ui.h"
#include "../common/result_item.h"
#include "../macos/macos_ui_internal.h"
#include "../../core/app_context.h"
#include "../../core/history/history_manager_interface.h"

using namespace launcher::ui;

NSString * const kSessionMetadataManagerTitleUpdatedNotification = @"SessionMetadataManagerTitleUpdatedNotification";

@interface SMMetadata : NSObject
@property (nonatomic, weak)   NSWindow *window;
@property (nonatomic, copy)   NSString *generatedTitle;
@property (nonatomic, copy)   NSString *iconSymbolName;
@property (nonatomic, strong) NSColor  *tintColor;
@property (nonatomic, strong) NSMutableArray<NSString *> *firstUtterances;
@end

@implementation SMMetadata
- (instancetype)init {
    self = [super init];
    if (self) {
        _generatedTitle = @"New Chat";
        // Leave icon nil to allow SessionMetadataManager to assign a creative
        // symbol during registration.
        _firstUtterances = [NSMutableArray arrayWithCapacity:4];
    }
    return self;
}
@end

namespace launcher {
namespace ui {

#pragma mark - Singleton plumbing

SessionMetadataManager &SessionMetadataManager::shared() {
    static SessionMetadataManager instance;
    return instance;
}

SessionMetadataManager::SessionMetadataManager() {
    // Use weak-key dictionary so windows that go away are purged automatically.
    table_ = [NSMapTable weakToStrongObjectsMapTable];
}

SessionMetadataManager::~SessionMetadataManager() = default;

#pragma mark - Public API

void SessionMetadataManager::registerWindow(NSWindow *window) {
    if (!window) return;
    SMMetadata *m = ensureMetadata(window);
    if (!m.tintColor) {
        m.tintColor = pickRandomTint();
    }
    if (!m.iconSymbolName) {
        m.iconSymbolName = pickRandomIconForWindow(window);
    }
    // Ensure window starts with default title
    if (window.title.length == 0) {
        window.title = m.generatedTitle;
    }
}

void SessionMetadataManager::userSentMessage(NSWindow *window, NSString *message) {
    if (!window || !message) return;
    SMMetadata *m = ensureMetadata(window);
    if (!m.firstUtterances) {
        m.firstUtterances = [NSMutableArray arrayWithCapacity:4];
    }
    // Store first user message only.
    NSString *trimmed = [message stringByTrimmingCharactersInSet:[NSCharacterSet whitespaceAndNewlineCharacterSet]];
    if (trimmed.length > 0 && m.firstUtterances.count == 0) {
        [m.firstUtterances addObject:trimmed];
    }

    // If title still default and we have first message → set title.
    if (m.generatedTitle && [m.generatedTitle isEqualToString:@"New Chat"] && m.firstUtterances.count >= 1) {
        m.generatedTitle = generateTitleFromFirstMessage(m.firstUtterances.firstObject);
        if (m.generatedTitle.length == 0) {
            m.generatedTitle = @"Chat";
        }
        dispatch_async(dispatch_get_main_queue(), ^{
            window.title = m.generatedTitle;
            [[NSNotificationCenter defaultCenter] postNotificationName:kSessionMetadataManagerTitleUpdatedNotification
                                                                object:window];
        });
    }

    // Persist updated metadata after processing user message
    persistMetadataForWindow(window);
}

NSString *SessionMetadataManager::iconSymbolNameForWindow(NSWindow *window) const {
    if (!window) return nil;
    SMMetadata *m = (SMMetadata *)[table_ objectForKey:window];
    return m ? m.iconSymbolName : nil;
}

NSColor *SessionMetadataManager::tintColorForWindow(NSWindow *window) const {
    if (!window) return nil;
    SMMetadata *m = (SMMetadata *)[table_ objectForKey:window];
    return m ? m.tintColor : [NSColor labelColor];
}

NSString *SessionMetadataManager::generatedTitleForWindow(NSWindow *window) const {
    if (!window) return nil;
    SMMetadata *m = (SMMetadata *)[table_ objectForKey:window];
    return m ? m.generatedTitle : nil;
}

#pragma mark - Metadata Persistence Helpers

void SessionMetadataManager::persistMetadataForWindow(NSWindow *window) {
    if (!window) { return; }

    // Resolve windowController -> uiInstance -> conversationId & manager
    NSWindowController *controller = window.windowController;
    if (!controller) { return; }

    launcher::ui::MacOSChatUI *ui = nil;
    // Use objc_msgSend to avoid ARC issues with C++ types
    typedef launcher::ui::MacOSChatUI *(*GetterFunc)(id, SEL);
    GetterFunc getter = (GetterFunc)objc_msgSend;
    if ([controller respondsToSelector:@selector(uiInstance)]) {
        ui = getter(controller, @selector(uiInstance));
    }

    if (!ui) { return; }

    std::string convId = ui->conversationId();
    if (convId.empty()) { return; }

    // Build JSON meta object
    SMMetadata *m = (SMMetadata *)[table_ objectForKey:window];
    if (!m) { return; }

    nlohmann::json meta;
    if (m.iconSymbolName) {
        meta["icon_symbol"] = std::string([m.iconSymbolName UTF8String]);
    }
    if (m.tintColor) {
        NSColor *srgb = [m.tintColor colorUsingColorSpace:[NSColorSpace sRGBColorSpace]];
        meta["tint"] = { srgb.redComponent, srgb.greenComponent, srgb.blueComponent, srgb.alphaComponent };
    }
    if (m.generatedTitle) {
        meta["title"] = std::string([m.generatedTitle UTF8String]);
    }

    // Update conversation manager if available
    launcher::core::ConversationManager *cm = ui->conversationManager();
    if (cm) {
        cm->setMeta(meta);
        auto fullJson = cm->exportToJson();
        launcher::core::persistence::ConversationStore::instance().saveConversation(convId, fullJson);

        // ---- Record in recent history so launcher bar can reopen this chat ----
        // Build a history item with stable identifier (conversation id).
        // Store an empty path because conversation is reopened via identifier.
        std::string titleStr = m.generatedTitle ? std::string([m.generatedTitle UTF8String]) : convId;
        ResultItem histItem(
            titleStr,
            "",                 // path unused for chat conversations
            "",                 // iconPath not used by history list
            1.0,                 // arbitrary score (HistoryManager recalculates)
            "chat",             // type
            convId
        );

        // Record launch through injected history manager accessed via AppContext
        AppDelegate *appDelegate = (AppDelegate *)[[NSApplication sharedApplication] delegate];
        auto *ctx = appDelegate ? appDelegate.ctx : nullptr;
        if (ctx && ctx->historyManager) {
            ctx->historyManager->recordLaunch(histItem);
        }
    }
}

void SessionMetadataManager::applyMetadataForWindow(NSWindow *window,
                                                   NSString *iconSymbol,
                                                   NSColor *tint,
                                                   NSString *title) {
    if (!window) { return; }
    SMMetadata *m = ensureMetadata(window);

    if (iconSymbol) {
        m.iconSymbolName = iconSymbol;
    }
    if (tint) {
        m.tintColor = tint;
    }
    if (title) {
        m.generatedTitle = title;
        window.title = title;

        // notify title update
        dispatch_async(dispatch_get_main_queue(), ^{
            [[NSNotificationCenter defaultCenter] postNotificationName:kSessionMetadataManagerTitleUpdatedNotification
                                                                object:window];
        });
    }

    // After applying, persist to disk so future launches have it.
    persistMetadataForWindow(window);
}

#pragma mark - Helper private methods

id SessionMetadataManager::ensureMetadata(NSWindow *window) {
    SMMetadata *m = (SMMetadata *)[table_ objectForKey:window];
    if (!m) {
        m = [[SMMetadata alloc] init];
        m.window = window;
        m.generatedTitle = @"New Chat";
        [table_ setObject:m forKey:window];
    }
    return m;
}

NSString *SessionMetadataManager::pickRandomIcon() const {
    static NSArray<NSString *> *symbols = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        symbols = SidebarSymbolPool();
    });
    uint32_t idx = arc4random_uniform((uint32_t)symbols.count);
    return symbols[idx];
}

NSString *SessionMetadataManager::pickRandomIconForWindow(NSWindow *window) const {
    if (!window) {
        return @"bubble.left"; // sensible default
    }

    NSArray<NSString *> *symbols = SidebarSymbolPool();
    if (symbols.count == 0) {
        return @"bubble.left";
    }

    // Mix window pointer with current day to create a pseudo-random but stable
    // index that changes at most once per day.
    uint64_t daysSince1970 = (uint64_t)([[NSDate date] timeIntervalSince1970] / (60 * 60 * 24));
    uintptr_t ptrBits = reinterpret_cast<uintptr_t>(window);
    uint64_t mixed = daysSince1970 ^ ptrBits;
    uint32_t idx = (uint32_t)(mixed % symbols.count);

    NSString *candidate = symbols[idx];

    // Verify that the symbol exists on the running macOS version.  Fallback if
    // AppKit returns nil (symbol not present on this OS release).
    if (@available(macOS 11.0, *)) {
        NSImage *test = [NSImage imageWithSystemSymbolName:candidate accessibilityDescription:nil];
        if (test) {
            return candidate;
        }
    }

    // Fallback to older small pool if symbol unavailable.
    return pickRandomIcon();
}

NSColor *SessionMetadataManager::pickRandomTint() const {
    static NSArray<NSColor *> *palette = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        palette = @[ [NSColor systemRedColor],
                     [NSColor systemOrangeColor],
                     [NSColor systemYellowColor],
                     [NSColor systemGreenColor],
                     [NSColor systemTealColor],
                     [NSColor systemBlueColor],
                     [NSColor systemIndigoColor],
                     [NSColor systemPurpleColor],
                     [NSColor systemPinkColor],
                     [NSColor systemBrownColor] ];
    });
    uint32_t idx = arc4random_uniform((uint32_t)palette.count);
    return palette[idx];
}

NSString *SessionMetadataManager::generateTitleFromFirstMessage(NSString *message) const {
    if (!message || message.length == 0) return @"Chat";
    NSArray<NSString *> *words = [message componentsSeparatedByCharactersInSet:[NSCharacterSet whitespaceAndNewlineCharacterSet]];
    NSMutableArray<NSString *> *filtered = [NSMutableArray array];
    for (NSString *w in words) {
        if (w.length > 0) {
            [filtered addObject:w];
        }
        if (filtered.count >= 8) break;
    }
    NSString *joined = [filtered componentsJoinedByString:@" "];
    // Capitalise first letter of joined string.
    if (joined.length > 0) {
        joined = [joined stringByReplacingCharactersInRange:NSMakeRange(0,1)
                                                 withString:[[joined substringToIndex:1] uppercaseString]];
    }
    return joined;
}

} // namespace ui
} // namespace launcher 