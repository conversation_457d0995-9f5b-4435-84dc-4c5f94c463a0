#pragma once

#include <memory>
#include "../../core/persistence/session_serializer.h"

#ifdef __OBJC__
@class NSWindow;
#else
class NSWindow;
#endif

namespace launcher::ui {
class SessionAutosaveManagerImpl;

class SessionAutosaveManager {
public:
    static SessionAutosaveManager &shared();

    void start();
    void stop();

    // Restores windows from the previous autosaved session (invoked at app launch).
    // Returns true if at least one window was successfully restored.
    bool restorePreviousSession();

    // Records a snapshot of a window being closed so it can be restored later
    // via the Reopen-Closed-Tab shortcut.  Must be called on the main thread.
    void recordClosedWindow(NSWindow *window);

    // Pops the most-recently closed window (if any) from the internal stack and
    // restores it.  If |anchorWindow| is provided, the reopened tab will be
    // inserted into the same logical tab group as that window whenever
    // possible.  Returns true if a window was reopened, false otherwise.
    bool reopenLastClosedWindow(NSWindow *anchorWindow = nullptr);

    // Reopen a closed window using a specific snapshot (used by TabGroupManager)
    bool reopenClosedWindowFromSnapshot(const launcher::core::persistence::WindowSnapshot &snapshot,
                                        NSWindow *anchorWindow = nullptr);

private:
    SessionAutosaveManager();
    ~SessionAutosaveManager();
    SessionAutosaveManager(const SessionAutosaveManager&) = delete;
    SessionAutosaveManager &operator=(const SessionAutosaveManager&) = delete;

    // Internal periodic autosave task.
    void performAutosave();

    std::unique_ptr<SessionAutosaveManagerImpl> pImpl_;
};
} // namespace launcher::ui 