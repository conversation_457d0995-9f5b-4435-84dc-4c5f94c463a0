#ifndef MINICHAT_CHAT_VIEW_CONTROLLER_H
#define MINICHAT_CHAT_VIEW_CONTROLLER_H

#import <Cocoa/Cocoa.h>

#import "../../core/model/Message.h"

@class MessageView;

/**
 * @class ChatViewController
 * @brief Controller for displaying chat messages in a virtualized list
 *
 * Uses NSCollectionView for efficient rendering of large message lists
 * with proper memory usage and scrolling performance.
 */
@interface ChatViewController : NSViewController <NSCollectionViewDataSource,
                                                  NSCollectionViewDelegate,
                                                  NSCollectionViewDelegateFlowLayout>

// Properties
@property(nonatomic, strong) NSCollectionView* collectionView;
@property(nonatomic, strong) NSScrollView* scrollView;
@property(nonatomic, strong) NSMutableArray<Message*>* messages;
@property(nonatomic, assign) BOOL autoScrollToBottom;

// --- Add Animation State Dictionaries ---
@property(nonatomic, strong) NSMutableDictionary<NSString*, NSString*>* targetMessageContent;
@property(nonatomic, strong) NSMutableDictionary<NSString*, NSString*>* displayedMessageContent;
@property(nonatomic, strong) NSMutableDictionary<NSString*, NSTimer*>* animationTimers;
@property(nonatomic, strong) NSMutableDictionary<NSString*, NSNumber*>* wordIndex; // Tracks the current word index being displayed
// --- End Animation State Dictionaries ---

// Methods
- (void)addMessage:(Message*)message;
- (void)addMessages:(NSArray<Message*>*)messages;
- (void)clearMessages;
- (void)scrollToBottom:(BOOL)animated;
- (void)updateLayout;
- (void)reloadData;

// --- Add methods for streaming updates ---
/**
 * @brief Replaces the content and type of an existing message.
 * Used for updating streaming messages.
 * 
 * @param messageId The ID of the message to update.
 * @param newContent The new raw content for the message.
 * @param newType The new message type.
 */
- (void)replaceMessageContent:(NSString*)messageId 
               withNewContent:(NSString*)newContent 
                      newType:(MessageType)newType;

/**
 * @brief Finalizes a message after streaming is complete.
 * Currently might just re-layout the item.
 *
 * @param messageId The ID of the message to finalize.
 */
- (void)finalizeMessage:(NSString*)messageId;
// --- End methods for streaming updates ---

@end

#endif  // MINICHAT_CHAT_VIEW_CONTROLLER_H
