#import "sidebar_item.h"
#include "../../core/util/debug.h"

@implementation SidebarItem

+ (instancetype)groupWithTitle:(NSString *)title children:(NSArray<SidebarItem *> *)children {
    SidebarItem *item = [[SidebarItem alloc] init];
    item.title = title;
    item.children = children ?: @[];
    item.groupHeader = YES;
    return item;
}

+ (instancetype)leafWithTitle:(NSString *)title icon:(NSImage *)icon {
    SidebarItem *item = [[SidebarItem alloc] init];
    item.title = title;
    item.icon = icon;
    item.children = @[];
    item.groupHeader = NO;
    return item;
}

@end 