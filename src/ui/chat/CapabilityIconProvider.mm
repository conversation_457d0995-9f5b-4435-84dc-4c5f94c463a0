#import "CapabilityIconProvider.h"

@implementation CapabilityIconProvider

+ (NSImage *)iconForCapability:(launcher::core::Capability)cap {
    NSString *symbol = nil;
    switch (cap) {
        case launcher::core::Capability::ToolUse:
            symbol = @"wrench";
            break;
        case launcher::core::Capability::ImageInput:
            symbol = @"photo";
            break;
        case launcher::core::Capability::AudioInput:
            symbol = @"mic";
            break;
        case launcher::core::Capability::ImageGeneration:
            symbol = @"paintbrush";
            break;
        case launcher::core::Capability::SpeechGeneration:
            symbol = @"speaker.wave.2";
            break;
        case launcher::core::Capability::LargeContext:
            symbol = @"book";
            break;
        case launcher::core::Capability::MultimodalRealtime:
            symbol = @"bolt.circle";
            break;
        case launcher::core::Capability::HighReasoning:
            symbol = @"sparkles";
            break;
        case launcher::core::Capability::ComputerUse:
            symbol = @"desktopcomputer";
            break;
        default:
            symbol = nil;
            break;
    }
    if (!symbol) return nil;
    NSImage *img = [NSImage imageWithSystemSymbolName:symbol accessibilityDescription:nil];
    img.size = NSMakeSize(13, 13);
    return img;
}

@end 