#pragma once

#import <Cocoa/Cocoa.h>

@class SidebarViewController;

/**
 * SidebarManager is a process-wide façade that guarantees *each* Chat window (i.e. each
 * NSSplitViewController that hosts a Chat UI) owns an independent full-height sidebar.
 *
 * Internally the manager caches a SidebarViewController + NSSplitViewItem bundle per host
 * NSSplitViewController.  When a window becomes main the manager ensures that a sidebar bundle
 * exists for it and attaches it if necessary – it never *moves* an existing sidebar away from
 * another window.
 *
 * This design keeps the original window's sidebar intact when the user tears a tab out to create a
 * new window while still using a single point of coordination for data updates.
 */
@interface SidebarManager : NSObject

/// Returns the singleton instance.
+ (instancetype)sharedManager;

/// Returns *one* (arbitrary) sidebar split-view item – mostly for legacy callers.  Newly written
/// code should not rely on a single global sidebar instance.
@property (nonatomic, readonly) NSSplitViewItem *sidebarItem __attribute__((deprecated("Use per-window sidebar instances instead.")));

/// Convenience accessor for *one* underlying SidebarViewController. Deprecated for the same
/// reason as `sidebarItem`.
@property (nonatomic, readonly) SidebarViewController *sidebarVC __attribute__((deprecated("Use per-window sidebar instances instead.")));

/// Ensures the shared sidebar is hosted by the given split-view controller.  Usually you do *not*
/// need to call this manually – the manager observes NSWindowDidBecomeMainNotification and moves
/// the sidebar automatically.  However, the very first window may call it eagerly after
/// construction to display the sidebar without waiting for AppKit notifications.
- (void)attachSidebarToSplitViewController:(NSSplitViewController *)svc;

@end 