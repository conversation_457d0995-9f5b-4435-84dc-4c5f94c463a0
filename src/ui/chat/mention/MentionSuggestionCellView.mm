#import "MentionSuggestionCellView.h"
#include "../../../core/util/debug.h"

@interface MentionSuggestionCellView ()
@property (nonatomic, strong, readwrite) NSTextField *primaryTextField;
@property (nonatomic, strong, readwrite) NSTextField *secondaryTextField;
@property (nonatomic, strong, readwrite) NSImageView *avatarImageView;
@end

@implementation MentionSuggestionCellView

- (instancetype)initWithFrame:(NSRect)frameRect {
    self = [super initWithFrame:frameRect];
    if (self) {
        [self setupSubviews];
    }
    return self;
}

- (void)setupSubviews {
    DBM(@"Setting up custom cell view");
    
    // Create and configure the avatar image view
    _avatarImageView = [[NSImageView alloc] initWithFrame:NSZeroRect];
    _avatarImageView.translatesAutoresizingMaskIntoConstraints = NO;
    _avatarImageView.wantsLayer = YES;
    _avatarImageView.layer.cornerRadius = 12.0; // Make it circular
    _avatarImageView.layer.masksToBounds = YES;
    [self addSubview:_avatarImageView];
    
    // Create and configure the primary text field
    _primaryTextField = [[NSTextField alloc] initWithFrame:NSZeroRect];
    _primaryTextField.translatesAutoresizingMaskIntoConstraints = NO;
    _primaryTextField.bezeled = NO;
    _primaryTextField.drawsBackground = NO;
    _primaryTextField.editable = NO;
    _primaryTextField.selectable = NO;
    _primaryTextField.font = [NSFont systemFontOfSize:13.0 weight:NSFontWeightMedium];
    _primaryTextField.textColor = [NSColor labelColor];
    _primaryTextField.lineBreakMode = NSLineBreakByTruncatingTail;
    [self addSubview:_primaryTextField];
    
    // Create and configure the secondary text field
    _secondaryTextField = [[NSTextField alloc] initWithFrame:NSZeroRect];
    _secondaryTextField.translatesAutoresizingMaskIntoConstraints = NO;
    _secondaryTextField.bezeled = NO;
    _secondaryTextField.drawsBackground = NO;
    _secondaryTextField.editable = NO;
    _secondaryTextField.selectable = NO;
    _secondaryTextField.font = [NSFont systemFontOfSize:11.0];
    _secondaryTextField.textColor = [NSColor secondaryLabelColor];
    _secondaryTextField.lineBreakMode = NSLineBreakByTruncatingTail;
    [self addSubview:_secondaryTextField];
    
    // Set up constraints
    [NSLayoutConstraint activateConstraints:@[
        // Avatar constraints
        [_avatarImageView.leadingAnchor constraintEqualToAnchor:self.leadingAnchor constant:8.0],
        [_avatarImageView.centerYAnchor constraintEqualToAnchor:self.centerYAnchor],
        [_avatarImageView.widthAnchor constraintEqualToConstant:24.0],
        [_avatarImageView.heightAnchor constraintEqualToConstant:24.0],
        
        // Primary text field constraints
        [_primaryTextField.leadingAnchor constraintEqualToAnchor:_avatarImageView.trailingAnchor constant:8.0],
        [_primaryTextField.trailingAnchor constraintEqualToAnchor:self.trailingAnchor constant:-8.0],
        [_primaryTextField.topAnchor constraintEqualToAnchor:self.topAnchor constant:4.0],
        
        // Secondary text field constraints
        [_secondaryTextField.leadingAnchor constraintEqualToAnchor:_avatarImageView.trailingAnchor constant:8.0],
        [_secondaryTextField.trailingAnchor constraintEqualToAnchor:self.trailingAnchor constant:-8.0],
        [_secondaryTextField.topAnchor constraintEqualToAnchor:_primaryTextField.bottomAnchor constant:0.0],
        [_secondaryTextField.bottomAnchor constraintEqualToAnchor:self.bottomAnchor constant:-4.0]
    ]];
    
    // Set textField property from NSTableCellView (for compatibility)
    self.textField = _primaryTextField;
}

- (void)configureCellWithSuggestion:(MentionSuggestion *)suggestion {
    // Set the primary text
    self.primaryTextField.stringValue = suggestion.displayText ?: @"";
    
    // Set the secondary text if available
    if (suggestion.secondaryText) {
        self.secondaryTextField.stringValue = suggestion.secondaryText;
        self.secondaryTextField.hidden = NO;
    } else {
        self.secondaryTextField.stringValue = @"";
        self.secondaryTextField.hidden = YES;
    }
    
    // Load avatar image if available
    if (suggestion.avatarURL) {
        // In a real implementation, you might want to use a cached image loader
        // For now, we'll use a simple approach
        self.avatarImageView.image = nil; // Clear existing image
        self.avatarImageView.hidden = NO;
        
        // Load image asynchronously
        NSURLSession *session = [NSURLSession sharedSession];
        [[session dataTaskWithURL:suggestion.avatarURL
                completionHandler:^(NSData * _Nullable data, NSURLResponse * _Nullable response, NSError * _Nullable error) {
                    if (data) {
                        NSImage *image = [[NSImage alloc] initWithData:data];
                        dispatch_async(dispatch_get_main_queue(), ^{
                            self.avatarImageView.image = image;
                        });
                    }
                }] resume];
    } else {
        // No avatar URL, use a placeholder or hide
        self.avatarImageView.image = [NSImage imageNamed:NSImageNameUser];
        self.avatarImageView.hidden = NO;
    }
}

@end 