#import <Cocoa/Cocoa.h>
#import "../model/MentionSuggestion.h"

NS_ASSUME_NONNULL_BEGIN

/**
 * Custom table cell view for displaying mention suggestions with avatar and secondary text.
 */
@interface MentionSuggestionCellView : NSTableCellView

/** The primary display text field (inherited from NSTableCellView) */
@property (nonatomic, strong, readonly) NSTextField *primaryTextField;

/** The secondary/subtitle text field */
@property (nonatomic, strong, readonly) NSTextField *secondaryTextField;

/** The avatar image view */
@property (nonatomic, strong, readonly) NSImageView *avatarImageView;

/**
 * Configures the cell view with the given suggestion data.
 *
 * @param suggestion The MentionSuggestion object containing the data to display.
 */
- (void)configureCellWithSuggestion:(MentionSuggestion *)suggestion;

@end

NS_ASSUME_NONNULL_END 