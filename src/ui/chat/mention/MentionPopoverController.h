// src/ui/chat/mention/MentionPopoverController.h

#import <Cocoa/Cocoa.h>
#import "../model/MentionSuggestion.h"
#import "../model/BackendError.h"
#import "MentionViewController.h"

NS_ASSUME_NONNULL_BEGIN

@class MentionPopoverController;
@class MentionViewController;

// --- Delegate Protocol ---
/**
 * Delegate protocol for MentionPopoverController to interact with the backend
 * and notify about user selections or cancellations.
 */
@protocol MentionPopoverDelegate <NSObject>

@required
/**
 * Request backend search for mention suggestions.
 * The implementation should call the backend asynchronously and eventually call the completion block.
 *
 * @param controller The popover controller instance making the request.
 * @param query The search query string (text after '@').
 * @param requestIdentifier An NSNumber wrapping a unique uint64_t identifying this specific request instance. Used for cancellation.
 * @param completionBlock Block to call with results or error. MUST be called on the main thread.
 */
- (void)mentionController:(MentionPopoverController *)controller
           searchForQuery:(NSString *)query
               identifier:(NSNumber *)requestIdentifier // Identifier (NSNumber wrapping uint64_t)
          completionBlock:(void (^)(NSArray<MentionSuggestion *> * _Nullable suggestions, BackendError * _Nullable error))completionBlock;

/**
 * Called when the user selects a suggestion from the popover.
 * The delegate is responsible for updating the text view.
 *
 * @param controller The popover controller instance.
 * @param suggestion The selected MentionSuggestion object.
 * @param replacementRange The range in the text view that should be replaced with the mention.
 */
- (void)mentionController:(MentionPopoverController *)controller
      didSelectSuggestion:(MentionSuggestion *)suggestion
                  atRange:(NSRange)replacementRange;

@optional
/**
 * Called by the MentionPopoverController when it cancels an ongoing NSOperation
 * (e.g., due to a new search starting before the previous one finished).
 * The delegate should use this signal to cancel the corresponding backend request
 * using the provided identifier.
 *
 * @param controller The popover controller instance.
 * @param requestIdentifier The NSNumber identifier of the operation/request that was cancelled internally.
 */
- (void)mentionController:(MentionPopoverController *)controller
didCancelOperationWithIdentifier:(NSNumber *)requestIdentifier;

@end


// --- Controller Interface ---
/**
 * Manages the popover UI for displaying @mention suggestions.
 * Handles debouncing, interaction with the backend via a delegate, and user selection.
 */
@interface MentionPopoverController : NSObject <NSPopoverDelegate, MentionViewControllerDelegate>

/** The delegate responsible for handling backend requests and selection actions. */
@property (nonatomic, weak) id<MentionPopoverDelegate> delegate;

/** The text view to which the mention popover is anchored and interacts with. */
@property (nonatomic, weak) NSTextView * _Nullable targetTextView;

/** Read-only access to the underlying popover. */
@property (nonatomic, strong, readonly) NSPopover *popover;

/** Read-only access to the view controller managing the popover's content. */
@property (nonatomic, strong, readonly) MentionViewController *mentionViewController;

/** The range of the original mention trigger ('@' + query) that initiated the popover. */
@property (nonatomic, assign, readonly) NSRange triggeringRange;

/** Indicates whether the mention popover is currently visible. */
@property (nonatomic, assign, readonly) BOOL isVisible;

/**
 * Initializes the controller.
 *
 * @return An initialized MentionPopoverController instance.
 */
- (instancetype)init NS_DESIGNATED_INITIALIZER;

/**
 * Shows the popover, anchors it to the specified range in the target text view,
 * and initiates a search for the given query.
 *
 * @param query The initial search query.
 * @param textView The text view to anchor to.
 * @param range The range in the text view (usually the '@' + query) to anchor the popover relative to.
 */
- (void)showAndSearchWithQuery:(NSString *)query
             anchoredToTextView:(NSTextView *)textView
                          range:(NSRange)range;

/**
 * Updates the search query for the currently displayed popover.
 * This will typically cancel any pending search and start a new one after debouncing.
 *
 * @param query The new search query.
 * @param range The updated range in the text view representing the mention trigger + query.
 */
- (void)updateSearchQuery:(NSString *)query range:(NSRange)range;

/**
 * Hides the popover and cancels any pending search operations.
 */
- (void)hide;

// --- NEW: Methods for Keyboard Navigation ---

/**
 * Attempts to move the selection in the suggestion list up.
 * @return YES if the selection was moved, NO otherwise (e.g., already at top).
 */
- (BOOL)moveSelectionUp;

/**
 * Attempts to move the selection in the suggestion list down.
 * @return YES if the selection was moved, NO otherwise (e.g., already at bottom or list empty).
 */
- (BOOL)moveSelectionDown;

/**
 * Attempts to confirm the currently selected suggestion.
 * If successful, informs the delegate via `mentionController:didSelectSuggestion:atRange:`.
 * @return YES if a suggestion was selected and confirmed, NO otherwise.
 */
- (BOOL)confirmSelection;

@end

NS_ASSUME_NONNULL_END 