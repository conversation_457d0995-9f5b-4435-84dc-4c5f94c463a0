#import "MentionPopoverController.h"
#import "MentionViewController.h" // Assuming this exists or will be created
#include "../../../core/util/debug.h"
#import <CommonCrypto/CommonDigest.h> // For uint64_t if using random generation
#include <stdlib.h> // For arc4random

// Constants
static const NSTimeInterval kMentionDebounceDelay = 0.25; // Debounce delay in seconds

// Custom NSOperationQueue category to add the afterDelay method for older macOS
@interface NSOperationQueue (DelayedOperations)
- (void)addOperationAfterDelay:(NSOperation *)operation delay:(NSTimeInterval)delay;
@end

@implementation NSOperationQueue (DelayedOperations)
- (void)addOperationAfterDelay:(NSOperation *)operation delay:(NSTimeInterval)delay {
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(delay * NSEC_PER_SEC)),
                   self.underlyingQueue ?: dispatch_get_global_queue(QOS_CLASS_USER_INITIATED, 0),
                   ^{
                       if (!operation.isCancelled) {
                           [self addOperation:operation];
                       }
                   });
}
@end

@interface MentionPopoverController () <NSPopoverDelegate, MentionViewControllerDelegate>

// Readwrite redeclarations for internal use (note the 'readwrite' property attribute)
@property (nonatomic, strong, readwrite) NSPopover *popover;
@property (nonatomic, strong, readwrite) MentionViewController *mentionViewController;
@property (nonatomic, assign, readwrite) NSRange currentMentionRange;
@property (nonatomic, assign, readwrite) BOOL isVisible;
@property (nonatomic, assign, readwrite) NSRange triggeringRange;
// Do not redeclare targetTextView - it's already declared in the .h file

// Internal properties
@property (nonatomic, strong) NSOperationQueue *searchQueue; // For debouncing
@property (nonatomic, strong, nullable) NSBlockOperation *currentSearchOperation;
@property (nonatomic, strong, nullable) NSNumber *currentRequestIdentifier; // Stores the NSNumber wrapping uint64_t
@property (nonatomic, strong, nullable) NSString *currentQuery;

@end

@implementation MentionPopoverController

- (instancetype)init {
    self = [super init];
    if (self) {
        // DBM("init", @"Initializing");
        
        _currentMentionRange = NSMakeRange(NSNotFound, 0);
        _triggeringRange = NSMakeRange(NSNotFound, 0);
        _isVisible = NO;
        
        // Setup Debounce Queue
        _searchQueue = [[NSOperationQueue alloc] init];
        _searchQueue.maxConcurrentOperationCount = 1; // Serial queue for debouncing
        _searchQueue.qualityOfService = NSQualityOfServiceUserInitiated;
        
        // Setup Content View Controller
        _mentionViewController = [[MentionViewController alloc] init];
        _mentionViewController.delegate = self; // Set delegate for selection
        
        // Setup Popover
        _popover = [[NSPopover alloc] init];
        _popover.contentViewController = _mentionViewController;
        _popover.behavior = NSPopoverBehaviorSemitransient; // Close on outside click
        _popover.animates = YES;
        _popover.delegate = self; // To know when it closes
        
        // Set an initial size (adjust as needed)
        _popover.contentSize = NSMakeSize(250, 150);
    }
    return self;
}

// --- Public API ---

- (void)showAndSearchWithQuery:(NSString *)query
             anchoredToTextView:(NSTextView *)textView
                          range:(NSRange)range // This is the full @mention range
{
    if (!textView || !self.delegate) {
        ERM(@"Error: TextView or Delegate is nil.");
        return;
    }
    
    DBM(@"Showing for query '%@' at range %@", query, NSStringFromRange(range));
    
    self.targetTextView = textView;
    self.currentMentionRange = range; // Store the range for potential replacement
    self.triggeringRange = range;     // Save the original triggering range
    self.currentQuery = [query copy]; // Store initial query
    
    // Show popover relative to the start of the range (the '@' symbol usually)
    NSRect positioningRect = [self rectForRange:NSMakeRange(range.location, 0) inTextView:textView];
    if (NSIsEmptyRect(positioningRect)) {
        ERM(@"Error: Could not calculate positioning rect. Falling back to view bounds.");
        positioningRect = textView.bounds;
    }
    
    // Ensure view controller is ready before showing
    [self.mentionViewController view]; // Load the view if not already loaded
    [self.mentionViewController updateSuggestions:@[] isLoading:YES]; // Show loading state initially
    
    // Show the popover
    if (!self.popover.isShown) {
        [self.popover showRelativeToRect:positioningRect
                                  ofView:textView
                           preferredEdge:NSRectEdgeMaxY]; // Prefer showing below the text
        self.isVisible = YES;

        // Ensure the text view remains the first responder so typing can continue.
        // Without this, showing the popover might cause the text view to lose focus.
        [textView.window makeFirstResponder:textView];

        // Initial search with the provided query
        [self performSearchWithQuery:query];
    } else {
        // If already shown, just update position if necessary (might not be needed)
        // self.popover.positioningRect = positioningRect;
    }
}

- (void)updateSearchQuery:(NSString *)query range:(NSRange)range {
    if (!self.isVisible || !self.targetTextView) {
        return;
    }
    
    DBM(@"Updating query to '%@', range %@", query, NSStringFromRange(range));
    
    self.currentMentionRange = range; // Update the range
    self.currentQuery = [query copy];
    
    // Update positioning only if the location changed significantly (optional)
    // NSRect positioningRect = [self rectForRange:NSMakeRange(range.location, 0) inTextView:self.targetTextView];
    // self.popover.positioningRect = positioningRect;
    
    // Perform the new search (debounced)
    [self performSearchWithQuery:query];
}

- (void)hide {
    if (!self.isVisible) return;
    
    DBM(@"Hiding popover and cancelling search.");
    
    [self cancelCurrentSearchOperation];
    
    if (self.popover.isShown) {
        [self.popover performClose:self];
    }

    // Reset state immediately, don't wait for popoverDidClose
    self.isVisible = NO;
    self.targetTextView = nil;
    self.currentMentionRange = NSMakeRange(NSNotFound, 0);
    self.triggeringRange = NSMakeRange(NSNotFound, 0);
    self.currentQuery = nil;
    self.currentRequestIdentifier = nil; // Clear identifier when hiding
    [self.mentionViewController updateSuggestions:@[] isLoading:NO]; // Clear suggestions
    [self.mentionViewController resetSelection]; // Reset table view selection
}

// --- Internal Search Logic ---

- (void)performSearchWithQuery:(NSString *)query {
    // 1. Cancel any previously scheduled/running search operation
    [self cancelCurrentSearchOperation];
    
    // 2. Generate a NEW unique identifier for this request
    uint64_t rawIdentifier = 0;
    arc4random_buf(&rawIdentifier, sizeof(rawIdentifier)); // Use arc4random for uniqueness
    NSNumber *newRequestIdentifier = @(rawIdentifier);
    self.currentRequestIdentifier = newRequestIdentifier;
    
    DBM(@"Scheduling search for query '%@' with ID: %@", query, newRequestIdentifier);
    
    // 3. Create a new search operation (debounced)
    __weak MentionPopoverController *weakSelf = self;
    self.currentSearchOperation = [NSBlockOperation blockOperationWithBlock:^{
        // This block executes after the debounce delay
        MentionPopoverController *strongSelf = weakSelf;
        if (!strongSelf || strongSelf.currentSearchOperation.isCancelled) {
             DBM(@"Search operation cancelled or self deallocated before execution (ID: %@)", newRequestIdentifier);
            return;
        }
        
        // Check if the query is still relevant (might have changed during debounce)
        NSString *queryToSearch = [strongSelf.currentQuery copy];
        
        DBM(@"Executing search for query '%@' (ID: %@)", queryToSearch, newRequestIdentifier);
        
        // --- Call the Delegate --- //
        dispatch_async(dispatch_get_main_queue(), ^{ // Ensure delegate call is from main thread initially
            if (strongSelf.delegate && [strongSelf.delegate respondsToSelector:@selector(mentionController:searchForQuery:identifier:completionBlock:)]) {
                
                // Update UI to show loading state before calling delegate
                [strongSelf.mentionViewController updateSuggestions:@[] isLoading:YES];
                
                [strongSelf.delegate mentionController:strongSelf
                                        searchForQuery:queryToSearch
                                            identifier:newRequestIdentifier // Pass the generated identifier
                                       completionBlock:^(NSArray<MentionSuggestion *> * _Nullable suggestions, BackendError * _Nullable error) {
                    // --- Delegate Completion Block (Runs on Main Thread) --- //
                     MentionPopoverController *innerSelf = weakSelf;
                     if (!innerSelf) return;
                     
                     // IMPORTANT: Check if this is the result for the *current* active request identifier
                     // This prevents processing stale results if a new search was started quickly.

                     // Explicitly check for nil current identifier first
                     NSNumber *currentID = innerSelf.currentRequestIdentifier; // Read property once
                     if (!currentID) {
                         DBM(@"Ignoring result for ID: %@ because popover is hidden/reset (currentID is nil)", newRequestIdentifier);
                         return;
                     }

                     // Now compare if currentID was not nil
                     if (![newRequestIdentifier isEqualToNumber:currentID]) {
                         DBM(@"Ignoring stale result for ID: %@ (Current ID: %@)", newRequestIdentifier, currentID);
                         return;
                     }

                     // Check if the operation associated with this ID was cancelled in the meantime
                     // (Although backend should ideally report CANCELLED error, double-check)
                     if (innerSelf.currentSearchOperation && innerSelf.currentSearchOperation.isCancelled) {
                          DBM(@"Search operation was cancelled before results processed (ID: %@)", newRequestIdentifier);
                         // Don't update UI if cancelled explicitly, backend should send CANCELLED error.
                         // But if backend didn't, we might want to hide loading here.
                         // [innerSelf.mentionViewController updateSuggestions:@[] isLoading:NO];
                         // return; // Let error handling below manage UI
                     }

                    
                    if (error) {
                        ERM(@"Received error for ID %@: %@", newRequestIdentifier, error.localizedDescription);
                        // Handle error - Show message in popover? Hide popover?
                        // If cancelled, popover might already be hidden or closing.
                        if (error.cppErrorCode == launcher::core::BackendErrorCode::CANCELLED) {
                             DBM(@"Search explicitly cancelled (ID: %@)", newRequestIdentifier);
                             // Don't show error UI for cancellation, likely intended.
                             [innerSelf.mentionViewController updateSuggestions:@[] isLoading:NO]; // Hide loading
                        } else {
                            // Show other errors
                            [innerSelf.mentionViewController showError:error.localizedDescription];
                        }
                    } else {
                        DBM(@"Received %lu suggestions for ID: %@", (unsigned long)suggestions.count, newRequestIdentifier);
                        // Update popover content with suggestions
                        [innerSelf.mentionViewController updateSuggestions:suggestions ?: @[] isLoading:NO];
                        
                        // Adjust popover size based on content? (Optional)
                        // [innerSelf.popover setContentSize:[innerSelf.mentionViewController preferredContentSize]];
                    }
                }];
            } else {
                 ERM(@"Delegate not set or does not respond to searchForQuery selector (ID: %@)", newRequestIdentifier);
            }
        });
    }];
    
    // 4. Add operation to queue with delay
    if (!self.currentSearchOperation) return; // Should not happen
    
    // Use our custom category method that works on all macOS versions
    [self.searchQueue addOperationAfterDelay:self.currentSearchOperation delay:kMentionDebounceDelay];
}

- (void)cancelCurrentSearchOperation {
    if (self.currentSearchOperation && !self.currentSearchOperation.isFinished && !self.currentSearchOperation.isCancelled) {
        DBM(@"Cancelling previous search operation with ID: %@", self.currentRequestIdentifier);
        [self.currentSearchOperation cancel];
        
        // Notify delegate that *we* cancelled the NSOperation
        if (self.delegate && [self.delegate respondsToSelector:@selector(mentionController:didCancelOperationWithIdentifier:)]) {
            if (self.currentRequestIdentifier) { // Ensure we have an identifier to send
                 [self.delegate mentionController:self didCancelOperationWithIdentifier:self.currentRequestIdentifier];
            }
        }
    }
    self.currentSearchOperation = nil;
    // Keep currentRequestIdentifier until a new one is generated or hidden
    // self.currentRequestIdentifier = nil; // Don't nil here, completion block needs it for comparison
}

// --- Helper for Positioning --- //

- (NSRect)rectForRange:(NSRange)range inTextView:(NSTextView *)textView {
    if (range.location == NSNotFound || !textView.layoutManager) {
        return NSZeroRect;
    }
    
    // Use layoutManager to find the rect for the character range
    NSRange glyphRange = [textView.layoutManager glyphRangeForCharacterRange:range actualCharacterRange:NULL];
    NSRect rect = [textView.layoutManager boundingRectForGlyphRange:glyphRange inTextContainer:textView.textContainer];
    
    // Adjust rect to be in the text view's coordinate system
    rect = NSOffsetRect(rect, textView.textContainerOrigin.x, textView.textContainerOrigin.y);
    
    // Ensure the rect is not empty (e.g., if range has zero length)
    if (NSIsEmptyRect(rect) && range.location <= textView.string.length) {
        // If empty, try getting rect for a single character at the start of the range
        NSRange singleCharRange = NSMakeRange(range.location, 1);
        if (range.location < textView.string.length) {
            rect = [textView.layoutManager boundingRectForGlyphRange:
                   [textView.layoutManager glyphRangeForCharacterRange:singleCharRange actualCharacterRange:NULL] 
                   inTextContainer:textView.textContainer];
            rect = NSOffsetRect(rect, textView.textContainerOrigin.x, textView.textContainerOrigin.y);
        }
    }

    // If still empty, use the insertion point rect
    if (NSIsEmptyRect(rect)) {
        // Use selected range as fallback
        rect = [textView.layoutManager boundingRectForGlyphRange:
               [textView.layoutManager glyphRangeForCharacterRange:textView.selectedRange actualCharacterRange:NULL] 
               inTextContainer:textView.textContainer];
        rect = NSOffsetRect(rect, textView.textContainerOrigin.x, textView.textContainerOrigin.y);
    }
    
    return rect;
}

// --- NSPopoverDelegate Methods ---

- (void)popoverDidClose:(NSNotification *)notification {
    // Called when the popover closes for any reason (e.g., clicking outside)
    if (self.isVisible) { // Check if we didn't already handle the close via hide method
        DBM(@"Popover closed via notification.");
        // Treat this the same as an explicit hide
        [self hide]; // Will cancel operations and reset state
    }
}

// Optional: Control detachable window behavior
- (BOOL)popoverShouldDetach:(NSPopover *)popover {
    return NO; // Prevent detaching into a separate window
}

// --- MentionViewControllerDelegate Methods ---

- (void)mentionViewController:(MentionViewController *)controller didSelectSuggestion:(MentionSuggestion *)suggestion {
    DBM(@"Suggestion selected: %@ (ID: %@)", suggestion.displayText, suggestion.identifier);
    
    if (self.delegate && [self.delegate respondsToSelector:@selector(mentionController:didSelectSuggestion:atRange:)]) {
        // Use the stored `currentMentionRange` for replacement
        if (self.currentMentionRange.location != NSNotFound) {
            [self.delegate mentionController:self 
                           didSelectSuggestion:suggestion 
                                     atRange:self.currentMentionRange];
        } else {
             ERM(@"Error: Invalid mention range stored.");
        }
    } else {
        ERM(@"Error: Delegate not set or invalid.");
    }
    
    [self hide]; // Hide after selection
}

- (void)mentionViewController:(MentionViewController *)controller didUpdatePreferredContentSize:(NSSize)size {
    DBM(@"Updating popover size to: %@", NSStringFromSize(size));
    
    // Apply constraints to the size
    NSSize boundedSize = NSMakeSize(
        MIN(MAX(size.width, 250.0), 400.0),    // Width between 250 and 400
        MIN(MAX(size.height, 100.0), 300.0)    // Height between 100 and 300
    );
    
    // Update the popover size
    self.popover.contentSize = boundedSize;
}

// --- Keyboard Navigation Implementation ---

- (BOOL)moveSelectionUp {
    if (!self.isVisible || !self.mentionViewController) return NO;
    DBM(@"Attempting to move selection up in MentionViewController");
    BOOL handled = [self.mentionViewController moveSelectionUp];
    DBM(@"Result: %s", handled ? "YES" : "NO");
    return handled;
}

- (BOOL)moveSelectionDown {
    if (!self.isVisible || !self.mentionViewController) return NO;
    DBM(@"Attempting to move selection down in MentionViewController");
    BOOL handled = [self.mentionViewController moveSelectionDown];
     DBM(@"Result: %s", handled ? "YES" : "NO");
    return handled;
}

- (BOOL)confirmSelection {
    if (!self.isVisible || !self.mentionViewController) return NO;
    DBM(@"Attempting to confirm selection in MentionViewController");
    // `confirmSelection` in MentionViewController will trigger the delegate method `mentionViewController:didSelectSuggestion:` above if successful
    BOOL handled = [self.mentionViewController confirmSelection]; 
    DBM(@"Result: %s", handled ? "YES" : "NO");
    return handled;
}

- (void)dealloc {
    DBM(@"Deallocating");
    [self cancelCurrentSearchOperation];
    [_searchQueue cancelAllOperations];
    if (_popover) {
        // Make sure delegate is nilled before popover might be released elsewhere
        _popover.delegate = nil;
        if (_popover.isShown) {
            [_popover close];
        }
        _popover.contentViewController = nil; // Break retain cycle if any
    }
    _mentionViewController.delegate = nil; // Break delegate cycle
}

@end 