#import <Cocoa/Cocoa.h>
#import "../model/MentionSuggestion.h"

NS_ASSUME_NONNULL_BEGIN

@class MentionViewController;

// --- Delegate Protocol ---
/**
 * Delegate protocol for MentionViewController to notify its owner (MentionPopoverController)
 * when a suggestion is selected by the user.
 */
@protocol MentionViewControllerDelegate <NSObject>

@required
/**
 * Called when the user selects a suggestion from the list.
 *
 * @param controller The view controller instance.
 * @param suggestion The selected MentionSuggestion object.
 */
- (void)mentionViewController:(MentionViewController *)controller didSelectSuggestion:(MentionSuggestion *)suggestion;

@optional
/**
 * Called when the view controller's preferred content size changes.
 * This allows the popover to resize based on content.
 *
 * @param controller The view controller instance.
 * @param size The new preferred content size.
 */
- (void)mentionViewController:(MentionViewController *)controller didUpdatePreferredContentSize:(NSSize)size;

@end


// --- Controller Interface ---
/**
 * View controller responsible for displaying the list of mention suggestions
 * within the MentionPopoverController's popover.
 */
@interface MentionViewController : NSViewController

/** The delegate to notify when a suggestion is selected. */
@property (nonatomic, weak) id<MentionViewControllerDelegate> delegate;

/** The preferred content size based on current content state */
@property (nonatomic, assign) NSSize preferredContentSize;

/**
 * Updates the displayed suggestions and loading/error state.
 *
 * @param suggestions An array of MentionSuggestion objects to display. Can be empty.
 * @param isLoading YES if data is currently being loaded, NO otherwise.
 */
- (void)updateSuggestions:(NSArray<MentionSuggestion *> *)suggestions isLoading:(BOOL)isLoading;

/**
 * Displays an error message to the user.
 *
 * @param errorMessage The error message string to display.
 */
- (void)showError:(NSString *)errorMessage;

/**
 * Moves the selection focus up in the suggestions list.
 * Returns YES if the action was handled (e.g., selection changed), NO otherwise.
 */
- (BOOL)moveSelectionUp;

/**
 * Moves the selection focus down in the suggestions list.
 * Returns YES if the action was handled (e.g., selection changed), NO otherwise.
 */
- (BOOL)moveSelectionDown;

/**
 * Confirms the currently selected suggestion (e.g., user presses Enter).
 * This will trigger the `mentionViewController:didSelectSuggestion:` delegate method.
 * Returns YES if a selection was confirmed, NO otherwise (e.g., no selection).
 */
- (BOOL)confirmSelection;

/**
 * Resets the current selection state.
 */
- (void)resetSelection;

/**
 * Updates the preferred content size based on current content state.
 * This will trigger the optional `mentionViewController:didUpdatePreferredContentSize:` delegate method.
 */
- (void)updatePreferredContentSize;

@end

NS_ASSUME_NONNULL_END 