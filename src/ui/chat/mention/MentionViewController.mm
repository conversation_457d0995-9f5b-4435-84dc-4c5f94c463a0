#import "MentionViewController.h"
#import "MentionSuggestionCellView.h"  // Import our new cell view
#include "../../../core/util/debug.h"

// Define cell identifier
static NSUserInterfaceItemIdentifier const kMentionCellIdentifier = @"MentionCellIdentifier";

// TODO: Add a Table View or Collection View to display suggestions

@interface MentionViewController () <NSTableViewDataSource, NSTableViewDelegate>

@property (nonatomic, strong) NSArray<MentionSuggestion *> *suggestions;
@property (nonatomic, strong) NSProgressIndicator *loadingIndicator;
@property (nonatomic, strong) NSTextField *errorLabel;
@property (nonatomic, strong) NSTableView *tableView;
@property (nonatomic, strong) NSScrollView *scrollView; // Keep reference to scroll view
@property (nonatomic, assign) NSInteger selectedIndex;

@end

@implementation MentionViewController

- (instancetype)init {
    self = [super initWithNibName:nil bundle:nil];
    if (self) {
        _suggestions = @[];
        _selectedIndex = -1; // No selection initially
    }
    return self;
}

- (void)loadView {
    // Create a basic container view
    self.view = [[NSView alloc] initWithFrame:NSMakeRect(0, 0, 250, 150)]; // Example size
    self.view.wantsLayer = YES;
    // self.view.layer.backgroundColor = [NSColor windowBackgroundColor].CGColor; // Use window background for popover

    // --- Loading Indicator ---
    _loadingIndicator = [[NSProgressIndicator alloc] init]; // Frame set by constraints
    _loadingIndicator.style = NSProgressIndicatorStyleSpinning;
    _loadingIndicator.controlSize = NSControlSizeSmall;
    _loadingIndicator.translatesAutoresizingMaskIntoConstraints = NO;
    _loadingIndicator.hidden = YES;
    [self.view addSubview:_loadingIndicator];
    
    // --- Error Label ---
    _errorLabel = [NSTextField labelWithString:@"Error placeholder"];
    _errorLabel.translatesAutoresizingMaskIntoConstraints = NO;
    _errorLabel.textColor = [NSColor secondaryLabelColor];
    _errorLabel.alignment = NSTextAlignmentCenter;
    _errorLabel.lineBreakMode = NSLineBreakByWordWrapping;
    _errorLabel.hidden = YES;
    [self.view addSubview:_errorLabel];

    // --- Table View Setup ---
    _tableView = [[NSTableView alloc] init]; // Frame set by constraints
    _tableView.headerView = nil;
    _tableView.usesAlternatingRowBackgroundColors = YES;
    _tableView.allowsMultipleSelection = NO;
    _tableView.allowsEmptySelection = YES;
    _tableView.gridStyleMask = NSTableViewGridNone;
    _tableView.selectionHighlightStyle = NSTableViewSelectionHighlightStyleRegular;
    _tableView.backgroundColor = [NSColor clearColor]; // Make table background clear
    _tableView.wantsLayer = YES; // Ensure layer for potential styling
    _tableView.rowHeight = 40.0; // Increased row height for our custom cell

    // Single column
    NSTableColumn *column = [[NSTableColumn alloc] initWithIdentifier:@"SuggestionColumn"];
    column.title = @"Suggestions"; // Not visible as header is nil
    column.resizingMask = NSTableColumnAutoresizingMask; 
    [_tableView addTableColumn:column];

    // Set delegate and data source
    _tableView.dataSource = self;
    _tableView.delegate = self;

    // Double-click action
    _tableView.doubleAction = @selector(tableViewDoubleClick:);
    _tableView.target = self;

    // --- Scroll View Setup ---
    _scrollView = [[NSScrollView alloc] init]; // Frame set by constraints
    _scrollView.translatesAutoresizingMaskIntoConstraints = NO;
    _scrollView.documentView = _tableView;
    _scrollView.hasVerticalScroller = YES;
    _scrollView.autohidesScrollers = YES;
    _scrollView.borderType = NSNoBorder; // No border for the scroll view itself
    _scrollView.drawsBackground = NO; // Don't draw scroll view background
    [self.view addSubview:_scrollView];
    
    // --- Constraints --- 
    [NSLayoutConstraint activateConstraints:@[        
        // Loading Indicator centered
        [_loadingIndicator.centerXAnchor constraintEqualToAnchor:self.view.centerXAnchor],
        [_loadingIndicator.centerYAnchor constraintEqualToAnchor:self.view.centerYAnchor],
        
        // Error Label centered, with padding
        [_errorLabel.leadingAnchor constraintEqualToAnchor:self.view.leadingAnchor constant:10],
        [_errorLabel.trailingAnchor constraintEqualToAnchor:self.view.trailingAnchor constant:-10],
        [_errorLabel.centerYAnchor constraintEqualToAnchor:self.view.centerYAnchor],

        // Scroll View filling the view
        [_scrollView.topAnchor constraintEqualToAnchor:self.view.topAnchor],
        [_scrollView.bottomAnchor constraintEqualToAnchor:self.view.bottomAnchor],
        [_scrollView.leadingAnchor constraintEqualToAnchor:self.view.leadingAnchor],
        [_scrollView.trailingAnchor constraintEqualToAnchor:self.view.trailingAnchor],
    ]];

    DBM(@"View loaded with NSTableView");
}

- (void)viewDidLoad {
    [super viewDidLoad];
    // Initial state
    [self updateSuggestions:@[] isLoading:NO];
}

// --- Public Methods (Implementations) ---

- (void)updateSuggestions:(NSArray<MentionSuggestion *> *)suggestions isLoading:(BOOL)isLoading {
    DBM(@"Updating with %lu suggestions, isLoading: %d", (unsigned long)suggestions.count, isLoading);
    self.suggestions = suggestions ?: @[];
    self.selectedIndex = (self.suggestions.count > 0) ? 0 : -1; // Default select first

    dispatch_async(dispatch_get_main_queue(), ^{ 
        self.loadingIndicator.hidden = !isLoading;
        self.errorLabel.hidden = YES;
        self.scrollView.hidden = isLoading; // Show/hide scroll view containing table

        if (isLoading) {
            [self.loadingIndicator startAnimation:nil];
            // Optionally set table view to empty state while loading
            // self.suggestions = @[]; 
            // [self.tableView reloadData];
        } else {
            [self.loadingIndicator stopAnimation:nil];
        }
        
        [self.tableView reloadData];
        
        // Adjust selection after reloading data
        if (self.selectedIndex != -1) {
             [self.tableView selectRowIndexes:[NSIndexSet indexSetWithIndex:self.selectedIndex] byExtendingSelection:NO];
             [self.tableView scrollRowToVisible:self.selectedIndex];
        } else {
             [self.tableView deselectAll:nil]; // Deselect if no items or default selection is -1
        }
        
        // Adjust popover size based on content
        [self updatePreferredContentSize];
    });
}

- (void)showError:(NSString *)errorMessage {
    DBM(@"Displaying error: %@", errorMessage);
    self.suggestions = @[];
    self.selectedIndex = -1;

    dispatch_async(dispatch_get_main_queue(), ^{ 
        self.loadingIndicator.hidden = YES;
        [self.loadingIndicator stopAnimation:nil];
        self.scrollView.hidden = YES; // Hide scroll view on error
        self.errorLabel.hidden = NO;
        self.errorLabel.stringValue = errorMessage ?: @"An unknown error occurred.";
        [self.tableView reloadData]; // Clear table
        
        // Update size for error state
        [self updatePreferredContentSize];
    });
}

- (void)updatePreferredContentSize {
    // Calculate preferred size based on content
    CGFloat width = 300.0; // Default width
    CGFloat height;
    
    if (!self.errorLabel.hidden) {
        // Error state - height based on error label
        CGFloat errorHeight = [self.errorLabel.cell cellSizeForBounds:NSMakeRect(0, 0, width - 20, CGFLOAT_MAX)].height;
        height = errorHeight + 40.0; // Add padding
    } else if (!self.loadingIndicator.hidden) {
        // Loading state
        height = 100.0; // Fixed height for loading
    } else {
        // Suggestions state - height based on number of rows
        NSInteger rowCount = MIN(self.suggestions.count, 6); // Max 6 visible rows
        if (rowCount == 0) rowCount = 1; // At least one row height
        height = rowCount * self.tableView.rowHeight + 10.0; // Add padding
    }
    
    // Set a minimum height
    height = MAX(height, 80.0);
    
    // Update preferred content size
    NSSize preferredSize = NSMakeSize(width, height);
    if (!NSEqualSizes(self.preferredContentSize, preferredSize)) {
        self.preferredContentSize = preferredSize;
        
        // Notify delegate if it's interested in the size change
        // This assumes we add a delegate method for size changes
        if (self.delegate && [self.delegate respondsToSelector:@selector(mentionViewController:didUpdatePreferredContentSize:)]) {
            [self.delegate mentionViewController:self didUpdatePreferredContentSize:preferredSize];
        }
    }
}

- (BOOL)moveSelectionUp {
    if (self.suggestions.count == 0) return NO;
    
    NSInteger newIndex = self.selectedIndex - 1;
    if (newIndex < 0) {
        newIndex = self.suggestions.count - 1; // Wrap around
    }
    
    self.selectedIndex = newIndex;
    DBM(@"Selected index: %ld", (long)self.selectedIndex);
    [self.tableView selectRowIndexes:[NSIndexSet indexSetWithIndex:self.selectedIndex] byExtendingSelection:NO];
    [self.tableView scrollRowToVisible:self.selectedIndex];
    return YES;
}

- (BOOL)moveSelectionDown {
    if (self.suggestions.count == 0) return NO;
    
    NSInteger newIndex = self.selectedIndex + 1;
    if (newIndex >= self.suggestions.count) {
        newIndex = 0; // Wrap around
    }
    
    self.selectedIndex = newIndex;
    DBM(@"Selected index: %ld", (long)self.selectedIndex);
    [self.tableView selectRowIndexes:[NSIndexSet indexSetWithIndex:self.selectedIndex] byExtendingSelection:NO];
    [self.tableView scrollRowToVisible:self.selectedIndex];
    return YES;
}

- (BOOL)confirmSelection {
    if (self.selectedIndex >= 0 && self.selectedIndex < self.suggestions.count) {
        MentionSuggestion *selectedSuggestion = self.suggestions[self.selectedIndex];
        DBM(@"Confirming selection: %@", selectedSuggestion.displayText);
        
        if (self.delegate && [self.delegate respondsToSelector:@selector(mentionViewController:didSelectSuggestion:)]) {
            [self.delegate mentionViewController:self didSelectSuggestion:selectedSuggestion];
            return YES;
        }
    }
    return NO;
}

- (void)resetSelection {
    self.selectedIndex = -1;
    [self.tableView deselectAll:nil];
}

// --- NSTableViewDataSource Methods --- //

- (NSInteger)numberOfRowsInTableView:(NSTableView *)tableView {
    return self.suggestions.count;
}

// --- NSTableViewDelegate Methods --- //

- (NSView *)tableView:(NSTableView *)tableView viewForTableColumn:(NSTableColumn *)tableColumn row:(NSInteger)row {
    if (row < 0 || row >= self.suggestions.count) return nil;
    
    MentionSuggestion *suggestion = self.suggestions[row];
    
    // Use our custom cell view
    MentionSuggestionCellView *cellView = [tableView makeViewWithIdentifier:kMentionCellIdentifier owner:self];
    if (!cellView) {
        // Create a new cell view if one doesn't exist
        cellView = [[MentionSuggestionCellView alloc] initWithFrame:NSMakeRect(0, 0, tableColumn.width, self.tableView.rowHeight)];
        cellView.identifier = kMentionCellIdentifier;
    }
    
    // Configure the cell with the suggestion data
    [cellView configureCellWithSuggestion:suggestion];
    
    return cellView;
}

- (void)tableViewSelectionDidChange:(NSNotification *)notification {
    NSInteger selectedRow = self.tableView.selectedRow;
    if (selectedRow != -1 && selectedRow < self.suggestions.count) {
        self.selectedIndex = selectedRow;
        DBM(@"Table selection changed to index: %ld", (long)self.selectedIndex);
    } else {
        self.selectedIndex = -1;
    }
}

// Handle double-click to confirm selection
- (void)tableViewDoubleClick:(id)sender {
     if (self.tableView.clickedRow != -1) {
         self.selectedIndex = self.tableView.clickedRow;
         [self confirmSelection];
     }
}

@end 