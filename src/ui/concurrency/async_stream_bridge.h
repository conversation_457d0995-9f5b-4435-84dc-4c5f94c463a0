#pragma once

#import <Foundation/Foundation.h>

#ifdef __cplusplus
#include "core/util/stream_generator.h"
#include "core/http/sse_decoder.h"
#include "utilities/cancellation_token.h"
#endif

NS_ASSUME_NONNULL_BEGIN

typedef void (^DeltaChunkBlock)(NSString * _Nonnull text, B<PERSON><PERSON> done);
typedef void (^StreamErrorBlock)(NSError * _Nonnull error);

@interface DeltaStreamHandle : NSObject
- (void)cancel;
@end

#ifdef __cplusplus
namespace launcher { namespace ui { namespace bridge {

DeltaStreamHandle * consumeDeltaStream(::launcher::core::util::AsyncStream<http::Delta> stream,
                                       std::shared_ptr<launcher::core::utilities::CancellationToken> token,
                                       DeltaChunkBlock chunkHandler,
                                       StreamErrorBlock errorHandler);

}}}
#endif

NS_ASSUME_NONNULL_END 