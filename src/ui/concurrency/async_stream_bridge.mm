#import "async_stream_bridge.h"

#ifdef __cplusplus
#include <thread>
#include <atomic>
#import <dispatch/dispatch.h>
#include <vector>
#include <chrono>
#endif

@interface DeltaStreamHandle () {
#ifdef __cplusplus
    std::shared_ptr<launcher::core::utilities::CancellationToken> _token;
#endif
}
@end

@implementation DeltaStreamHandle
#ifdef __cplusplus
- (instancetype)initWithToken:(std::shared_ptr<launcher::core::utilities::CancellationToken>)token {
    if (self = [super init]) {
        _token = std::move(token);
    }
    return self;
}
#endif
- (void)cancel {
#ifdef __cplusplus
    if (_token) _token->cancel();
#endif
}
@end

@implementation NSObject (AsyncStreamBridge)
@end

#ifdef __cplusplus
namespace launcher { namespace ui { namespace bridge {

DeltaStreamHandle * consumeDeltaStream(::launcher::core::util::AsyncStream<http::Delta> stream,
                                       std::shared_ptr<launcher::core::utilities::CancellationToken> token,
                                       DeltaChunkBlock chunkHandler,
                                       StreamErrorBlock errorHandler) {
    // Run consumer on background thread to avoid blocking main.
    std::thread([s = std::move(stream), chunkHandler, errorHandler, token]() mutable {
        const int kBatchLimit = 8;
        const std::chrono::milliseconds kFlushInterval(10);
        std::vector<std::string> batch;
        batch.reserve(kBatchLimit);
        auto flush = [&](BOOL doneFlag) {
            if (batch.empty()) return;
            // make copy for dispatch
            std::vector<std::string> toSend;
            toSend.swap(batch);
            dispatch_async(dispatch_get_main_queue(), ^{
                for (size_t i=0;i<toSend.size();++i) {
                    const auto &str = toSend[i];
                    BOOL isLast = (i == toSend.size()-1) && doneFlag;
                    chunkHandler([NSString stringWithUTF8String:str.c_str()] ?: @"", isLast);
                }
            });
        };
        auto lastFlush = std::chrono::steady_clock::now();
        while (true) {
            if (token->isCancelled()) {
                flush(YES);
                break;
            }
            auto opt = s.waitNext(token);
            if (!opt.has_value()) {
                flush(NO);
                break;
            }
            const auto &delta = *opt;
            batch.emplace_back(delta.content.data(), delta.content.size());
            if (batch.size() >= kBatchLimit || delta.done) {
                flush(delta.done);
                lastFlush = std::chrono::steady_clock::now();
            } else {
                auto now = std::chrono::steady_clock::now();
                if (now - lastFlush >= kFlushInterval) {
                    flush(NO);
                    lastFlush = now;
                }
            }
            if (delta.done) break;
        }
        flush(NO);
        if (auto errOpt = s.error()) {
            dispatch_async(dispatch_get_main_queue(), ^{
                NSError *nsErr = [NSError errorWithDomain:@"StreamError" code:errOpt->status_code userInfo:@{NSLocalizedDescriptionKey: [NSString stringWithUTF8String: errOpt->message.c_str()]}];
                errorHandler(nsErr);
            });
        }
    }).detach();

    return [[DeltaStreamHandle alloc] initWithToken:token];
}

}}}
#endif 