#import "concurrency_manager.h"
#include "../../core/util/debug.h"

@implementation ConcurrencyManager

+ (instancetype)sharedManager {
    static ConcurrencyManager *sharedInstance = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        sharedInstance = [[self alloc] initInternal];
        DBM(@"Shared instance created.");
    });
    return sharedInstance;
}

// Private designated initializer
- (instancetype)initInternal {
    self = [super init];
    if (self) {
        // Debouncing Queue: Serial (maxConcurrentOperationCount = 1)
        _debouncingQueue = [[NSOperationQueue alloc] init];
        _debouncingQueue.name = @"com.yourapp.DebouncingQueue";
        _debouncingQueue.maxConcurrentOperationCount = 1;
        _debouncingQueue.qualityOfService = NSQualityOfServiceUserInitiated;

        // UI Background Queue: Concurrent, limited
        _uiBackgroundQueue = [[NSOperationQueue alloc] init];
        _uiBackgroundQueue.name = @"com.yourapp.UIBackgroundQueue";
        // Limit concurrency to avoid resource exhaustion - adjust as needed
        _uiBackgroundQueue.maxConcurrentOperationCount = MAX(1, (NSInteger)[[NSProcessInfo processInfo] activeProcessorCount] / 2);
        _uiBackgroundQueue.qualityOfService = NSQualityOfServiceUtility;

        DBM(@"Queues initialized (Debounce: serial, UI Background: %ld concurrent)", (long)_uiBackgroundQueue.maxConcurrentOperationCount);
    }
    return self;
}

@end 