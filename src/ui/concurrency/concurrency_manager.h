#import <Foundation/Foundation.h>
#import <AppKit/AppKit.h> // For NSOperationQueue

NS_ASSUME_NONNULL_BEGIN

/**
 * @class ConcurrencyManager
 * @brief Manages shared NSOperationQueues for UI-related concurrent tasks.
 *
 * Provides shared queues for tasks like debouncing UI input, scheduling
 * short background UI updates, etc. These are separate from the backend GCD queues.
 */
@interface ConcurrencyManager : NSObject

/**
 * @brief A shared serial queue suitable for debouncing operations
 *        where only the latest task should execute.
 */
@property (nonatomic, strong, readonly) NSOperationQueue *debouncingQueue;

/**
 * @brief A shared concurrent queue for short, non-critical UI-related background tasks.
 *        Limit the concurrency level to avoid overwhelming the system.
 */
@property (nonatomic, strong, readonly) NSOperationQueue *uiBackgroundQueue;

/**
 * @brief Singleton instance for accessing the shared queues.
 */
+ (instancetype)sharedManager;

// Make init private for singleton
- (instancetype)init NS_UNAVAILABLE;
+ (instancetype)new NS_UNAVAILABLE;

@end

NS_ASSUME_NONNULL_END 