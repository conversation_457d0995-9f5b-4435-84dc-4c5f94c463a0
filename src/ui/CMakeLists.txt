# Create UI library
add_library(ui STATIC
    concurrency/concurrency_manager.mm
    concurrency/async_stream_bridge.mm
    chat/macos_chat_ui.mm
    chat/ComposerView.mm
)

# Include directories
target_include_directories(ui
    PUBLIC
        ${CMAKE_CURRENT_SOURCE_DIR}
    PRIVATE
        ${cmark_SOURCE_DIR}/src
        ${cmark_BINARY_DIR}/src
)

# Add platform-specific sources and libraries
if(APPLE)
    target_sources(ui PRIVATE
        macos/macos_ui_core.mm
        macos/macos_ui_app_delegate.mm
        macos/macos_ui_views.mm
        macos/preferences_window_controller.mm
        macos/browser_history_importer.mm
        macos/application_visibility_manager.mm
        macos/menu_bar_manager.mm
        macos/controllers/models_pane_controller.mm
        macos/general_pane_controller.mm
        macos/tab_switcher_controller.mm
        macos/hud_window_utils.mm
        macos/resources/AppIcon.m
        common/message_formatter.mm
        chat/macos_chat_ui.mm
        chat/ComposerView.mm
        chat/ChatViewController.mm
        chat/MessageView.mm
        concurrency/async_stream_bridge.mm
    )
    
    # Set Objective-C++ properties for .mm files
    set_source_files_properties(
        macos/macos_ui_core.mm
        macos/macos_ui_app_delegate.mm
        macos/macos_ui_views.mm
        macos/preferences_window_controller.mm
        macos/browser_history_importer.mm
        macos/application_visibility_manager.mm
        macos/menu_bar_manager.mm
        macos/controllers/models_pane_controller.mm
        macos/general_pane_controller.mm
        macos/tab_switcher_controller.mm
        macos/hud_window_utils.mm
        common/message_formatter.mm
        chat/macos_chat_ui.mm
        chat/ComposerView.mm
        chat/ChatViewController.mm
        chat/MessageView.mm
        concurrency/async_stream_bridge.mm
        PROPERTIES
        COMPILE_FLAGS "-x objective-c++"
    )
    
    # Set language property for Objective-C files
    set_source_files_properties(
        macos/resources/AppIcon.m
        PROPERTIES
        LANGUAGE C
        COMPILE_FLAGS "-x objective-c"
    )
    
    find_library(APPKIT_LIBRARY AppKit REQUIRED)
    find_library(FOUNDATION_LIBRARY Foundation REQUIRED)
    find_library(QUARTZCORE_LIBRARY QuartzCore REQUIRED)
    find_library(SECURITY_LIBRARY Security REQUIRED)
    find_library(CARBON_LIBRARY Carbon REQUIRED)
    
    # Find SQLite3
    find_package(PkgConfig QUIET)
    if(PkgConfig_FOUND)
        pkg_check_modules(SQLITE3 QUIET sqlite3)
    endif()
    
    if(NOT SQLITE3_FOUND)
        find_path(SQLITE3_INCLUDE_DIR sqlite3.h)
        find_library(SQLITE3_LIBRARY sqlite3)
        if(SQLITE3_INCLUDE_DIR AND SQLITE3_LIBRARY)
            set(SQLITE3_FOUND TRUE)
            set(SQLITE3_LIBRARIES ${SQLITE3_LIBRARY})
            set(SQLITE3_INCLUDE_DIRS ${SQLITE3_INCLUDE_DIR})
        endif()
    endif()
    
    if(NOT SQLITE3_FOUND)
        message(FATAL_ERROR "SQLite3 not found")
    endif()
    
    target_include_directories(ui PRIVATE ${SQLITE3_INCLUDE_DIRS})
    target_link_libraries(ui PRIVATE 
        ${APPKIT_LIBRARY} 
        ${FOUNDATION_LIBRARY} 
        ${QUARTZCORE_LIBRARY} 
        ${SECURITY_LIBRARY}
        ${CARBON_LIBRARY}
        ${SQLITE3_LIBRARIES}
        "-framework UniformTypeIdentifiers"
    )
endif()

# Link with dependencies
target_link_libraries(ui
    PRIVATE
        core
        nlohmann_json::nlohmann_json
        ui_chat
)

if(APPLE)
    target_link_libraries(ui
        PRIVATE
            mas_shortcut
            ui_macos
    )
endif()

# Add subdirectories
# common subdirectory now header-only; no build required
if(APPLE)
    add_subdirectory(macos)
endif()
add_subdirectory(chat) 