#include "icon_loader.h"

#import "resources/AppIcon.h" // contains createAppIcon + embedded binary

#import <AppKit/AppKit.h>

namespace {
// Internal helper to load icon data embedded in the binary.
NSImage *ImageFromEmbeddedData() {
    NSData *iconData = [NSData dataWithBytes:AppIconData length:AppIconDataLength];
    if (!iconData || iconData.length == 0) { return nil; }
    return [[NSImage alloc] initWithData:iconData];
}
}

NSImage *LoadAppIcon(void) {
    static NSImage *cached = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        // 1. Try main bundle .icns
        NSString *icnsPath = [[NSBundle mainBundle] pathForResource:@"AppIcon" ofType:@"icns"];
        if (icnsPath) {
            cached = [[NSImage alloc] initWithContentsOfFile:icnsPath];
        }

        // 2. Fallback to resources/AppIcon.icns (support tests / dev bundles)
        if (!cached) {
            NSString *resDir = [[[NSBundle mainBundle] resourcePath] stringByAppendingPathComponent:@"resources"];
            NSString *altIcns = [resDir stringByAppendingPathComponent:@"AppIcon.icns"];
            if ([[NSFileManager defaultManager] fileExistsAtPath:altIcns]) {
                cached = [[NSImage alloc] initWithContentsOfFile:altIcns];
            }
        }

        // 3. Embedded binary data
        if (!cached) {
            cached = ImageFromEmbeddedData();
        }

        // 4. Programmatic generation (multi-resolution fallback)
        if (!cached) {
            cached = [[NSImage alloc] initWithSize:NSMakeSize(128, 128)];
            NSArray<NSNumber *> *sizes = @[ @16, @32, @64, @128, @256, @512 ];
            for (NSNumber *sz in sizes) {
                NSImage *icon = createAppIcon(sz.doubleValue);
                if (icon.representations.count > 0) {
                    [cached addRepresentation:icon.representations.firstObject];
                }
            }
        }
    });
    return cached;
}

NSImage *LoadMenuBarIcon(void) {
    static NSImage *cached = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        // 1. Try bundle PNG specifically sized for menu bar
        NSString *pngPath = [[NSBundle mainBundle] pathForResource:@"AppIcon" ofType:@"png"];
        if (pngPath) {
            cached = [[NSImage alloc] initWithContentsOfFile:pngPath];
        }

        // 2. Fallback to app icon scaled down
        if (!cached) {
            cached = [LoadAppIcon() copy];
        }

        // 3. Embedded binary data
        if (!cached) {
            cached = ImageFromEmbeddedData();
        }

        // 4. Generated placeholder
        if (!cached) {
            cached = createAppIcon(18);
        }

        // Ensure consistent size for status-bar usage
        [cached setSize:NSMakeSize(18, 18)];
    });
    return cached;
} 