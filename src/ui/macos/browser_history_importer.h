#pragma once

#include <future>
#include <map>
#include <mutex>
#include <string>
#include <unordered_map>
#include <vector>
#include "../../core/interfaces/ibrowser_history_importer.h"

namespace launcher {
namespace ui {

// ---------------------------------------------------------------------------
// Public data structure shared via interface
// ---------------------------------------------------------------------------

struct WebsiteEntry {
    std::string url;       // Full URL
    std::string title;     // Page title
    std::string domain;    // Domain name
    int visitCount;        // Number of visits
    double lastVisitTime;  // Last visit timestamp (epoch seconds)
    bool isSuggestion;     // Whether this is a search suggestion result
    std::string imageUrl;  // Optional preview image URL

    WebsiteEntry() : visitCount(0), lastVisitTime(0), isSuggestion(false) {}

    WebsiteEntry(const std::string& u,
                 const std::string& t,
                 const std::string& d,
                 int vc,
                 double lvt,
                 bool suggestion = false,
                 const std::string& img = "")
        : url(u), title(t), domain(d), visitCount(vc), lastVisitTime(lvt),
          isSuggestion(suggestion), imageUrl(img) {}
};

/**
 * @brief Class to handle importing browser history from various browsers
 */
class BrowserHistoryImporter : public IBrowserHistoryImporter {
 public:
    // -----------------------------------------------------------------
    // Construction – instantiate and inject where required.
    // -----------------------------------------------------------------
    BrowserHistoryImporter();
    ~BrowserHistoryImporter();

    /**
     * @brief Import history from all supported browsers
     * @return Number of entries imported
     */
    int importAllBrowserHistory() override;

    /**
     * @brief Continue importing history after permission request
     * @return Number of entries imported
     */
    int continueImportAllBrowserHistory() override;

    /**
     * @brief Import history from Safari
     * @return Number of entries imported
     */
    int importSafariHistory() override;

    /**
     * @brief Import history from Chrome
     * @return Number of entries imported
     */
    int importChromeHistory() override;

    /**
     * @brief Import history from Firefox
     * @return Number of entries imported
     */
    int importFirefoxHistory() override;

    /**
     * @brief Search for websites matching the query
     * @param query Search query
     * @param maxResults Maximum number of results to return
     * @return Vector of matching website entries
     */
    std::vector<launcher::ui::WebsiteEntry> searchWebsites(const std::string& query, int maxResults = 20) override;

    /**
     * @brief Fetch search engine suggestions for a query from Google
     * @param query Search query
     * @param maxResults Maximum number of results to return
     * @return Vector of suggested website entries
     */
    std::vector<launcher::ui::WebsiteEntry> fetchGoogleSuggestions(const std::string& query, int maxResults = 5);

    /**
     * @brief Fetch search engine suggestions for a query from Bing
     * @param query Search query
     * @param maxResults Maximum number of results to return
     * @return Vector of suggested website entries
     */
    std::vector<launcher::ui::WebsiteEntry> fetchBingSuggestions(const std::string& query, int maxResults = 5);

    /**
     * @brief Fetch search suggestions from multiple engines in parallel
     * @param query Search query
     * @param maxResults Maximum number of results to return per engine
     * @return Vector of suggested website entries
     */
    std::vector<launcher::ui::WebsiteEntry> fetchSearchSuggestions(const std::string& query, int maxResults = 5);

    /**
     * @brief Get the total number of website entries
     * @return Total number of website entries
     */
    size_t getTotalEntries() const override;

    /**
     * @brief Get most recent website entries sorted by last visit time.
     * @param maxCount Maximum number of entries to return
     * @return Vector of WebsiteEntry
     */
    std::vector<launcher::ui::WebsiteEntry> getTopEntries(size_t maxCount = 20) const override;

    /**
     * @brief Clear all imported history
     */
    void clearHistory();

    /**
     * @brief Calculate score for a website based on visit count and recency
     * @param entry Website entry
     * @return Score value
     */
    double calculateScore(const launcher::ui::WebsiteEntry& entry);

    /**
     * @brief Reset the permission request flag to show the dialog again
     */
    void resetPermissionRequestFlag() override;

 private:
    // Prevent copying
    BrowserHistoryImporter(const BrowserHistoryImporter&) = delete;
    BrowserHistoryImporter& operator=(const BrowserHistoryImporter&) = delete;

    /**
     * @brief Extract domain from URL
     * @param url Full URL
     * @return Domain name
     */
    std::string extractDomain(const std::string& url);

    /**
     * @brief Clean special characters from suggestion text
     * @param text Text to clean
     * @return Cleaned text
     */
    std::string cleanSuggestionText(const std::string& text);

    /**
     * @brief Add a website entry to the index
     * @param entry Website entry to add
     */
    void addWebsiteEntry(const launcher::ui::WebsiteEntry& entry);

    /**
     * @brief Perform HTTP request to fetch search suggestions
     * @param url API endpoint URL
     * @param headers Optional HTTP headers to include in the request
     * @return Response string
     */
    std::string performHttpRequest(const std::string& url,
                                   const std::map<std::string, std::string>& headers = {});

    std::vector<launcher::ui::WebsiteEntry> websiteEntries;
    std::unordered_map<std::string, size_t> urlToIndexMap;
    mutable std::mutex mutex;
    bool initialized;
};

}  // namespace ui
}  // namespace launcher