#pragma once

#import <Foundation/Foundation.h>

@class ResultModel;

NS_ASSUME_NONNULL_BEGIN

@interface RerankBridge : NSObject

/// Returns a new array of ResultModel objects reordered by the contextual
/// bandit reranker. If reranker is disabled or fails, returns the original
/// ordering.
+ (NSArray<ResultModel *> *)rerankResults:(NSArray<ResultModel *> *)results;

/// Record that the user opened/selected a particular result. This allows
/// the online learner to receive positive reward feedback.
+ (void)recordSelection:(ResultModel *)result;

/// Inform the learner that these results were shown (impressions). Should be
/// called immediately after presenting results to the user.
+ (void)recordImpressions:(NSArray<ResultModel *> *)results;

/// Flush any impressions from previous query (assigning 0 reward).
+ (void)flushPendingImpressions;

@end

NS_ASSUME_NONNULL_END 