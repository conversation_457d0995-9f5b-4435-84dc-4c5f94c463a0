// SearchResultsAdapter implementation
#import "search_results_adapter.h"

#import "../macos_ui_internal.h"   // Access AppDelegate properties & ResultsDelegate
#import <AppKit/AppKit.h>

#if __has_include(<AppKit/NSTableViewDiffableDataSource.h>)
#import <AppKit/NSTableViewDiffableDataSource.h>
#endif

#import "result_model.h"
#import <float.h>

// -----------------------------------------------------------------------------
@interface SearchResultsAdapter () <SearchCoordinatorDelegate>
@property(nonatomic, weak) AppDelegate *appDelegate;
@end

@implementation SearchResultsAdapter

- (instancetype)initWithAppDelegate:(AppDelegate *)appDelegate {
    self = [super init];
    if (self) {
        _appDelegate = appDelegate;
    }
    return self;
}

#pragma mark - SearchCoordinatorDelegate

- (void)searchCoordinatorDidUpdateResults:(NSArray<ResultModel *> *)results {
    BOOL restrictToChat    = [self.appDelegate.searchField.stringValue hasPrefix:@"/c"];

    NSMutableArray *dicts = [NSMutableArray arrayWithCapacity:results.count];
    for (ResultModel *model in results) {
        if (restrictToChat && ![model.type isEqualToString:@"chat"]) { continue; }
        [dicts addObject:[model toDictionary]];
    }

    // ---- Virtual Chat row ---------------------------------------------------
    static const double kLowScoreThreshold = 0.9;
    NSString *rawQuery = self.appDelegate.searchField.stringValue ?: @"";

    BOOL poorResults = dicts.count == 0;
    if (!poorResults) {
        NSDictionary *first = dicts.firstObject;
        double topScore = [first[@"score"] doubleValue];
        if (topScore < kLowScoreThreshold) { poorResults = YES; }
    }

    if (poorResults && !restrictToChat && rawQuery.length > 10) {
        NSDictionary *chatRow = @{ @"identifier": [NSString stringWithFormat:@"chat_query-%@", rawQuery],
                                   @"name":       rawQuery,
                                   @"path":       @"",
                                   @"iconPath":   @"",
                                   @"score":      @(DBL_MAX),
                                   @"type":       @"chat_query",
                                   @"question":   rawQuery };
        [dicts insertObject:chatRow atIndex:0];
    }

    // Update backing store for cell provider
    self.appDelegate.resultsDelegate.results = dicts;

#if __has_include(<AppKit/NSTableViewDiffableDataSource.h>)
    if (@available(macOS 11.0, *)) {
        NSMutableArray<NSString *> *identifiers = [NSMutableArray arrayWithCapacity:dicts.count];
        for (NSDictionary *d in dicts) {
            NSString *identifier = d[@"identifier"] ?: [NSString stringWithFormat:@"%@-%@", d[@"name"], d[@"path"]];
            [identifiers addObject:identifier];
        }
        NSDiffableDataSourceSnapshot<NSNumber *, NSString *> *snap = [[NSDiffableDataSourceSnapshot alloc] init];
        [snap appendSectionsWithIdentifiers:@[@0]];
        [snap appendItemsWithIdentifiers:identifiers intoSectionWithIdentifier:@0];
        if (self.appDelegate.diffableDataSource) {
            [self.appDelegate.diffableDataSource applySnapshot:snap animatingDifferences:NO];
        } else {
            [self.appDelegate.resultsTable reloadData];
        }
    } else {
        [self.appDelegate.resultsTable reloadData];
    }
#else
    [self.appDelegate.resultsTable reloadData];
#endif

    // Auto-select first row
    if (dicts.count > 0) {
        [self.appDelegate.resultsTable selectRowIndexes:[NSIndexSet indexSetWithIndex:0] byExtendingSelection:NO];
    }

    // Hide progress indicator
    [self.appDelegate showSearchInProgress:NO];
}

@end 