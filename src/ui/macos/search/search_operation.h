#pragma once

#import <Foundation/Foundation.h>

@class ResultModel;

/// Abstract base `NSOperation` subclass that performs a domain specific search.
@interface SearchOperation : NSOperation

@property(nonatomic, copy, readonly) NSString *query;
@property(nonatomic, assign, readonly) NSUInteger generation;

/// Results collected by the operation. Valid only after `isFinished == YES`.
@property(nonatomic, strong) NSArray<ResultModel *> *results;

/// Optional handler for incremental delta results (main-thread not guaranteed).
@property(nonatomic, copy) void (^incrementalHandler)(NSArray<ResultModel *> *delta);

- (instancetype)initWithQuery:(NSString *)query generation:(NSUInteger)generation;

@end 