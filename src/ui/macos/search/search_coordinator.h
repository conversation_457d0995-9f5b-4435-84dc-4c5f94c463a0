#pragma once

#import <Foundation/Foundation.h>

@class ResultModel;

NS_ASSUME_NONNULL_BEGIN

// Delegate that receives incremental snapshots of aggregated search results.
@protocol SearchCoordinatorDelegate <NSObject>
/// Called on the main thread whenever a new snapshot of results is available.
- (void)searchCoordinatorDidUpdateResults:(NSArray<ResultModel *> *)results;
@end

/// Central entry-point for reactive search across all domains (apps, files, websites).
@interface SearchCoordinator : NSObject

/// Singleton accessor.
@property(class, nonatomic, readonly) SearchCoordinator *shared;

/// The delegate that receives result snapshots. Weak on purpose to avoid retain cycles.
@property(nonatomic, weak) id<SearchCoordinatorDelegate> delegate;

/// Submits a new user query. Debounced and generation-checked internally.
- (void)submitQuery:(NSString *)query;

/// Cancels all outstanding search work and clears internal state.
- (void)cancelAll;

@end

NS_ASSUME_NONNULL_END 