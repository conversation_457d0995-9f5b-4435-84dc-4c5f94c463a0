#import "rerank_bridge.h"

#import "result_model.h"

#include <numeric>
#include <vector>
#include <algorithm>

#include "rerank_service.h"

using namespace ranking;

@implementation RerankBridge

+ (NSArray<ResultModel *> *)rerankResults:(NSArray<ResultModel *> *)results {
    if (results.count == 0) { return results; }

    std::vector<RerankService::Item> items;
    items.reserve(results.count);
    for (ResultModel *m in results) {
        std::string path = m.path ? [m.path UTF8String] : "";
        std::string type = m.type ? [m.type UTF8String] : "";
        double baseScore = m.score;
        items.push_back({path, type, baseScore});
    }

    std::vector<double> scores = RerankService::shared().scoreItems(items);

    // Build indices 0..n-1
    std::vector<size_t> indices(scores.size());
    std::iota(indices.begin(), indices.end(), 0);

    // Sort by score descending
    std::sort(indices.begin(), indices.end(), [&](size_t a, size_t b) {
        return scores[a] > scores[b];
    });

    NSMutableArray<ResultModel *> *ordered = [NSMutableArray arrayWithCapacity:results.count];
    for (size_t idx : indices) {
        [ordered addObject:results[idx]];
    }
    return [ordered copy];
}

+ (void)recordSelection:(ResultModel *)result {
    if (!result) return;
    std::string path = result.path ? [result.path UTF8String] : "";
    std::string type = result.type ? [result.type UTF8String] : "";
    RerankService::shared().recordReward({path, type, result.score}, 1.0);
}

+ (void)recordImpressions:(NSArray<ResultModel *> *)results {
    if (results.count == 0) return;
    for (ResultModel *m in results) {
        std::string path = m.path ? [m.path UTF8String] : "";
        std::string type = m.type ? [m.type UTF8String] : "";
        RerankService::shared().recordImpression({path, type, m.score});
    }
}

+ (void)flushPendingImpressions {
    RerankService::shared().flushPendingImpressions();
}

@end 