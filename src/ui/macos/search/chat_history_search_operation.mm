#import "chat_history_search_operation.h"
#import "result_model.h"

#include <filesystem>
#include <fstream>
#include <string>
#include <vector>
#include <algorithm>
#include <nlohmann/json.hpp>

#include "../../core/util/debug.h"

#import <Foundation/Foundation.h>

#include <cctype>
#include <cmath>

namespace fs = std::filesystem;

@implementation ChatHistorySearchOperation

- (void)main {
    if (self.isCancelled) { return; }

    // Determine query & mode
    std::string q = [self.query UTF8String];
    bool showAll = std::all_of(q.begin(), q.end(), [](unsigned char c) {
        return std::isspace(c);
    });
    std::string qLower;
    qLower.reserve(q.size());
    std::transform(q.begin(), q.end(), std::back_inserter(qLower), [](unsigned char c){ return std::tolower(c); });

    // Locate conversations directory – mirror ConversationStore::applicationSupportDir logic.
    fs::path convDir;
#ifdef __APPLE__
    const char *home = getenv("HOME");
    if (!home) {
        self.results = @[];
        return;
    }
    convDir = fs::path(home) / "Library" / "Application Support" / "MicroLauncher" / "Conversations";
#else
    convDir = fs::path("Conversations");
#endif
    if (!fs::exists(convDir)) {
        self.results = @[];
        return;
    }

    // Collect .json files
    std::vector<fs::directory_entry> files;
    for (const auto &entry : fs::directory_iterator(convDir)) {
        if (entry.is_regular_file() && entry.path().extension() == ".json") {
            files.push_back(entry);
        }
    }

    // Sort by modification date descending for quick limit
    std::sort(files.begin(), files.end(), [](const fs::directory_entry &a, const fs::directory_entry &b){
        return fs::last_write_time(a) > fs::last_write_time(b);
    });

    NSMutableArray<ResultModel *> *arr = [NSMutableArray array];

    const size_t kMaxFiles = 200; // safety cap
    size_t processed = 0;

    for (const auto &entry : files) {
        if (self.isCancelled) return;
        if (++processed > kMaxFiles) break;

        // Parse JSON file
        std::ifstream ifs(entry.path());
        if (!ifs.is_open()) continue;
        nlohmann::json j;
        try {
            ifs >> j;
        } catch (...) {
            continue; // skip bad file
        }

        // Conversation id (filename)
        std::string convId = entry.path().stem().string();

        // Determine custom icon + tint from metadata (if available)
        NSString *iconSymbol = nil;
        NSString *tintHex    = nil;
        if (j.contains("meta") && j["meta"].is_object()) {
            const auto &meta = j["meta"];
            if (meta.contains("icon_symbol") && meta["icon_symbol"].is_string()) {
                std::string sym = meta["icon_symbol"].get<std::string>();
                iconSymbol = @(sym.c_str());
            }
            if (meta.contains("tint") && meta["tint"].is_array() && meta["tint"].size() == 4) {
                // Convert RGBA components (0..1 floats) to #RRGGBBAA string.
                auto clamp = [](double v) { return std::max(0.0, std::min(1.0, v)); };
                double r = clamp(meta["tint"][0].get<double>());
                double g = clamp(meta["tint"][1].get<double>());
                double b = clamp(meta["tint"][2].get<double>());
                double a = clamp(meta["tint"][3].get<double>());
                int ri = static_cast<int>(std::round(r * 255.0));
                int gi = static_cast<int>(std::round(g * 255.0));
                int bi = static_cast<int>(std::round(b * 255.0));
                int ai = static_cast<int>(std::round(a * 255.0));
                tintHex = [NSString stringWithFormat:@"#%02X%02X%02X%02X", ri, gi, bi, ai];
            }
        }

        // Determine title
        std::string title;
        if (j.contains("meta") && j["meta"].contains("title") && j["meta"]["title"].is_string()) {
            title = j["meta"]["title"].get<std::string>();
        }
        if (title.empty() && j.contains("messages") && j["messages"].is_array() && !j["messages"].empty()) {
            // Use first user message as fallback
            for (const auto &m : j["messages"]) {
                if (m.value("role", "") == "user") {
                    title = m.value("content", "");
                    break;
                }
            }
        }
        if (title.empty()) title = "Chat";

        // Determine last user message (for quick reopen)
        std::string lastUser;
        if (j.contains("messages") && j["messages"].is_array()) {
            for (auto it = j["messages"].rbegin(); it != j["messages"].rend(); ++it) {
                if ((*it).value("role", "") == "user") {
                    lastUser = (*it).value("content", "");
                    break;
                }
            }
        }

        // Compute score
        int score = 0;
        if (showAll) {
            score = 50;
        } else {
            std::string titleLower = title; std::transform(titleLower.begin(), titleLower.end(), titleLower.begin(), ::tolower);
            bool matched = false;
            if (titleLower.find(qLower) != std::string::npos) { score += 100; matched = true; }
            if (!matched && j.contains("messages") && j["messages"].is_array()) {
                size_t limit = 10; size_t count = 0;
                for (auto it = j["messages"].rbegin(); it != j["messages"].rend() && count < limit; ++it, ++count) {
                    std::string content = (*it).value("content", "");
                    std::string contentLower = content; std::transform(contentLower.begin(), contentLower.end(), contentLower.begin(), ::tolower);
                    if (contentLower.find(qLower) != std::string::npos) { score += 75; break; }
                }
            }
            if (score == 0) continue; // no match
        }

        // Simple recency boost using file order (already sorted by mtime)
        int recency = std::max(0, 40 - static_cast<int>(processed));
        score += recency;

        double normScore = std::min(0.02 + score / 100.0, 0.95);

        NSString *nsTitle = @(title.c_str());
        NSString *nsSnippet = @(lastUser.c_str());
        // Use bare conversation id as stable identifier (no "chat-" prefix).
        NSString *identifier = @(convId.c_str());

        NSDictionary *dict = @{ @"identifier": identifier,
                                 @"name": nsTitle ?: @"Chat",
                                 @"path": nsSnippet ?: @"",
                                 @"iconPath": @"",
                                 @"score": @(normScore),
                                 @"type": @"chat",
                                 @"question": nsSnippet ?: @"",
                                 @"iconSymbol": iconSymbol ?: @"",
                                 @"tintHex": tintHex ?: @"" };
        [arr addObject:[[ResultModel alloc] initWithDictionary:dict]];
    }

    // Sort by score desc
    [arr sortUsingComparator:^NSComparisonResult(ResultModel *a, ResultModel *b) {
        return a.score > b.score ? NSOrderedAscending : NSOrderedDescending;
    }];

    if (arr.count > 30) {
        arr = [NSMutableArray arrayWithArray:[arr subarrayWithRange:NSMakeRange(0, 30)]];
    }

    self.results = arr;
}

@end 