// SearchResultsAdapter – converts reactive search ResultModel snapshots into
// dictionaries + applies them to the NSTableView via ResultsTableDelegate or
// diffable-datasource.
#pragma once

#import <Foundation/Foundation.h>

@class AppDelegate;
@class ResultModel;

NS_ASSUME_NONNULL_BEGIN

@interface SearchResultsAdapter : NSObject

- (instancetype)initWithAppDelegate:(AppDelegate *)appDelegate NS_DESIGNATED_INITIALIZER;
- (instancetype)init NS_UNAVAILABLE;

@end

NS_ASSUME_NONNULL_END 