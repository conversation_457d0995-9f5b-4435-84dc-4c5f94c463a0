#pragma once

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

/// Value-object representing a single launcher search result.
@interface ResultModel : NSObject <NSCopying>

@property(nonatomic, copy, readonly) NSString *identifier; // unique id (name+path or generated)
@property(nonatomic, copy, readonly) NSString *name;
@property(nonatomic, copy, readonly) NSString *path;
@property(nonatomic, copy, readonly) NSString *iconPath;
@property(nonatomic, readonly) double score;
@property(nonatomic, copy, readonly) NSString *type;
@property(nonatomic, copy, readonly) NSString *actionId;
@property(nonatomic, readonly) BOOL requiresApproval;
@property(nonatomic, copy, readonly) NSString *iconSymbol; // optional SF symbol name for custom icon
@property(nonatomic, copy, readonly) NSString *tintHex;    // optional hex colour (e.g. #RRGGBBAA)

- (instancetype)initWithDictionary:(NSDictionary *)dict;
- (NSDictionary *)toDictionary;

@end

NS_ASSUME_NONNULL_END 