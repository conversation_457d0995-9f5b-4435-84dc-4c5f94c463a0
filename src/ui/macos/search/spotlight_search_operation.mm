#import "spotlight_search_operation.h"
#import <Foundation/Foundation.h>
#import <AppKit/AppKit.h>
#import <objc/message.h>
#import "result_model.h"

@interface SpotlightSearchOperation () {
    NSPredicate *_predicate;
    NSString *_resultType;
}

@property(nonatomic, strong) NSMetadataQuery *metadataQuery;
@property(nonatomic, strong) NSMutableArray<ResultModel *> *accumResults;
@property(nonatomic, strong) NSMutableSet<NSString *> *seenPaths;
@property(nonatomic, assign) BOOL executing;
@property(nonatomic, assign) BOOL finished;
@property(nonatomic, strong) id finishObserver;
@property(nonatomic, strong) id updateObserver;

// Extended configuration
@property(nonatomic, copy, nullable) NSArray<NSString *> *searchScopes;
@property(nonatomic, assign) NSTimeInterval timeout;            // total max runtime (default 6 s)
@property(nonatomic, assign) NSTimeInterval inactivityInterval; // idle time before finishing (default 0.25 s)

// Runtime state helpers
@property(nonatomic, strong) NSDate *startDate;
@property(nonatomic, strong) NSDate *lastPublishDate;
@property(nonatomic, strong) dispatch_source_t watchdogTimer;

// Query pooling helpers
+ (NSMetadataQuery *)_dequeueQueryInstance;
+ (void)_enqueueQueryInstance:(NSMetadataQuery *)query;

@end

@implementation SpotlightSearchOperation

@synthesize executing = _executing;
@synthesize finished = _finished;

#pragma mark - Initialiser

// Convenience initializer – uses defaults for scopes and timeout.
- (instancetype)initWithQuery:(NSString *)query
                   generation:(NSUInteger)generation
                     predicate:(NSPredicate *)predicate
                    resultType:(NSString *)resultType {
    return [self initWithQuery:query
                     generation:generation
                       predicate:predicate
                      resultType:resultType
                    searchScopes:nil
                         timeout:6.0];
}

// Designated initializer with full configuration.
- (instancetype)initWithQuery:(NSString *)query
                   generation:(NSUInteger)generation
                     predicate:(NSPredicate *)predicate
                    resultType:(NSString *)resultType
                  searchScopes:(NSArray<NSString *> *)searchScopes
                       timeout:(NSTimeInterval)timeout {
    if (self = [super initWithQuery:query generation:generation]) {
        _predicate = predicate ?: [NSPredicate predicateWithValue:NO];
        _resultType = [resultType copy] ?: @"file";
        _accumResults = [NSMutableArray array];
        _seenPaths = [NSMutableSet set];

        _searchScopes = [searchScopes copy];
        _timeout = timeout > 0.0 ? timeout : 6.0;
        _inactivityInterval = 0.25; // 250 ms idle window
    }
    return self;
}

#pragma mark - NSOperation overrides (KVO)

- (BOOL)isAsynchronous { return YES; }
- (BOOL)isExecuting { return _executing; }
- (BOOL)isFinished { return _finished; }

- (void)start {
    if (self.isCancelled) { [self _finish]; return; }
    [self willChangeValueForKey:@"isExecuting"]; _executing = YES; [self didChangeValueForKey:@"isExecuting"];

    __weak __typeof(self) weakSelf = self;

    // Ensure Spotlight query is created and driven on the main thread because
    // NSMetadataQuery relies on an active run-loop to deliver notifications.
    dispatch_async(dispatch_get_main_queue(), ^{
        __typeof(self) strongSelf = weakSelf;
        if (!strongSelf || strongSelf.isCancelled) { return; }

        // Acquire a query instance from the shared pool (or create one).
        strongSelf.metadataQuery = [SpotlightSearchOperation _dequeueQueryInstance];

        // Configure search scopes (fallback to LocalComputerScope for broader compatibility).
        if (strongSelf.searchScopes.count) {
            strongSelf.metadataQuery.searchScopes = strongSelf.searchScopes;
        } else {
            strongSelf.metadataQuery.searchScopes = @[NSMetadataQueryLocalComputerScope];
        }

        // Cap max items to avoid runaway memory (available on macOS 14+).
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Warc-performSelector-leaks"
        SEL setMaxCountSel = NSSelectorFromString(@"setMaxItemCount:");
        if ([strongSelf.metadataQuery respondsToSelector:setMaxCountSel]) {
            ((void (*)(id, SEL, NSUInteger))objc_msgSend)(strongSelf.metadataQuery, setMaxCountSel, (NSUInteger)5000);
        }
#pragma clang diagnostic pop

        // Apply predicate.
        strongSelf.metadataQuery.predicate = strongSelf->_predicate;

        NSNotificationCenter *nc = [NSNotificationCenter defaultCenter];

        strongSelf.finishObserver = [nc addObserverForName:NSMetadataQueryDidFinishGatheringNotification
                                                    object:strongSelf.metadataQuery
                                                     queue:nil
                                                usingBlock:^(__unused NSNotification *note) {
            if (!strongSelf || strongSelf.finished) return;
            // Final batch published – stop query and clean up immediately.
            [strongSelf _stopQuery];
            // Remove observers
            NSNotificationCenter *lnc = [NSNotificationCenter defaultCenter];
            if (strongSelf.finishObserver) [lnc removeObserver:strongSelf.finishObserver];
            if (strongSelf.updateObserver) [lnc removeObserver:strongSelf.updateObserver];
            strongSelf.finishObserver = nil;
            strongSelf.updateObserver = nil;
            // Complete operation
            [strongSelf _completeAndFinish];
        }];

        strongSelf.updateObserver = [nc addObserverForName:NSMetadataQueryDidUpdateNotification
                                                    object:strongSelf.metadataQuery
                                                     queue:nil
                                                usingBlock:^(NSNotification *note) {
            NSArray *added = note.userInfo[NSMetadataQueryUpdateAddedItemsKey];
            if (added.count) {
                [strongSelf _publishItems:added];
            }
        }];

        [strongSelf.metadataQuery startQuery];

        // Start watchdog timer after query begins
        [strongSelf _startWatchdog];
    });
}

- (void)_stopQuery {
    [self.metadataQuery stopQuery];

    // Cancel watchdog
    if (self.watchdogTimer) {
        dispatch_source_cancel(self.watchdogTimer);
        self.watchdogTimer = nil;
    }
}

- (void)_finish {
    // Return query instance to pool for reuse.
    if (self.metadataQuery) {
        [SpotlightSearchOperation _enqueueQueryInstance:self.metadataQuery];
        self.metadataQuery = nil;
    }

    [self willChangeValueForKey:@"isExecuting"];
    [self willChangeValueForKey:@"isFinished"];
    _executing = NO;
    _finished = YES;
    [self didChangeValueForKey:@"isFinished"];
    [self didChangeValueForKey:@"isExecuting"];
}

- (void)_completeAndFinish {
    // Flush any remaining items before finishing.
    [self _publishAllItems];
    self.results = [self.accumResults copy];

    if (self.completionBlock) self.completionBlock();
    [self _finish];
}

// Accessor helpers
- (NSString *)resultType { return _resultType; }

#pragma mark - Publishing helpers

- (void)_publishAllItems { [self _publishItems:self.metadataQuery.results]; }

- (void)_publishItems:(NSArray<NSMetadataItem *> *)items {
    if (self.isCancelled) return;
    self.lastPublishDate = [NSDate date];
    NSMutableArray<ResultModel *> *models = [NSMutableArray arrayWithCapacity:items.count];

    // Pre-tokenise query once (lower-case, whitespace-separated, ignore empties).
    NSCharacterSet *separators = [[NSCharacterSet alphanumericCharacterSet] invertedSet];
    NSMutableArray<NSString *> *queryTokens = [NSMutableArray array];
    for (NSString *tok in [[self.query lowercaseString] componentsSeparatedByCharactersInSet:separators]) {
        if (tok.length) { [queryTokens addObject:tok]; }
    }

    BOOL multiToken = (queryTokens.count > 1);
    NSString *singleTokenLower = multiToken ? @"" : (queryTokens.firstObject ?: @"");

    for (NSMetadataItem *item in items) {
        NSString *path = [item valueForAttribute:NSMetadataItemPathKey];
        if (!path) continue;
        if (ShouldExcludePath(path)) continue; // system/hidden filter
        if ([self.seenPaths containsObject:path]) continue; // duplicate guard

        // Determine display name and final result type. If the caller asked
        // for generic "file" results but the item is actually an application
        // bundle ("*.app"), treat it as an app so it receives proper ranking
        // weight in the aggregator. This avoids artificially penalising apps
        // that are discovered via filename search.

        NSString *name = path.lastPathComponent;

        // Local variable so we can override the type without mutating the
        // ivar (which reflects the original intent of the query).
        NSString *localType = _resultType;

        BOOL isAppBundle = [[path lowercaseString] hasSuffix:@".app"];
        if (isAppBundle) {
            // If we are in file-mode but encounter an app bundle, promote it.
            if ([_resultType isEqualToString:@"file"]) {
                localType = @"app";
            }

            // Strip the ".app" suffix for cleaner display purposes when the
            // final type is "app".
            if ([name hasSuffix:@".app"]) {
                name = [name substringToIndex:name.length - 4];
            }
        }

        double score = 0.8;
        NSString *lowerName = [name lowercaseString];

        if (!multiToken) {
            // Legacy single-token scoring (exact > prefix > baseline)
            if ([lowerName isEqualToString:singleTokenLower]) {
                score = 0.99;
            } else if ([lowerName hasPrefix:singleTokenLower]) {
                score = 0.95;
            } else {
                // New heuristic (Option 2a): if the search token appears at the start of any
                // word inside the filename (word-boundary prefix), treat it as a strong
                // prefix match. This ensures queries like "code" boost "Visual Studio Code"
                // to the same 0.95 level even though the term is not the leading substring.

                BOOL wordPrefix = NO;
                NSArray<NSString *> *nameParts = [lowerName componentsSeparatedByCharactersInSet:separators];
                for (NSString *part in nameParts) {
                    if (part.length == 0) { continue; }
                    if ([part hasPrefix:singleTokenLower]) { wordPrefix = YES; break; }
                }
                if (wordPrefix) {
                    score = 0.95;
                }
            }
        } else {
            // Multi-token scoring
            NSUInteger matched = 0;
            for (NSString *tok in queryTokens) {
                if ([lowerName rangeOfString:tok].location != NSNotFound) {
                    matched += 1;
                }
            }

            BOOL containsAll = (matched == queryTokens.count);

            BOOL inOrder = NO;
            if (containsAll) {
                // Check sequential order of tokens in filename.
                NSUInteger searchPos = 0;
                inOrder = YES;
                for (NSString *tok in queryTokens) {
                    NSRange r = [lowerName rangeOfString:tok options:0 range:NSMakeRange(searchPos, lowerName.length - searchPos)];
                    if (r.location == NSNotFound) { inOrder = NO; break; }
                    searchPos = r.location + r.length;
                }
            }

            if (inOrder) {
                score = 0.99;
            } else if (containsAll) {
                score = 0.95;
            } else {
                // Partial match – small incremental boost proportional to coverage.
                double ratio = (double)matched / (double)queryTokens.count;
                score = 0.8 + 0.05 * ratio; // max 0.85 when all but one tokens match
            }
        }

        NSDictionary *dict = @{ @"name": name ?: @"",
                                 @"path": path ?: @"",
                                 @"iconPath": @"",
                                 @"score": @(score),
                                 @"type": localType };
        [models addObject:[[ResultModel alloc] initWithDictionary:dict]];
        [self.seenPaths addObject:path];
    }

    if (models.count) {
        // Accumulate for final snapshot
        [self.accumResults addObjectsFromArray:models];

        if (self.incrementalHandler) {
            self.incrementalHandler(models);
        }
    }
}

#pragma mark - Query instance pooling

+ (NSMutableArray<NSMetadataQuery *> *)_queryPool {
    static NSMutableArray<NSMetadataQuery *> *pool = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        pool = [NSMutableArray array];
    });
    return pool;
}

// Fetch query instance from the pool or create a new one.
+ (NSMetadataQuery *)_dequeueQueryInstance {
    NSMetadataQuery *q = nil;
    @synchronized([self class]) {
        NSMutableArray *pool = [self _queryPool];
        if (pool.count) {
            q = pool.lastObject;
            [pool removeLastObject];
        }
    }
    if (!q) {
        q = [[NSMetadataQuery alloc] init];
    }
    return q;
}

// Return query instance to pool after cleaning observers / predicate.
+ (void)_enqueueQueryInstance:(NSMetadataQuery *)query {
    if (!query) return;
    // Stop the query before returning it to the pool. We intentionally avoid
    // resetting the predicate here because assigning FALSEPREDICATE (or any
    // invalid predicate) triggers an exception inside NSMetadataQuery. The
    // next consumer will always set its own valid predicate prior to
    // `startQuery`, so clearing is unnecessary.
    [query stopQuery];
    NSMutableArray *pool = [self _queryPool];
    [pool addObject:query];

    const NSUInteger kMaxPoolSize = 8;
    if (pool.count > kMaxPoolSize) {
        // Evict oldest
        [pool removeObjectAtIndex:0];
    }
}

#pragma mark - Watchdog helpers

- (void)_startWatchdog {
    self.startDate = [NSDate date];
    self.lastPublishDate = self.startDate;

    dispatch_queue_t queue = dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0);
    self.watchdogTimer = dispatch_source_create(DISPATCH_SOURCE_TYPE_TIMER, 0, 0, queue);
    if (!self.watchdogTimer) { return; }

    const NSTimeInterval poll = 0.5; // 500 ms check interval
    dispatch_source_set_timer(self.watchdogTimer,
                              dispatch_time(DISPATCH_TIME_NOW, poll * NSEC_PER_SEC),
                              poll * NSEC_PER_SEC,
                              (uint64_t)(0.05 * NSEC_PER_SEC)); // 50 ms leeway

    __weak __typeof(self) weakSelf = self;
    dispatch_source_set_event_handler(self.watchdogTimer, ^{
        __typeof(self) strongSelf = weakSelf;
        if (!strongSelf) return;
        if (strongSelf.finished) {
            dispatch_source_cancel(strongSelf.watchdogTimer);
            strongSelf.watchdogTimer = nil;
            return;
        }

        NSDate *now = [NSDate date];
        NSTimeInterval totalElapsed = [now timeIntervalSinceDate:strongSelf.startDate];
        NSTimeInterval idleElapsed = [now timeIntervalSinceDate:strongSelf.lastPublishDate];

        if (totalElapsed > strongSelf.timeout || idleElapsed > strongSelf.inactivityInterval) {
            dispatch_async(dispatch_get_main_queue(), ^{
                if (!strongSelf || strongSelf.finished) return;
                [strongSelf _stopQuery];
                NSNotificationCenter *nc = [NSNotificationCenter defaultCenter];
                if (strongSelf.finishObserver) [nc removeObserver:strongSelf.finishObserver];
                if (strongSelf.updateObserver) [nc removeObserver:strongSelf.updateObserver];
                strongSelf.finishObserver = nil;
                strongSelf.updateObserver = nil;
                [strongSelf _completeAndFinish];
            });
        }
    });

    dispatch_resume(self.watchdogTimer);
}

#pragma mark - NSOperation overrides (cancel)

- (void)cancel {
    [super cancel];

    // If the operation has not yet started executing, defer finishing to `-start`.
    // This avoids driving the operation to the `finished` state while it is still
    // sitting in an NSOperationQueue that has not invoked `-start`, which triggers
    // the runtime warning: "went isFinished=YES without being started by the queue".
    if (!self.executing) {
        return;
    }

    dispatch_async(dispatch_get_main_queue(), ^{
        if (self.finished) return;
        [self _stopQuery];
        NSNotificationCenter *nc = [NSNotificationCenter defaultCenter];
        if (self.finishObserver) [nc removeObserver:self.finishObserver];
        if (self.updateObserver) [nc removeObserver:self.updateObserver];
        self.finishObserver = nil;
        self.updateObserver = nil;
        [self _finish];
    });
}

#pragma mark - Path filtering helpers

static BOOL ShouldExcludePath(NSString *path) {
    if (!path) { return YES; }

    // Quick hidden component check ("." prefix).
    for (NSString *comp in path.pathComponents) {
        if ([comp hasPrefix:@"."]) {
            return YES; // hidden file or directory
        }
    }

    NSString *lower = [path lowercaseString];

    // Explicit allow-list: do NOT exclude items located in the sealed
    // system Applications folder. These are first-party apps shipped with
    // macOS (e.g. Calculator.app) that reside under /System/Applications.
    // We intentionally check before the system-level deny-list because that
    // list contains "/system".
    if ([lower hasPrefix:@"/system/applications"])
    {
        return NO;  // keep System Applications
    }

    // System-level prefixes that we want to hide.
    NSArray<NSString *> *systemPrefixes = @[ @"/system", @"/library", @"/usr", @"/bin",
                                             @"/sbin", @"/private", @"/etc", @"/var" ];
    for (NSString *pref in systemPrefixes) {
        if ([lower hasPrefix:pref]) {
            // Allow "/applications" explicitly even though it shares root with /library.
            return YES;
        }
    }

    // Exclude user Library (~/Library) as it is considered internal.
    // We compare against "/library" earlier only for root path; here we catch user library.
    NSRange userLib = [lower rangeOfString:@"/library" options:0];
    if (userLib.location != NSNotFound && userLib.location > 0) {
        // Anything inside any Library directory is ignored.
        return YES;
    }

    return NO; // keep by default
}

// Maximum results we keep in winners heap
static const NSUInteger kMaxWinners = 120;

@end 