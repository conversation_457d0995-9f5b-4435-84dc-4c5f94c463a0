#pragma once

#import "search_operation.h"

/**
 * ChatHistorySearchOperation – scans on-disk saved conversation JSON files and
 * produces ResultModel entries with type == "chat".  It supports two modes:
 *
 *  – "show all" (empty or whitespace-only query) → returns recently updated
 *    conversations ordered by modification date.
 *  – Query string provided → performs simple case-insensitive substring match
 *    against conversation title and the text of chat messages (limited sample)
 *    to compute a relevance score.
 *
 * The operation runs entirely on a background NSOperationQueue created by
 * SearchCoordinator.  It is safe to run concurrently with other domain
 * operations and does not touch UI APIs.
 */
@interface ChatHistorySearchOperation : SearchOperation
@end 