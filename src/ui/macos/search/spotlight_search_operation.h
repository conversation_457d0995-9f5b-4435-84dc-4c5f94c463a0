#pragma once

#import "search_operation.h"

/// Generic SearchOperation that streams results from an `NSMetadataQuery` and publishes
/// incremental deltas. Concrete domains (apps, files, etc.) are configured via the
/// designated initializer rather than subclassing.
@interface SpotlightSearchOperation : SearchOperation

/// Designated initializer.
///
/// @param query        The user's search string (passed through to `SearchOperation`).
/// @param generation   Generation counter supplied by `SearchCoordinator`.
/// @param predicate    Predicate that will be assigned to `NSMetadataQuery`.
/// @param resultType   Domain identifier (e.g. "app", "file"). Used in `ResultModel`.
- (instancetype)initWithQuery:(NSString *)query
                  generation:(NSUInteger)generation
                    predicate:(NSPredicate *)predicate
                   resultType:(NSString *)resultType;

/// Designated initializer with configurable search scope(s) and timeout.
///
/// @param query         The user's search string (passed through to `SearchOperation`).
/// @param generation    Generation counter supplied by `SearchCoordinator`.
/// @param predicate     Predicate that will be assigned to `NSMetadataQuery`.
/// @param resultType    Domain identifier (e.g. "app", "file"). Used in `ResultModel`.
/// @param searchScopes  Optional array of Spotlight scopes; defaults to LocalComputer if nil.
/// @param timeout       Maximum total runtime in seconds before the operation auto‐finishes. Defaults to 6 s.
- (instancetype)initWithQuery:(NSString *)query
                  generation:(NSUInteger)generation
                    predicate:(NSPredicate *)predicate
                   resultType:(NSString *)resultType
                 searchScopes:(nullable NSArray<NSString *> *)searchScopes
                      timeout:(NSTimeInterval)timeout;

@end 