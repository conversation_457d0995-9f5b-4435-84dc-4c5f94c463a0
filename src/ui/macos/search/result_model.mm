#import "result_model.h"

@implementation ResultModel

- (instancetype)initWithDictionary:(NSDictionary *)dict {
    if (self = [super init]) {
        _name = [dict[@"name"] copy] ?: @"";
        _path = [dict[@"path"] copy] ?: @"";
        _iconPath = [dict[@"iconPath"] copy] ?: @"";
        _score = [dict[@"score"] doubleValue];
        _type = [dict[@"type"] copy] ?: @"";
        _iconSymbol = [dict[@"iconSymbol"] copy] ?: @"";
        _tintHex = [dict[@"tintHex"] copy] ?: @"";
        // Build a stable identifier; fallback to name+path
        if (dict[@"identifier"]) {
            _identifier = [dict[@"identifier"] copy];
        } else {
            _identifier = [NSString stringWithFormat:@"%@-%@", _name, _path];
        }
    }
    return self;
}

- (NSDictionary *)toDictionary {
    return @{ @"identifier": self.identifier ?: @"",
              @"name": self.name ?: @"",
              @"path": self.path ?: @"",
              @"iconPath": self.iconPath ?: @"",
              @"score": @(self.score),
              @"type": self.type ?: @"",
              @"iconSymbol": self.iconSymbol ?: @"",
              @"tintHex": self.tintHex ?: @"" };
}

#pragma mark - NSCopying

- (id)copyWithZone:(NSZone *)zone {
    // Immutable → shallow copy ok
    return self;
}

- (NSUInteger)hash {
    return self.identifier.hash;
}

- (BOOL)isEqual:(id)object {
    if (![object isKindOfClass:[ResultModel class]]) return NO;
    return [self.identifier isEqualToString:((ResultModel *)object).identifier];
}

@end 