#import "website_search_operation.h"
#import "result_model.h"
#import "../browser_history_importer.h"
#import <Cocoa/Cocoa.h>          // NSApplication
#import <Foundation/Foundation.h>
#import "../macos_ui_internal.h"  // declares AppDelegate with ctx
#include "../../../core/interfaces/ibrowser_history_importer.h"
#include "../../../core/app_context.h"

#define MAX_RESULTS 40

@class AppDelegate;  // forward declaration

@implementation WebsiteSearchOperation

- (void)main {
    if (self.isCancelled) return;

    NSString *queryStr = self.query ?: @"";

    NSMutableArray<ResultModel *> *res = [NSMutableArray array];

    // Direct URL detection
    BOOL isLikelyURL = ([queryStr containsString:@"."] || [queryStr hasPrefix:@"http://"] || [queryStr hasPrefix:@"https://"]);
    if (isLikelyURL) {
        NSString *urlStr = queryStr;
        if (![urlStr hasPrefix:@"http://"] && ![urlStr hasPrefix:@"https://"]) {
            urlStr = [@"https://" stringByAppendingString:urlStr];
        }
        NSDictionary *dict = @{ @"name": queryStr,
                                 @"path": urlStr,
                                 @"iconPath": @"",
                                 @"score": @(0.95),
                                 @"type": @"website" };
        [res addObject:[[ResultModel alloc] initWithDictionary:dict]];
    }

    launcher::ui::IBrowserHistoryImporter *importerPtr = nullptr;
    AppDelegate *appDelegate = (AppDelegate *)[[NSApplication sharedApplication] delegate];
    if (appDelegate && appDelegate.ctx && appDelegate.ctx->browserHistory) {
        importerPtr = appDelegate.ctx->browserHistory.get();
    } else {
        // No importer available – finish with direct URL results only.
        self.results = res;
        return;
    }

    auto &importer = *importerPtr;

    std::vector<launcher::ui::WebsiteEntry> entries = importer.searchWebsites([queryStr UTF8String], MAX_RESULTS - (int)res.count);

    for (const auto &entry : entries) {
        if (self.isCancelled) return;
        NSString *path = @(entry.url.c_str());
        NSString *name = entry.title.empty() ? @(entry.domain.c_str()) : @(entry.title.c_str());
        double score = 0.90;
        if (entry.isSuggestion) {
            score = 0.85; // Lower weight for auto‐suggestion results
        }

        NSDictionary *dict = @{ @"name": name ?: @"",
                                 @"path": path ?: @"",
                                 @"iconPath": @"",
                                 @"score": @(score),
                                 @"type": @"website" };
        [res addObject:[[ResultModel alloc] initWithDictionary:dict]];
    }

    // Google search fallback item
    if (queryStr.length > 0) {
        NSString *escaped = [queryStr stringByAddingPercentEncodingWithAllowedCharacters:[NSCharacterSet URLQueryAllowedCharacterSet]];
        NSString *googleURL = [NSString stringWithFormat:@"https://www.google.com/search?q=%@", escaped];
        NSDictionary *googleDict = @{ @"name": [NSString stringWithFormat:@"%@", queryStr],
                                      @"path": googleURL,
                                      @"iconPath": @"",
                                      @"score": @(0.80),
                                      @"type": @"website" };
        [res addObject:[[ResultModel alloc] initWithDictionary:googleDict]];
    }

    self.results = res;
}

@end 