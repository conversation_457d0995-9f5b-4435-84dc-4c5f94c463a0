#import "search_coordinator.h"
#import "rerank_bridge.h"

#import "search_operation.h"
#import "website_search_operation.h"
#import "result_model.h"
#import "spotlight_search_operation.h"
#import "app_search_operation.h"
#import "chat_history_search_operation.h"

#include <queue>
#include <vector>
#include <cmath>

/// Serial queue used for thread-safe generation handling.
static dispatch_queue_t g_generationQueue;

@interface SearchCoordinator ()
@property(nonatomic, strong) NSOperationQueue *operationQueue;
@property(nonatomic, assign) NSUInteger currentGeneration;
@property(nonatomic, strong) NSTimer *debounceTimer;
@property(nonatomic, strong) NSMutableDictionary<NSString *, ResultModel *> *aggregateMap;
@property(nonatomic, assign) NSUInteger finishedOps;
@property(nonatomic, assign) NSUInteger opsExpected;
@property(nonatomic, strong) NSMutableArray<NSString *> *prevSnapshotIds;

// C++ min-heap (keeps lowest score at top) to store winners efficiently
@end

// Helper structs for heap management (must be outside @interface in ObjC++)
struct HeapEntry {
    double score;                // effective score (includes tie-break weight)
    NSString *identifier;        // strong reference handled by ARC at outer level
};

struct HeapCompare {
    bool operator()(const HeapEntry &a, const HeapEntry &b) const { return a.score > b.score; }
};

static double weightForType(NSString *type) {
    static NSDictionary<NSString *, NSNumber *> *weights;
    static dispatch_once_t once;
    dispatch_once(&once, ^{
        weights = @{ @"app": @2, @"chat": @2, @"website": @1, @"file": @0 };
    });
    NSNumber *w = weights[type];
    return w ? w.doubleValue : 0.0;
}

// Maximum results we keep in winners heap
static const NSUInteger kMaxWinners = 120;

@interface SearchCoordinator () {
    std::priority_queue<HeapEntry, std::vector<HeapEntry>, HeapCompare> _winnerHeap;
    BOOL _heapDirty;  // indicates snapshot needs refresh
}

@end

@implementation SearchCoordinator

+ (void)initialize {
    if (self == [SearchCoordinator class]) {
        g_generationQueue = dispatch_queue_create("launcher.search.coordinator.generation", DISPATCH_QUEUE_SERIAL);
    }
}

+ (instancetype)shared {
    static SearchCoordinator *sharedInstance = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        sharedInstance = [[self alloc] initPrivate];
    });
    return sharedInstance;
}

// Disallow external init
- (instancetype)init {
    @throw [NSException exceptionWithName:NSInternalInconsistencyException
                                   reason:@"Use +[SearchCoordinator shared] instead."
                                 userInfo:nil];
}

- (instancetype)initPrivate {
    if (self = [super init]) {
        _operationQueue = [[NSOperationQueue alloc] init];
        _operationQueue.qualityOfService = NSQualityOfServiceUserInitiated;
        _operationQueue.maxConcurrentOperationCount = 3;
        _currentGeneration = 0;
    }
    return self;
}

#pragma mark - Public

- (void)submitQuery:(NSString *)query {
    if (!query) { return; }

    [RerankBridge flushPendingImpressions];
    [self cancelAll];

    // Execute search immediately (debounce removed)
    [self _executeSearchForQuery:query];
}

#pragma mark - Public helpers

- (void)cancelAll {
    [self.debounceTimer invalidate];
    [self.operationQueue cancelAllOperations];
    self.aggregateMap = [NSMutableDictionary dictionary];
    self.finishedOps = 0;
    self.prevSnapshotIds = nil;

   // Clear C++ heap
    while (!_winnerHeap.empty()) { _winnerHeap.pop(); }
    _heapDirty = NO;
}

#pragma mark - Internal

- (void)_executeSearchForQuery:(NSString *)query {
    // Handle empty string fast-clear
    if (query.length == 0) {
        [self cancelAll];
        if ([self.delegate respondsToSelector:@selector(searchCoordinatorDidUpdateResults:)]) {
            [self.delegate searchCoordinatorDidUpdateResults:@[]];
        }
        return;
    }

    [self cancelAll];

    // Increase generation
    dispatch_sync(g_generationQueue, ^{
        self.currentGeneration += 1;
    });
    NSUInteger gen = self.currentGeneration;

    self.aggregateMap = [NSMutableDictionary dictionary];
    self.finishedOps = 0;

    BOOL isFileQuery = query.length > 3;

    // Advanced tokenisation: break on any non-alphanumeric character (whitespace, punctuation, symbols).
    NSCharacterSet *separators = [[NSCharacterSet alphanumericCharacterSet] invertedSet];
    NSMutableArray<NSString *> *tokens = [NSMutableArray array];
    for (NSString *tok in [[query lowercaseString] componentsSeparatedByCharactersInSet:separators]) {
        if (tok.length) { [tokens addObject:tok]; }
    }

    NSMutableArray<SearchOperation *> *ops = [NSMutableArray array];

    // Always include application search backed by the C++ AppIndex.
    [ops addObject:[[AppSearchOperation alloc] initWithQuery:query generation:gen]];

    if (isFileQuery) {
        if (tokens.count > 1) {
            // Build AND predicate: each token must be present in the filename (case/diacritic-insensitive)
            NSMutableArray<NSPredicate *> *andSubs = [NSMutableArray arrayWithCapacity:tokens.count];
            for (NSString *tok in tokens) {
                [andSubs addObject:[NSPredicate predicateWithFormat:@"kMDItemContentType == 'com.apple.application-bundle' && kMDItemFSName CONTAINS[cd] %@", tok]];
            }
            NSPredicate *andCompound = [NSCompoundPredicate andPredicateWithSubpredicates:andSubs];

            // Strict op added first so its higher-scored results win duplicate races.
            SpotlightSearchOperation *strictOp = [[SpotlightSearchOperation alloc] initWithQuery:query
                                                                                      generation:gen
                                                                                        predicate:andCompound
                                                                                       resultType:@"app"];

            [ops addObject:strictOp];
        } else {
            // Single token – fallback to legacy behaviour.
            NSPredicate *appPredicate = [NSPredicate predicateWithFormat:@"kMDItemContentType == 'com.apple.application-bundle' && kMDItemFSName CONTAINS[cd] %@", query];
            SpotlightSearchOperation *appOp = [[SpotlightSearchOperation alloc] initWithQuery:query
                                                                                    generation:gen
                                                                                      predicate:appPredicate
                                                                                     resultType:@"app"];
            [ops addObject:appOp];
        }
    } else {
        // Short query (<= 2 chars) – run a narrow Spotlight search over application bundles.
        NSPredicate *appPredicate = [NSPredicate predicateWithFormat:
            @"kMDItemContentType == 'com.apple.application-bundle' && kMDItemFSName BEGINSWITH[cd] %@", query];
        SpotlightSearchOperation *appOp = [[SpotlightSearchOperation alloc] initWithQuery:query
                                                                              generation:gen
                                                                                predicate:appPredicate
                                                                               resultType:@"app"];
        [ops addObject:appOp];
    }

    [ops addObject:[[ChatHistorySearchOperation alloc] initWithQuery:query generation:gen]];
    [ops addObject:[[WebsiteSearchOperation alloc] initWithQuery:query generation:gen]];

    self.opsExpected = ops.count;

    for (SearchOperation *op in ops) {
        __weak __typeof(self) weakSelf = self;
        // Incremental handler – merge deltas as they arrive.
        op.incrementalHandler = ^(NSArray<ResultModel *> *delta) {
            __typeof(self) strongSelf = weakSelf;
            if (!strongSelf) return;
            if (op.isCancelled) return;
            if (op.generation != strongSelf.currentGeneration) return;

            [strongSelf _mergeResults:delta];
        };

        // Final completion – catch any tail results and mark finished.
        op.completionBlock = ^{
            __typeof(self) strongSelf = weakSelf;
            if (!strongSelf) return;
            if (op.isCancelled) return;
            if (op.generation != strongSelf.currentGeneration) return;

            [strongSelf _mergeResults:op.results];
        };
        [self.operationQueue addOperation:op];
    }
}

- (void)_mergeResults:(NSArray<ResultModel *> *)newResults {
    @synchronized (self) {
        for (ResultModel *model in newResults) {
            // Compute effective score (primary score + tiny weight to prioritise type)
            double effective = model.score + weightForType(model.type) * 0.001;

            // Check duplicate
            ResultModel *existing = self.aggregateMap[model.identifier];
            if (existing) {
                double existingEff = existing.score + weightForType(existing.type) * 0.001;
                if (effective <= existingEff + 1e-6) {
                    continue; // worse or equal – ignore
                }
                // Better score – replace in map, keep duplicate in heap (lazy cleanup)
                self.aggregateMap[model.identifier] = model;
            } else {
                self.aggregateMap[model.identifier] = model;
            }

            // Push new entry into heap
            HeapEntry entry{effective, model.identifier};
            _winnerHeap.push(entry);
            _heapDirty = YES;

            // Enforce capacity with lazy deletion
            while (_winnerHeap.size() > kMaxWinners) {
                HeapEntry top = _winnerHeap.top();
                _winnerHeap.pop();
                ResultModel *current = self.aggregateMap[top.identifier];
                if (!current) { continue; } // identifier already removed – skip
                double curEff = current.score + weightForType(current.type) * 0.001;
                if (fabs(curEff - top.score) < 1e-6) {
                    // This is the true lowest winner – evict
                    [self.aggregateMap removeObjectForKey:top.identifier];
                } // else heap entry stale – ignore and continue loop
            }
        }

        self.finishedOps += 1;

        // Only dispatch update when all operations finished for this generation
        if (self.finishedOps < self.opsExpected) {
            return;
        }

        if (!_heapDirty) { return; }

        NSArray<ResultModel *> *snapshot = [self _sortedSnapshot];
        snapshot = [RerankBridge rerankResults:snapshot];
        // Notify bandit that these items were shown.
        [RerankBridge recordImpressions:snapshot];

        // Build identifier array
        NSMutableArray<NSString *> *ids = [NSMutableArray arrayWithCapacity:snapshot.count];
        for (ResultModel *m in snapshot) {
            if (m.identifier) [ids addObject:m.identifier];
        }

        // If identifiers unchanged, skip UI update to prevent flicker
        if (self.prevSnapshotIds && [self.prevSnapshotIds isEqualToArray:ids]) {
            _heapDirty = NO;
            return;
        }

        self.prevSnapshotIds = ids;

        if ([self.delegate respondsToSelector:@selector(searchCoordinatorDidUpdateResults:)]) {
            dispatch_async(dispatch_get_main_queue(), ^{
                [self.delegate searchCoordinatorDidUpdateResults:snapshot];
            });
        }

        _heapDirty = NO;
    }
}

- (NSArray<ResultModel *> *)_sortedSnapshot {
    NSArray<ResultModel *> *all = self.aggregateMap.allValues;
    return [all sortedArrayUsingComparator:^NSComparisonResult(ResultModel *a, ResultModel *b) {
        if (a.score != b.score) {
            return a.score > b.score ? NSOrderedAscending : NSOrderedDescending;
        }
        static NSDictionary *weights; if (!weights) {
            weights = @{ @"app": @2, @"chat": @2, @"website": @1, @"file": @0 };
        }
        NSInteger wa = [weights[a.type] integerValue];
        NSInteger wb = [weights[b.type] integerValue];
        if (wa == wb) return NSOrderedSame;
        return wa > wb ? NSOrderedAscending : NSOrderedDescending;
    }];
}

@end 