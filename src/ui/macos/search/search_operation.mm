#import "search_operation.h"
#import "result_model.h"

@interface SearchOperation ()
@property(nonatomic, copy, readwrite) NSString *query;
@property(nonatomic, assign, readwrite) NSUInteger generation;
// results property remains declared in the public header; no need to redeclare here.
@end

@implementation SearchOperation

- (instancetype)initWithQuery:(NSString *)query generation:(NSUInteger)generation {
    if (self = [super init]) {
        _query = [query copy] ?: @"";
        _generation = generation;
        _results = @[];
        self.qualityOfService = NSQualityOfServiceUserInitiated;
    }
    return self;
}

- (void)main {
    // Subclasses must override `main` and set `self.results` before exiting.
    // Provide an assertion in debug builds to catch improper subclassing.
#if DEBUG
    NSAssert(NO, @"Subclasses of SearchOperation must implement -main and populate results.");
#endif
}

@end 