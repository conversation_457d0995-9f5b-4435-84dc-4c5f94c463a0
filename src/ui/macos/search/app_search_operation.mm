#import "app_search_operation.h"
#import "result_model.h"
#import <Cocoa/Cocoa.h>
#import "../macos_ui_internal.h"  // declares AppDelegate.ctx

// C++ includes
#include "../../../core/interfaces/iapp_index.h"
#include "../../../core/index/app_index.h"   // for fallback singleton & AppResult
#include "../../../core/app_context.h"

@class AppDelegate;

@implementation AppSearchOperation

- (void)main {
    if (self.isCancelled) { return; }

    // Convert NSString query to std::string_view for C++ AppIndex
    std::string_view query_sv = [self.query UTF8String] ?: "";


    // Retrieve index through injected application context when available.
    launcher::core::IAppIndex *indexPtr = nullptr;
    AppDelegate *appDelegate = (AppDelegate *)[[NSApplication sharedApplication] delegate];
    if (appDelegate && appDelegate.ctx && appDelegate.ctx->appIndex) {
        indexPtr = appDelegate.ctx->appIndex.get();
    } else {
        // No index available → return empty results.
        self.results = @[];
        return;
    }

    auto &index = *indexPtr;

    std::vector<launcher::core::AppResult> matches = index.search(query_sv);

    NSMutableArray<ResultModel *> *arr = [NSMutableArray arrayWithCapacity:matches.size()];

    for (const auto &match : matches) {
        if (self.isCancelled) { return; }

        NSDictionary *dict = @{ @"name": @(match.name.c_str()),
                                 @"path": @(match.path.c_str()),
                                 @"iconPath": @(match.iconPath.c_str()),
                                 @"score": @(match.score),
                                 @"type": @"app" };
        [arr addObject:[[ResultModel alloc] initWithDictionary:dict]];
    }

    // Keep only top 40 to reduce churn (AppIndex already sorted by score).
    if (arr.count > 40) {
        arr = [NSMutableArray arrayWithArray:[arr subarrayWithRange:NSMakeRange(0, 40)]];
    }

    self.results = arr;
}

@end 