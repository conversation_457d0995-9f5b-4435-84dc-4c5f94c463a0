#import <Cocoa/Cocoa.h>
#import <ApplicationServices/ApplicationServices.h>
#include "application_visibility_manager.h"
#include "../../core/util/debug.h"
#include "menu_bar_manager.h"

namespace launcher {
namespace ui {

ApplicationVisibilityManager& ApplicationVisibilityManager::shared() {
    static ApplicationVisibilityManager instance;
    return instance;
}

ApplicationVisibilityManager::ApplicationVisibilityManager() 
    : openWindowCount_(0), 
      isInForegroundMode_(false),
      queue_(dispatch_queue_create("com.launcher.ApplicationVisibilityManager", DISPATCH_QUEUE_SERIAL)) {
    DBG("ApplicationVisibilityManager initialized");
}

ApplicationVisibilityManager::~ApplicationVisibilityManager() {
    // ARC automatically manages dispatch queue lifecycle
    // No need to call dispatch_release with ARC
}

void ApplicationVisibilityManager::openChatWindow() {
    int previousCount = openWindowCount_.fetch_add(1);
    DBG("Chat window opened, count now: " << (previousCount + 1));
    
    // If this was the first window, transform to foreground app
    if (previousCount == 0) {
        transformToForegroundApp();
    }
}

void ApplicationVisibilityManager::closeChatWindow() {
    int previousCount = openWindowCount_.fetch_sub(1);
    
    // Prevent going negative in case of mismatched calls
    if (previousCount <= 0) {
        openWindowCount_.store(0);
        DBG("Warning: closeChatWindow called with no open windows");
        return;
    }
    
    DBG("Chat window closed, count now: " << (previousCount - 1));
    
    // If this was the last window, transform back to background app
    if (previousCount == 1) {
        transformToBackgroundApp();
    }
}

bool ApplicationVisibilityManager::isInForegroundMode() const {
    return isInForegroundMode_.load();
}

void ApplicationVisibilityManager::transformToForegroundApp() {
    // Use serial queue to prevent conflicting transform calls
    dispatch_async(queue_, ^{
        DBG("Transforming to foreground application");
        
        // Skip if already in foreground mode
        if (isInForegroundMode_.load()) {
            DBG("Already in foreground mode, skipping transform");
            return;
        }
        
        // Transform the process to a foreground application
        ProcessSerialNumber psn = { 0, kCurrentProcess };
        OSStatus err = TransformProcessType(&psn, kProcessTransformToForegroundApplication);
        
        if (err != noErr) {
            ERR("Failed to transform to foreground application, error: " << err);
            return;
        }
        
        // Update NSApplication's activation policy to match
        dispatch_async(dispatch_get_main_queue(), ^{
            [NSApp setActivationPolicy:NSApplicationActivationPolicyRegular];
            
            // Activate the app to ensure it appears in ⌘-Tab and Dock
            [NSApp activateIgnoringOtherApps:YES];
            
            // Update state after successful transform
            isInForegroundMode_.store(true);
            DBG("Successfully transformed to foreground application");
            
            // Ensure main menu
            MenuBarManager::shared().ensureMainMenu();
        });
    });
}

void ApplicationVisibilityManager::transformToBackgroundApp() {
    // Use serial queue to prevent conflicting transform calls
    dispatch_async(queue_, ^{
        DBG("Transforming to background application");
        
        // Skip if already in background mode
        if (!isInForegroundMode_.load()) {
            DBG("Already in background mode, skipping transform");
            return;
        }
        
        // Transform the process back to a UI Element application
        ProcessSerialNumber psn = { 0, kCurrentProcess };
        OSStatus err = TransformProcessType(&psn, kProcessTransformToUIElementApplication);
        
        if (err != noErr) {
            ERR("Failed to transform to background application, error: " << err);
            return;
        }
        
        // Update NSApplication's activation policy to match
        dispatch_async(dispatch_get_main_queue(), ^{
            [NSApp setActivationPolicy:NSApplicationActivationPolicyAccessory];
            
            // Update state after successful transform
            isInForegroundMode_.store(false);
            DBG("Successfully transformed to background application");
        });
    });
}

void ApplicationVisibilityManager::ensureForeground() {
    // Reuse the internal helper but keep reference count unchanged.
    transformToForegroundApp();
}

}} // namespace launcher::ui 