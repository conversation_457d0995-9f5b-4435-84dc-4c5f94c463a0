# UI macOS library
if(APPLE)
    add_library(ui_macos STATIC
        macos_ui_core.mm
        macos_ui_app_delegate.mm
        macos_ui_views.mm
        preferences_window_controller.mm
        controllers/models_pane_controller.mm
        controllers/plugins_pane_controller.mm
        ../models/runtime_plugin_cell_model.mm
        models/plugin_table_model.mm
        general_pane_controller.mm
        browser_history_importer.mm
        search/search_coordinator.mm
        search/search_operation.mm
        search/spotlight_search_operation.mm
        search/website_search_operation.mm
        search/app_search_operation.mm
        search/chat_history_search_operation.mm
        search/rerank_bridge.mm
        search/result_model.mm
        search/search_results_adapter.mm
        default_browser_icon.mm
        icon_loader.mm
        status_menu_controller.mm
        chat_navigator.mm
        application_visibility_manager.mm
        menu_bar_manager.mm
        # hotkey_manager.mm – removed (replaced by MASShortcutMonitor)
        resources/AppIcon.m
        ../common/message_formatter.mm
        ../common/icon_utils.mm
        services/icon_provider.mm
        views/rounded_image_view.mm
        views/result_cell_view.mm
        views/capability_strip_view.mm
        views/plugin_row_view.mm
        views/threat_summary_view.mm
        views/plugin_details_view.mm
        views/swatch_view.mm
        controllers/launcher_bar_controller.mm
    )

    # Include directories
    target_include_directories(ui_macos
        PUBLIC
            ${CMAKE_CURRENT_SOURCE_DIR}
            ${CMAKE_CURRENT_SOURCE_DIR}/../chat
            ${cmark_SOURCE_DIR}/src
            ${cmark_BINARY_DIR}/src
    )

    # Find required libraries
    find_library(APPKIT_LIBRARY AppKit REQUIRED)
    find_library(FOUNDATION_LIBRARY Foundation REQUIRED)
    find_library(SQLITE3_LIBRARY SQLite3 REQUIRED)
    find_library(CARBON_LIBRARY Carbon REQUIRED)

    # Link with dependencies
    target_link_libraries(ui_macos
        PRIVATE
            core
            ranking
            nlohmann_json::nlohmann_json
            mas_shortcut
            ${APPKIT_LIBRARY}
            ${FOUNDATION_LIBRARY}
            ${SQLITE3_LIBRARY}
            ${CARBON_LIBRARY}
            "-framework ImageIO"
    )
    
    # Set Objective-C++ properties for .mm files
    set_source_files_properties(
        macos_ui_core.mm
        macos_ui_app_delegate.mm
        macos_ui_views.mm
        preferences_window_controller.mm
        controllers/models_pane_controller.mm
        controllers/plugins_pane_controller.mm
        ../models/runtime_plugin_cell_model.mm
        models/plugin_table_model.mm
        general_pane_controller.mm
        browser_history_importer.mm
        search/search_coordinator.mm
        search/search_operation.mm
        search/spotlight_search_operation.mm
        search/website_search_operation.mm
        search/app_search_operation.mm
        search/chat_history_search_operation.mm
        search/rerank_bridge.mm
        search/result_model.mm
        search/search_results_adapter.mm
        default_browser_icon.mm
        icon_loader.mm
        status_menu_controller.mm
        chat_navigator.mm
        application_visibility_manager.mm
        menu_bar_manager.mm
        # hotkey_manager.mm – removed (replaced by MASShortcutMonitor)
        ../common/message_formatter.mm
        ../common/icon_utils.mm
        services/icon_provider.mm
        views/rounded_image_view.mm
        views/result_cell_view.mm
        views/capability_strip_view.mm
        views/plugin_row_view.mm
        views/threat_summary_view.mm
        views/plugin_details_view.mm
        views/swatch_view.mm
        controllers/launcher_bar_controller.mm
        PROPERTIES
        COMPILE_FLAGS "-x objective-c++"
    )
    
    # Set language property for Objective-C files
    set_source_files_properties(
        resources/AppIcon.m
        PROPERTIES
        LANGUAGE C
        COMPILE_FLAGS "-x objective-c"
    )
endif() 