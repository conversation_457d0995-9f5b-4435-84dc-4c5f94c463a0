#pragma once

#import <Cocoa/Cocoa.h>
#include <dispatch/dispatch.h>

// Internal/private interfaces for macOS UI implementation
// Not exported or installed; only for use by macos_ui_*.mm files

#include <memory>
#include <vector>
#include "macos_ui.h"

namespace launcher {
namespace core {
    class Context;
    struct ToolParameter;
    struct AppContext; // forward declaration for injected services
}
namespace ui {
    class MacOSUI;
    class MacOSChatUI;
    class ChatUIInterface;
}
}

// Global list of chat window instances (defined in macos_ui_views.mm)
extern std::vector<std::shared_ptr<launcher::ui::MacOSChatUI>> g_launcher_chat_instances;

// --- Forward declarations for new window controllers ---
@class ResultModel;
@protocol SearchCoordinatorDelegate;

// --- AppDelegate ---
@interface AppDelegate : NSObject <NSApplicationDelegate, SearchCoordinatorDelegate>
@property (nonatomic, assign) launcher::ui::MacOSUI* uiInstance;
@property (nonatomic, strong) NSStatusItem* statusItem;
@property (nonatomic, strong) NSWindow* launcherBar;
@property (nonatomic, strong) NSTextField* searchField;
@property (nonatomic, strong) NSTableView* resultsTable;
@property (nonatomic, strong) NSScrollView* scrollView;
@property (nonatomic, strong) NSProgressIndicator* searchProgressIndicator;
@property (nonatomic, assign) launcher::ui::LaunchCallback launchCallback;
@property (nonatomic, strong) NSCache* iconCache;
@property (nonatomic, strong) ResultsTableDelegate* resultsDelegate;
@property (nonatomic, strong) SearchFieldDelegate* searchFieldDelegate;
@property (nonatomic, strong) PreferencesWindowController* settingsWindowController;
@property (nonatomic, strong) dispatch_group_t searchGroup;
@property (nonatomic, strong) NSMutableArray* appResults;
@property (nonatomic, strong) NSMutableArray* fileResults;
@property (nonatomic, strong) NSMutableArray* websiteResults;
@property (nonatomic, assign) BOOL isSearching;
@property (nonatomic, assign) BOOL isAppSearchComplete;
@property (nonatomic, assign) BOOL isFileSearchComplete;
@property (nonatomic, assign) BOOL isWebsiteSearchComplete;
@property (nonatomic, strong) NSWindow *parameterFormWindow;
@property (nonatomic, strong) NSMutableDictionary *parameterFields;
@property (nonatomic, strong) NSMutableDictionary *parameterValues;
@property (nonatomic, copy) void (^parameterFormCompletionHandler)(BOOL, NSDictionary *);
@property (nonatomic, assign) launcher::core::Context selectedTextContext;
@property (nonatomic, assign) std::shared_ptr<launcher::ui::ChatUIInterface> chatUI;
#if __has_include(<AppKit/NSTableViewDiffableDataSource.h>)
@property (nonatomic, strong) NSTableViewDiffableDataSource<NSNumber *, NSString *> *diffableDataSource;
#endif
@property (nonatomic, assign) launcher::core::AppContext* ctx; // non-owning pointer to shared AppContext
- (void)createLauncherBar;
- (NSMenu*)createStatusMenu;
- (void)toggleLauncherBar:(id)sender;
- (void)hideLauncherBarAndRestore;
- (void)toggleLaunchAtLogin:(id)sender;
- (void)performParallelSearch:(NSString*)searchText;
- (void)showSearchInProgress:(BOOL)inProgress;
- (void)displayFormattedOutput:(const std::string&)output;
- (void)showParameterForm:(const std::vector<launcher::core::ToolParameter>&)parameters 
             completionHandler:(void (^)(BOOL completed, NSDictionary *values))completionHandler;
- (void)showChat:(id)sender;
- (void)openChatAndSendQuestion:(NSString *)question;
@end

// --- ResultsTableDelegate ---
@interface ResultsTableDelegate : NSObject <NSTableViewDelegate, NSTableViewDataSource>
@property (nonatomic, strong) NSArray* results;
@property (nonatomic, assign) launcher::ui::MacOSUI* uiInstance;
@property (nonatomic, assign) launcher::ui::LaunchCallback launchCallback;
@property (nonatomic, strong) NSCache* iconCache;
- (instancetype)initWithResults:(NSArray*)results;
- (NSImage*)iconForFileType:(NSString*)fileType;
- (NSImage*)iconForFilePath:(NSString*)filePath;
- (NSImage*)iconForWebsite:(NSString*)urlString;
@end

// --- SearchFieldDelegate ---
@interface SearchFieldDelegate : NSObject <NSTextFieldDelegate>
@end

// --- RoundedImageView ---
@interface RoundedImageView : NSImageView
@property (nonatomic) CGFloat cornerRadius;
@end

// --- ResultCellView ---
@interface ResultCellView : NSTableCellView
@property (nonatomic, strong) NSImageView* appIconView;
@property (nonatomic, strong) NSTextField* nameField;
@property (nonatomic, strong) NSTextField* pathField;
@property (nonatomic, strong) NSTextField* typeField;
@property (nonatomic, strong) RoundedImageView* resultImageView;
@end

// --- CustomTableRowView ---
@interface CustomTableRowView : NSTableRowView
@end
