#import "preferences_window_controller.h"
#import <AppKit/AppKit.h>

// Project headers
#include "browser_history_importer.h"
#import "controllers/models_pane_controller.h"
#import "general_pane_controller.h"
#import "controllers/plugins_pane_controller.h"

using launcher::ui::SaveSettingsCallback;

#ifdef __OBJC__

@interface PreferencesWindowController () <NSToolbarDelegate>
// Removed per refactor: old General-pane UI control outlets now live in GeneralPaneController.
@end

@implementation PreferencesWindowController {
    // We currently only have one pane (General) but structure allows more panes later.
    NSToolbarItemIdentifier _generalIdentifier;
    NSMutableDictionary<NSString*, NSViewController*>* _panes;
}

- (instancetype)initWithSaveCallback:(SaveSettingsCallback)callback {
    self = [super initWithWindow:nil];
    if (self) {
        self.saveCallback = callback;
        _generalIdentifier = @"general";
        [self setupWindow];
    }
    return self;
}

#pragma mark - Window & Toolbar Setup

- (void)setupWindow {
    // Create preferences window (modern style)
    NSRect frame = NSMakeRect(0, 0, 800, 500);
    NSWindow* window = [[NSWindow alloc] initWithContentRect:frame
                                                   styleMask:NSWindowStyleMaskTitled |
                                                             NSWindowStyleMaskClosable |
                                                             NSWindowStyleMaskMiniaturizable |
                                                             NSWindowStyleMaskUnifiedTitleAndToolbar
                                                     backing:NSBackingStoreBuffered
                                                       defer:NO];
    [window center];
    window.title = @"Preferences";
    if (@available(macOS 11.0, *)) {
        window.toolbarStyle = NSWindowToolbarStylePreference;
    }

    // Build toolbar
    NSToolbar* toolbar = [[NSToolbar alloc] initWithIdentifier:@"PreferencesToolbar"];
    toolbar.delegate = self;
    toolbar.allowsUserCustomization = NO;
    toolbar.displayMode = NSToolbarDisplayModeIconAndLabel;
    toolbar.selectedItemIdentifier = _generalIdentifier;
    [window setToolbar:toolbar];

    window.delegate = (id<NSWindowDelegate>)self;

    // Instantiate panes --------------------------------------------------
    _panes = [NSMutableDictionary dictionary];

    GeneralPaneController* generalVC = [[GeneralPaneController alloc] init];
    _panes[_generalIdentifier] = generalVC;

    ModelsPaneController* modelsVC = [[ModelsPaneController alloc] init];
    _panes[@"models"] = modelsVC;

    // New Plugins pane ---------------------------------------------------
    PluginsPaneController* pluginsVC = [[PluginsPaneController alloc] init];
    _panes[@"plugins"] = pluginsVC;

    // Placeholder panes
    NSArray* placeholderIdentifiers = @[ @"actions", @"appearance", @"advanced" ];
    for (NSString* ident in placeholderIdentifiers) {
        NSViewController* vc = [[NSViewController alloc] init];
        NSView* placeholderView = [[NSView alloc] initWithFrame:frame];
        placeholderView.autoresizingMask = NSViewWidthSizable | NSViewHeightSizable;

        NSTextField* label = [NSTextField labelWithString:[ident.capitalizedString stringByAppendingString:@" Preferences (Coming Soon)"]];
        label.font = [NSFont boldSystemFontOfSize:18.0];
        label.translatesAutoresizingMaskIntoConstraints = NO;
        [placeholderView addSubview:label];
        [NSLayoutConstraint activateConstraints:@[
            [label.centerXAnchor constraintEqualToAnchor:placeholderView.centerXAnchor],
            [label.centerYAnchor constraintEqualToAnchor:placeholderView.centerYAnchor]
        ]];

        vc.view = placeholderView;
        _panes[ident] = vc;
    }

    // Final window assignments
    self.window = window;
    self.contentViewController = generalVC;

    // Ensure window keeps intended size after contentViewController assignment.
    [window setContentSize:NSMakeSize(800, 500)];
}

#pragma mark - Show / Hide

- (void)showWindow {
    [self.window makeKeyAndOrderFront:nil];
}

- (void)closeWindow {
    [self.window orderOut:nil];
}

#pragma mark - Toolbar delegate

- (NSArray<NSToolbarItemIdentifier>*)toolbarAllowedItemIdentifiers:(__unused NSToolbar*)toolbar {
    return @[ _generalIdentifier, @"models", @"actions", @"appearance", @"advanced", @"plugins" ];
}

- (NSArray<NSToolbarItemIdentifier>*)toolbarDefaultItemIdentifiers:(__unused NSToolbar*)toolbar {
    return @[ _generalIdentifier, @"models", @"actions", @"appearance", @"advanced", @"plugins" ];
}

- (NSArray<NSToolbarItemIdentifier>*)toolbarSelectableItemIdentifiers:(__unused NSToolbar*)toolbar {
    return @[ _generalIdentifier, @"models", @"actions", @"appearance", @"advanced", @"plugins" ];
}

- (NSToolbarItem*)toolbar:(__unused NSToolbar*)toolbar
    itemForItemIdentifier:(NSToolbarItemIdentifier)itemIdentifier
 willBeInsertedIntoToolbar:(BOOL)__unused flag {
    if ([itemIdentifier isEqualToString:_generalIdentifier]) {
        return [self makeToolbarItemWithIdentifier:itemIdentifier
                                              icon:@"gearshape"
                                             label:@"General"];
    } else if ([itemIdentifier isEqualToString:@"models"]) {
        return [self makeToolbarItemWithIdentifier:itemIdentifier icon:@"sparkles" label:@"Models"];
    } else if ([itemIdentifier isEqualToString:@"actions"]) {
        return [self makeToolbarItemWithIdentifier:itemIdentifier icon:@"bolt" label:@"Actions"];
    } else if ([itemIdentifier isEqualToString:@"appearance"]) {
        return [self makeToolbarItemWithIdentifier:itemIdentifier icon:@"paintbrush" label:@"Appearance"];
    } else if ([itemIdentifier isEqualToString:@"advanced"]) {
        return [self makeToolbarItemWithIdentifier:itemIdentifier icon:@"gearshape.2" label:@"Advanced"];
    } else if ([itemIdentifier isEqualToString:@"plugins"]) {
        return [self makeToolbarItemWithIdentifier:itemIdentifier icon:@"puzzlepiece.extension" label:@"Plugins"];
    }
    return nil;
}

- (void)toolbarItemClicked:(id)sender {
    NSToolbarItem* item = (NSToolbarItem*)sender;
    NSString* ident = item.itemIdentifier;
    NSToolbar* tb = self.window.toolbar;
    tb.selectedItemIdentifier = ident;

    NSViewController* vc = _panes[ident];
    if (vc) {
        self.contentViewController = vc;
    }
}

#pragma mark - Helpers

- (NSToolbarItem*)makeToolbarItemWithIdentifier:(NSToolbarItemIdentifier)identifier
                                          icon:(NSString*)systemImageName
                                         label:(NSString*)label {
    NSToolbarItem* item = [[NSToolbarItem alloc] initWithItemIdentifier:identifier];
    if (@available(macOS 11.0, *)) {
        item.image = [NSImage imageWithSystemSymbolName:systemImageName accessibilityDescription:label];
    } else {
        item.image = [NSImage imageNamed:NSImageNamePreferencesGeneral];
    }
    item.label = label;
    item.paletteLabel = label;
    item.target = self;
    item.action = @selector(toolbarItemClicked:);
    return item;
}

#pragma mark - NSWindowDelegate

- (void)windowWillClose:(__unused NSNotification *)notification {
    // Ask each pane to save changes if it implements saveChanges
    for (NSString* key in _panes) {
        id pane = _panes[key];
        if ([pane respondsToSelector:@selector(saveChanges)]) {
            [pane performSelector:@selector(saveChanges) withObject:nil];
        }
    }
    if (self.saveCallback) {
        self.saveCallback();
    }
}

#pragma mark - Compatibility stubs

// Legacy callers still expect these selectors. They now simply forward to the
// per-pane controllers that apply changes immediately.
- (void)loadSettings {
    // General pane loads its settings during -loadView, nothing else to do.
}

- (void)saveSettings {
    // Each pane writes through on every action; additionally windowWillClose
    // already iterates over _panes and invokes saveChanges.  Keeping empty for
    // backwards compatibility.
}

@end

#endif  // __OBJC__ 