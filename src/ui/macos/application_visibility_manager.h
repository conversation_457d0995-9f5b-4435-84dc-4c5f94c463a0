#pragma once

#include <atomic>
#include <dispatch/dispatch.h>

namespace launcher {
namespace ui {

/**
 * Manages the application's visibility in macOS app switcher (⌘-Tab) and Dock.
 * Toggles between foreground and background app modes based on whether
 * chat windows are currently open.
 */
class ApplicationVisibilityManager {
public:
    /**
     * Returns the shared singleton instance.
     */
    static ApplicationVisibilityManager& shared();
    
    /**
     * Call when a chat window is opened. When the first window opens,
     * transforms the app into a foreground application visible in ⌘-Tab.
     */
    void openChatWindow();
    
    /**
     * Call when a chat window is closed. When the last window closes,
     * transforms the app back into a background/accessory application.
     */
    void closeChatWindow();
    
    /**
     * Returns whether the application is currently in foreground mode.
     */
    bool isInForegroundMode() const;

    /**
     * Ensures the application is a foreground (NSApplicationActivationPolicyRegular)
     * app.  If it is already in foreground mode the call is a no-op.  Unlike
     * openChatWindow(), this does not increment the internal window count and
     * therefore should be used for transient foreground needs such as
     * displaying the Settings window.
     */
    void ensureForeground();

private:
    ApplicationVisibilityManager();
    ~ApplicationVisibilityManager();
    
    // Disallow copying and assignment
    ApplicationVisibilityManager(const ApplicationVisibilityManager&) = delete;
    ApplicationVisibilityManager& operator=(const ApplicationVisibilityManager&) = delete;
    
    void transformToForegroundApp();
    void transformToBackgroundApp();
    
    // Reference count of open chat windows
    std::atomic<int> openWindowCount_;
    
    // Current visibility state
    std::atomic<bool> isInForegroundMode_;
    
    // Serial queue for synchronizing visibility transitions
    dispatch_queue_t queue_;
};

}} // namespace launcher::ui 