#pragma once

//------------------------------------------------------------------------------
// status_menu_controller.h
//
// Encapsulates construction of the NSStatusItem and its attached menu. The
// controller is initialised with a generic target (e.g. AppDelegate) whose
// selector methods are wired to the various menu commands.
//------------------------------------------------------------------------------

#import <AppKit/AppKit.h>

@interface StatusMenuController : NSObject

// Designated initialiser. |target| receives the action selectors that were
// previously set up in AppDelegate (showLauncherBar:, showChat:, etc.).
- (instancetype)initWithTarget:(id)target;

// Exposes the created status-bar item so callers can tweak image, etc.
@property(nonatomic, readonly) NSStatusItem *statusItem;

// Convenience to update the "Launch at Login" check-mark dynamically when the
// underlying setting changes from elsewhere.
- (void)updateLaunchAtLoginState:(BOOL)isEnabled;

@end
