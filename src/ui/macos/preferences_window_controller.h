#pragma once

#include "../common/result_item.h"
#include <functional>

namespace launcher {
namespace ui { using SaveSettingsCallback = std::function<void()>; }  // namespace ui
}  // namespace launcher

#ifdef __OBJC__
#import <Cocoa/Cocoa.h>

/**
 * A window controller that presents application preferences in the native macOS
 * "Settings" style (Finder/Safari look & feel). All UI is created
 * programmatically (no XIB/Storyboard) to satisfy project guidelines.
 */
@interface PreferencesWindowController : NSWindowController <NSToolbarDelegate>

/** Callback invoked when the user saves preferences. */
@property(nonatomic, assign) launcher::ui::SaveSettingsCallback saveCallback;

/** Designated initializer. */
- (instancetype)initWithSaveCallback:(launcher::ui::SaveSettingsCallback)callback;

/** Display the window, bringing the app to foreground if required. */
- (void)showWindow;

/** Close the window without saving. */
- (void)closeWindow;

/** Load current settings from persistent storage into UI controls. */
- (void)loadSettings;

/** Persist the settings that are currently shown in the UI. */
- (void)saveSettings;

@end

// Alias so existing Objective-C code that still refers to SettingsWindowController
// continues to compile with zero-touch changes.
@compatibility_alias SettingsWindowController PreferencesWindowController;

#endif  // __OBJC__ 