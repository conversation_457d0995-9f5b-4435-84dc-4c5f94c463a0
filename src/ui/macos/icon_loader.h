#pragma once

//------------------------------------------------------------------------------
// icon_loader.h
//
// Utility helpers for resolving the launcher's application and menu-bar icons
// from bundle resources, falling back to the embedded `AppIconData` or a
// programmatically generated placeholder.
//------------------------------------------------------------------------------

#import <AppKit/AppKit.h>

// Returns the 18-pt monochrome icon used for the status-bar item.  The function
// caches the result so subsequent calls are inexpensive.
NSImage *LoadMenuBarIcon(void);

// Returns the multi-resolution application icon that is shown in the Dock,
// ⌘Tab switcher and about panel.  Cached after first construction.
NSImage *LoadAppIcon(void); 