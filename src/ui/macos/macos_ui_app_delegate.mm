#include "macos_ui.h"
#include "macos_ui_internal.h"

#include <iostream>
#include <algorithm>
#include <chrono>
#include <cmath>
#include <cstdlib>
#include <ctime>
#include <filesystem>
#include <fstream>
#include <map>
#include <memory>
#include <regex>
#include <sstream>
#include <string>
#include <thread>
#include <vector>

#include "../../core/util/debug.h"
#include "../common/message_formatter.h"
#include "../chat/macos_chat_ui.h"
#include "preferences_window_controller.h"
#include "browser_history_importer.h"
#import "../chat/session_autosave_manager.h"
#import "../chat/tabs/tab_group_manager.h"
#include "tab_switcher_controller.h"
#include "../chat/chat_window_factory.h"
#import "hud_window_utils.h"
#import <CoreGraphics/CoreGraphics.h>

#import <Cocoa/Cocoa.h>
#import <Foundation/Foundation.h>
#import <AppKit/AppKit.h>
#import <Carbon/Carbon.h>
#import <QuartzCore/QuartzCore.h>
#import <objc/runtime.h>
#import <UniformTypeIdentifiers/UniformTypeIdentifiers.h>
#import <UniformTypeIdentifiers/UTCoreTypes.h>
#import <AppKit/NSTableViewDiffableDataSource.h>
#import <float.h>

#import "search/search_coordinator.h" // New reactive search core
#import "search/result_model.h"
#import "default_browser_icon.h"
#include <nlohmann/json.hpp>
#import "icon_loader.h"
#import "../../third_party/mas_shortcut/include/MASShortcutMonitor.h"
#include "../macos/application_visibility_manager.h"
#include "hotkey_utils.h"
#import "status_menu_controller.h"
#import "chat_navigator.h"
#import "search/search_results_adapter.h"
#include "../../core/app_context.h"
#include "../common/result_item.h"
#import "controllers/launcher_bar_controller.h"
#include "../../core/history/history_manager_interface.h"
#include "../../core/interfaces/iconfig_manager.h"
#include "../../core/interfaces/ibrowser_history_importer.h"  // @add_include
#include "injected_services.h"  // shared helpers
#include <cassert>
#include "../../core/util/result.h"

using namespace launcher::core;
using namespace launcher::ui;

using launcher::ui::ConfigService;

// Objective-C implementations
@interface AppDelegate () <NSWindowDelegate>
@property(nonatomic, strong) StatusMenuController *statusMenuController;
@property(nonatomic, strong) LauncherBarController *launcherController; // Extracted controller for launcher UI
@property(nonatomic, strong) SearchResultsAdapter *searchResultsAdapter;
@end

@implementation AppDelegate

// Add hidden Edit menu so common text shortcuts (⌘C/⌘V/⌘X/⌘A) work even in LSUIElement mode
static void SetupStandardEditMenu() {
    // If a main menu already exists, respect it.
    if ([NSApp mainMenu] != nil) { return; }

    NSMenu *mainMenu = [[NSMenu alloc] initWithTitle:@"MainMenu"];

    // Edit menu placeholder in menu bar (never shown for LSUIElement apps)
    NSMenuItem *editRootItem = [[NSMenuItem alloc] initWithTitle:@"Edit"
                                                          action:NULL
                                                   keyEquivalent:@""];
    [mainMenu addItem:editRootItem];

    // Build the Edit submenu with standard actions
    NSMenu *editSub = [[NSMenu alloc] initWithTitle:@"Edit"];
    struct ShortcutSpec { SEL action; NSString *title; NSString *key; } specs[] = {
        { @selector(cut:),        @"Cut",        @"x" },
        { @selector(copy:),       @"Copy",       @"c" },
        { @selector(paste:),      @"Paste",      @"v" },
        { @selector(selectAll:),  @"Select All", @"a" },
    };
    for (const auto &s : specs) {
        NSMenuItem *mi = [[NSMenuItem alloc] initWithTitle:s.title
                                                    action:s.action
                                             keyEquivalent:s.key];
        mi.keyEquivalentModifierMask = NSEventModifierFlagCommand;
        [editSub addItem:mi];
    }

    [editRootItem setSubmenu:editSub];
    [NSApp setMainMenu:mainMenu];
}

- (void)applicationDidFinishLaunching:(NSNotification*)notification {
    // Install hidden Edit menu for standard text operations
    SetupStandardEditMenu();
    
    // Restore previous chat session windows (if any). If none were restored,
    // fall back to creating a brand-new chat window.
    bool restored = launcher::ui::SessionAutosaveManager::shared().restorePreviousSession();
    if (!restored) {
        // Use existing helper to instantiate and show a chat window.
        [self showChat:nil];
    }
    
    // Initialize search-related properties
    self.appResults = [NSMutableArray array];
    self.fileResults = [NSMutableArray array];
    self.websiteResults = [NSMutableArray array];
    self.isSearching = NO;
    
    // Initialize browser history importer in background
    dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
        @autoreleasepool {
            // Import browser history
            IBrowserHistoryImporter* historyImporterPtr = nullptr;
            if (self.ctx && self.ctx->browserHistory) {
                historyImporterPtr = self.ctx->browserHistory.get();
            } else {
                return; // no importer available
            }

            auto &historyImporter = *historyImporterPtr;
            int importedCount = historyImporter.importAllBrowserHistory();
            DBM(@"Imported %d website entries from browser history", importedCount);

            // Feed top websites into HistoryManager as bootstrap using
            // recorded visit statistics.
            auto *ctx = self.ctx; // AppDelegate has property ctx
            if (!ctx || !ctx->historyManager) { return; }
            auto &historyMgr = *ctx->historyManager;
            auto topSites = historyImporter.getTopEntries(30);
            for (const auto &site : topSites) {
                launcher::ui::ResultItem item(site.title.empty() ? site.domain : site.title,
                                                           site.url,
                                                           "",
                                                           1.0,
                                                           "website");
                historyMgr.recordBootstrap(item,
                                           static_cast<std::time_t>(site.lastVisitTime),
                                           static_cast<uint32_t>(site.visitCount));
            }
        }
    });
    
    // Build status-bar item and menu via helper controller
    self.statusMenuController = [[StatusMenuController alloc] initWithTarget:self];
    self.statusItem = self.statusMenuController.statusItem;
    
    // Resolve icons through dedicated helper (ensures single-responsibility & caching)
    NSImage *menuBarIcon = LoadMenuBarIcon();
    NSImage *appIcon     = LoadAppIcon();
    
    // Set the menu bar icon
    [menuBarIcon setSize:NSMakeSize(18, 18)];
    // [menuBarIcon setTemplate:YES]; // Make it work well in both light and dark mode
    self.statusItem.button.image = menuBarIcon;
    
    // Set the application icon
    [NSApp setApplicationIconImage:appIcon];
    
    // Create search window
    [self createLauncherBar];

    // Register global hotkeys (launcher & chat)
    [self _updateGlobalHotkeys];

    // Listen for runtime changes emitted by Preferences.
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(_launcherHotkeyDidChange:)
                                                 name:@"LauncherHotkeyDidChange"
                                               object:nil];

    // Observer for chat hotkey updates
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(_chatHotkeyDidChange:)
                                                 name:@"ChatHotkeyDidChange"
                                               object:nil];

    // Register for local key events (navigation, escape handling, etc.)
    [NSEvent addLocalMonitorForEventsMatchingMask:NSEventMaskKeyDown handler:^NSEvent*(NSEvent* event) {
        // Check if the key window is the launcher bar
        BOOL isLauncherBarKey = [self.launcherBar isKeyWindow];

        if ([event keyCode] == 53) { // Escape key
            if (isLauncherBarKey) {
                [self hideLauncherBarAndRestore];
                return nil; // Consume
            }
            return event;
        } else if (([event modifierFlags] & NSEventModifierFlagCommand) &&
                   [event keyCode] == 49) {
            // Command+Space – let system handle if our global hot-key failed
            return event;
        } else if (isLauncherBarKey && [event keyCode] == 125) { // Down arrow
            int selectedRow = [self.resultsTable selectedRow];
            if (selectedRow < [self.resultsTable numberOfRows] - 1) {
                [self.resultsTable selectRowIndexes:[NSIndexSet indexSetWithIndex:selectedRow + 1] byExtendingSelection:NO];
                [self.resultsTable scrollRowToVisible:selectedRow + 1];
                self.uiInstance->setSelectedIndex(selectedRow + 1);
            }
            return nil;
        } else if (isLauncherBarKey && [event keyCode] == 126) { // Up arrow
            int selectedRow = [self.resultsTable selectedRow];
            if (selectedRow > 0) {
                [self.resultsTable selectRowIndexes:[NSIndexSet indexSetWithIndex:selectedRow - 1] byExtendingSelection:NO];
                [self.resultsTable scrollRowToVisible:selectedRow - 1];
                self.uiInstance->setSelectedIndex(selectedRow - 1);
            }
            return nil;
        } else if (isLauncherBarKey &&
                   ([event modifierFlags] & NSEventModifierFlagCommand) &&
                   [event keyCode] == 36) { // Cmd+Return → open chat directly
            NSString *query = self.searchField.stringValue ?: @"";
            if (query.length > 0) {
                [self openChatAndSendQuestion:query];
                return nil; // Consume event – launcher will hide inside helper
            }
        } else if (isLauncherBarKey && [event keyCode] == 36) { // Return key
            if ([self.resultsTable selectedRow] >= 0) {
                [self tableViewDoubleClick:self.resultsTable];
                return nil;
            }
        } else if (([event modifierFlags] & NSEventModifierFlagCommand) && [event keyCode] == 6) { // Cmd+Z
            if (!isLauncherBarKey) { return event; }
        } else if (([event modifierFlags] & NSEventModifierFlagCommand) &&
                   ([event modifierFlags] & NSEventModifierFlagShift) && [event keyCode] == 6) {
            if (!isLauncherBarKey) { return event; }
        }

        return event; // Default: pass through
    }];

    launcher::ui::SessionAutosaveManager::shared().start();

    // Bridge new reactive search core
    self.searchResultsAdapter = [[SearchResultsAdapter alloc] initWithAppDelegate:self];
    [SearchCoordinator shared].delegate = self.searchResultsAdapter;
}

- (void)createLauncherBar {
    // Delegate construction to the new LauncherBarController to reduce
    // responsibilities inside AppDelegate.
    if (!self.launcherController) {
        self.launcherController = [[LauncherBarController alloc] initWithAppDelegate:self];
    }
    [self.launcherController createLauncherBar];
}

- (void)showLauncherBar:(id)sender {
    // Forward to controller
    if (!self.launcherController) { [self createLauncherBar]; }
    [self.launcherController show];
}

- (void)toggleLauncherBar:(id)sender {
    if (!self.launcherController) { [self createLauncherBar]; }
    [self.launcherController toggle];
}

- (void)hideLauncherBarAndRestore {
    [self.launcherController hideAndRestore];
}

- (void)showAbout:(id)sender {
    NSString* appName = @"MicroLauncher";
    NSString* version = @"0.1.0";
    NSString* copyright = @"© 2025 MicroLauncher Team";
    NSString* credits = @"A micro application launcher for macOS and Windows.\n\nDeveloped by the MicroLauncher Team.";
    
    // Create an attributed string for credits with the copyright info
    NSMutableAttributedString* creditsAttrString = [[NSMutableAttributedString alloc] 
                                                   initWithString:[NSString stringWithFormat:@"%@\n\n%@", 
                                                                  credits, copyright]];
    
    NSDictionary* options = @{
        NSAboutPanelOptionApplicationName: appName,
        NSAboutPanelOptionApplicationVersion: version,
        NSAboutPanelOptionVersion: version,
        NSAboutPanelOptionCredits: creditsAttrString
    };
    
    [NSApp activateIgnoringOtherApps:YES];
    [NSApp orderFrontStandardAboutPanelWithOptions:options];
}

- (void)showSettings:(id)sender {
    // Bridge the standard macOS "Preferences…" selector to the unified
    // C++ implementation owned by MacOSUI.  This guarantees we always use
    // the singleton PreferencesWindowController that was pre-created during
    // MacOSUI::initialize.
    if (self.uiInstance) {
        // Forward to the cross-language facade.  The default parameter is
        // acceptable – callers rarely need a save callback from the menu.
        self.uiInstance->showSettingsWindow();
    } else {
        // Extremely defensive fall-back: create a temporary controller so
        // the user still gets a window even if uiInstance is unexpectedly
        // nil (unit tests, edge bootstrap cases, etc.).
        if (!self.settingsWindowController) {
            self.settingsWindowController = [[PreferencesWindowController alloc] initWithSaveCallback:nil];
        }
        [self.settingsWindowController loadSettings];
        [self.settingsWindowController showWindow];
        [NSApp activateIgnoringOtherApps:YES];
    }
}

- (void)tableViewDoubleClick:(id)sender {
    NSInteger row = [self.resultsTable clickedRow];
    if (row < 0) row = [self.resultsTable selectedRow];
    
    // Corrected logging call with format specifier
    DBM(@"Called with sender: %@", [sender description]);

    @try {
        if (row >= 0 && (NSUInteger)row < [self.resultsDelegate.results count]) { // Added cast
            NSDictionary* result = self.resultsDelegate.results[row];
            // Check if resultsDelegate is valid
            if (!self.resultsDelegate) {
                ERM(@"resultsDelegate is nil"); // Changed func arg to C-string
                return;
            }
            
            // Check if results array is valid
            if (!self.resultsDelegate.results) {
                ERM(@"results array is nil"); // Changed func arg to C-string
                return;
            }
            
            NSUInteger resultsCount = [self.resultsDelegate.results count];
            DBM(@"Results count: %lu", resultsCount);
            
            // Check if selected row is valid
            if (row < 0 || (NSUInteger)row >= resultsCount) { // Added cast
                ERM(@"Selected row %ld is out of bounds (0-%lu)", row, resultsCount - 1); // Changed func arg to C-string
                return;
            }
            
            // Check if launchCallback is set
            if (!self.launchCallback) {
                ERM(@"launchCallback is nil"); // Changed func arg to C-string
                return;
            }
            
            DBM(@"Calling launchSelectedApplication");
            [self launchSelectedApplication];
        }
    } @catch (NSException *exception) {
        ERM(@"%@", [exception description]); // Changed func arg to C-string
    }
}

- (void)launchSelectedApplication {
    NSInteger selectedRow = [self.resultsTable selectedRow];
    
    // Validate row index
    if (selectedRow < 0) {
        DBM(@"No row selected");
        return;
    }
    
    // Validate results delegate
    if (!self.resultsDelegate) {
        ERM(@"resultsDelegate is nil"); // Changed func arg to C-string
        return;
    }
    
    // Validate results array
    if (!self.resultsDelegate.results) {
        ERM(@"results array is nil"); // Changed func arg to C-string
        return;
    }
    
    NSUInteger resultsCount = [self.resultsDelegate.results count];
    
    // Validate row bounds (selectedRow >= 0 check is done above)
    if ((NSUInteger)selectedRow >= resultsCount) { // Added cast
        ERM(@"row index %ld is out of bounds (0-%lu)", selectedRow, resultsCount - 1); // Changed func arg to C-string
        return;
    }
    
    // Get result dictionary
    NSDictionary *result = self.resultsDelegate.results[selectedRow];
    if (!result) {
        ERM(@"result at index %ld is nil", selectedRow); // Changed func arg to C-string
        return;
    }
    
    // Extract values with safety checks
    NSString *name = result[@"name"] ?: @"";
    NSString *path = result[@"path"] ?: @"";
    NSString *iconPath = result[@"iconPath"] ?: @"";
    double score = [result[@"score"] doubleValue];
    NSString *type = result[@"type"] ?: @"app";
        
    // Only proceed if we have a valid path for non-chat items
    if (path.length == 0 && ![type isEqualToString:@"chat"] && ![type isEqualToString:@"chat_query"]) {
        ERM(@"Cannot launch: Invalid path"); // Changed file/func args to C-string
        return;
    }
    
    // Check if this is a website, file, or an app
    if ([type isEqualToString:@"website"]) {
        // Open the website URL in the default browser
        NSURL *url = [NSURL URLWithString:path];
        if (url) {
            [[NSWorkspace sharedWorkspace] openURL:url];
            
            // Hide window
            [self.launcherBar orderOut:nil];
        } else {
            // ERM(@"Invalid URL: %@", path); // Incorrect format usage
            ERM(@"Invalid URL: %@", path); // Changed file/func args to C-string
        }
    } else if ([type isEqualToString:@"file"]) {
        // Open the file with the default application
        NSURL *fileURL = [NSURL fileURLWithPath:path];
        // Revert to using the older openURL method due to deployment target issues
        #pragma clang diagnostic push
        #pragma clang diagnostic ignored "-Wdeprecated-declarations"
        BOOL success = [[NSWorkspace sharedWorkspace] openURL:fileURL];
        if (!success) {
            ERM(@"Failed to open file: %@", path);
        }
        #pragma clang diagnostic pop
        
        // Hide window
        [self.launcherBar orderOut:nil];
    } else if ([type isEqualToString:@"chat"]) {
        // Identifier now carries the raw conversation ID with no prefix.
        NSString *convId = result[@"identifier"] ?: @"";
        if (convId.length > 0) {
            [self openChatConversationWithId:convId];
        } else {
            // If somehow missing, treat it as an ad-hoc question.
            NSString *question = result[@"question"] ?: name;
            [self openChatAndSendQuestion:question];
        }
        return;
    } else if ([type isEqualToString:@"chat_query"]) {
        NSString *question = result[@"question"] ?: name;
        [self openChatAndSendQuestion:question];
        return;
    } else {
        // Validate launch callback
        if (!self.launchCallback) {
            ERM(@"launchCallback is nil"); // Changed file/func args to C-string
            return;
        }
        
        // Create ResultItem
        launcher::ui::ResultItem item(
            [name UTF8String],
            [path UTF8String],
            [iconPath UTF8String],
            score,
            [type UTF8String]
        );
        
        // Call launch callback
        self.launchCallback(item);
        
        // Hide window
        [self.launcherBar orderOut:nil];
    }
}

- (void)statusItemClicked:(id)sender {
    // Show the menu when the status item is clicked
    [self.statusItem.button performClick:nil];
}

- (void)toggleLaunchAtLogin:(id)sender {
    NSMenuItem* menuItem = (NSMenuItem*)sender;
    bool currentState = [menuItem state] == NSControlStateValueOn;
    bool newState = !currentState;
    
    // Update the menu item state
    [menuItem setState:newState ? NSControlStateValueOn : NSControlStateValueOff];
    
    // Update the launch at login setting via platform helper
    auto res = self.uiInstance->getPlatform()->setLaunchAtLogin(newState);
    bool success = static_cast<bool>(res);
    if (success) {
        // Persist setting using injected configuration service when available.
        ConfigService().setBool("general.startup_on_login", newState);
        ConfigService().requestSave();
        
        DBM(@"Launch at login %@", newState ? @"enabled" : @"disabled");
    } else {
        // Revert the menu item state if the operation failed
        [menuItem setState:currentState ? NSControlStateValueOn : NSControlStateValueOff];
        
        // Show an error alert
        NSAlert* alert = [[NSAlert alloc] init];
        [alert setMessageText:@"Error"];
        NSString *msg = @"Failed to update launch at login setting.";
        if (!success) {
            std::string err = res.error();
            msg = [NSString stringWithUTF8String:err.c_str()];
        }
        [alert setInformativeText:msg];
        [alert addButtonWithTitle:@"OK"];
        [alert runModal];
    }
}

- (void)checkForUpdates:(id)sender {
    // For now, just show an alert that this feature is coming soon
    NSAlert *alert = [[NSAlert alloc] init];
    [alert setMessageText:@"Updates"];
    [alert setInformativeText:@"Automatic updates will be available in a future version."];
    [alert addButtonWithTitle:@"OK"];
    [alert runModal];
}

- (void)showSearchInProgress:(BOOL)inProgress {
    dispatch_async(dispatch_get_main_queue(), ^{
        if (inProgress) {
            [self.searchProgressIndicator setHidden:NO];
            [self.searchProgressIndicator startAnimation:nil];
        } else {
            [self.searchProgressIndicator stopAnimation:nil];
            [self.searchProgressIndicator setHidden:YES];
        }
    });
}

- (void)performParallelSearch:(NSString*)searchText {
    // Show activity indicator
    [self showSearchInProgress:YES];

    // Forward query to the unified reactive search coordinator. It will handle
    // debouncing, generation tracking, and parallel domain operations.
    [[SearchCoordinator shared] submitQuery:searchText];
}

- (void)showChat:(id)sender {
    // Always create a brand-new chat window via the unified factory for
    // consistent behaviour with ⌘N and menu actions.
    auto newUI = launcher::ui::ChatWindowFactory::createNewWindow();
    if (newUI) {
        // Store reference so helper methods (e.g. openChatAndSendQuestion:)
        // can reuse it as an anchor.
        self.chatUI = newUI;
    }
}

- (void)applicationWillTerminate:(NSNotification *)notification {
    // Release global hot-key resources
    [[MASShortcutMonitor sharedMonitor] unregisterAllShortcuts];
}

// === Added helper to spawn chat tab and send question ===
- (void)openChatAndSendQuestion:(NSString *)question {
    [[ChatNavigator shared] openChatAndSendQuestion:question];
    [self hideLauncherBarAndRestore];
}

// === Open existing conversation by identifier ===
- (void)openChatConversationWithId:(NSString *)convId {
    [[ChatNavigator shared] openConversationWithId:convId];
    [self hideLauncherBarAndRestore];
}

#pragma mark - Hotkey change handling

- (void)_launcherHotkeyDidChange:(NSNotification *)note {
    (void)note; // parameters currently unused; kept for future use
    [self _updateGlobalHotkeys];
}

- (void)_chatHotkeyDidChange:(NSNotification *)note {
    [self _updateGlobalHotkeys];
}

- (void)_updateGlobalHotkeys {
    // Prefer injected config manager; fallback to singleton.
    const launcher::core::IConfigManager& cm = ConfigService();

    // Launcher hotkey
    int launcherKey = cm.getInt("general.launcher_hotkey.keycode", kVK_Space);
    UInt32 launcherMods = (UInt32)cm.getInt("general.launcher_hotkey.mods", cmdKey);
    MASShortcut *launcherSC = [MASShortcut shortcutWithKeyCode:launcherKey
                                              modifierFlags:CocoaModifiersFromCarbon(launcherMods)];

    // Chat hotkey
    int chatKey = cm.getInt("general.chat_hotkey.keycode", kVK_ANSI_E);
    UInt32 chatMods = (UInt32)cm.getInt("general.chat_hotkey.mods", cmdKey);
    MASShortcut *chatSC = [MASShortcut shortcutWithKeyCode:chatKey
                                           modifierFlags:CocoaModifiersFromCarbon(chatMods)];

    __weak __typeof(self) weakSelf = self;

    [[MASShortcutMonitor sharedMonitor] unregisterAllShortcuts];

    // Register launcher shortcut (toggle launcher bar)
    [[MASShortcutMonitor sharedMonitor] registerShortcut:launcherSC withAction:^{
        [weakSelf toggleLauncherBar:nil];
    }];

    // Register chat shortcut (open chat window)
    [[MASShortcutMonitor sharedMonitor] registerShortcut:chatSC withAction:^{
        [weakSelf showChat:nil];
    }];
}

#pragma mark - NSWindowDelegate

- (void)windowDidResignKey:(NSNotification *)notification {
    if (notification.object == self.launcherBar) {
        [self hideLauncherBarAndRestore];
    }
}

@end
