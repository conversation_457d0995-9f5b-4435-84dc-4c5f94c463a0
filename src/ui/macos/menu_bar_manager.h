// Created new file
#ifndef LAUNCHER_UI_MENU_BAR_MANAGER_H_
#define LAUNCHER_UI_MENU_BAR_MANAGER_H_

#include <atomic>

namespace launcher {
namespace ui {

// MenuBarManager is responsible for constructing and owning the main
// NSMenu shown when the application is in foreground mode.  It is a
// lightweight singleton that ensures the menu is created exactly once
// and can later expose helpers to enable/disable dynamic items (e.g.
// "Stop Generating").
class MenuBarManager {
public:
    // Returns the shared singleton instance.
    static MenuBarManager& shared();

    // Builds and installs the main menu bar if it has not been created
    // yet. Safe to call multiple times from any thread.
    void ensureMainMenu();

    // Future extension: reflect streaming state in menu items.
    void setStreamingActive(bool active);

private:
    MenuBarManager();
    ~MenuBarManager();

    MenuBarManager(const MenuBarManager&) = delete;
    MenuBarManager& operator=(const MenuBarManager&) = delete;

    // Internal flag indicating whether the menu bar has been created.
    std::atomic_bool menuCreated_;
};

}  // namespace ui
}  // namespace launcher

#endif  // LAUNCHER_UI_MENU_BAR_MANAGER_H_