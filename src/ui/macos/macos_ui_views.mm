#include "macos_ui.h"
#include "macos_ui_internal.h"

#include <iostream>
#include <algorithm>
#include <chrono>
#include <cmath>
#include <cstdlib>
#include <ctime>
#include <filesystem>
#include <fstream>
#include <map>
#include <memory>
#include <regex>
#include <sstream>
#include <string>
#include <thread>
#include <vector>

#include "../../core/util/debug.h"
#include "../common/message_formatter.h"
#include "../common/result_item.h"
#include "../chat/macos_chat_ui.h"
#include "preferences_window_controller.h"
#include "browser_history_importer.h"
#import "../../third_party/mas_shortcut/include/MASShortcut.h"
#import "../../third_party/mas_shortcut/include/MASShortcutMonitor.h"
#include "hotkey_utils.h"
#include "../macos/application_visibility_manager.h"
#import "../chat/session_autosave_manager.h"
#import "../chat/tabs/tab_group_manager.h"
#include "tab_switcher_controller.h"
#include "../chat/chat_window_factory.h"
#import "hud_window_utils.h"
#import <CoreGraphics/CoreGraphics.h>

#import <Cocoa/Cocoa.h>
#import <Foundation/Foundation.h>
#import <AppKit/AppKit.h>
#import <Carbon/Carbon.h>
#import <QuartzCore/QuartzCore.h>
#import <objc/runtime.h>
#import <UniformTypeIdentifiers/UniformTypeIdentifiers.h>
#import <UniformTypeIdentifiers/UTCoreTypes.h>

#import "resources/AppIcon.h"
#import "services/icon_provider.h"
#import "default_browser_icon.h"
#include <stdio.h>
#include "../../core/app_context.h"
#include "../../core/history/history_manager_interface.h"
#include "../../core/interfaces/iconfig_manager.h"
#include <cassert>
#include "injected_services.h"  // shared helpers

using namespace launcher::core;
using namespace launcher::ui;

// Definition of global chat UI instances vector declared in macos_ui_internal.h
std::vector<std::shared_ptr<launcher::ui::MacOSChatUI>> g_launcher_chat_instances;

using launcher::ui::ConfigService;

@implementation ResultsTableDelegate

- (instancetype)initWithResults:(NSArray*)results {
    self = [super init];
    if (self) {
        self.results = results;
        // Initialize the icon cache if it's nil
        if (!self.iconCache) {
            self.iconCache = [[NSCache alloc] init];
            [self.iconCache setCountLimit:100]; // Limit cache size
        }
    }
    return self;
}

- (NSInteger)numberOfRowsInTableView:(NSTableView *)tableView {
    return [self.results count];
}

- (NSView *)tableView:(NSTableView *)tableView viewForTableColumn:(NSTableColumn *)tableColumn row:(NSInteger)row {
    // Validate row index
    if (row < 0 || (NSUInteger)row >= [self.results count]) { // Added cast
        return nil;
    }
    
    ResultCellView *cellView = [tableView makeViewWithIdentifier:@"ResultCell" owner:self];
    
    if (!cellView) {
        cellView = [[ResultCellView alloc] initWithFrame:NSMakeRect(0, 0, tableView.frame.size.width, tableView.rowHeight)];
        cellView.identifier = @"ResultCell";
    }
    
    // Get result data
    NSDictionary *result = self.results[row];
    if (!result) {
        return cellView; // Return empty cell if no result
    }
    
    NSString *identifier = result[@"identifier"] ?: [NSString stringWithFormat:@"%@-%@", result[@"name"], result[@"path"]];
    NSString *name = result[@"name"];
    NSString *path = result[@"path"];
    NSString *iconPath = result[@"iconPath"];
    NSString *type = result[@"type"] ?: @"app";
    NSString *iconSymbol = result[@"iconSymbol"];
    NSString *tintHex    = result[@"tintHex"];
    NSString *imageUrl = result[@"imageUrl"]; // Get the image URL if available
    
    // Set text fields
    [cellView.nameField setStringValue:name ?: @""];    
    [cellView.pathField setStringValue:path ?: @""];
    
    // Set type field if available
    if ([type isEqualToString:@"file"]) {
        // For files, show the file type
        NSString *fileExtension = [path pathExtension];
        if (fileExtension.length > 0) {
            [cellView.typeField setStringValue:[NSString stringWithFormat:@"File (%@)", fileExtension]];
        } else {
            [cellView.typeField setStringValue:@"File"];
        }
    } else if ([type isEqualToString:@"app"]) {
        [cellView.typeField setStringValue:@"App"];
    } else if ([type isEqualToString:@"website"]) {
        [cellView.typeField setStringValue:@"Web"];
    } else if ([type isEqualToString:@"chat"]) {
        [cellView.typeField setStringValue:@"Chat"];
    } else if ([type isEqualToString:@"chat_query"]) {
        [cellView.typeField setStringValue:@"Ask"];
    } else {
        [cellView.typeField setStringValue:type];
    }
    
    // Decide icon depending on type before falling back to IconProvider
    __block BOOL customIconSet = NO;

    // Helper to convert #RRGGBBAA / #RRGGBB into NSColor
    auto ColorFromHex = ^NSColor *(NSString *hex) {
        if (!hex || hex.length < 7) return nil;
        unsigned int r=0,g=0,b=0,a=255;
        const char *cstr = [hex UTF8String];
        if (hex.length == 9) {
            sscanf(cstr+1, "%02X%02X%02X%02X", &r,&g,&b,&a);
        } else {
            sscanf(cstr+1, "%02X%02X%02X", &r,&g,&b);
        }
        return [NSColor colorWithRed:r/255.0 green:g/255.0 blue:b/255.0 alpha:a/255.0];
    };

    // Helper to create image from emoji character
    auto ImageFromEmoji = ^NSImage *(NSString *emoji, CGFloat size) {
        if (!emoji) return nil;
        NSImage *img = [[NSImage alloc] initWithSize:NSMakeSize(size, size)];
        [img lockFocus];
        NSDictionary *attrs = @{ NSFontAttributeName: [NSFont systemFontOfSize:size] };
        [emoji drawInRect:NSMakeRect(0, 0, size, size) withAttributes:attrs];
        [img unlockFocus];
        return img;
    };

    if ([type isEqualToString:@"chat"] && iconSymbol && iconSymbol.length > 0) {
        // Build SF Symbol image with optional tint
        if (@available(macOS 11.0, *)) {
            NSImage *sym = [NSImage imageWithSystemSymbolName:iconSymbol accessibilityDescription:nil];
            if (sym) {
                NSImageSymbolConfiguration *cfg = [NSImageSymbolConfiguration configurationWithPointSize:24 weight:NSFontWeightRegular];
                sym = [sym imageWithSymbolConfiguration:cfg];
                NSColor *tint = ColorFromHex(tintHex);
                if (tint) {
                    // Manual tint to maintain multicolour symbol compatibility
                    NSImage *copy = [sym copy];
                    [copy setTemplate:YES];
                    [copy lockFocus];
                    [tint set];
                    NSRectFillUsingOperation(NSMakeRect(0, 0, copy.size.width, copy.size.height), NSCompositingOperationSourceAtop);
                    [copy unlockFocus];
                    [copy setTemplate:NO];
                    sym = copy;
                }
                [cellView.appIconView setImage:sym];
                customIconSet = YES;
            }
        }
    }

    if (!customIconSet) {
        NSImage *placeholder = [NSImage imageNamed:NSImageNameQuickLookTemplate];
        [cellView.appIconView setImage:placeholder];
    }

    // Bind identifier to cell to guard against row reuse
    static void *kCellIdentifierKey = &kCellIdentifierKey;
    objc_setAssociatedObject(cellView, kCellIdentifierKey, identifier, OBJC_ASSOCIATION_RETAIN_NONATOMIC);

    // Query IconProvider only when we have not set a custom icon.
    if (!customIconSet) {
        [[IconProvider shared] requestIconForPath:path type:type imageURL:imageUrl completion:^(NSImage * _Nullable icon) {
            if (!icon) return;
            // Verify cell still represents same identifier
            NSString *currentId = objc_getAssociatedObject(cellView, kCellIdentifierKey);
            if (![currentId isEqualToString:identifier]) return;
            [cellView.appIconView setImage:icon];
        }];
    }
    
    // Handle result image if available
    if (imageUrl && imageUrl.length > 0) {
        // Check if we have the image in cache
        NSImage *resultImage = [self.iconCache objectForKey:imageUrl];
        
        if (resultImage) {
            // Use cached image
            [cellView.resultImageView setImage:resultImage];
            
            // Adjust the frame for better positioning
            NSRect currentFrame = cellView.resultImageView.frame;
            currentFrame.origin.y = (tableView.rowHeight - currentFrame.size.height) / 2; // Center vertically
            cellView.resultImageView.frame = currentFrame;
            
            [cellView.resultImageView setHidden:NO];
        } else {
            // Load image asynchronously
            [cellView.resultImageView setHidden:YES]; // Hide until loaded
            
            dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
                NSImage *downloadedImage = nil;
                
                @try {
                    NSURL *url = [NSURL URLWithString:imageUrl];
                    if (url) {
                        NSData *imageData = [NSData dataWithContentsOfURL:url];
                        if (imageData) {
                            downloadedImage = [[NSImage alloc] initWithData:imageData];
                            
                            // Cache the image
                            if (downloadedImage) {
                                [self.iconCache setObject:downloadedImage forKey:imageUrl];
                            }
                        }
                    }
                } @catch (NSException *exception) {
                    // ERM("MacOSUI::loadImage", @"Error loading image from URL %@: %@", imageUrl, [exception description]); // Incorrect format usage
                    ERM(@"Error loading image from URL %@: %@", imageUrl, [exception description]); // Added @ prefix, changed %s to %@, removed UTF8String
                }
                
                // Update UI on main thread
                if (downloadedImage) {
                    dispatch_async(dispatch_get_main_queue(), ^{
                        // Make sure the cell is still visible and for the same row
                        NSInteger visibleRow = [tableView rowForView:cellView];
                        if (visibleRow == row) {
                            // Set the image - this will trigger the rounded corners in RoundedImageView
                            [cellView.resultImageView setImage:downloadedImage];
                            
                            // Adjust the frame for better positioning
                            NSRect currentFrame = cellView.resultImageView.frame;
                            currentFrame.origin.y = (tableView.rowHeight - currentFrame.size.height) / 2; // Center vertically
                            cellView.resultImageView.frame = currentFrame;
                            
                            [cellView.resultImageView setHidden:NO];
                        }
                    });
                }
            });
        }
    } else {
        // No image URL, hide the image view
        [cellView.resultImageView setHidden:YES];
    }
    
    return cellView;
}

- (NSImage*)iconForWebsite:(NSString*)urlString {
    // Prefer the system's default browser icon.
    NSImage *webIcon = GetDefaultBrowserIcon();
    if (webIcon) {
        return webIcon;
    }

    // Try to create a generic web icon if no specific one is available
    webIcon = nil;
    
    // First try to get Safari's icon as a fallback
    NSString* safariPath = @"/Applications/Safari.app";
    if ([[NSFileManager defaultManager] fileExistsAtPath:safariPath]) {
        webIcon = [[NSWorkspace sharedWorkspace] iconForFile:safariPath];
    }
    
    // If Safari icon not available, create a generic globe icon
    if (!webIcon) {
        // Use a system symbol if available (macOS 11+)
        if (@available(macOS 11.0, *)) {
            webIcon = [NSImage imageWithSystemSymbolName:@"globe" accessibilityDescription:@"Website"];
        }
    }
    
    // If still no icon, use a generic document icon
    if (!webIcon) {
        // webIcon = [[NSWorkspace sharedWorkspace] iconForFileType:NSFileTypeForHFSTypeCode(kGenericDocumentIcon)]; // Deprecated
        if (@available(macOS 12.0, *)) {
            webIcon = [[NSWorkspace sharedWorkspace] iconForContentType:UTTypeInternetLocation];
            if (!webIcon) webIcon = [[NSWorkspace sharedWorkspace] iconForContentType:UTTypeItem];
        } else {
            #pragma clang diagnostic push
            #pragma clang diagnostic ignored "-Wdeprecated-declarations"
            webIcon = [[NSWorkspace sharedWorkspace] iconForFileType:NSFileTypeForHFSTypeCode(kGenericDocumentIcon)];
            #pragma clang diagnostic pop
        }
    }
    
    return webIcon;
}

- (void)tableView:(NSTableView *)tableView didAddRowView:(NSTableRowView *)rowView forRow:(NSInteger)row {
    // Custom row styling can be added here
}

- (NSTableRowView *)tableView:(NSTableView *)tableView rowViewForRow:(NSInteger)row {
    CustomTableRowView *rowView = [[CustomTableRowView alloc] initWithFrame:NSZeroRect];
    return rowView;
}

- (NSImage*)iconForFilePath:(NSString*)filePath {
    if (!filePath || filePath.length == 0) {
        return nil;
    }
    
    // Use NSWorkspace to get the icon for the file
    NSImage* icon = [[NSWorkspace sharedWorkspace] iconForFile:filePath];
    
    // If we couldn't get an icon for the specific file, try to get one for its file type
    if (!icon) {
        NSString* fileExtension = [filePath pathExtension];
        if (fileExtension.length > 0) {
            icon = [self iconForFileType:fileExtension];
        }
    }
    
    return icon;
}

- (NSImage*)iconForFileType:(NSString*)fileType {
    if (!fileType || fileType.length == 0) {
        return nil;
    }
    
    // Use NSWorkspace to get the icon for the file type
    // NSImage* icon = [[NSWorkspace sharedWorkspace] iconForFileType:fileType]; // Deprecated
    NSImage *icon = nil;
     if (@available(macOS 12.0, *)) {
        UTType *contentType = [UTType typeWithFilenameExtension:fileType];
        if (contentType) {
            icon = [[NSWorkspace sharedWorkspace] iconForContentType:contentType];
        }
     }
    
    // If we couldn't get an icon for the file type, use a generic document icon
    if (!icon) {
        // icon = [[NSWorkspace sharedWorkspace] iconForFileType:NSFileTypeForHFSTypeCode(kGenericDocumentIcon)]; // Deprecated
         if (@available(macOS 12.0, *)) {
             icon = [[NSWorkspace sharedWorkspace] iconForContentType:UTTypeData]; // Replace UTTypeGeneric
             if (!icon) icon = [[NSWorkspace sharedWorkspace] iconForContentType:UTTypeItem]; // Add fallback
         } else {
             #pragma clang diagnostic push
             #pragma clang diagnostic ignored "-Wdeprecated-declarations"
             icon = [[NSWorkspace sharedWorkspace] iconForFileType:NSFileTypeForHFSTypeCode(kGenericDocumentIcon)];
             #pragma clang diagnostic pop
         }
    }
    
    return icon;
}

@end

@implementation SearchFieldDelegate

- (void)controlTextDidChange:(NSNotification *)notification {
    NSTextField *textField = [notification object];
    NSString *searchText = [textField stringValue];
    
    // Get the app delegate
    AppDelegate *appDelegate = (AppDelegate *)[[NSApplication sharedApplication] delegate];
    
    // If search text is empty, populate recent history results
    if (searchText.length == 0) {
        auto *ctx = appDelegate.ctx;
        if (!ctx || !ctx->historyManager) { return; }

        int maxResCfg = ConfigService().getMaxResults();

        std::size_t historyDisplayLimit = maxResCfg > 0 ? static_cast<std::size_t>(maxResCfg) : 50;

        std::vector<launcher::ui::ResultItem> items =
            ctx->historyManager->getTopItems(historyDisplayLimit);

        NSMutableArray *dicts = [NSMutableArray arrayWithCapacity:items.size()];
        for (const auto &res : items) {
            NSString *name      = [NSString stringWithUTF8String:res.name.c_str()];
            NSString *path      = [NSString stringWithUTF8String:res.path.c_str()];
            NSString *iconPath  = [NSString stringWithUTF8String:res.iconPath.c_str()];
            NSNumber *score     = [NSNumber numberWithDouble:res.score];
            NSString *type      = [NSString stringWithUTF8String:res.type.c_str()];
            // Always take the identifier from ResultItem when present. Fallback to
            // synthesised "name-path" key only when no identifier is available.
            NSString *identifier = [NSString stringWithUTF8String:res.identifier.c_str()];
            if (!identifier || identifier.length == 0) {
                identifier = [NSString stringWithFormat:@"%@-%@", name ?: @"", path ?: @""];
            }
            [dicts addObject:@{ @"identifier": identifier ?: @"",
                                @"name": name ?: @"",
                                @"path": path ?: @"",
                                @"iconPath": iconPath ?: @"",
                                @"score": score,
                                @"type": type ?: @"app" }];
        }

        appDelegate.resultsDelegate.results = dicts;
        [appDelegate.resultsTable reloadData];
        if (dicts.count > 0) {
            [appDelegate.resultsTable selectRowIndexes:[NSIndexSet indexSetWithIndex:0] byExtendingSelection:NO];
        }
        [appDelegate showSearchInProgress:NO];
        return;
    }
    
    // legacy placeholder removed – prefix handling now unified below

    // Unified reactive search through SearchCoordinator.
    // Forward the raw query (without special prefix) to the parallel search pipeline.

    NSString *forwardQuery = searchText;

    // Strip "/c" prefix to allow generic ranking but keep chats only filter in delegate.
    if ([searchText hasPrefix:@"/c"]) {
        forwardQuery = [searchText substringFromIndex:2];
        if (forwardQuery.length == 0) { forwardQuery = @" "; }
    }

    // Show spinner immediately and delegate debouncing to coordinator.
    [appDelegate performParallelSearch:forwardQuery];
}

@end

@implementation CustomTableRowView

- (void)drawRect:(NSRect)dirtyRect {
    [super drawRect:dirtyRect];
    [self setEmphasized:NO];
}

@end 