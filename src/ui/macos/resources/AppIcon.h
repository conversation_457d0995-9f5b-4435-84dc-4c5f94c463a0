#pragma once

#import <Cocoa/Cocoa.h>

#ifdef __cplusplus
extern "C" {
#endif

// Function to create an app icon programmatically
NSImage* createAppIcon(CGFloat size);

// Icon data is now available in AppIcon.icns and AppIcon.png
// Use the .icns file for the app bundle and the .png for other purposes
// These files are generated using the scripts/generate_app_icon.sh script
extern const unsigned char AppIconData[];
extern const unsigned int AppIconDataLength;

#ifdef __cplusplus
}
#endif