#import <Cocoa/Cocoa.h>
#import "AppIcon.h"

// Define empty icon data (we're using programmatic icon instead)
const unsigned char AppIconData[] = {};
const unsigned int AppIconDataLength = 0;

@interface AppIconView : NSView
@end

@implementation AppIconView

- (void)drawRect:(NSRect)dirtyRect {
    [super drawRect:dirtyRect];
    
    NSRect bounds = self.bounds;
    CGFloat size = MIN(bounds.size.width, bounds.size.height);
    NSRect drawRect = NSMakeRect(
        bounds.origin.x + (bounds.size.width - size) / 2,
        bounds.origin.y + (bounds.size.height - size) / 2,
        size,
        size
    );
    
    // Draw a rocket icon
    NSBezierPath *path = [NSBezierPath bezierPath];
    
    // Rocket body
    CGFloat rocketWidth = size * 0.5;
    CGFloat rocketHeight = size * 0.8;
    NSRect rocketRect = NSMakeRect(
        drawRect.origin.x + (drawRect.size.width - rocketWidth) / 2,
        drawRect.origin.y + (drawRect.size.height - rocketHeight) / 2,
        rocketWidth,
        rocketHeight
    );
    
    [path moveToPoint:NSMakePoint(NSMidX(rocketRect), NSMaxY(rocketRect))];
    [path lineToPoint:NSMakePoint(NSMaxX(rocketRect), NSMidY(rocketRect) + rocketHeight * 0.2)];
    [path lineToPoint:NSMakePoint(NSMaxX(rocketRect), NSMinY(rocketRect) + rocketHeight * 0.1)];
    [path lineToPoint:NSMakePoint(NSMidX(rocketRect) + rocketWidth * 0.3, NSMinY(rocketRect))];
    [path lineToPoint:NSMakePoint(NSMidX(rocketRect) - rocketWidth * 0.3, NSMinY(rocketRect))];
    [path lineToPoint:NSMakePoint(NSMinX(rocketRect), NSMinY(rocketRect) + rocketHeight * 0.1)];
    [path lineToPoint:NSMakePoint(NSMinX(rocketRect), NSMidY(rocketRect) + rocketHeight * 0.2)];
    [path closePath];
    
    // Window
    CGFloat windowSize = rocketWidth * 0.4;
    NSRect windowRect = NSMakeRect(
        NSMidX(rocketRect) - windowSize / 2,
        NSMidY(rocketRect) + rocketHeight * 0.1,
        windowSize,
        windowSize
    );
    NSBezierPath *windowPath = [NSBezierPath bezierPathWithOvalInRect:windowRect];
    
    // Fins
    NSBezierPath *finPath = [NSBezierPath bezierPath];
    [finPath moveToPoint:NSMakePoint(NSMinX(rocketRect), NSMinY(rocketRect) + rocketHeight * 0.2)];
    [finPath lineToPoint:NSMakePoint(NSMinX(rocketRect) - rocketWidth * 0.2, NSMinY(rocketRect))];
    [finPath lineToPoint:NSMakePoint(NSMinX(rocketRect), NSMinY(rocketRect) + rocketHeight * 0.1)];
    [finPath closePath];
    
    NSBezierPath *finPath2 = [NSBezierPath bezierPath];
    [finPath2 moveToPoint:NSMakePoint(NSMaxX(rocketRect), NSMinY(rocketRect) + rocketHeight * 0.2)];
    [finPath2 lineToPoint:NSMakePoint(NSMaxX(rocketRect) + rocketWidth * 0.2, NSMinY(rocketRect))];
    [finPath2 lineToPoint:NSMakePoint(NSMaxX(rocketRect), NSMinY(rocketRect) + rocketHeight * 0.1)];
    [finPath2 closePath];
    
    // Flame
    NSBezierPath *flamePath = [NSBezierPath bezierPath];
    [flamePath moveToPoint:NSMakePoint(NSMidX(rocketRect) - rocketWidth * 0.2, NSMinY(rocketRect))];
    [flamePath lineToPoint:NSMakePoint(NSMidX(rocketRect), NSMinY(rocketRect) - rocketHeight * 0.3)];
    [flamePath lineToPoint:NSMakePoint(NSMidX(rocketRect) + rocketWidth * 0.2, NSMinY(rocketRect))];
    [flamePath closePath];
    
    // Draw with colors
    [[NSColor orangeColor] setFill];
    [flamePath fill];
    
    [[NSColor lightGrayColor] setFill];
    [path fill];
    
    [[NSColor darkGrayColor] setStroke];
    [path setLineWidth:1.0];
    [path stroke];
    
    [[NSColor blueColor] setFill];
    [windowPath fill];
    
    [[NSColor lightGrayColor] setFill];
    [finPath fill];
    [finPath2 fill];
}

@end

NSImage* createAppIcon(CGFloat size) {
    NSImage *image = [[NSImage alloc] initWithSize:NSMakeSize(size, size)];
    [image lockFocus];
    
    AppIconView *view = [[AppIconView alloc] initWithFrame:NSMakeRect(0, 0, size, size)];
    [view drawRect:NSMakeRect(0, 0, size, size)];
    
    [image unlockFocus];
    return image;
} 