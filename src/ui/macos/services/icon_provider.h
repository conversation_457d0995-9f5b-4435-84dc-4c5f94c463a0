#pragma once

#import <Foundation/Foundation.h>
#import <AppKit/AppKit.h>

NS_ASSUME_NONNULL_BEGIN

typedef void (^IconProviderCompletion)(NSImage * _Nullable icon);

/// <PERSON>ton responsible for async icon resolution with in-memory NSCache.
@interface IconProvider : NSObject

+ (instancetype)shared;

/// Request an icon. Completion always executed on main thread.
/// @param path  File/app path or URL string; may be nil.
/// @param type  Hint type: app / file / website / action / folder
/// @param imageURL Optional remote image url for website.
- (void)requestIconForPath:(NSString * _Nullable)path
                      type:(NSString *)type
                  imageURL:(NSString * _Nullable)imageURL
                 completion:(IconProviderCompletion)completion;

@end

NS_ASSUME_NONNULL_END 