#import "icon_provider.h"

#import <UniformTypeIdentifiers/UniformTypeIdentifiers.h>
#import <CommonCrypto/CommonCrypto.h>
#import <ImageIO/ImageIO.h>
#import <CoreServices/CoreServices.h>

static const NSUInteger kCacheCostLimitMB = 40;
static const NSUInteger kCacheCostLimit = kCacheCostLimitMB * 1024 * 1024;
static NSString *const kDiskCacheFolder = @"icons"; // inside Library/Caches/bundleID
static const NSTimeInterval kIconNetworkTimeout = 0.3; // 300 ms
static const NSTimeInterval kDiskPurgeAge = 30 * 24 * 60 * 60; // 30 days

@interface IconProvider ()
@property(nonatomic, strong) NSCache<NSString *, NSImage *> *cache;
@property(nonatomic, strong) NSOperationQueue *workQueue;
@end

@implementation IconProvider

+ (instancetype)shared {
    static IconProvider *s = nil; static dispatch_once_t once; dispatch_once(&once, ^{ s = [[self alloc] init]; });
    return s;
}

- (instancetype)init {
    if (self = [super init]) {
        // Clear on-disk cache for this fresh application session to ensure
        // icons are always regenerated with latest system style/colour.
        [self clearDiskCacheOnLaunch];

        _cache = [[NSCache alloc] init];
        _cache.totalCostLimit = kCacheCostLimit;
        _workQueue = [[NSOperationQueue alloc] init];
        _workQueue.maxConcurrentOperationCount = 4;
        _workQueue.qualityOfService = NSQualityOfServiceUtility;

        // Schedule disk cache purge once per launch.
        [self purgeDiskCacheIfNeeded];
    }
    return self;
}

- (void)requestIconForPath:(NSString *)path type:(NSString *)type imageURL:(NSString *)imageURL completion:(IconProviderCompletion)completion {
    if (!completion) return;
    NSString *cacheBase = path ?: (imageURL ?: @"placeholder");
    NSString *cacheKey = [NSString stringWithFormat:@"%@-%@", type, cacheBase];

    // Build disk filename from SHA1
    NSData *keyData = [cacheKey dataUsingEncoding:NSUTF8StringEncoding];
    NSString *sha1 = SHA1StringForData(keyData);
    NSString *diskPath = [[diskCacheDirectory() stringByAppendingPathComponent:sha1] stringByAppendingPathExtension:@"png"];

    // RAM cache lookup
    NSImage *cached = [self.cache objectForKey:cacheKey];
    if (cached) {
        dispatch_async(dispatch_get_main_queue(), ^{ completion(cached); });
        return;
    }

    // Disk cache lookup
    if ([[NSFileManager defaultManager] fileExistsAtPath:diskPath]) {
        NSImage *diskImage = [[NSImage alloc] initWithContentsOfFile:diskPath];
        if (diskImage) {
            [self.cache setObject:diskImage forKey:cacheKey cost:0];
            dispatch_async(dispatch_get_main_queue(), ^{ completion(diskImage); });
            return;
        }
    }

    // Return immediately with nil (caller may show placeholder)
    dispatch_async(dispatch_get_main_queue(), ^{ completion(nil); });

    __weak __typeof(self) weakSelf = self;
    [self.workQueue addOperationWithBlock:^{
        NSImage *icon = [weakSelf generateIconForPath:path type:type imageURL:imageURL];
        if (!icon) return;
        NSUInteger cost = icon.representations.firstObject.pixelsHigh * icon.representations.firstObject.pixelsWide * 4;
        [weakSelf.cache setObject:icon forKey:cacheKey cost:cost];

        // Persist to disk as PNG
        @autoreleasepool {
            CGImageRef cgImage = [icon CGImageForProposedRect:NULL context:NULL hints:NULL];
            if (cgImage) {
                CFURLRef url = (__bridge CFURLRef)[NSURL fileURLWithPath:diskPath];
                CGImageDestinationRef dest = CGImageDestinationCreateWithURL(url, kUTTypePNG, 1, NULL);
                if (dest) {
                    CGImageDestinationAddImage(dest, cgImage, nil);
                    CGImageDestinationFinalize(dest);
                    CFRelease(dest);
                }
            }
        }
        dispatch_async(dispatch_get_main_queue(), ^{ completion(icon); });
    }];
}

#pragma mark - Disk cache helpers

- (void)purgeDiskCacheIfNeeded {
    NSString *dir = diskCacheDirectory();
    dispatch_async(dispatch_get_global_queue(QOS_CLASS_UTILITY, 0), ^{
        NSFileManager *fm = [NSFileManager defaultManager];
        NSArray<NSString *> *files = [fm contentsOfDirectoryAtPath:dir error:nil];
        NSTimeInterval now = [[NSDate date] timeIntervalSince1970];
        NSUInteger totalBytes = 0;
        for (NSString *file in files) {
            NSString *full = [dir stringByAppendingPathComponent:file];
            NSDictionary *attrs = [fm attributesOfItemAtPath:full error:nil];
            if (!attrs) continue;
            totalBytes += [attrs[NSFileSize] unsignedIntegerValue];
            NSDate *mod = attrs[NSFileModificationDate];
            if (mod && now - mod.timeIntervalSince1970 > kDiskPurgeAge) {
                [fm removeItemAtPath:full error:nil];
            }
        }
        // If still >120 MB purge oldest
        const NSUInteger kMaxBytes = 120 * 1024 * 1024;
        if (totalBytes > kMaxBytes) {
            NSArray *sorted = [files sortedArrayUsingComparator:^NSComparisonResult(NSString *a, NSString *b) {
                NSString *fa = [dir stringByAppendingPathComponent:a];
                NSString *fb = [dir stringByAppendingPathComponent:b];
                NSDictionary *aa = [fm attributesOfItemAtPath:fa error:nil];
                NSDictionary *ab = [fm attributesOfItemAtPath:fb error:nil];
                return [aa[NSFileModificationDate] compare:ab[NSFileModificationDate]];
            }];
            for (NSString *file in sorted) {
                if (totalBytes <= kMaxBytes) break;
                NSString *full = [dir stringByAppendingPathComponent:file];
                NSDictionary *attr = [fm attributesOfItemAtPath:full error:nil];
                totalBytes -= [attr[NSFileSize] unsignedIntegerValue];
                [fm removeItemAtPath:full error:nil];
            }
        }
    });
}

#pragma mark - Cache lifecycle helpers

/// Remove all persisted icon cache files each time the app launches.  This
/// guarantees updated colourful icons after every relaunch without relying on
/// timestamp heuristics.
- (void)clearDiskCacheOnLaunch {
    NSString *dir = diskCacheDirectory();
    NSFileManager *fm = [NSFileManager defaultManager];
    [fm removeItemAtPath:dir error:nil];
    [fm createDirectoryAtPath:dir withIntermediateDirectories:YES attributes:nil error:nil];
}

#pragma mark - helpers

- (NSImage *)generateIconForPath:(NSString *)path type:(NSString *)type imageURL:(NSString *)imageURL {
    @autoreleasepool {
        NSImage *icon = nil;
        if ([type isEqualToString:@"website"]) {
            // Attempt to fetch default browser icon once per launch
            static NSImage *defaultBrowserIcon = nil;
            static dispatch_once_t onceToken;
            dispatch_once(&onceToken, ^{
                NSString *bundleID = (__bridge_transfer NSString *)LSCopyDefaultHandlerForURLScheme(CFSTR("http"));
                if (bundleID) {
                    NSURL *appURL = [[NSWorkspace sharedWorkspace] URLForApplicationWithBundleIdentifier:bundleID];
                    if (appURL) {
                        defaultBrowserIcon = [[NSWorkspace sharedWorkspace] iconForFile:appURL.path];
                    }
                }
                if (!defaultBrowserIcon) {
                    defaultBrowserIcon = [NSImage imageNamed:NSImageNameNetwork];
                }
            });
            icon = defaultBrowserIcon;
        } else if ([type isEqualToString:@"app"] && path) {
            icon = [[NSWorkspace sharedWorkspace] iconForFile:path];
        } else if ([type isEqualToString:@"file"] && path) {
            NSString *ext = path.pathExtension;
            if (ext.length > 0) {
                if (@available(macOS 11.0, *)) {
                    UTType *ct = [UTType typeWithFilenameExtension:ext];
                    icon = ct ? [[NSWorkspace sharedWorkspace] iconForContentType:ct] : nil;
                }
                if (!icon) icon = [[NSWorkspace sharedWorkspace] iconForFileType:ext];
            } else {
                icon = [[NSWorkspace sharedWorkspace] iconForFile:path];
            }
        } else if ([type isEqualToString:@"chat"]) {
            icon = ChatIcon();
        } else if ([type isEqualToString:@"chat_query"]) {
            icon = ChatQueryIcon();
        }
        if (!icon) {
            if (@available(macOS 11.0, *)) icon = [NSImage imageWithSystemSymbolName:@"doc" accessibilityDescription:nil];
            if (!icon) icon = [NSImage imageNamed:NSImageNameCaution];
        }
        return icon;
    }
}

// SHA1 helper
static NSString *SHA1StringForData(NSData *data) {
    unsigned char digest[CC_SHA1_DIGEST_LENGTH];
    CC_SHA1(data.bytes, (CC_LONG)data.length, digest);
    NSMutableString *out = [NSMutableString stringWithCapacity:CC_SHA1_DIGEST_LENGTH*2];
    for (int i = 0; i < CC_SHA1_DIGEST_LENGTH; i++) {
        [out appendFormat:@"%02x", digest[i]];
    }
    return out;
}

static NSString *diskCacheDirectory() {
    static NSString *dir = nil; static dispatch_once_t once; dispatch_once(&once, ^{
        NSArray<NSString *> *paths = NSSearchPathForDirectoriesInDomains(NSCachesDirectory, NSUserDomainMask, YES);
        NSString *cacheRoot = paths.firstObject ?: NSTemporaryDirectory();
        NSString *bundleID = [[NSBundle mainBundle] bundleIdentifier] ?: @"launcher";
        dir = [cacheRoot stringByAppendingPathComponent:bundleID];
        dir = [dir stringByAppendingPathComponent:kDiskCacheFolder];
        [[NSFileManager defaultManager] createDirectoryAtPath:dir withIntermediateDirectories:YES attributes:nil error:nil];
    });
    return dir;
}

// -----------------------------------------------------------------------------
// Helpers – resolve colourful macOS icons for virtual result types
// -----------------------------------------------------------------------------

static NSImage *ChatIcon() {
    // Base template symbol is cached once – we tint a fresh copy each call so
    // every chat row may receive a unique colour.  The symbol chosen contains
    // quotation marks inside a chat bubble (macOS 11+).
    static NSImage *base_symbol = nil;
    static dispatch_once_t once_symbol;
    dispatch_once(&once_symbol, ^{
        if (@available(macOS 11.0, *)) {
            base_symbol = [NSImage imageWithSystemSymbolName:@"bubble.left.and.bubble.right.fill"
                                    accessibilityDescription:nil];
        } else {
            // Older systems fall back to the generic network symbol.
            base_symbol = [NSImage imageNamed:NSImageNameNetwork];
        }
        [base_symbol setTemplate:YES];
    });

    // Curated accent palette – matches macOS system colours.
    static NSArray<NSColor *> *palette = nil;
    static dispatch_once_t once_palette;
    dispatch_once(&once_palette, ^{
        palette = @[ NSColor.systemBlueColor,
                     NSColor.systemRedColor,
                     NSColor.systemGreenColor,
                     NSColor.systemOrangeColor,
                     NSColor.systemPinkColor,
                     NSColor.systemPurpleColor,
                     NSColor.systemYellowColor,
                     NSColor.systemTealColor ];
    });

    uint32_t idx = arc4random_uniform((uint32_t)palette.count);
    NSColor *tint = palette[idx];

    if (@available(macOS 11.0, *)) {
        NSImageSymbolConfiguration *conf = [NSImageSymbolConfiguration configurationWithHierarchicalColor:tint];
        return [base_symbol imageWithSymbolConfiguration:conf];
    } else {
        // Manual tinting fallback for macOS <11
        NSImage *img = [base_symbol copy];
        [img lockFocus];
        [tint set];
        NSRect imageRect = NSMakeRect(0, 0, img.size.width, img.size.height);
        NSRectFillUsingOperation(imageRect, NSCompositingOperationSourceIn);
        [img unlockFocus];
        [img setTemplate:NO];
        return img;
    }
}

static NSImage *ChatQueryIcon() {
    // Random-tinted quotation-bubble symbol for chat query rows.
    static NSImage *base_symbol = nil;
    static dispatch_once_t once_symbol;
    dispatch_once(&once_symbol, ^{
        if (@available(macOS 11.0, *)) {
            base_symbol = [NSImage imageWithSystemSymbolName:@"bubble.left.and.bubble.right.fill"
                                    accessibilityDescription:nil];
        } else {
            base_symbol = [NSImage imageNamed:NSImageNameNetwork];
        }
        [base_symbol setTemplate:YES];
    });

    // Shared accent palette (duplicated for simplicity – could be refactored).
    static NSArray<NSColor *> *palette = nil;
    static dispatch_once_t once_palette;
    dispatch_once(&once_palette, ^{
        palette = @[ NSColor.systemBlueColor,
                     NSColor.systemRedColor,
                     NSColor.systemGreenColor,
                     NSColor.systemOrangeColor,
                     NSColor.systemPinkColor,
                     NSColor.systemPurpleColor,
                     NSColor.systemYellowColor,
                     NSColor.systemTealColor ];
    });

    uint32_t idx = arc4random_uniform((uint32_t)palette.count);
    NSColor *tint = palette[idx];

    if (@available(macOS 11.0, *)) {
        NSImageSymbolConfiguration *conf = [NSImageSymbolConfiguration configurationWithHierarchicalColor:tint];
        return [base_symbol imageWithSymbolConfiguration:conf];
    } else {
        NSImage *img = [base_symbol copy];
        [img lockFocus];
        [tint set];
        NSRect rect = NSMakeRect(0, 0, img.size.width, img.size.height);
        NSRectFillUsingOperation(rect, NSCompositingOperationSourceIn);
        [img unlockFocus];
        [img setTemplate:NO];
        return img;
    }
}

@end 