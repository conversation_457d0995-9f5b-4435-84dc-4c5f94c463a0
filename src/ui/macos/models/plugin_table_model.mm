#import "plugin_table_model.h"
#include "../../../core/plugins/plugin_registry.h"
#include "../../models/runtime_plugin_cell_model.h"

#ifdef __OBJC__

@interface PluginTableModel ()
@property(nonatomic, assign) const launcher::core::plugins::PluginRegistry* reg;
@property(nonatomic, strong) NSArray<RuntimePluginCellModel*> *rows;
@property(nonatomic, copy) NSString *search;
@property(nonatomic, assign) NSInteger enabledState; // -1 all 0 disabled 1 enabled
@end

@implementation PluginTableModel

- (instancetype)initWithRegistry:(const launcher::core::plugins::PluginRegistry*)registry {
    self = [super init];
    if (self) {
        _reg = registry;
        _search = @"";
        _enabledState = -1;
        [self refresh];
    }
    return self;
}

- (void)refresh {
    using launcher::core::plugins::PluginMeta;
    if (!_reg) { self.rows = @[]; return; }
    auto span = _reg->list();
    NSMutableArray *arr = [NSMutableArray arrayWithCapacity:span.size()];
    for (const PluginMeta& m : span) {
        // enabled filter
        if (_enabledState == 0 && m.enabled) continue;
        if (_enabledState == 1 && !m.enabled) continue;

        auto modelArr = [RuntimePluginCellModel modelsFromDescriptors:std::vector{m.descriptor}];
        if (modelArr.count == 0) continue;
        RuntimePluginCellModel *cellModel = modelArr.firstObject;

        if (_search.length > 0) {
            if ([[cellModel.runtimeId lowercaseString] rangeOfString:[_search lowercaseString]].location == NSNotFound) {
                continue;
            }
        }
        [arr addObject:cellModel];
    }
    self.rows = arr;
}

- (NSInteger)rowCount { return self.rows.count; }
- (RuntimePluginCellModel*)modelAt:(NSInteger)row { return (row>=0 && row<self.rows.count) ? self.rows[row] : nil; }

- (void)applyFilter:(NSString *)text enabledState:(NSInteger)enabledState {
    self.search = text ?: @"";
    self.enabledState = enabledState;
    [self refresh];
}

@end

#endif 