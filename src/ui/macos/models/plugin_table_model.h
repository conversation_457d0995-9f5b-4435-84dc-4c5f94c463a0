#pragma once

#ifdef __OBJC__
#import <Foundation/Foundation.h>
@class RuntimePluginCellModel;

namespace launcher::core::plugins { class PluginRegistry; }

/// Objective-C wrapper converting PluginRegistry → NSArray for NSTableView.
@interface PluginTableModel : NSObject

- (instancetype)initWithRegistry:(const launcher::core::plugins::PluginRegistry*)registry;

/// Rebuild internal rows from registry (optionally retain pointer)
- (void)refresh;

/// Apply search text (case-insensitive substring match) and enabled filter.
/// enabledState: -1 = all, 0 = disabled-only, 1 = enabled-only.
- (void)applyFilter:(NSString*)text enabledState:(NSInteger)enabledState;

- (NSInteger)rowCount;
- (RuntimePluginCellModel*)modelAt:(NSInteger)row;

@end
#endif 