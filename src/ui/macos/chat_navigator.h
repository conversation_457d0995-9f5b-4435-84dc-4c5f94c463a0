#pragma once

//------------------------------------------------------------------------------
// chat_navigator.h
//
// Provides reusable helpers to open a new chat window with an initial prompt
// or to reopen an existing conversation by identifier.  Centralising this
// logic removes ~250 lines from AppDelegate and keeps chat-specific code in
// one place.
//------------------------------------------------------------------------------

#import <Foundation/Foundation.h>

@interface ChatNavigator : NSObject

+ (instancetype)shared;

// Spawns a new chat window/tab and streams the provided question immediately.
- (void)openChatAndSendQuestion:(NSString *)question;

// Reopens an existing conversation JSON by id. If the conversation is already
// visible it is activated instead.
- (void)openConversationWithId:(NSString *)convId;

@end 