// tab_switcher_controller.h
// Modern Control-Tab switcher overlay for cycling between chat tabs.
//
// Provides a lightweight Cocoa controller that mimics macOS ⌘-Tab behaviour, but operates
// on windows tracked by TabGroupManager. Hold Control and tap Tab / Shift-Tab to cycle
// through tabs, and release Control to activate the selected window. A translucent
// vertical overlay presents each tab's icon and title.
//
// Author: o3 assistant
// -----------------------------------------------------------------------------

#pragma once

#ifdef __APPLE__
#import <AppKit/AppKit.h>

NS_ASSUME_NONNULL_BEGIN

// Forward-declare TabGroupManager to avoid heavy include.
@class TabGroupManager;

@interface TabSwitcherController : NSObject

// Shared singleton instance.
+ (instancetype)sharedController;

// Installs the local NSEvent monitors. Call once during app startup.
- (void)installEventMonitors;

// Removes monitors and cleans up overlay (for tests / deinit).
- (void)tearDown;

@end

NS_ASSUME_NONNULL_END

#endif // __APPLE__ 