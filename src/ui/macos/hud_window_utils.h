#pragma once

#ifdef __APPLE__
#import <AppKit/AppKit.h>

// -----------------------------------------------------------------------------
// hud_window_utils.h
// Utility for creating a consistent blurred HUD-style background view that
// matches the launcher bar aesthetic. This centralises appearance parameters
// (material, blending mode, corner radius) so multiple UI components can use a
// single source of truth.
// -----------------------------------------------------------------------------

// Creates and returns a configured NSVisualEffectView with HUD material.
// The caller owns the returned view and should insert it into the desired
// window hierarchy (e.g., set as contentView or add as subview).
//
// Parameters:
//   frame         The initial frame for the effect view (typically
//                 window.contentView.bounds).
//   corner_radius The corner radius applied to the view's backing layer.
NSVisualEffectView *CreateHUDContentView(NSRect frame, CGFloat corner_radius);

#endif // __APPLE__ 