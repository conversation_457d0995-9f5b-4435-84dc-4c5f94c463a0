// hud_window_utils.mm
// -----------------------------------------------------------------------------
// Implementation of helper for generating a blurred HUD-style background view
// shared by the launcher bar and tab switcher overlay.
// -----------------------------------------------------------------------------

#ifdef __APPLE__

#import "hud_window_utils.h"

NSVisualEffectView *CreateHUDContentView(NSRect frame, CGFloat corner_radius) {
    NSVisualEffectView *vev = [[NSVisualEffectView alloc] initWithFrame:frame];
    vev.autoresizingMask = NSViewWidthSizable | NSViewHeightSizable;
    vev.material = NSVisualEffectMaterialToolTip;
    vev.blendingMode = NSVisualEffectBlendingModeWithinWindow;
    vev.appearance = [NSAppearance appearanceNamed:NSAppearanceNameVibrantDark];
    vev.state = NSVisualEffectStateActive;
    vev.wantsLayer = YES;
    vev.layer.cornerRadius = corner_radius;
    return vev;
}

#endif // __APPLE__ 