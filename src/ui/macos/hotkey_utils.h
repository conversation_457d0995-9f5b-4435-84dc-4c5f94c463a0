// hotkey_utils.h
// Utility helpers for converting between Cocoa and Carbon hot-key representations
// and building human-readable shortcut strings.
//
// Author: o3 assistant
// -----------------------------------------------------------------------------

#pragma once

#import <AppKit/AppKit.h>
#import <Carbon/Carbon.h>

#ifdef __OBJC__

/// Convert Cocoa NSEventModifierFlags to Carbon modifier bit-field.
static inline UInt32 CarbonModifiersFromCocoa(NSEventModifierFlags cocoa_flags) {
    UInt32 carbon = 0;
    if (cocoa_flags & NSEventModifierFlagCommand) carbon |= cmdKey;
    if (cocoa_flags & NSEventModifierFlagOption)  carbon |= optionKey;
    if (cocoa_flags & NSEventModifierFlagControl) carbon |= controlKey;
    if (cocoa_flags & NSEventModifierFlagShift)   carbon |= shiftKey;
    return carbon;
}

/// Convert Carbon modifier flags to Cocoa NSEventModifierFlags (device-independent subset).
static inline NSEventModifierFlags CocoaModifiersFromCarbon(UInt32 carbon_flags) {
    NSEventModifierFlags cocoa = 0;
    if (carbon_flags & cmdKey)     cocoa |= NSEventModifierFlagCommand;
    if (carbon_flags & optionKey)  cocoa |= NSEventModifierFlagOption;
    if (carbon_flags & controlKey) cocoa |= NSEventModifierFlagControl;
    if (carbon_flags & shiftKey)   cocoa |= NSEventModifierFlagShift;
    return cocoa;
}

/// Quick glyph formatter for common keys.
static inline NSString *HumanStringForShortcut(UInt16 key_code, NSEventModifierFlags cocoa_flags) {
    NSMutableString *s = [NSMutableString string];
    if (cocoa_flags & NSEventModifierFlagCommand)  [s appendString:@"⌘ "];
    if (cocoa_flags & NSEventModifierFlagOption)   [s appendString:@"⌥ "];
    if (cocoa_flags & NSEventModifierFlagControl)  [s appendString:@"⌃ "];
    if (cocoa_flags & NSEventModifierFlagShift)    [s appendString:@"⇧ "];

    switch (key_code) {
        case kVK_Space:     [s appendString:@"Space"]; break;
        case kVK_Return:    [s appendString:@"Return"]; break;
        case kVK_Escape:    [s appendString:@"Esc"]; break;
        case kVK_LeftArrow: [s appendString:@"←"]; break;
        case kVK_RightArrow:[s appendString:@"→"]; break;
        case kVK_UpArrow:   [s appendString:@"↑"]; break;
        case kVK_DownArrow: [s appendString:@"↓"]; break;
        default: {
            // Try to obtain printable character for current layout.
            TISInputSourceRef source = TISCopyCurrentKeyboardLayoutInputSource();
            if (!source) { [s appendString:@"?"]; break; }
            CFDataRef layoutData = (CFDataRef)TISGetInputSourceProperty(source, kTISPropertyUnicodeKeyLayoutData);
            if (layoutData) {
                const UCKeyboardLayout *keyboardLayout = (const UCKeyboardLayout *)CFDataGetBytePtr(layoutData);
                UInt32 deadKeyState = 0;
                UniChar chars[4];
                UniCharCount realLength;
                OSStatus err = UCKeyTranslate(keyboardLayout, key_code, kUCKeyActionDisplay, 0,
                                               LMGetKbdType(), 0, &deadKeyState,
                                               sizeof(chars) / sizeof(chars[0]), &realLength, chars);
                if (err == noErr && realLength > 0) {
                    NSString *str = [[NSString alloc] initWithCharacters:chars length:(NSUInteger)realLength];
                    [s appendString:str.uppercaseString];
                } else {
                    [s appendFormat:@"%hu", key_code];
                }
            } else {
                [s appendFormat:@"%hu", key_code];
            }
            if (source) CFRelease(source);
            break;
        }
    }
    return s;
}

#endif // __OBJC__ 