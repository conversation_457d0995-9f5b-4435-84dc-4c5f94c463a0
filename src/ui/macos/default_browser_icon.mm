#import "default_browser_icon.h"
#import <UniformTypeIdentifiers/UniformTypeIdentifiers.h>
#import <Foundation/Foundation.h>

NSImage *GetDefaultBrowserIcon(void) {
    static NSImage *icon = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        @autoreleasepool {
            // 1. Ask LaunchServices which application would open an HTTPS URL.
            NSURL *testURL = [NSURL URLWithString:@"https://example.com"];
            NSURL *browserAppURL = nil;
            if (testURL) {
                browserAppURL = [[NSWorkspace sharedWorkspace] URLForApplicationToOpenURL:testURL];
            }

            if (browserAppURL) {
                icon = [[NSWorkspace sharedWorkspace] iconForFile:browserAppURL.path];
            }

            // 2. Fallback to Safari.app's icon if LaunchServices did not return one.
            if (!icon) {
                NSString *safariPath = @"/Applications/Safari.app";
                if ([[NSFileManager defaultManager] fileExistsAtPath:safariPath]) {
                    icon = [[NSWorkspace sharedWorkspace] iconForFile:safariPath];
                }
            }

            // 3. Fallback to SF Symbol "globe" (macOS 11+).
            if (!icon) {
                if (@available(macOS 11.0, *)) {
                    icon = [NSImage imageWithSystemSymbolName:@"globe" accessibilityDescription:@"Website"];
                }
            }

            // 4. Final fallback to generic Internet Location icon.
            if (!icon) {
                if (@available(macOS 12.0, *)) {
                    icon = [[NSWorkspace sharedWorkspace] iconForContentType:UTTypeInternetLocation];
                    if (!icon) {
                        icon = [[NSWorkspace sharedWorkspace] iconForContentType:UTTypeItem];
                    }
                }
            }
        }
    });

    return icon;
} 