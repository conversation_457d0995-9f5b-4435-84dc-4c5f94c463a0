#pragma once

#import <Cocoa/Cocoa.h>
#include <cassert>
#include "../../core/app_context.h"
#import "macos_ui_internal.h"  // provides AppDelegate interface with ctx property

// Forward declare ObjC AppDelegate to avoid heavy includes.
@class AppDelegate;

namespace launcher {
namespace ui {

// Helper: access global AppContext set up at application startup.
static inline launcher::core::AppContext* AppCtx() {
    AppDelegate *appDel = (AppDelegate *)[[NSApplication sharedApplication] delegate];
    assert(appDel && "NSApp delegate must be AppDelegate");
    assert(appDel.ctx && "AppContext not initialised");
    return appDel ? appDel.ctx : nullptr;
}

// Config manager accessor (crashes early if missing).
static inline launcher::core::IConfigManager& ConfigService() {
    auto *ctx = AppCtx();
    assert(ctx && ctx->configManager && "Missing configManager in AppContext");
    return *ctx->configManager;
}

// Context manager accessor.
static inline launcher::core::ContextManager& ContextService() {
    auto *ctx = AppCtx();
    assert(ctx && ctx->contextManager && "Missing contextManager in AppContext");
    return *ctx->contextManager;
}

// History manager accessor (optional).
static inline launcher::core::IHistoryManager* HistoryService() {
    auto *ctx = AppCtx();
    return ctx ? ctx->historyManager.get() : nullptr;
}

// Runtime manager accessor (optional).
static inline launcher::core::plugins::RuntimeManagerSvc* RuntimeManagerService() {
    auto* ctx = AppCtx();
    return ctx ? ctx->runtimeManager.get() : nullptr;
}

// Event bus accessor (optional).
static inline launcher::core::events::EventBusService* EventBusService() {
    auto* ctx = AppCtx();
    return ctx ? ctx->eventBus.get() : nullptr; // eventBus member to be added in AppContext
}

} // namespace ui
} // namespace launcher 