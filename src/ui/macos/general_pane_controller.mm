#import "general_pane_controller.h"
#import "hotkey_utils.h"
#include "../../core/interfaces/iconfig_manager.h"
#include "browser_history_importer.h"
#include "../../core/app_context.h"
#include "../../core/history/history_manager_interface.h"
#import <AppKit/AppKit.h>
#import "../../third_party/mas_shortcut/include/MASShortcut.h"
#import "../../third_party/mas_shortcut/include/MASShortcutView.h"
#import "macos_ui_internal.h"
#include <cassert> // for assert
#include "injected_services.h"  // shared helper
using launcher::ui::ConfigService;

#ifdef __OBJC__

@interface GeneralPaneController ()
// UI controls
@property(nonatomic, strong) MASShortcutView* hotkeyView;
@property(nonatomic, strong) MASShortcutView* chatHotkeyView;
@property(nonatomic, strong) NSButton* launchAtLoginCheckbox;
@property(nonatomic, strong) NSSlider* maxResultsSlider;
@property(nonatomic, strong) NSTextField* maxResultsValueLabel;
@property(nonatomic, strong) NSButton* requestBrowserHistoryPermissionsButton;
@property(nonatomic, strong) NSButton* enableHistoryCheckbox;
@property(nonatomic, strong) NSButton* clearHistoryButton;
@end

@implementation GeneralPaneController

- (void)loadView {
    // Root background with vibrancy
    NSVisualEffectView* rootView = [[NSVisualEffectView alloc] initWithFrame:NSMakeRect(0, 0, 800, 500)];
    rootView.translatesAutoresizingMaskIntoConstraints = NO;
    if (@available(macOS 10.14, *)) {
        rootView.material = NSVisualEffectMaterialSidebar;
    } else {
        rootView.material = NSVisualEffectMaterialLight;
    }
    rootView.state = NSVisualEffectStateActive;

    // Content view that is horizontally centred and hosts the actual stack
    NSView* contentView = [[NSView alloc] initWithFrame:NSZeroRect];
    contentView.translatesAutoresizingMaskIntoConstraints = NO;
    [rootView addSubview:contentView];

    // Constrain contentView centered horizontally with a fixed width and pinned to the top
    [NSLayoutConstraint activateConstraints:@[
        [contentView.centerXAnchor constraintEqualToAnchor:rootView.centerXAnchor],
        [contentView.topAnchor constraintEqualToAnchor:rootView.topAnchor],
        [contentView.widthAnchor constraintEqualToConstant:640.0],
        [contentView.bottomAnchor constraintLessThanOrEqualToAnchor:rootView.bottomAnchor]
    ]];

    // Main vertical stack inside the centred content view
    NSStackView* rootStack = [NSStackView stackViewWithViews:@[]];
    rootStack.orientation = NSUserInterfaceLayoutOrientationVertical;
    rootStack.alignment = NSLayoutAttributeCenterX;
    rootStack.spacing = 24.0;
    rootStack.edgeInsets = NSEdgeInsetsMake(32, 0, 32, 0);
    rootStack.translatesAutoresizingMaskIntoConstraints = NO;
    [contentView addSubview:rootStack];

    // Activation section (header removed)
    {
        NSStackView* row = [NSStackView stackViewWithViews:@[]];
        row.orientation = NSUserInterfaceLayoutOrientationHorizontal;
        row.alignment = NSLayoutAttributeCenterY;
        row.spacing = 12;

        NSTextField* label = [self labelWithString:@"Activation Hotkey:"];
        [row addArrangedSubview:label];

        self.hotkeyView = [[MASShortcutView alloc] initWithFrame:NSMakeRect(0, 0, 160, 25)];
        self.hotkeyView.style = MASShortcutViewStyleTexturedRect;
        // self.hotkeyView.style = MASShortcutViewStyleRounded;
        self.hotkeyView.translatesAutoresizingMaskIntoConstraints = NO;
        [self.hotkeyView.widthAnchor constraintEqualToConstant:160].active = YES;
        [self.hotkeyView.heightAnchor constraintEqualToConstant:25].active = YES;
        
        // Set callback for when shortcut changes
        GeneralPaneController* weakSelf = self;
        self.hotkeyView.shortcutValueChange = ^(MASShortcutView * _Nonnull sender) {
            [weakSelf shortcutDidChange:sender];
        };
        
        [row addArrangedSubview:self.hotkeyView];

        // Chat hotkey row (⌘E by default)
        NSStackView* chatRow = [NSStackView stackViewWithViews:@[]];
        chatRow.orientation = NSUserInterfaceLayoutOrientationHorizontal;
        chatRow.alignment = NSLayoutAttributeCenterY;
        chatRow.spacing = 12;

        NSTextField* chatLabel = [self labelWithString:@"Open Chat Hotkey:"];
        [chatRow addArrangedSubview:chatLabel];

        self.chatHotkeyView = [[MASShortcutView alloc] initWithFrame:NSMakeRect(0, 0, 160, 25)];
        self.chatHotkeyView.style = MASShortcutViewStyleTexturedRect;
        self.chatHotkeyView.translatesAutoresizingMaskIntoConstraints = NO;
        [self.chatHotkeyView.widthAnchor constraintEqualToConstant:160].active = YES;
        [self.chatHotkeyView.heightAnchor constraintEqualToConstant:25].active = YES;

        self.chatHotkeyView.shortcutValueChange = ^(MASShortcutView * _Nonnull sender) {
            [weakSelf chatShortcutDidChange:sender];
        };

        [chatRow addArrangedSubview:self.chatHotkeyView];

        [rootStack addArrangedSubview:row];
        [rootStack addArrangedSubview:chatRow];
    }

    // Behaviour section (header removed)
    {
        // Launch at login row
        self.launchAtLoginCheckbox = [[NSButton alloc] init];
        self.launchAtLoginCheckbox.buttonType = NSButtonTypeSwitch;
        self.launchAtLoginCheckbox.title = @"Launch at login";
        [self.launchAtLoginCheckbox setTarget:self];
        [self.launchAtLoginCheckbox setAction:@selector(launchAtLoginCheckboxClicked:)];
        [rootStack addArrangedSubview:self.launchAtLoginCheckbox];

        // Max results row
        NSStackView* row = [NSStackView stackViewWithViews:@[]];
        row.orientation = NSUserInterfaceLayoutOrientationHorizontal;
        row.alignment = NSLayoutAttributeCenterY;
        row.spacing = 12;

        NSTextField* label = [self labelWithString:@"Maximum Results:"];
        [row addArrangedSubview:label];

        self.maxResultsSlider = [[NSSlider alloc] init];
        self.maxResultsSlider.minValue = 10;
        self.maxResultsSlider.maxValue = 50;
        self.maxResultsSlider.numberOfTickMarks = 5;
        self.maxResultsSlider.allowsTickMarkValuesOnly = YES;
        self.maxResultsSlider.translatesAutoresizingMaskIntoConstraints = NO;
        [[self.maxResultsSlider.widthAnchor constraintEqualToConstant:200] setActive:YES];
        [self.maxResultsSlider setTarget:self];
        [self.maxResultsSlider setAction:@selector(maxResultsSliderChanged:)];
        [row addArrangedSubview:self.maxResultsSlider];

        self.maxResultsValueLabel = [self labelWithString:@"0"];
        [row addArrangedSubview:self.maxResultsValueLabel];

        [rootStack addArrangedSubview:row];
    }

    // Privacy section (header removed)
    {
        // Enable History toggle
        self.enableHistoryCheckbox = [[NSButton alloc] init];
        self.enableHistoryCheckbox.buttonType = NSButtonTypeSwitch;
        self.enableHistoryCheckbox.title = @"Enable Recent-item History";
        [self.enableHistoryCheckbox setTarget:self];
        [self.enableHistoryCheckbox setAction:@selector(enableHistoryCheckboxClicked:)];
        [rootStack addArrangedSubview:self.enableHistoryCheckbox];

        // Clear history button
        self.clearHistoryButton = [NSButton buttonWithTitle:@"Clear History…"
                                                    target:self
                                                    action:@selector(clearHistoryClicked:)];
        self.clearHistoryButton.bezelStyle = NSBezelStyleRounded;
        [rootStack addArrangedSubview:self.clearHistoryButton];

        // Existing permissions button
        self.requestBrowserHistoryPermissionsButton = [NSButton buttonWithTitle:@"Enable Browser History Search"
                                                                         target:self
                                                                         action:@selector(requestBrowserHistoryPermissionsClicked:)];
        self.requestBrowserHistoryPermissionsButton.bezelStyle = NSBezelStyleRounded;
        [rootStack addArrangedSubview:self.requestBrowserHistoryPermissionsButton];
    }

    self.view = rootView;

    // Constraint rootStack to edges of the centred contentView
    [NSLayoutConstraint activateConstraints:@[
        [rootStack.topAnchor constraintEqualToAnchor:contentView.topAnchor],
        [rootStack.leadingAnchor constraintEqualToAnchor:contentView.leadingAnchor],
        [rootStack.trailingAnchor constraintEqualToAnchor:contentView.trailingAnchor],
        [rootStack.bottomAnchor constraintLessThanOrEqualToAnchor:contentView.bottomAnchor]
    ]];

    // Load persisted values
    [self loadSettings];
}

#pragma mark - Helpers

- (NSTextField*)sectionHeaderWithTitle:(NSString*)title {
    NSTextField* tf = [NSTextField labelWithString:title];
    tf.font = [NSFont systemFontOfSize:14 weight:NSFontWeightSemibold];
    return tf;
}

- (NSTextField*)labelWithString:(NSString*)str {
    NSTextField* tf = [NSTextField labelWithString:str];
    tf.font = [NSFont systemFontOfSize:NSFont.systemFontSize weight:NSFontWeightRegular];
    return tf;
}

#pragma mark - Shortcut Change Handling

- (void)shortcutDidChange:(MASShortcutView *)sender {
    auto &cm = ConfigService();
    MASShortcut *shortcut = sender.shortcutValue;
    
    if (shortcut) {
        UInt16 keyCode = shortcut.keyCode;
        NSEventModifierFlags cocoaFlags = shortcut.modifierFlags;
        UInt32 carbonMods = CarbonModifiersFromCocoa(cocoaFlags);
        
        cm.setInt("general.launcher_hotkey.keycode", keyCode);
        cm.setInt("general.launcher_hotkey.mods", (int)carbonMods);
        cm.requestSave();
        
        // Broadcast change to the rest of the app so it can re-register.
        [[NSNotificationCenter defaultCenter] postNotificationName:@"LauncherHotkeyDidChange"
                                                            object:nil
                                                          userInfo:@{ @"keyCode" : @(keyCode),
                                                                      @"mods"   : @(carbonMods) }];
    } else {
        // Handle shortcut being cleared - use default Command+Space
        UInt16 keyCode = kVK_Space;
        UInt32 carbonMods = cmdKey;
        
        cm.setInt("general.launcher_hotkey.keycode", keyCode);
        cm.setInt("general.launcher_hotkey.mods", (int)carbonMods);
        cm.requestSave();
        
        // Reset the shortcut view to the default
        NSEventModifierFlags cocoaMods = CocoaModifiersFromCarbon(carbonMods);
        MASShortcut *defaultShortcut = [MASShortcut shortcutWithKeyCode:keyCode modifierFlags:cocoaMods];
        [self.hotkeyView setShortcutValue:defaultShortcut];
        
        // Broadcast change
        [[NSNotificationCenter defaultCenter] postNotificationName:@"LauncherHotkeyDidChange"
                                                            object:nil
                                                          userInfo:@{ @"keyCode" : @(keyCode),
                                                                      @"mods"   : @(carbonMods) }];
    }
}

- (void)chatShortcutDidChange:(MASShortcutView *)sender {
    auto &cm = ConfigService();
    MASShortcut *shortcut = sender.shortcutValue;

    if (shortcut) {
        UInt16 keyCode = shortcut.keyCode;
        UInt32 carbonMods = CarbonModifiersFromCocoa(shortcut.modifierFlags);

        cm.setInt("general.chat_hotkey.keycode", keyCode);
        cm.setInt("general.chat_hotkey.mods", (int)carbonMods);
        cm.requestSave();

        [[NSNotificationCenter defaultCenter] postNotificationName:@"ChatHotkeyDidChange"
                                                            object:nil
                                                          userInfo:@{ @"keyCode" : @(keyCode),
                                                                      @"mods"   : @(carbonMods) }];
    } else {
        UInt16 keyCode = kVK_ANSI_E;
        UInt32 carbonMods = cmdKey;

        cm.setInt("general.chat_hotkey.keycode", keyCode);
        cm.setInt("general.chat_hotkey.mods", (int)carbonMods);
        cm.requestSave();

        NSEventModifierFlags cocoaMods = CocoaModifiersFromCarbon(carbonMods);
        MASShortcut *defaultShortcut = [MASShortcut shortcutWithKeyCode:keyCode modifierFlags:cocoaMods];
        [self.chatHotkeyView setShortcutValue:defaultShortcut];

        [[NSNotificationCenter defaultCenter] postNotificationName:@"ChatHotkeyDidChange"
                                                            object:nil
                                                          userInfo:@{ @"keyCode" : @(keyCode),
                                                                      @"mods"   : @(carbonMods) }];
    }
}

#pragma mark - UI Events

- (void)launchAtLoginCheckboxClicked:(__unused id)sender {
    bool state = (self.launchAtLoginCheckbox.state == NSControlStateValueOn);
    auto &cm = ConfigService();
    cm.setBool("general.startup_on_login", state);
    cm.requestSave();
}

- (void)maxResultsSliderChanged:(__unused id)sender {
    int value = (int)self.maxResultsSlider.intValue;
    self.maxResultsValueLabel.stringValue = [NSString stringWithFormat:@"%d", value];
    auto &cm = ConfigService();
    cm.setInt("launcher_bar.max_results", value);
    cm.requestSave();
}

- (void)requestBrowserHistoryPermissionsClicked:(__unused id)sender {
    NSAlert* alert = [[NSAlert alloc] init];
    alert.messageText = @"Browser History Access";
    alert.informativeText =
        @"This app needs permission to access your browser history to provide website search "
         "functionality. You may need to grant Full Disk Access in System Settings → Privacy & "
         "Security → Full Disk Access.";
    [alert addButtonWithTitle:@"Continue"];
    [alert addButtonWithTitle:@"Open Privacy Settings"];
    [alert addButtonWithTitle:@"Skip Browser History"];

    NSModalResponse response = [alert runModal];
    if (response == NSAlertSecondButtonReturn) {
        NSURL* privacyURL = [NSURL
            URLWithString:@"x-apple.systempreferences:com.apple.preference.security?Privacy_AllFiles"];
        [[NSWorkspace sharedWorkspace] openURL:privacyURL];
    } else if (response == NSAlertThirdButtonReturn) {
        return;  // User chose to skip
    }

    AppDelegate *appDelegate = (AppDelegate *)[[NSApplication sharedApplication] delegate];
    if (appDelegate && appDelegate.ctx && appDelegate.ctx->browserHistory) {
        appDelegate.ctx->browserHistory->resetPermissionRequestFlag();
    }
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1 * NSEC_PER_SEC)),
                   dispatch_get_main_queue(), ^{
                       ({
                           if (appDelegate && appDelegate.ctx && appDelegate.ctx->browserHistory) {
                               appDelegate.ctx->browserHistory->continueImportAllBrowserHistory();
                           }
                       });
                   });
}

#pragma mark - History actions

- (void)enableHistoryCheckboxClicked:(id)sender {
    BOOL enabled = ([(NSButton*)sender state] == NSControlStateValueOn);
    auto &cm = ConfigService();
    cm.setBool("history.enabled", enabled);
    cm.requestSave();

    AppDelegate *appDelegate = (AppDelegate *)[[NSApplication sharedApplication] delegate];
    auto *ctx = appDelegate.ctx;
    if (ctx && ctx->historyManager) {
        ctx->historyManager->setEnabled(enabled);
    }
}

- (void)clearHistoryClicked:(id)sender {
    NSAlert *alert = [[NSAlert alloc] init];
    [alert setMessageText:@"Clear History?"];
    [alert setInformativeText:@"This will remove all stored recent items. This action cannot be undone."];
    [alert addButtonWithTitle:@"Clear"];
    [alert addButtonWithTitle:@"Cancel"];
    [alert setAlertStyle:NSAlertStyleWarning];
    if ([alert runModal] != NSAlertFirstButtonReturn) { return; }

    AppDelegate *appDelegate = (AppDelegate *)[[NSApplication sharedApplication] delegate];
    auto *ctx = appDelegate.ctx;
    if (ctx && ctx->historyManager) {
        ctx->historyManager->clearAll();
    }
}

#pragma mark - Settings

- (void)loadSettings {
    auto &cm = ConfigService();

    // Determine if new numeric storage is present by checking for sentinel defaults
    const int kSentinel = -9999;
    bool hasNumericLauncherHotkey = (cm.getInt("general.launcher_hotkey.keycode", kSentinel) != kSentinel) &&
                                    (cm.getInt("general.launcher_hotkey.mods", kSentinel) != kSentinel);
    if (hasNumericLauncherHotkey) {
        int keyCode = cm.getInt("general.launcher_hotkey.keycode", kVK_Space);
        UInt32 carbonMods = (UInt32)cm.getInt("general.launcher_hotkey.mods", cmdKey);
        NSEventModifierFlags cocoaMods = CocoaModifiersFromCarbon(carbonMods);
        
        MASShortcut *shortcut = [MASShortcut shortcutWithKeyCode:keyCode modifierFlags:cocoaMods];
        [self.hotkeyView setShortcutValue:shortcut];
    } else {
        // Fallback to old string-based config for migration
        std::string hotkeyStr = cm.getString("general.launcher_hotkey", "⌘ Space");
        // Very naive parse – treat anything containing "⌘" as Command+Space
        UInt16 keyCode = kVK_Space;
        UInt32 carbonMods = cmdKey;
        NSEventModifierFlags cocoaMods = CocoaModifiersFromCarbon(carbonMods);
        
        MASShortcut *shortcut = [MASShortcut shortcutWithKeyCode:keyCode modifierFlags:cocoaMods];
        [self.hotkeyView setShortcutValue:shortcut];

        // Write migrated numeric version back to config
        cm.setInt("general.launcher_hotkey.keycode", keyCode);
        cm.setInt("general.launcher_hotkey.mods", (int)carbonMods);
        cm.requestSave();
    }

    bool launchAtLogin = cm.getBool("general.startup_on_login", true);
    self.launchAtLoginCheckbox.state = launchAtLogin ? NSControlStateValueOn : NSControlStateValueOff;

    int maxResults = cm.getInt("launcher_bar.max_results", 20);
    self.maxResultsSlider.integerValue = maxResults;
    self.maxResultsValueLabel.stringValue = [NSString stringWithFormat:@"%d", maxResults];

    BOOL historyEnabled = cm.getBool("history.enabled", true);
    self.enableHistoryCheckbox.state = historyEnabled ? NSControlStateValueOn : NSControlStateValueOff;

    // --- Chat hotkey ---
    bool hasNumericChatHotkey = (cm.getInt("general.chat_hotkey.keycode", kSentinel) != kSentinel) &&
                                (cm.getInt("general.chat_hotkey.mods", kSentinel) != kSentinel);
    if (hasNumericChatHotkey) {
        int chatKey = cm.getInt("general.chat_hotkey.keycode", kVK_ANSI_E);
        UInt32 chatMods = (UInt32)cm.getInt("general.chat_hotkey.mods", cmdKey);
        MASShortcut *chatSc = [MASShortcut shortcutWithKeyCode:chatKey
                                                modifierFlags:CocoaModifiersFromCarbon(chatMods)];
        [self.chatHotkeyView setShortcutValue:chatSc];
    } else {
        UInt16 defaultKey = kVK_ANSI_E;
        UInt32 defaultMods = cmdKey;
        MASShortcut *chatSc = [MASShortcut shortcutWithKeyCode:defaultKey
                                                modifierFlags:CocoaModifiersFromCarbon(defaultMods)];
        [self.chatHotkeyView setShortcutValue:chatSc];

        cm.setInt("general.chat_hotkey.keycode", defaultKey);
        cm.setInt("general.chat_hotkey.mods", (int)defaultMods);
        cm.requestSave();
    }
}

- (void)saveChanges {
    // Nothing extra for now since we write-through on every action, but keep for future use.
}

@end

#endif  // __OBJC__ 