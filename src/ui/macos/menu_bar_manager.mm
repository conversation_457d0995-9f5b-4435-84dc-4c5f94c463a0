#import <Cocoa/Cocoa.h>

#include "menu_bar_manager.h"
#include "application_visibility_manager.h"  // For logging convenience
#include "../../core/util/debug.h"

namespace launcher {
namespace ui {

MenuBarManager& MenuBarManager::shared() {
    static MenuBarManager instance;
    return instance;
}

MenuBarManager::MenuBarManager() : menuCreated_(false) {
    DBG("MenuBarManager initialised");
}

MenuBarManager::~MenuBarManager() = default;

void MenuBarManager::ensureMainMenu() {
    // Fast-path if already done.
    if (menuCreated_.load()) {
        return;
    }

    // Build menu on main thread – AppKit is not thread-safe.
    dispatch_async(dispatch_get_main_queue(), ^{
        if (menuCreated_.load()) {
            return;  // Another thread beat us to it.
        }

        DBG("Constructing main menu bar");

        NSMenu* mainMenu = [[NSMenu alloc] initWithTitle:@"MainMenu"];

        // ---------------- Application menu ----------------
        NSString* appName = [[NSProcessInfo processInfo] processName];
        NSMenu* appMenu = [[NSMenu alloc] initWithTitle:appName];
        NSMenuItem* appMenuItem = [[NSMenuItem alloc] initWithTitle:@"" action:nil keyEquivalent:@""];
        [appMenuItem setSubmenu:appMenu];
        [mainMenu addItem:appMenuItem];

        // About
        [appMenu addItemWithTitle:[NSString stringWithFormat:@"About %@", appName]
                           action:@selector(orderFrontStandardAboutPanel:)
                    keyEquivalent:@""];
        [appMenu addItem:[NSMenuItem separatorItem]];

        // Preferences placeholder – target will route via responder chain.
        [appMenu addItemWithTitle:@"Preferences…"
                           action:@selector(showPreferences:)
                    keyEquivalent:@","];
        [appMenu addItem:[NSMenuItem separatorItem]];

        // Hide/Hide Others/Show All (standard selectors)
        [appMenu addItemWithTitle:[NSString stringWithFormat:@"Hide %@", appName]
                           action:@selector(hide:)
                    keyEquivalent:@"h"];
        NSMenuItem* hideOthers = [appMenu addItemWithTitle:@"Hide Others"
                                                    action:@selector(hideOtherApplications:)
                                             keyEquivalent:@"h"];
        [hideOthers setKeyEquivalentModifierMask:(NSEventModifierFlagCommand | NSEventModifierFlagOption)];
        [appMenu addItemWithTitle:@"Show All"
                           action:@selector(unhideAllApplications:)
                    keyEquivalent:@""];
        [appMenu addItem:[NSMenuItem separatorItem]];

        // Quit
        [appMenu addItemWithTitle:[NSString stringWithFormat:@"Quit %@", appName]
                           action:@selector(terminate:)
                    keyEquivalent:@"q"];

        // ---------------- Edit menu ----------------
        NSMenu* editMenu = [[NSMenu alloc] initWithTitle:@"Edit"];
        NSMenuItem* editMenuItem = [[NSMenuItem alloc] initWithTitle:@"Edit" action:nil keyEquivalent:@""];
        [editMenuItem setSubmenu:editMenu];
        [mainMenu addItem:editMenuItem];

        struct EditCommand { const char* title; SEL action; const char* key; };
        const EditCommand kEditCommands[] = {
            {"Undo", @selector(undo:), "z"},
            {"Redo", @selector(redo:), "Z"},
            {nullptr, nullptr, nullptr}, // separator
            {"Cut", @selector(cut:), "x"},
            {"Copy", @selector(copy:), "c"},
            {"Paste", @selector(paste:), "v"},
            {"Select All", @selector(selectAll:), "a"},
        };
        for (const auto& cmd : kEditCommands) {
            if (!cmd.title) {
                [editMenu addItem:[NSMenuItem separatorItem]];
                continue;
            }
            NSMenuItem* item = [editMenu addItemWithTitle:@(cmd.title)
                                                   action:cmd.action
                                            keyEquivalent:@(cmd.key)];
            (void)item;  // suppress unused warnings
        }

        // ---------------- Chat menu ----------------
        NSMenu* chatMenu = [[NSMenu alloc] initWithTitle:@"Chat"];
        NSMenuItem* chatMenuItem = [[NSMenuItem alloc] initWithTitle:@"Chat" action:nil keyEquivalent:@""];
        [chatMenuItem setSubmenu:chatMenu];
        [mainMenu addItem:chatMenuItem];

        NSMenuItem *newChatItem = [chatMenu addItemWithTitle:@"New Chat"
                                                      action:@selector(newChat:)
                                               keyEquivalent:@""];
        // Remove default ⌘ modifier as keyEquivalent is empty
        [newChatItem setKeyEquivalentModifierMask:0];
        [chatMenu addItemWithTitle:@"Clear Messages"
                            action:@selector(clearMessages:)
                     keyEquivalent:@"k"];
        [chatMenu addItem:[NSMenuItem separatorItem]];
        [chatMenu addItemWithTitle:@"Stop Generating"
                            action:@selector(stopGenerating:)
                     keyEquivalent:@"."];

        // ---------------- Window menu ----------------
        NSMenu* windowMenu = [[NSMenu alloc] initWithTitle:@"Window"];
        [NSApp setWindowsMenu:windowMenu];
        NSMenuItem* windowMenuItem = [[NSMenuItem alloc] initWithTitle:@"Window" action:nil keyEquivalent:@""];
        [windowMenuItem setSubmenu:windowMenu];
        [mainMenu addItem:windowMenuItem];

        // Let Cocoa populate standard window items automatically.

        // --- Custom window/tab management shortcuts ---
        struct WindowCommand {
            const char *title;
            SEL action;
            const char *key;
            NSEventModifierFlags modifiers;
        };
        const WindowCommand kWindowCommands[] = {
            {"New Window",          @selector(newWindow:),           "n", NSEventModifierFlagCommand},
            {"New Tab",             @selector(newTab:),              "t", NSEventModifierFlagCommand},
            {"Close",               @selector(closeTabOrWindow:),    "w", NSEventModifierFlagCommand},
            {nullptr, nullptr, nullptr, 0}, // separator
            {"Reopen Closed Tab",   @selector(reopenLastClosedTab:),  "T", (NSEventModifierFlagCommand | NSEventModifierFlagShift)},
            {nullptr, nullptr, nullptr, 0}, // separator
            {"Show All Tabs",       @selector(showAllTabs:),         "\\", (NSEventModifierFlagCommand | NSEventModifierFlagShift)},
            {nullptr, nullptr, nullptr, 0}, // separator
            {"Next Tab",            @selector(selectNextTab:),       "]", (NSEventModifierFlagCommand | NSEventModifierFlagShift)},
            {"Previous Tab",        @selector(selectPreviousTab:),   "[", (NSEventModifierFlagCommand | NSEventModifierFlagShift)},
            {nullptr, nullptr, nullptr, 0}, // separator
            {"Close All Windows",   @selector(closeAllWindows:),    "w", (NSEventModifierFlagCommand | NSEventModifierFlagOption)},
        };
        for (const auto &cmd : kWindowCommands) {
            if (!cmd.title) {
                [windowMenu addItem:[NSMenuItem separatorItem]];
                continue;
            }
            NSMenuItem *item = [windowMenu addItemWithTitle:@(cmd.title)
                                                     action:cmd.action
                                              keyEquivalent:@(cmd.key)];
            [item setKeyEquivalentModifierMask:cmd.modifiers];
            (void)item;
        }

        // --- End custom shortcuts ---

        // ---------------- Help menu ----------------
        NSMenu* helpMenu = [[NSMenu alloc] initWithTitle:@"Help"];
        NSMenuItem* helpMenuItem = [[NSMenuItem alloc] initWithTitle:@"Help" action:nil keyEquivalent:@""];
        [helpMenuItem setSubmenu:helpMenu];
        [mainMenu addItem:helpMenuItem];
        [helpMenu addItemWithTitle:[NSString stringWithFormat:@"%@ Help", appName]
                             action:@selector(showHelp:)
                      keyEquivalent:@"?"];

        // Install as the app's menu bar.
        [NSApp setMainMenu:mainMenu];
        menuCreated_.store(true);
        DBG("Main menu bar installed");
    });
}

void MenuBarManager::setStreamingActive(bool /*active*/) {
    // TODO(onhao): enable/disable "Stop Generating" item dynamically.
}

}  // namespace ui
}  // namespace launcher 