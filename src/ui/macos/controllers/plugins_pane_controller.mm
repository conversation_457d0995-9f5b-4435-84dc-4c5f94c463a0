#import "plugins_pane_controller.h"
#include "../../models/runtime_plugin_cell_model.h"
#import <AppKit/AppKit.h>
#include "../injected_services.h"
#include "../../../core/plugins/runtime_manager_service.h"
#include "../../../core/events/event_bus_service.h"
#include <memory>
#import "../views/plugin_row_view.h"
#import "../models/plugin_table_model.h"
#include "../../../core/foundation/capability256.h"
#include "../../../core/security/capability_threat.hh"
#import "../views/swatch_view.h"
#include "../../../core/plugins/plugin_events.h"

#ifdef __OBJC__

using launcher::core::plugins::RuntimeManagerSvc;

@interface PluginsPaneController ()
@property(nonatomic, strong) NSScrollView *scrollView;
@property(nonatomic, strong) NSTableView  *tableView;
@property(nonatomic, strong) PluginTableModel *tableModel;
@property(nonatomic, strong) NSPopover *legendPopover;
@property(nonatomic, strong) NSSearchField *searchField;
@property(nonatomic, strong) NSSegmentedControl *filterControl;
@end

@implementation PluginsPaneController

- (void)loadView {
    // Root view with vibrancy matching other panes
    NSVisualEffectView *root = [[NSVisualEffectView alloc] initWithFrame:NSMakeRect(0, 0, 800, 500)];
    if (@available(macOS 10.14, *)) {
        root.material = NSVisualEffectMaterialSidebar;
    }
    root.state = NSVisualEffectStateActive;

    // Search field -------------------------------------------------------
    self.searchField = [[NSSearchField alloc] initWithFrame:NSZeroRect];
    self.searchField.placeholderString = @"Search Plugins";
    self.searchField.translatesAutoresizingMaskIntoConstraints = NO;
    [self.searchField setTarget:self];
    [self.searchField setAction:@selector(searchChanged:)];

    // Filter control (All/Enabled/Disabled)
    self.filterControl = [[NSSegmentedControl alloc] initWithFrame:NSZeroRect];
    self.filterControl.segmentCount = 3;
    [self.filterControl setLabel:@"All"     forSegment:0];
    [self.filterControl setLabel:@"Enabled" forSegment:1];
    [self.filterControl setLabel:@"Disabled" forSegment:2];
    self.filterControl.segmentStyle = NSSegmentStyleTexturedRounded;
    [self.filterControl setTarget:self];
    [self.filterControl setAction:@selector(filterChanged:)];
    [self.filterControl setSelectedSegment:0];

    // Scroll + TableView --------------------------------------------------
    self.scrollView = [[NSScrollView alloc] initWithFrame:root.bounds];
    self.scrollView.translatesAutoresizingMaskIntoConstraints = NO;
    self.scrollView.hasVerticalScroller   = YES;
    self.scrollView.autohidesScrollers    = YES;

    self.tableView = [[NSTableView alloc] initWithFrame:NSZeroRect];
    self.tableView.headerView = nil; // clean Settings look without column headers
    self.tableView.usesAlternatingRowBackgroundColors = YES;
    // Variable row heights handled via delegate methods
    self.tableView.delegate   = self;
    self.tableView.dataSource = self;

    // Columns -------------------------------------------------------------
    NSTableColumn *pluginCol = [[NSTableColumn alloc] initWithIdentifier:@"plugin"];
    pluginCol.width = 700.0;
    [self.tableView addTableColumn:pluginCol];

    self.scrollView.documentView = self.tableView;
    [root addSubview:self.searchField];
    [root addSubview:self.filterControl];
    [root addSubview:self.scrollView];

    // After adding scrollView, add help button top-right
    NSButton *helpBtn = [NSButton buttonWithTitle:@"?" target:self action:@selector(showLegend:)];
    helpBtn.font = [NSFont boldSystemFontOfSize:13.0];
    helpBtn.bezelStyle = NSBezelStyleHelpButton;
    helpBtn.translatesAutoresizingMaskIntoConstraints = NO;
    [root addSubview:helpBtn];

    // AutoLayout
    [NSLayoutConstraint activateConstraints:@[
        [self.searchField.topAnchor constraintEqualToAnchor:root.topAnchor constant:8.0],
        [self.searchField.leadingAnchor constraintEqualToAnchor:root.leadingAnchor constant:8.0],
        [self.searchField.widthAnchor constraintEqualToConstant:200.0],

        [self.filterControl.centerYAnchor constraintEqualToAnchor:self.searchField.centerYAnchor],
        [self.filterControl.leadingAnchor constraintEqualToAnchor:self.searchField.trailingAnchor constant:8.0],

        [self.scrollView.topAnchor constraintEqualToAnchor:self.searchField.bottomAnchor constant:8.0],
        [self.scrollView.bottomAnchor constraintEqualToAnchor:root.bottomAnchor],
        [self.scrollView.leadingAnchor constraintEqualToAnchor:root.leadingAnchor],
        [self.scrollView.trailingAnchor constraintEqualToAnchor:root.trailingAnchor],
        [helpBtn.trailingAnchor constraintEqualToAnchor:root.trailingAnchor constant:-8.0],
        [helpBtn.topAnchor constraintEqualToAnchor:root.topAnchor constant:8.0]
    ]];

    self.view = root;

    // Initial data population -------------------------------------------
    [self reloadFromRegistry];

    // Subscribe to PluginScanCompleteEvent to refresh when scan finishes.
    auto* eb = launcher::ui::EventBusService();
    if (eb) {
        __weak __typeof(self) weakSelf = self;
        eb->subscribe<launcher::core::plugins::PluginScanCompleteEvent>(
            [weakSelf](const launcher::core::plugins::PluginScanCompleteEvent* /*evt_ptr*/) {
                dispatch_async(dispatch_get_main_queue(), ^{
                    [weakSelf reloadFromRegistry];
                });
            });

        // Subscribe to runtime error to rollback checkbox and refresh row
        eb->subscribe<launcher::core::plugins::PluginRuntimeErrorEvent>(
            [weakSelf](const launcher::core::plugins::PluginRuntimeErrorEvent* /*evt_ptr*/) {
                dispatch_async(dispatch_get_main_queue(), ^{
                    [weakSelf reloadFromRegistry];
                });
            });
    }
}

#pragma mark - Data Reload helpers

- (void)setStubData:(NSArray<RuntimePluginCellModel *> *)stub {
    self.tableModel = nil;
    [self.tableView reloadData];
}

- (void)reloadFromRegistry {
    RuntimeManagerSvc *svc = launcher::ui::RuntimeManagerService();
    if (!svc) { return; }
    if (!self.tableModel) {
        self.tableModel = [[PluginTableModel alloc] initWithRegistry:&svc->registry()];
    } else {
        [self.tableModel refresh];
    }
    [self.tableView reloadData];
}

#pragma mark - NSTableViewDataSource

- (NSInteger)numberOfRowsInTableView:(__unused NSTableView *)tableView {
    return [self.tableModel rowCount];
}

- (NSView *)tableView:(NSTableView *)tv
   viewForTableColumn:(NSTableColumn *)tableColumn
                  row:(NSInteger)row {
    RuntimePluginCellModel *model = [self.tableModel modelAt:row];
    NSString *identifier = @"plugin_row";
    PluginRowView *cell = [tv makeViewWithIdentifier:identifier owner:self];
    if (!cell) {
        cell = [[PluginRowView alloc] initWithFrame:NSMakeRect(0, 0, tableColumn.width, tv.rowHeight)];
        cell.identifier = identifier;
    }
    [cell configureWithModel:model];

    __weak __typeof(self) wself = self;
    cell.toggleCallback = ^{
        __strong __typeof(self) sself = wself;
        if (!sself) return;
        NSInteger idx = [tv rowForView:cell];
        if (idx != -1) {
            [tv noteHeightOfRowsWithIndexesChanged:[NSIndexSet indexSetWithIndex:idx]];
        }
    };

    return cell;
}

#pragma mark - NSTableViewDelegate
// Currently no row selection logic (read-only)

- (void)showLegend:(id)sender {
    if (!self.legendPopover) {
        self.legendPopover = [[NSPopover alloc] init];
        NSViewController *vc = [[NSViewController alloc] init];
        NSScrollView *sv = [[NSScrollView alloc] initWithFrame:NSMakeRect(0,0,340,340)];
        sv.hasVerticalScroller = YES;
        NSGridView *grid = [[NSGridView alloc] initWithFrame:NSZeroRect];
        grid.translatesAutoresizingMaskIntoConstraints = YES;
        grid.rowSpacing    = 2.0;
        grid.columnSpacing = 8.0;
        for (uint16_t i=0;i<launcher::core::security::kCapabilityBits;++i) {
            kai::Capability cap = static_cast<kai::Capability>(i);
            launcher::core::security::ThreatClass cls = launcher::core::security::threatClass(cap);
            SwatchView *sw = [[SwatchView alloc] initWithThreatClass:cls];
            NSString *name = [NSString stringWithUTF8String:kai::toString(cap).data()];
            NSTextField *nameLabel = [NSTextField labelWithString:name];
            NSString *clsText = cls==launcher::core::security::ThreatClass::kSafe?@"Safe":(cls==launcher::core::security::ThreatClass::kSensitive?@"Sensitive":@"Dangerous");
            NSTextField *clsLabel = [NSTextField labelWithString:clsText];
            clsLabel.textColor = [NSColor secondaryLabelColor];
            [grid addRowWithViews:@[sw, nameLabel, clsLabel]];
        }
        [grid layoutSubtreeIfNeeded];
        NSSize fit = [grid fittingSize];
        grid.frame = NSMakeRect(0, 0, sv.frame.size.width, fit.height);
        sv.documentView = grid;
        vc.view = sv;
        self.legendPopover.contentViewController = vc;
        self.legendPopover.behavior = NSPopoverBehaviorTransient;
    }
    [self.legendPopover showRelativeToRect:[sender bounds] ofView:sender preferredEdge:NSRectEdgeMinY];
}

#pragma mark - Row Height Delegate

- (CGFloat)tableView:(NSTableView *)tableView heightOfRow:(NSInteger)row {
    NSView *view = [tableView viewAtColumn:0 row:row makeIfNecessary:NO];
    if (view) {
        return view.fittingSize.height;
    }
    // Fallback estimate – assume collapsed height for now, could use heuristics
    RuntimePluginCellModel *model = [self.tableModel modelAt:row];
    return [PluginRowView estimatedExpandedHeightForModel:model];
}

#pragma mark - Search / Filter Actions

- (void)searchChanged:(id)sender {
    [NSObject cancelPreviousPerformRequestsWithTarget:self selector:@selector(applySearchAndFilter) object:nil];
    [self performSelector:@selector(applySearchAndFilter) withObject:nil afterDelay:0.2];
}

- (void)filterChanged:(id)sender {
    [self applySearchAndFilter];
}

- (void)applySearchAndFilter {
    NSString *q = self.searchField.stringValue ?: @"";
    NSInteger seg = self.filterControl.selectedSegment;
    NSInteger state = -1;
    if (seg == 1) state = 1; // Enabled
    else if (seg == 2) state = 0; // Disabled
    [self.tableModel applyFilter:q enabledState:state];
    [self.tableView reloadData];
}

@end

#endif /* __OBJC__ */ 