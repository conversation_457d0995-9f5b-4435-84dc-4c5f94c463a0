// Created new header for LauncherBarController
#pragma once

#import <Cocoa/Cocoa.h>

@class AppDelegate;

NS_ASSUME_NONNULL_BEGIN

/**
 * LauncherBarController
 * ---------------------
 * Dedicated object responsible for constructing and managing the Launcher Bar
 * window (search field, results table, progress indicator, etc.).  By
 * delegating this UI logic out of `AppDelegate` we significantly reduce the
 * size of that monolithic file and restore the single-responsibility
 * principle.
 */
@interface LauncherBarController : NSObject

// Designated initializer – retains a weak reference to the owning
// AppDelegate so we can fill back pointers (window, searchField, …).
- (instancetype)initWithAppDelegate:(AppDelegate *)appDelegate NS_DESIGNATED_INITIALIZER;
- (instancetype)init NS_UNAVAILABLE;

// MARK: – Public API ---------------------------------------------------------

/// Builds the window and sub-views if they haven't been created yet.
- (void)createLauncherBar;

/// Presents the launcher bar, capturing the previous front-most application
/// so we can restore focus later.
- (void)show;

/// Toggles visibility of the launcher (show if hidden, hide if visible).
- (void)toggle;

/// Hides the launcher and re-activates the previously front-most app.
- (void)hideAndRestore;

// MARK: – Exposed read-only widgets -----------------------------------------

@property(nonatomic, readonly) NSWindow *window;
@property(nonatomic, readonly) NSTextField *searchField;
@property(nonatomic, readonly) NSProgressIndicator *searchProgressIndicator;
@property(nonatomic, readonly) NSTableView *resultsTable;
@property(nonatomic, readonly) NSScrollView *scrollView;
#if __has_include(<AppKit/NSTableViewDiffableDataSource.h>)
@property(nonatomic, readonly, nullable) NSTableViewDiffableDataSource *diffableDataSource;
#endif

@end

NS_ASSUME_NONNULL_END 