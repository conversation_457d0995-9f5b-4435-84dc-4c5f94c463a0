#import "models_pane_controller.h"

// System headers
#import <Cocoa/Cocoa.h>
#import <QuartzCore/QuartzCore.h>  // For CALayer border

// Project headers
#include "../../../core/config/ai_provider_config.h"
#include "../../../core/util/debug.h"
#include <algorithm>
#include <cctype>
#include <map>
#include <regex>
#include "../../../core/llm/provider_catalog.h"
#include "../../../core/llm/model_id_parser.h"  // Shared grouping helpers
#include "../../../core/interfaces/iconfig_manager.h"  // @add_include
#include "../injected_services.h"  // shared service helpers
#include <cassert>

using launcher::core::AIProviderConfig;
// using launcher::core::ConfigManager;
using launcher::core::ModelConfig;
using launcher::core::ParsedModelId;
using launcher::core::parseModelId;
using launcher::ui::ConfigService;

// Wrapper classes for outline view items -----------------------------------

#ifdef __OBJC__

@interface ModelItem : NSObject
@property(nonatomic, readonly) ModelConfig config;
- (instancetype)initWithConfig:(const ModelConfig&)cfg;
@end

@implementation ModelItem {
    ModelConfig _config;
}

- (instancetype)initWithConfig:(const ModelConfig&)cfg {
    self = [super init];
    if (self) {
        _config = cfg;
    }
    return self;
}

- (ModelConfig)config {
    return _config;
}
@end

@interface CategoryItem : NSObject
@property(nonatomic, strong) NSString* title;
@property(nonatomic, strong) NSMutableArray<ModelItem*>* children;
@end

@implementation CategoryItem
- (instancetype)init {
    self = [super init];
    if (self) {
        _children = [[NSMutableArray alloc] init];
    }
    return self;
}
@end

@interface ModelsPaneController ()
// Removed old popup properties; no UI-level properties needed
@end

@implementation ModelsPaneController {
    std::vector<AIProviderConfig> providers_;
    std::vector<ModelConfig> current_models_; // still used for search filtering
    NSMutableArray* categories_;   // array of CategoryItem*
    NSTableView* providerTable_;   // left pane remains NSTableView
    NSOutlineView* modelsOutline_; // right pane hierarchical list
    NSSearchField* searchField_;
    NSString* selectedProviderId_;
    // New: Tabbed interface and General controls
    NSTabView* tabView_;
    NSSecureTextField* apiKeyField_;
    NSPopUpButton* defaultModelPopup_;
    NSTextField* apiHostField_;
    NSTextField* orgIdField_;
    NSButton* streamCheckbox_;
    NSButton* refreshButton_;
}

- (instancetype)init {
    self = [super initWithNibName:nil bundle:nil];
    if (self) {
    }
    return self;
}

- (void)loadView {
    NSView* root = [[NSView alloc] initWithFrame:NSMakeRect(0, 0, 800, 500)];
    self.view = root;

    // Split view
    NSSplitView* split = [[NSSplitView alloc] initWithFrame:root.bounds];
    split.vertical = YES;
    split.dividerStyle = NSSplitViewDividerStyleThin;
    split.autoresizingMask = NSViewWidthSizable | NSViewHeightSizable;
    [root addSubview:split];

    // LEFT: Providers source list
    NSScrollView* provScroll = [[NSScrollView alloc] initWithFrame:NSMakeRect(0,0,150,root.bounds.size.height)];
    provScroll.hasVerticalScroller = YES;
    providerTable_ = [[NSTableView alloc] initWithFrame:provScroll.contentView.bounds];
    providerTable_.headerView = nil;
    providerTable_.rowSizeStyle = NSTableViewRowSizeStyleDefault;
    providerTable_.rowHeight = 24; // slightly taller for image + text
    NSTableColumn* col = [[NSTableColumn alloc] initWithIdentifier:@"providerColumn"];
    [providerTable_ addTableColumn:col];
    providerTable_.delegate = self;
    providerTable_.dataSource = self;
    providerTable_.selectionHighlightStyle = NSTableViewSelectionHighlightStyleSourceList;
    provScroll.documentView = providerTable_;
    [split addSubview:provScroll];

    // RIGHT container
    NSView* right = [[NSView alloc] initWithFrame:NSMakeRect(0, 0, 550, root.bounds.size.height)];
    right.autoresizingMask = NSViewWidthSizable | NSViewHeightSizable;
    [split addSubview:right];

    // ------------------------------------------------------------------
    // Create classic NSTabView (General | Models)
    // ------------------------------------------------------------------
    tabView_ = [[NSTabView alloc] initWithFrame:right.bounds];
    tabView_.autoresizingMask = NSViewWidthSizable | NSViewHeightSizable;
    [right addSubview:tabView_];

    // -------------------------------------------------------------
    // GENERAL TAB
    // -------------------------------------------------------------
    NSView* generalView = [[NSView alloc] initWithFrame:right.bounds];
    generalView.autoresizingMask = NSViewWidthSizable | NSViewHeightSizable;

    // ------------------------------------------------------------------
    // Auto-layout driven General tab using NSGridView for perfect spacing.
    // ------------------------------------------------------------------

    // Use native AppKit styling
    CGFloat bw = 1.0 / [NSScreen mainScreen].backingScaleFactor;

    // Factory helper for read-only label
    auto makeLabel = ^NSTextField*(NSString* text) {
        NSTextField* lbl = [[NSTextField alloc] initWithFrame:NSZeroRect];
        lbl.stringValue = text;
        lbl.bezeled = NO;
        lbl.drawsBackground = NO;
        lbl.editable = NO;
        lbl.selectable = NO;
        lbl.translatesAutoresizingMaskIntoConstraints = NO;
        return lbl;
    };

    NSTextField* apiKeyLabel      = makeLabel(@"API Key:");
    NSTextField* defaultLabel     = makeLabel(@"Default Model:");
    NSTextField* hostLabel        = makeLabel(@"API Host:");
    NSTextField* orgLabel         = makeLabel(@"Organization ID:");

    apiKeyField_       = [[NSSecureTextField alloc] initWithFrame:NSZeroRect];
    defaultModelPopup_ = [[NSPopUpButton alloc] initWithFrame:NSZeroRect];
    apiHostField_      = [[NSTextField alloc] initWithFrame:NSZeroRect];
    orgIdField_        = [[NSTextField alloc] initWithFrame:NSZeroRect];

    streamCheckbox_ = [[NSButton alloc] initWithFrame:NSZeroRect];
    streamCheckbox_.buttonType = NSButtonTypeSwitch;
    streamCheckbox_.title = @"Stream Responses?";

    // Refresh button for model list
    refreshButton_ = [[NSButton alloc] initWithFrame:NSZeroRect];
    refreshButton_.title = @"Refresh";
    refreshButton_.bezelStyle = NSBezelStyleRounded;
    refreshButton_.controlSize = NSControlSizeSmall;
    [refreshButton_ setTarget:self];
    [refreshButton_ setAction:@selector(refreshModels:)];

    // Placeholder empty views for grid alignment
    NSView* spacer1 = [[NSView alloc] initWithFrame:NSZeroRect];

    // Build grid (auto-save, no saveButton_ row)
    NSArray* rows = @[ @[apiKeyLabel, apiKeyField_],
                       @[defaultLabel, defaultModelPopup_, refreshButton_],
                       @[hostLabel, apiHostField_],
                       @[orgLabel, orgIdField_],
                       @[spacer1, streamCheckbox_] ];

    NSMutableArray<NSArray<NSView*>*>* normalized = [[NSMutableArray alloc] init];
    // Ensure each row has equal columns (3)
    for (NSArray* r in rows) {
        NSMutableArray* m = [r mutableCopy];
        while (m.count < 3) { [m addObject:[[NSView alloc] initWithFrame:NSZeroRect]]; }
        [normalized addObject:m];
    }

    NSGridView* grid = [NSGridView gridViewWithViews:normalized];
    grid.translatesAutoresizingMaskIntoConstraints = NO;
    grid.rowSpacing = 12.0;
    grid.columnSpacing = 8.0;

    // First column fixed width (110) and baseline alignment
    [[grid columnAtIndex:0] setWidth:110.0];
    [[grid columnAtIndex:0] setXPlacement:NSGridCellPlacementLeading];

    [generalView addSubview:grid];

    // Pin grid inside view with 16 px insets
    [NSLayoutConstraint activateConstraints:@[
        [grid.topAnchor constraintEqualToAnchor:generalView.topAnchor constant:20],
        [grid.leadingAnchor constraintEqualToAnchor:generalView.leadingAnchor constant:16],
        [grid.trailingAnchor constraintEqualToAnchor:generalView.trailingAnchor constant:-16]
    ]];

    // -------------------------------------------------------------
    // MODELS TAB
    // -------------------------------------------------------------
    NSView* modelsView = [[NSView alloc] initWithFrame:right.bounds];
    modelsView.autoresizingMask = NSViewWidthSizable | NSViewHeightSizable;

    // Search field inside modelsView
    searchField_ = [[NSSearchField alloc] initWithFrame:NSMakeRect(10, modelsView.bounds.size.height - 34, 300, 24)];
    searchField_.delegate = self;
    searchField_.placeholderString = @"Filter models";
    searchField_.autoresizingMask = NSViewMinYMargin;
    [modelsView addSubview:searchField_];

    // Models outline
    NSScrollView* modelScroll = [[NSScrollView alloc] initWithFrame:NSMakeRect(0, 0, modelsView.bounds.size.width, modelsView.bounds.size.height - 40)];
    modelScroll.autoresizingMask = NSViewWidthSizable | NSViewHeightSizable;
    modelScroll.hasVerticalScroller = YES;

    modelsOutline_ = [[NSOutlineView alloc] initWithFrame:modelScroll.contentView.bounds];
    modelsOutline_.delegate = self;
    modelsOutline_.dataSource = self;
    modelsOutline_.usesAlternatingRowBackgroundColors = YES;
    modelsOutline_.allowsEmptySelection = NO;

    NSTableColumn* nameCol = [[NSTableColumn alloc] initWithIdentifier:@"name"];
    nameCol.title = @"Model";
    nameCol.width = 280;
    nameCol.headerToolTip = @"Model identifier";
    [modelsOutline_ addTableColumn:nameCol];
    NSTableColumn* tokenCol = [[NSTableColumn alloc] initWithIdentifier:@"tokens"];
    tokenCol.title = @"Max";
    tokenCol.width = 60;
    tokenCol.headerToolTip = @"Maximum context tokens";
    [modelsOutline_ addTableColumn:tokenCol];
    NSTableColumn* toolCol = [[NSTableColumn alloc] initWithIdentifier:@"tool"];
    toolCol.title = @"🛠";
    toolCol.width = 30;
    toolCol.headerToolTip = @"Tool calling";
    [modelsOutline_ addTableColumn:toolCol];
    NSTableColumn* visionCol = [[NSTableColumn alloc] initWithIdentifier:@"vision"];
    visionCol.title = @"📷";
    visionCol.width = 30;
    visionCol.headerToolTip = @"Image input";
    [modelsOutline_ addTableColumn:visionCol];
    NSTableColumn* reasonCol = [[NSTableColumn alloc] initWithIdentifier:@"reason"];
    reasonCol.title = @"Cog"; // reasoning
    reasonCol.width = 30;
    reasonCol.headerToolTip = @"Extended reasoning endpoint";
    [modelsOutline_ addTableColumn:reasonCol];

    NSTableColumn* audioCol = [[NSTableColumn alloc] initWithIdentifier:@"audio"];
    audioCol.title = @"🎙"; // audio in
    audioCol.width = 30;
    audioCol.headerToolTip = @"Audio input (STT)";
    [modelsOutline_ addTableColumn:audioCol];

    NSTableColumn* imgGenCol = [[NSTableColumn alloc] initWithIdentifier:@"imggen"];
    imgGenCol.title = @"🖌"; // image generation
    imgGenCol.width = 30;
    imgGenCol.headerToolTip = @"Image generation";
    [modelsOutline_ addTableColumn:imgGenCol];

    NSTableColumn* speechCol = [[NSTableColumn alloc] initWithIdentifier:@"speech"];
    speechCol.title = @"🔊"; // speech out
    speechCol.width = 30;
    speechCol.headerToolTip = @"Speech/TTS output";
    [modelsOutline_ addTableColumn:speechCol];

    NSTableColumn* ctxCol = [[NSTableColumn alloc] initWithIdentifier:@"ctx"];
    ctxCol.title = @"📖"; // large context
    ctxCol.width = 30;
    ctxCol.headerToolTip = @"Large context window";
    [modelsOutline_ addTableColumn:ctxCol];

    modelScroll.documentView = modelsOutline_;
    [modelsView addSubview:modelScroll];

    // Add tabs to tab view
    NSTabViewItem* generalItem = [[NSTabViewItem alloc] initWithIdentifier:@"general"];
    generalItem.label = @"General";
    generalItem.view = generalView;
    [tabView_ addTabViewItem:generalItem];

    NSTabViewItem* modelsItem = [[NSTabViewItem alloc] initWithIdentifier:@"models"];
    modelsItem.label = @"Models";
    modelsItem.view = modelsView;
    [tabView_ addTabViewItem:modelsItem];

    [self loadSettings];
}

#pragma mark - Data loading / filtering

- (void)reloadModelsForProvider:(const std::string&)providerId {
    current_models_.clear();
    categories_ = [[NSMutableArray alloc] init];

    // Map group_key -> vector<pair<ModelConfig, ParsedModelId>>
    struct ItemPair { ModelConfig cfg; ParsedModelId parsed; };
    std::map<std::string, std::vector<ItemPair>> grouped;

    for (const auto& p : providers_) {
        if (p.provider_id == providerId) {
            for (const auto& model : p.models) {
                ParsedModelId pm = parseModelId(model.id, providerId);
                grouped[pm.group_key].push_back({model, pm});
                current_models_.push_back(model);  // For search filtering compatibility
            }
            break;
        }
    }

    // Build a sortable list of groups with metadata (major descending)
    struct GroupMeta {
        std::string key;
        std::string title;
        double major;
        int variant_rank;
    };
    std::vector<GroupMeta> metas;
    for (const auto& kv : grouped) {
        if (kv.second.empty()) continue;
        const auto& frontParsed = kv.second.front().parsed;
        metas.push_back({kv.first, frontParsed.group_title, frontParsed.major, frontParsed.group_variant_rank});
    }
    std::sort(metas.begin(), metas.end(), [](const GroupMeta& a, const GroupMeta& b) {
        if (a.major != b.major) return a.major > b.major; // higher numeric version first
        if (a.variant_rank != b.variant_rank) return a.variant_rank < b.variant_rank; // e.g., 4o before 4
        return a.key < b.key;
    });

    for (const auto& meta : metas) {
        auto vecPairs = grouped[meta.key];
        std::sort(vecPairs.begin(), vecPairs.end(), [](const ItemPair& a, const ItemPair& b) {
            if (a.parsed.variant_rank != b.parsed.variant_rank) return a.parsed.variant_rank < b.parsed.variant_rank;
            if (a.parsed.context_k != b.parsed.context_k) return a.parsed.context_k > b.parsed.context_k;
            return a.cfg.display_name < b.cfg.display_name;
        });

        CategoryItem* catItem = [[CategoryItem alloc] init];
        catItem.title = [NSString stringWithUTF8String:meta.title.c_str()];
        for (const auto& pair : vecPairs) {
            ModelItem* mi = [[ModelItem alloc] initWithConfig:pair.cfg];
            [catItem.children addObject:mi];
        }
        [categories_ addObject:catItem];
    }

    [modelsOutline_ reloadData];
    // Expand top-level items by default
    for (NSInteger i = 0; i < categories_.count; ++i) {
        [modelsOutline_ expandItem:[categories_ objectAtIndex:i]];
    }
}

- (void)applySearchFilter {
    NSString* query = searchField_.stringValue;
    if (query.length == 0) {
        // Reload full list
        if (selectedProviderId_) {
            std::string prov(selectedProviderId_.UTF8String);
            [self reloadModelsForProvider:prov];
        }
        return;
    }

    std::string q = query.lowercaseString.UTF8String;

    // Filter models first
    std::vector<ModelConfig> filtered;
    for (const auto& m : current_models_) {
        std::string nameLower = m.display_name.empty() ? m.id : m.display_name;
        std::transform(nameLower.begin(), nameLower.end(), nameLower.begin(), ::tolower);
        if (nameLower.find(q) != std::string::npos) {
            filtered.push_back(m);
        }
    }

    // Rebuild category hierarchy based on filtered list
    categories_ = [[NSMutableArray alloc] init];
    struct ItemPair { ModelConfig cfg; ParsedModelId parsed; };
    std::map<std::string, std::vector<ItemPair>> grouped;
    for (const auto& model : filtered) {
        ParsedModelId pm = parseModelId(model.id, selectedProviderId_.UTF8String);
        grouped[pm.group_key].push_back({model, pm});
    }

    struct GroupMeta { std::string key; std::string title; double major; int variant_rank; };
    std::vector<GroupMeta> metas;
    for (const auto& kv : grouped) {
        if (kv.second.empty()) continue;
        const auto& frontParsed = kv.second.front().parsed;
        metas.push_back({kv.first, frontParsed.group_title, frontParsed.major, frontParsed.group_variant_rank});
    }
    std::sort(metas.begin(), metas.end(), [](const GroupMeta& a, const GroupMeta& b) {
        if (a.major != b.major) return a.major > b.major; // higher numeric version first
        if (a.variant_rank != b.variant_rank) return a.variant_rank < b.variant_rank; // e.g., 4o before 4
        return a.key < b.key;
    });

    for (const auto& meta : metas) {
        auto vecPairs = grouped[meta.key];
        std::sort(vecPairs.begin(), vecPairs.end(), [](const ItemPair& a, const ItemPair& b) {
            if (a.parsed.variant_rank != b.parsed.variant_rank) return a.parsed.variant_rank < b.parsed.variant_rank;
            if (a.parsed.context_k != b.parsed.context_k) return a.parsed.context_k > b.parsed.context_k;
            return a.cfg.display_name < b.cfg.display_name;
        });

        CategoryItem* catItem = [[CategoryItem alloc] init];
        catItem.title = [NSString stringWithUTF8String:meta.title.c_str()];
        for (const auto& pair : vecPairs) {
            ModelItem* mi = [[ModelItem alloc] initWithConfig:pair.cfg];
            [catItem.children addObject:mi];
        }
        [categories_ addObject:catItem];
    }

    [modelsOutline_ reloadData];
}

- (void)loadSettings {
    launcher::core::IConfigManager* cfgPtr = nullptr;
    AppDelegate *appDelegateCfg = (AppDelegate *)[[NSApplication sharedApplication] delegate];
    if (appDelegateCfg && appDelegateCfg.ctx && appDelegateCfg.ctx->configManager) {
        cfgPtr = appDelegateCfg.ctx->configManager.get();
    } else {
        cfgPtr = &ConfigService();
    }
    auto &cfg = *cfgPtr;
    providers_ = cfg.getAIProviders();
    // Merge dynamic models via ProviderCatalog
    for (auto& p : providers_) {
        // Use dependency-injected ProviderCatalog from AppContext (via AppDelegate)
        auto *appDel = (AppDelegate *)[[NSApplication sharedApplication] delegate];
        if (appDel && appDel.ctx && appDel.ctx->providerCatalog) {
            auto fetched = appDel.ctx->providerCatalog->list(p.provider_id);
            if (!fetched.empty()) {
                p.models = std::move(fetched);
            }
        }
    }

    // ------------------------------------------------------------------
    // Re-order providers so high-priority ones appear first in the list.
    // Currently we only prioritise the OpenAI provider, but the mechanism
    // can easily be extended by adding more IDs to the priority vector.
    // ------------------------------------------------------------------
    static const std::vector<std::string> kPriority = {"openai"};

    auto priorityOf = [&](const std::string& pid) {
        std::string lower = pid;
        std::transform(lower.begin(), lower.end(), lower.begin(), ::tolower);
        for (size_t i = 0; i < kPriority.size(); ++i) {
            std::string p = kPriority[i];
            std::transform(p.begin(), p.end(), p.begin(), ::tolower);
            if (lower == p) return static_cast<int>(i);
        }
        return static_cast<int>(kPriority.size());
    };

    std::stable_sort(providers_.begin(), providers_.end(), [&](const AIProviderConfig& a, const AIProviderConfig& b) {
        int pa = priorityOf(a.provider_id);
        int pb = priorityOf(b.provider_id);
        return pa < pb; // lower priority index means earlier in the list
    });

    [providerTable_ reloadData];
    // Select first provider by default
    if (providers_.size() > 0) {
        [providerTable_ selectRowIndexes:[NSIndexSet indexSetWithIndex:0] byExtendingSelection:NO];
        selectedProviderId_ = [NSString stringWithUTF8String:providers_[0].provider_id.c_str()];
        // Objective-C++: convert const char* to std::string explicitly for clarity
        std::string prov(selectedProviderId_.UTF8String);
        [self reloadModelsForProvider:prov];
        [self updateGeneralTabUI];
    }
}

#pragma mark - NSTableViewDataSource / Delegate

- (NSInteger)numberOfRowsInTableView:(NSTableView*)tableView {
    if (tableView == providerTable_) return providers_.size();
    return 0;
}

- (id)tableView:(NSTableView*)tableView objectValueForTableColumn:(NSTableColumn*)column row:(NSInteger)row {
    if (tableView != providerTable_) {
        return nil;
    }

    if (row >= 0 && row < (NSInteger)providers_.size()) {
        const auto& prov = providers_[row];
        NSString* display = [NSString stringWithUTF8String:prov.name.empty() ? prov.provider_id.c_str() : prov.name.c_str()];
        return display;
    }
    return nil;
}

- (void)tableViewSelectionDidChange:(NSNotification*)notification {
    if (notification.object == providerTable_) {
        NSInteger row = providerTable_.selectedRow;
        if (row >= 0 && row < (NSInteger)providers_.size()) {
            selectedProviderId_ = [NSString stringWithUTF8String:providers_[row].provider_id.c_str()];
            // Objective-C++: convert const char* to std::string explicitly for clarity
            std::string prov(selectedProviderId_.UTF8String);
            [self reloadModelsForProvider:prov];
            [self updateGeneralTabUI];
        }
    }
}

#pragma mark - NSSearchFieldDelegate
- (void)controlTextDidChange:(NSNotification*)obj {
    if (obj.object == searchField_) {
        [self applySearchFilter];
    }
}

#pragma mark - Persistence
- (void)saveChanges {
    if (!selectedProviderId_) return;

    launcher::core::IConfigManager* cfgPtr = nullptr;
    AppDelegate *appDelegateCfg = (AppDelegate *)[[NSApplication sharedApplication] delegate];
    if (appDelegateCfg && appDelegateCfg.ctx && appDelegateCfg.ctx->configManager) {
        cfgPtr = appDelegateCfg.ctx->configManager.get();
    } else {
        cfgPtr = &ConfigService();
    }

    std::string provId = selectedProviderId_.UTF8String;

    // Collect selected model for global default
    NSString* repObj = [[defaultModelPopup_ selectedItem] representedObject];
    std::string selectedModel = repObj ? std::string(repObj.UTF8String) : "";

    for (auto& p : providers_) {
        if (p.provider_id == provId) {
            p.api_key = apiKeyField_.stringValue.UTF8String;
            p.base_url = apiHostField_.stringValue.UTF8String;
            p.api_key_variable = orgIdField_.stringValue.UTF8String;

            // Persist the newly selected default model for this provider
            if (!selectedModel.empty()) {
                p.default_model = selectedModel;
            }
            break;
        }
    }

    // Persist provider changes (API keys + default model)
    if (cfgPtr) {
        cfgPtr->replaceAIProviders(providers_);
        // Keep global default provider in sync for launcher bar, etc.
        cfgPtr->setString("default_ai_provider", provId);
        cfgPtr->requestSave();
    }
}

#pragma mark - NSOutlineViewDataSource
- (NSInteger)outlineView:(NSOutlineView*)outlineView numberOfChildrenOfItem:(id)item {
    if (item == nil) {
        return categories_.count;
    }
    if ([item isKindOfClass:[CategoryItem class]]) {
        CategoryItem* cat = (CategoryItem*)item;
        return cat.children.count;
    }
    return 0;
}

- (id)outlineView:(NSOutlineView*)outlineView child:(NSInteger)index ofItem:(id)item {
    if (item == nil) {
        return [categories_ objectAtIndex:index];
    }
    if ([item isKindOfClass:[CategoryItem class]]) {
        CategoryItem* cat = (CategoryItem*)item;
        return [cat.children objectAtIndex:index];
    }
    return nil;
}

- (BOOL)outlineView:(NSOutlineView*)outlineView isItemExpandable:(id)item {
    return [item isKindOfClass:[CategoryItem class]];
}

#pragma mark - NSOutlineViewDelegate
- (id)outlineView:(NSOutlineView*)outlineView objectValueForTableColumn:(NSTableColumn*)tableColumn byItem:(id)item {
    NSString* ident = tableColumn.identifier;
    if ([item isKindOfClass:[CategoryItem class]]) {
        // Show category title only in name column
        if ([ident isEqualToString:@"name"]) {
            return ((CategoryItem*)item).title;
        }
        return @"";
    }
    // Model row
    ModelItem* mi = (ModelItem*)item;
    const ModelConfig m = mi.config;
    if ([ident isEqualToString:@"name"]) {
        NSString* display = m.display_name.empty() ? [NSString stringWithUTF8String:m.id.c_str()] : [NSString stringWithUTF8String:m.display_name.c_str()];
        return display;
    } else if ([ident isEqualToString:@"tokens"]) {
        return @(m.max_tokens);
    } else if ([ident isEqualToString:@"tool"]) {
        return m.supports_tool_calling ? @"✓" : @"";
    } else if ([ident isEqualToString:@"vision"]) {
        return m.supports_image_input ? @"✓" : @"";
    } else if ([ident isEqualToString:@"audio"]) {
        return m.supports_audio_input ? @"✓" : @"";
    } else if ([ident isEqualToString:@"imggen"]) {
        return m.supports_image_generation ? @"✓" : @"";
    } else if ([ident isEqualToString:@"speech"]) {
        return m.supports_speech_generation ? @"✓" : @"";
    } else if ([ident isEqualToString:@"ctx"]) {
        return m.supports_large_context ? @"✓" : @"";
    } else if ([ident isEqualToString:@"reason"]) {
        return m.supports_reasoning ? @"✓" : @"";
    }
    return @"";
}

- (void)outlineViewSelectionDidChange:(NSNotification*)notification {
    NSInteger row = [modelsOutline_ selectedRow];
    if (row < 0) return;
    id item = [modelsOutline_ itemAtRow:row];
    if (![item isKindOfClass:[ModelItem class]]) return; // ignore category rows
    ModelItem* mi = (ModelItem*)item;
    const ModelConfig mc = mi.config;
    std::string providerId = selectedProviderId_.UTF8String;

    launcher::core::IConfigManager* cfgPtr = nullptr;
    AppDelegate *appDelegateCfg = (AppDelegate *)[[NSApplication sharedApplication] delegate];
    if (appDelegateCfg && appDelegateCfg.ctx && appDelegateCfg.ctx->configManager) {
        cfgPtr = appDelegateCfg.ctx->configManager.get();
    } else {
        cfgPtr = &ConfigService();
    }
    if (cfgPtr) {
        cfgPtr->setProviderDefaultModel(providerId, mc.id);
        cfgPtr->requestSave();
    }
}

// View-based outline: provide cell views
- (NSView*)outlineView:(NSOutlineView*)outlineView viewForTableColumn:(NSTableColumn*)tableColumn item:(id)item {
    NSString* ident = tableColumn.identifier;
    NSTableCellView* cell = [outlineView makeViewWithIdentifier:ident owner:self];
    if (!cell) {
        cell = [[NSTableCellView alloc] initWithFrame:NSMakeRect(0, 0, tableColumn.width, 16)];
        NSTextField* textField = [[NSTextField alloc] initWithFrame:cell.bounds];
        textField.autoresizingMask = NSViewWidthSizable | NSViewHeightSizable;
        textField.bezeled = NO;
        textField.drawsBackground = NO;
        textField.editable = NO;
        textField.selectable = NO;
        cell.textField = textField;
        [cell addSubview:textField];
        cell.identifier = ident;
    }

    if ([item isKindOfClass:[CategoryItem class]]) {
        if ([ident isEqualToString:@"name"]) {
            cell.textField.stringValue = ((CategoryItem*)item).title;
            cell.textField.font = [NSFont boldSystemFontOfSize:13];
        } else {
            cell.textField.stringValue = @"";
        }
    } else if ([item isKindOfClass:[ModelItem class]]) {
        ModelItem* mi = (ModelItem*)item;
        const ModelConfig m = mi.config;
        if ([ident isEqualToString:@"name"]) {
            NSString* display = m.display_name.empty() ? [NSString stringWithUTF8String:m.id.c_str()] : [NSString stringWithUTF8String:m.display_name.c_str()];
            cell.textField.stringValue = display;
            cell.textField.font = [NSFont systemFontOfSize:12];
        } else if ([ident isEqualToString:@"tokens"]) {
            cell.textField.stringValue = [NSString stringWithFormat:@"%d", m.max_tokens];
        } else if ([ident isEqualToString:@"tool"]) {
            cell.textField.stringValue = m.supports_tool_calling ? @"✓" : @"";
        } else if ([ident isEqualToString:@"vision"]) {
            cell.textField.stringValue = m.supports_image_input ? @"✓" : @"";
        } else if ([ident isEqualToString:@"audio"]) {
            cell.textField.stringValue = m.supports_audio_input ? @"✓" : @"";
        } else if ([ident isEqualToString:@"imggen"]) {
            cell.textField.stringValue = m.supports_image_generation ? @"✓" : @"";
        } else if ([ident isEqualToString:@"speech"]) {
            cell.textField.stringValue = m.supports_speech_generation ? @"✓" : @"";
        } else if ([ident isEqualToString:@"ctx"]) {
            cell.textField.stringValue = m.supports_large_context ? @"✓" : @"";
        } else if ([ident isEqualToString:@"reason"]) {
            cell.textField.stringValue = m.supports_reasoning ? @"✓" : @"";
        } else {
            cell.textField.stringValue = @"";
        }
    }
    return cell;
}

#pragma mark - General Tab Helpers

- (void)updateGeneralTabUI {
    if (!selectedProviderId_) return;
    std::string provId = selectedProviderId_.UTF8String;
    AIProviderConfig* target = nullptr;
    for (auto& p : providers_) {
        if (p.provider_id == provId) {
            target = &p;
            break;
        }
    }
    if (!target) return;

    apiKeyField_.stringValue = [NSString stringWithUTF8String:target->api_key.c_str()];
    apiHostField_.stringValue = [NSString stringWithUTF8String:target->base_url.c_str()];
    orgIdField_.stringValue = [NSString stringWithUTF8String:target->api_key_variable.c_str()];

    // Populate default model popup
    [defaultModelPopup_ removeAllItems];
    defaultModelPopup_.autoenablesItems = NO; // We manage enabled state manually

    // -------------------------------------------------------------
    // Group models using the same parseModelId logic as the outline
    // -------------------------------------------------------------

    struct ItemPair { const ModelConfig* cfg; ParsedModelId parsed; };
    std::map<std::string, std::vector<ItemPair>> grouped;
    for (const auto& model : target->models) {
        ParsedModelId pm = parseModelId(model.id, target->provider_id);
        grouped[pm.group_key].push_back({&model, pm});
    }

    // Build metadata for group ordering
    struct GroupMeta {
        std::string key;
        std::string title;
        double major;
        int variant_rank;
    };

    std::vector<GroupMeta> metas;
    for (const auto& kv : grouped) {
        if (kv.second.empty()) continue;
        const auto& fp = kv.second.front().parsed;
        metas.push_back({kv.first, fp.group_title, fp.major, fp.group_variant_rank});
    }

    std::sort(metas.begin(), metas.end(), [](const GroupMeta& a, const GroupMeta& b) {
        if (a.major != b.major) return a.major > b.major;
        if (a.variant_rank != b.variant_rank) return a.variant_rank < b.variant_rank;
        return a.key < b.key;
    });

    // Determine default model for this provider (provider-specific first, then legacy global)
    std::string providerDefaultModel = target->default_model;
    // No legacy fallback – global default model removed.

    // Build the menu contents
    NSMenuItem* selectedItem = nil;
    for (size_t gi = 0; gi < metas.size(); ++gi) {
        const auto& meta = metas[gi];

        // Header (disabled)
        NSString* headerTitle = [NSString stringWithUTF8String:meta.title.c_str()];
        NSMenuItem* header = [[NSMenuItem alloc] initWithTitle:headerTitle action:nil keyEquivalent:@""];
        header.enabled = NO;
        [defaultModelPopup_.menu addItem:header];

        // Sort items within the group
        auto vecPairs = grouped[meta.key];
        std::sort(vecPairs.begin(), vecPairs.end(), [](const ItemPair& a, const ItemPair& b) {
            if (a.parsed.variant_rank != b.parsed.variant_rank) return a.parsed.variant_rank < b.parsed.variant_rank;
            if (a.parsed.context_k != b.parsed.context_k) return a.parsed.context_k > b.parsed.context_k;
            return a.cfg->display_name < b.cfg->display_name;
        });

        // Add each model as selectable item
        for (const auto& pair : vecPairs) {
            const ModelConfig* m = pair.cfg;
            NSString* title = m->display_name.empty() ? [NSString stringWithUTF8String:m->id.c_str()] : [NSString stringWithUTF8String:m->display_name.c_str()];
            NSMenuItem* item = [[NSMenuItem alloc] initWithTitle:title action:nil keyEquivalent:@""];
            [item setRepresentedObject:[NSString stringWithUTF8String:m->id.c_str()]];
            [defaultModelPopup_.menu addItem:item];

            // Select item if it matches global default keys
            if (!providerDefaultModel.empty() && m->id == providerDefaultModel) {
                selectedItem = item;
            }
        }

        // Separator between groups (except after the last one)
        if (gi + 1 < metas.size()) {
            [defaultModelPopup_.menu addItem:[NSMenuItem separatorItem]];
        }
    }

    // Set selection
    if (selectedItem) {
        [defaultModelPopup_ selectItem:selectedItem];
    } else {
        // Default to first selectable item if no match
        NSInteger idx = [defaultModelPopup_.menu indexOfItemWithRepresentedObject:nil];
        if (idx != -1 && idx + 1 < defaultModelPopup_.numberOfItems) {
            [defaultModelPopup_ selectItemAtIndex:idx + 1];
        }
    }
}

// Provide view-based cells with an icon + provider name
- (NSView*)tableView:(NSTableView*)tableView viewForTableColumn:(NSTableColumn*)tableColumn row:(NSInteger)row {
    if (tableView != providerTable_) {
        return nil;
    }

    NSTableCellView* cell = [tableView makeViewWithIdentifier:@"ProviderCell" owner:self];
    if (!cell) {
        cell = [[NSTableCellView alloc] initWithFrame:NSMakeRect(0, 0, tableColumn.width, tableView.rowHeight)];
        cell.identifier = @"ProviderCell";

        // Image view (16×16) aligned left
        NSImageView* iv = [[NSImageView alloc] initWithFrame:NSMakeRect(3, (tableView.rowHeight - 16) / 2, 16, 16)];
        iv.imageScaling = NSImageScaleProportionallyDown;
        [cell addSubview:iv];
        cell.imageView = iv;

        // Text field next to icon
        NSTextField* tf = [[NSTextField alloc] initWithFrame:NSMakeRect(24, 0, tableColumn.width - 26, tableView.rowHeight)];
        tf.autoresizingMask = NSViewWidthSizable | NSViewHeightSizable;
        tf.bezeled = NO;
        tf.drawsBackground = NO;
        tf.editable = NO;
        tf.selectable = NO;
        [cell addSubview:tf];
        cell.textField = tf;
    }

    if (row >= 0 && row < (NSInteger)providers_.size()) {
        const auto& prov = providers_[row];
        NSString* display = [NSString stringWithUTF8String:prov.name.empty() ? prov.provider_id.c_str() : prov.name.c_str()];
        cell.textField.stringValue = display;

        NSString* symName = systemSymbolNameForProvider(prov.provider_id);
        NSImage* icon = [NSImage imageWithSystemSymbolName:symName accessibilityDescription:nil];
        if (!icon) {
            icon = [NSImage imageWithSystemSymbolName:@"questionmark.circle" accessibilityDescription:nil];
        }
        icon.size = NSMakeSize(16, 16);
        cell.imageView.image = icon;
    }

    return cell;
}

// Action to refresh models list / popup
- (void)refreshModels:(id)sender {
    [self updateGeneralTabUI];
}

// ---------------------------------------------------------------------------
// Helper: map provider ID to an SF-Symbol name (light-weight placeholder icons)
// ---------------------------------------------------------------------------
static NSString* systemSymbolNameForProvider(const std::string& pid) {
    std::string id = pid;
    std::transform(id.begin(), id.end(), id.begin(), ::tolower);

    if (id == "openai") return @"sparkles";            // ✨
    if (id == "replicate") return @"arrow.triangle.2.circlepath";
    if (id == "ollama") return @"doc.on.clipboard";
    if (id == "mistral" || id == "mistral ai") return @"wind";
    if (id == "anthropic") return @"person.3.sequence";
    if (id == "openrouter") return @"network";
    if (id == "perplexity" || id == "perplexity ai") return @"questionmark.bubble";
    if (id == "azure") return @"cloud";
    if (id == "google" || id == "google ai") return @"globe";
    if (id == "groq") return @"bolt.horizontal.circle";
    if (id == "together" || id == "together ai") return @"person.2.circle";
    if (id == "xai" || id == "x.ai") return @"xmark.square";
    return @"questionmark.circle";
}

@end

#endif  // __OBJC__ 