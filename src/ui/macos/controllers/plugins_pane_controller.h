#pragma once

#ifdef __OBJC__
#import <AppKit/AppKit.h>

@class RuntimePluginCellModel;

@interface PluginsPaneController : NSViewController <NSTableViewDataSource, NSTableViewDelegate>

/// Reload data from RuntimeManagerSvc snapshot (main-thread safe).
- (void)reloadFromRuntimeManager;

/// Unit-test seam: inject stub runtime models then reload the table.
- (void)setStubData:(NSArray<RuntimePluginCellModel*>*)stub;

@property(nonatomic, readonly) NSTableView *tableView;

@end
#endif 