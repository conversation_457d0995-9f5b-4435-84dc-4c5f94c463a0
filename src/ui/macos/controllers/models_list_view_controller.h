// Models list tab view controller declaration
#pragma once

#ifdef __OBJC__
#import <Cocoa/Cocoa.h>

#include <string>
#include <vector>
#include "../../../core/config/ai_provider_config.h"

// Forward declaration
@class ModelsListViewController;

/**
 * @brief View controller that displays searchable, grouped list of models for a provider.
 *
 * This class contains its own search field and NSOutlineView.  The presentation logic
 * is largely extracted from the original ModelsPaneController implementation so it can
 * be embedded in an NSTabViewController.
 */
@interface ModelsListViewController : NSViewController <NSOutlineViewDelegate, NSOutlineViewDataSource, NSSearchFieldDelegate>

/// Set full provider list (needed for search across provider changes).
- (void)setProviders:(const std::vector<launcher::core::AIProviderConfig>&)providers;

/// Reload UI for the specified provider id (e.g. "openai").
- (void)reloadForProviderId:(const std::string&)providerId;

@end

#endif // __OBJC__ 