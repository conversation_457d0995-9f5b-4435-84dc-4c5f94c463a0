// Created new implementation for LauncherBarController
#import "launcher_bar_controller.h"
#import "../macos_ui_internal.h"  // Provides full AppDelegate interface with properties

#import <Carbon/Carbon.h>
#if __has_include(<AppKit/NSTableViewDiffableDataSource.h>)
#import <AppKit/NSTableViewDiffableDataSource.h>
#endif

// System headers
#include <float.h>
#include <cassert> // @add_include for assert

// Library / project headers
#include "../../../core/context/context.h"
#include "../../../core/context/context_providers.h"
#include "../../../core/util/debug.h"
#import "../hud_window_utils.h"
#import <Foundation/Foundation.h>
#include "../../common/result_item.h"
#include "../../../core/app_context.h"
#include "../../../core/history/history_manager_interface.h"
#include "../../../core/interfaces/iconfig_manager.h"  // config interface
#include "../injected_services.h" // shared service helpers

// Forward declare Objective-C++ AppDelegate so we can reference ivars/properties
// without importing the massive implementation header chain.
@class AppDelegate;

// Use shared helper provided by injected_services.h
using launcher::ui::ConfigService;

// -----------------------------------------------------------------------------
// MARK: – Private interface
// -----------------------------------------------------------------------------
@interface LauncherBarController ()
@property(nonatomic, weak)   AppDelegate *appDelegate;  // back-pointer (non-owning)
@property(nonatomic, strong, nullable) NSRunningApplication *previousFrontmostApp;

@property(nonatomic, readwrite) NSWindow *window;
@property(nonatomic, readwrite) NSTextField *searchField;
@property(nonatomic, readwrite) NSProgressIndicator *searchProgressIndicator;
@property(nonatomic, readwrite) NSTableView *resultsTable;
@property(nonatomic, readwrite) NSScrollView *scrollView;
#if __has_include(<AppKit/NSTableViewDiffableDataSource.h>)
@property(nonatomic, readwrite, nullable) NSTableViewDiffableDataSource *diffableDataSource;
#endif
@end

@implementation LauncherBarController

// -----------------------------------------------------------------------------
#pragma mark – Lifecycle
// -----------------------------------------------------------------------------

- (instancetype)initWithAppDelegate:(AppDelegate *)appDelegate {
    self = [super init];
    if (self) {
        _appDelegate = appDelegate; // weak by property semantics
    }
    return self;
}

// -----------------------------------------------------------------------------
#pragma mark – Public API
// -----------------------------------------------------------------------------

- (void)createLauncherBar {
    if (self.window) {
        // Already created – nothing to do.
        return;
    }

    // ------------------- Window ------------------------------------------------
    NSRect frame = NSMakeRect(0, 0, 800, 600);
    self.window = [[NSWindow alloc] initWithContentRect:frame
                                               styleMask:NSWindowStyleMaskTitled | NSWindowStyleMaskResizable | NSWindowStyleMaskFullSizeContentView
                                                 backing:NSBackingStoreBuffered
                                                   defer:NO];

    // Visual effect background (HUD-style acrylic)
    NSVisualEffectView *effectView = CreateHUDContentView([[self.window contentView] bounds], 10.0);
    self.window.contentView = effectView;

    self.window.releasedWhenClosed = NO;
    [self.window center];
    self.window.backgroundColor = [NSColor clearColor];
    self.window.opaque = NO;
    self.window.movableByWindowBackground = YES;
    self.window.titlebarAppearsTransparent = YES;
    self.window.titleVisibility = NSWindowTitleHidden;
    self.window.showsToolbarButton = NO;
    [[self.window standardWindowButton:NSWindowZoomButton] setHidden:YES];
    [[self.window standardWindowButton:NSWindowMiniaturizeButton] setHidden:YES];
    [[self.window standardWindowButton:NSWindowCloseButton] setHidden:YES];

    // Delegate back to AppDelegate for window-level callbacks (focus loss, etc.).
    self.window.delegate = (id<NSWindowDelegate>)self.appDelegate;

    // ------------------- Search Field -----------------------------------------
    NSRect searchFieldFrame = NSMakeRect(10, frame.size.height - 60, frame.size.width - 20, 50);
    self.searchField = [[NSTextField alloc] initWithFrame:searchFieldFrame];
    self.searchField.placeholderString = @"Search… (⌘⏎ to chat)";
    self.searchField.bezeled = YES;
    self.searchField.bezelStyle = NSTextFieldRoundedBezel;
    self.searchField.font = [NSFont systemFontOfSize:18.0];
    self.searchField.focusRingType = NSFocusRingTypeNone;
    self.searchField.autoresizingMask = NSViewWidthSizable | NSViewMinYMargin;

    // Attach delegate that was previously wired in AppDelegate
    self.searchField.delegate = (id<NSTextFieldDelegate>)self.appDelegate.searchFieldDelegate;

    // ------------------- Progress Indicator -----------------------------------
    NSRect progressFrame = NSMakeRect(frame.size.width - 40, frame.size.height - 45, 20, 20);
    self.searchProgressIndicator = [[NSProgressIndicator alloc] initWithFrame:progressFrame];
    self.searchProgressIndicator.style = NSProgressIndicatorStyleSpinning;
    self.searchProgressIndicator.displayedWhenStopped = NO;
    self.searchProgressIndicator.autoresizingMask = NSViewMinXMargin | NSViewMinYMargin;
    self.searchProgressIndicator.hidden = YES;

    // ------------------- Results Table ----------------------------------------
    NSRect tableFrame = NSMakeRect(0, 10, frame.size.width - 10, frame.size.height - 70);
    self.resultsTable = [[NSTableView alloc] initWithFrame:tableFrame];
    self.resultsTable.selectionHighlightStyle = NSTableViewSelectionHighlightStyleRegular;
    if (@available(macOS 11.0, *)) {
        self.resultsTable.style = NSTableViewStyleSourceList;
    }
    self.resultsTable.backgroundColor = [NSColor clearColor];
    self.resultsTable.rowHeight = 50.0; // Taller rows for icons
    self.resultsTable.intercellSpacing = NSMakeSize(10.0, 8.0);

    NSTableColumn *resultColumn = [[NSTableColumn alloc] initWithIdentifier:@"result"];
    resultColumn.title = @"Result";
    resultColumn.width = tableFrame.size.width;
    [self.resultsTable addTableColumn:resultColumn];

    // Delegate + data-source still live on AppDelegate.resultsDelegate
    self.resultsTable.delegate   = (id<NSTableViewDelegate>)self.appDelegate.resultsDelegate;
    self.resultsTable.dataSource = (id<NSTableViewDataSource>)self.appDelegate.resultsDelegate;
    self.resultsTable.target     = self.appDelegate;
    self.resultsTable.doubleAction = @selector(tableViewDoubleClick:);
    self.resultsTable.headerView = nil;

    // ------------------- Scroll View ------------------------------------------
    self.scrollView = [[NSScrollView alloc] initWithFrame:tableFrame];
    self.scrollView.documentView = self.resultsTable;
    self.scrollView.hasVerticalScroller = YES;
    self.scrollView.autoresizingMask = NSViewWidthSizable | NSViewHeightSizable;
    self.scrollView.drawsBackground = NO;

    // ------------------- Compose ---------------------------------------------
    [effectView addSubview:self.searchField];
    [effectView addSubview:self.searchProgressIndicator];
    [effectView addSubview:self.scrollView];

    // Make search field first responder by default
    [self.window makeFirstResponder:self.searchField];

#if __has_include(<AppKit/NSTableViewDiffableDataSource.h>)
    if (@available(macOS 11.0, *)) {
        __weak __typeof(self) weakSelf = self;
        self.diffableDataSource = [[NSTableViewDiffableDataSource alloc] initWithTableView:self.resultsTable
                                                                             cellProvider:^NSView * _Nullable(NSTableView * _Nonnull tableView,
                                                                                                               NSTableColumn * _Nonnull tableColumn,
                                                                                                               NSInteger row,
                                                                                                               id  _Nonnull itemIdentifier) {
            return [weakSelf.appDelegate.resultsDelegate tableView:tableView
                                               viewForTableColumn:tableColumn
                                                             row:row];
        }];
        self.resultsTable.dataSource = self.diffableDataSource;
    }
#endif

    // ------------------- Publish pointers back to AppDelegate -----------------
    self.appDelegate.launcherBar              = self.window;
    self.appDelegate.searchField              = self.searchField;
    self.appDelegate.searchProgressIndicator  = self.searchProgressIndicator;
    self.appDelegate.resultsTable             = self.resultsTable;
    self.appDelegate.scrollView               = self.scrollView;
#if __has_include(<AppKit/NSTableViewDiffableDataSource.h>)
    self.appDelegate.diffableDataSource       = self.diffableDataSource;
#endif

    // Pre-seed results with history so the list is visible on first appearance.
    [self updateHistoryResults];
}

- (void)show {
    // Refresh history each time bar is shown if the search field is empty.
    if (self.searchField.stringValue.length == 0) {
        [self updateHistoryResults];
    }

    // Collect selected text context for agents.
    auto &contextManager = launcher::ui::ContextService();
    DBM(@"Collecting selected text context before showing launcher bar (controller)");
    self.appDelegate.selectedTextContext = contextManager.collectContextFromProviders({"selected_text"});

    // Remember the front-most app so we can restore focus when hiding.
    NSRunningApplication *frontApp = [[NSWorkspace sharedWorkspace] frontmostApplication];
    if (frontApp.processIdentifier != [[NSRunningApplication currentApplication] processIdentifier]) {
        self.previousFrontmostApp = frontApp;
    } else {
        self.previousFrontmostApp = nil;
    }

    [self.window makeKeyAndOrderFront:nil];
    [NSApp activateIgnoringOtherApps:YES];
    [self.searchField becomeFirstResponder];
    [self.searchField selectText:nil];
}

- (void)toggle {
    if (self.window.isVisible) {
        [self hideAndRestore];
    } else {
        [self show];
    }
}

- (void)hideAndRestore {
    if (self.window && self.window.isVisible) {
        [self.window orderOut:nil];
    }
    if (self.previousFrontmostApp && !self.previousFrontmostApp.terminated &&
        self.previousFrontmostApp.processIdentifier != [[NSRunningApplication currentApplication] processIdentifier]) {
        [self.previousFrontmostApp activateWithOptions:NSApplicationActivateIgnoringOtherApps];
    }
    self.previousFrontmostApp = nil;
}

- (void)updateHistoryResults {
    auto *ctx = self.appDelegate.ctx;
    if (!ctx || !ctx->historyManager) { return; }

    if (!ctx->historyManager->isEnabled()) {
        return;
    }

    int maxResCfg = ConfigService().getMaxResults();

    std::size_t historyDisplayLimit = maxResCfg > 0 ? static_cast<std::size_t>(maxResCfg) : 50;

    std::vector<launcher::ui::ResultItem> items =
        ctx->historyManager->getTopItems(historyDisplayLimit);

    NSMutableArray *dicts = [NSMutableArray arrayWithCapacity:items.size()];
    for (const auto &res : items) {
        NSString *name       = [NSString stringWithUTF8String:res.name.c_str()];
        NSString *path       = [NSString stringWithUTF8String:res.path.c_str()];
        NSString *iconPath   = [NSString stringWithUTF8String:res.iconPath.c_str()];
        NSNumber *score      = [NSNumber numberWithDouble:res.score];
        NSString *type       = [NSString stringWithUTF8String:res.type.c_str()];
        NSString *identifier = nil;
        if (!res.identifier.empty()) {
            // Preserve stable identifier exactly as stored (bare conversation id for chats).
            identifier = [NSString stringWithUTF8String:res.identifier.c_str()];
        } else {
            // Fallback synthesised identifier for legacy items without stable id.
            identifier = [NSString stringWithFormat:@"%@-%@", name ?: @"", path ?: @""];
        }
        [dicts addObject:@{ @"identifier": identifier ?: @"",
                            @"name":       name ?: @"",
                            @"path":       path ?: @"",
                            @"iconPath":   iconPath ?: @"",
                            @"score":      score ?: @0,
                            @"type":       type ?: @"app" }];
    }

    self.appDelegate.resultsDelegate.results = dicts;

#if __has_include(<AppKit/NSTableViewDiffableDataSource.h>)
    if (self.diffableDataSource) {
        // Build identifiers array for diffable snapshot.
        NSMutableArray<NSString *> *ids = [NSMutableArray arrayWithCapacity:dicts.count];
        for (NSDictionary *d in dicts) {
            NSString *iid = d[@"identifier"] ?: [NSString stringWithFormat:@"%@-%@", d[@"name"], d[@"path"]];
            [ids addObject:iid];
        }
        NSDiffableDataSourceSnapshot<NSNumber *, NSString *> *snap = [[NSDiffableDataSourceSnapshot alloc] init];
        [snap appendSectionsWithIdentifiers:@[@0]];
        [snap appendItemsWithIdentifiers:ids intoSectionWithIdentifier:@0];
        [self.diffableDataSource applySnapshot:snap animatingDifferences:NO];
    } else
#endif
    {
        [self.resultsTable reloadData];
    }

    // Auto-select first row if any.
    if (dicts.count > 0) {
        [self.resultsTable selectRowIndexes:[NSIndexSet indexSetWithIndex:0] byExtendingSelection:NO];
    }

    // Ensure spinner hidden.
    [self.appDelegate showSearchInProgress:NO];
}

@end 