#include "browser_history_importer.h"
#include <iostream>
#include <algorithm>
#include <fstream>
#include <sstream>
#include <regex>
#include <ctime>
#include <filesystem>
#include <future>
#include "../../core/util/debug.h"

#import <Foundation/Foundation.h>
#import <AppKit/AppKit.h>
#import <sqlite3.h>

// Static flag to track if permission has been requested
static bool g_permissionRequested = false;

// Function to reset permission request flag (can be called from settings)
extern "C" {
    void resetBrowserHistoryPermissionFlag() {
        g_permissionRequested = false;
    }
}

// Objective-C helper class for handling asynchronous imports
@interface BrowserHistoryImporterHelper : NSObject
+ (instancetype)sharedInstance;
- (void)continueImportAllBrowserHistoryForImporter:(void*)importer;
@end

@implementation BrowserHistoryImporterHelper
+ (instancetype)sharedInstance {
    static BrowserHistoryImporterHelper *instance = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        instance = [[self alloc] init];
    });
    return instance;
}

- (void)continueImportAllBrowserHistoryForImporter:(void*)importer {
    @autoreleasepool {
        launcher::ui::BrowserHistoryImporter* historyImporter = 
            static_cast<launcher::ui::BrowserHistoryImporter*>(importer);
        if (historyImporter) {
            historyImporter->continueImportAllBrowserHistory();
        }
    }
}
@end

namespace launcher {
namespace ui {

// Helper function to extract domain from URL
std::string BrowserHistoryImporter::extractDomain(const std::string& url) {
    std::regex domainRegex("^(?:https?:\\/\\/)?(?:www\\.)?([^:\\/\\n?]+)");
    std::smatch match;
    if (std::regex_search(url, match, domainRegex) && match.size() > 1) {
        return match[1].str();
    }
    return "";
}

// Helper function to clean special characters from suggestion text
std::string BrowserHistoryImporter::cleanSuggestionText(const std::string& text) {
    // Manual removal of specific problematic UTF-8 sequences
    std::string cleaned = text;
    
    // Remove the specific Unicode characters that are still appearing (ee 80 80 and ee 80 81)
    const std::vector<std::string> specificUnicodeSequences = {
        "\xEE\x80\x80", // Unicode character with hex ee 80 80
        "\xEE\x80\x81"  // Unicode character with hex ee 80 81
    };
    
    for (const auto& sequence : specificUnicodeSequences) {
        size_t pos = 0;
        while ((pos = cleaned.find(sequence, pos)) != std::string::npos) {
            cleaned.erase(pos, sequence.length());
        }
    }
    
    // Remove multiple spaces
    std::regex multipleSpacesRegex("\\s+");
    cleaned = std::regex_replace(cleaned, multipleSpacesRegex, " ");
    
    // Trim leading and trailing spaces
    std::regex trimRegex("^\\s+|\\s+$");
    cleaned = std::regex_replace(cleaned, trimRegex, "");
    
    return cleaned;
}

// Implementation of the static method to reset permission flag
void BrowserHistoryImporter::resetPermissionRequestFlag() {
    g_permissionRequested = true;
}

BrowserHistoryImporter::BrowserHistoryImporter() : initialized(false) {
    // Constructor
}

BrowserHistoryImporter::~BrowserHistoryImporter() {
    // Destructor
}

// Perform HTTP request to fetch search suggestions
std::string BrowserHistoryImporter::performHttpRequest(const std::string& url, const std::map<std::string, std::string>& headers) {
    NSString* nsUrl = [NSString stringWithUTF8String:url.c_str()];
    NSURL* requestUrl = [NSURL URLWithString:nsUrl];
    
    DBM(@"Performing HTTP request to: %@", nsUrl);
    
    if (!requestUrl) {
        ERM(@"Invalid URL: %@", nsUrl);
        return "";
    }
    
    NSMutableURLRequest* request = [NSMutableURLRequest requestWithURL:requestUrl
                                             cachePolicy:NSURLRequestUseProtocolCachePolicy
                                         timeoutInterval:3.0]; // Reduced timeout for faster response
    
    // Add custom headers if provided
    if (!headers.empty()) {
        for (const auto& header : headers) {
            NSString* headerName = [NSString stringWithUTF8String:header.first.c_str()];
            NSString* headerValue = [NSString stringWithUTF8String:header.second.c_str()];
            [request setValue:headerValue forHTTPHeaderField:headerName];
            DBM(@"Added header: %@ : %@", headerName, headerValue);
        }
    }
    
    __block NSString* responseString = nil;
    __block NSError* responseError = nil;
    __block bool requestCompleted = false;
    
    NSURLSession* session = [NSURLSession sharedSession];
    NSURLSessionDataTask* task = [session dataTaskWithRequest:request
                                            completionHandler:^(NSData* data, NSURLResponse* response, NSError* error) {
        if (error) {
            responseError = error;
            ERM(@"HTTP request error: %@", error.localizedDescription);
        } else if (data) {
            responseString = [[NSString alloc] initWithData:data encoding:NSUTF8StringEncoding];
            
            NSHTTPURLResponse* httpResponse = (NSHTTPURLResponse*)response;
            DBM(@"HTTP response status code: %ld", httpResponse.statusCode);
            
            if (!responseString) {
                ERM(@"Failed to decode response data as UTF-8");
                
                // Try alternative encodings
                NSArray* encodings = @[
                    @(NSASCIIStringEncoding),
                    @(NSISOLatin1StringEncoding),
                    @(NSISOLatin2StringEncoding),
                    @(NSUTF16StringEncoding),
                    @(NSUTF16BigEndianStringEncoding),
                    @(NSUTF16LittleEndianStringEncoding)
                ];
                
                for (NSNumber* encodingNum in encodings) {
                    NSStringEncoding encoding = [encodingNum unsignedIntegerValue];
                    responseString = [[NSString alloc] initWithData:data encoding:encoding];
                    if (responseString) {
                        DBM(@"Successfully decoded response using alternative encoding");
                        break;
                    }
                }
                
                // If still no valid string, try a fallback approach
                if (!responseString) {
                    ERM(@"Could not decode response with any encoding");
                    
                    // Print the first few bytes of the response for debugging
                    const unsigned char* bytes = (const unsigned char*)[data bytes];
                    size_t length = std::min((size_t)20, (size_t)[data length]);
                    std::string byteStr = "First " + std::to_string(length) + " bytes of response: ";
                    for (size_t i = 0; i < length; i++) {
                        char hex[4];
                        snprintf(hex, 4, "%02x ", bytes[i]);
                        byteStr += hex;
                    }
                    // NSString* byteDebugStr = [NSString stringWithUTF8String:byteStr.c_str()]; // Removed unused variable
                    // DBM("performHttpRequest", @"%@", byteDebugStr);
                }
            }
        } else {
            ERM(@"No data received from HTTP request");
        }
        requestCompleted = true;
    }];
    
    [task resume];
    
    // Wait for the request to complete (with timeout)
    NSDate* timeoutDate = [NSDate dateWithTimeIntervalSinceNow:3.0]; // Reduced timeout
    while (!requestCompleted && [timeoutDate timeIntervalSinceNow] > 0) {
        [[NSRunLoop currentRunLoop] runMode:NSDefaultRunLoopMode beforeDate:[NSDate dateWithTimeIntervalSinceNow:0.1]];
    }
    
    if (!requestCompleted) {
        ERM(@"HTTP request timed out");
        return "";
    }
    
    if (responseError) {
        ERM(@"Error fetching search suggestions: %@", responseError.localizedDescription);
        return "";
    }
    
    if (responseString) {
        DBM(@"Received response of length: %lu", (unsigned long)[responseString length]);
    } else {
        DBM(@"No response received");
    }
    
    return responseString ? [responseString UTF8String] : "";
}

// Fetch search engine suggestions from Google
std::vector<WebsiteEntry> BrowserHistoryImporter::fetchGoogleSuggestions(const std::string& query, int maxResults) {
    std::vector<WebsiteEntry> suggestions;
    
    if (query.empty() || query.length() < 2) {
        return suggestions;
    }
    
    // URL encode the query
    NSString* nsQuery = [NSString stringWithUTF8String:query.c_str()];
    NSString* encodedQuery = [nsQuery stringByAddingPercentEncodingWithAllowedCharacters:[NSCharacterSet URLQueryAllowedCharacterSet]];
    
    // Construct the Google suggestion API URL with the exact parameters from the provided headers
    std::string apiUrl = "https://www.google.com/complete/search?q=";
    apiUrl += [encodedQuery UTF8String];
    apiUrl += "&cp=3&client=gws-wiz&xssi=t&gs_pcrt=undefined&hl=vi&authuser=0&psi=bP7LZ6yZBYqo2roPvb7SmAk.1741422188463&dpr=2";
    
    DBM(@"Fetching Google suggestions for query: %@", nsQuery);
    DBM(@"Google API URL: %@", [NSString stringWithUTF8String:apiUrl.c_str()]);
    
    // Set up the headers exactly as provided
    std::map<std::string, std::string> headers = {
        {"accept", "*/*"},
        {"accept-encoding", "gzip, deflate, br, zstd"},
        {"accept-language", "en-US,en;q=0.9"},
        {"cookie", "AEC=AVcja2cSKTh-yC5xDQaq7lLrf0E2bCV_P3Y6ySWgvSf9Ht1lqhVR0z2ICw; NID=522=J6s-YMMlEWSE8Ux8GrSEJNKJ9CGKHvP-olvFV5ADNGr3isb0oZ0NYEE8LRCKUmdShHQNEgX06Y0nE6YcPuEJOy1jSdXwqXhQjEzWijgMDNDlUgx-kyMpvJP2NfgSBrgonGPSSKsL0XbJ0h9qZWM5nl0yw_LqPMBqwMCe4lrMoCoSqZ7D1ND6TPtnk5rLC0vr5XItYDYSqQjmJBZOKqx6JKxAjWjO97X8; OGPC=19046228-1:"},
        {"downlink", "10"},
        {"priority", "u=1, i"},
        {"referer", "https://www.google.com/"},
        {"rtt", "50"},
        {"sec-ch-prefers-color-scheme", "light"},
        {"sec-ch-ua", "\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\""},
        {"sec-ch-ua-arch", "\"arm\""},
        {"sec-ch-ua-bitness", "\"64\""},
        {"sec-ch-ua-form-factors", "\"Desktop\""},
        {"sec-ch-ua-full-version", "\"134.0.6998.45\""},
        {"sec-ch-ua-full-version-list", "\"Chromium\";v=\"134.0.6998.45\", \"Not:A-Brand\";v=\"********\", \"Google Chrome\";v=\"134.0.6998.45\""},
        {"sec-ch-ua-mobile", "?0"},
        {"sec-ch-ua-model", "\"\""},
        {"sec-ch-ua-platform", "\"macOS\""},
        {"sec-ch-ua-platform-version", "\"15.3.1\""},
        {"sec-ch-ua-wow64", "?0"},
        {"sec-fetch-dest", "empty"},
        {"sec-fetch-mode", "cors"},
        {"sec-fetch-site", "same-origin"},
        {"user-agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/134.0.0.0 Safari/537.36"}
    };
    
    // Fetch suggestions with the custom headers
    std::string response = performHttpRequest(apiUrl, headers);
    
    if (response.empty()) {
        DBM(@"Google API returned empty response");
        return suggestions;
    }
    
    DBM(@"Google API response length: %lu", (unsigned long)response.length());
    
    // Check if the response starts with ")]}'," which is a common prefix in Google's XSSI-protected responses
    if (response.substr(0, 5) == ")]}'," || response.substr(0, 4) == ")]}'" || response.substr(0, 2) == ")]") {
        // Find the first '[' character after the prefix
        size_t jsonStart = response.find('[');
        if (jsonStart != std::string::npos) {
            response = response.substr(jsonStart);
            DBM(@"Removed XSSI protection prefix, new length: %lu", (unsigned long)response.length());
        } else {
            ERM(@"Could not find JSON start after XSSI prefix");
            // Print a sample of the response for debugging
            std::string sampleResponse = response.substr(0, std::min(size_t(200), response.length()));
            ERM(@"Response sample: %@", [NSString stringWithUTF8String:sampleResponse.c_str()]);
            return suggestions;
        }
    }
    
    // Parse JSON response
    NSData* jsonData = [NSData dataWithBytes:response.c_str() length:response.length()];
    NSError* error = nil;
    id jsonObject = [NSJSONSerialization JSONObjectWithData:jsonData options:0 error:&error];
    
    if (error || !jsonObject) {
        ERM(@"Error parsing Google search suggestions JSON: %@", error ? error.localizedDescription : @"Unknown error");
        
        // Print a sample of the response for debugging
        std::string sampleResponse = response.substr(0, std::min(size_t(200), response.length()));
        ERM(@"Response sample: %@", [NSString stringWithUTF8String:sampleResponse.c_str()]);
        return suggestions;
    }
    
    // The Google complete response format is a nested array structure
    // Format: [[["suggestion1", ...], ["suggestion2", ...], ...], {...}]
    if ([jsonObject isKindOfClass:[NSArray class]]) {
        NSArray* outerArray = (NSArray*)jsonObject;
        
        // Check if the first element is an array (contains the suggestions)
        if (outerArray.count > 0 && [[outerArray objectAtIndex:0] isKindOfClass:[NSArray class]]) {
            NSArray* suggestionsArray = [outerArray objectAtIndex:0];
            DBM(@"Google suggestions count: %lu", (unsigned long)suggestionsArray.count);
            
            int count = 0;
            
            for (id suggestionItem in suggestionsArray) {
                if (![suggestionItem isKindOfClass:[NSArray class]]) {
                    continue;
                }
                
                NSArray* suggestionData = (NSArray*)suggestionItem;
                if (suggestionData.count == 0 || ![suggestionData[0] isKindOfClass:[NSString class]]) {
                    continue;
                }
                
                // The first element in each suggestion array is the suggestion text
                NSString* suggestionStr = suggestionData[0];
                
                // Remove HTML tags if present (like <b></b>)
                NSString* cleanedStr = [suggestionStr stringByReplacingOccurrencesOfString:@"<b>" withString:@""];
                cleanedStr = [cleanedStr stringByReplacingOccurrencesOfString:@"</b>" withString:@""];
                
                std::string suggestionText = [cleanedStr UTF8String];
                
                // Check for image URL in the suggestion data
                // Google may include image URLs in the response data
                std::string imageUrl = "";
                if (suggestionData.count > 3 && [suggestionData[3] isKindOfClass:[NSDictionary class]]) {
                    NSDictionary* extraData = suggestionData[3];
                    if ([extraData objectForKey:@"i"] && [[extraData objectForKey:@"i"] isKindOfClass:[NSString class]]) {
                        NSString* imageUrlStr = [extraData objectForKey:@"i"];
                        imageUrl = [imageUrlStr UTF8String];
                        DBM(@"Found Google image URL: %@", [NSString stringWithUTF8String:imageUrl.c_str()]);
                    }
                }
                
                // Check if this looks like a website query
                bool isWebsiteQuery = ([suggestionStr containsString:@".com"] || 
                                     [suggestionStr containsString:@".org"] || 
                                     [suggestionStr containsString:@".net"] ||
                                     [suggestionStr containsString:@".io"] ||
                                     [suggestionStr containsString:@".app"] ||
                                     [suggestionStr containsString:@"http"]) && ![suggestionStr containsString:@" "];
                
                std::string url;
                std::string domain;
                
                if (isWebsiteQuery) {
                    // Ensure it has http:// prefix if not already present
                    url = suggestionText;
                    if (url.find("http") != 0) {
                        url = "https://" + url;
                    }
                    domain = extractDomain(url);
                } else {
                    // For non-website queries, use a search URL
                    url = "https://www.google.com/search?q=" + suggestionText;
                    domain = "google.com";
                }
                
                // Create a website entry with image URL if available
                WebsiteEntry entry(url, suggestionText, domain, 0, time(nullptr), true, imageUrl);
                suggestions.push_back(entry);
                
                count++;
                if (count >= maxResults) {
                    break;
                }
            }
        } else {
            ERM(@"Google API response doesn't have the expected nested array format");
        }
    } else {
        ERM(@"Unexpected JSON response type: not an array");
    }
    
    return suggestions;
}

// Fetch search engine suggestions from Bing
std::vector<WebsiteEntry> BrowserHistoryImporter::fetchBingSuggestions(const std::string& query, int maxResults) {
    std::vector<WebsiteEntry> suggestions;
    
    if (query.empty() || query.length() < 2) {
        return suggestions;
    }
    
    // URL encode the query
    NSString* nsQuery = [NSString stringWithUTF8String:query.c_str()];
    NSString* encodedQuery = [nsQuery stringByAddingPercentEncodingWithAllowedCharacters:[NSCharacterSet URLQueryAllowedCharacterSet]];
    
    // Construct the Bing suggestion API URL
    std::string apiUrl = "https://www.bing.com/AS/Suggestions?pt=page.home&mkt=en-vn&cp=3&csr=1&msbqf=false&pths=1&cvid=66EEB8816E66482A9E21062B28333D75&qry=";
    apiUrl += [encodedQuery UTF8String];
    
    DBM(@"Fetching Bing suggestions for query: %@", nsQuery);
    DBM(@"Bing API URL: %@", [NSString stringWithUTF8String:apiUrl.c_str()]);
    
    // Set up headers for Bing API
    std::map<std::string, std::string> headers = {
        {"accept", "*/*"},
        {"accept-language", "en-US,en;q=0.9"},
        {"user-agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/134.0.0.0 Safari/537.36"}
    };
    
    // Fetch suggestions with the custom headers
    std::string response = performHttpRequest(apiUrl, headers);
    
    if (response.empty()) {
        DBM(@"Bing API returned empty response");
        return suggestions;
    }
    
    DBM(@"Bing API response length: %lu", (unsigned long)response.length());
    
    // Parse JSON response
    NSData* jsonData = [NSData dataWithBytes:response.c_str() length:response.length()];
    NSError* error = nil;
    id jsonObject = [NSJSONSerialization JSONObjectWithData:jsonData options:0 error:&error];
    
    if (error || !jsonObject) {
        ERM(@"Error parsing Bing search suggestions JSON: %@", error ? error.localizedDescription : @"Unknown error");
        
        // Print a sample of the response for debugging
        std::string sampleResponse = response.substr(0, std::min(size_t(200), response.length()));
        ERM(@"Response sample: %@", [NSString stringWithUTF8String:sampleResponse.c_str()]);
        return suggestions;
    }
    
    // The Bing response format is a JSON object with an "s" array containing suggestion objects
    if ([jsonObject isKindOfClass:[NSDictionary class]]) {
        NSDictionary* jsonDict = (NSDictionary*)jsonObject;
        
        // Check if the "s" key exists and is an array
        if ([jsonDict objectForKey:@"s"] && [[jsonDict objectForKey:@"s"] isKindOfClass:[NSArray class]]) {
            NSArray* suggestionsArray = [jsonDict objectForKey:@"s"];
            DBM(@"Bing suggestions count: %lu", (unsigned long)suggestionsArray.count);
            
            int count = 0;
            
            for (id suggestionItem in suggestionsArray) {
                if (![suggestionItem isKindOfClass:[NSDictionary class]]) {
                    continue;
                }
                
                NSDictionary* suggestionDict = (NSDictionary*)suggestionItem;
                
                // Get the suggestion text from the "q" field
                if (![suggestionDict objectForKey:@"q"] || ![[suggestionDict objectForKey:@"q"] isKindOfClass:[NSString class]]) {
                    continue;
                }
                
                NSString* suggestionStr = [suggestionDict objectForKey:@"q"];
                std::string suggestionText = [suggestionStr UTF8String];
                
                // Clean the suggestion text to remove special characters
                std::string cleanedSuggestionText = cleanSuggestionText(suggestionText);
                
                // Check for image URL in the ext.im field
                std::string imageUrl = "";
                if ([suggestionDict objectForKey:@"ext"] && 
                    [[suggestionDict objectForKey:@"ext"] isKindOfClass:[NSDictionary class]]) {
                    NSDictionary* extDict = [suggestionDict objectForKey:@"ext"];
                    
                    // Check for image URL in the "im" field
                    if ([extDict objectForKey:@"im"] && 
                        [[extDict objectForKey:@"im"] isKindOfClass:[NSString class]]) {
                        NSString* imageUrlStr = [extDict objectForKey:@"im"];
                        std::string relativeImageUrl = [imageUrlStr UTF8String];
                        
                        // Convert relative URL to absolute URL if needed
                        if (relativeImageUrl.substr(0, 1) == "/") {
                            imageUrl = "https://www.bing.com" + relativeImageUrl;
                        } else {
                            imageUrl = relativeImageUrl;
                        }
                        
                        DBM(@"Found Bing image URL: %@", [NSString stringWithUTF8String:imageUrl.c_str()]);
                    }
                }
                
                // Get additional information if available
                NSString* description = @"";
                if ([suggestionDict objectForKey:@"ext"] && 
                    [[suggestionDict objectForKey:@"ext"] isKindOfClass:[NSDictionary class]]) {
                    NSDictionary* extDict = [suggestionDict objectForKey:@"ext"];
                    if ([extDict objectForKey:@"des"] && 
                        [[extDict objectForKey:@"des"] isKindOfClass:[NSString class]]) {
                        description = [extDict objectForKey:@"des"];
                    }
                }
                
                // Check if this looks like a website query
                bool isWebsiteQuery = ([suggestionStr containsString:@".com"] || 
                                     [suggestionStr containsString:@".org"] || 
                                     [suggestionStr containsString:@".net"] ||
                                     [suggestionStr containsString:@".io"] ||
                                     [suggestionStr containsString:@".app"] ||
                                     [suggestionStr containsString:@"http"]) && ![suggestionStr containsString:@" "];
                
                std::string url;
                std::string domain;
                std::string title = cleanedSuggestionText;
                
                // If there's a description, add it to the title
                if ([description length] > 0) {
                    std::string cleanedDescription = cleanSuggestionText([description UTF8String]);
                    title += " - " + cleanedDescription;
                }
                
                if (isWebsiteQuery) {
                    // Ensure it has http:// prefix if not already present
                    url = cleanedSuggestionText;
                    if (url.find("http") != 0) {
                        url = "https://" + url;
                    }
                    domain = extractDomain(url);
                } else {
                    // For non-website queries, use a search URL
                    url = "https://www.bing.com/search?q=" + cleanedSuggestionText;
                    domain = "bing.com";
                }
                
                // Create a website entry with image URL if available
                WebsiteEntry entry(url, title, domain, 0, time(nullptr), true, imageUrl);
                suggestions.push_back(entry);
                
                count++;
                if (count >= maxResults) {
                    break;
                }
            }
        } else {
            ERM(@"Bing API response doesn't have the expected 's' array");
            
            // Print the keys in the dictionary for debugging
            std::string keysStr = "Dictionary keys: ";
            for (NSString* key in [jsonDict allKeys]) {
                keysStr += std::string([key UTF8String]) + " ";
            }
            DBM(@"%@", [NSString stringWithUTF8String:keysStr.c_str()]);
        }
    } else {
        ERM(@"Unexpected JSON response type from Bing: not a dictionary");
    }
    
    return suggestions;
}

// Fetch search suggestions from multiple engines in parallel
std::vector<WebsiteEntry> BrowserHistoryImporter::fetchSearchSuggestions(const std::string& query, int maxResults) {
    std::vector<WebsiteEntry> allSuggestions;
    
    if (query.empty() || query.length() < 2) {
        return allSuggestions;
    }
    
    NSString* nsQuery = [NSString stringWithUTF8String:query.c_str()];
    DBM(@"Fetching search suggestions for query: %@", nsQuery);
    
    // Make a copy of the query for the async calls
    std::string queryCopy = query;
    
    // Launch parallel requests to Google and Bing
    auto googleFuture = std::async(std::launch::async, [this, queryCopy, maxResults]() {
        return this->fetchGoogleSuggestions(queryCopy, maxResults);
    });
    
    auto bingFuture = std::async(std::launch::async, [this, queryCopy, maxResults]() {
        return this->fetchBingSuggestions(queryCopy, maxResults);
    });
    
    // Get results from both futures
    std::vector<WebsiteEntry> googleSuggestions;
    std::vector<WebsiteEntry> bingSuggestions;
    
    try {
        // Wait for both futures with a timeout
        DBM(@"Waiting for Google suggestions...");
        auto status = googleFuture.wait_for(std::chrono::seconds(2));
        if (status == std::future_status::ready) {
            googleSuggestions = googleFuture.get();
            DBM(@"Received %lu Google suggestions", (unsigned long)googleSuggestions.size());
        } else {
            DBM(@"Google suggestions timed out");
        }
    } catch (const std::exception& e) {
        ERM(@"Error getting Google suggestions: %@", @(e.what()));
    }
    
    try {
        DBM(@"Waiting for Bing suggestions...");
        auto status = bingFuture.wait_for(std::chrono::seconds(2));
        if (status == std::future_status::ready) {
            bingSuggestions = bingFuture.get();
            DBM(@"Received %lu Bing suggestions", (unsigned long)bingSuggestions.size());
        } else {
            DBM(@"Bing suggestions timed out");
        }
    } catch (const std::exception& e) {
        ERM(@"Error getting Bing suggestions: %@", @(e.what()));
    }
    
    // Merge results, alternating between Google and Bing
    std::unordered_map<std::string, bool> seenSuggestions;
    
    for (int i = 0; i < maxResults * 2; i++) {
        const std::vector<WebsiteEntry>& currentSource = (i % 2 == 0) ? googleSuggestions : bingSuggestions;
        int sourceIndex = i / 2;
        
        if (sourceIndex < static_cast<int>(currentSource.size())) {
            const WebsiteEntry& entry = currentSource[sourceIndex];
            
            // Check if we've already seen this suggestion text
            if (seenSuggestions.find(entry.title) == seenSuggestions.end()) {
                seenSuggestions[entry.title] = true;
                allSuggestions.push_back(entry);
                
                NSString* entryTitle = [NSString stringWithUTF8String:entry.title.c_str()];
                DBM(@"Added suggestion: %@", entryTitle);
                
                if (allSuggestions.size() >= static_cast<size_t>(maxResults * 2)) {
                    break;
                }
            } else {
                NSString* entryTitle = [NSString stringWithUTF8String:entry.title.c_str()];
                DBM(@"Skipped duplicate suggestion: %@", entryTitle);
            }
        }
    }
    
    DBM(@"Total combined suggestions: %lu", (unsigned long)allSuggestions.size());
    return allSuggestions;
}

int BrowserHistoryImporter::importAllBrowserHistory() {
    std::lock_guard<std::mutex> lock(mutex);
    
    if (initialized) {
        // Already initialized, clear existing data
        websiteEntries.clear();
        urlToIndexMap.clear();
    }
    
    // Check if permissions have been explicitly requested via settings
    if (g_permissionRequested) {
        // Permission has been explicitly requested, proceed with import
        return continueImportAllBrowserHistory();
    } else {
        // Permission has not been requested, just mark as initialized without showing dialog
        // User can request permissions via settings if needed
        initialized = true;
        WRM(@"Browser history import skipped (permissions not requested)");
        return 0;
    }
}

int BrowserHistoryImporter::continueImportAllBrowserHistory() {
    int totalImported = 0;
    
    // Import from all browsers
    totalImported += importSafariHistory();
    totalImported += importChromeHistory();
    totalImported += importFirefoxHistory();
    
    // Sort entries by visit count (descending)
    std::sort(websiteEntries.begin(), websiteEntries.end(), 
            [](const WebsiteEntry& a, const WebsiteEntry& b) {
                return a.visitCount > b.visitCount;
            });
    
    initialized = true;
    
    DBM(@"Imported %lu website entries from browser history", (unsigned long)websiteEntries.size());
    return totalImported;
}

int BrowserHistoryImporter::importSafariHistory() {
    int importCount = 0;
    
    // Safari history is stored in ~/Library/Safari/History.db
    NSString* homeDir = NSHomeDirectory();
    NSString* safariHistoryPath = [homeDir stringByAppendingPathComponent:@"Library/Safari/History.db"];
    
    // Check if the file exists
    if (![[NSFileManager defaultManager] fileExistsAtPath:safariHistoryPath]) {
        DBM(@"Safari history database not found at: %@", safariHistoryPath);
        return 0;
    }
    
    // Request permission to access Safari history (UI must be on main thread)
    __block NSModalResponse response = NSAlertSecondButtonReturn; // default to Cancel

    // Block that shows the alert and returns user's choice
    NSModalResponse (^showAlert)(void) = ^NSModalResponse {
        NSAlert *alert = [[NSAlert alloc] init];
        [alert setMessageText:@"Permission Required"];
        [alert setInformativeText:@"This app needs permission to access your Safari browsing history to provide website search functionality. Please grant access in the following dialog."];
        [alert addButtonWithTitle:@"Continue"];
        [alert addButtonWithTitle:@"Cancel"];
        return [alert runModal];
    };

    if ([NSThread isMainThread]) {
        response = showAlert();
    } else {
        // Execute synchronously on main thread so the calling code can use the response.
        dispatch_sync(dispatch_get_main_queue(), ^{
            response = showAlert();
        });
    }

    if (response != NSAlertFirstButtonReturn) {
        DBM(@"User declined Safari history access permission");
        return 0;
    }
    
    // Try to open the database with read-only permissions
    sqlite3* db;
    int rc = sqlite3_open_v2([safariHistoryPath UTF8String], &db, SQLITE_OPEN_READONLY, nullptr);
    
    if (rc != SQLITE_OK) {
        ERM(@"Cannot open Safari history database: %@", @(sqlite3_errmsg(db)));
        
        // If access was denied, try to guide the user to grant permission
        if (std::string(sqlite3_errmsg(db)).find("authorization") != std::string::npos) {
            dispatch_async(dispatch_get_main_queue(), ^{
                NSAlert *permissionAlert = [[NSAlert alloc] init];
                [permissionAlert setMessageText:@"Permission Denied"];
                [permissionAlert setInformativeText:@"Access to Safari history was denied. Please grant Full Disk Access permission to this application in System Preferences > Security & Privacy > Privacy > Full Disk Access."];
                [permissionAlert addButtonWithTitle:@"Open Privacy Settings"];
                [permissionAlert addButtonWithTitle:@"OK"];
                
                NSModalResponse permResponse = [permissionAlert runModal];
                if (permResponse == NSAlertFirstButtonReturn) {
                    // Open the Privacy settings
                    NSURL *privacyURL = [NSURL URLWithString:@"x-apple.systempreferences:com.apple.preference.security?Privacy_AllFiles"];
                    [[NSWorkspace sharedWorkspace] openURL:privacyURL];
                }
            });
        }
        
        sqlite3_close(db);
        return 0;
    }
    
    // Query to get history entries with visit counts
    const char* query = "SELECT history_items.url, '' as title, "
                        "history_items.visit_count as visit_count, "
                        "MAX(history_visits.visit_time) as last_visit "
                        "FROM history_items "
                        "JOIN history_visits ON history_items.id = history_visits.history_item "
                        "GROUP BY history_items.id "
                        "ORDER BY visit_count DESC";
    
    sqlite3_stmt* stmt;
    rc = sqlite3_prepare_v2(db, query, -1, &stmt, nullptr);
    
    if (rc != SQLITE_OK) {
        ERM(@"Failed to prepare Safari history query: %@", @(sqlite3_errmsg(db)));
        sqlite3_close(db);
        return 0;
    }
    
    // Process results
    while (sqlite3_step(stmt) == SQLITE_ROW) {
        const char* url = reinterpret_cast<const char*>(sqlite3_column_text(stmt, 0));
        const char* title = reinterpret_cast<const char*>(sqlite3_column_text(stmt, 1));
        int visitCount = sqlite3_column_int(stmt, 2);
        double lastVisit = sqlite3_column_double(stmt, 3);
        
        if (url && title) {
            std::string urlStr(url);
            std::string titleStr(title ? title : "");
            std::string domain = extractDomain(urlStr);
            
            // Create and add entry
            WebsiteEntry entry(urlStr, titleStr, domain, visitCount, lastVisit);
            addWebsiteEntry(entry);
            importCount++;
        }
    }
    
    sqlite3_finalize(stmt);
    sqlite3_close(db);
    
    DBM(@"Imported %d entries from Safari history", importCount);
    return importCount;
}

int BrowserHistoryImporter::importChromeHistory() {
    int importCount = 0;
    
    // Chrome history is stored in ~/Library/Application Support/Google/Chrome/Default/History
    NSString* homeDir = NSHomeDirectory();
    NSString* chromeHistoryPath = [homeDir stringByAppendingPathComponent:
                                  @"Library/Application Support/Google/Chrome/Default/History"];
    
    // Check if the file exists
    if (![[NSFileManager defaultManager] fileExistsAtPath:chromeHistoryPath]) {
        DBM(@"Chrome history database not found at: %@", chromeHistoryPath);
        return 0;
    }
    
    // Create a temporary copy of the database (Chrome may lock the original)
    NSString* tempDbPath = [NSTemporaryDirectory() stringByAppendingPathComponent:@"chrome_history_temp.db"];
    NSError* error = nil;
    
    [[NSFileManager defaultManager] removeItemAtPath:tempDbPath error:nil]; // Remove if exists
    [[NSFileManager defaultManager] copyItemAtPath:chromeHistoryPath toPath:tempDbPath error:&error];
    
    if (error) {
        ERM(@"Failed to copy Chrome history database: %@", error.localizedDescription);
        
        // If access was denied, guide the user to grant permission
        if ([error.domain isEqualToString:NSCocoaErrorDomain] && 
            (error.code == NSFileReadNoPermissionError || error.code == NSFileReadUnknownError)) {
            dispatch_async(dispatch_get_main_queue(), ^{
                NSAlert *permissionAlert = [[NSAlert alloc] init];
                [permissionAlert setMessageText:@"Permission Denied"];
                [permissionAlert setInformativeText:@"Access to Chrome history was denied. Please grant Full Disk Access permission to this application in System Preferences > Security & Privacy > Privacy > Full Disk Access."];
                [permissionAlert addButtonWithTitle:@"Open Privacy Settings"];
                [permissionAlert addButtonWithTitle:@"OK"];
                
                NSModalResponse permResponse = [permissionAlert runModal];
                if (permResponse == NSAlertFirstButtonReturn) {
                    // Open the Privacy settings
                    NSURL *privacyURL = [NSURL URLWithString:@"x-apple.systempreferences:com.apple.preference.security?Privacy_AllFiles"];
                    [[NSWorkspace sharedWorkspace] openURL:privacyURL];
                }
            });
        }
        
        return 0;
    }
    
    sqlite3* db;
    int rc = sqlite3_open_v2([tempDbPath UTF8String], &db, SQLITE_OPEN_READONLY, nullptr);
    
    if (rc != SQLITE_OK) {
        ERM(@"Cannot open Chrome history database: %@", @(sqlite3_errmsg(db)));
        sqlite3_close(db);
        return 0;
    }
    
    // Query to get history entries with visit counts
    const char* query = "SELECT urls.url, urls.title, urls.visit_count, "
                        "urls.last_visit_time FROM urls "
                        "ORDER BY urls.visit_count DESC";
    
    sqlite3_stmt* stmt;
    rc = sqlite3_prepare_v2(db, query, -1, &stmt, nullptr);
    
    if (rc != SQLITE_OK) {
        ERM(@"Failed to prepare Chrome history query: %@", @(sqlite3_errmsg(db)));
        sqlite3_close(db);
        return 0;
    }
    
    // Process results
    while (sqlite3_step(stmt) == SQLITE_ROW) {
        const char* url = reinterpret_cast<const char*>(sqlite3_column_text(stmt, 0));
        const char* title = reinterpret_cast<const char*>(sqlite3_column_text(stmt, 1));
        int visitCount = sqlite3_column_int(stmt, 2);
        double lastVisit = sqlite3_column_int64(stmt, 3) / 1000000.0 - 11644473600.0; // Convert Windows time to Unix time
        
        if (url) {
            std::string urlStr(url);
            std::string titleStr(title ? title : "");
            std::string domain = extractDomain(urlStr);
            
            // Create and add entry
            WebsiteEntry entry(urlStr, titleStr, domain, visitCount, lastVisit);
            addWebsiteEntry(entry);
            importCount++;
        }
    }
    
    sqlite3_finalize(stmt);
    sqlite3_close(db);
    
    // Clean up temporary file
    [[NSFileManager defaultManager] removeItemAtPath:tempDbPath error:nil];
    
    DBM(@"Imported %d entries from Chrome history", importCount);
    return importCount;
}

int BrowserHistoryImporter::importFirefoxHistory() {
    int importCount = 0;
    
    // Find Firefox profiles directory
    NSString* homeDir = NSHomeDirectory();
    NSString* firefoxProfilesDir = [homeDir stringByAppendingPathComponent:
                                   @"Library/Application Support/Firefox/Profiles"];
    
    // Check if the directory exists
    if (![[NSFileManager defaultManager] fileExistsAtPath:firefoxProfilesDir]) {
        DBM(@"Firefox profiles directory not found at: %@", firefoxProfilesDir);
        return 0;
    }
    
    // Find profile directories
    NSError* error = nil;
    NSArray* profileDirs = [[NSFileManager defaultManager] contentsOfDirectoryAtPath:firefoxProfilesDir 
                                                                               error:&error];
    
    if (error) {
        ERM(@"Failed to read Firefox profiles directory: %@", error.localizedDescription);
        
        // If access was denied, guide the user to grant permission
        if ([error.domain isEqualToString:NSCocoaErrorDomain] && 
            (error.code == NSFileReadNoPermissionError || error.code == NSFileReadUnknownError)) {
            dispatch_async(dispatch_get_main_queue(), ^{
                NSAlert *permissionAlert = [[NSAlert alloc] init];
                [permissionAlert setMessageText:@"Permission Denied"];
                [permissionAlert setInformativeText:@"Access to Firefox history was denied. Please grant Full Disk Access permission to this application in System Preferences > Security & Privacy > Privacy > Full Disk Access."];
                [permissionAlert addButtonWithTitle:@"Open Privacy Settings"];
                [permissionAlert addButtonWithTitle:@"OK"];
                
                NSModalResponse permResponse = [permissionAlert runModal];
                if (permResponse == NSAlertFirstButtonReturn) {
                    // Open the Privacy settings
                    NSURL *privacyURL = [NSURL URLWithString:@"x-apple.systempreferences:com.apple.preference.security?Privacy_AllFiles"];
                    [[NSWorkspace sharedWorkspace] openURL:privacyURL];
                }
            });
        }
        
        return 0;
    }
    
    // Process each profile
    for (NSString* profileDir in profileDirs) {
        // Skip hidden files
        if ([profileDir hasPrefix:@"."]) {
            continue;
        }
        
        NSString* placesDbPath = [[firefoxProfilesDir stringByAppendingPathComponent:profileDir] 
                                 stringByAppendingPathComponent:@"places.sqlite"];
        
        // Check if places.sqlite exists
        if (![[NSFileManager defaultManager] fileExistsAtPath:placesDbPath]) {
            continue;
        }
        
        // Create a temporary copy of the database (Firefox may lock the original)
        NSString* tempDbPath = [NSTemporaryDirectory() stringByAppendingPathComponent:@"firefox_places_temp.db"];
        NSError* copyError = nil;
        
        [[NSFileManager defaultManager] removeItemAtPath:tempDbPath error:nil]; // Remove if exists
        [[NSFileManager defaultManager] copyItemAtPath:placesDbPath toPath:tempDbPath error:&copyError];
        
        if (copyError) {
            ERM(@"Failed to copy Firefox places database: %@", copyError.localizedDescription);
            
            // If access was denied, guide the user to grant permission
            if ([copyError.domain isEqualToString:NSCocoaErrorDomain] && 
                (copyError.code == NSFileReadNoPermissionError || copyError.code == NSFileReadUnknownError)) {
                dispatch_async(dispatch_get_main_queue(), ^{
                    NSAlert *permissionAlert = [[NSAlert alloc] init];
                    [permissionAlert setMessageText:@"Permission Denied"];
                    [permissionAlert setInformativeText:@"Access to Firefox history was denied. Please grant Full Disk Access permission to this application in System Preferences > Security & Privacy > Privacy > Full Disk Access."];
                    [permissionAlert addButtonWithTitle:@"Open Privacy Settings"];
                    [permissionAlert addButtonWithTitle:@"OK"];
                    
                    NSModalResponse permResponse = [permissionAlert runModal];
                    if (permResponse == NSAlertFirstButtonReturn) {
                        // Open the Privacy settings
                        NSURL *privacyURL = [NSURL URLWithString:@"x-apple.systempreferences:com.apple.preference.security?Privacy_AllFiles"];
                        [[NSWorkspace sharedWorkspace] openURL:privacyURL];
                    }
                });
            }
            
            continue;
        }
        
        sqlite3* db;
        int rc = sqlite3_open_v2([tempDbPath UTF8String], &db, SQLITE_OPEN_READONLY, nullptr);
        
        if (rc != SQLITE_OK) {
            ERM(@"Cannot open Firefox places database: %@", @(sqlite3_errmsg(db)));
            sqlite3_close(db);
            continue;
        }
        
        // Query to get history entries with visit counts
        const char* query = "SELECT moz_places.url, moz_places.title, "
                            "moz_places.visit_count, moz_places.last_visit_date/1000000 "
                            "FROM moz_places "
                            "WHERE moz_places.visit_count > 0 "
                            "ORDER BY moz_places.visit_count DESC";
        
        sqlite3_stmt* stmt;
        rc = sqlite3_prepare_v2(db, query, -1, &stmt, nullptr);
        
        if (rc != SQLITE_OK) {
            ERM(@"Failed to prepare Firefox history query: %@", @(sqlite3_errmsg(db)));
            sqlite3_close(db);
            continue;
        }
        
        // Process results
        while (sqlite3_step(stmt) == SQLITE_ROW) {
            const char* url = reinterpret_cast<const char*>(sqlite3_column_text(stmt, 0));
            const char* title = reinterpret_cast<const char*>(sqlite3_column_text(stmt, 1));
            int visitCount = sqlite3_column_int(stmt, 2);
            double lastVisit = sqlite3_column_double(stmt, 3);
            
            if (url) {
                std::string urlStr(url);
                std::string titleStr(title ? title : "");
                std::string domain = extractDomain(urlStr);
                
                // Create and add entry
                WebsiteEntry entry(urlStr, titleStr, domain, visitCount, lastVisit);
                addWebsiteEntry(entry);
                importCount++;
            }
        }
        
        sqlite3_finalize(stmt);
        sqlite3_close(db);
        
        // Clean up temporary file
        [[NSFileManager defaultManager] removeItemAtPath:tempDbPath error:nil];
    }
    
    DBM(@"Imported %d entries from Firefox history", importCount);
    return importCount;
}

void BrowserHistoryImporter::addWebsiteEntry(const WebsiteEntry& entry) {
    // Check if URL already exists
    auto it = urlToIndexMap.find(entry.url);
    if (it != urlToIndexMap.end()) {
        // Update existing entry
        WebsiteEntry& existingEntry = websiteEntries[it->second];
        existingEntry.visitCount += entry.visitCount;
        existingEntry.lastVisitTime = std::max(existingEntry.lastVisitTime, entry.lastVisitTime);
        if (existingEntry.title.empty() && !entry.title.empty()) {
            existingEntry.title = entry.title;
        }
    } else {
        // Add new entry
        urlToIndexMap[entry.url] = websiteEntries.size();
        websiteEntries.push_back(entry);
    }
}

double BrowserHistoryImporter::calculateScore(const WebsiteEntry& entry) {
    // Base score from visit count (logarithmic scale to prevent very popular sites from dominating)
    double score = 0.5 * log10(1 + entry.visitCount);
    
    // Recency bonus (higher score for recently visited sites)
    double currentTime = static_cast<double>(time(nullptr));
    double daysSinceLastVisit = (currentTime - entry.lastVisitTime) / (60 * 60 * 24);
    
    // Recency factor (decays over time)
    double recencyFactor = 0;
    if (daysSinceLastVisit < 1) {
        // Visited today
        recencyFactor = 0.5;
    } else if (daysSinceLastVisit < 7) {
        // Visited this week
        recencyFactor = 0.3;
    } else if (daysSinceLastVisit < 30) {
        // Visited this month
        recencyFactor = 0.2;
    } else {
        // Visited more than a month ago
        recencyFactor = 0.1;
    }
    
    score += recencyFactor;
    
    // Cap score at 0.99 to allow for other factors
    return std::min(score, 0.99);
}

std::vector<WebsiteEntry> BrowserHistoryImporter::searchWebsites(
    const std::string& query, int maxResults) {
    
    // Make a copy of the query for the async call
    std::string queryCopy = query;
    
    // Start fetching search suggestions in parallel with local search
    std::future<std::vector<WebsiteEntry>> suggestionsFuture = std::async(
        std::launch::async,
        [this, queryCopy, maxResults]() {
            return this->fetchSearchSuggestions(queryCopy, maxResults / 2);
        }
    );
    
    std::lock_guard<std::mutex> lock(mutex);
    
    // Initialize if needed
    if (!initialized) {
        importAllBrowserHistory();
    }
    
    // Prepare results
    std::vector<std::pair<WebsiteEntry, double>> scoredResults;
    
    // Check if query is a URL or domain
    bool isLikelyURL = query.find('.') != std::string::npos || 
                       query.find("http") != std::string::npos;
    
    // Search through entries
    for (const auto& entry : websiteEntries) {
        double score = 0.0;
        
        // Calculate match score
        if (isLikelyURL) {
            // URL matching
            if (entry.url.find(query) != std::string::npos) {
                score = 0.9; // High score for URL match
            } else if (entry.domain.find(query) != std::string::npos) {
                score = 0.8; // Good score for domain match
            }
        } else {
            // Title matching
            if (!entry.title.empty()) {
                std::string lowerTitle = entry.title;
                std::string lowerQuery = query;
                
                // Convert to lowercase for case-insensitive comparison
                std::transform(lowerTitle.begin(), lowerTitle.end(), lowerTitle.begin(), ::tolower);
                std::transform(lowerQuery.begin(), lowerQuery.end(), lowerQuery.begin(), ::tolower);
                
                if (lowerTitle == lowerQuery) {
                    score = 0.9; // Exact title match
                } else if (lowerTitle.find(lowerQuery) != std::string::npos) {
                    score = 0.7; // Title contains query
                }
            }
            
            // Domain matching (for non-URL queries)
            if (entry.domain.find(query) != std::string::npos) {
                score = std::max(score, 0.6); // Domain contains query
            }
        }
        
        // If we have a match, adjust score based on visit history
        if (score > 0) {
            // Combine match score with history-based score
            double historyScore = calculateScore(entry);
            score = 0.7 * score + 0.3 * historyScore;
            
            scoredResults.push_back({entry, score});
        }
    }
    
    // Sort by score (descending)
    std::sort(scoredResults.begin(), scoredResults.end(), 
              [](const auto& a, const auto& b) {
                  return a.second > b.second;
              });
    
    // Extract top results from history
    std::vector<WebsiteEntry> results;
    for (size_t i = 0; i < std::min(static_cast<size_t>(maxResults), scoredResults.size()); ++i) {
        results.push_back(scoredResults[i].first);
    }
    
    // Get search suggestions (which were being fetched in parallel)
    std::vector<WebsiteEntry> suggestions;
    try {
        auto status = suggestionsFuture.wait_for(std::chrono::seconds(1));
        if (status == std::future_status::ready) {
            suggestions = suggestionsFuture.get();
        }
    } catch (const std::exception& e) {
        ERM(@"Error getting search suggestions: %@", @(e.what()));
    }
    
    // Add suggestions to results, ensuring no duplicates
    std::unordered_map<std::string, bool> seenUrls;
    for (const auto& result : results) {
        seenUrls[result.url] = true;
    }
    
    for (const auto& suggestion : suggestions) {
        if (seenUrls.find(suggestion.url) == seenUrls.end()) {
            results.push_back(suggestion);
            seenUrls[suggestion.url] = true;
            
            if (results.size() >= static_cast<size_t>(maxResults)) {
                break;
            }
        }
    }
    
    return results;
}

size_t BrowserHistoryImporter::getTotalEntries() const {
    std::lock_guard<std::mutex> lock(mutex);
    return websiteEntries.size();
}

std::vector<WebsiteEntry> BrowserHistoryImporter::getTopEntries(size_t maxCount) const {
    std::lock_guard<std::mutex> lock(mutex);
    std::vector<WebsiteEntry> sorted = websiteEntries;
    std::sort(sorted.begin(), sorted.end(), [](const WebsiteEntry& a, const WebsiteEntry& b) {
        return a.lastVisitTime > b.lastVisitTime;
    });
    if (sorted.size() > maxCount) {
        sorted.resize(maxCount);
    }
    return sorted;
}

void BrowserHistoryImporter::clearHistory() {
    std::lock_guard<std::mutex> lock(mutex);
    websiteEntries.clear();
    urlToIndexMap.clear();
    initialized = false;
}

} // namespace ui
} // namespace launcher 