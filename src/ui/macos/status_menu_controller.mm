#include "status_menu_controller.h"

#import <Cocoa/Cocoa.h>

@implementation StatusMenuController {
    __weak id _weakTarget; // AppDelegate or other receiver
    NSMenuItem *_launchAtLoginItem; // needs dynamic state updates
}

- (instancetype)initWithTarget:(id)target {
    self = [super init];
    if (!self) { return nil; }

    _weakTarget = target;

    // Create the status-bar item
    _statusItem = [[NSStatusBar systemStatusBar] statusItemWithLength:NSVariableStatusItemLength];

    // Attach menu (built below)
    _statusItem.menu = [self buildMenuWithTarget:target];

    // Ensure clicking on icon shows the menu (for users that expect that)
    _statusItem.button.action = @selector(statusItemClicked:);
    _statusItem.button.target = target;

    return self;
}

#pragma mark - Public helpers

- (void)updateLaunchAtLoginState:(BOOL)isEnabled {
    if (_launchAtLoginItem) {
        _launchAtLoginItem.state = isEnabled ? NSControlStateValueOn : NSControlStateValueOff;
    }
}

#pragma mark - Internal menu construction

- (NSMenu *)buildMenuWithTarget:(id)target {
    NSMenu *menu = [[NSMenu alloc] init];

    // About
    NSMenuItem *about = [[NSMenuItem alloc] initWithTitle:@"About MicroLauncher"
                                                   action:@selector(showAbout:)
                                            keyEquivalent:@""];
    about.target = target;
    [menu addItem:about];

    [menu addItem:[NSMenuItem separatorItem]];

    // Launcher
    NSMenuItem *launcher = [[NSMenuItem alloc] initWithTitle:@"Launcher"
                                                      action:@selector(showLauncherBar:)
                                               keyEquivalent:@"f"];
    launcher.target = target;
    launcher.keyEquivalentModifierMask = NSEventModifierFlagCommand;
    [menu addItem:launcher];

    // New Chat
    NSMenuItem *chat = [[NSMenuItem alloc] initWithTitle:@"New Chat"
                                                  action:@selector(showChat:)
                                           keyEquivalent:@"n"];
    chat.target = target;
    chat.keyEquivalentModifierMask = NSEventModifierFlagCommand;
    [menu addItem:chat];

    // Preferences
    NSMenuItem *prefs = [[NSMenuItem alloc] initWithTitle:@"Preferences…"
                                                   action:@selector(showSettings:)
                                            keyEquivalent:@","];
    prefs.target = target;
    prefs.keyEquivalentModifierMask = NSEventModifierFlagCommand;
    [menu addItem:prefs];

    [menu addItem:[NSMenuItem separatorItem]];

    // Launch at login toggle (state set externally)
    _launchAtLoginItem = [[NSMenuItem alloc] initWithTitle:@"Launch at Login"
                                                    action:@selector(toggleLaunchAtLogin:)
                                             keyEquivalent:@""];
    _launchAtLoginItem.target = target;
    [menu addItem:_launchAtLoginItem];

    // Check for updates
    NSMenuItem *updates = [[NSMenuItem alloc] initWithTitle:@"Check for Updates…"
                                                     action:@selector(checkForUpdates:)
                                              keyEquivalent:@""];
    updates.target = target;
    [menu addItem:updates];

    [menu addItem:[NSMenuItem separatorItem]];

    // Services submenu (macOS integrates it automatically)
    NSMenuItem *servicesRoot = [[NSMenuItem alloc] initWithTitle:@"Services"
                                                         action:nil
                                                  keyEquivalent:@""];
    NSMenu *servicesMenu = [[NSMenu alloc] initWithTitle:@"Services"];
    servicesRoot.submenu = servicesMenu;
    [menu addItem:servicesRoot];
    [NSApp setServicesMenu:servicesMenu];

    [menu addItem:[NSMenuItem separatorItem]];

    // Hide / Quit
    NSMenuItem *hide = [[NSMenuItem alloc] initWithTitle:@"Hide MicroLauncher"
                                                  action:@selector(hide:)
                                           keyEquivalent:@"h"];
    hide.target = nil;
    hide.keyEquivalentModifierMask = NSEventModifierFlagCommand;
    [menu addItem:hide];

    NSMenuItem *hideOthers = [[NSMenuItem alloc] initWithTitle:@"Hide Others"
                                                        action:@selector(hideOtherApplications:)
                                                 keyEquivalent:@"h"];
    hideOthers.target = nil;
    hideOthers.keyEquivalentModifierMask = NSEventModifierFlagCommand | NSEventModifierFlagOption;
    [menu addItem:hideOthers];

    NSMenuItem *showAll = [[NSMenuItem alloc] initWithTitle:@"Show All"
                                                     action:@selector(unhideAllApplications:)
                                              keyEquivalent:@""];
    showAll.target = nil;
    [menu addItem:showAll];

    [menu addItem:[NSMenuItem separatorItem]];

    NSMenuItem *quit = [[NSMenuItem alloc] initWithTitle:@"Quit"
                                                  action:@selector(terminate:)
                                           keyEquivalent:@"q"];
    quit.target = nil;
    quit.keyEquivalentModifierMask = NSEventModifierFlagCommand;
    [menu addItem:quit];

    return menu;
}

@end 