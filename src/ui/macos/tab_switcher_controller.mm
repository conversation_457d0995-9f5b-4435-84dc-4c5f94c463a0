// tab_switcher_controller.mm
//------------------------------------------------------------------------------
// Implementation of modern Control-Tab switcher overlay.
//------------------------------------------------------------------------------

#ifdef __APPLE__

#import "tab_switcher_controller.h"

#import <Carbon/Carbon.h> // For key codes like kVK_Tab
#import "../chat/tabs/tab_group_manager.h"
#import "../common/icon_utils.h" // New utility for tinted symbols
#include "../../core/util/debug.h"
#import "hud_window_utils.h"

// Convenience macro for main-thread enforcement.
#define ENSURE_MAIN_THREAD()                                                                        \
    if (![NSThread isMainThread]) {                                                                 \
        dispatch_sync(dispatch_get_main_queue(), ^{                                                 \
            [self _ensureMainThreadImpl:NSStringFromSelector(_cmd)];                                \
        });                                                                                        \
        return;                                                                                    \
    }

@interface TabSwitcherItemView : NSView
@property (nonatomic, strong) NSImageView *iconView;
@property (nonatomic, strong) NSTextField *titleField;
@end

@implementation TabSwitcherItemView
- (instancetype)initWithIcon:(NSImage *)icon title:(NSString *)title maximumWidth:(CGFloat)maxWidth {
    self = [super initWithFrame:NSZeroRect];
    if (self) {
        // Configure icon
        _iconView = [[NSImageView alloc] initWithFrame:NSMakeRect(0, 0, 20, 20)];
        [_iconView setImageScaling:NSImageScaleProportionallyUpOrDown];
        _iconView.image = icon;
        _iconView.translatesAutoresizingMaskIntoConstraints = NO;

        // Configure label
        _titleField = [[NSTextField alloc] initWithFrame:NSZeroRect];
        _titleField.bezeled = NO;
        _titleField.editable = NO;
        _titleField.drawsBackground = NO;
        _titleField.selectable = NO;
        _titleField.font = [NSFont systemFontOfSize:14 weight:NSFontWeightRegular];
        _titleField.textColor = [NSColor labelColor];
        _titleField.alignment = NSTextAlignmentLeft;
        _titleField.lineBreakMode = NSLineBreakByTruncatingTail;
        _titleField.stringValue = title ?: @"Untitled";
        _titleField.translatesAutoresizingMaskIntoConstraints = NO;
        [_titleField setContentCompressionResistancePriority:NSLayoutPriorityDefaultLow forOrientation:NSLayoutConstraintOrientationHorizontal];

        // Layout stack orientation horizontal inside each item view (icon + title)
        NSStackView *hStack = [[NSStackView alloc] init];
        hStack.orientation = NSUserInterfaceLayoutOrientationHorizontal;
        hStack.spacing = 8.0;
        hStack.edgeInsets = NSEdgeInsetsMake(4, 4, 4, 4);
        hStack.translatesAutoresizingMaskIntoConstraints = NO;
        [hStack addArrangedSubview:_iconView];
        [hStack addArrangedSubview:_titleField];
        [self addSubview:hStack];

        // Constraints
        [NSLayoutConstraint activateConstraints:@[
            [hStack.leadingAnchor constraintEqualToAnchor:self.leadingAnchor],
            [hStack.trailingAnchor constraintLessThanOrEqualToAnchor:self.trailingAnchor],
            [hStack.topAnchor constraintEqualToAnchor:self.topAnchor],
            [hStack.bottomAnchor constraintEqualToAnchor:self.bottomAnchor],
            [_iconView.widthAnchor constraintEqualToConstant:20],
            [_iconView.heightAnchor constraintEqualToConstant:20],
            [self.widthAnchor constraintLessThanOrEqualToConstant:maxWidth]
        ]];

        // Styling
        self.wantsLayer = YES;
        self.layer.cornerRadius = 6.0;
    }
    return self;
}
@end

//---- Main controller ---------------------------------------------------------
@interface TabSwitcherController ()
@property (nonatomic) id keyDownMonitor;
@property (nonatomic) id flagsMonitor;
@property (nonatomic, assign) BOOL controlHeld;
@property (nonatomic, strong) NSPanel *overlayPanel;
@property (nonatomic, strong) NSStackView *stackView;
@property (nonatomic, copy) NSArray<NSWindow *> *windowList;
@property (nonatomic, assign) NSInteger currentIndex;
@property (nonatomic, strong) NSMutableDictionary<NSString *, NSMutableArray<NSWindow *> *> *lruMap; // LRU stacks per group
@end

@implementation TabSwitcherController

+ (instancetype)sharedController {
    static TabSwitcherController *instance = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        instance = [[TabSwitcherController alloc] initPrivate];
    });
    return instance;
}

- (instancetype)initPrivate {
    self = [super init];
    if (self) {
        _controlHeld = NO;
        _currentIndex = 0;
        _lruMap = [NSMutableDictionary dictionary];
        // Observe selection changes to maintain recency order
        [[NSNotificationCenter defaultCenter] addObserver:self
                                                 selector:@selector(tabSelectionChanged:)
                                                     name:kTabGroupManagerSelectionChangedNotification
                                                   object:nil];
    }
    return self;
}

- (instancetype)init { @throw [NSException exceptionWithName:@"Singleton"
                                                     reason:@"Use +sharedController"
                                                   userInfo:nil]; return nil; }

#pragma mark - Public API

- (void)installEventMonitors {
    if (self.keyDownMonitor || self.flagsMonitor) { return; } // Already installed

    __weak TabSwitcherController *weakSelf = self;
    self.keyDownMonitor = [NSEvent addLocalMonitorForEventsMatchingMask:NSEventMaskKeyDown
                                                                handler:^NSEvent * _Nullable(NSEvent * _Nonnull event) {
        return [weakSelf handleKeyDown:event] ? nil : event;
    }];

    self.flagsMonitor = [NSEvent addLocalMonitorForEventsMatchingMask:NSEventMaskFlagsChanged
                                                               handler:^NSEvent * _Nullable(NSEvent * _Nonnull event) {
        [weakSelf handleFlagsChanged:event];
        return event;
    }];

    DBM(@"TabSwitcherController installed event monitors");
}

- (void)tearDown {
    if (self.keyDownMonitor) { [NSEvent removeMonitor:self.keyDownMonitor]; self.keyDownMonitor = nil; }
    if (self.flagsMonitor) { [NSEvent removeMonitor:self.flagsMonitor]; self.flagsMonitor = nil; }
    [self dismissOverlay];
    [[NSNotificationCenter defaultCenter] removeObserver:self name:kTabGroupManagerSelectionChangedNotification object:nil];
    DBM(@"TabSwitcherController torn down");
}

#pragma mark - Event Handling

- (BOOL)handleKeyDown:(NSEvent *)event {
    if (!self.controlHeld) { return NO; } // we only handle events while Control is held

    if (event.keyCode != kVK_Tab) { return NO; }

    if (!self.overlayPanel.isVisible) {
        [self buildWindowListAndShowOverlay];
    }

    BOOL shift = (event.modifierFlags & NSEventModifierFlagShift) != 0;
    if (shift) {
        self.currentIndex = (self.currentIndex - 1 + self.windowList.count) % self.windowList.count;
    } else {
        self.currentIndex = (self.currentIndex + 1) % self.windowList.count;
    }
    [self updateHighlight];
    return YES; // swallow
}

- (void)handleFlagsChanged:(NSEvent *)event {
    BOOL nowHeld = (event.modifierFlags & NSEventModifierFlagControl) != 0;
    if (nowHeld && !self.controlHeld) {
        self.controlHeld = YES; // Control pressed
    } else if (!nowHeld && self.controlHeld) {
        self.controlHeld = NO; // Control released
        if (self.overlayPanel.isVisible) {
            [self finalizeSelectionAndDismiss];
        }
    }
}

#pragma mark - Overlay Construction

- (void)buildWindowListAndShowOverlay {
    ENSURE_MAIN_THREAD();

    NSWindow *active = [NSApp keyWindow];
    NSString *gid = [[TabGroupManager sharedManager] groupIdForWindow:active] ?: @"";

    // All windows currently in the group (ground truth)
    NSArray<NSWindow *> *allWindows = [[TabGroupManager sharedManager] windowsInGroup:gid];
    if (allWindows.count == 0) {
        allWindows = @[active ?: [NSApp mainWindow] ?: [NSApp windows].firstObject];
    }

    // Assemble LRU-ordered list
    NSMutableArray<NSWindow *> *ordered = [NSMutableArray array];
    NSMutableArray<NSWindow *> *recencyStack = self.lruMap[gid];
    if (recencyStack) {
        // Prune any windows that no longer exist.
        NSIndexSet *invalid = [recencyStack indexesOfObjectsPassingTest:^BOOL(NSWindow * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
            return ![allWindows containsObject:obj] || obj == nil;
        }];
        if (invalid.count) { [recencyStack removeObjectsAtIndexes:invalid]; }

        [ordered addObjectsFromArray:recencyStack];
    }

    // Append remaining windows to preserve completeness.
    for (NSWindow *w in allWindows) {
        if (![ordered containsObject:w]) { [ordered addObject:w]; }
    }

    self.windowList = ordered;

    // Reset index: initialise to the currently active window. The first Tab key press
    // handled in `handleKeyDown:` will advance to the next most-recent window, so we
    // avoid a double increment that previously caused selection to jump two steps.
    NSUInteger idxOfActive = [ordered indexOfObject:active];
    self.currentIndex = (idxOfActive == NSNotFound ? 0 : idxOfActive);

    // Build UI once
    if (!self.overlayPanel) { [self createOverlayPanel]; }

    // Clear previous stack items
    [self.stackView.arrangedSubviews makeObjectsPerformSelector:@selector(removeFromSuperview)];

    CGFloat maxWidth = 260.0;
    for (NSWindow *win in ordered) {
        NSImage *icon = LUGenerateWindowSymbolIcon(win, 14.0, YES);
        if (!icon) {
            icon = win.miniwindowImage ?: [NSApp applicationIconImage];
        }
        NSString *title = win.title ?: @"Untitled";
        TabSwitcherItemView *item = [[TabSwitcherItemView alloc] initWithIcon:icon title:title maximumWidth:maxWidth];
        [self.stackView addArrangedSubview:item];
        item.translatesAutoresizingMaskIntoConstraints = NO;
        [item.widthAnchor constraintEqualToConstant:maxWidth].active = YES;
    }

    [self updateHighlight];

    // Position panel centered on screen
    NSRect frame = self.overlayPanel.frame;
    frame.size.width = maxWidth + 16; // padding
    frame.size.height = self.stackView.fittingSize.height + 16;
    [self.overlayPanel setFrame:frame display:NO];

    NSRect referenceRect;
    if (active) {
        referenceRect = [active frame];
    } else {
        referenceRect = (active.screen ?: NSScreen.mainScreen).visibleFrame;
    }

    CGFloat x = NSMidX(referenceRect) - frame.size.width / 2.0;
    CGFloat y = NSMidY(referenceRect) - frame.size.height / 2.0;
    [self.overlayPanel setFrameOrigin:NSMakePoint(x, y)];

    [self.overlayPanel orderFront:nil];
}

- (void)createOverlayPanel {
    NSPanel *panel = [[NSPanel alloc] initWithContentRect:NSMakeRect(0, 0, 300, 400)
                                                 styleMask:NSWindowStyleMaskNonactivatingPanel | NSWindowStyleMaskHUDWindow
                                                   backing:NSBackingStoreBuffered
                                                     defer:YES];
    panel.level = NSStatusWindowLevel;
    panel.opaque = NO;
    panel.hasShadow = YES;
    panel.backgroundColor = [NSColor clearColor];
    panel.collectionBehavior = NSWindowCollectionBehaviorTransient | NSWindowCollectionBehaviorIgnoresCycle;
    panel.hidesOnDeactivate = YES;

    // Visual effect background (shared style with launcher bar)
    NSVisualEffectView *vev = CreateHUDContentView(panel.contentView.bounds, 10.0);
    [panel.contentView addSubview:vev];

    // Vertical stack
    NSStackView *stack = [[NSStackView alloc] initWithFrame:NSZeroRect];
    stack.orientation = NSUserInterfaceLayoutOrientationVertical;
    stack.alignment = NSLayoutAttributeLeft;
    stack.spacing = 4.0;
    stack.translatesAutoresizingMaskIntoConstraints = NO;
    [vev addSubview:stack];

    // Constraints to center
    [NSLayoutConstraint activateConstraints:@[
        [stack.centerXAnchor constraintEqualToAnchor:vev.centerXAnchor],
        [stack.centerYAnchor constraintEqualToAnchor:vev.centerYAnchor]
    ]];

    self.overlayPanel = panel;
    self.stackView = stack;
}

#pragma mark - Highlight & Selection

- (void)updateHighlight {
    ENSURE_MAIN_THREAD();
    NSUInteger idx = self.currentIndex;
    for (NSUInteger i = 0; i < self.stackView.arrangedSubviews.count; ++i) {
        NSView *view = self.stackView.arrangedSubviews[i];
        BOOL selected = (i == idx);
        view.layer.backgroundColor = selected ? [[NSColor controlAccentColor] colorWithAlphaComponent:0.8].CGColor : [NSColor clearColor].CGColor;
    }
}

- (void)finalizeSelectionAndDismiss {
    ENSURE_MAIN_THREAD();
    if (self.windowList.count == 0) { [self dismissOverlay]; return; }
    NSWindow *target = self.windowList[self.currentIndex];
    [self dismissOverlay];

    if (target) {
        // Bring to front and update selection tracking.
        if (@available(macOS 10.12, *)) {
            if (target.tabGroup) {
                target.tabGroup.selectedWindow = target;
            }
        }
        [target makeKeyAndOrderFront:nil];
        [[TabGroupManager sharedManager] setSelectedWindow:target];
        DBM(@"TabSwitcher activated window %p", target);
    }
}

- (void)dismissOverlay {
    [self.overlayPanel orderOut:nil];
}

#pragma mark - Helpers

- (void)_ensureMainThreadImpl:(NSString *)selectorName {
    SEL sel = NSSelectorFromString(selectorName);
    if ([self respondsToSelector:sel]) {
        ((void (*)(id, SEL))[self methodForSelector:sel])(self, sel);
    }
}

#pragma mark - Selection Tracking (LRU)

// Updates lruMap whenever the selected tab changes.
- (void)tabSelectionChanged:(NSNotification *)note {
    ENSURE_MAIN_THREAD();
    NSWindow *window = note.userInfo[@"window"];
    if (!window || (id)window == [NSNull null]) { return; }
    NSString *gid = note.userInfo[@"groupId"] ?: @"";

    NSMutableArray<NSWindow *> *stack = self.lruMap[gid];
    if (!stack) {
        stack = [NSMutableArray array];
        self.lruMap[gid] = stack;
    }

    // Remove if it already exists (pointer equality) and insert at front.
    NSUInteger existingIdx = [stack indexOfObjectIdenticalTo:window];
    if (existingIdx != NSNotFound) { [stack removeObjectAtIndex:existingIdx]; }
    [stack insertObject:window atIndex:0];
}

@end

#endif // __APPLE__ 