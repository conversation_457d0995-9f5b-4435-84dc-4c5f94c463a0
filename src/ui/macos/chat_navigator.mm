#include "chat_navigator.h"

#include "macos_ui_internal.h" // g_launcher_chat_instances + forward class decl
#include "../chat/macos_chat_ui.h"
#import "../chat/tabs/tab_group_manager.h"
#import <Cocoa/Cocoa.h>
#include <fstream>
#include <nlohmann/json.hpp>

using launcher::ui::MacOSChatUI;

@implementation ChatNavigator {
    std::shared_ptr<MacOSChatUI> _lastChatUI; // strong reference
}

+ (instancetype)shared {
    static ChatNavigator *inst;
    static dispatch_once_t once;
    dispatch_once(&once, ^{ inst = [[ChatNavigator alloc] init]; });
    return inst;
}

#pragma mark - Public helpers

- (void)openChatAndSendQuestion:(NSString *)question {
    if (!question || question.length == 0) { return; }

    // Determine existing anchor window & group id from last chat UI if any.
    NSWindow *anchorWindow = nil;
    NSString *destGroupId  = nil;
    if (_lastChatUI) {
        anchorWindow = _lastChatUI->getNativeWindow();
        if (anchorWindow) {
            destGroupId = [[TabGroupManager sharedManager] groupIdForWindow:anchorWindow];
        }
    }

    // Create a fresh chat UI.
    auto newUI = std::make_shared<MacOSChatUI>();
    if (!newUI->initialize()) { return; }
    NSWindow *newWindow = newUI->getNativeWindow();
    if (!newWindow) { return; }

    // Ensure a tab-group id exists.
    if (!destGroupId) {
        destGroupId = [[TabGroupManager sharedManager] createGroupWithTitle:nil tint:[NSColor systemTealColor]];
        if (anchorWindow) {
            [[TabGroupManager sharedManager] addWindow:anchorWindow toGroup:destGroupId];
        }
    }

    // Register in group & tab with anchor.
    [[TabGroupManager sharedManager] addWindow:newWindow toGroup:destGroupId];
    if (anchorWindow && @available(macOS 10.12, *)) {
        [anchorWindow addTabbedWindow:newWindow ordered:NSWindowAbove];
    }

    // Show + stream.
    newUI->show();
    newUI->initiateStreamForMessage(question);

    // Track globally.
    g_launcher_chat_instances.push_back(newUI);
    _lastChatUI = newUI;

    [[TabGroupManager sharedManager] setSelectedWindow:newWindow];
}

- (void)openConversationWithId:(NSString *)convId {
    if (!convId || convId.length == 0) { return; }

    // If already visible – just activate.
    NSWindow *existing = [[TabGroupManager sharedManager] windowForConversationId:convId];
    if (existing) {
        [existing makeKeyAndOrderFront:nil];
        [[TabGroupManager sharedManager] setSelectedWindow:existing];
        return;
    }

    // If closed but in sidebar, reopen.
    if ([[TabGroupManager sharedManager] reopenClosedConversationId:convId anchorWindow:nil]) {
        return;
    }

    // Load JSON from file.
    NSString *homeDir = NSHomeDirectory();
    NSString *convDir = [[homeDir stringByAppendingPathComponent:@"Library"]
                           stringByAppendingPathComponent:@"Application Support/MicroLauncher/Conversations"];
    NSString *filePath = [[convDir stringByAppendingPathComponent:convId] stringByAppendingPathExtension:@"json"];
    if (![[NSFileManager defaultManager] fileExistsAtPath:filePath]) { return; }

    nlohmann::json j;
    try {
        std::ifstream ifs([filePath UTF8String]);
        if (!ifs.is_open()) { return; }
        ifs >> j;
    } catch (...) { return; }

    // Determine grouping anchor.
    NSWindow *anchorWindow = nil;
    NSString *destGroupId  = nil;
    if (_lastChatUI) {
        anchorWindow = _lastChatUI->getNativeWindow();
        if (anchorWindow) {
            destGroupId = [[TabGroupManager sharedManager] groupIdForWindow:anchorWindow];
        }
    }

    auto newUI = std::make_shared<MacOSChatUI>();
    if (!newUI->initialize()) { return; }
    NSWindow *newWindow = newUI->getNativeWindow();
    if (!newWindow) { return; }

    if (!destGroupId) {
        destGroupId = [[TabGroupManager sharedManager] createGroupWithTitle:nil tint:[NSColor systemTealColor]];
        if (anchorWindow) {
            [[TabGroupManager sharedManager] addWindow:anchorWindow toGroup:destGroupId];
        }
    }

    [[TabGroupManager sharedManager] addWindow:newWindow toGroup:destGroupId];
    if (anchorWindow && @available(macOS 10.12, *)) {
        [anchorWindow addTabbedWindow:newWindow ordered:NSWindowAbove];
    }

    newUI->show();
    newUI->loadConversationFromJson(j);

    g_launcher_chat_instances.push_back(newUI);
    _lastChatUI = newUI;
    [[TabGroupManager sharedManager] setSelectedWindow:newWindow];
}

@end 