#import <Cocoa/Cocoa.h>

#ifndef DEFAULT_BROWSER_ICON_H
#define DEFAULT_BROWSER_ICON_H

// Returns a cached NSImage representing the user's default web-browser.
//
// The helper performs the following lookup *once* per process:
// 1. Ask LaunchServices for the application that will open an HTTPS URL.
// 2. Return that application's icon.
// 3. Fallback to Safari icon, then SF-Symbol "globe", then generic .webloc icon.
//
// The returned image is retained for the lifetime of the process.
NSImage *GetDefaultBrowserIcon(void);

#endif  // DEFAULT_BROWSER_ICON_H 