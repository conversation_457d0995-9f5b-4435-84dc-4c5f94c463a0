#pragma once

#ifdef __OBJC__
#import <AppKit/AppKit.h>
@class RuntimePluginCellModel;

@interface PluginRowView : NSTableCellView

- (void)configureWithModel:(RuntimePluginCellModel*)model;

/// Called by PluginsPaneController to receive notification when details toggled so table can update height.
@property(nonatomic, copy) void (^toggleCallback)(void);

/// Collapsed summary-only height
+ (CGFloat)collapsedHeight;

/// Helper to compute expanded height based on model; used when row view not yet realised.
//  Returns collapsedHeight if unable to compute.
+ (CGFloat)estimatedExpandedHeightForModel:(RuntimePluginCellModel*)model;

/// Expose current model for disclosure view lazy creation
@property(nonatomic, strong, readonly) RuntimePluginCellModel *currentModel;

@end
#endif 