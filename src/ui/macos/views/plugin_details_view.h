#pragma once

#ifdef __OBJC__
#import <AppKit/AppKit.h>

/// Expanded detail panel listing each active capability with threat colouring
@interface PluginDetailsView : NSView

- (instancetype)initWithCapabilitiesHex:(NSString *)hex enabled:(BOOL)enabled NS_DESIGNATED_INITIALIZER;
- (instancetype)init NS_UNAVAILABLE;

/// Return preferred height for table row calculations
- (CGFloat)preferredHeight;

@end
#endif 