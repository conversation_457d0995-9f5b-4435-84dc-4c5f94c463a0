#pragma once

#ifdef __OBJC__
#import <AppKit/AppKit.h>
#include "../../../core/security/capability_threat.hh"

static inline NSColor* ColorForThreat(launcher::core::security::ThreatClass cls, BOOL enabled) {
    if (!enabled) { return [NSColor tertiaryLabelColor]; }
    switch (cls) {
        case launcher::core::security::ThreatClass::kSafe:
            if (@available(macOS 10.14, *)) return [NSColor systemGreenColor];
            return [NSColor colorWithCalibratedRed:0.18 green:0.74 blue:0.33 alpha:1];
        case launcher::core::security::ThreatClass::kSensitive:
            if (@available(macOS 10.14, *)) return [NSColor systemOrangeColor];
            return [NSColor colorWithCalibratedRed:1.0 green:0.55 blue:0.0 alpha:1];
        case launcher::core::security::ThreatClass::kDangerous:
            return [NSColor systemRedColor];
    }
    return [NSColor labelColor];
}
#endif 