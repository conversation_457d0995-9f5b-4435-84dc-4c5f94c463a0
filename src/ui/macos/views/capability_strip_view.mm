#import "capability_strip_view.h"
#import "threat_color.h"
#import <QuartzCore/QuartzCore.h>
#include "../../../core/foundation/capability256.h"

#ifdef __OBJC__

using launcher::core::security::ThreatClass;
using launcher::core::security::threatClass;

@interface CapabilityStripView ()
@property(nonatomic, copy)   NSString *hex;
@property(nonatomic, assign) BOOL enabled;
@end

@implementation CapabilityStripView

- (instancetype)initWithCapabilitiesHex:(NSString *)hex enabled:(BOOL)enabled {
    self = [super initWithFrame:NSZeroRect];
    if (self) {
        _hex = [hex copy];
        _enabled = enabled;
        self.wantsLayer = YES;
        [self rebuildTooltips];
    }
    return self;
}

- (void)setCapabilitiesHex:(NSString *)hex enabled:(BOOL)enabled {
    _hex = [hex copy];
    _enabled = enabled;
    [self rebuildTooltips];
    [self setNeedsDisplay:YES];
}

- (BOOL)isFlipped { return YES; }

- (NSSize)intrinsicContentSize {
    const int rows = launcher::core::security::kCapabilityBits / 16;
    return NSMakeSize(16 * 8, rows * 8);
}

- (void)drawRect:(NSRect)dirtyRect {
    [super drawRect:dirtyRect];
    CGContextRef ctx = [[NSGraphicsContext currentContext] CGContext];

    const CGFloat dot = 6.0;
    const CGFloat gap = 2.0;

    // Iterate bits (128) derived from hex string
    for (NSUInteger nibbleIdx = 0; nibbleIdx < _hex.length; ++nibbleIdx) {
        unichar ch = [_hex characterAtIndex:nibbleIdx];
        uint8_t nibble;
        if (ch >= '0' && ch <= '9') nibble = ch - '0';
        else if (ch >= 'a' && ch <= 'f') nibble = 10 + (ch - 'a');
        else if (ch >= 'A' && ch <= 'F') nibble = 10 + (ch - 'A');
        else nibble = 0;

        for (int bitInNibble = 0; bitInNibble < 4; ++bitInNibble) {
            bool bitSet = (nibble & (1 << (3 - bitInNibble))) != 0;
            NSUInteger bitIndex = nibbleIdx * 4 + bitInNibble; // 0..127
            NSUInteger row = bitIndex / 16;
            NSUInteger col = bitIndex % 16;

            NSRect rect = NSMakeRect(col * (dot + gap), row * (dot + gap), dot, dot);
            ThreatClass cls = threatClass(static_cast<kai::Capability>(bitIndex));
            NSColor *colr = ColorForThreat(cls, _enabled && bitSet);
            CGContextSetFillColorWithColor(ctx, colr.CGColor);
            CGContextFillEllipseInRect(ctx, rect);
        }
    }
}

#pragma mark - Tooltips

- (void)rebuildTooltips {
    [self removeAllToolTips];
    const CGFloat dot = 6.0;
    const CGFloat gap = 2.0;
    for (NSUInteger i = 0; i < launcher::core::security::kCapabilityBits; ++i) {
        NSUInteger row = i / 16;
        NSUInteger col = i % 16;
        NSRect rect = NSMakeRect(col * (dot + gap), row * (dot + gap), dot, dot);
        [self addToolTipRect:rect owner:self userData:(void*)(uintptr_t)i];
    }
}

- (NSString *)view:(NSView *)view stringForToolTip:(NSToolTipTag)tag point:(NSPoint)point userData:(void *)data {
    uintptr_t idx = (uintptr_t)data;
    kai::Capability cap = static_cast<kai::Capability>(idx);
    return [NSString stringWithUTF8String:kai::toString(cap).data()];
}

@end

#endif 