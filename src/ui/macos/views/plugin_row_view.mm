#import "plugin_row_view.h"
#import "threat_summary_view.h"
#import "plugin_details_view.h"
#import "../../models/runtime_plugin_cell_model.h"
#import "../injected_services.h"
#include "../../../core/plugins/plugin_events.h"
#include "../../../core/events/event_bus_service.h"
#include <memory>
#include <string>

#ifdef __OBJC__

@interface PluginRowView ()
@property(nonatomic, strong) NSButton *checkbox;
@property(nonatomic, strong) NSTextField *runtimeLabel;
@property(nonatomic, strong) NSTextField *versionLabel;
@property(nonatomic, strong) ThreatSummaryView *summaryView;
@property(nonatomic, strong) NSButton *detailsButton;
@property(nonatomic, strong) PluginDetailsView *detailsView;
@property(nonatomic, assign) BOOL detailsVisible;
@property(nonatomic, strong) RuntimePluginCellModel *currentModel;
@end

@implementation PluginRowView

- (instancetype)initWithFrame:(NSRect)frameRect {
    self = [super initWithFrame:frameRect];
    if (self) {
        self.checkbox = [NSButton checkboxWithTitle:@"" target:self action:@selector(toggle:)];
        self.checkbox.bezelStyle = NSBezelStyleRegularSquare;
        self.checkbox.translatesAutoresizingMaskIntoConstraints = NO;

        self.runtimeLabel = [[NSTextField alloc] initWithFrame:NSZeroRect];
        self.runtimeLabel.editable = NO;
        self.runtimeLabel.bezeled = NO;
        self.runtimeLabel.drawsBackground = NO;
        self.runtimeLabel.font = [NSFont boldSystemFontOfSize:12.0];
        self.runtimeLabel.translatesAutoresizingMaskIntoConstraints = NO;

        self.versionLabel = [[NSTextField alloc] initWithFrame:NSZeroRect];
        self.versionLabel.editable = NO;
        self.versionLabel.bezeled = NO;
        self.versionLabel.drawsBackground = NO;
        self.versionLabel.textColor = [NSColor secondaryLabelColor];
        self.versionLabel.font = [NSFont systemFontOfSize:11.0];
        self.versionLabel.translatesAutoresizingMaskIntoConstraints = NO;

        // Summary threat dots (3) ------------------------------------------------
        self.summaryView = [[ThreatSummaryView alloc] initWithCapabilitiesHex:@"" enabled:YES];
        self.summaryView.translatesAutoresizingMaskIntoConstraints = NO;

        // Details disclosure button -------------------------------------------
        NSImage *chevron = nil;
        if (@available(macOS 11.0, *)) {
            chevron = [NSImage imageWithSystemSymbolName:@"chevron.right" accessibilityDescription:@"Show details"];
            NSImageSymbolConfiguration *cfg = [NSImageSymbolConfiguration configurationWithPointSize:11 weight:NSFontWeightRegular];
            chevron = [chevron imageWithSymbolConfiguration:cfg];
        }
        if (chevron) {
            self.detailsButton = [NSButton buttonWithImage:chevron target:self action:@selector(toggleDetails:)];
            self.detailsButton.imagePosition = NSImageOnly;
            self.detailsButton.bezelStyle = NSBezelStyleInline;
        } else {
            // Fallback text link on older macOS
            self.detailsButton = [NSButton buttonWithTitle:@"Details" target:self action:@selector(toggleDetails:)];
            self.detailsButton.bezelStyle = NSBezelStyleInline;
            self.detailsButton.font = [NSFont systemFontOfSize:11.0 weight:NSFontWeightRegular];
        }
        self.detailsButton.translatesAutoresizingMaskIntoConstraints = NO;

        // Horizontal summary stack
        NSStackView *summaryStack = [NSStackView stackViewWithViews:@[self.checkbox, self.runtimeLabel, self.versionLabel, self.summaryView, self.detailsButton]];
        summaryStack.orientation = NSUserInterfaceLayoutOrientationHorizontal;
        summaryStack.alignment = NSLayoutAttributeCenterY;
        summaryStack.spacing = 6.0;
        summaryStack.translatesAutoresizingMaskIntoConstraints = NO;

        // Vertical stack combining summary & details
        NSStackView *vertStack = [NSStackView stackViewWithViews:@[summaryStack]];
        vertStack.orientation = NSUserInterfaceLayoutOrientationVertical;
        vertStack.alignment = NSLayoutAttributeLeading;
        vertStack.spacing = 4.0;
        vertStack.translatesAutoresizingMaskIntoConstraints = NO;

        [self addSubview:vertStack];

        [NSLayoutConstraint activateConstraints:@[
            [vertStack.leadingAnchor constraintEqualToAnchor:self.leadingAnchor constant:4.0],
            [vertStack.trailingAnchor constraintLessThanOrEqualToAnchor:self.trailingAnchor constant:-4.0],
            [vertStack.topAnchor constraintEqualToAnchor:self.topAnchor constant:2.0],
            [vertStack.bottomAnchor constraintEqualToAnchor:self.bottomAnchor constant:-2.0]
        ]];

        self.detailsVisible = NO;
    }
    return self;
}

- (void)configureWithModel:(RuntimePluginCellModel *)model {
    self.checkbox.state = model.enabled ? NSControlStateValueOn : NSControlStateValueOff;
    self.checkbox.tag = (NSInteger)(__bridge void*)model; // store pointer for action
    self.runtimeLabel.stringValue = model.runtimeId;
    self.versionLabel.stringValue = model.version;
    [self.summaryView setCapabilitiesHex:model.capabilitiesHex enabled:model.enabled];

    // Remove previous details view; will be created lazily on first disclosure
    if (self.detailsView) {
        [self.detailsView removeFromSuperview];
        self.detailsView = nil;
    }

    self.currentModel = model;

    self.detailsVisible = NO;

    // Reset disclosure icon or text
    if (@available(macOS 11.0, *)) {
        NSImage *chevron = [NSImage imageWithSystemSymbolName:@"chevron.right" accessibilityDescription:@"Show details"];
        NSImageSymbolConfiguration *cfg = [NSImageSymbolConfiguration configurationWithPointSize:11 weight:NSFontWeightRegular];
        self.detailsButton.image = [chevron imageWithSymbolConfiguration:cfg];
        self.detailsButton.title = @"";
    } else {
        self.detailsButton.title = @"Details";
    }
}

- (void)toggle:(id)sender {
    NSButton *cb = (NSButton*)sender;
    RuntimePluginCellModel *model = (__bridge RuntimePluginCellModel*)(void*)cb.tag;
    BOOL enabled = (cb.state == NSControlStateValueOn);

    // Persist via config
    auto& cfg = launcher::ui::ConfigService();
    std::string key = std::string("plugins.disabled.") + [model.runtimeId UTF8String];
    cfg.setBool(key, !enabled);
    cfg.requestSave(500);

    // Publish event
    auto* eb = launcher::ui::EventBusService();
    if (eb) {
        auto evt = std::make_shared<launcher::core::plugins::PluginStateChangeEvent>();
        evt->id = [model.runtimeId UTF8String];
        evt->enabled = enabled;
        eb->publish(std::static_pointer_cast<const launcher::core::plugins::PluginStateChangeEvent>(evt));
    }

    // Update summary colours counts
    [self.summaryView setCapabilitiesHex:model.capabilitiesHex enabled:enabled];

    // Update details view if visible
    if (self.detailsView && !self.detailsView.hidden) {
        [self.detailsView removeFromSuperview];
        self.detailsView = [[PluginDetailsView alloc] initWithCapabilitiesHex:model.capabilitiesHex enabled:enabled];
        // reinstate into stack
        NSStackView *vertStack = (NSStackView*)self.subviews.firstObject;
        [vertStack addArrangedSubview:self.detailsView];
        self.detailsView.hidden = !self.detailsVisible;
    }
}

#pragma mark - Details Disclosure

- (void)toggleDetails:(id)sender {
    // Lazy create detailsView if needed
    if (!self.detailsView) {
        self.detailsView = [[PluginDetailsView alloc] initWithCapabilitiesHex:self.currentModel.capabilitiesHex enabled:self.currentModel.enabled];
        self.detailsView.hidden = YES;
        NSStackView *vertStack = (NSStackView*)self.subviews.firstObject;
        [vertStack addArrangedSubview:self.detailsView];
    }

    self.detailsVisible = !self.detailsVisible;
    self.detailsView.hidden = !self.detailsVisible;

    // Update icon / text
    if (@available(macOS 11.0, *)) {
        NSString *symbolName = self.detailsVisible ? @"chevron.down" : @"chevron.right";
        NSImage *img = [NSImage imageWithSystemSymbolName:symbolName accessibilityDescription:self.detailsVisible?@"Hide details":@"Show details"];
        NSImageSymbolConfiguration *cfg = [NSImageSymbolConfiguration configurationWithPointSize:11 weight:NSFontWeightRegular];
        self.detailsButton.image = [img imageWithSymbolConfiguration:cfg];
        self.detailsButton.title = @"";
    } else {
        self.detailsButton.title = self.detailsVisible ? @"Hide" : @"Details";
    }

    if (self.toggleCallback) {
        self.toggleCallback();
    }
}

#pragma mark - Static helpers

+ (CGFloat)collapsedHeight { return 48.0; }

+ (CGFloat)estimatedExpandedHeightForModel:(RuntimePluginCellModel *)model {
    // Rough estimate: collapsed + 18px * active capability count (capped)
    const NSUInteger maxRows = 8; // heuristic to avoid giant rows
    NSUInteger activeCount = 0;
    NSString *hex = model.capabilitiesHex;
    for (NSUInteger i = 0; i < hex.length; ++i) {
        unichar ch = [hex characterAtIndex:i];
        uint8_t nibble;
        if (ch >= '0' && ch <= '9') nibble = ch - '0';
        else if (ch >= 'a' && ch <= 'f') nibble = 10 + (ch - 'a');
        else if (ch >= 'A' && ch <= 'F') nibble = 10 + (ch - 'A');
        else nibble = 0;
        activeCount += __builtin_popcount(nibble);
    }
    activeCount = MIN(activeCount, maxRows);
    return [self collapsedHeight] + 18.0 * activeCount;
}

@end

#endif 