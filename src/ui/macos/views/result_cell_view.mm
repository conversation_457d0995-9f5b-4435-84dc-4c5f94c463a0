#import <Cocoa/Cocoa.h>
#import "../macos_ui_internal.h"

// ResultCellView implementation separated from macos_ui_app_delegate.mm to
// simplify that file and make this reusable elsewhere.

@implementation ResultCellView

- (instancetype)initWithFrame:(NSRect)frameRect {
    self = [super initWithFrame:frameRect];
    if (self) {
        // Create app icon view
        self.appIconView = [[NSImageView alloc] initWithFrame:NSMakeRect(5, 5, 40, 40)];
        self.appIconView.imageScaling = NSImageScaleProportionallyUpOrDown;
        [self addSubview:self.appIconView];

        // Create name field
        self.nameField = [[NSTextField alloc] initWithFrame:NSMakeRect(55, 25, frameRect.size.width - 65, 20)];
        self.nameField.bezeled = NO;
        self.nameField.drawsBackground = NO;
        self.nameField.editable = NO;
        self.nameField.selectable = NO;
        self.nameField.font = [NSFont boldSystemFontOfSize:14.0];
        [self addSubview:self.nameField];

        // Create path/description field
        self.pathField = [[NSTextField alloc] initWithFrame:NSMakeRect(55, 5, frameRect.size.width - 150, 20)];
        self.pathField.bezeled = NO;
        self.pathField.drawsBackground = NO;
        self.pathField.editable = NO;
        self.pathField.selectable = NO;
        self.pathField.font = [NSFont systemFontOfSize:12.0];
        self.pathField.textColor = [NSColor grayColor];
        // Ensure long paths are truncated in the middle so both start and end remain visible
        self.pathField.lineBreakMode = NSLineBreakByTruncatingMiddle;
        [[self.pathField cell] setUsesSingleLineMode:YES];
        [self addSubview:self.pathField];

        // Create type field (right aligned, light)
        self.typeField = [[NSTextField alloc] initWithFrame:NSMakeRect(frameRect.size.width - 110, 25, 50, 20)];
        self.typeField.bezeled = NO;
        self.typeField.drawsBackground = NO;
        self.typeField.editable = NO;
        self.typeField.selectable = NO;
        self.typeField.font = [NSFont systemFontOfSize:12.0];
        self.typeField.textColor = [NSColor grayColor];
        self.typeField.alignment = NSTextAlignmentRight;
        [self addSubview:self.typeField];

        // Create optional result image preview (hidden by default)
        self.resultImageView = [[RoundedImageView alloc] initWithFrame:NSMakeRect(frameRect.size.width - 120, 5, 40, 40)];
        self.resultImageView.imageScaling = NSImageScaleProportionallyUpOrDown;
        self.resultImageView.hidden = YES;
        [self addSubview:self.resultImageView];
    }
    return self;
}

@end 