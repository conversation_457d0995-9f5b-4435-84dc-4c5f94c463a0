#import "plugin_details_view.h"
#import "swatch_view.h"
#include "../../../core/foundation/capability256.h"
#include "../../../core/security/capability_threat.hh"
#ifdef __OBJC__

using launcher::core::security::ThreatClass;
using launcher::core::security::threatClass;

@interface PluginDetailsView ()
@property(nonatomic, strong) NSGridView *grid;
@end

@implementation PluginDetailsView

- (instancetype)initWithCapabilitiesHex:(NSString *)hex enabled:(BOOL)enabled {
    self = [super initWithFrame:NSZeroRect];
    if (self) {
        self.translatesAutoresizingMaskIntoConstraints = NO;
        [self buildGridWithHex:hex enabled:enabled];
    }
    return self;
}

- (void)buildGridWithHex:(NSString *)hex enabled:(BOOL)enabled {
    _grid = [[NSGridView alloc] initWithFrame:NSZeroRect];
    _grid.translatesAutoresizingMaskIntoConstraints = NO;
    _grid.rowSpacing = 2.0;
    _grid.columnSpacing = 8.0;
    [self addSubview:_grid];

    [NSLayoutConstraint activateConstraints:@[
        [_grid.leadingAnchor constraintEqualToAnchor:self.leadingAnchor constant:0],
        [_grid.trailingAnchor constraintEqualToAnchor:self.trailingAnchor constant:0],
        [_grid.topAnchor constraintEqualToAnchor:self.topAnchor constant:0],
        [_grid.bottomAnchor constraintEqualToAnchor:self.bottomAnchor constant:0]
    ]];

    // Iterate bits of hex
    for (NSUInteger nibbleIdx = 0; nibbleIdx < hex.length; ++nibbleIdx) {
        unichar ch = [hex characterAtIndex:nibbleIdx];
        uint8_t nibble;
        if (ch >= '0' && ch <= '9') nibble = ch - '0';
        else if (ch >= 'a' && ch <= 'f') nibble = 10 + (ch - 'a');
        else if (ch >= 'A' && ch <= 'F') nibble = 10 + (ch - 'A');
        else nibble = 0;
        for (int bitInNibble = 0; bitInNibble < 4; ++bitInNibble) {
            bool bitSet = (nibble & (1 << (3 - bitInNibble))) != 0;
            if (!bitSet) continue;
            NSUInteger bitIndex = nibbleIdx * 4 + bitInNibble; // 0..127
            kai::Capability cap = static_cast<kai::Capability>(bitIndex);
            ThreatClass cls = threatClass(cap);

            SwatchView *swatch = [[SwatchView alloc] initWithThreatClass:cls];
            NSString *capName = [NSString stringWithUTF8String:kai::toString(cap).data()];
            NSTextField *label = [NSTextField labelWithString:capName];
            label.font = [NSFont systemFontOfSize:11.0];
            label.textColor = enabled ? [NSColor labelColor] : [NSColor tertiaryLabelColor];

            [_grid addRowWithViews:@[swatch, label]];
        }
    }
}

- (CGFloat)preferredHeight {
    [self layoutSubtreeIfNeeded];
    return _grid.fittingSize.height;
}

- (NSSize)intrinsicContentSize {
    return NSMakeSize(NSViewNoIntrinsicMetric, self.preferredHeight);
}

@end

#endif 