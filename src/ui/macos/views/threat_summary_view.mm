#import "threat_summary_view.h"
#import "threat_color.h"
#include "../../../core/security/capability_threat.hh"
#ifdef __OBJC__

using launcher::core::security::ThreatClass;
using launcher::core::security::threatClass;

@interface ThreatSummaryView ()
@property(nonatomic, copy) NSString *hex;
@property(nonatomic, assign) BOOL enabled;
@property(nonatomic, assign) NSUInteger safeCount;
@property(nonatomic, assign) NSUInteger sensitiveCount;
@property(nonatomic, assign) NSUInteger dangerousCount;
@end

@implementation ThreatSummaryView

static const CGFloat kDot = 8.0;
static const CGFloat kGap = 4.0;

- (instancetype)initWithCapabilitiesHex:(NSString *)hex enabled:(BOOL)enabled {
    self = [super initWithFrame:NSZeroRect];
    if (self) {
        _hex = [hex copy];
        _enabled = enabled;
        [self recomputeCounts];
        self.wantsLayer = YES;
    }
    return self;
}

- (void)setCapabilitiesHex:(NSString *)hex enabled:(BOOL)enabled {
    _hex = [hex copy];
    _enabled = enabled;
    [self recomputeCounts];
    [self setNeedsDisplay:YES];
}

- (void)recomputeCounts {
    _safeCount = _sensitiveCount = _dangerousCount = 0;
    for (NSUInteger nibbleIdx = 0; nibbleIdx < _hex.length; ++nibbleIdx) {
        unichar ch = [_hex characterAtIndex:nibbleIdx];
        uint8_t nibble;
        if (ch >= '0' && ch <= '9') nibble = ch - '0';
        else if (ch >= 'a' && ch <= 'f') nibble = 10 + (ch - 'a');
        else if (ch >= 'A' && ch <= 'F') nibble = 10 + (ch - 'A');
        else nibble = 0;
        for (int bitInNibble = 0; bitInNibble < 4; ++bitInNibble) {
            bool bitSet = (nibble & (1 << (3 - bitInNibble))) != 0;
            if (!bitSet) continue;
            NSUInteger bitIndex = nibbleIdx * 4 + bitInNibble; // 0..127
            ThreatClass cls = threatClass(static_cast<kai::Capability>(bitIndex));
            switch (cls) {
                case ThreatClass::kSafe: ++_safeCount; break;
                case ThreatClass::kSensitive: ++_sensitiveCount; break;
                case ThreatClass::kDangerous: ++_dangerousCount; break;
            }
        }
    }
}

- (BOOL)isFlipped { return YES; }

- (NSSize)intrinsicContentSize {
    return NSMakeSize(kDot * 3 + kGap * 2, kDot);
}

- (void)drawRect:(NSRect)dirtyRect {
    [super drawRect:dirtyRect];
    CGContextRef ctx = [[NSGraphicsContext currentContext] CGContext];

    const ThreatClass classes[3] = { ThreatClass::kSafe, ThreatClass::kSensitive, ThreatClass::kDangerous };
    const NSUInteger counts[3]   = { _safeCount, _sensitiveCount, _dangerousCount };

    for (int i = 0; i < 3; ++i) {
        NSColor *fill = ColorForThreat(classes[i], _enabled && counts[i] > 0);
        CGFloat x = i * (kDot + kGap);
        NSRect rect = NSMakeRect(x, 0, kDot, kDot);
        CGContextSetFillColorWithColor(ctx, fill.CGColor);
        CGContextFillEllipseInRect(ctx, rect);
    }
}

#pragma mark - Accessibility

- (BOOL)isAccessibilityElement { return YES; }

- (NSString *)accessibilityLabel {
    return [NSString stringWithFormat:@"%lu safe, %lu sensitive, %lu dangerous capabilities",
            (unsigned long)_safeCount, (unsigned long)_sensitiveCount, (unsigned long)_dangerousCount];
}

@end

#endif 