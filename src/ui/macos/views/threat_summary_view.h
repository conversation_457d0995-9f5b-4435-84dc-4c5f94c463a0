#pragma once

#ifdef __OBJC__
#import <AppKit/AppKit.h>

/// Small horizontal summary of plugin capabilities showing up to three coloured dots
/// representing Safe, Sensitive, and Dangerous capability groups.
@interface ThreatSummaryView : NSView

- (instancetype)initWithCapabilitiesHex:(NSString *)hex enabled:(BOOL)enabled NS_DESIGNATED_INITIALIZER;
- (instancetype)init NS_UNAVAILABLE;

/// Update view when model changes (e.g. plugin enabled toggled)
- (void)setCapabilitiesHex:(NSString *)hex enabled:(BOOL)enabled;

@end
#endif 