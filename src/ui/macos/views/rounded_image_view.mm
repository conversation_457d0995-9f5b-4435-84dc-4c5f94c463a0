#import <Cocoa/Cocoa.h>
#import "../macos_ui_internal.h"

// RoundedImageView implementation moved from macos_ui_app_delegate.mm to reduce
// monolithic file size and improve modularity.

@implementation RoundedImageView

- (instancetype)initWithFrame:(NSRect)frameRect {
    self = [super initWithFrame:frameRect];
    if (self) {
        [self commonInit];
    }
    return self;
}

- (instancetype)initWithCoder:(NSCoder *)coder {
    self = [super initWithCoder:coder];
    if (self) {
        [self commonInit];
    }
    return self;
}

- (void)commonInit {
    self.cornerRadius = 6.0;
    self.wantsLayer = YES;
}

- (void)setImage:(NSImage *)image {
    [super setImage:image];
    [self setNeedsDisplay:YES];
}

- (void)drawRect:(NSRect)dirtyRect {
    if (!self.image) {
        [super drawRect:dirtyRect];
        return;
    }

    NSBezierPath *path = [NSBezierPath bezierPathWithRoundedRect:self.bounds
                                                         xRadius:self.cornerRadius
                                                         yRadius:self.cornerRadius];
    [path setClip];

    // Optional subtle border for better visual separation.
    [[NSColor lightGrayColor] set];
    [path stroke];

    // Draw the image respecting flips.
    NSRect imageRect = NSMakeRect(0, 0, self.bounds.size.width, self.bounds.size.height);
    [self.image drawInRect:imageRect
                  fromRect:NSZeroRect
                 operation:NSCompositingOperationSourceOver
                  fraction:1.0
            respectFlipped:YES
                     hints:nil];
}

@end 