#pragma once

#ifdef __OBJC__
#import <AppKit/AppKit.h>
#include "../../../core/security/capability_threat.hh"
#include "../../../core/security/mask128.hh"

// Simple coloured dot strip representing up to 128 capability bits.
@interface CapabilityStripView : NSView

- (instancetype)initWithCapabilitiesHex:(NSString*)hex enabled:(BOOL)enabled;
- (void)setCapabilitiesHex:(NSString*)hex enabled:(BOOL)enabled;

@end
#endif 