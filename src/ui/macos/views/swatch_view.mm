#import "swatch_view.h"
#import "threat_color.h"

#ifdef __OBJC__

@interface SwatchView ()
@property(nonatomic, assign) launcher::core::security::ThreatClass threat;
@end

@implementation SwatchView

- (instancetype)initWithThreatClass:(launcher::core::security::ThreatClass)cls {
    self = [super initWithFrame:NSMakeRect(0,0,10,10)];
    if (self) {
        _threat = cls;
        self.wantsLayer = YES;
        self.translatesAutoresizingMaskIntoConstraints = NO;
        [self setContentHuggingPriority:NSLayoutPriorityRequired forOrientation:NSLayoutConstraintOrientationHorizontal];
        [self setContentHuggingPriority:NSLayoutPriorityRequired forOrientation:NSLayoutConstraintOrientationVertical];
        [self setContentCompressionResistancePriority:NSLayoutPriorityRequired forOrientation:NSLayoutConstraintOrientationHorizontal];
        [self setContentCompressionResistancePriority:NSLayoutPriorityRequired forOrientation:NSLayoutConstraintOrientationVertical];
        [self.widthAnchor constraintEqualToConstant:10.0].active = YES;
        [self.heightAnchor constraintEqualToConstant:10.0].active = YES;
    }
    return self;
}

- (BOOL)isFlipped { return YES; }

- (void)drawRect:(NSRect)dirtyRect {
    [super drawRect:dirtyRect];
    CGContextRef ctx = [[NSGraphicsContext currentContext] CGContext];
    NSColor *c = ColorForThreat(self.threat, YES);
    CGContextSetFillColorWithColor(ctx, c.CGColor);
    CGContextFillEllipseInRect(ctx, self.bounds);
}

@end

#endif 