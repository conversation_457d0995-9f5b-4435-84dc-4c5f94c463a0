#pragma once

#include <functional>
#include <memory>
#include <vector>

#include "../../core/platform/macos/macos_platform.h"
#include "../common/result_item.h"

// Forward declaration for injected context struct
namespace launcher { namespace core { struct AppContext; } }

// Forward declarations for Objective-C classes
#ifdef __OBJC__
@class AppDelegate;
@class ResultsTableDelegate;
@class SearchFieldDelegate;
@class PreferencesWindowController;
#else
class AppDelegate;
class ResultsTableDelegate;
class SearchFieldDelegate;
class PreferencesWindowController;
#endif

namespace launcher {
namespace ui {

// Callback aliases previously defined in UIInterface
using LaunchCallback = std::function<void(const ResultItem& item)>;
using SaveSettingsCallback = std::function<void()>;

/**
 * @brief MacOS implementation of the UI interface
 *
 * This class provides a macOS-specific implementation of the UI interface,
 * using Cocoa and AppKit to create a menu bar application with a launcher bar.
 */
class MacOSUI {
 public:
    /**
     * @brief Constructor
     */
    explicit MacOSUI(std::shared_ptr<launcher::core::AppContext> ctx = nullptr);

    /**
     * @brief Destructor
     */
    ~MacOSUI();

    /**
     * @brief Initialize the UI
     *
     * @param launchCallback Callback function for launch events
     * @return true if initialization was successful, false otherwise
     */
    bool initialize(LaunchCallback launchCallback);

    /**
     * @brief Show the launcher bar
     *
     * @return true if successful, false otherwise
     */
    bool showLauncherBar();

    /**
     * @brief Hide the launcher bar
     *
     * @return true if successful, false otherwise
     */
    bool hideLauncherBar();

    /**
     * @brief Show the settings window
     *
     * @param saveCallback Callback function for save events
     * @return true if successful, false otherwise
     */
    bool showSettingsWindow(SaveSettingsCallback saveCallback = nullptr);

    /**
     * @brief Hide the settings window
     *
     * @return true if successful, false otherwise
     */
    bool hideSettingsWindow();

    /**
     * @brief Update the search results
     *
     * @param results List of search results
     * @return true if successful, false otherwise
     */
    bool updateResults(const std::vector<ResultItem>& results);

    /**
     * @brief Set the input text in the launcher bar
     *
     * @param text The text to set
     * @return true if the text was set successfully, false otherwise
     */
    bool setInputText(const std::string& text);

    /**
     * @brief Get the current input text from the launcher bar
     *
     * @return The current input text
     */
    std::string getInputText() const;

    /**
     * @brief Select the next result in the list
     *
     * @return true if selection was changed successfully, false otherwise
     */
    bool selectNextResult();

    /**
     * @brief Select the previous result in the list
     *
     * @return true if selection was changed successfully, false otherwise
     */
    bool selectPreviousResult();

    /**
     * @brief Get the currently selected result
     *
     * @return The currently selected result, or nullptr if no result is selected
     */
    ResultItem* getSelectedResult();

    /**
     * @brief Execute the currently selected result
     *
     * @return true if execution was successful, false otherwise
     */
    bool executeSelectedResult();

    /**
     * @brief Set the theme for the launcher bar
     *
     * @param themeName The name of the theme to apply
     * @return true if the theme was applied successfully, false otherwise
     */
    bool setTheme(const std::string& themeName);

    /**
     * @brief Run the UI event loop
     *
     * @return The exit code
     */
    int run();

    /**
     * @brief Get the platform interface
     *
     * @return Pointer to the platform interface
     */
    const launcher::core::MacOSPlatform* getPlatform() const;

    /**
     * @brief Set the selected index in the results list
     *
     * @param index The index to select
     */
    void setSelectedIndex(int index);

    /**
     * @brief Get the selected index in the results list
     *
     * @return The selected index, or -1 if no item is selected
     */
    int getSelectedIndex() const;

    /**
     * @brief Get all current results
     *
     * @return The current results list
     */
    const std::vector<ResultItem>& getResults() const;

    // Accessor methods for Objective-C classes
    AppDelegate* getAppDelegate() const;
    ResultsTableDelegate* getResultsDelegate() const;
    SearchFieldDelegate* getSearchFieldDelegate() const;
    PreferencesWindowController* getPreferencesWindowController() const;

 private:
    // Private implementation details
    class Impl;
    std::unique_ptr<Impl> pImpl;
};

}  // namespace ui
}  // namespace launcher