#pragma once

#import <AppKit/AppKit.h>

#ifdef __OBJC__

@class MASShortcutView;

/**
 * GeneralPaneController is responsible for the "General" preference pane which contains
 * system-wide settings such as hotkey activation, launch behavior, and general UX.
 * It handles both UI layout/construction and the associated business logic for 
 * loading/saving values via launcher::core::ConfigManager.
 */
@interface GeneralPaneController : NSViewController

/// Persist any pending changes to ConfigManager.
- (void)saveChanges;

@end

#endif  // __OBJC__ 