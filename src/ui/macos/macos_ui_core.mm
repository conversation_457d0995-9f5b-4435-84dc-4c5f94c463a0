#include "macos_ui.h"
#include "macos_ui_internal.h"
#include "../../core/app_context.h"

#include <iostream>
#include <algorithm>
#include <chrono>
#include <cmath>
#include <cstdlib>
#include <ctime>
#include <filesystem>
#include <fstream>
#include <map>
#include <memory>
#include <regex>
#include <sstream>
#include <string>
#include <thread>
#include <vector>
#include <utility>

#include "../../core/config/config_manager.h"
#include "../../core/platform/macos/macos_platform.h"
#include "../../core/util/debug.h"
#include "../common/message_formatter.h"
#include "../chat/macos_chat_ui.h" // Added for Chat UI support
#include "preferences_window_controller.h"
#include "browser_history_importer.h"
#import "../../third_party/mas_shortcut/include/MASShortcut.h"
#import "../../third_party/mas_shortcut/include/MASShortcutMonitor.h"
#include "hotkey_utils.h"
#include "../macos/application_visibility_manager.h" // For ApplicationVisibilityManager
#import "../chat/session_autosave_manager.h"
#import "../chat/tabs/tab_group_manager.h" // Added for tab grouping integration
#include "tab_switcher_controller.h"
#include "../chat/chat_window_factory.h"
#import "hud_window_utils.h"
#import <CoreGraphics/CoreGraphics.h>
#include "injected_services.h"  // shared helpers

#import <Cocoa/Cocoa.h>
#import <Foundation/Foundation.h>
#import <AppKit/AppKit.h>
#import <Carbon/Carbon.h>
#import <QuartzCore/QuartzCore.h>
#import <objc/runtime.h>
#import <UniformTypeIdentifiers/UniformTypeIdentifiers.h>
#import <UniformTypeIdentifiers/UTCoreTypes.h>

#import "resources/AppIcon.h"

using namespace launcher::core;
using namespace launcher::ui;

// PIMPL implementation
class MacOSUI::Impl {
public:
    Impl() : application(nil), appDelegate(nil), resultsDelegate(nil), 
             searchFieldDelegate(nil), settingsWindowController(nil), selectedIndex(-1) {}
    
    // Shared services context (non-null in production code)
    std::shared_ptr<launcher::core::AppContext> context;
    
    NSApplication* application;
    AppDelegate* appDelegate;
    ResultsTableDelegate* resultsDelegate;
    SearchFieldDelegate* searchFieldDelegate;
    PreferencesWindowController* settingsWindowController;
    std::vector<ResultItem> results;
    int selectedIndex;
    SaveSettingsCallback saveSettingsCallback;
    std::unique_ptr<core::MacOSPlatform> platform;
};

// MacOSUI implementation
MacOSUI::MacOSUI(std::shared_ptr<launcher::core::AppContext> ctx) : pImpl(std::make_unique<Impl>()) {
    pImpl->context = std::move(ctx);
    // Create platform instance (requires configuration)
    IConfigManager* cfgPtr = nullptr;
    if (pImpl->context && pImpl->context->configManager) {
        cfgPtr = pImpl->context->configManager.get();
    }
    if (cfgPtr) {
        pImpl->platform = std::make_unique<core::MacOSPlatform>(*cfgPtr);
    } else {
        // Fallback: create dummy ConfigManager to satisfy platform dependency (unit tests)
        static core::ConfigManager fallbackCfg;
        fallbackCfg.initialize();
        pImpl->platform = std::make_unique<core::MacOSPlatform>(fallbackCfg);
    }
}

MacOSUI::~MacOSUI() = default;

// Accessor methods for Objective-C classes
AppDelegate* MacOSUI::getAppDelegate() const {
    return pImpl->appDelegate;
}

ResultsTableDelegate* MacOSUI::getResultsDelegate() const {
    return pImpl->resultsDelegate;
}

SearchFieldDelegate* MacOSUI::getSearchFieldDelegate() const {
    return pImpl->searchFieldDelegate;
}

PreferencesWindowController* MacOSUI::getPreferencesWindowController() const {
    return pImpl->settingsWindowController;
}

void MacOSUI::setSelectedIndex(int index) {
    pImpl->selectedIndex = index;
}

int MacOSUI::getSelectedIndex() const {
    return pImpl->selectedIndex;
}

bool MacOSUI::initialize(LaunchCallback launchCallback) {
    @autoreleasepool {
        // Create application
        pImpl->application = [NSApplication sharedApplication];
        
        // Don't force accessory mode here - let ApplicationVisibilityManager handle app visibility
        // App starts as accessory (LSUIElement=true) and will become foreground when chat windows open
        
        // Create app delegate
        pImpl->appDelegate = [[AppDelegate alloc] init];
        pImpl->appDelegate.ctx = pImpl->context.get();
        pImpl->appDelegate.uiInstance = this;
        pImpl->appDelegate.launchCallback = launchCallback;
        
        // Log callback status
        if (launchCallback) {
            // DBM(@"LaunchCallback is set");
        } else {
            WRM(@"LaunchCallback is NULL");
        }
        
        pImpl->appDelegate.iconCache = [[NSCache alloc] init];
        [pImpl->appDelegate.iconCache setCountLimit:100]; // Limit cache size
        [pImpl->application setDelegate:pImpl->appDelegate];
        
        // Create results delegate
        pImpl->resultsDelegate = [[ResultsTableDelegate alloc] initWithResults:@[]];
        pImpl->resultsDelegate.uiInstance = this;
        pImpl->resultsDelegate.launchCallback = launchCallback;
        pImpl->resultsDelegate.iconCache = pImpl->appDelegate.iconCache;
        
        // Store the results delegate in the app delegate
        pImpl->appDelegate.resultsDelegate = pImpl->resultsDelegate;
        
        // Create search field delegate
        pImpl->searchFieldDelegate = [[SearchFieldDelegate alloc] init];
        
        // Store the search field delegate in the app delegate
        pImpl->appDelegate.searchFieldDelegate = pImpl->searchFieldDelegate;
        
        // Create settings window controller
        pImpl->settingsWindowController = [[PreferencesWindowController alloc] initWithSaveCallback:nil];
        pImpl->appDelegate.settingsWindowController = pImpl->settingsWindowController;
        
        // Install Control-Tab switcher overlay once
        [[TabSwitcherController sharedController] installEventMonitors];
        
        return true;
    }
}

bool MacOSUI::showLauncherBar() {
    @autoreleasepool {
        // For unit tests, we might not have a real UI environment
        // In that case, just return true to pass the test
        if (NSApp == nil || [NSApp isRunning] == NO) {
            return true;
        }
        
        // Collect selected text context before showing the launcher bar
        auto& contextManager = launcher::ui::ContextService();
        DBM(@"Collecting selected text context before showing launcher bar");
        launcher::core::Context selectedTextContext = contextManager.collectContextFromProviders({"selected_text"});
        
        // Store the selected text context in the app delegate
        pImpl->appDelegate.selectedTextContext = selectedTextContext;
        
        // Create launcher bar if it doesn't exist
        if (!pImpl->appDelegate.launcherBar) {
            [pImpl->appDelegate createLauncherBar];
        }
        
        // Show window
        [pImpl->appDelegate.launcherBar makeKeyAndOrderFront:nil];
        [NSApp activateIgnoringOtherApps:YES];
        
        // Focus search field
        if (pImpl->appDelegate.searchField) {
            [pImpl->appDelegate.launcherBar makeFirstResponder:pImpl->appDelegate.searchField];
            [pImpl->appDelegate.searchField selectText:nil];
        }
        
        return true;
    }
}

bool MacOSUI::hideLauncherBar() {
    @autoreleasepool {
        if (pImpl->appDelegate.launcherBar && [pImpl->appDelegate.launcherBar isVisible]) {
            // Use helper to also restore focus to previously active application
            [pImpl->appDelegate hideLauncherBarAndRestore];
            return true;
        }
        return false;
    }
}

bool MacOSUI::updateResults(const std::vector<ResultItem>& results) {
    @autoreleasepool {
        // Convert results to NSArray of NSDictionaries
        NSMutableArray* nsResults = [NSMutableArray arrayWithCapacity:results.size()];
        
        DBM(@"Updating results with %lu items", results.size());
        
        for (const auto& result : results) {
            NSString* name = [NSString stringWithUTF8String:result.name.c_str()];
            NSString* path = [NSString stringWithUTF8String:result.path.c_str()];
            NSString* iconPath = [NSString stringWithUTF8String:result.iconPath.c_str()];
            NSNumber* score = [NSNumber numberWithDouble:result.score];
            NSString* type = [NSString stringWithUTF8String:result.type.c_str()];
            
            // DBM(@"Adding result: %@ (%@), type: %@", name, path, type);
            
            NSString *identifier = [NSString stringWithFormat:@"%@-%@", name ?: @"", path ?: @""];  // stable id
            [nsResults addObject:@{
                @"identifier": identifier,
                @"name": name ?: @"",
                @"path": path ?: @"",
                @"iconPath": iconPath ?: @"",
                @"score": score,
                @"type": type ?: @"app"
            }];
        }
        
        // Store the results in the pImpl for later access
        pImpl->results = results;
        
        // Ensure UI updates happen on the main thread
        dispatch_async(dispatch_get_main_queue(), ^{
            // Update results in the delegate
            pImpl->resultsDelegate.results = nsResults;
            
            // Check if launchCallback is set
            if (pImpl->resultsDelegate.launchCallback) {
                // DBM(@"LaunchCallback is set in updateResults");
            } else {
                WRM(@"LaunchCallback is NULL in updateResults");
                // Re-set the launch callback
                pImpl->resultsDelegate.launchCallback = pImpl->appDelegate.launchCallback;
                if (pImpl->resultsDelegate.launchCallback) {
                    DBM(@"LaunchCallback re-set from appDelegate");
                } else {
                    ERM(@"Unable to re-set LaunchCallback from appDelegate");
                }
            }
            
            // Apply snapshot if we're using diffable datasource (macOS 11+)
            [pImpl->appDelegate setResultsDelegate:pImpl->resultsDelegate];

            #if __has_include(<AppKit/NSTableViewDiffableDataSource.h>)
                if (@available(macOS 11.0, *)) {
                    if (pImpl->appDelegate.diffableDataSource) {
                        NSMutableArray<NSString *> *identifiers = [NSMutableArray arrayWithCapacity:nsResults.count];
                        for (NSDictionary *d in nsResults) {
                            NSString *identifier = d[@"identifier"] ?: [NSString stringWithFormat:@"%@-%@", d[@"name"], d[@"path"]];
                            [identifiers addObject:identifier];
                        }

                        NSDiffableDataSourceSnapshot<NSNumber *, NSString *> *snap = [[NSDiffableDataSourceSnapshot alloc] init];
                        [snap appendSectionsWithIdentifiers:@[@0]];
                        [snap appendItemsWithIdentifiers:identifiers intoSectionWithIdentifier:@0];
                        [pImpl->appDelegate.diffableDataSource applySnapshot:snap animatingDifferences:NO];
                    } else {
                        [pImpl->appDelegate.resultsTable reloadData];
                    }
                } else {
                    [pImpl->appDelegate.resultsTable reloadData];
                }
            #else
                [pImpl->appDelegate.resultsTable reloadData];
            #endif

            // Select the first row if there are results
            if (pImpl->results.size() > 0) {
                [pImpl->appDelegate.resultsTable selectRowIndexes:[NSIndexSet indexSetWithIndex:0] byExtendingSelection:NO];
            }
        });
        
        return true;
    }
}

bool MacOSUI::showSettingsWindow(SaveSettingsCallback saveCallback) {
    @autoreleasepool {
        if (pImpl->settingsWindowController) {
            pImpl->saveSettingsCallback = saveCallback;
            pImpl->settingsWindowController.saveCallback = saveCallback;

            // Ensure the app is in the foreground so the Settings window is
            // actually visible even when the launcher is still running in
            // LSUIElement accessory mode.
            launcher::ui::ApplicationVisibilityManager::shared().ensureForeground();

            [pImpl->settingsWindowController loadSettings];
            [pImpl->settingsWindowController showWindow];
            [NSApp activateIgnoringOtherApps:YES];
            return true;
        }
        return false;
    }
}

bool MacOSUI::hideSettingsWindow() {
    @autoreleasepool {
        if (pImpl->settingsWindowController) {
            [pImpl->settingsWindowController closeWindow];
            return true;
        }
        return false;
    }
}

int MacOSUI::run() {
    @autoreleasepool {
        DBM(@"Starting UI event loop");
        
        // Run the application
        [pImpl->application run];
        
        DBM(@"UI event loop ended");
        
        return 0;
    }
}

const launcher::core::MacOSPlatform* MacOSUI::getPlatform() const {
    return pImpl->platform.get();
}

bool MacOSUI::setInputText(const std::string& text) {
    if (!pImpl || !pImpl->appDelegate || !pImpl->appDelegate.searchField) {
        return false;
    }
    
    NSString* nsText = [NSString stringWithUTF8String:text.c_str()];
    [pImpl->appDelegate.searchField setStringValue:nsText];
    return true;
}

std::string MacOSUI::getInputText() const {
    if (!pImpl || !pImpl->appDelegate || !pImpl->appDelegate.searchField) {
        return "";
    }
    
    NSString* text = [pImpl->appDelegate.searchField stringValue];
    return text ? [text UTF8String] : "";
}

bool MacOSUI::selectNextResult() {
    if (!pImpl || !pImpl->appDelegate || !pImpl->appDelegate.resultsTable) {
        return false;
    }
    
    NSInteger currentIndex = [pImpl->appDelegate.resultsTable selectedRow];
    NSInteger count = [pImpl->appDelegate.resultsTable numberOfRows];
    
    if (count <= 0) {
        return false;
    }
    
    NSInteger newIndex = (currentIndex + 1) % count;
    [pImpl->appDelegate.resultsTable selectRowIndexes:[NSIndexSet indexSetWithIndex:newIndex] byExtendingSelection:NO];
    [pImpl->appDelegate.resultsTable scrollRowToVisible:newIndex];
    return true;
}

bool MacOSUI::selectPreviousResult() {
    if (!pImpl || !pImpl->appDelegate || !pImpl->appDelegate.resultsTable) {
        return false;
    }
    
    NSInteger currentIndex = [pImpl->appDelegate.resultsTable selectedRow];
    NSInteger count = [pImpl->appDelegate.resultsTable numberOfRows];
    
    if (count <= 0) {
        return false;
    }
    
    NSInteger newIndex = (currentIndex - 1 + count) % count;
    [pImpl->appDelegate.resultsTable selectRowIndexes:[NSIndexSet indexSetWithIndex:newIndex] byExtendingSelection:NO];
    [pImpl->appDelegate.resultsTable scrollRowToVisible:newIndex];
    return true;
}

ResultItem* MacOSUI::getSelectedResult() {
    if (!pImpl || !pImpl->appDelegate || !pImpl->appDelegate.resultsTable || pImpl->results.empty()) {
        return nullptr;
    }
    
    NSInteger selectedRow = [pImpl->appDelegate.resultsTable selectedRow];
    if (selectedRow < 0 || static_cast<size_t>(selectedRow) >= pImpl->results.size()) {
        return nullptr;
    }
    
    return &pImpl->results[selectedRow];
}

const std::vector<ResultItem>& MacOSUI::getResults() const {
    return pImpl->results;
}

bool MacOSUI::executeSelectedResult() {
    if (!pImpl) {
        return false;
    }
    
    ResultItem* selectedItem = getSelectedResult();
    if (!selectedItem) {
        return false;
    }
    
    // Check if this is an app
    if (pImpl->appDelegate && pImpl->appDelegate.launchCallback) {
        // Launch app
        pImpl->appDelegate.launchCallback(*selectedItem);
        return true;
    }
    
    return false;
}

bool MacOSUI::setTheme(const std::string& themeName) {
    if (!pImpl || !pImpl->appDelegate || !pImpl->appDelegate.launcherBar) {
        return false;
    }
    
    // Simple theme implementation - could be expanded
    if (themeName == "dark") {
        if (@available(macOS 10.14, *)) {
            [pImpl->appDelegate.launcherBar setAppearance:[NSAppearance appearanceNamed:NSAppearanceNameDarkAqua]];
        }
        return true;
    } else if (themeName == "light") {
        if (@available(macOS 10.14, *)) {
            [pImpl->appDelegate.launcherBar setAppearance:[NSAppearance appearanceNamed:NSAppearanceNameAqua]];
        }
        return true;
    }
    
    // Default to system theme
    [pImpl->appDelegate.launcherBar setAppearance:nil];
    return true;
}

