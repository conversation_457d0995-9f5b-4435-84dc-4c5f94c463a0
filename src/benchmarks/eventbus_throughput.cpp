#include "../core/async/executor_service.h"
#include "../core/foundation/registry.h"
#include "../core/memory/arena_allocator_service.h"
#include "../core/events/event_bus_service.h"

#include <benchmark/benchmark.h>
#include <atomic>
#include <thread>
#include <chrono>
#include <cstdlib>
#include <algorithm>
#include <iostream>
#include <ctime>

using namespace launcher::core;

struct DummyEvt { int v; };

// Global counters to expose metrics after the benchmark run so CI can grep them.
std::atomic<double> g_throughput{0.0};
std::atomic<double> g_idle_cpu_pct{0.0};

static void BM_EventBusThroughput(benchmark::State& state) {
    foundation::ServiceRegistry registry;
    memory::ArenaAllocatorSvc    arena{registry};
    async::ExecutorService       exec{registry};
    exec.start();
    events::EventBusService bus{registry, nullptr}; // Pass nullptr for diagnostics if not used
    bus.start();

    std::atomic<int> received{0};
    auto sub = bus.subscribe<DummyEvt>([&](const DummyEvt* /*evt_ptr*/) {
        received.fetch_add(1, std::memory_order_relaxed);
    });

    const int batch = static_cast<int>(state.range(0));

    // ---- measure wall & CPU time across all iterations ----
    const auto   wall_start = std::chrono::steady_clock::now();
    const clock_t cpu_start = std::clock();

    for (auto _ : state) {
        received.store(0, std::memory_order_relaxed);
        for (int i = 0; i < batch; /*increment inside loop*/) {
            // Ensure each publish succeeds even when EventBus is under back-pressure.
            // In bounded-capacity builds (e.g. CI tests with KAI_EVENTBUS_CAPACITY=8)
            // a non-blocking publish may fail.  We spin-yield until the message is
            // accepted to keep the benchmark result meaningful and avoid an
            // infinite wait on the `received` counter below.
            auto evt = std::make_shared<DummyEvt>(DummyEvt{i});
            auto res = bus.publish(std::static_pointer_cast<const DummyEvt>(evt));
            if (res) {
                ++i; // published successfully – proceed to next message
            } else {
                // Back-pressure: give dispatcher a chance to drain.
                std::this_thread::yield();
            }
        }
        // spin until all processed
        while (received.load(std::memory_order_relaxed) < batch) {
            std::this_thread::yield();
        }
    }

    // ---- aggregate metrics ----
    const auto wall_end = std::chrono::steady_clock::now();
    const double wall_sec = std::chrono::duration<double>(wall_end - wall_start).count();
    const double cpu_sec  = static_cast<double>(std::clock() - cpu_start) / static_cast<double>(CLOCKS_PER_SEC);

    const double total_msgs = static_cast<double>(batch) * static_cast<double>(state.iterations());
    const double throughput  = total_msgs / wall_sec; // msgs / s

    const double cpu_util = (wall_sec > 0.0) ? std::min(cpu_sec / wall_sec, 1.0) : 0.0;
    const double idle_pct = 100.0 * (1.0 - cpu_util);

    // expose via counters
    state.counters["throughput_msgs_per_s"] = benchmark::Counter(throughput, benchmark::Counter::kIsRate);
    state.counters["idle_cpu_pct"]          = benchmark::Counter(idle_pct, benchmark::Counter::kAvgIterationsRate);
    state.counters["messages"]             = benchmark::Counter(total_msgs, benchmark::Counter::kIsIterationInvariantRate);

    // Explicitly unsubscribe to ensure we don't access handler after bus destruction
    sub.unsubscribe();

    bus.stop();
    exec.stop();

    // store globals for main() summary
    g_throughput.store(throughput, std::memory_order_relaxed);
    g_idle_cpu_pct.store(idle_pct, std::memory_order_relaxed);
}

BENCHMARK(BM_EventBusThroughput)->Arg(10000);

int main(int argc, char** argv) {
    benchmark::Initialize(&argc, argv);
    benchmark::RunSpecifiedBenchmarks();

    extern std::atomic<double> g_throughput;
    extern std::atomic<double> g_idle_cpu_pct;

    std::cout << "EventBus throughput: " << static_cast<int>(g_throughput.load())
              << " lines/s (Idle CPU: " << static_cast<int>(g_idle_cpu_pct.load()) << "%)" << std::endl;

    // Avoid static-destructor order issues in dependent services by exiting
    std::_Exit(0);
} 