#include <benchmark/benchmark.h>
#include "snapshot/probe_cache.hh"
#include <filesystem>
#include <random>

using launcher::snapshot::ProbeCache;
using launcher::core::security::Verdict;

static std::array<uint8_t, 32> makeSha(uint64_t seed) {
    std::array<uint8_t, 32> s{};
    std::mt19937_64 rng(seed);
    for (auto &b : s) b = static_cast<uint8_t>(rng() & 0xFFu);
    return s;
}

// Slow-path benchmark (includes canonicalisation). Reduced to 100k iterations
static void BM_ProbeCache_Hit(benchmark::State& state) {
    auto tmp = std::filesystem::temp_directory_path() / "probe_cache_bench";
    std::filesystem::remove_all(tmp);
    ProbeCache cache(tmp, 1024);

    auto sha = makeSha(42);
    cache.update(tmp / "foo", sha, Verdict{Verdict::Code::kAllowed});
    cache.flush();

    for (auto _ : state) {
        benchmark::DoNotOptimize(cache.lookup(tmp / "foo", sha));
    }
}

// Fast-path benchmark using pre-computed ProbeKey.
static void BM_ProbeCache_HitFast(benchmark::State& state) {
    auto tmp = std::filesystem::temp_directory_path() / "probe_cache_bench_fast";
    std::filesystem::remove_all(tmp);
    ProbeCache cache(tmp, 1024);

    auto sha = makeSha(42);
    auto key = ProbeCache::makeKey(tmp / "foo", sha);
    cache.update(key, Verdict{Verdict::Code::kAllowed});
    cache.flush();

    for (auto _ : state) {
        benchmark::DoNotOptimize(cache.lookup(key));
    }
}

BENCHMARK(BM_ProbeCache_Hit)->Iterations(100'000);
BENCHMARK(BM_ProbeCache_HitFast)->Iterations(1'000'000);
BENCHMARK_MAIN(); 