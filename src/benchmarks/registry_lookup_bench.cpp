#include "../core/foundation/registry.h"
#include "../core/memory/arena_allocator_service.h"
#include <chrono>
#include <iostream>
#include <cassert>
#include <vector>
#include <algorithm>
#include <numeric>

using namespace launcher::core::foundation;
using namespace launcher::core::memory;

int main() {
    ServiceRegistry   reg;
    ArenaAllocatorSvc arena{reg};

    constexpr std::size_t kIters  = 10'000'000; // 10 M
    constexpr std::size_t kBatch  = 1'000;      // sample every 1 000 ops
    std::vector<double>   samples;
    samples.reserve(kIters / kBatch);

    auto batch_start = std::chrono::steady_clock::now();
    for (std::size_t i = 0; i < kIters; ++i) {
        [[maybe_unused]] auto& svc = reg.get<ArenaAllocatorSvc>();
        if ((i + 1) % kBatch == 0) {
            auto now     = std::chrono::steady_clock::now();
            auto ns_per  = std::chrono::duration_cast<std::chrono::nanoseconds>(now - batch_start).count() /
                          static_cast<double>(kBatch);
            samples.push_back(ns_per);
            batch_start = now;
        }
    }

    // Compute statistics ---------------------------------------------------
    const double avg_ns = std::accumulate(samples.begin(), samples.end(), 0.0) / samples.size();

    std::sort(samples.begin(), samples.end());
    const std::size_t idx95  = static_cast<std::size_t>(samples.size() * 0.95);
    const double      p95_ns = samples[idx95];

    std::cout << "Average lookup: " << avg_ns << " ns\n";
    std::cout << "p95 lookup:     " << p95_ns << " ns\n";

    const double kMaxP95 = 5.0; // CI threshold
    if (p95_ns > kMaxP95) {
        std::cerr << "ServiceRegistry p95 regression: " << p95_ns << " ns (limit " << kMaxP95
                  << " ns)\n";
        return 1;
    }
    return 0;
} 