#include "../core/runtime/ring_queue_backend.hh"

#include <benchmark/benchmark.h>
#include <thread>
#include <atomic>
#include <vector>

using namespace launcher::core;

namespace {

struct DummyTask {
    int v;
};

constexpr std::size_t kCap = 1024;
using QueueT = runtime::RingQueueBackend<DummyTask, kCap>;

} // namespace

static void BM_RingQueue(benchmark::State& state) {
    QueueT q;
    const int producers = static_cast<int>(state.range(0));
    const int ops       = static_cast<int>(state.range(1));

    std::atomic<bool> stop{false};
    std::atomic<int>  produced{0};
    std::atomic<int>  consumed{0};

    // Consumer thread
    std::thread consumer([&]() {
        DummyTask task;
        while (!stop.load(std::memory_order_acquire)) {
            if (q.try_pop(task)) {
                ++consumed;
            } else {
                std::this_thread::yield();
            }
        }
        // Drain tail
        while (q.try_pop(task)) ++consumed;
    });

    // Producer threads
    std::vector<std::thread> prod_threads;
    for (int p = 0; p < producers; ++p) {
        prod_threads.emplace_back([&]() {
            for (auto _ : state) {
                for (int i = 0; i < ops; ++i) {
                    while (!q.try_push(DummyTask{i})) {
                        benchmark::DoNotOptimize(i);
                    }
                    ++produced;
                }
            }
        });
    }

    for (auto& t : prod_threads) t.join();
    stop.store(true, std::memory_order_release);
    consumer.join();

    state.counters["produced"] = static_cast<double>(produced.load());
    state.counters["consumed"] = static_cast<double>(consumed.load());
    const auto stats = q.stats();
    state.counters["drop"] = static_cast<double>(stats.drop);
    state.counters["high_water_pct"] = static_cast<double>(stats.high_water_pct);
}

BENCHMARK(BM_RingQueue)->Args({2, 100000});

BENCHMARK_MAIN(); 