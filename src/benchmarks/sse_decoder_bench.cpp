#include <benchmark/benchmark.h>
#include "core/http/sse_decoder.h"
#include "core/memory/slab_arena.h"
#include <vector>
#include <string_view>
#include <memory>
#if defined(KAI_JSON_PARSER_SIMDJSON)
#include <simdjson.h>
#endif

using http::SSEDecoder;
using http::Delta;
using launcher::core::memory::SlabArena;

// Helper identical to production decoder – minimal copy to avoid pulling in the entire
// implementation.
namespace {
inline std::string_view ltrim_view_sv(std::string_view s) {
    while (!s.empty() && std::isspace(static_cast<unsigned char>(s.front()))) {
        s.remove_prefix(1);
    }
    return s;
}

inline std::string_view stripDataPrefixSv(std::string_view line) {
    if (line.rfind("data:", 0) == 0) {
        line.remove_prefix(5);
    }
    line = ltrim_view_sv(line);
    if (!line.empty() && line.back() == '\r') {
        line.remove_suffix(1);
    }
    return line;
}
} // namespace

#if defined(KAI_JSON_PARSER_SIMDJSON)
using launcher::core::memory::SlabArena;

static void ReplayLinesSimd(const std::vector<std::string_view>& lines, benchmark::State& state) {
    thread_local simdjson::ondemand::parser parser;
    std::size_t bytes_per_iter = 0;

    for (auto _ : state) {
        SlabArena sink; // local – counts allocations per iteration

        for (std::string_view raw_line : lines) {
            std::string_view line = stripDataPrefixSv(raw_line);
            if (line.empty()) continue;
            if (line == "[DONE]" || line == "DONE" || line.find("\"message_stop\"") != std::string_view::npos) {
                continue;
            }

            simdjson::ondemand::document doc;
            auto err = parser.iterate(line, 0).get(doc);
            if (err) [[unlikely]] {
                continue; // malformed or partial – ignore like production decoder
            }

            // Generic: attempt to grab first string value for \"content\" or \"text\" field.
            simdjson::ondemand::value target;
            // Fast path – OpenAI style: choices[0].delta.content
            auto choices_val = doc["choices"];
            if (!choices_val.error()) {
                auto choices_arr = choices_val.get_array();
                if (!choices_arr.error()) {
                    for (auto elem : choices_arr) {
                        auto delta_obj = elem["delta"];
                        if (!delta_obj.error()) {
                            auto content_val = delta_obj["content"];
                            if (!content_val.error()) {
                                auto sv = std::string_view(content_val.get_string());
                                sink.append(sv);
                                break; // only need first choice
                            }
                        }
                        break; // processed first element regardless
                    }
                }
            }

            // Anthropic style: delta.text
            auto delta_val = doc["delta"];
            if (!delta_val.error()) {
                auto text_val = delta_val["text"];
                if (!text_val.error()) {
                    auto sv = std::string_view(text_val.get_string());
                    sink.append(sv);
                }
            }
        }

        bytes_per_iter += sink.size();
    }

    // Report average bytes per iteration to compare allocation footprint.
    double avg_bytes = static_cast<double>(bytes_per_iter) / static_cast<double>(state.iterations());
    state.counters["alloc_bytes"] = benchmark::Counter(avg_bytes, benchmark::Counter::kDefaults);
}
#endif // KAI_JSON_PARSER_SIMDJSON

static void ReplayLines(SSEDecoder& dec, const std::vector<std::string_view>& lines) {
    auto sink = std::make_shared<SlabArena>();
    dec.setSink(sink);
    for (auto line : lines) {
        benchmark::DoNotOptimize(dec.feed(line));
    }
}

// OpenAI sample SSE lines
static const std::vector<std::string_view> kOpenAILines = {
    "data: {\"choices\":[{\"delta\":{\"role\":\"assistant\"}}]}\n",
    "data: {\"choices\":[{\"delta\":{\"content\":\"Hello\"}}]}\n",
    "data: {\"choices\":[{\"delta\":{\"content\":\", world!\"}}]}\n",
    "data: [DONE]\n"
};

static void BM_OpenAI_Delta(benchmark::State& state) {
    for (auto _ : state) {
        auto decoder = SSEDecoder::create("openai");
        ReplayLines(*decoder, kOpenAILines);
    }
}
BENCHMARK(BM_OpenAI_Delta);

// Anthropic sample
static const std::vector<std::string_view> kAnthropicLines = {
    "data: {\"type\":\"message_start\"}\n",
    "data: {\"delta\":{\"text\":\"Hello\"}}\n",
    "data: {\"delta\":{\"text\":\", world!\"}}\n",
    "data: {\"type\":\"message_stop\"}\n"
};

static void BM_Anthropic_Delta(benchmark::State& state) {
    for (auto _ : state) {
        auto decoder = SSEDecoder::create("anthropic");
        ReplayLines(*decoder, kAnthropicLines);
    }
}
BENCHMARK(BM_Anthropic_Delta);

#if defined(KAI_JSON_PARSER_SIMDJSON)

// ---------------- Simdjson variants ----------------------------------------

static void BM_OpenAI_Delta_Simd(benchmark::State& state) {
    ReplayLinesSimd(kOpenAILines, state);
}
BENCHMARK(BM_OpenAI_Delta_Simd);

static void BM_Anthropic_Delta_Simd(benchmark::State& state) {
    ReplayLinesSimd(kAnthropicLines, state);
}
BENCHMARK(BM_Anthropic_Delta_Simd);

#endif // KAI_JSON_PARSER_SIMDJSON

// Ensure single entry point for benchmark.
BENCHMARK_MAIN(); 