#include "../core/foundation/registry.h"
#include "../core/memory/arena_allocator_service.h"

#include <chrono>
#include <iostream>
#include <string_view>

using namespace launcher::core::foundation;
using namespace launcher::core::memory;

int main() {
    ServiceRegistry   reg;
    ArenaAllocatorSvc arena{reg};
    constexpr std::string_view kName = "ArenaAllocatorSvc";

    constexpr std::size_t kIters = 5'000'000; // 5M lookups
    auto start = std::chrono::steady_clock::now();
    for (std::size_t i = 0; i < kIters; ++i) {
        [[maybe_unused]] IService* svc = reg.get(kName);
    }
    auto end = std::chrono::steady_clock::now();
    double ns_per = std::chrono::duration_cast<std::chrono::nanoseconds>(end - start).count() / static_cast<double>(kIters);
    std::cout << "String lookup avg: " << ns_per << " ns\n";

    // Guard: ensure O(1) ~ < 40 ns (generous) to flag regressions
#ifdef NDEBUG
    constexpr double kMaxNs = 40.0;  // Release/RelWithDebInfo guard – regression if slower
#else
    // Debug builds include asserts and lack full optimisation; allow higher bound.
    constexpr double kMaxNs = 200.0;
#endif
    if (ns_per > kMaxNs) {
        std::cerr << "Service name lookup regression: " << ns_per << " ns (limit " << kMaxNs << ")\n";
        return 1;
    }
    return 0;
} 