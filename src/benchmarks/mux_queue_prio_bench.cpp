#include "../core/runtime/mux_queue.hh"

#include <benchmark/benchmark.h>
#include <atomic>
#include <random>
#include <thread>
#include <vector>

using namespace launcher::core;

namespace {

struct Task {
    int         id;
    std::uint8_t priority;
};

constexpr std::size_t kSubCap = 1024;
using QueueT = runtime::MuxQueueBackend<Task, kSubCap, 3>;

std::uint8_t randomPriority(std::mt19937& rng) {
    // 0:15 %, 1:80 %, 2:5 % distribution (high/normal/low)
    std::uniform_int_distribution<int> dist(0, 99);
    int v = dist(rng);
    if (v < 15) return 0;
    if (v < 95) return 1;
    return 2;
}

} // namespace

static void BM_MuxQueuePrio(benchmark::State& state) {
    QueueT q;
    const int producers = static_cast<int>(state.range(0));
    const int ops       = static_cast<int>(state.range(1));

    std::atomic<bool> stop{false};
    std::atomic<int> produced{0};
    std::atomic<int> consumed{0};

    // Consumer thread -----------------------------------------------------
    std::thread consumer([&]() {
        Task task;
        while (!stop.load(std::memory_order_acquire)) {
            if (q.try_pop(task)) {
                ++consumed;
            } else {
                std::this_thread::yield();
            }
        }
        while (q.try_pop(task)) ++consumed;
    });

    // Producer threads ----------------------------------------------------
    std::vector<std::thread> prod_threads;
    prod_threads.reserve(producers);
    for (int p = 0; p < producers; ++p) {
        prod_threads.emplace_back([&, p]() {
            std::mt19937 rng(static_cast<uint32_t>(p + 12345));
            for (auto _ : state) {
                for (int i = 0; i < ops; ++i) {
                    const int id = p * ops + i;
                    Task task{id, randomPriority(rng)};
                    while (!q.try_push(std::move(task))) {
                        benchmark::DoNotOptimize(id);
                    }
                    ++produced;
                }
            }
        });
    }

    for (auto& t : prod_threads) t.join();
    stop.store(true, std::memory_order_release);
    consumer.join();

    state.counters["produced"] = static_cast<double>(produced.load());
    state.counters["consumed"] = static_cast<double>(consumed.load());
    const auto stats = q.stats();
    state.counters["drop"] = static_cast<double>(stats.drop);
    state.counters["high_water_pct"] = static_cast<double>(stats.high_water_pct);
}

BENCHMARK(BM_MuxQueuePrio)->Args({2, 100000});

BENCHMARK_MAIN(); 