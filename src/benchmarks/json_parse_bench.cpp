#include <benchmark/benchmark.h>

#include <string>
#include <string_view>
#include <fstream>
#include <cstring>
#include <yyjson.h>

#if defined(KAI_JSON_PARSER_SIMDJSON)
#include <simdjson.h>
#endif
#include <rapidjson/document.h>

/*
   Simple payload similar to OpenAI chat completions response (truncated).
   Keeping it inline avoids disk IO during benchmark.
*/
static constexpr char kSample<PERSON><PERSON>[] = R"JSON({
  "id": "chatcmpl-abc123",
  "object": "chat.completion",
  "created": 1677858242,
  "choices": [{
    "index": 0,
    "message": {
      "role": "assistant",
      "content": "Hello, world!"
    },
    "finish_reason": "stop"
  }],
  "usage": {
    "prompt_tokens": 13,
    "completion_tokens": 7,
    "total_tokens": 20
  }
})JSON";

constexpr size_t kSampleJsonLen = sizeof(kSampleJson) - 1;

#if defined(KAI_JSON_PARSER_SIMDJSON)
// Precompute padded string for simdjson on-demand parsing
static const simdjson::padded_string kSampleJsonPadded(k<PERSON><PERSON><PERSON><PERSON>, kSampleJsonLen);
#endif

// ---------------- Allocation tracking helpers ------------------------------
struct AllocStats {
    size_t bytes = 0;
    size_t count = 0;
    void reset() { bytes = 0; count = 0; }
};

// ---------------- RapidJSON counting allocator -----------------------------
#include <cstdlib>

struct CountingCrtAllocator {
    static const bool kNeedFree = true;
    static thread_local AllocStats* stats;

    static void* Malloc(size_t size) {
        if (stats) { stats->bytes += size; stats->count += 1; }
        return std::malloc(size);
    }
    static void* Realloc(void* originalPtr, size_t originalSize, size_t newSize) {
        if (stats) { stats->bytes += newSize; stats->count += 1; }
        (void)originalSize; // unused
        return std::realloc(originalPtr, newSize);
    }
    static void Free(void* ptr) { std::free(ptr); }
};
thread_local AllocStats* CountingCrtAllocator::stats = nullptr;

// ---------------- yyjson counting allocator --------------------------------
static void* yyjsonCountMalloc(void* ctx, size_t size) {
    auto* s = static_cast<AllocStats*>(ctx);
    s->bytes += size;
    s->count += 1;
    return std::malloc(size);
}

static void* yyjsonCountRealloc(void* ctx, void* ptr, size_t old_size, size_t size) {
    auto* s = static_cast<AllocStats*>(ctx);
    s->bytes += size;
    s->count += 1;
    (void)old_size;
    return std::realloc(ptr, size);
}

static void yyjsonCountFree(void* /*ctx*/, void* ptr) { std::free(ptr); }

// -----------------------------------------------------------------------------
// RapidJSON DOM parse benchmark
// -----------------------------------------------------------------------------
static void BM_RapidJsonParse(benchmark::State& state) {
    size_t total_bytes = 0, total_cnt = 0;
    AllocStats stats;
    for (auto _ : state) {
        stats.reset();
        CountingCrtAllocator::stats = &stats;
        rapidjson::MemoryPoolAllocator<CountingCrtAllocator> alloc;
        rapidjson::GenericDocument<rapidjson::UTF8<>, rapidjson::MemoryPoolAllocator<CountingCrtAllocator>> d(&alloc);
        d.Parse<rapidjson::kParseStopWhenDoneFlag>(kSampleJson, kSampleJsonLen);
        benchmark::DoNotOptimize(d);
        total_bytes += stats.bytes;
        total_cnt += stats.count;
    }
    state.counters["alloc_bytes"] = static_cast<double>(total_bytes) / state.iterations();
    state.counters["alloc_count"] = static_cast<double>(total_cnt) / state.iterations();
}
BENCHMARK(BM_RapidJsonParse);

// -----------------------------------------------------------------------------
// simdjson on-demand parse benchmark (compiled only when available)
// -----------------------------------------------------------------------------
#if defined(KAI_JSON_PARSER_SIMDJSON)
static void BM_SimdJsonParse(benchmark::State& state) {
    thread_local simdjson::ondemand::parser parser;
    for (auto _ : state) {
        simdjson::ondemand::document doc = parser.iterate(kSampleJsonPadded);
        benchmark::DoNotOptimize(doc);
    }
}
BENCHMARK(BM_SimdJsonParse);
#endif

// -----------------------------------------------------------------------------
// yyjson DOM parse benchmark with allocation tracking
// -----------------------------------------------------------------------------
static void BM_YyjsonParse(benchmark::State& state) {
    // Persistent pool allocator reused across iterations to avoid per-call malloc/free.
    static thread_local char yyjson_pool_buf[16384]; // 16 KiB – sufficient for sample JSON
    yyjson_alc pool_alc{};
    yyjson_alc_pool_init(&pool_alc, yyjson_pool_buf, sizeof(yyjson_pool_buf));

    size_t total_bytes = 0, total_cnt = 0;
    AllocStats stats{}; // single stats holder to track allocations issued by pool
    pool_alc.ctx = &stats;
    pool_alc.malloc = yyjsonCountMalloc;
    pool_alc.realloc = yyjsonCountRealloc;
    pool_alc.free = yyjsonCountFree;

    // Thread-local in-situ buffer initialised once to avoid per-iteration memcpy.
    alignas(64) static thread_local char in_situ[kSampleJsonLen + YYJSON_PADDING_SIZE] = {};
    static thread_local bool buf_init = false;
    if (!buf_init) {
        memcpy(in_situ, kSampleJson, kSampleJsonLen);
        buf_init = true;
    }

    for (auto _ : state) {
        size_t before_bytes = stats.bytes;
        size_t before_cnt = stats.count;

        yyjson_doc* doc = yyjson_read_opts(in_situ, kSampleJsonLen, YYJSON_READ_INSITU, &pool_alc, nullptr);
        benchmark::DoNotOptimize(doc);
        yyjson_doc_free(doc);

        total_bytes += (stats.bytes - before_bytes);
        total_cnt += (stats.count - before_cnt);
    }

    state.counters["alloc_bytes"] = static_cast<double>(total_bytes) / state.iterations();
    state.counters["alloc_count"] = static_cast<double>(total_cnt) / state.iterations();
}
BENCHMARK(BM_YyjsonParse);

BENCHMARK_MAIN(); 