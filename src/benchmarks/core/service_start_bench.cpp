#include "core/foundation/registry.h"
#include "core/memory/arena_allocator_service.h"
#include "core/async/executor_service.h"
#include "core/diagnostics/diagnostics_service.h"
#include "core/events/event_bus_service.h"

#include <benchmark/benchmark.h>

using namespace launcher::core;

static void BM_ServiceStart(benchmark::State& state) {
    for (auto _ : state) {
        foundation::ServiceRegistry reg;

        // Instantiate core services ------------------------------------------------
        memory::ArenaAllocatorSvc arena{reg};
        async::ExecutorService    exec{reg};
        // Instantiate EventBus *before* DiagnosticsService so the latter can
        // safely fetch it via ServiceBase dependency injection. Diagnostics
        // pointer is set later once diag is available.
        events::EventBusService   bus{reg}; // diag==nullptr at ctor
        diagnostics::DiagnosticsService diag{reg};

        // Cross-link diagnostics ---------------------------------------------------
        exec.setDiagnostics(&diag);
        bus.setDiagnostics(&diag);

        // Register services --------------------------------------------------------
        // arena, exec, bus, diag auto-registered by their ctors

        // Lifecycle (measured by SpanGuard inside ServiceRegistry) -----------------
        reg.startAll();
        reg.stopAll();
    }

    // The benchmark framework already reports wall-clock; add custom if desired.
}

BENCHMARK(BM_ServiceStart);

int main(int argc, char** argv) {
    benchmark::Initialize(&argc, argv);
    benchmark::RunSpecifiedBenchmarks();
    // Exit quickly to avoid static dtor ordering issues influencing next tests.
    std::_Exit(0);
} 