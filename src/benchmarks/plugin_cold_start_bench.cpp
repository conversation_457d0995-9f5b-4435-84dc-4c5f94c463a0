#include "../core/async/executor_service.h"
#include "../core/events/event_bus_service.h"
#include "../core/foundation/registry.h"
#include "../core/memory/arena_allocator_service.h"
#include "../core/diagnostics/diagnostics_service.h"
#include "../core/security/verification_store.hh"
#include "../core/plugins/runtime_manager_service.h"

#include <chrono>
#include <filesystem>
#include <fstream>
#include <iostream>
#include <thread>

using namespace launcher::core;

// -----------------------------------------------------------------------------
// Helper – generate N tiny manifest files quickly.  We avoid touching the
// heavyweight shared-library path; RuntimeManagerSvc fast-path parses only
// the TOML manifests, so these 2-3 line files suffice.
// -----------------------------------------------------------------------------
static void createDummyManifests(const std::filesystem::path& dir, int count) {
    std::error_code ec;
    std::filesystem::create_directories(dir, ec);
    for (int i = 0; i < count; ++i) {
        std::ofstream out(dir / ("dummy_" + std::to_string(i) + ".toml"));
        out << "id = \"dummy_" << i << "\"\n";
        out << "runtime = \"null\"\n";
    }
}

int main() {
    constexpr int kPlugins = 5000;

    // ----------------------------------------------------------------------------------
    // Boot minimal service graph required by RuntimeManagerSvc (Arena, Executor, Bus).
    // ----------------------------------------------------------------------------------
    foundation::ServiceRegistry registry;
    memory::ArenaAllocatorSvc    arena{registry};
    async::ExecutorService       exec{registry};
    exec.start();
    events::EventBusService      bus{registry, nullptr};
    bus.start();
    diagnostics::DiagnosticsService diag{registry};
    diag.start();

    security::VerificationStore  vstore;
    plugins::RuntimeManagerSvc   rms{registry, vstore};

    // ----------------------------------------------------
    // Populate temp dir with synthetic manifests.
    // ----------------------------------------------------
    const auto temp_root = std::filesystem::temp_directory_path() / "plugin_cold_start_bench";
    std::filesystem::remove_all(temp_root);
    createDummyManifests(temp_root, kPlugins);

    // Inject additional search path by putting manifests under ./Plugins where
    // loadManifests() expects them.  Create sub-dir accordingly.
    const auto pluginsDir = temp_root / "Plugins";
    std::error_code ec;
    std::filesystem::create_directories(pluginsDir, ec);
    // move manifests into Plugins/ to satisfy default path logic
    for (const auto& entry : std::filesystem::directory_iterator(temp_root)) {
        if (entry.is_regular_file()) {
            std::filesystem::rename(entry.path(), pluginsDir / entry.path().filename(), ec);
        }
    }

    // Change cwd to temp_root so that relative "./Plugins" path resolves.
    std::filesystem::current_path(temp_root, ec);

    // ----------------------------------------------------
    auto t0 = std::chrono::high_resolution_clock::now();
    auto res = rms.loadManifests();
    auto t1 = std::chrono::high_resolution_clock::now();

    if (!res) {
        std::cerr << "loadManifests failed: " << static_cast<int>(res.error()) << "\n";
        return 1;
    }

    const auto ms = std::chrono::duration_cast<std::chrono::milliseconds>(t1 - t0).count();
    const auto discovered = rms.registry().list().size();

    std::cout << "Cold-start: discovered " << discovered << "/" << kPlugins << " plugins in " << ms << " ms\n";

    if (discovered < static_cast<std::size_t>(kPlugins)) {
        std::cerr << "Discovery failed – missing plugins\n";
        return 1;
    }
    if (ms > 800) {
        std::cerr << "Cold-start regression: took " << ms << " ms (>800)\n";
        return 1;
    }

    diag.stop();
    bus.stop();
    exec.stop();
    return 0;
} 