#include "../core/runtime/mux_queue.hh"

#include <benchmark/benchmark.h>
#include <atomic>
#include <chrono>
#include <random>
#include <thread>
#include <vector>
#include <mutex>

using namespace launcher::core;

namespace {
struct Task {
    int64_t id;
    std::uint8_t priority;
};

constexpr std::size_t kCap = 1024;
using QueueT = runtime::MuxQueueBackend<Task, kCap, 3>;

std::uint8_t distPrio(std::mt19937& rng) {
    std::uniform_int_distribution<int> d(0, 99);
    int v = d(rng);
    if (v < 15) return 0; // high
    if (v < 95) return 1; // normal
    return 2;            // low
}
}

static void BM_MuxQueueStarvation(benchmark::State& state) {
    QueueT q;
    const int producers = 2;
    const int ops       = static_cast<int>(state.range(0)); // total ops per thread

    std::atomic<bool> stop{false};
    std::vector<long long> low_latencies; // microseconds
    low_latencies.reserve(ops);
    std::mutex lat_mtx;

    // Consumer -----------------------------------------------------------
    std::thread consumer([&]() {
        using clock = std::chrono::steady_clock;
        Task t;
        while (!stop.load(std::memory_order_acquire) || q.size() != 0) {
            if (q.try_pop(t)) {
                if (t.priority == 2) {
                    auto now = clock::now();
                    auto push_tp = std::chrono::steady_clock::time_point(std::chrono::microseconds(t.id));
                    auto us  = std::chrono::duration_cast<std::chrono::microseconds>(now - push_tp).count();
                    std::scoped_lock lk(lat_mtx);
                    low_latencies.push_back(us);
                }
            } else {
                std::this_thread::yield();
            }
        }
    });

    // Producers ----------------------------------------------------------
    std::vector<std::thread> prod_vec;
    prod_vec.reserve(producers);
    for (int p = 0; p < producers; ++p) {
        prod_vec.emplace_back([&, p]() {
            std::mt19937 rng(static_cast<uint32_t>(p + 123));
            using clock = std::chrono::steady_clock;
            while (!stop.load(std::memory_order_acquire)) {
                for (int i = 0; i < ops && !stop.load(std::memory_order_acquire); ++i) {
                    std::uint8_t pr = distPrio(rng);
                    Task task;
                    task.priority = pr;
                    if (pr == 2) {
                        auto tp = clock::now();
                        auto us = std::chrono::duration_cast<std::chrono::microseconds>(tp.time_since_epoch()).count();
                        task.id = static_cast<int64_t>(us);
                    } else {
                        task.id = i;
                    }
                    // Attempt to push; give up quickly if stop flag is set to
                    // avoid spinning forever when the queue is full after the
                    // benchmark ends.
                    while (!stop.load(std::memory_order_acquire) && !q.try_push(std::move(task))) {
                        benchmark::DoNotOptimize(task.id);
                    }
                    if (stop.load(std::memory_order_acquire)) break; // outer for-loop guard
                }
            }
        });
    }

    // Main benchmark loop – only the main thread interacts with Google
    // Benchmark's timing API, ensuring we respect its single-threaded
    // invariants. We simply spin in the outer loop for the benchmark's
    // requested duration while producer/consumer threads exercise the
    // queue in the background.
    for (auto _ : state) {
        benchmark::DoNotOptimize(q.size());
    }

    stop.store(true, std::memory_order_release);
    for (auto& t : prod_vec) t.join();
    consumer.join();

    // Compute ratio >100 us
    size_t exceed = 0;
    {
        std::scoped_lock lk(lat_mtx);
        for (auto us : low_latencies) if (us > 100) ++exceed;
        double pct = (low_latencies.empty() ? 0.0 : (static_cast<double>(exceed) * 100.0 / low_latencies.size()));
        state.counters["starve_pct"] = pct;
        if (pct > 1.0) {
            state.SkipWithError("Starvation percentile exceeded 1% budget");
        }
    }
}

BENCHMARK(BM_MuxQueueStarvation)->Arg(5000);
BENCHMARK_MAIN(); 