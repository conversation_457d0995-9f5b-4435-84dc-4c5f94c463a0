#include "../core/async/executor_service.h"
#include "../core/events/event_bus_service.h"
#include "../core/foundation/registry.h"
#include "../core/memory/arena_allocator_service.h"
#include "../core/runtime/runtime_scanner.hh"

#include <chrono>
#include <filesystem>
#include <fstream>
#include <iostream>
#include <random>
#include <thread>
#include <atomic>

using namespace launcher::core;

static void createDummyFiles(const std::filesystem::path& dir, int count) {
    std::error_code ec;
    std::filesystem::create_directories(dir, ec);
    std::mt19937 rng{1337};
    std::uniform_int_distribution<uint8_t> dist(0, 255);
    for (int i = 0; i < count; ++i) {
        auto file_path = dir / ("runtime_" + std::to_string(i));
        std::ofstream out(file_path, std::ios::binary);
        char buf[4096];
        for (auto& b : buf) b = static_cast<char>(dist(rng));
        out.write(buf, sizeof(buf));
    }
}

int main() {
    constexpr int kRuntimes = 200;

    auto temp_root = std::filesystem::temp_directory_path() / "runtime_scanner_bench";
    std::filesystem::remove_all(temp_root);
    createDummyFiles(temp_root, kRuntimes);

    foundation::ServiceRegistry registry;
    memory::ArenaAllocatorSvc    arena{registry};
    async::ExecutorService       exec{registry};
    exec.start();
    events::EventBusService      bus{registry};
    bus.start();

    std::atomic<int> discovered{0};
    auto sub = bus.subscribe<runtime::RuntimeDiscoveredEvent>([&](const runtime::RuntimeDiscoveredEvent* /*evt_ptr*/) {
        discovered.fetch_add(1, std::memory_order_relaxed);
    });

    runtime::RuntimeScanner scanner(exec, bus, {temp_root});
    if (!scanner.start()) {
        std::cerr << "Failed to start scanner\n";
        return 1;
    }

    auto t0 = std::chrono::high_resolution_clock::now();

    // Wait until all runtimes discovered or timeout 2 s.
    const auto deadline = t0 + std::chrono::seconds(2);
    while (discovered.load(std::memory_order_relaxed) < kRuntimes && std::chrono::high_resolution_clock::now() < deadline) {
        std::this_thread::sleep_for(std::chrono::milliseconds(1));
    }

    auto t1 = std::chrono::high_resolution_clock::now();
    auto ms = std::chrono::duration_cast<std::chrono::milliseconds>(t1 - t0).count();

    std::cout << "Cold scan " << discovered.load() << "/" << kRuntimes << " runtimes in " << ms << " ms\n";

    auto stats = scanner.stats();
    std::cout << "queue_drop=" << stats.drop << "\n";

    scanner.stop();
    exec.stop();
    bus.stop();

    if (discovered.load() < kRuntimes) {
        std::cerr << "Scanner failed to discover all runtimes\n";
        return 1;
    }

    if (ms > 150) {
        std::cerr << "Cold scan regression: took " << ms << " ms (>150)\n";
        return 1;
    }

    return 0;
} 