// Baseline micro-benchmarks for LLM core – Phase 0
// ---------------------------------------------------
// Measures latency and heap allocation of two hot paths:
//   1. JSON request construction (OpenAI-style)
//   2. SSE decoding (OpenAI-style)
//
// The executable prints human-readable results to stdout. Results are *rough*
// but give a stable baseline for later regression tracking.
//
// Build: part of CMake target `llm_baseline_bench`
// Usage: ./llm_baseline_bench | cat

#include <chrono>
#include <iostream>
#include <string>
#include <string_view>
#include <atomic>
#include <vector>
#include <nlohmann/json.hpp>
#include <cstdlib>

#include "core/http/sse_decoder.h"
#include "core/providers/openai/openai_chat_api.h"
#include "core/context/context.h"
#include "core/memory/slab_arena.h"

// ---------------------------------------------------------------------------
// Simple global allocation counter – counts *bytes* requested via operator new.
// NOTE: This is limited (does not track frees, aligned_new, placement new, etc.)
// but works well enough for differential comparisons between code revisions.
// ---------------------------------------------------------------------------
static std::atomic<size_t> g_alloc_bytes{0};

void* operator new(std::size_t sz) {
    g_alloc_bytes.fetch_add(sz, std::memory_order_relaxed);
    return std::malloc(sz);
}

void operator delete(void* ptr) noexcept {
    std::free(ptr);
}

void* operator new[](std::size_t sz) {
    g_alloc_bytes.fetch_add(sz, std::memory_order_relaxed);
    return std::malloc(sz);
}

void operator delete[](void* ptr) noexcept {
    std::free(ptr);
}

void* operator new(std::size_t sz, const std::nothrow_t&) noexcept {
    g_alloc_bytes.fetch_add(sz, std::memory_order_relaxed);
    return std::malloc(sz);
}

void* operator new[](std::size_t sz, const std::nothrow_t&) noexcept {
    g_alloc_bytes.fetch_add(sz, std::memory_order_relaxed);
    return std::malloc(sz);
}

void operator delete(void* ptr, const std::nothrow_t&) noexcept {
    std::free(ptr);
}

void operator delete[](void* ptr, const std::nothrow_t&) noexcept {
    std::free(ptr);
}

// Utility to measure a callable and return {elapsed_ns, bytes_allocated}
struct BenchmarkResult {
    double ms;    // milliseconds
    size_t bytes; // bytes allocated during call
};

template <typename Fn>
BenchmarkResult measure(Fn&& fn) {
    size_t before = g_alloc_bytes.load(std::memory_order_relaxed);
    auto t0 = std::chrono::high_resolution_clock::now();

    fn();

    auto t1 = std::chrono::high_resolution_clock::now();
    size_t after = g_alloc_bytes.load(std::memory_order_relaxed);

    double ms = std::chrono::duration<double, std::milli>(t1 - t0).count();
    return {ms, after - before};
}

// ---------------------------------------------------------------------------
// 1) JSON request build benchmark (simplified OpenAI model)
// ---------------------------------------------------------------------------
static BenchmarkResult bench_json_build(size_t iterations = 1000) {
    const std::string model_name = "gpt-4o";
    const std::vector<std::pair<std::string, std::string>> context_msgs = {
        {"system", "You are a helpful assistant."},
        {"user", "Who won the world series in 2020?"},
        {"assistant", "The Los Angeles Dodgers won the 2020 World Series."},
    };
    const std::string prompt = "Where was it played?";

    return measure([&]() {
        for (size_t i = 0; i < iterations; ++i) {
            nlohmann::json body;
            body["model"] = model_name;
            body["temperature"] = 0.7;
            body["max_tokens"] = 64;

            nlohmann::json msgs = nlohmann::json::array();
            for (const auto& [role, content] : context_msgs) {
                msgs.push_back({{"role", role}, {"content", content}});
            }
            msgs.push_back({{"role", "user"}, {"content", prompt}});
            body["messages"] = std::move(msgs);

            std::string dumped = body.dump();
            // prevent optimisation-out
            asm volatile("" :: "r"(dumped.data()) : "memory");
        }
    });
}

// ---------------------------------------------------------------------------
// 1b) RapidJSON build via real OpenAIModel::buildChatPayload
// ---------------------------------------------------------------------------
static BenchmarkResult bench_rapidjson_build(size_t iterations = 1000) {
    using namespace launcher::core;

    openai::ChatApi chatApi;
    Context dummyCtx; // empty
    openai::Options opts{};

    return measure([&]() {
        for (size_t i = 0; i < iterations; ++i) {
            rapidjson::StringBuffer buf; buf.Reserve(1024);
            rapidjson::Writer<rapidjson::StringBuffer> writer(buf);
            chatApi.buildPayload(writer, "hello", dummyCtx, opts, "gpt-3.5-turbo");
            asm volatile("" :: "r"(buf.GetString()) : "memory");
        }
    });
}

// ---------------------------------------------------------------------------
// 2) SSE decoding benchmark (simplified OpenAI delta stream)
// ---------------------------------------------------------------------------
static std::vector<std::string> make_fake_sse_stream(size_t chunks = 1000) {
    std::vector<std::string> out;
    out.reserve(chunks + 1);
    const std::string kPayloadPrefix =
        "data: {\"choices\":[{\"delta\":{\"content\":\"";
    const std::string kPayloadSuffix = "\"}}]}\n";

    for (size_t i = 0; i < chunks; ++i) {
        std::string word = (i % 2 == 0) ? "Hello " : "world! ";
        out.emplace_back(kPayloadPrefix + word + kPayloadSuffix);
    }
    out.emplace_back("data: [DONE]\n");
    return out;
}

// RapidJSON SAX-based decoder (current implementation)
static BenchmarkResult bench_sse_decode_sax(size_t chunks = 1000) {
    const auto data = make_fake_sse_stream(chunks);
    return measure([&]() {
        auto arena = std::make_shared<launcher::core::memory::SlabArena>();
        auto decoder = http::SSEDecoder::create("openai");
        decoder->setSink(arena);
        for (const auto& line_str : data) { // line_str is std::string
            std::string_view line_sv = line_str; // Create string_view
            auto token = decoder->feed(line_sv);
            // prevent optimisation-out
            asm volatile("" :: "r"(&token) : "memory");
        }
    });
}

// Legacy nlohmann/json DOM-based extraction – for comparison only
static BenchmarkResult bench_sse_decode_nlohmann(size_t chunks = 1000) {
    const auto data = make_fake_sse_stream(chunks);
    return measure([&]() {
        for (const auto &line_str : data) { // line_str is std::string
            std::string_view line_sv = line_str; // Create string_view
            if (line_sv.rfind("data: ", 0) == 0) {
                std::string_view payload_sv = line_sv.substr(6);
                std::string payload_str(payload_sv); // nlohmann::json::parse needs std::string or char*
                if (payload_sv == "[DONE]\n" || payload_sv == "[DONE]") {
                    continue;
                }
                auto j = nlohmann::json::parse(payload_str, nullptr, false);
                if (!j.is_object() || !j.contains("choices")) continue;
                const auto &choices = j["choices"];
                if (!choices.is_array() || choices.empty()) continue;
                const auto &deltaObj = choices[0].value("delta", nlohmann::json{});
                if (deltaObj.contains("content")) {
                    volatile auto tmp = deltaObj["content"].get<std::string>();
                    (void)tmp;
                }
            }
        }
    });
}

int main() {
    const size_t json_iters   = 2000;
    const size_t sse_chunks   = 5000;

    auto json_res = bench_json_build(json_iters);
    auto rjson_res = bench_rapidjson_build(json_iters);
    auto sse_sax  = bench_sse_decode_sax(sse_chunks);
    auto sse_dom  = bench_sse_decode_nlohmann(sse_chunks);

    std::cout << "LLM Baseline Benchmarks (build #0)\n";
    std::cout << "----------------------------------\n";
    std::cout << "nlohmann JSON build (" << json_iters << " iters): "
              << json_res.ms << " ms, " << json_res.bytes/1024 << " KiB alloc\n";
    std::cout << "RapidJSON build   (" << json_iters << " iters): "
              << rjson_res.ms << " ms, " << rjson_res.bytes/1024 << " KiB alloc\n";
    std::cout << "SSE decode (RapidJSON SAX, " << sse_chunks << " chunks): "
              << sse_sax.ms << " ms, " << sse_sax.bytes/1024 << " KiB alloc\n";
    std::cout << "SSE decode (nlohmann DOM,  " << sse_chunks << " chunks): "
              << sse_dom.ms << " ms, " << sse_dom.bytes/1024 << " KiB alloc\n";
    return 0;
} 