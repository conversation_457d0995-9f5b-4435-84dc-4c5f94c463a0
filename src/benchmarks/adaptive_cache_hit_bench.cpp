#include <benchmark/benchmark.h>
#include "core/container/adaptive_cache.hh"

using namespace launcher::core::container;

static void BM_AdaptiveCache_Hit(benchmark::State& state) {
    AdaptiveCache<uint64_t, uint64_t> cache(1024);

    // Pre-fill
    for (uint64_t i = 0; i < 1024; ++i) {
        cache.getOrInsert(i, [i] { return i; });
    }

    uint64_t key = 42;
    cache.getOrInsert(key, [] { return 1337ull; }); // ensure key present

    for (auto _ : state) {
        benchmark::DoNotOptimize(cache.getOrInsert(key, [] { return 1337ull; }));
    }
}

BENCHMARK(BM_AdaptiveCache_Hit)->Iterations(1'000'000);

BENCHMARK_MAIN(); 