set(BENCHMARK_ENABLE_DOXYGEN OFF CACHE BOOL "Disable Google Benchmark docs" FORCE)

add_executable(llm_baseline_bench
    llm_baseline_bench.cpp
)

add_executable(event_bus_bench
    event_bus_bench.cpp
)

add_executable(registry_lookup_bench
    registry_lookup_bench.cpp
)

# Link against the necessary libraries
# llm pulls http_client, context, config transitively.
# We link http_client directly for SSEDecoder heavy tests.

target_link_libraries(llm_baseline_bench
    PRIVATE
    llm
    http_client
    nlohmann_json::nlohmann_json
)

target_include_directories(llm_baseline_bench
    PRIVATE
    ${CMAKE_SOURCE_DIR}/src
)

target_link_libraries(event_bus_bench
    PRIVATE
    core
)

target_include_directories(event_bus_bench PRIVATE ${CMAKE_SOURCE_DIR}/src)

target_link_libraries(registry_lookup_bench PRIVATE core)

# Optimise micro-benchmarks even in Debug builds to keep runtime short and
# thresholds meaningful. We do not switch the whole build to Release – only
# these targets.
target_compile_options(registry_lookup_bench PRIVATE -O3 -DNDEBUG)

target_include_directories(registry_lookup_bench PRIVATE ${CMAKE_SOURCE_DIR}/src)

# -------------------------------------------------------------------
# CI perf-gate: run micro-benchmarks as CTest tests. They return
# non-zero exit status on regression, so no extra parsing is needed.
# -------------------------------------------------------------------

add_test(NAME EventBusBench COMMAND event_bus_bench --benchmark_min_time=0.2s)
add_test(NAME RegistryLookupBench COMMAND registry_lookup_bench --benchmark_min_time=0.2s)

# Tag micro-benchmarks so developers can opt-in via `ctest -L bench`
set_tests_properties(EventBusBench PROPERTIES LABELS bench)
set_tests_properties(RegistryLookupBench PROPERTIES LABELS bench)

# Fetch Google Benchmark
include(FetchContent)
set(BENCHMARK_ENABLE_TESTING OFF CACHE BOOL "Disable benchmark tests" FORCE)
set(BENCHMARK_ENABLE_GTEST_TESTS OFF CACHE BOOL "Disable benchmark gtest" FORCE)
FetchContent_Declare(
    googlebench
    GIT_REPOSITORY https://github.com/google/benchmark.git
    GIT_TAG v1.8.3
)
FetchContent_MakeAvailable(googlebench)

# Ensure benchmark links pthread
if(NOT WIN32)
    set(THREADS_PREFER_PTHREAD_FLAG ON)
    find_package(Threads REQUIRED)
endif()

add_executable(eventbus_throughput_bench
    eventbus_throughput.cpp
)

target_link_libraries(eventbus_throughput_bench PRIVATE core benchmark::benchmark Threads::Threads)

target_include_directories(eventbus_throughput_bench PRIVATE ${CMAKE_SOURCE_DIR}/src)

# Optimise throughput micro-benchmark to avoid CI flakiness.
target_compile_options(eventbus_throughput_bench PRIVATE -O3 -DNDEBUG)

add_test(NAME EventBusThroughput COMMAND eventbus_throughput_bench --benchmark_min_time=0.2s)
set_tests_properties(EventBusThroughput PROPERTIES LABELS bench)

# -------------- Service string lookup bench -------------------------------
add_executable(service_string_lookup_bench
    service_string_lookup_bench.cpp
)

target_link_libraries(service_string_lookup_bench PRIVATE core)

# Same optimisation flags for name-based lookup bench.
target_compile_options(service_string_lookup_bench PRIVATE
    $<$<CONFIG:Release>:-O3;-DNDEBUG>
    $<$<CONFIG:RelWithDebInfo>:-O3;-DNDEBUG>
    $<$<CONFIG:MinSizeRel>:-O3;-DNDEBUG>
)

# Disable sanitiser instrumentation for this micro-benchmark – the additional
# runtime overhead skews the 40 ns lookup guard causing CI failures even when
# the implementation is healthy.  Clang/GCC accept `-fno-sanitize=all` to
# negate earlier global `-fsanitize=` flags applied via ENABLE_ASAN / ENABLE_TSAN.
if(CMAKE_CXX_COMPILER_ID MATCHES "Clang|AppleClang|GNU")
    target_compile_options(service_string_lookup_bench PRIVATE -fno-sanitize=all)
    target_link_options(service_string_lookup_bench PRIVATE -fno-sanitize=all)
endif()

add_test(NAME ServiceStringLookup COMMAND service_string_lookup_bench --benchmark_min_time=0.2s)
set_tests_properties(ServiceStringLookup PROPERTIES LABELS bench)

# -------------- Service start/stop bench ------------------------------------
add_executable(service_start_bench
    core/service_start_bench.cpp
)

target_link_libraries(service_start_bench PRIVATE core benchmark::benchmark Threads::Threads)

target_include_directories(service_start_bench PRIVATE ${CMAKE_SOURCE_DIR}/src)

add_test(NAME ServiceStartBench COMMAND service_start_bench --benchmark_repetitions=1 --benchmark_min_time=0.2s)
set_tests_properties(ServiceStartBench PROPERTIES LABELS bench)

add_executable(adaptive_cache_hit_bench
    adaptive_cache_hit_bench.cpp
)

target_link_libraries(adaptive_cache_hit_bench PRIVATE benchmark::benchmark Threads::Threads)

target_include_directories(adaptive_cache_hit_bench PRIVATE ${CMAKE_SOURCE_DIR}/src)

# Optimise micro-benchmark
target_compile_options(adaptive_cache_hit_bench PRIVATE -O3 -DNDEBUG)

# If developer wants to debug UB, compile with ASan for this target.
if(DEFINED ENV{KAI_ENABLE_ASAN_BENCH})
    target_compile_options(adaptive_cache_hit_bench PRIVATE -fsanitize=address -fno-omit-frame-pointer)
    target_link_options(adaptive_cache_hit_bench PRIVATE -fsanitize=address -fno-omit-frame-pointer)
endif()

add_test(NAME AdaptiveCacheHitBench COMMAND adaptive_cache_hit_bench --benchmark_min_time=0.2s)

set_tests_properties(AdaptiveCacheHitBench PROPERTIES LABELS bench)

add_executable(adaptive_cache_mixed_bench
    adaptive_cache_mixed_bench.cpp
)

target_link_libraries(adaptive_cache_mixed_bench PRIVATE benchmark::benchmark Threads::Threads)

target_include_directories(adaptive_cache_mixed_bench PRIVATE ${CMAKE_SOURCE_DIR}/src)

target_compile_options(adaptive_cache_mixed_bench PRIVATE -O3 -DNDEBUG)

add_test(NAME AdaptiveCacheMixedBench COMMAND adaptive_cache_mixed_bench --benchmark_min_time=0.2s)
set_tests_properties(AdaptiveCacheMixedBench PROPERTIES LABELS bench)

add_executable(ring_queue_bench
    ring_queue_bench.cpp
)

target_link_libraries(ring_queue_bench PRIVATE core benchmark::benchmark Threads::Threads)

target_include_directories(ring_queue_bench PRIVATE ${CMAKE_SOURCE_DIR}/src)

target_compile_options(ring_queue_bench PRIVATE -O3 -DNDEBUG)

## RingQueueBench intentionally not registered with CTest due to flaky
# thread-affinity issues on macOS. Developers can run it manually via
# `./build/bin/ring_queue_bench`. 

add_executable(runtime_scanner_cold_scan
    runtime_scanner_cold_scan.cpp
)

target_link_libraries(runtime_scanner_cold_scan PRIVATE core Threads::Threads)

target_include_directories(runtime_scanner_cold_scan PRIVATE ${CMAKE_SOURCE_DIR}/src)

# Ensure cold-scan benchmark is not affected by build-time overrides of
# bounded queue capacities.  For correctness benchmarks we want *no* event
# drops or executor back-pressure.
target_compile_definitions(runtime_scanner_cold_scan PRIVATE 
    KAI_EXECUTOR_CAPACITY=0 
    KAI_EVENTBUS_CAPACITY=0)

add_test(NAME RuntimeScannerColdScan COMMAND runtime_scanner_cold_scan)
set_tests_properties(RuntimeScannerColdScan PROPERTIES LABELS bench)

add_executable(probe_cache_hit_bench
    probe_cache_hit_bench.cpp
)

target_link_libraries(probe_cache_hit_bench PRIVATE snapshot_lib benchmark::benchmark Threads::Threads)

target_include_directories(probe_cache_hit_bench PRIVATE ${CMAKE_SOURCE_DIR}/src)

target_compile_options(probe_cache_hit_bench PRIVATE -O3 -DNDEBUG)

add_test(NAME ProbeCacheHitBench COMMAND probe_cache_hit_bench --benchmark_min_time=0.2s)
set_tests_properties(ProbeCacheHitBench PROPERTIES LABELS bench)

add_executable(verifier_pipeline_bench
    verifier_pipeline_bench.cpp
)

target_link_libraries(verifier_pipeline_bench PRIVATE core benchmark::benchmark Threads::Threads)

target_include_directories(verifier_pipeline_bench PRIVATE ${CMAKE_SOURCE_DIR}/src)

target_compile_options(verifier_pipeline_bench PRIVATE -O3 -DNDEBUG)

add_test(NAME VerifierPipelineBench COMMAND verifier_pipeline_bench --benchmark_min_time=0.2s)
set_tests_properties(VerifierPipelineBench PROPERTIES LABELS bench)

# ----------------- MuxQueue benchmark (optional via flag) -------------------
if(KAI_ENABLE_MUX_QUEUE)
    add_executable(mux_queue_prio_bench
        mux_queue_prio_bench.cpp
    )

    add_executable(mux_queue_starvation_bench
        mux_queue_starvation_bench.cpp
    )

    target_link_libraries(mux_queue_prio_bench PRIVATE core benchmark::benchmark Threads::Threads)
    target_include_directories(mux_queue_prio_bench PRIVATE ${CMAKE_SOURCE_DIR}/src)
    target_compile_options(mux_queue_prio_bench PRIVATE -O3 -DNDEBUG)

    target_link_libraries(mux_queue_starvation_bench PRIVATE core benchmark::benchmark Threads::Threads)
    target_include_directories(mux_queue_starvation_bench PRIVATE ${CMAKE_SOURCE_DIR}/src)
    target_compile_options(mux_queue_starvation_bench PRIVATE -O3 -DNDEBUG)

    add_test(NAME MuxQueueStarvationBench COMMAND mux_queue_starvation_bench --benchmark_min_time=0.2s)
    set_tests_properties(MuxQueueStarvationBench PROPERTIES LABELS bench)
endif()

add_executable(runtime_scan_200_bench
    runtime_scan_200_bench.cpp
)

target_link_libraries(runtime_scan_200_bench PRIVATE core benchmark::benchmark Threads::Threads)

target_include_directories(runtime_scan_200_bench PRIVATE ${CMAKE_SOURCE_DIR}/src)

target_compile_options(runtime_scan_200_bench PRIVATE -O3 -DNDEBUG)

target_compile_definitions(runtime_scan_200_bench PRIVATE 
    KAI_EXECUTOR_CAPACITY=0 
    KAI_EVENTBUS_CAPACITY=0)

add_test(NAME RuntimeScan200Bench COMMAND runtime_scan_200_bench --benchmark_format=json --benchmark_out=perf_runtime_scan_200_current.json)
set_tests_properties(RuntimeScan200Bench PROPERTIES LABELS bench)

# ---------------------------------------------------------------------------
# JSON parse micro-bench
# ---------------------------------------------------------------------------

add_executable(json_parse_bench
    json_parse_bench.cpp
)

target_link_libraries(json_parse_bench PRIVATE benchmark::benchmark Threads::Threads
    yyjson
    $<$<STREQUAL:${KAI_JSON_PARSER},simdjson>:simdjson>
)
target_include_directories(json_parse_bench PRIVATE ${CMAKE_SOURCE_DIR}/src)

# Optimise for stable timing
target_compile_options(json_parse_bench PRIVATE -O3 -DNDEBUG)

add_test(NAME JsonParseBench COMMAND json_parse_bench --benchmark_min_time=0.1s)
set_tests_properties(JsonParseBench PROPERTIES LABELS bench)

# ---------------------------------------------------------------------------
# SSE decoder micro-bench
# ---------------------------------------------------------------------------

add_executable(sse_decoder_bench
    sse_decoder_bench.cpp
)

target_link_libraries(sse_decoder_bench PRIVATE core benchmark::benchmark Threads::Threads
    $<$<STREQUAL:${KAI_JSON_PARSER},simdjson>:simdjson>
)
target_include_directories(sse_decoder_bench PRIVATE ${CMAKE_SOURCE_DIR}/src)

target_compile_options(sse_decoder_bench PRIVATE -O3 -DNDEBUG)

add_test(NAME SseDecoderBench COMMAND sse_decoder_bench --benchmark_min_time=0.1s)
set_tests_properties(SseDecoderBench PROPERTIES LABELS bench)

add_executable(plugin_cold_start_bench
    plugin_cold_start_bench.cpp
)

target_link_libraries(plugin_cold_start_bench
    PRIVATE
        core
        snapshot_lib
        Threads::Threads)

target_include_directories(plugin_cold_start_bench PRIVATE ${CMAKE_SOURCE_DIR}/src)

target_compile_options(plugin_cold_start_bench PRIVATE -O3 -DNDEBUG)

set_target_properties(plugin_cold_start_bench PROPERTIES
    CXX_STANDARD 23
    CXX_STANDARD_REQUIRED ON
    CXX_EXTENSIONS OFF
)

add_test(NAME PluginColdStartBench COMMAND plugin_cold_start_bench)
set_tests_properties(PluginColdStartBench PROPERTIES LABELS bench) 