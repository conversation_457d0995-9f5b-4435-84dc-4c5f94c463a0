#include <benchmark/benchmark.h>

// Disable heavy runtime verifications for micro-benchmark – we are interested
// in framework overhead, not Security.framework latency.
#define KAI_DISABLE_VERIFIER_RUNTIME
#include "core/security/verifier_strategy.hh"

using namespace launcher::core::security;

static void BM_VerifierPipeline(benchmark::State& state) {
    CDHash dummy{};
    for (auto _ : state) {
        auto v = VerifierStrategy<kPolicyAll>::verify(dummy);
        benchmark::DoNotOptimize(v);
    }
}

BENCHMARK(BM_VerifierPipeline)->Iterations(1'000'000);

BENCHMARK_MAIN(); 