#include "../core/async/executor_service.h"
#include "../core/events/event_bus_service.h"
#include "../core/foundation/registry.h"
#include "../core/memory/arena_allocator_service.h"
#include "../core/runtime/runtime_scanner.hh"

#include <benchmark/benchmark.h>

#include <atomic>
#include <chrono>
#include <filesystem>
#include <fstream>
#include <random>
#include <thread>
#include <cstdio>

using namespace launcher::core;
using namespace std::chrono_literals;

namespace {

// ---------------------------------------------------------------------
// Helper that creates \p count dummy files under \p dir.  Each file is
// 4 KiB filled with pseudo-random bytes so the SHA-256 calculation in
// RuntimeScanner performs real work while remaining deterministic.
// ---------------------------------------------------------------------
static void createDummyFiles(const std::filesystem::path& dir, int count) {
    std::error_code ec;
    std::filesystem::create_directories(dir, ec);
    std::mt19937 rng{1337};
    std::uniform_int_distribution<uint8_t> dist(0, 255);
    for (int i = 0; i < count; ++i) {
        auto file_path = dir / ("runtime_" + std::to_string(i));
        std::ofstream out(file_path, std::ios::binary);
        char buf[4096];
        for (auto& b : buf) b = static_cast<char>(dist(rng));
        out.write(buf, sizeof(buf));
    }
}

} // namespace

// ---------------------------------------------------------------------
// Google-Benchmark entry point measuring a *cold* scan of 200 runtimes.
// The benchmark fails via SkipWithError when the wall-clock latency
// exceeds 150 ms or when not all runtimes are discovered.
// ---------------------------------------------------------------------
static void BM_RuntimeScan200(benchmark::State& state) {
    constexpr int kRuntimes = 200;

    for (auto _ : state) {
        // Environment setup – not part of timed section under UseManualTime

        // Create a unique temp directory for this iteration – prevents OS
        // FS cache from skewing results when the benchmark is executed
        // multiple times on the same runner.
        const auto unique_tag = std::to_string(std::chrono::steady_clock::now().time_since_epoch().count());
        const auto temp_root  = std::filesystem::temp_directory_path() / ("runtime_scan_bench_" + unique_tag);
        std::filesystem::remove_all(temp_root);
        createDummyFiles(temp_root, kRuntimes);

        foundation::ServiceRegistry registry;
        memory::ArenaAllocatorSvc    arena{registry};
        async::ExecutorService       exec{registry};
        exec.start();
        events::EventBusService      bus{registry};
        bus.start();

        std::atomic<int> discovered{0};
        auto sub = bus.subscribe<runtime::RuntimeDiscoveredEvent>([&](const runtime::RuntimeDiscoveredEvent* /*evt_ptr*/) {
            discovered.fetch_add(1, std::memory_order_relaxed);
        });

        runtime::RuntimeScanner scanner(exec, bus, {temp_root});
        if (!scanner.start()) {
            state.SkipWithError("RuntimeScanner failed to start");
            return;
        }

        const auto t0 = std::chrono::high_resolution_clock::now();

        const auto deadline = t0 + 2s; // hard 2-second timeout safeguard
        while (discovered.load(std::memory_order_relaxed) < kRuntimes &&
               std::chrono::high_resolution_clock::now() < deadline) {
            std::this_thread::sleep_for(1ms);
        }
        const auto t1 = std::chrono::high_resolution_clock::now();
        const double wall_sec = std::chrono::duration<double>(t1 - t0).count();

        // Cleanup – still outside the measured time window (manual timing)

        scanner.stop();
        exec.stop();
        bus.stop();
        sub.unsubscribe();
        std::filesystem::remove_all(temp_root);

        // -------------------- validation / perf gate --------------------
        if (discovered.load(std::memory_order_relaxed) < kRuntimes) {
            state.SkipWithError("Failed to discover all runtimes (" + std::to_string(discovered.load()) + "/200)");
        } else if (wall_sec * 1e3 /*ms*/ > 150.0) {
            char msg[128];
            std::snprintf(msg, sizeof(msg), "Cold scan took %.2f ms (>150)", wall_sec * 1e3);
            state.SkipWithError(msg);
        }

        // Provide manual timing so Google-Benchmark records the number we
        // just measured instead of the stop-watch time (which we paused).
        state.SetIterationTime(wall_sec);
    }
}

BENCHMARK(BM_RuntimeScan200)->Iterations(1)->UseManualTime();
BENCHMARK_MAIN(); 