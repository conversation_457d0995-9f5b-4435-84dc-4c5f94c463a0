#include <benchmark/benchmark.h>
#include "core/container/adaptive_cache.hh"

using namespace launcher::core::container;

static void BM_AdaptiveCache_Mixed(benchmark::State &state) {
    constexpr std::size_t capacity_sets = 256; // small so growth can happen quickly
    AdaptiveCache<uint64_t, uint64_t, NullPolicy, 64, 16> cache(capacity_sets, /*initial_ways*/2);

    // Pre-fill cache with some keys so we have hits immediately.
    for (uint64_t i = 0; i < capacity_sets; ++i) {
        cache.getOrInsert(i, [i] { return i; });
    }

    const uint64_t hit_key = 42;
    cache.getOrInsert(hit_key, [] { return 123ull; });

    uint64_t miss_key_base = 1'000'000;

    for (auto _ : state) {
        // 50 % hit
        benchmark::DoNotOptimize(cache.getOrInsert(hit_key, [] { return 123ull; }));
        // 50 % miss – always new key, pushes growth
        benchmark::DoNotOptimize(cache.getOrInsert(miss_key_base++, [] { return 0ull; }));
    }

    state.counters["assoc"] = static_cast<double>(cache.ways());
}

BENCHMARK(BM_AdaptiveCache_Mixed)->Iterations(1'000'000);

BENCHMARK_MAIN(); 