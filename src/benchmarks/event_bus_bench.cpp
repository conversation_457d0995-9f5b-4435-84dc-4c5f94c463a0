#include "../core/async/executor_service.h"
#include "../core/events/event_bus_service.h"
#include "../core/foundation/registry.h"
#include "../core/memory/arena_allocator_service.h"
#include <chrono>
#include <iostream>
#include <vector>
#include <algorithm>
#include <atomic>
#include <thread>

using namespace launcher::core;

struct BenchEvent { int value; };

int main() {
    foundation::ServiceRegistry registry;
    memory::ArenaAllocatorSvc    arena{registry};
    async::ExecutorService       exec{registry};
    exec.start();
    events::EventBusService bus{registry, nullptr}; // Pass nullptr for diagnostics if not used
    bus.start();

    constexpr int kMsgs = 10'000;
    std::atomic<int> received{0};

    auto sub = bus.subscribe<BenchEvent>([&](const BenchEvent* evt_ptr) {
        if (evt_ptr) { // evt_ptr is const BenchEvent*
             // To match original logic of `received.fetch_add(1, ...)` assuming BenchEvent{i} means event 'i'
             // and we increment a counter.
             // If we need the value: `int val = evt_ptr->value;`
            received.fetch_add(1, std::memory_order_relaxed);
        }
    });

    std::vector<long long> latencies;
    latencies.reserve(kMsgs);

    for (int i = 0; i < kMsgs; ++i) {
        auto start = std::chrono::high_resolution_clock::now();
        auto evt = std::make_shared<BenchEvent>(BenchEvent{i});
        bus.publish(std::static_pointer_cast<const BenchEvent>(evt));
        // busy wait until delivered
        // The received counter now just counts messages.
        // To ensure message 'i' is delivered, we need a different mechanism or assume
        // serial delivery for this single-threaded publisher test.
        // For this benchmark, counting total received messages up to 'i+1' is equivalent.
        while (received.load(std::memory_order_relaxed) < (i + 1)) {
            std::this_thread::yield();
        }
        auto end = std::chrono::high_resolution_clock::now();
        latencies.push_back(std::chrono::duration_cast<std::chrono::microseconds>(end - start).count());
    }

    // Compute p95
    std::sort(latencies.begin(), latencies.end());
    auto p95 = latencies[static_cast<std::size_t>(kMsgs * 0.95)];

    std::cout << "p95 latency = " << p95 << " us" << std::endl;

    // Fail the benchmark if latency exceeds 500 µs (Slice-1 acceptance)
    if (p95 > 500) {
        std::cerr << "EventBus latency regression: p95=" << p95 << "µs (limit 500µs)\n";
        return 1;
    }

    bus.stop();
    exec.stop();
    return 0;
} 