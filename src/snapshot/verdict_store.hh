/*
// -----------------------------------------------------------------------------
// @file verdict_store.hh
// @brief Persistent L2 cache (FlatSnapshot) for VerificationStore.
//        Provides zero-alloc hot reads, ghost entries for negatives and
//        MetricSource counters.  Slice-2 deliverable.
// -----------------------------------------------------------------------------
*/

#pragma once

#include <atomic>
#include <cstdint>
#include <filesystem>
#include <mutex>
#include <vector>
#include <unordered_map>
#include <cstring>

#include "core/container/adaptive_cache.hh"
#include "core/security/hash_types.hh"
#include "core/storage/flat_snapshot.hh"
#include "core/util/debug.h"

namespace launcher::snapshot {

class VerdictStore {
 public:
    using CDHash  = launcher::core::security::CDHash;
    using Verdict = launcher::core::security::Verdict;

    struct Stats {
        uint64_t l1_hit{0};
        uint64_t l2_hit{0};
        uint64_t ghost_hit{0};
        uint64_t miss{0};
        // AdaptiveCache internal
        uint64_t l1_set_hit{0}, l1_set_miss{0}, l1_evict{0};
    };

    static constexpr bool is_metric_source = true;

    explicit VerdictStore(const std::filesystem::path &dir,
                          std::size_t l1_sets_pow2 = 4096,
                          uint32_t    flush_threshold = 32,
                          bool        prewarm_on_load = true) noexcept;
    ~VerdictStore() noexcept;

    Verdict lookup(const CDHash &hash) noexcept;
    void    persist(const CDHash &hash, Verdict verdict) noexcept;
    void    flush() noexcept;

    [[nodiscard]] Stats stats() const noexcept;

    VerdictStore(const VerdictStore&)            = delete;
    VerdictStore& operator=(const VerdictStore&) = delete;
    VerdictStore(VerdictStore&&)                 = delete;
    VerdictStore& operator=(VerdictStore&&)      = delete;

 private:
    // ------------------------------ Snapshot layout -------------------------
    struct SnapHeader {
        char     magic[4]; // "VRS1"
        uint32_t count;    // number of records
        uint32_t reserved; // pad to 12 B
    };
    struct Record {
        CDHash hash;       // 20 B
        uint8_t verdict;   // 1 B – cast from Verdict::Code
        uint8_t pad[3]{};  // 3 B padding to 24 B total
        bool operator<(const Record &other) const noexcept {
            return std::memcmp(hash.bytes, other.hash.bytes, 20) < 0;
        }
    };

    // ------------------------------ Internals --------------------------------
    using AdaptiveCacheT = launcher::core::container::AdaptiveCache<CDHash, Verdict,
                                                                   launcher::core::container::NullPolicy,
                                                                   64, 4>;

    void loadSnapshot(const std::filesystem::path &path) noexcept;

    // ------------------------------ Members ----------------------------------
    std::filesystem::path dir_;
    AdaptiveCacheT        l1_;

    // Dirty write-buffer before flush (unordered_map for easy enumeration)
    std::unordered_map<CDHash, Verdict, std::hash<CDHash>> dirty_;
    std::mutex           dirty_mtx_;
    uint32_t             flush_threshold_{32};

    // L2 snapshot
    launcher::core::storage::FlatSnapshot snapshot_;
    const Record *records_{nullptr};
    uint32_t      record_count_{0};
    bool          prewarm_{true};

    // Counters
    std::atomic<uint64_t> l1_hit_{0}, l2_hit_{0}, ghost_hit_{0}, miss_{0};
};

} // namespace launcher::snapshot 