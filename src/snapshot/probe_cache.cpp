// -----------------------------------------------------------------------------
// @file probe_cache.cpp
// -----------------------------------------------------------------------------

#include "probe_cache.hh"

#include <fstream>
#include <span>
#include <string_view>
#include <cassert>
#include <cstring>
#include <algorithm>  // for lower_bound
#include <vector>
#include <cstddef>
#include <system_error>
#include <mutex>
#include <shared_mutex>
#include <bit>

#ifndef KAI_USE_LMDB_PROBECACHE
namespace launcher::snapshot {

using launcher::core::util::Expected;
using launcher::core::storage::FlatSnapshot;

// ------------------------ static helpers ----------------------------------
// FNV implementation kept for legacy references but superseded by std::hash.
uint64_t ProbeCache::fnv1a64(std::string_view s) noexcept {
    constexpr uint64_t kOffset = 0xcbf29ce484222325ULL;
    constexpr uint64_t kPrime  = 0x100000001b3ULL;
    uint64_t acc = kOffset;
    for (unsigned char c : s) {
        acc ^= static_cast<uint64_t>(c);
        acc *= kPrime;
    }
    return acc;
}

ProbeKey ProbeCache::makeKey(const std::filesystem::path& path,
                             const std::array<uint8_t, 32>& sha256) noexcept {
    ProbeKey k{};

    // Resolve path (canonical or lexically_normal) without producing an extra
    // string allocation.  We keep the resolved `std::filesystem::path` object
    // alive so that the underlying `string_type` storage backs our
    // std::string_view.
    std::error_code ec;
    std::filesystem::path resolved = std::filesystem::weakly_canonical(path, ec);
    if (ec) {
        resolved = path.lexically_normal();
    }

    std::string_view sv{resolved.native()};

    // Use libc++/libstdc++ splitmix64‐style hash – already well mixed.
    k.path_hash = std::hash<std::string_view>{}(sv);

    // take first 8 bytes of sha256 and interpret little-endian
    std::memcpy(&k.mtime_low64, sha256.data(), sizeof(uint64_t));
    return k;
}

// ------------------------------ ctor --------------------------------------
ProbeCache::ProbeCache(const std::filesystem::path& cache_dir,
                       std::size_t l1_sets_pow2,
                       uint32_t flush_threshold,
                       bool prewarm_on_load) noexcept
    : dir_(cache_dir), l1_(l1_sets_pow2), prewarm_on_load_{prewarm_on_load}, flush_threshold_{flush_threshold} {
    // Ensure directory exists
    std::error_code ec;
    std::filesystem::create_directories(dir_, ec);

    // -------------- SQLite ----------------
    const auto db_path = dir_ / "probe_cache.sqlite";
    int rc = sqlite3_open_v2(db_path.c_str(), &db_, SQLITE_OPEN_READWRITE | SQLITE_OPEN_CREATE | SQLITE_OPEN_NOMUTEX, nullptr);
    if (rc != SQLITE_OK) {
        ERR("ProbeCache: cannot open sqlite " << db_path << " err=" << sqlite3_errmsg(db_));
        db_ = nullptr; // degrade gracefully; L3 disabled
    } else {
        ensureSchema();
        sqlite3_exec(db_, "PRAGMA journal_mode=WAL;", nullptr, nullptr, nullptr);
        sqlite3_exec(db_, "PRAGMA synchronous=NORMAL;", nullptr, nullptr, nullptr);

        // Prepare cached statements (select, upsert)
        static const char* kSelect = "SELECT verdict FROM probe_cache WHERE path_hash=? AND mtime_low64=?;";
        if (sqlite3_prepare_v2(db_, kSelect, -1, &stmt_select_, nullptr) != SQLITE_OK) {
            stmt_select_ = nullptr;
        }
        static const char* kUpsert = "INSERT INTO probe_cache(path_hash,mtime_low64,verdict) VALUES(?,?,?) ON CONFLICT(path_hash,mtime_low64) DO UPDATE SET verdict=excluded.verdict;";
        if (sqlite3_prepare_v2(db_, kUpsert, -1, &stmt_upsert_, nullptr) != SQLITE_OK) {
            stmt_upsert_ = nullptr;
        }
    }

    // -------------- Snapshot --------------
    loadSnapshot(dir_ / "probe_cache.snap");
}

// ----------------------------- ensureSchema -------------------------------
void ProbeCache::ensureSchema() noexcept {
    if (!db_) return;
    const char* sql = "CREATE TABLE IF NOT EXISTS probe_cache(\n"
                      "  path_hash       INTEGER NOT NULL,\n"
                      "  mtime_low64     INTEGER NOT NULL,\n"
                      "  verdict         INTEGER NOT NULL,\n"
                      "  PRIMARY KEY(path_hash, mtime_low64)\n"
                      ");";
    char* err = nullptr;
    int rc = sqlite3_exec(db_, sql, nullptr, nullptr, &err);
    if (rc != SQLITE_OK) {
        ERR("ProbeCache: failed to create table " << err);
        sqlite3_free(err);
    }
}

// -------------------- Txn helpers --------------------
void ProbeCache::beginTxnIfNeeded() noexcept {
    if (!db_ || in_txn_) return;
    sqlite3_exec(db_, "BEGIN IMMEDIATE;", nullptr, nullptr, nullptr);
    in_txn_ = true;
}

void ProbeCache::commitTxn() noexcept {
    if (!db_ || !in_txn_) return;
    sqlite3_exec(db_, "COMMIT;", nullptr, nullptr, nullptr);
    in_txn_ = false;
}

// ---------------------- snapshotLookup ------------------------------------
ProbeCache::Verdict ProbeCache::snapshotLookup(const ProbeKey& key) noexcept {
    {
        std::shared_lock lock(snapshot_mtx_);
        if (!snap_records_) return {ProbeCache::Verdict::Code::kUnknown};
        auto begin = snap_records_;
        auto end   = snap_records_ + snap_count_;
        auto* it = std::lower_bound(begin, end, key, [](const Record& rec, const ProbeKey& k){ return (rec.key.path_hash < k.path_hash) || (rec.key.path_hash == k.path_hash && rec.key.mtime_low64 < k.mtime_low64); });
        if (it != end && it->key == key) {
            return it->verdict;
        }
    }
    return {ProbeCache::Verdict::Code::kUnknown};
}

// ---------------------- sqliteLookup --------------------------------------
ProbeCache::Verdict ProbeCache::sqliteLookup(const ProbeKey& key) noexcept {
    if (!db_) return {ProbeCache::Verdict::Code::kUnknown};
    std::lock_guard<std::recursive_mutex> lock(sqlite_mtx_);
    if (!stmt_select_) return {ProbeCache::Verdict::Code::kUnknown};
    sqlite3_reset(stmt_select_);
    sqlite3_clear_bindings(stmt_select_);
    sqlite3_bind_int64(stmt_select_, 1, static_cast<sqlite3_int64>(key.path_hash));
    sqlite3_bind_int64(stmt_select_, 2, static_cast<sqlite3_int64>(key.mtime_low64));
    Verdict res{};
    if (sqlite3_step(stmt_select_) == SQLITE_ROW) {
        int v = sqlite3_column_int(stmt_select_, 0);
        res.code = static_cast<Verdict::Code>(v);
    }
    return res;
}

// --------------------------- lookup (ProbeKey) -----------------------
[[gnu::flatten]] ProbeCache::Verdict ProbeCache::lookup(const ProbeKey& key) noexcept {
    // Fast-path variant without path canonicalisation.
    // Logic mirrors the original path-based implementation but skips makeKey().

    // L1
    auto& v = l1_.getOrInsert(key, [] { return Verdict{}; });
    if (v.code != Verdict::Code::kUnknown) {
        l1_hit_.fetch_add(1, std::memory_order_relaxed);
        return v;
    }

    // L2
    Verdict snap_v = snapshotLookup(key);
    if (snap_v.code != Verdict::Code::kUnknown) {
        l2_hit_.fetch_add(1, std::memory_order_relaxed);
        v = snap_v;
        return snap_v;
    }

    // L3
    Verdict sql_v = sqliteLookup(key);
    if (sql_v.code != Verdict::Code::kUnknown) {
        l3_hit_.fetch_add(1, std::memory_order_relaxed);
        v = sql_v;
        return sql_v;
    }

    miss_.fetch_add(1, std::memory_order_relaxed);
    return sql_v; // unknown
}

// --------------------------- update (ProbeKey) -----------------------
void ProbeCache::update(const ProbeKey& key, Verdict verdict) noexcept {
    l1_.getOrInsert(key, [&] { return verdict; }) = verdict;

    if (!db_) return;
    std::lock_guard<std::recursive_mutex> lock(sqlite_mtx_);
    beginTxnIfNeeded();
    if (stmt_upsert_) {
        sqlite3_reset(stmt_upsert_);
        sqlite3_clear_bindings(stmt_upsert_);
        sqlite3_bind_int64(stmt_upsert_, 1, static_cast<sqlite3_int64>(key.path_hash));
        sqlite3_bind_int64(stmt_upsert_, 2, static_cast<sqlite3_int64>(key.mtime_low64));
        sqlite3_bind_int(stmt_upsert_,   3, static_cast<int>(verdict.code));
        sqlite3_step(stmt_upsert_);
    }
    ++pending_updates_;
    if (pending_updates_ >= flush_threshold_) {
        flush();
    }
}

// --------------------------- lookup (path+sha) – refactored ----------
[[nodiscard]] ProbeCache::Verdict ProbeCache::lookup(const std::filesystem::path& path,
                                                    const std::array<uint8_t, 32>& sha256) noexcept {
    return lookup(makeKey(path, sha256));
}

// --------------------------- update (path+sha) – refactored ----------
void ProbeCache::update(const std::filesystem::path& path,
                        const std::array<uint8_t, 32>& sha256,
                        Verdict verdict) noexcept {
    update(makeKey(path, sha256), verdict);
}

// --------------------------- regenerateSnapshot ---------------------------
void ProbeCache::regenerateSnapshot() noexcept {
    if (!db_) return;
    // Open select ordered
    const char* kSelectAll = "SELECT path_hash, mtime_low64, verdict FROM probe_cache ORDER BY path_hash, mtime_low64;";
    sqlite3_stmt* stmt = nullptr;
    if (sqlite3_prepare_v2(db_, kSelectAll, -1, &stmt, nullptr) != SQLITE_OK) {
        return;
    }
    std::vector<std::byte> payload;
    struct SnapHeader { char magic[4]; uint32_t version; uint32_t count; uint32_t pad; } hdr;
    std::memcpy(hdr.magic, "PBC1", 4);
    hdr.version = 1;
    hdr.count = 0; // placeholder
    hdr.pad = 0;

    payload.resize(sizeof(SnapHeader));
    std::memcpy(payload.data(), &hdr, sizeof(SnapHeader));
    // Reserve capacity to avoid reallocations
    payload.reserve(sizeof(SnapHeader) + 2048 * sizeof(Record));

    uint32_t count = 0;
    while (sqlite3_step(stmt) == SQLITE_ROW) {
        Record rec{};
        rec.key.path_hash   = static_cast<uint64_t>(sqlite3_column_int64(stmt, 0));
        rec.key.mtime_low64 = static_cast<uint64_t>(sqlite3_column_int64(stmt, 1));
        rec.verdict.code    = static_cast<Verdict::Code>(sqlite3_column_int(stmt, 2));
        const std::byte* p = reinterpret_cast<const std::byte*>(&rec);
        payload.insert(payload.end(), p, p + sizeof(Record));
        ++count;
    }
    sqlite3_finalize(stmt);
    // patch count in header
    std::memcpy(payload.data() + offsetof(SnapHeader, count), &count, sizeof(uint32_t));
    // update hdr variable (first bytes in payload already set)
    hdr.count = count;

    const auto tmp_path  = dir_ / "probe_cache.new";
    const auto final_path = dir_ / "probe_cache.snap";
    if (!FlatSnapshot::create(payload, tmp_path)) {
        return;
    }
    std::error_code ec;
    std::filesystem::rename(tmp_path, final_path, ec);
    if (ec) {
        std::filesystem::remove(tmp_path, ec);
    }

    // mmap new snapshot
    loadSnapshot(final_path);
}

// ----------------------------- flush --------------------------------------
void ProbeCache::flush() noexcept {
    std::lock_guard<std::recursive_mutex> g(sqlite_mtx_);
    commitTxn();
    pending_updates_ = 0;
    regenerateSnapshot();
    flush_count_.fetch_add(1, std::memory_order_relaxed);
}

// ----------------------------- stats --------------------------------------
ProbeCache::Stats ProbeCache::stats() const noexcept {
    Stats s{};
    s.l1_hit     = l1_hit_.load(std::memory_order_relaxed);
    s.l2_hit     = l2_hit_.load(std::memory_order_relaxed);
    s.l3_hit     = l3_hit_.load(std::memory_order_relaxed);
    s.miss       = miss_.load(std::memory_order_relaxed);
    s.flush_count = flush_count_.load(std::memory_order_relaxed);
    auto ac = l1_.stats();
    s.l1_set_hit  = ac.hit;
    s.l1_set_miss = ac.miss;
    s.l1_evict    = ac.evict;
    return s;
}

// ------------------------------ destructor -------------------------------
ProbeCache::~ProbeCache() noexcept {
    flush(); // ensure snapshot up-to-date & txn committed
    if (stmt_select_) sqlite3_finalize(stmt_select_);
    if (stmt_upsert_) sqlite3_finalize(stmt_upsert_);
    if (db_) {
        sqlite3_close(db_);
        db_ = nullptr;
    }
}

// ----------------------- loadSnapshot helper -----------------------------
void ProbeCache::loadSnapshot(const std::filesystem::path& path) noexcept {
    auto snapExp = FlatSnapshot::mmapReadOnly(path);
    if (!snapExp) {
        return;
    }

#ifdef NDEBUG
    if (!snapExp.value().verify()) {
        return;
    }
#else
    // In Debug builds we still call verify() for diagnostics but do not abort
    // on failure to simplify development.
    (void)snapExp.value().verify();
#endif

    std::unique_lock lock(snapshot_mtx_);
    snapshot_ = std::move(snapExp.value());
    auto payload = snapshot_.payload();

    struct SnapHeader { char magic[4]; uint32_t version; uint32_t count; uint32_t pad; };
    if (payload.size() < sizeof(SnapHeader)) return;

    const auto* hdr = reinterpret_cast<const SnapHeader*>(payload.data());
    if (std::memcmp(hdr->magic, "PBC1", 4) != 0 || hdr->version != 1) {
        return; // incompatible
    }
    snap_count_ = hdr->count;
    const std::byte* record_bytes = payload.data() + sizeof(SnapHeader);
    if (payload.size() < sizeof(SnapHeader) + snap_count_ * sizeof(Record)) {
        snap_records_ = nullptr;
        snap_count_ = 0;
        return;
    }
    snap_records_ = reinterpret_cast<const Record*>(record_bytes);

    if (prewarm_on_load_) {
        for (uint32_t i = 0; i < snap_count_; ++i) {
            l1_.getOrInsert(snap_records_[i].key, [&,i]{ return snap_records_[i].verdict; });
        }
    }
}

static_assert(std::endian::native==std::endian::little, "ProbeCache requires little-endian host");

} // namespace launcher::snapshot 
#endif // !KAI_USE_LMDB_PROBECACHE 