// -----------------------------------------------------------------------------
// @file probe_cache_service.hh
// @brief Service wrapper around ProbeCache offering lookup/update/flush and
//        MetricSource integration. Injects DiagnosticsService for metrics.
//        Lives in snapshot layer but registerable via ServiceRegistry.
// -----------------------------------------------------------------------------

#pragma once

#include <filesystem>

#include "core/foundation/iservice.h"
#include "core/foundation/registry.h"
#include "core/util/result.h"
#include "core/util/debug.h"
#include "core/diagnostics/diagnostics_service.h"
#include "core/util/metrics.hh"
#include "core/interfaces/metric_source_api.h"
#include "core/interfaces/probe_cache_api.h"

#include "snapshot/probe_cache.hh"

namespace launcher::snapshot {

class ProbeCacheSvc final : public launcher::core::foundation::IService,
                            public launcher::core::interfaces::IProbeCache,
                            public launcher::core::interfaces::IMetricSource {
 public:
    static constexpr launcher::core::foundation::ServiceId kId =
        launcher::core::foundation::ServiceId::kProbeCacheSvc;

    // Constructor – registers with registry and captures DiagnosticsService.
    ProbeCacheSvc(launcher::core::foundation::ServiceRegistry& registry,
                  launcher::core::diagnostics::DiagnosticsService& diag,
                  std::filesystem::path cache_dir =
                      std::filesystem::temp_directory_path() / "kai_probe_cache")
        : diag_{diag},
          cache_{cache_dir} {
        auto res = registry.registerService(*this);
        if (!res) {
            ERR("ProbeCacheSvc registration failed");
        }
    }

    // IService API -----------------------------------------------------
    [[nodiscard]] launcher::core::foundation::ServiceId id() const noexcept override { return kId; }

    launcher::core::util::Result<void> start() override {
        running_ = true;
        DBG("ProbeCacheSvc started");
        updateMetrics();
        return launcher::core::util::Result<void>::success();
    }

    void stop() noexcept override {
        if (!running_) return;
        running_ = false;
        cache_.flush();
        updateMetrics();
        DBG("ProbeCacheSvc stopped – flushed cache");
    }

    // Public forwarding API -------------------------------------------
    [[nodiscard]] launcher::core::security::Verdict lookup(const std::filesystem::path& path,
                                                            const std::array<uint8_t, 32>& sha) noexcept {
        auto v = cache_.lookup(path, sha);
        return v;
    }

    void update(const std::filesystem::path& path,
                const std::array<uint8_t, 32>& sha,
                launcher::core::security::Verdict verdict) noexcept {
        cache_.update(path, sha, verdict);
    }

    void flush() noexcept { cache_.flush(); }

    [[nodiscard]] ProbeCache::Stats stats() const noexcept { return cache_.stats(); }

    // IMetricSource -----------------------------------------------------
    void emitMetrics(launcher::core::diagnostics::DiagnosticsService& d) noexcept override {
        pushMetrics(d, cache_.stats());
    }

 private:
    static void pushMetrics(launcher::core::diagnostics::DiagnosticsService& diag,
                              const ProbeCache::Stats& st) noexcept {
        diag.setGauge("probe_cache.l1_hit", static_cast<int64_t>(st.l1_hit));
        diag.setGauge("probe_cache.l2_hit", static_cast<int64_t>(st.l2_hit));
        diag.setGauge("probe_cache.l3_hit", static_cast<int64_t>(st.l3_hit));
        diag.setGauge("probe_cache.miss",    static_cast<int64_t>(st.miss));
        diag.setGauge("probe_cache.flush_total", static_cast<int64_t>(st.flush_count));
    }

    void updateMetrics() { pushMetrics(diag_, cache_.stats()); }

    launcher::core::diagnostics::DiagnosticsService& diag_;
    ProbeCache cache_;
    bool running_{false};
};

static_assert(launcher::core::util::MetricSource<ProbeCache>, "ProbeCache must satisfy MetricSource");

} // namespace launcher::snapshot 