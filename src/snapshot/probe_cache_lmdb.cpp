// -----------------------------------------------------------------------------
// @file probe_cache_lmdb.cpp
// @brief LMDB-backed ProbeCache implementation. Compiled when
//        KAI_USE_LMDB_PROBECACHE is defined.  Mirrors API of the SQLite/
//        FlatSnapshot version but persists verdicts directly in LMDB.
// -----------------------------------------------------------------------------

#ifdef KAI_USE_LMDB_PROBECACHE

#include "probe_cache.hh"

#include <lmdb.h>
#include <cassert>
#include <cstring>
#include <filesystem>
#include <system_error>
#include <mutex>
#include <shared_mutex>
#include <span>
#include <vector>
#include <atomic>
#include <string_view>
#include <bit>
#include "core/util/expected.h"
#include <unistd.h>
#include <sys/stat.h>
#include <thread>
#include <string>

#include "core/util/debug.h"

namespace launcher::snapshot {

enum class ProbeCacheError {
    MapFull,
    TxnBeginFail,
    TxnCommitFail,
    DbOpenFail,
    EnvOpenFail,
};

template<typename T>
using PCExpected = launcher::core::util::Expected<T, ProbeCacheError>;

// -------------------------- internal helpers -------------------------------
namespace {
#ifdef KAI_LMDB_MAP_SIZE_MB
constexpr size_t kDefaultMapSizeBytes = static_cast<size_t>(KAI_LMDB_MAP_SIZE_MB) * 1024ull * 1024ull;
#else
constexpr size_t kDefaultMapSizeBytes = 128ull * 1024ull * 1024ull;
#endif
}

// Small RAII guard for LMDB txns -------------------------------------------------
class TxnGuard {
public:
    TxnGuard(MDB_env* env, bool write) noexcept : env_{env} {
        int flags = write ? 0 : MDB_RDONLY;
        int rc = mdb_txn_begin(env_, nullptr, flags, &txn_);
        if (rc != MDB_SUCCESS) {
            txn_ = nullptr;
        }
    }
    ~TxnGuard() {
        if (txn_) mdb_txn_abort(txn_);
    }
    MDB_txn* get() const noexcept { return txn_; }
    [[nodiscard]] bool ok() const noexcept { return txn_ != nullptr; }
    // Commit only for write txns
    void commit() noexcept {
        if (txn_) {
            mdb_txn_commit(txn_);
            txn_ = nullptr;
        }
    }
private:
    MDB_env* env_;
    MDB_txn* txn_{nullptr};
};

// -------------------------- LmdbStore --------------------------------------
class LmdbStore {
public:
    explicit LmdbStore(const std::filesystem::path& dir) noexcept : dir_(dir) {
        int rc = mdb_env_create(&env_);
        if (rc != MDB_SUCCESS) {
            ERR("LMDB: env_create failed " << mdb_strerror(rc));
            env_ = nullptr;
            return;
        }
        mdb_env_set_maxreaders(env_, 512);
        mdb_env_set_mapsize(env_, kDefaultMapSizeBytes);
        std::error_code ec;
        std::filesystem::create_directories(dir, ec);
        // Verify directory ownership equals current user (hardened runtime rule)
        struct stat st{};
        std::string dir_str = dir.string();
        if (stat(dir_str.c_str(), &st) == 0 && st.st_uid != getuid()) {
            ERR("LMDB: cache directory owned by different UID – aborting");
            mdb_env_close(env_);
            env_ = nullptr;
            return;
        }
        rc = mdb_env_open(env_, dir.c_str(), MDB_NOSYNC | MDB_NORDAHEAD | MDB_NOMEMINIT, 0600);
        if (rc != MDB_SUCCESS) {
            ERR("LMDB: env_open failed " << mdb_strerror(rc));
            mdb_env_close(env_);
            env_ = nullptr;
            return;
        }
        // Open DBI in write txn once
        MDB_txn* txn;
        rc = mdb_txn_begin(env_, nullptr, 0, &txn);
        if (rc != MDB_SUCCESS) {
            ERR("LMDB: txn_begin failed " << mdb_strerror(rc));
            return;
        }
        rc = mdb_dbi_open(txn, nullptr, MDB_CREATE | MDB_INTEGERKEY | MDB_DUPSORT | MDB_DUPFIXED, &dbi_);
        if (rc != MDB_SUCCESS) {
            ERR("LMDB: dbi_open failed " << mdb_strerror(rc));
            mdb_txn_abort(txn);
            return;
        }
        mdb_txn_commit(txn);
    }

    ~LmdbStore() noexcept {
        if (env_) {
            mdb_dbi_close(env_, dbi_);
            mdb_env_close(env_);
        }
    }

    [[nodiscard]] launcher::core::security::Verdict
    lookup(const ProbeKey& key) noexcept {
        launcher::core::security::Verdict v{};
        // Thread-local read txn reuse for performance
        struct ReaderCache { MDB_txn* txn{nullptr}; uint32_t ops{0}; uint64_t epoch{static_cast<uint64_t>(-1)}; };
        static thread_local ReaderCache reader;

        auto renew_reader = [&]() {
            if (reader.txn) {
                mdb_txn_abort(reader.txn);
                reader.txn = nullptr;
            }
            int rc = mdb_txn_begin(env_, nullptr, MDB_RDONLY, &reader.txn);
            if (rc != MDB_SUCCESS) {
                reader.txn = nullptr;
                return false;
            }
            reader.ops = 0;
            reader.epoch = env_epoch_.load(std::memory_order_relaxed);
            return true;
        };

        uint64_t current_epoch = env_epoch_.load(std::memory_order_relaxed);

        if (reader.epoch != current_epoch) {
            // Environment replaced, reset reader
            if (reader.txn) {
                mdb_txn_abort(reader.txn);
                reader.txn = nullptr;
            }
            reader.ops = 0;
            reader.epoch = current_epoch;
        }

        if (!reader.txn) {
            if (!renew_reader()) return v;
        } else if (reader.ops >= 1000) {
            // reset & renew after N ops
            mdb_txn_reset(reader.txn);
            int rc = mdb_txn_renew(reader.txn);
            if (rc != MDB_SUCCESS) {
                renew_reader();
            }
            reader.ops = 0;
        }

        MDB_val mdb_key{sizeof(ProbeKey), const_cast<ProbeKey*>(&key)};
        MDB_val mdb_val{};
        int rc = mdb_get(reader.txn, dbi_, &mdb_key, &mdb_val);
        ++reader.ops;
        if (rc == MDB_SUCCESS && mdb_val.mv_size == sizeof(uint8_t)) {
            uint8_t code = *reinterpret_cast<uint8_t*>(mdb_val.mv_data);
            v.code = static_cast<launcher::core::security::Verdict::Code>(code);
        }
        return v;
    }

    void upsert(const ProbeKey& key, launcher::core::security::Verdict verdict) noexcept {
        std::lock_guard<std::mutex> g(write_mtx_);
        TxnGuard txn(env_, /*write=*/true);
        if (!txn.ok()) return;
        MDB_val mdb_key{sizeof(ProbeKey), const_cast<ProbeKey*>(&key)};
        uint8_t code_byte = static_cast<uint8_t>(verdict.code);
        MDB_val mdb_val{sizeof(uint8_t), &code_byte};
        mdb_put(txn.get(), dbi_, &mdb_key, &mdb_val, 0);
        txn.commit();
        ++pending_updates_;
    }

    void flush() noexcept {
        if (!env_) return;
        std::lock_guard<std::mutex> g(write_mtx_);
        // LMDB commits are synchronous already (except NOSYNC), force fsync if needed
        mdb_env_sync(env_, 1);
        pending_updates_ = 0;
        compactIfNeeded();
    }

    void compactIfNeeded() noexcept {
#ifdef KAI_LMDB_COMPACT_ON_STOP
        if (!env_) return;
        MDB_envinfo info{};
        MDB_stat stat{};
        mdb_env_info(env_, &info);
        mdb_env_stat(env_, &stat);
        size_t used = (info.mi_last_pgno + 1ull) * stat.ms_psize;
        double util = static_cast<double>(used) / static_cast<double>(info.mi_mapsize);
        if (util > 0.75) return; // utilisation fine

        std::string tmp_path = (dir_ / "probe_cache_compact.mdb").string();
        int rc = mdb_env_copy2(env_, tmp_path.c_str(), MDB_CP_COMPACT);
        if (rc != MDB_SUCCESS) {
            ERR("LMDB: compact copy failed " << mdb_strerror(rc));
            return;
        }
        // Close env before replacing file
        mdb_dbi_close(env_, dbi_);
        mdb_env_close(env_);
        env_ = nullptr;

        std::filesystem::path mapfile = dir_ / "data.mdb"; // default map name in NOSUBDIR
        std::error_code ec;
        std::filesystem::rename(tmp_path, mapfile, ec);
        if (ec) {
            ERR("LMDB: rename compact file failed " << ec.message());
        }
        // Reopen env
        env_epoch_.fetch_add(1, std::memory_order_relaxed);

        int rc2 = mdb_env_create(&env_);
        if (rc2 != MDB_SUCCESS) {
            ERR("LMDB: env recreate failed after compaction " << mdb_strerror(rc2));
            return;
        }
        mdb_env_set_maxreaders(env_, 512);
        mdb_env_set_mapsize(env_, kDefaultMapSizeBytes);
        std::string dir_str = dir_.string();
        rc2 = mdb_env_open(env_, dir_str.c_str(), MDB_NOSYNC | MDB_NORDAHEAD | MDB_NOMEMINIT, 0600);
        if (rc2 != MDB_SUCCESS) {
            ERR("LMDB: env reopen failed after compaction " << mdb_strerror(rc2));
            return;
        }
        MDB_txn* txn = nullptr;
        if (mdb_txn_begin(env_, nullptr, 0, &txn) == MDB_SUCCESS) {
            mdb_dbi_open(txn, nullptr, MDB_CREATE | MDB_INTEGERKEY | MDB_DUPSORT | MDB_DUPFIXED, &dbi_);
            mdb_txn_commit(txn);
        }
#endif // KAI_LMDB_COMPACT_ON_STOP
    }

private:
    MDB_env* env_{nullptr};
    MDB_dbi  dbi_{};
    std::mutex write_mtx_;
    uint32_t pending_updates_{0};
    std::filesystem::path dir_;
    std::atomic<uint64_t> env_epoch_{0};
};

// -------------------------- static helpers ---------------------------------
// FNV kept for potential compatibility / tests but not used in fast path.
uint64_t ProbeCache::fnv1a64(std::string_view s) noexcept {
    constexpr uint64_t kOffset = 0xcbf29ce484222325ULL;
    constexpr uint64_t kPrime  = 0x100000001b3ULL;
    uint64_t acc = kOffset;
    for (unsigned char c : s) {
        acc ^= static_cast<uint64_t>(c);
        acc *= kPrime;
    }
    return acc;
}

ProbeKey ProbeCache::makeKey(const std::filesystem::path& path,
                             const std::array<uint8_t, 32>& sha256) noexcept {
    ProbeKey k{};
    std::error_code ec;
    std::filesystem::path resolved = std::filesystem::weakly_canonical(path, ec);
    if (ec) {
        resolved = path.lexically_normal();
    }
    std::string_view sv{resolved.native()};
    k.path_hash = std::hash<std::string_view>{}(sv);
    std::memcpy(&k.mtime_low64, sha256.data(), sizeof(uint64_t));
    return k;
}

// ------------------------------ ctor --------------------------------------
ProbeCache::ProbeCache(const std::filesystem::path& cache_dir,
                       std::size_t l1_sets_pow2,
                       uint32_t /*flush_threshold*/,
                       bool /*prewarm*/ ) noexcept
    : l1_(l1_sets_pow2), store_(new LmdbStore(cache_dir)) {}

// ------------------------------ dtor --------------------------------------
ProbeCache::~ProbeCache() noexcept {
    flush();
    delete store_;
}

// --------------------------- lookup (ProbeKey) -----------------------
[[gnu::flatten]] launcher::core::security::Verdict ProbeCache::lookup(const ProbeKey& key) noexcept {
    // L1
    auto& v = l1_.getOrInsert(key, []{ return launcher::core::security::Verdict{}; });
    if (v.code != launcher::core::security::Verdict::Code::kUnknown) {
        l1_hit_.fetch_add(1, std::memory_order_relaxed);
        return v;
    }

    // LMDB lookup
    auto lmdb_v = store_->lookup(key);
    if (lmdb_v.code != launcher::core::security::Verdict::Code::kUnknown) {
        l3_hit_.fetch_add(1, std::memory_order_relaxed);
        v = lmdb_v;
        return lmdb_v;
    }

    miss_.fetch_add(1, std::memory_order_relaxed);
    return lmdb_v; // unknown
}

// --------------------------- update (ProbeKey) -----------------------
void ProbeCache::update(const ProbeKey& key,
                        launcher::core::security::Verdict verdict) noexcept {
    l1_.getOrInsert(key, [&]{ return verdict; }) = verdict;
    store_->upsert(key, verdict);
}

// --------------------------- lookup (path+sha) – refactored ----------
launcher::core::security::Verdict ProbeCache::lookup(const std::filesystem::path& path,
                                                     const std::array<uint8_t, 32>& sha256) noexcept {
    return lookup(makeKey(path, sha256));
}

// --------------------------- update (path+sha) – refactored ----------
void ProbeCache::update(const std::filesystem::path& path,
                        const std::array<uint8_t, 32>& sha256,
                        launcher::core::security::Verdict verdict) noexcept {
    update(makeKey(path, sha256), verdict);
}

// --------------------------- flush ----------------------------------------
void ProbeCache::flush() noexcept {
    store_->flush();
    flush_count_.fetch_add(1, std::memory_order_relaxed);
}

// --------------------------- stats ----------------------------------------
ProbeCache::Stats ProbeCache::stats() const noexcept {
    Stats s{};
    s.l1_hit  = l1_hit_.load(std::memory_order_relaxed);
    s.l2_hit  = 0;
    s.miss    = miss_.load(std::memory_order_relaxed);
    s.flush_count = flush_count_.load(std::memory_order_relaxed);
    auto ac = l1_.stats();
    s.l1_set_hit  = ac.hit;
    s.l1_set_miss = ac.miss;
    s.l1_evict    = ac.evict;
    s.l3_hit = l3_hit_.load(std::memory_order_relaxed);
    return s;
}

static_assert(std::endian::native==std::endian::little, "ProbeCache requires little-endian host");

} // namespace launcher::snapshot

#endif // KAI_USE_LMDB_PROBECACHE 