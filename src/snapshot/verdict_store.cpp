// -----------------------------------------------------------------------------
// @file verdict_store.cpp
// -----------------------------------------------------------------------------

#include "verdict_store.hh"

#include <algorithm>
#include <cstring>
#include <fstream>
#include <span>
#include <system_error>
#include <vector>
#include <cstddef>
#include <bit>
#include <filesystem>

#include "core/util/debug.h"

namespace launcher::snapshot {

using launcher::core::storage::FlatSnapshot;
using launcher::core::security::CDHash;

namespace {
static bool cdhashLess(const CDHash &a, const CDHash &b) noexcept {
    return std::memcmp(a.bytes, b.bytes, 20) < 0;
}
} // namespace

// ------------------------------- ctor -----------------------------------------
VerdictStore::VerdictStore(const std::filesystem::path &dir,
                           std::size_t l1_sets_pow2,
                           uint32_t    flush_threshold,
                           bool        prewarm_on_load) noexcept
    : dir_(dir), l1_(l1_sets_pow2), flush_threshold_{flush_threshold}, prewarm_{prewarm_on_load} {
    std::error_code ec;
    std::filesystem::create_directories(dir_, ec);
    loadSnapshot(dir_ / "verdict_store.snap");
}

// ------------------------------ dtor ------------------------------------------
VerdictStore::~VerdictStore() noexcept {
    flush();
}

// ------------------------------ lookup ---------------------------------------
VerdictStore::Verdict VerdictStore::lookup(const CDHash &hash) noexcept {
    // L1
    auto &v = l1_.getOrInsert(hash, [] { return Verdict{}; });
    if (v.code != Verdict::Code::kUnknown) {
        l1_hit_.fetch_add(1, std::memory_order_relaxed);
        return v;
    }

    // L2 (snapshot) – binary search inside mmap
    if (records_) {
        const Record key_probe{hash, 0, {0, 0, 0}};
        const Record *begin = records_;
        const Record *end   = records_ + record_count_;
        auto it = std::lower_bound(begin, end, key_probe, [](const Record &lhs, const Record &rhs) {
            return std::memcmp(lhs.hash.bytes, rhs.hash.bytes, 20) < 0;
        });
        if (it != end && std::memcmp(it->hash.bytes, hash.bytes, 20) == 0) {
            Verdict snap_v{};
            snap_v.code = static_cast<Verdict::Code>(it->verdict);
            if (snap_v.code != Verdict::Code::kUnknown) {
                l2_hit_.fetch_add(1, std::memory_order_relaxed);
                // promote into L1
                v = snap_v;
                return snap_v;
            }
            // Ghost entry stored previously
            ghost_hit_.fetch_add(1, std::memory_order_relaxed);
        }
    }

    // MISS – create ghost so future cold start avoids expensive hit chain
    miss_.fetch_add(1, std::memory_order_relaxed);
    persist(hash, Verdict{}); // kUnknown ghost
    return Verdict{};
}

// ----------------------------- persist ---------------------------------------
void VerdictStore::persist(const CDHash &hash, Verdict verdict) noexcept {
    // Update L1 immediately
    l1_.getOrInsert(hash, [&] { return verdict; }) = verdict;

    bool should_flush = false;
    {
        std::lock_guard lg(dirty_mtx_);
        auto [it, inserted] = dirty_.try_emplace(hash, verdict);
        if (!inserted) it->second = verdict;
        if (dirty_.size() >= flush_threshold_) {
            should_flush = true;
        }
    }

    if (should_flush) {
        flush();
    }
}

// ------------------------------ flush ----------------------------------------
void VerdictStore::flush() noexcept {
    std::unordered_map<CDHash, Verdict, std::hash<CDHash>> local_dirty;
    {
        std::lock_guard lg(dirty_mtx_);
        if (dirty_.empty()) return;
        local_dirty.swap(dirty_);
    }

    // Build sorted dirty vector
    std::vector<Record> dirty_vec;
    dirty_vec.reserve(local_dirty.size());
    for (const auto &kv : local_dirty) {
        Record r{};
        r.hash = kv.first;
        r.verdict = static_cast<uint8_t>(kv.second.code);
        dirty_vec.push_back(r);
    }
    std::sort(dirty_vec.begin(), dirty_vec.end(), [](const Record &a, const Record &b) {
        return cdhashLess(a.hash, b.hash);
    });

    // Merge with existing records (already sorted)
    std::vector<std::byte> payload;
    payload.reserve(sizeof(SnapHeader) + (record_count_ + dirty_vec.size()) * sizeof(Record));

    SnapHeader hdr{};
    std::memcpy(hdr.magic, "VRS1", 4);
    hdr.count = 0; // placeholder
    hdr.reserved = 0;
    payload.insert(payload.end(), reinterpret_cast<std::byte*>(&hdr), reinterpret_cast<std::byte*>(&hdr) + sizeof(SnapHeader));

    auto emitRecord = [&](const Record &rec) {
        payload.insert(payload.end(), reinterpret_cast<const std::byte*>(&rec), reinterpret_cast<const std::byte*>(&rec) + sizeof(Record));
        ++hdr.count;
    };

    size_t i = 0, j = 0;
    const Record *old = records_;
    while (i < record_count_ && j < dirty_vec.size()) {
        if (cdhashLess(old[i].hash, dirty_vec[j].hash)) {
            emitRecord(old[i]);
            ++i;
        } else if (cdhashLess(dirty_vec[j].hash, old[i].hash)) {
            emitRecord(dirty_vec[j]);
            ++j;
        } else {
            // same hash – dirty overrides
            emitRecord(dirty_vec[j]);
            ++i; ++j;
        }
    }
    // Remaining tails
    while (i < record_count_) { emitRecord(old[i++]); }
    while (j < dirty_vec.size()) { emitRecord(dirty_vec[j++]); }

    // Patch count in payload
    std::memcpy(payload.data() + offsetof(SnapHeader, count), &hdr.count, sizeof(uint32_t));

    // Write snapshot to temp and atomically rename
    const auto tmp = dir_ / "verdict_store.new";
    const auto final = dir_ / "verdict_store.snap";
    if (!FlatSnapshot::create(payload, tmp)) {
        ERR("VerdictStore: failed to create snapshot");
        return;
    }
    std::error_code ec;
    std::filesystem::rename(tmp, final, ec);
    if (ec) {
        std::filesystem::remove(tmp, ec);
        ERR("VerdictStore: rename failed " << ec.message());
    }

    // Reload snapshot to mmap new version (prewarm optional)
    loadSnapshot(final);
}

// ----------------------------- stats -----------------------------------------
VerdictStore::Stats VerdictStore::stats() const noexcept {
    Stats s{};
    s.l1_hit    = l1_hit_.load(std::memory_order_relaxed);
    s.l2_hit    = l2_hit_.load(std::memory_order_relaxed);
    s.ghost_hit = ghost_hit_.load(std::memory_order_relaxed);
    s.miss      = miss_.load(std::memory_order_relaxed);

    auto ac = l1_.stats();
    s.l1_set_hit  = ac.hit;
    s.l1_set_miss = ac.miss;
    s.l1_evict    = ac.evict;
    return s;
}

// --------------------------- loadSnapshot ------------------------------------
void VerdictStore::loadSnapshot(const std::filesystem::path &path) noexcept {
    auto exp = FlatSnapshot::mmapReadOnly(path);
    if (!exp) {
        return;
    }
#ifdef NDEBUG
    if (!exp.value().verify()) {
        return;
    }
#else
    (void)exp.value().verify();
#endif
    snapshot_ = std::move(exp.value());
    auto payload = snapshot_.payload();
    if (payload.size() < sizeof(SnapHeader)) return;

    const auto *hdr = reinterpret_cast<const SnapHeader*>(payload.data());
    if (std::memcmp(hdr->magic, "VRS1", 4) != 0) return;
    record_count_ = hdr->count;
    const std::byte *rec_bytes = payload.data() + sizeof(SnapHeader);
    if (payload.size() < sizeof(SnapHeader) + record_count_ * sizeof(Record)) {
        records_ = nullptr;
        record_count_ = 0;
        return;
    }
    records_ = reinterpret_cast<const Record*>(rec_bytes);

    if (prewarm_) {
        for (uint32_t k = 0; k < record_count_; ++k) {
            Verdict v{};
            v.code = static_cast<Verdict::Code>(records_[k].verdict);
            if (v.code != Verdict::Code::kUnknown) {
                l1_.getOrInsert(records_[k].hash, [&,k]{ return v; });
            }
        }
    }
}

static_assert(std::endian::native == std::endian::little, "VerdictStore requires little-endian host");

} // namespace launcher::snapshot 