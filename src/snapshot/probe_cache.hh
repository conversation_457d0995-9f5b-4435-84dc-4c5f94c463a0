// -----------------------------------------------------------------------------
// @file probe_cache.hh
// @brief Multi-tier probe cache (AdaptiveCache + FlatSnapshot + SQLite).
//        Hot path is zero-allocation; persistence via SQLite & FlatSnapshot.
// -----------------------------------------------------------------------------

#pragma once

#include <array>
#include <atomic>
#include <cstddef>
#include <cstdint>
#include <filesystem>
#include <mutex>
#include <shared_mutex>
#ifndef KAI_USE_LMDB_PROBECACHE
#include <sqlite3.h>
#endif
#include <vector>

#include "core/container/adaptive_cache.hh"
#include "core/security/hash_types.hh" // Verdict
#include "core/storage/flat_snapshot.hh"
#include "core/util/expected.h"
#include "core/util/debug.h"

namespace launcher::snapshot {

// ----------------------------------------------------------------------------
// ProbeKey – 16-byte composite key (path_hash + mtime_sha256_low64).
// ----------------------------------------------------------------------------
struct ProbeKey {
    uint64_t path_hash;      // 64-bit FNV-1a of canonicalised path
    uint64_t mtime_low64;    // low 64 bits of SHA-256(mtime)

    bool operator==(const ProbeKey&) const noexcept = default;
};

static_assert(sizeof(ProbeKey) == 16, "ProbeKey must remain 16 bytes (ABI with LMDB MDB_INTEGERKEY | MDB_DUPSORT | MDB_DUPFIXED)");

} // namespace launcher::snapshot

// std::hash specialisation
namespace std {

template<>
struct hash<launcher::snapshot::ProbeKey> {
    size_t operator()(const launcher::snapshot::ProbeKey& k) const noexcept {
        // mix with splitmix64
        uint64_t x = k.path_hash ^ (k.mtime_low64 + 0x9e3779b97f4a7c15ULL + (k.path_hash<<6) + (k.path_hash>>2));
        // finalise 64->size_t
        x ^= x >> 30; x *= 0xbf58476d1ce4e5b9ULL;
        x ^= x >> 27; x *= 0x94d049bb133111ebULL;
        x ^= x >> 31;
        return static_cast<size_t>(x);
    }
};

} // namespace std

namespace launcher::snapshot {

#ifndef KAI_USE_LMDB_PROBECACHE
// Existing SQLite/FlatSnapshot backed implementation ----------------------------------
class ProbeCache {
public:
    // Stats exposed via MetricSource concept
    struct Stats {
        uint64_t l1_hit{0}, l2_hit{0}, l3_hit{0}, miss{0};
        uint64_t flush_count{0};
        // AdaptiveCache internal
        uint64_t l1_set_hit{0}, l1_set_miss{0}, l1_evict{0};
    };

    static constexpr bool is_metric_source = true;

    // Constructor with tunables:
    //   cache_dir        – directory where sqlite + snapshot live
    //   l1_sets_pow2     – number of sets (power-of-two) for AdaptiveCache L1
    //   flush_threshold  – number of pending upserts before auto-flush
    //   prewarm_on_load  – when true (default) snapshot records are eagerly
    //                      inserted into L1 on load for zero-miss warm start.
    explicit ProbeCache(const std::filesystem::path& cache_dir,
                        std::size_t l1_sets_pow2 = 4096,
                        uint32_t   flush_threshold = 32,
                        bool       prewarm_on_load = true) noexcept;

    ~ProbeCache() noexcept;

    ProbeCache(const ProbeCache&)            = delete;
    ProbeCache& operator=(const ProbeCache&) = delete;
    ProbeCache(ProbeCache&&)                 = delete;
    ProbeCache& operator=(ProbeCache&&)      = delete;

    // Lookup returns stored Verdict or kUnknown when cache miss.
    [[nodiscard]] launcher::core::security::Verdict
    lookup(const std::filesystem::path& path,
           const std::array<uint8_t, 32>& mtime_sha256) noexcept;

    void update(const std::filesystem::path& path,
                const std::array<uint8_t, 32>& mtime_sha256,
                launcher::core::security::Verdict verdict) noexcept;

    // Flush pending SQLite tx and regenerate FlatSnapshot.
    void flush() noexcept;

    [[nodiscard]] Stats stats() const noexcept;

    // PUBLIC API EXTENSION ---------------------------------------------
    // Fast-path overloads that operate on already-computed ProbeKey.
    // Caller is responsible for supplying correct key derived from
    // (canonical_path, mtime_sha256_low64).  This removes per-call
    // filesystem canonicalisation and string hashing overhead when
    // the caller can cache ProbeKey.
    [[nodiscard]] launcher::core::security::Verdict
    lookup(const ProbeKey& key) noexcept;

    void update(const ProbeKey& key,
                launcher::core::security::Verdict verdict) noexcept;

    // Expose key construction helper so external high-volume callers
    // can compute ProbeKey once and reuse it across look-ups.
    static ProbeKey makeKey(const std::filesystem::path& path,
                            const std::array<uint8_t, 32>& sha256) noexcept;

private:
    using Verdict = launcher::core::security::Verdict;
    using AdaptiveCacheT = launcher::core::container::AdaptiveCache<ProbeKey, Verdict,
                                                                   launcher::core::container::NullPolicy,
                                                                   64, 16>;

    // ---------------------------------------------------------------------
    // Internal helpers
    // ---------------------------------------------------------------------
    static uint64_t fnv1a64(std::string_view s) noexcept;

    Verdict snapshotLookup(const ProbeKey& key) noexcept;
    Verdict sqliteLookup(const ProbeKey& key) noexcept;

    void ensureSchema() noexcept;
    void beginTxnIfNeeded() noexcept;
    void commitTxn() noexcept;
    void regenerateSnapshot() noexcept;
    void loadSnapshot(const std::filesystem::path& path) noexcept;

    // ---------------------------------------------------------------------
    // Data members
    // ---------------------------------------------------------------------
    std::filesystem::path dir_;
    AdaptiveCacheT        l1_;

    // L2 snapshot
    launcher::core::storage::FlatSnapshot snapshot_;
    const struct Record { ProbeKey key; Verdict verdict; }* snap_records_{nullptr};
    uint32_t snap_count_{0};

    mutable std::shared_mutex snapshot_mtx_;

    // L3 SQLite
    sqlite3* db_{nullptr};
    sqlite3_stmt* stmt_select_{nullptr};
    sqlite3_stmt* stmt_upsert_{nullptr};
    std::recursive_mutex sqlite_mtx_;
    bool in_txn_{false};
    uint32_t pending_updates_{0};

    // Counters
    std::atomic<uint64_t> l1_hit_{0}, l2_hit_{0}, l3_hit_{0}, miss_{0}, flush_count_{0};

    bool prewarm_on_load_{true};
    uint32_t flush_threshold_{32};
};
#endif  // !KAI_USE_LMDB_PROBECACHE

#ifdef KAI_USE_LMDB_PROBECACHE
// -----------------------------------------------------------------------------
// LMDB-backed ProbeCache declaration (definition in probe_cache_lmdb.cpp)
// -----------------------------------------------------------------------------
class ProbeCache {
public:
    struct Stats {
        uint64_t l1_hit{0}, miss{0};
        uint64_t l2_hit{0}; // LMDB only tier after L1, still keep field for uniformity
        uint64_t l3_hit{0}; // placeholder to preserve interface
        uint64_t flush_count{0};
        uint64_t l1_set_hit{0}, l1_set_miss{0}, l1_evict{0};
    };
    static constexpr bool is_metric_source = true;

    explicit ProbeCache(const std::filesystem::path& cache_dir,
                        std::size_t l1_sets_pow2 = 4096,
                        uint32_t   flush_threshold = 32,
                        bool       /*unused*/ = true) noexcept;
    ~ProbeCache() noexcept;

    ProbeCache(const ProbeCache&)            = delete;
    ProbeCache& operator=(const ProbeCache&) = delete;
    ProbeCache(ProbeCache&&)                 = delete;
    ProbeCache& operator=(ProbeCache&&)      = delete;

    [[nodiscard]] launcher::core::security::Verdict
    lookup(const std::filesystem::path& path,
           const std::array<uint8_t, 32>& mtime_sha256) noexcept;

    void update(const std::filesystem::path& path,
                const std::array<uint8_t, 32>& mtime_sha256,
                launcher::core::security::Verdict verdict) noexcept;

    void flush() noexcept;

    [[nodiscard]] Stats stats() const noexcept;

    // PUBLIC API EXTENSION ---------------------------------------------
    // Fast-path overloads that operate on already-computed ProbeKey.
    // Caller is responsible for supplying correct key derived from
    // (canonical_path, mtime_sha256_low64).  This removes per-call
    // filesystem canonicalisation and string hashing overhead when
    // the caller can cache ProbeKey.
    [[nodiscard]] launcher::core::security::Verdict
    lookup(const ProbeKey& key) noexcept;

    void update(const ProbeKey& key,
                launcher::core::security::Verdict verdict) noexcept;

    // Expose key construction helper so external high-volume callers
    // can compute ProbeKey once and reuse it across look-ups.
    static ProbeKey makeKey(const std::filesystem::path& path,
                            const std::array<uint8_t, 32>& sha256) noexcept;

private:
    using Verdict = launcher::core::security::Verdict;
    using AdaptiveCacheT = launcher::core::container::AdaptiveCache<ProbeKey, Verdict,
                                                                   launcher::core::container::NullPolicy,
                                                                   64, 16>;
    static uint64_t fnv1a64(std::string_view s) noexcept;

    // L1 fast cache
    AdaptiveCacheT l1_;

    // LMDB handles persistence
    class LmdbStore* store_;

    // counters
    std::atomic<uint64_t> l1_hit_{0}, l2_hit_{0}, l3_hit_{0}, miss_{0}, flush_count_{0};
};

#endif  // KAI_USE_LMDB_PROBECACHE

} // namespace launcher::snapshot 