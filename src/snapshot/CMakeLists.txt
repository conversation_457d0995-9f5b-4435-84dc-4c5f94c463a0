# Select backend implementation sources depending on LMDB flag
if(KAI_USE_LMDB_PROBECACHE)
    set(PROBE_CACHE_BACKEND_SRC
        probe_cache_lmdb.cpp)
else()
    set(PROBE_CACHE_BACKEND_SRC
        probe_cache.cpp)
endif()

# Add VerdictStore source
list(APPEND PROBE_CACHE_BACKEND_SRC
     verdict_store.cpp)

add_library(snapshot_lib STATIC
    ${PROBE_CACHE_BACKEND_SRC}
    probe_cache_service.hh
)

target_include_directories(snapshot_lib PUBLIC
    ${CMAKE_CURRENT_SOURCE_DIR}
    ${CMAKE_SOURCE_DIR}/src
)

# Previously, SQLite discovery and linking were unconditional. Make conditional.
if(NOT KAI_USE_LMDB_PROBECACHE)
    # Find SQLite3
    find_path(SQLITE3_INCLUDE_DIR sqlite3.h)
    find_library(SQLITE3_LIBRARY sqlite3)
    if(NOT SQLITE3_LIBRARY OR NOT SQLITE3_INCLUDE_DIR)
        message(FATAL_ERROR "SQLite3 not found for snapshot_lib")
    endif()

    target_include_directories(snapshot_lib PRIVATE ${SQLITE3_INCLUDE_DIR})
    target_link_libraries(snapshot_lib PUBLIC ${SQLITE3_LIBRARY} core)
else()
    # LMDB backend – add include dir already set for lmdb target
    target_link_libraries(snapshot_lib PUBLIC lmdb core)
endif()

target_compile_features(snapshot_lib PUBLIC cxx_std_20)

# Link crypto helper when Ed25519 signing enabled
if(KAI_ENABLE_FLATSNAPSHOT_SIGN)
    target_link_libraries(snapshot_lib PUBLIC kai_crypto)
endif() 