# Contributing to <PERSON> <PERSON> <PERSON><PERSON>auncher

Thank you for your interest in contributing! We value high-quality, deterministic builds and a hardened runtime. Please read these quick guidelines before opening a PR.

## 1. Service Registry changes

The service kernel headers (`service_id.h`, `service_topology.h`, `service_name_phf.h`, `service_meta.h`, `service_static_checks.h`) are **auto-generated** at build-time by the tool `kai-service-gen`.

To add a new service _or_ change dependencies of an existing one you now only need to edit **`services.yaml`** at the project root:

```yaml
- name: YourNewService
  deps: [DependencyA, DependencyB]
```

Rules:

1.  Keep names in **PascalCase**; they become enum constants.
2.  The order of entries in the YAML file is the numeric order of the `ServiceId` enum. **Append** new services to avoid re-ordering existing IDs.
3.  List every compile-time dependency in `deps:`. The generator validates acyclic order.

When you run CMake/Ninja the generator will:

-   Validate the DAG (cycles cause a build error).
-   Emit the updated headers into `build/generated/services/`.
-   Trigger a compile; `static_assert`s in the generated `service_static_checks.h` guarantee runtime order correctness.

CI also runs `kai-service-gen --dry-run` to ensure the committed YAML is valid.

_No manual header edits are required and any PR that touches generated headers will be rejected._

## 2. Coding standards

-   C++20, 4-space indent, 100-column limit; run `clang-format` before committing.
-   Prefer composition, RAII, zero-allocation paths (see `core_cpp` Cursor rule).
-   Use `DBG/ERR` macros rather than `std::cout` / `NSLog`.

## 3. Commit hygiene

-   Squash commits that belong together.
-   Include a brief description and reference any relevant issue IDs.

## 4. Pull-request checklist

-   [ ] `./build.sh` passes (Release + Debug).
-   [ ] `./run.sh` starts without errors.
-   [ ] `ctest` succeeds.
-   [ ] Added/updated unit tests where applicable.
-   [ ] Updated docs if you changed public APIs or service topology.

Happy hacking! – The Kai Team
