# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [Unreleased]

## [0.1.36] - 2025-05-20



## [0.1.35] - 2025-05-20



## [0.1.34] - 2025-05-19



## [0.1.33] - 2025-05-19



## [0.1.32] - 2025-05-18



## [0.1.31] - 2025-05-18



## [0.1.30] - 2025-05-15



## [0.1.29] - 2025-05-12



## [0.1.28] - 2025-05-10



## [0.1.27] - 2025-05-10



## [0.1.26] - 2025-04-10



## [0.1.25] - 2025-04-09



## [0.1.24] - 2025-03-29



## [0.1.23] - 2025-03-29



## [0.1.22] - 2025-03-28



## [0.1.21] - 2025-03-27



## [0.1.20] - 2025-03-27



## [0.1.19] - 2025-03-24



## [0.1.18] - 2025-03-15



## [0.1.17] - 2025-03-11



## [0.1.16] - 2025-03-09



## [0.1.15] - 2025-03-09



## [0.1.14] - 2025-03-08

## [0.1.13] - 2025-03-08

## [0.1.12] - 2025-03-08

## [0.1.11] - 2025-03-08

## [0.1.10] - 2025-03-08

## [0.1.9] - 2025-03-08

## [0.1.8] - 2025-03-08

### Added

-   Implemented Action system core components:

    -   Defined Action interface with methods for identification, execution, and metadata
    -   Created Tool and Request classes for static and dynamic actions
    -   Implemented Context and ContextValue classes for managing contextual data
    -   Added ActionFactory for registering and retrieving actions
    -   Implemented ActionResult for handling action execution results
    -   Added comprehensive tests for Action interface, Context, and ContextValue classes

-   Implemented Context Management System:

    -   Designed and implemented Context data structure for storing contextual information
    -   Created ContextValue class for type-safe storage of different data types
    -   Implemented ContextProvider interface for collecting context from various sources
    -   Created platform-specific implementations for context providers (macOS, Windows, generic)
    -   Implemented providers for clipboard content, active window, selected text, open files, current directory, current application, and time/location
    -   Added ContextManager for centralized management of context providers
    -   Created ContextProviderFactory for creating platform-specific providers
    -   Added comprehensive tests for context providers and context management

-   Implemented Static Actions:

    -   Created actions infrastructure for registering static tools
    -   Implemented AppLaunchTool for launching applications through the Action interface
    -   Added support for application path and arguments in the AppLaunchTool
    -   Integrated AppLaunchTool with the platform interface for cross-platform compatibility
    -   Added comprehensive tests for the AppLaunchTool
    -   Updated main application to register and use actions

-   Enhanced LauncherBar UI:
    -   Renamed SearchWindow to LauncherBar to reflect expanded functionality
    -   Updated UI interface to support both application launching and action execution
    -   Added support for displaying and executing actions based on context
    -   Implemented methods for input text management, result selection, and theme customization
    -   Enhanced result item structure to include action information and descriptions
    -   Added action callback for executing actions with context
    -   Updated main application to integrate with the Action system and Context Management System
    -   Implemented backward compatibility for existing code

## [0.1.7] - 2025-03-07

## [0.1.6] - 2025-03-07

## [0.1.5] - 2025-03-07

## [0.1.4] - 2025-03-06

## [0.1.3] - 2025-03-06

## [0.1.2] - 2025-03-06

## [0.1.1] - 2025-03-06

### Added

-   Enhanced search bar and results list UI:
    -   Implemented custom cell views with application icons
    -   Added keyboard navigation (up/down arrows, return key)
    -   Implemented icon loading and caching system with NSCache
    -   Improved UI styling with modern macOS appearance
-   Created macOS app bundle packaging:
    -   Implemented release build configuration with optimizations
    -   Created proper macOS app bundle structure with Info.plist
    -   Added LSUIElement flag for menu bar only application
    -   Configured app bundle for installation in Applications folder
    -   Created packaging script for automated app bundle creation
-   Implemented settings interface:
    -   Created settings window with customization options
    -   Added support for customizing activation hotkey
    -   Implemented launch at login functionality
    -   Added option to set maximum number of search results
    -   Integrated with ConfigManager for settings persistence
-   Implemented platform-specific features:
    -   Added global hotkey registration for macOS using Carbon API
    -   Implemented launch at login functionality for macOS using ServiceManagement framework
    -   Added platform version detection

### Changed

-   Improved search results display with application icons and better formatting
-   Enhanced window appearance with transparent titlebar and better spacing
-   Fixed arrow key navigation crash by properly calling C++ setSelectedIndex method
-   Changed launch callback to use launchApplication instead of launchApp alias
-   Updated project documentation with comprehensive memory.md file
-   Updated build system to link against Carbon and ServiceManagement frameworks for macOS

### Fixed

-   Fixed segmentation fault when using arrow keys for navigation
-   Fixed application launch functionality by using correct method name
-   Fixed build errors related to missing AppIconData and AppIconDataLength definitions

## [0.1.0] - 2024-03-05

### Added

-   Implemented macOS menu bar application shell:

    -   Created `macos_ui.h` and `macos_ui.mm` for the core UI functionality
    -   Added `AppIcon.m` for the application icon resources (rocket icon)
    -   Implemented `CMakeLists.txt` for building the macOS UI component
    -   Added status bar icon with dropdown menu
    -   Implemented search window with text field and results table
    -   Added global hotkey support (Command+Space)
    -   Added Escape key to close the search window

-   Set up UI testing infrastructure:
    -   Created `macos_ui_test.mm` for testing the macOS UI
    -   Added `CMakeLists.txt` for UI tests
    -   Updated the main tests `CMakeLists.txt` to include UI tests
    -   Implemented basic tests for UI initialization, window management, and results updating

### Changed

-   Updated build system to support macOS UI components and tests
