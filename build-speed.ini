
DEEP MODE – BUILD-SPEED GAP ANALYSIS
====================================

I. Baseline - What we already have
----------------------------------
1. Build system: CMake + Ninja (fastest mainstream generator).
2. Local parallelism: `cmake --build . --parallel $(nproc)` – good.
3. Compiler cache: auto-enables `ccache` if present.
4. Separate `Release` vs `Debug` profiles with sane flags.
5. Static analysis (clang-tidy / IWYU) is outside normal build.

This is already a “good” baseline, so further wins require more targeted optimisation.

II. Where the time usually goes
-------------------------------
• Front-end (parsing / template instantiation) ≈ 60-80 %
• Linking (ld64) ≈ 10-25 %
• CMake configure/generate ≈ 2-5 %
• Tests, code-sign, notarise, etc. (pipeline, not pure build) ≈ 5-15 %

III. Hot-spot identification options
------------------------------------
A. Build profiling  
   • `ninja -t stats` / `ninja -t compdb | clang++ @- -ftime-trace`  
   • `ninja -d stats` → identifies longest compilation units.  
B. Link-time stats: `-Wl,-time` (ld64) or use `lld` with `-ftime-trace`.  
C. CMake trace: `cmake --trace-expand …` to find superfluous dependency scans.

Without hard numbers yet, the usual culprits are:
• Header bloat / transitive includes (§VI-B).  
• Re-compilation of unchanged code due to broad target dependency graphs (§VI-D).  
• Expensive link (many objects, no incremental link, no `lld`).

IV. Strategy menu
-----------------
Below is a menu grouped by ROI (▲ = big win, ● = medium, ○ = small) and cost:

A. Compiler-side accelerators  
▲ Pre-compiled headers (PCH) via CMake’s `target_precompile_headers(...)`.  
▲ Unity/Jumbo builds (`CMAKE_UNITY_BUILD=ON`, tune `CMAKE_UNITY_BUILD_BATCH_SIZE`).  
▲ Clang C++20 Modules (experimental, high effort, future-proof).  
● Thin-LTO + Incremental build (clang, retains caching cross TUs).  
○ `-ftime-trace` gated builds for profiling (developer-only).

B. External caching / distribution  
▲ `sccache` (remote or local) – drop-in replacement for `ccache`, parallelises object compression & supports clang’s `-MJ` dep scanning.  
● `ccache` tuning: set `CCACHE_BASEDIR`, `CCACHE_MAX_SIZE`, `CCACHE_SLOPPINESS=time_macros`.  
○ `ccache --show-stats` in CI to monitor hit rate.

C. Link-time accelerators  
▲ Use `lld` (`-DCMAKE_LINKER=lld` on macOS via `brew install llvm`). In practice 2-3× faster than ld64.  
● Enable incremental linking: `-Wl,-cache_path_lto,<dir>` or Xcode’s `incremental` but lld is simpler.  
○ Split monolithic libs into smaller shared libs to leverage parallel links (trade-off: more dynamic libs at runtime).

D. Dependency graph / target structure  
▲ Break monolith target into smaller, *static* libraries – unchanged targets won’t rebuild.  
● Mark header-only / interface libraries with `add_library(name INTERFACE)` and link as INTERFACE to avoid rebuilds.  
○ Use `object` libraries for heavy TUs reused by multiple downstream libs.

E. Header hygiene  
▲ Enforce IWYU; create umbrella headers for PCH; forbid nested `#include <iostream>` etc.  
● Replace common `.h` includes with fwd declares where possible – compile time drops ~15-30 % in large C++20 codebases.  
○ Use include-guards + `#pragma once` if not already.

F. Tooling & scripts  
▲ Parallel CMake configure with `cmake -E env …` is minor; real gain is to **avoid re-configure**: cache build type env var and only re-run Configure when `CMakeLists.txt` changes.  
● Persist build dir between debug cycles; `run.sh` always runs full configure (costs 1-3 s) – detect “already configured” and skip.  
○ Add `build/compile_commands.json` symlink (equals 0 s but helps tooling).

V. Trade-off matrix
-------------------
| Option | Build-time win | One-time cost | Maint. cost | Risk |
|--------|----------------|---------------|-------------|------|
| PCH    | 30-50 %        | 30-60 min impl | Low         | None |
| Unity  | 20-40 %        | 10 min toggle  | Med (*ODR duplicates / large objs*) | Medium |
| sccache| 0-100 % (hit-based) | 15 min | Low | None |
| lld    | 2-3× faster link | 10 min install | Low | Need codesign trust settings |
| IWYU   | 10-25 %        | multi-day cleanup | Med | None |
| Modules| 30-70 % (long-term) | Weeks | High | Compiler experimental |
| Target split | 0-80 % depending on churn | hours-days | Low | None |

VI. Recommended phased plan (best overall option set)
-----------------------------------------------------
1. **Immediate (0-1 day)**  
   • Install `sccache`, change `build.sh`/`run.sh` launcher vars:  
     `export CCACHE_DISABLE=1; export SCCACHE_BUCKET=...; cmake … -DCMAKE_*_COMPILER_LAUNCHER=sccache`.  
   • Change scripts to *skip* CMake configure if `CMakeCache.txt` exists & build-type unchanged.

2. **Short term (1-3 days)**  
   • Enable PCH:  
     ```cmake
     target_precompile_headers(core PUBLIC
         <vector> <string> "src/core/pch.h"
     )
     ```  
     Add one `pch.h` umbrella with stable RTL/in-house headers.  
   • Measure with `ninja -t recompact` / `ninja -t stats`, iterate.

3. **Medium term (3-5 days)**  
   • Turn on Unity builds for CI/nightly where extra debug symbol size isn’t an issue:  
     `set(CMAKE_UNITY_BUILD ON)` in `ci/BuildRelease.cmake`.  
   • Evaluate `lld` (needs `brew install llvm && export LD=lld`). Address codesign.

4. **Long term (>1 wk)**  
   • Gradual IWYU automated refactors (script already logs offenders → fix top 30).  
   • Pilot LLVM C++20 Modules on a small sub-library (`util/`).  
   • Split `core/` monolith into components (`core/base`, `core/alg`, `core/io`) each as static lib.

VII. Next steps
---------------
1. Pick quick wins: **PCH + sccache + lld** give largest speed-up with negligible risk.  
2. Decide whether Unity builds are acceptable for Debug (bigger .o, slower incremental).  
3. After adopting, profile again – if >80 % time is now linking, consider link caching or partitioning libs.

Let me know which option(s) you’d like to GO implement, or ask for deeper dives into any specific area.

────────────────────────────────────────────────────────────────────────────

Deep mode – Comprehensive build-time analysis and remediation options

────────────────────────────────────────────────────────────────────────────
A. First-pass diagnosis (what’s eating the minutes?)

1. Static analysis pipeline is always on  
   • `ENABLE_CLANG_TIDY` defaults to ON in root `CMakeLists.txt`.  
   • Every translation unit is therefore compiled twice: once by Clang’s
     frontend and again by clang-tidy (plus the AST walk). This alone
     inflates wall-clock build time by 2-4×, especially in Release/O3.

2. Release + -O3 is the default for `build.sh`  
   • Optimisation passes dominate CPU when `-O3` is combined with LTO
     or heavy inlining.  
   • Thin-LTO is OFF by default, but if someone toggles
     `-DKAI_ENABLE_THINLTO=ON`, link and code-gen stages explode.

3. Header explosion & template-heavy, header-only deps  
   • Abseil, Fmt, nlohmann/json, RapidJSON, concurrentqueue are all
     header-only. Each `.cpp` that includes them pays the full parse +
     template instantiation cost.  
   • Kai core also transitively re-exports many of these headers,
     amplifying rebuild scope when a core header changes.

4. Absence of compile cache / PCH / unity build  
   • No `ccache`/`sccache` launcher, no pre-compiled header, and no
     `CMAKE_UNITY_BUILD`. Every incremental build recompiles unchanged
     files.

5. Third-party FetchContent libraries re-built per build directory  
   • Multiple dirs (`build`, `build_debug`, `build_asan`, …) each
     recompile the same externals. A clean build in a fresh dir forces
     a full rebuild.

6. Link & codesign overhead (macOS)  
   • Static linking of big libs + default ld64 makes the link step one
     of the slowest single commands (>30 s is common).  
   • If codesign verification is ON (it is unless
     `KAI_DISABLE_CODESIGN_VERIFY` is set) the binary is re-hashed after
     each incremental link – extra 5-10 s.

────────────────────────────────────────────────────────────────────────────
B. Measuring before fixing (so we know where to attack)

1. Ninja profile  
   `NINJA_STATUS` or `ninja -d stats` shows time spent per edge.  
   `ninja -d profile` → `profile.json` viewable in Chrome trace viewer.

2. Clang time traces  
   Add `-ftime-trace` (or `CMAKE_CXX_FLAGS_RELWITHDEBINFO`) temporarily
   to pinpoint the worst-offending TUs.

3. Linker timing  
   `export LD_PRINT_OPTIONS=1` (ld64) or `-time` flag to see symbol
   resolution, LTO, code-gen slices.

4. Cache hit-rate (when we add ccache)  
   `ccache -s` after a rebuild will quantify savings.

────────────────────────────────────────────────────────────────────────────
C. Remediation options, with pros / cons and estimated win

1. Opt-out static analysis for day-to-day builds  
   a) Default `ENABLE_CLANG_TIDY` to OFF unless user passes
      `-DENABLE_CLANG_TIDY=ON`.  
   b) Provide a `./scripts/analyse.sh` wrapper that re-configures with
      tidy and runs `ninja clang-tidy`.  
   - Win: 2-4× faster full builds; tiny code changes rebuild in seconds.  
   - Con: risk of style / bug regressions if CI isn’t enforcing tidy.  
   - Mitigation: keep tidy as a mandatory CI step.

2. Introduce a “fast-dev” build preset  
   Add `-DCMAKE_BUILD_TYPE=RelWithDebInfo -O1` + `-gline-tables-only`
   and disable LTO, codesign verify, ThinLTO.  
   - Win: 30-50 % quicker compiles, usable stepping in LLDB.  
   - Con: Perf not quite Release; but acceptable for local loops.

3. Compiler cache (`ccache` or `sccache`) via CMake launchers  
   ```
   export CCACHE_DIR=~/.cache/ccache-kai
   cmake -DCMAKE_C_COMPILER_LAUNCHER=ccache \
         -DCMAKE_CXX_COMPILER_LAUNCHER=ccache …
   ```  
   - Win: 5-20× speed-up for incremental builds; almost free.  
   - Con: first build still slow; need to ensure cache isn’t wiped by
     ASan / ThinLTO flag changes.

4. Pre-Compiled Header (PCH) for core & ui  
   • Create `pch.hpp` that includes stable system + Abseil + Json + Fmt.  
   • `target_precompile_headers(core PRIVATE pch.hpp)` (CMake ≥3.16).  
   - Win: up to 60 % reduction in cold compile time for large TUs.  
   - Con: Requires disciplined header hygiene and incrementally bumps
     rebuild cost when `pch.hpp` changes.

5. Unity / jumbo builds (`CMAKE_UNITY_BUILD=ON`)  
   • Groups multiple `.cpp` files into a single TU, amortising header
     parse cost.  
   • Best applied to translation-unit heavy parts (plugins, tests).  
   - Win: 30-70 % less compile time.  
   - Con: Can surface ODR or conflicting-macro issues; disable for
     templates-heavy targets if necessary.

6. Offload / parallelise static libraries  
   • Build heavy third-party libs (abseil, fmt) once in a shared
     “toolchain” build and install to a local prefix. Core builds then
     just link.  
   • Alternatively pin Homebrew versions on macOS.  
   - Win: removes 20-30 % of clean build cost.  
   - Con: extra dependency management work; need reproducible versions.

7. Faster linker  
   • Switch to LLVM `ld64.lld` on Apple (`-fuse-ld=lld`).  
   • Enable incremental linking with `-incremental`.  
   - Win: 2-3× faster links; large debug links go from 30 s → 10 s.  
   - Con: Must verify codesign + Hardened Runtime still pass.

8. Conditional codesign verify  
   • Already supported via `KAI_DISABLE_CODESIGN_VERIFY`.  
   • Expose an env flag in `build.sh` so devs can flip it easily.

9. Header diet / forward declarations  
   • Audit core headers for over-including (esp. STL, Abseil, Json).  
   • Move rarely-used utilities into `.cpp`.  
   - Win: compound improvements on top of PCH/unity.

10. Distributed builds (icing on the cake)  
    • `sccache + dist` or Icecream if team has spare CI runners.  
    • Possible 5-10× on >8 core clusters.

────────────────────────────────────────────────────────────────────────────
D. Recommended path (ordered, incremental)

1. Flip static analysis opt-out: make tidy OFF by default, keep it in CI.  
2. Add ccache launcher to `build.sh` + a `.ccache` pre-configured stanza.  
3. Provide a `build-dev.sh` preset that is RelWithDebInfo + codesign-off.  
4. Introduce a small PCH for core; validate with CI.  
5. Experiment with `CMAKE_UNITY_BUILD=ON` for plugin + test modules.  
6. Evaluate `-fuse-ld=lld` and measure link step.  
7. Longer-term: move heavy header-only deps to compiled static libs.

Executing 1–3 typically yields 70-80 % build-time reduction with minimal
risk. Steps 4-6 can squeeze another 20-30 % but demand more diligence.

Let me know which option(s) you’d like to go ahead with and I’ll switch
to Go mode to implement the corresponding changes.
