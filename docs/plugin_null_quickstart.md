# Quick-start: Writing a Null Plugin

This short guide shows how to create the minimal **null** plugin that validates
<PERSON>'s loader pipeline without touching any OS resource.

> A null plugin exposes metadata only – no capabilities, no runtime needed.

## 1. Skeleton

```cpp
// src/plugins/your_plugin/your_plugin.cpp
#include "core/plugins/abi.h"
#include <cstring>

extern "C" KaiPluginInfo kai_plugin_get_info() {
    KaiPluginInfo info{};
    std::strncpy(info.id, "your_plugin", sizeof(info.id));
    info.abi_major = kKaiAbiMajor;
    info.abi_minor = kKaiAbiMinor;
    info.runtime   = KAI_RUNTIME_NULL;     // Host does nothing else
    std::memset(info.capabilities, 0, sizeof(info.capabilities));
    return info;
}
```

## 2. CMakeLists.txt

```cmake
add_library(your_plugin SHARED your_plugin.cpp)
set_target_properties(your_plugin PROPERTIES PREFIX "" OUTPUT_NAME "your_plugin")

# Convenience copy so `kai-launcher` discovers it in-place
set(PLUGIN_DEST "${CMAKE_RUNTIME_OUTPUT_DIRECTORY}/Plugins")
add_custom_command(TARGET your_plugin POST_BUILD
    COMMAND ${CMAKE_COMMAND} -E make_directory ${PLUGIN_DEST}
    COMMAND ${CMAKE_COMMAND} -E copy $<TARGET_FILE:your_plugin> ${PLUGIN_DEST})
```

## 3. Build & run

```bash
./build.sh          # build host + plugin
./run.sh            # launch Kai – DiagnosticsService will report plugins.discovered=1
```

### Allocator shim

The global **rpmalloc** heap is shared with plugins via the auto-included
`kai_plugin_alloc_shim.h`, so any `new`/`delete` inside the plugin uses the same
arena as the host – zero fragmentation.

---

_See `docs/security/sandbox_profile_template.sb` for adding a sandbox profile
once the plugin needs real capabilities._
