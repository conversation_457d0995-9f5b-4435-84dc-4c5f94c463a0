# MicroLauncher Development Workflow

## Quick Start (Parallel-First)

### **Development Iteration** ⚡ **Recommended**

```bash
# Build + Fast Tests + Run (< 3 seconds total)
./build.sh && ./run.sh

# Or with automatic testing during build:
./build.sh --test && ./run.sh
```

### **Pre-Commit Validation** 🔍

```bash
# Complete test validation (5.02s, 67% faster than legacy)
./scripts/run_tests.sh all
```

## Enhanced Build & Test Scripts

### **1. Primary Build Script** (`./build.sh`)

**Default**: Build + Fast Tests (1.68s, 89% faster than legacy)

```bash
./build.sh                    # Build + Fast parallel tests by default
```

**Options**:

```bash
./build.sh --test-all         # Build + All tests (5.02s)
./build.sh --no-test          # Build only (skip tests)
./build.sh --test-legacy      # Build + Sequential tests (debugging)
./build.sh --help             # Show all options
```

### **2. Development Script** (`./run.sh`)

**Default**: Build + Run application

```bash
./run.sh                      # Standard build and run
```

**With Testing**:

```bash
./run.sh --fast-tests         # Build + Fast Tests + Run
./run.sh --all-tests          # Build + All Tests + Run
./run.sh --legacy-tests       # Build + Sequential Tests + Run (debugging)
./run.sh --help               # Show all options
```

### **3. Unified Test Runner** (`./scripts/run_tests.sh`) ⭐ **NEW**

**Fast Development Testing** (Default):

```bash
./scripts/run_tests.sh        # Fast parallel tests (1.68s)
```

**Complete Validation**:

```bash
./scripts/run_tests.sh all    # All tests in parallel (5.02s)
```

**Legacy Sequential** (Debugging):

```bash
./scripts/run_tests.sh legacy # Sequential execution (15.35s)
```

**Performance Benchmarks**:

```bash
./scripts/run_tests.sh benchmarks  # Benchmarks only
```

**Advanced Options**:

```bash
./scripts/run_tests.sh fast -j 8 -v          # 8 jobs, verbose
./scripts/run_tests.sh all --timeout 600     # 10min timeout
./scripts/run_tests.sh legacy --verbose      # Sequential debugging
```

### **4. CI/CD Quality Checks** (`./scripts/run-checks.sh`)

**Enhanced with parallel testing**:

```bash
./scripts/run-checks.sh       # Format + Analysis + Parallel Tests
```

Includes:

-   Code formatting (clang-format)
-   Static analysis (cppcheck)
-   Parallel build (all CPU cores)
-   Complete parallel test validation (5.02s)

## Specialized Test Runners

### **Fast Development** (`./scripts/run_tests_fast.sh`)

```bash
./scripts/run_tests_fast.sh   # 1.68s, 89% faster, 26/30 tests
```

-   Perfect for TDD and rapid iteration
-   Excludes slow tests: `EventBusIdleTest`, `SearchCacheTest`, `ServiceStartBench`, `McpClientServiceTest`
-   Automatic parallel execution with all CPU cores

### **Complete Validation** (`./scripts/run_tests_parallel.sh`)

```bash
./scripts/run_tests_parallel.sh  # 5.02s, 67% faster, all 30 tests
```

-   Comprehensive test coverage
-   All tests with optimal parallelization
-   Configurable timeout and verbosity

## Performance Comparison

| **Method**            | **Time** | **Improvement** | **Use Case**          |
| --------------------- | -------- | --------------- | --------------------- |
| **Fast Parallel**     | 1.68s    | 89% faster      | Development iteration |
| **All Parallel**      | 5.02s    | 67% faster      | Pre-commit validation |
| **Legacy Sequential** | 15.35s   | Baseline        | Debugging only        |

## Development Workflows

### **Rapid Development Cycle**

```bash
# 1. Code changes
vim src/core/some_file.cpp

# 2. Quick validation (1.68s)
./scripts/run_tests.sh

# 3. Build and test run
./build.sh && ./run.sh
```

### **Feature Development**

```bash
# 1. Initial development
./build.sh --test           # Build + Test

# 2. Iterate with fast feedback
./scripts/run_tests.sh      # Fast tests only (1.68s)

# 3. Complete validation before commit
./scripts/run_tests.sh all  # All tests (5.02s)

# 4. Final verification
./scripts/run-checks.sh     # Format + Analysis + Tests
```

### **Debugging Test Issues**

```bash
# 1. Identify failing test
./scripts/run_tests.sh all

# 2. Run specific failing test
cd build && ctest --rerun-failed --output-on-failure

# 3. Debug with sequential execution if needed
./scripts/run_tests.sh legacy --verbose

# 4. Debug specific timing issues
ctest -R "TestName" --verbose
```

### **Performance Testing**

```bash
# Run only benchmarks
./scripts/run_tests.sh benchmarks

# Or direct CTest
cd build && ctest -L bench --output-on-failure
```

## IDE Integration

### **VS Code** (`tasks.json`)

```json
{
    "version": "2.0.0",
    "tasks": [
        {
            "label": "Fast Tests",
            "type": "shell",
            "command": "./scripts/run_tests.sh",
            "group": "test",
            "presentation": { "echo": true, "reveal": "always" }
        },
        {
            "label": "All Tests",
            "type": "shell",
            "command": "./scripts/run_tests.sh all",
            "group": "test"
        },
        {
            "label": "Build + Test",
            "type": "shell",
            "command": "./build.sh --test",
            "group": "build"
        }
    ]
}
```

### **CLion/Other IDEs**

-   **Build**: `./build.sh --test`
-   **Test**: `./scripts/run_tests.sh`
-   **Debug**: `./scripts/run_tests.sh legacy --verbose`

## CI/CD Integration

### **GitHub Actions Example**

```yaml
- name: Build and Test
  run: ./scripts/run-checks.sh

- name: Complete Validation
  run: ./scripts/run_tests.sh all --timeout 300

- name: Performance Benchmarks
  run: ./scripts/run_tests.sh benchmarks
```

### **Local Pre-commit Hook**

```bash
#!/bin/bash
# .git/hooks/pre-commit
./scripts/run_tests.sh all
```

## Troubleshooting

### **Common Issues**

**Build directory not found**:

```bash
# Solution: Build first
./build.sh --no-test
```

**Tests not configured**:

```bash
# Solution: Reconfigure CMake
cd build && cmake .. -G Ninja
```

**Slow performance**:

```bash
# Check CPU utilization during tests
./scripts/run_tests.sh fast -v
```

**Test failures**:

```bash
# Get detailed output
./scripts/run_tests.sh all --verbose

# Debug specific test
cd build && ctest --rerun-failed --output-on-failure

# Sequential debugging if timing-related
./scripts/run_tests.sh legacy --verbose
```

## Best Practices

### **✅ Recommended**

-   Use fast tests (`./scripts/run_tests.sh`) for development
-   Run complete validation (`./scripts/run_tests.sh all`) before commits
-   Use enhanced build script (`./build.sh`) for consistent workflow
-   Leverage parallel execution for all workflows

### **⚠️ Use Sparingly**

-   Legacy sequential execution (`./scripts/run_tests.sh legacy`) only for debugging
-   Manual CTest commands - prefer wrapper scripts

### **❌ Avoid**

-   Running tests without parallel execution (unless debugging)
-   Skipping tests during development
-   Using outdated sequential-only workflows

## Migration Guide

### **From Legacy Workflow**

```bash
# OLD (15.35s)
cd build && ctest -V

# NEW (1.68s for development, 5.02s for validation)
./scripts/run_tests.sh        # Fast development
./scripts/run_tests.sh all    # Complete validation
```

### **Enhanced Build Integration**

```bash
# OLD
./build.sh
# manually run tests...

# NEW
./build.sh                    # Automatically includes fast tests
./build.sh --test-all         # Complete validation
```

The project now has a **modern, efficient, parallel-first testing infrastructure** that encourages frequent testing during development while maintaining comprehensive validation capabilities.
