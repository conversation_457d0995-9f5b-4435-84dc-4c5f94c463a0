# Slice-2 Implementation Prompt Library

> Use these prompts verbatim (or tailor minimally) when engaging kai-agent/<PERSON>tGP<PERSON> to work on Security & Discovery Slice-2. Each prompt embeds the project's required instructions, coding standards, and hard constraints to maximise output quality while keeping interactions short.

---

## 0. General Preamble (include at top of every prompt)

```
You are kai-agent, an elite C++20 & Objective-C++ engineer working on MicroLauncher (code name: kai).  Follow the **required_instructions** block verbatim.  Operate in **Go mode** unless explicitly asked to analyse.

Hard bans: no `rm -rf`, no Swift / Storyboards, no unsandboxed subprocesses.
Build system: CMake + Ninja.  UI: Objective-C++ only.
Logging: DBG / ERR macros.  Error-handling: Expected<T,E>.
Style: Google C++ (4-space, 100 cols, .clang-format).
Security: Hardened Runtime, codesign & notarisation MUST pass.
```

Place this preamble **once** at the beginning of a chat session or as the first system message when switching topics.

---

## 1. AdaptiveCache (E-1a / 3-1-core)

### Prompt

```
Implement `include/kai/container/adaptive_cache.hh` (header-only fast path, explicit instantiation `.cpp` later) as per Slice-2 blueprint:
• Striped, lock-free shards (CPU×2 default) with adaptive kWays 2…16.
• `template<class K,class V,class Policy>`; expose `expected_find`, `insert`, `evict_if`.
• Zero steady-state heap allocs; rely on fixed-size arrays & `std::optional`.
• Provide `stats()` for MetricSource concept.
Also write `tests/adaptive_cache_stats_test.cpp` (GTest) and `benchmarks/adaptive_cache_hit_bench.cpp` (Google Benchmark).  Conform to all project coding rules.

Expected Output:
- New file `include/kai/container/adaptive_cache.hh` (header-only implementation).
- New file `src/container/adaptive_cache_inst.cpp` containing explicit instantiations.
- New unit test `tests/adaptive_cache_stats_test.cpp`.
- New benchmark `benchmarks/adaptive_cache_hit_bench.cpp`.

Acceptance Criteria:
- `./build.sh` succeeds on Release, ASan and TSan builds.
- `ctest -R adaptive_cache_stats_test` passes with zero failures.
- `benchmarks/adaptive_cache_hit_bench` shows ≥95 % hit-rate @ ≥5 M ops/s on developer machine.
- Linter (`clang-format`) reports no diffs.
- Static analysis shows **zero** dynamic allocations on the fast path.
```

---

## 2. QueueTraits Refactor (E-3a / 3-2-core)

### Prompt

```
Refactor existing `runtime/ring_queue_backend` to use new `QueueTraits` concept.
Deliverables:
1. `include/kai/runtime/event_queue.hh` – add `template<class Traits>` and default alias `RingQueueTraits`.
2. Provide `RingQueueTraits` (lock-free, spin-wait) and `DispatchChannelTraits` (dispatch_semaphore-based) traits in the same header.
3. Update `runtime/ring_queue_backend.hh` to satisfy concept.
4. Unit test `queue_traits_spin_block_test.cpp` measuring hit/miss, high_water_pct.
Ensure zero regressions; existing callers must compile without change.

Expected Output:
- Modified `include/kai/runtime/event_queue.hh` with concept-based Traits API.
- Updated `runtime/ring_queue_backend.hh` implementing `RingQueueTraits`.
- New unit test `tests/queue_traits_spin_block_test.cpp`.

Acceptance Criteria:
- All previous targets still compile without code changes.
- `ctest -R queue_traits_spin_block_test` passes and reports high_water_pct < 60 % under synthetic load.
- No new heap allocations introduced on push/pop hot path (verified via ASan).
```

---

## 3. Mask128 Helpers & PolicyMask Integration (E-1b/c)

### Prompt

```
Add `include/kai/security/mask128.hh` implementing constexpr `Mask128` with `has`, `set`, `popcount`, `to_string`.
Update `security/verifier_strategy.hh` to replace old `EnumMask` with compile-time `PolicyMask` alias of `Mask128`.
Write `tests/mask128_helper_test.cpp` covering bit ops & constexpr correctness.
Preserve zero-alloc property; inline hot funcs.

Expected Output:
- New header `include/kai/security/mask128.hh`.
- Updated `include/kai/security/verifier_strategy.hh` using `PolicyMask`.
- New test `tests/mask128_helper_test.cpp`.

Acceptance Criteria:
- `ctest -R mask128_helper_test` passes.
- All compile-time constexpr evaluations succeed (no ODR-usage complaints).
- Binary size increase ≤ 2 KB (checked via `size` diff).
```

---

## 4. VerificationStore → AdaptiveCache Port (E-1d)

### Prompt

```
Rewrite `include/kai/security/verification_store.hh` to use `AdaptiveCache<CDHash,Verdict>`.
Requirements:
• L1: AdaptiveCache with 64 shards, kWays auto-tuned.
• Miss path falls back to existing L2 SQLite cache (no change).
• Provide `expected_lookup` and `record_verdict` API preserving existing call-sites.
• Update unit tests (`tests/verification_pipeline_order_test.cpp`).

Expected Output:
- Updated `include/kai/security/verification_store.hh` & any companion `.cpp`.
- Updated unit test `tests/verification_pipeline_order_test.cpp`.

Acceptance Criteria:
- Verification pipeline unit test passes with p95 lookup ≤ 7 µs (bench or micro-measure).
- Zero steady-state heap allocs on cache hit path (checked with ASan heap profiler).
- No public API signature changes for callers.
```

---

## 5. FlatSnapshot & ProbeCache Rewrite (3-4 & 3-3)

### Prompt

```
Implement signed FlatSnapshot storage:
1. `include/kai/storage/flat_snapshot.hh` + `.cpp` with header `{magic,version,build_sha,crc32,sig_len,signature}` and mmap-backed readonly view.
2. Provide CLI helper in `tools/kai-snapshot` to build snapshots (optional flag OK).
3. Rewrite `snapshot/probe_cache.hh/.cpp` to use FlatSnapshot (+ AdaptiveCache L1, SQLite L2).
4. Fuzzer `fuzz/flat_snapshot_fuzzer.cpp` for header corruption.
Meet zero-alloc, p95 lookup ≤7 µs, CRC + Ed25519 signature verify.

Expected Output:
- New `include/kai/storage/flat_snapshot.hh` and `src/storage/flat_snapshot.cpp`.
- New CLI tool source under `tools/kai-snapshot/` with CMake target.
- Rewritten `snapshot/probe_cache.hh/.cpp`.
- New fuzz target `fuzz/flat_snapshot_fuzzer.cpp` added to CMake.
- Updated unit test `tests/flat_snapshot_crc_sig_test.cpp`.

Acceptance Criteria:
- `ctest -R flat_snapshot_crc_sig_test` passes.
- `flat_snapshot_fuzzer` builds and reaches ≥10 k exec/s locally.
- ProbeCache p95 lookup latency ≤ 7 µs (micro-bench comparisons).
- Snapshot file passes `codesign --verify` and signature check routine.
```

---

## 6. ServiceBase CRTP Injection (E-4a)

### Prompt

```
Add `include/kai/foundation/service_base.hh` providing `template<class Derived,class... Deps>` CRTP that statically asserts DAG and offers compile-time injection via `static constexpr std::tuple<Deps...> deps`.

Migrate one existing service (`Security::PolicyEngineService`) to inherit ServiceBase as a proof.
Add `core/foundation/service_static_checks.h` with static_assert that all services form an acyclic graph.

Expected Output:
- New `include/kai/foundation/service_base.hh`.
- Updated `Security::PolicyEngineService` source files.
- New header `core/foundation/service_static_checks.h` with DAG static assertions.

Acceptance Criteria:
- Full build passes; no cyclic-dependency static_assert fires.
- Runtime behaviour identical (unit tests unchanged).
- Binary size increase ≤ 1 KB.
```

---

## 7. Metrics & Diagnostics (E-5)

### Prompt

```
Update `include/kai/util/metrics.hh` to expose `concept MetricSource`.
Extend `diagnostics/metrics_exporter.{h,cpp}` to iterate over all `MetricSource` types via `for_each_type` and export Prometheus counters.
Add unit `tests/metric_source_enumeration_test.cpp` validating enumeration of AdaptiveCache and RingQueue instances.

Expected Output:
- Modified `include/kai/util/metrics.hh` with concept definition.
- Updated `diagnostics/metrics_exporter.{h,cpp}` implementing enumeration.
- New unit test `tests/metric_source_enumeration_test.cpp`.

Acceptance Criteria:
- Unit test enumerates ≥2 MetricSource implementations.
- Exported Prometheus text endpoint contains cache.hit counter after 1 second of runtime.
```

---

## 8. RuntimeScanner Coroutine & Executor Fan-out (E-2)

### Prompt

```
Implement `runtime/runtime_scanner.{h,cpp}` as a coroutine that consumes FsEvents, hashes mtime+sha256, and publishes `RuntimeDescriptor` to workers via EventQueue.
Follow blueprint:
• Worker threads = min(hw_threads/2,6).
• On each file, call `security::verifyPlugin()`, update ProbeCache.
• Counter `sec.verifier_ms` and EventQueue `high_water_pct` exported.
Unit test `cold_scan_integration_test.cpp` with 200 mixed runtimes (mocked).

Expected Output:
- New `runtime/runtime_scanner.h` and `.cpp`.
- Updated CMake to link coroutine TS.
- New unit test `tests/cold_scan_integration_test.cpp`.

Acceptance Criteria:
- Integration test passes: cold scan of 200 mocked runtimes ≤ 150 ms (stretch 125 ms).
- High_water_pct < 60 % during test run.
- No deadlocks detected under TSan.
```

---

## 9. CI & Build Flags (E-6)

### Prompt

```
Update CMake:
• Add options `KAI_ENABLE_VPATCH`, `KAI_ENABLE_MUX_QUEUE` default OFF.
• Ensure all new headers part of umbrella `core` target; create explicit instantiation list for AdaptiveCache.
• Add two new CI jobs: ASan+TSan matrix and codesign/notary check for FlatSnapshot.

Expected Output:
- Modified `cmake/KaiOptions.cmake` with new flags.
- Updated top-level `CMakeLists.txt` wiring explicit instantiation sources.
- YAML or config file updates defining two new CI jobs.

Acceptance Criteria:
- CI pipeline passes on all new jobs (GitHub or internal runner).
- Build succeeds with both flags toggled ON and OFF.
```

---

## 10. Docs Refresh (E-6 Day 18)

### Prompt

```
Create /docs/slice2_architecture.md summarising AdaptiveCache, FlatSnapshot, QueueTraits, ServiceBase, MetricSource and listing AC-1…AC-9 metrics collection methodology.
Include sequence diagrams and risk register.

Expected Output:
- New markdown file `docs/slice2_architecture.md` with >500 words, at least one mermaid diagram.
- Embedded table mapping epics → deliverables → metrics.

Acceptance Criteria:
- Document renders without markdownlint errors (`markdownlint docs/slice2_architecture.md`).
- Reviewed and approved by architecture-wg.
```

---

## Usage Tips

1. **Always** paste the _General Preamble_ first.
2. Replace ` with a concise task description if different.
3. Set `<mode>` to `go` to make the assistant implement, or `deep` for analysis.
4. Keep chats focussed: one prompt → one deliverable.
5. After code generation, run `./build.sh && ./run.sh` locally; file lint errors before committing.

---

Happy building †
