## Kai Plugin System – Detailed Implementation Plan

> Scope: Support for JavaScript/TypeScript (JavaScriptCore) and WebAssembly plugins under a hardened, capability-based security model.

---

### 1. Architectural Road-map – Three Slices (UPDATED 2025-05-30)

| Slice                              | Focus                               | Approx Duration | Key Deliverables                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      | Exit Criteria                                                                                                                    |
| ---------------------------------- | ----------------------------------- | --------------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | -------------------------------------------------------------------------------------------------------------------------------- |
| **Slice 1 – Bootstrap**            | Kernel & Diagnostics                | 3 weeks         | • ServiceRegistry with **numeric ServiceId enum** + `ServiceTraits` compile-time DAG<br>• **Checked-in canonical headers** (`service_id.h`, `service_topology.h`, `service_name_phf.h`). Changes are maintained manually; compile-time static*asserts catch drift.<br>+ \_Note: any mismatch between the enum, dependency arrays, or `kSorted` order triggers a `static_assert` in `service_static_checks.h`, so CI fails automatically if headers drift.*<br>• (Legacy YAML generator removed.)<br>• **constexpr `service_topology.h`** (pre-sorted) – compile-time cycle checks<br>• ArenaAllocatorSvc backed by **rpmalloc** (per-plugin heap) (`KAI_ALLOCATOR=mimalloc` for debug)<br>• ExecutorSvc & **EventBusSvc (blocking `wait_dequeue` variant + back-pressure metrics)**<br>• Diagnostics baseline (counters + micro-bench)<br>• `capability128.h` generator (128-bit mask)<br>• NullRuntime & stub RuntimeManager able to load a "null" runtime<br>• Seatbelt generator CLI (`kai-seatbelt-gen`, render-only) **+ read-only enforcement**<br>• Ad-hoc codesign **verify** | End-to-end services start/stop < 50 ms; Service lookup O(1) bench passes; `event_bus.idle_cpu_pct` ~0; Diagnostics counters live |
| **Slice 2 – Security & Discovery** | Capability, ProbeCache, Enforcement | 3 – 4 weeks     | • Capability mask plumbing to 128-bit bitmap + header-only helpers<br>• `PolicyEngineService` with **hard-deny** (CLI `--dry-run` flag)<br>• Seatbelt profile enforced **with pre-compiled cache (`.sb.bin`) to shave ≥3 ms per plugin**<br>• **ProbeCache.sqlite + parallel runtime scan via ExecutorSvc (cold-scan ≤150 ms for 200 runtimes)**<br>• Async codesign & seatbelt compile offloaded to Executor, verdict persisted to ProbeCache<br>• Introduce `RuntimeDescriptor` + `RuntimeAdapter` model; RuntimeManager dispatches Native/JS/Wasm via unified interface<br>• `JsRuntime` via `RuntimeBase` + `JSContextPool`<br>• "hello_js" plugin demo<br>• Experimental **WasmEdge** runtime flag (`KAI_USE_WASM_EDGE`)<br>• Preferences ▸ Plugins read-only list                                                                                                                                                                                                                                                                                                               | JS plugin round-trip ≤ 5 ms (p95); ProbeCache hit-rate ≥ 95 %; seatbelt violations return `KaiError::CapabilityDenied`           |
| **Slice 3 – Performance & Parity** | Runtime completeness & Hardening    | 4 weeks         | • Wasm runtime hardening: WasmEdge stabilisation + Wasmtime fallback; Native/XPC helper template<br>• **Transport concept & type-erased storage** (`TransportBase<Derived>`; zero virt hops) for future MCP/server bridging<br>• Thin-LTO + ICF; ccache & **incremental C++20 module adoption** (`util/debug`, `time_utils`, …) behind feature flag<br>• Optional **mimalloc** backend (`KAI_ALLOCATOR=mimalloc`) for debug instrumentation<br>• **HDR histogram & OTLP exporter** integrated into Diagnostics (latency buckets, memory budgets)<br>• **JSC & Wasm allocators hooked into ArenaAllocatorSvc; per-plugin memory budgets enforced**<br>• Context-pool LRU & watchdog timers<br>• Benchmarks suite; ASan / TSan matrix<br>• Codesign **notarisation**, fuzzers, OWASP audit                                                                                                                                                                                                                                                                                              | Wasm ≥ 85 % native; Thin-LTO shrinks binary ≥ 15 %; module-enabled incremental rebuilds ≤ 60 % baseline; notarisation passes     |

---

### 2. Detailed Slice Breakdown (condensed)

#### Slice 1 – Bootstrap (Weeks 1-3)

1. `abi/abi.h` – C ABI symbols & version negotiation (`kKaiAbiMajor`, `kKaiAbiMinor`).
2. `manifest.schema.json` – JSON schema for plugin manifests.
3. `capability128.h` – **128-bit enum + constexpr lookup** generated from schema.
4. `core/foundation/service_id.h` – numeric IDs; dependencies live in each service via `ServiceBase::kDeps`.
5. `ServiceRegistry` (array-backed) + legacy string-lookup shim.
6. `ArenaAllocatorSvc` backed by **rpmalloc** (per-plugin heap) & `ExecutorSvc` integrated into registry. (`KAI_ALLOCATOR=mimalloc` enables debug heap).
7. `EventBusSvc` upgraded to **blocking `wait_dequeue()` variant**; micro-benchmark (≥ 10 k msgs/s, idle CPU < 0.1 %).
8. Diagnostics baseline (`DiagnosticsService`) – counters, micro-bench hooks **+ `SpanGuard` RAII profiling around each `IService::start()`**.
9. Seatbelt generator CLI (`kai-seatbelt-gen`) emits profile **and read-only enforcement (no net/exec)**.
10. **`KAI_VERIFY_ZERO_ALLOC` compile-time guard** – unit-test asserts zero heap allocs on hot paths.
11. `NullRuntime` + stub `RuntimeManagerSvc` able to load a no-op runtime.
12. CI: ad-hoc codesign **verify** + ASan build.
13. `constexpr service_topology.h` – **build-generated** pre-sorted array eliminates runtime topo-sort; cycles detected at code-gen.
14. `ServiceBase<Derived,Deps…>` CRTP mix-in for constructor injection; replaces ad-hoc `attach*` helpers, enforces deps at compile-time.
15. Merge `PluginManagerSvc` into **RuntimeManagerSvc**; single discovery path for Native/JS/Wasm plugins.
16. **Native plugin allocator shim** (`kai_plugin_alloc_shim.h`) injects `operator new/delete` forwarding to `ArenaAllocatorSvc`, guaranteeing a single rpmalloc heap across core **and** plugins and eliminating mixed‐allocator fragmentation.

#### Slice 2 – Security (Weeks 4-7)

1. Header-only capability mask helpers (`has(cap)` etc.) built around a compact `uint64_t[2]` representation (16 bytes holding the 128-bit mask) passed by const-ref to minimise copying.
2. `PolicyEngineService` – evaluates HostOps; **hard-deny** default, override via `--dry-run`.
3. Seatbelt profile enforced in Debug & Release **with pre-compiled binary cache (`.sb.bin`, keyed by SHA-256 of profile text) shaving ≈3 ms per plugin** (env `KAI_SB_DISABLE` only honoured in Debug).
4. **ProbeCache.sqlite** with `{path, mtime, sha256, verdict}` rows; parallel scan tasks enqueued via `ExecutorSvc`; **lock-free MPMC ring exposes `queue_pct` metric; `KaiError::BackPressure` returned when >90 % full.**
5. Async codesign & seatbelt compile offloaded to `ExecutorSvc`; verdict stored in ProbeCache.
6. `JsRuntime` derived from `RuntimeBase` + `JSContextPool` (LRU=2).
7. Experimental **WasmEdge** runtime path (`KAI_USE_WASM_EDGE`) integrated; Preferences ▸ Plugins pane lists plugins & capabilities.
8. Diagnostics counters expanded (capability hit/miss, alloc stats, `runtime.scan_ms`).
9. CI: ASan + TSan matrix, fuzzers (libFuzzer) on manifest & seatbelt.
10. Codesign **notarisation** pipeline; OWASP audit; release tag 1.0.0.

These improvements feed into the slice deliverables and risk mitigations below.

#### Slice 3 – Performance & Parity (Weeks 8-11)

1. `WasmRuntime` (WasmEdge hardened, Wasmtime fallback) & Native/XPC helper template.
2. **Transport concept & type-erased storage** (`TransportBase<Derived>`) wired into `RuntimeManagerSvc`.
3. Thin-LTO + ICF, deterministic flags (`-ffile-prefix-map`, `SOURCE_DATE_EPOCH`).
4. **Incremental C++20 module adoption**: convert `util/debug.h`, `util/time_utils.h`, etc.; BMIs cached via ccache.
5. Optional **mimalloc** backend (`KAI_ALLOCATOR=mimalloc`) toggled for debug/ASan.
6. `JSContextPool` LRU integration, watchdog (timeout <100 ms).
7. **HDR histogram & OTLP exporter** in Diagnostics (latency buckets, memory budgets).
8. Benchmarks: JS ≤ 5 ms round-trip, Wasm ≥ 85 % native.
9. CI: ASan + TSan matrix, fuzzers (libFuzzer) on manifest & seatbelt.
10. Codesign **notarisation** pipeline; OWASP audit; release tag 1.0.0.

These improvements feed into the slice deliverables and risk mitigations below.

---

### 3. Strategic Improvements (cross-cutting)

The following long-term improvements apply across all slices to simplify the codebase, maximise scalability and guarantee deterministic performance:

1. **Compile-time Service Topology** – `service_id.h` and `service_topology.h` are **checked into source control**. Dependency arrays are verified by `static_assert`s (`service_static_checks.h`) ensuring cycles or drift fail the build; no external generator step is required.
2. **CRTP `ServiceBase` Constructor Injection** – Replaces ad-hoc `attach*` helpers with `ServiceBase<Derived, Deps…>` storing references in a tuple. Enforces dependency correctness at compile-time and removes extra virtual hops.
3. **Unified `RuntimeManagerSvc`** – Merges `PluginManagerSvc` with JS/Wasm runtime loaders; common `RuntimeDescriptor{type, path, caps}` + shared ProbeCache → less code duplication, single security pipeline.
4. **Zero-allocation Look-ups** – Build minimal-perfect hash (`bbhash`) tables at build-time for service names and plugin IDs; `mmap` on launch for O(1) look-ups with <4 KiB RAM.
5. **Bounded Queues & Back-pressure Metrics** – Lock-free MPMC ring (1 024 slots) for cross-service events with **blocking wait-dequeue** to eliminate idle spin; expose `queue_pct` counter; return `KaiError::BackPressure` when >90 % full.
6. **Observability Upgrade** – OTLP/Prometheus exporter guarded by compile-time flag; HDR histograms for p95 latency and memory budgets; counters such as `plugins.loaded_total`, `runtime.scan_ms_bucket`.
7. **ProbeCache + Parallel Runtime Discovery** – SQLite-backed cache stores `{path, mtime, sha256, verdict}`; directory scan fan-outs to `ExecutorSvc` workers so cold start with 200 runtimes finishes in ≤ 150 ms while avoiding redundant `dlopen`.
8. **Transport Concept & Type-Erased Storage** – Introduce `TransportBase<Derived>` CRTP with kind enum and inline fixed-size buffers; Stdio/HTTP/WebSocket transports compile with zero virtual hops yet stay hot-swappable.
9. **Arena-backed SIMDJSON JSON-RPC Codec** – Adopt `simdjson::ondemand` with custom `ArenaAllocatorSvc` hooks and varint framing for zero-allocation parsing; p95 decode ≤ 50 µs for 1 KiB payloads.
10. **Incremental C++20 Module Adoption** – Convert frequently-included utility headers (`util/debug.h`, `util/time_utils.h`, …) into `kai.*.ixx` modules to cut incremental rebuild time by 20–40 % while gating behind a Clang modules feature flag.
11. **Compact Capability Mask Representation** – Represent the 128-bit capability bitmap as two `uint64_t` words (`Mask128`) and always pass by `const&`; halves cache traffic versus the previous 256-bit design.
12. **Seatbelt Binary Cache & ProbeCache Warm-up** – Cache compiled seatbelt profiles (`.sb.bin`) under `~/Library/Caches/MicroLauncher/sb/` keyed by `sha256(profile_text)` and `mmap` ProbeCache with `MAP_POPULATE` on cold start to eliminate first-use page-faults and shave ≈3 ms per plugin load.
13. **Allocator Shim for Native Plugins** – Build-generated `kai_plugin_alloc_shim.h` ensures every plugin links `operator new/delete` to `ArenaAllocatorSvc`, preventing mixed heaps and improving cache locality.

These improvements feed into the slice deliverables and risk mitigations below.

---

### 4. Risk & Mitigation Table (updated)

| Risk                                                 | Slice | Mitigation                                                                      |
| ---------------------------------------------------- | ----- | ------------------------------------------------------------------------------- |
| BMI incompat between compilers                       | S1    | Pin Clang, fallback non-modules build                                           |
| Plugin ABI drift                                     | S3    | `KaiAbiVersion` struct & loader checks                                          |
| Runtime surface explosion                            | S2    | Central HostApiDispatcher tests & code-gen                                      |
| RSS bloat (JSC/Wasm)                                 | S3    | Per-plugin MemoryPool & watchdog, rpmalloc baseline                             |
| Seatbelt complexity                                  | S2    | Hard enforcement + `--dry-run` flag, single code-path                           |
| Capability mask exhaustion                           | S2    | 128-bit bitmap with reserved high bits                                          |
| Registry rebuild churn                               | S1    | ServiceRegistry two-level lookup                                                |
| Component model divergence                           | S3    | Dual runtime (WasmEdge/Wasmtime) gated by flag                                  |
| Seatbelt compile overhead                            | S2    | Cache pre-compiled `.sb.bin`; reuse when profile unchanged                      |
| Template bloat (CRTP)                                | S1    | Mitigate with precompiled headers + explicit instantiation                      |
| Header drift (`service_id.h` / `service_topology.h`) | S1    | `static_assert` in `service_static_checks.h` fails build if headers out of sync |

---

### 5. Acceptance Criteria Checklist (updated)

-   [ ] Core services start/stop deterministically (< 50 ms total).
-   [ ] JS plugin round-trip call latency ≤ 5 ms (p95).
-   [ ] Capability denial returns structured `KaiError`.
-   [ ] Watchdog terminates runaway script with < 100 ms overhead.
-   [ ] Wasm plugin ≥ 85 % native speed in micro-bench.
-   [ ] Thin-LTO + ICF reduce binary size ≥ 15 %.
-   [ ] rpmalloc memory overhead < 1 % vs system `malloc`.
-   [ ] EventBus ≥ 10 k msgs/s, < 0.5 ms latency (p95).
-   [ ] Cold start (no plugins) < 300 ms on reference hardware.
-   [ ] Plugin cold-load: JS < 80 ms, Wasm < 40 ms (p95).
-   [ ] Service lookup O(1) verified by micro-benchmark.
-   [ ] Core-only changes recompile ≤ 30 object files (ServiceRegistry two-level lookup).
-   [ ] WasmEdge runtime passes full plugin test-suite within 5 % perf delta.
-   [ ] All artifacts pass ad-hoc codesign **verify** from Slice 1 and notarisation by Slice 3.

---

### 6. References

-   Apple JavaScriptCore API Docs – <https://developer.apple.com/documentation/javascriptcore>
-   Wasmtime C++ Embedding Guide – <https://docs.wasmtime.dev/cpp.html>
-   WasmEdge C++ Embedding Guide – <https://wasmedge.org/book/en/embed/cpp.html>
-   rpmalloc – <https://github.com/mjansson/rpmalloc>
-   OWASP Top 10 for LLM – <https://owasp.org/www-project-top-10-for-large-language-model-applications/>

### 7. Service & Plugin Inventory

#### 7.1 Core Services (all implement `IService`) – Phase Mapping Update

| Service ID            | Responsibility                                          | Slice Introduced                          |
| --------------------- | ------------------------------------------------------- | ----------------------------------------- |
| `ServiceRegistry`     | Dependency injection container; compile-time DAG        | S1                                        |
| `ArenaAllocatorSvc`   | Provides pooled/arena memory resources                  | S1                                        |
| `ExecutorService`     | Thread-pool for async tasks                             | S1                                        |
| `EventBusService`     | Pub/Sub event routing for decoupled components          | S1                                        |
| `DiagnosticsService`  | Collects metrics, forwards to UI / logs                 | S1                                        |
| `RuntimeManagerSvc`   | Discovery, signature validation, (un)loading of plugins | S1                                        |
| `PolicyEngineService` | Evaluates actions against security policies             | S2                                        |
| `JsEngineService`     | Owns per-plugin `JSContext` instances                   | S2                                        |
| `WasmRuntimeService`  | Hosts WasmEdge/Wasmtime instances                       | S3                                        |
| `IndexService`        | Wrapper around `AppIndex`, exposes search APIs          | S1                                        |
| `ConfigService`       | Persisted user configuration                            | S1                                        |
| `HistoryService`      | Search & launch history                                 | S1                                        |
| `LlmFactoryService`   | Factory / registry for LLM back-ends                    | S1                                        |
| `HotpatchTableSvc`    | Provides HotpatchVerifierTable and metrics              | S3 (skeleton, awaits ServiceId generator) |

> NOTE: UI-level controllers (e.g., Preferences pane) will resolve these services through the registry but are **not** themselves `IService`s.

#### 7.2 Plugin Catalogue (initial wave)

| Plugin ID          | Runtime      | Trust Tier | Provides IService?                                      | Notes                                      |
| ------------------ | ------------ | ---------- | ------------------------------------------------------- | ------------------------------------------ |
| `scanner_macos`    | Native dylib | Elevated   | Yes (via C shim)                                        | Extracted from core; file-system scanning. |
| `llm_openai`       | Native dylib | Elevated   | Yes                                                     | OpenAI REST LLM backend.                   |
| `llm_anthropic`    | Native dylib | Elevated   | Yes                                                     | Anthropic Claude backend.                  |
| `sample_hello_js`  | JS (JSC)     | Community  | No – registers services through `kai.registerService()` | MVP demonstration.                         |
| `sample_math_wasm` | Wasm-native  | Community  | No                                                      | Perf demo; exports `add`, `mul` etc.       |

Lifecycle rules:

1. **Native / XPC Plugins** MUST export the following C symbols:

    ```c
    extern "C" KaiPluginInfo kai_plugin_get_info();
    extern "C" KaiStatus     kai_plugin_initialize(KaiServiceRegistry* reg);
    extern "C" KaiStatus     kai_plugin_start();
    extern "C" void          kai_plugin_stop();
    ```

    These map 1-to-1 to the host's `IService` phases. The RuntimeManager creates a thin `NativePluginService` wrapper implementing `IService` that forwards lifecycle calls to these exports.

2. **JS / Wasm Plugins** do **not** directly implement `IService` but can _register_ one or more services via the injected `kai.registerService(name, obj)` host API during their module's top-level execution. The JavaScriptCore context itself is owned by `JsEngineService`, which participates in the global lifecycle and takes responsibility for starting/stopping script execution.

3. All services registered by a plugin inherit the plugin's capability set and will be automatically unregistered by RuntimeManager when the plugin stops.

---

### 8. High-Level Architecture Diagram

```
┌──────────────────────────────────────────────┐
│                 kai-launcher                 │
│  (App Bundle, hardened runtime)              │
│                                              │
│  ┌─────────────┐   IService    ┌───────────┐ │
│  │ Service     │<──────────────│  UI Layer │ │
│  │  Kernel     │               │ (ObjC++)  │ │
│  │ (C++20)     │──────────────>│   Views   │ │
│  │  ├─Registry │   events      └───────────┘ │
│  │  ├─Executor │──────────────┐              │
│  │  ├─EventBus │              │              │
│  │  └─Services │<─────────────┘              │
│  └──────┬──────┘                             │
│         │   dlopen / JSContext / WasmEdge/Wasmtime    │
│         ▼                                    │
│  ┌──────────────────────────────────────────┐│
│  │            Plugin Boundary               ││
│  │  • Native .dylib (Elevated)              ││
│  │  • JavaScriptCore (Community)            ││
│  │  • Wasm (JSC / WasmEdge/Wasmtime)                 ││
│  │  • XPC Sandboxed Helpers (Isolated)      ││
│  └──────────────────────────────────────────┘│
└──────────────────────────────────────────────┘
```

-   Solid arrows = synchronous calls (direct C++ / Objective-C++).
-   Dashed arrows = EventBus pub/sub.
-   Plugin boundary enforced via capability gating and signature checks.

---

### 9. Repository Directory Diagram (target state)

```text
src/
├── core/
│   ├── foundation/          # IService, ServiceRegistry, life-cycle helpers
│   │   ├── service.h
│   │   └── registry.{h,cpp}
│   ├── async/               # Concurrency primitives
│   │   └── executor.{h,cpp}
│   ├── events/              # Pub/Sub event bus + event structs
│   │   ├── event_bus.{h,cpp}
│   │   └── types/
│   ├── services/            # Concrete service impls (IndexService, ConfigService…)
│   │   ├── index_service.{h,cpp}
│   │   ├── config_service.{h,cpp}
│   │   ├── history_service.{h,cpp}
│   │   ├── runtime_manager_service.{h,cpp}
│   │   ├── event_bus_service.{h,cpp}   # thin wrapper around core/events
│   │   ├── diagnostics_service.{h,cpp}
│   │   ├── policy_engine_service.{h,cpp}
│   │   └── llm_factory_service.{h,cpp}
│   ├── modules/             # C++20 module units (header-only algorithms)
│   │   ├── kai.fuzzy.ixx
│   │   ├── kai.json.ixx
│   │   └── …
│   ├── wasm/                # Native Wasmtime runtime wrapper
│   │   └── wasm_runtime.{h,cpp}
│   └── js/                  # JavaScriptCore engine integration
│       └── js_engine_service.{h,mm}
│
├── plugins/                 # Built & installed to App/Contents/Plugins/
│   ├── scanner_macos/       # Elevated native plugin (ex-core)
│   ├── llm_openai/
│   ├── llm_anthropic/
│   ├── sample/
│   │   ├── js/hello.js
│   │   └── wasm/math.wasm
│   └── template/
│       └── native/          # Developer skeleton for new native plugin
│
├── ranking/                 # Header-only scoring algorithms (migrated)
├── ui/                      # Objective-C++ AppKit UI
├── tests/                   # Unit & integration tests (GTest)
├── benchmarks/              # Micro-benchmarks & performance tests
├── scripts/                 # Tooling (kai-tsc-build.sh, signing helpers)
└── docs/                    # Developer docs & SDK guides
```

### 10. Memory Allocator Strategy – rpmalloc Baseline

Kai standardises on **rpmalloc** as the global allocator for both core and plugins. The `ArenaAllocatorSvc` remains the single public entry point, now implemented as a _header-only façade_ that forwards to rpmalloc while preserving API compatibility.

Key properties of rpmalloc:

1. Lock-free per-thread heaps, NUMA-aware.
2. O(1) allocation / free with deterministic ≤ 150 ns latency (p95).
3. Tiny code size (~25 KiB) and zero global locks.
4. Hardened runtime compliant – no RWX pages required.

Implementation guidelines:

• Build flag `-DKAI_ALLOCATOR=mimalloc` swaps in **mimalloc** for debug/ASan runs; production CI uses rpmalloc.
• Huge objects (≥ 128 KiB) are still served via `mmap` but wrapped in `TrackedMapping` so DiagnosticsService can enforce per-plugin budgets.
• `ArenaAllocatorSvc::stats()` publishes live heap metrics to Diagnostics and PolicyEngine.

Example usage for plugin authors:

```cpp
#include <kai/arena_allocator.h>
void* p = kai::alloc(64);
// ...
kai::free(p);
```

#### Memory Pool Strategy – Hybrid Arena + TLSF

To balance deterministic performance with low fragmentation across heterogeneous runtimes (C++, JavaScriptCore, Wasmtime), the host will expose a hybrid allocator to plugins:

Bump Arena (small objects) – allocations ≤ 256 B served from per-thread bump pointers; freed en-masse on GC/epoch rollover.
TLSF Slab (medium objects) – 256 B … 128 KiB handled by an in-process Two-Level Segregated-Fit pool providing O(1) alloc/free.
Huge Pages (≥ 128 KiB) – delegated directly to mmap and reclaimed with madvise(MADV_FREE).
The ArenaAllocatorSvc presents this policy behind a single header-only API; runtime shims (JSC / Wasmtime) forward their custom alloc hooks to it.

### 11. Tiered Plugin Loading

Kai supports three loading tiers to optimise start-up and hot-patch workflows:

| Tier          | Trigger                            | Actions                                                                                       |
| ------------- | ---------------------------------- | --------------------------------------------------------------------------------------------- |
| **Lazy**      | First invocation of plugin service | Parse manifest → signature check → load code → initialise                                     |
| **Pre-warm**  | Application splash / idle window   | Background load & init (JSContext/Wasm) so first call is fast                                 |
| **Hot-patch** | Developer upgrade or auto-update   | `stop` → dlopen new dylib (codesign validated) → `start`; preserves registry/state where safe |

Hot-patch is limited to _native elevated_ plugins and enforced by the PolicyEngine.

### 12. Null Plugin Quick-start

A minimal **null plugin** validates the loader pipeline without requiring a runtime.

1. Create a new directory under `src/plugins/your_plugin/` with a single `your_plugin.cpp` exporting:

```cpp
#include "core/plugins/abi.h"
#include <cstring>
extern "C" KaiPluginInfo kai_plugin_get_info() {
    KaiPluginInfo info{};
    std::strncpy(info.id, "your_plugin", sizeof(info.id));
    info.abi_major = kKaiAbiMajor;
    info.abi_minor = kKaiAbiMinor;
    info.runtime   = KAI_RUNTIME_NULL; // Null runtime – host does nothing else
    std::memset(info.capabilities, 0, sizeof(info.capabilities));
    return info;
}
```

2. Add a `CMakeLists.txt` similar to:

```cmake
add_library(your_plugin SHARED your_plugin.cpp)
set_target_properties(your_plugin PROPERTIES PREFIX "" OUTPUT_NAME "your_plugin")

# Optional convenience copy so dev builds auto-discover it
set(PLUGIN_DEST "${CMAKE_RUNTIME_OUTPUT_DIRECTORY}/Plugins")
add_custom_command(TARGET your_plugin POST_BUILD
    COMMAND ${CMAKE_COMMAND} -E make_directory ${PLUGIN_DEST}
    COMMAND ${CMAKE_COMMAND} -E copy $<TARGET_FILE:your_plugin> ${PLUGIN_DEST})
```

3. Re-run `./build.sh` – the host will report counts via Diagnostics (`plugins.discovered`) and emit a `PluginScanCompleteEvent`.

Use `PluginError` → string mapping for UI messaging if validation fails.
