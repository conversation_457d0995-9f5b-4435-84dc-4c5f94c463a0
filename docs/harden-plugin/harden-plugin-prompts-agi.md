## Hardened Plugin System – Implementation Prompt Pack

The following prompts are structured implementation tasks for upgrading **MicroLauncher**'s current naïve dynamic–library plugin mechanism to a defence-in-depth, permissioned, and sandboxed architecture. Each prompt references concrete files/functions that already exist in the repository so they can be executed incrementally in **Go mode**.

---

### 1 • Secure Plugin Discovery & Loading

**Prompt:**
"Refactor `src/core/llm/plugin_loader.{h,cpp}` so that `loadDirectory()` only enumerates plugins from:
• `~/Library/Application Support/MicroLauncher/Plugins` _(user-level)_
• `${APP_BUNDLE}/Contents/PlugIns` _(bundled)_
The new loader **must**:

1.  Validate that the file has extension `.dylib` **and** is codesigned & notarised – use `SecStaticCodeCheckValidity()` with `kSecCSCheckAllArchitectures | kSecCSStrictValidate`.
2.  Reject any plugin whose TeamID ≠ host TeamID unless it appears in `plugins_allowlist.json`.
3.  Log acceptance/rejection via `DBG/ERR`.
4.  Return a rich `Expected<void, std::string>` instead of bool."

---

### 2 • Plugin Manifest Schema & Parser

**Prompt:**
"Create `src/core/plugin_manifest.{h,cpp}` implementing:
• struct `PluginPermissions { bool needs_network; bool needs_files; bool needs_exec; };`
• struct `PluginManifest { std::string id, version; PluginPermissions perms; }`;
• static `Expected<PluginManifest,std::string> parseManifest(const fs::path& bundleDir);`
Manifest is a JSON file named `manifest.json` sitting next to the dylib/Wasm. Use RapidJSON. Integrate manifest parsing into `PluginLoader::load()` and store results in a new `PluginRegistry`."

---

### 3 • WebAssembly Runtime Integration

**Prompt:**
"Add third-party submodule Wasmtime (via `src/third_party/wasmtime/`). Create `src/core/wasm/wasm_runtime.{h,cpp}` that can:

1.  Load a `.wasm` file, instantiate with WASI disabled.
2.  Expose host imports: `log(string)`, `file_read(path)`, `http_fetch(url)` – each gate-checked against `PluginPermissions`.
3.  Run an exported function `ml_entry()` that takes and returns UTF-8 strings.
    Make sure build is optional via CMake option `ENABLE_WASM_PLUGINS`."

---

### 4 • XPC Sandboxed Helper for Native Plugins

**Prompt:**
"Define an XPC service target `kai.plugin-container` in `src/plugins/container/`. The helper:
• Is sandboxed with an entitlements file allowing **only** network-client and read-only access to the user's `Documents`.
• dlopen()s the actual plugin dylib inside the helper, never in host.
• Implements a thin request/response protobuf over XPC to execute plugin calls.
Host side gets `PluginProxy` class that hides IPC.
Add build rules and launch/terminate lifecycle management."

---

### 5 • LLM Guardrail Middleware

**Prompt:**
"Insert `PluginPolicyGuard` in `src/core/llm/` that intercepts all model-driven plugin invocations:
• It examines the structured natural-language request.
• Blocks if it maps to an action outside plugin's declared `PluginPermissions`.
• For high-risk ops (`needs_exec`, destructive file ops) trigger `MacOSUI` confirmation dialog before dispatch.
Unit-test policies in `tests/plugin_policy_guard_test.cpp`."

---

### 6 • Monitoring & Telemetry

**Prompt:**
"Augment `PluginLoader` to emit structured events via existing `AsyncLogger`:
`PLUGIN_LOADED`, `PLUGIN_REJECTED`, `PLUGIN_CALL_STARTED`, `PLUGIN_CALL_ENDED`, `PLUGIN_VIOLATION`.
Implement ring-buffered in-memory log with `util::RingBufferLogger` (new).
Add a menu item in UI to export last 1000 plugin events to JSON."

---

### 7 • CI Security Gates

**Prompt:**
"Create GitHub Action `ci/plugin_security.yml` that runs after build:
• `codesign --verify` on every artifact in `plugins/`
• Checks manifest schema validity via a lightweight schema-lint tool.
• Fails if a plugin requests `needs_exec=true` without explicit reviewer label."

---

### 8 • Documentation for Plugin Authors

**Prompt:**
"Generate `docs/plugin_dev_guide.md` covering:
• How to build a WebAssembly plugin (Rust example).
• How to build a native Swift/Rust dylib and required manifest.
• Security review checklist before submission.
Include code snippets and signing/notarisation steps."

---

### 9 • Fuzz & Pen-Test Harness

**Prompt:**
"Add target `tests/fuzz_plugin_manifest` using libFuzzer against `parseManifest()`.
Add PenTest helper `scripts/run_malicious_plugin.sh` that loads known-bad plugins inside the XPC container and verifies they fail gracefully."

---

### 10 • Emergency Kill-Switch

**Prompt:**
"Implement `PluginKillSwitch`:
• Fetches signed JSON from `https://updates.microlauncher.ai/plugins/kill-list.json` every 6h.
• If plugin id & version match, disable loading and alert user via UI banner.
• Store kill-list in encrypted (AES-GCM) cache on disk."

---

Use these prompts sequentially or piecemeal in **Go mode** to progressively converge on a hardened, sandbox-oriented plugin framework that aligns with the security architecture report.
