# Hardened Plugin System Implementation Prompts for MicroLauncher

This document contains detailed implementation prompts for creating a secure, sandboxed plugin system for MicroLauncher based on the comprehensive security architecture research. Each prompt is designed to be executed incrementally in **Go mode** and references specific files and patterns from the existing codebase.

## Phase 1: Foundation & Security Infrastructure

### 1.1 • Secure Plugin Directory Structure & Permissions

**Prompt:**
"Refactor `src/core/llm/plugin_loader.{h,cpp}` to implement secure plugin discovery:

1. Create new class `SecurePluginLoader` that inherits from a new `IPluginLoader` interface
2. Restrict plugin loading to ONLY these directories:

    - `~/Library/Application Support/MicroLauncher/Plugins/` (user plugins)
    - `${APP_BUNDLE}/Contents/PlugIns/` (bundled plugins)
    - `/Library/Application Support/MicroLauncher/Plugins/` (system-wide, requires admin)

3. Implement directory permission validation:

    ```cpp
    Expected<void, std::string> validateDirectoryPermissions(const fs::path& dir) {
        // Check ownership (must be current user or root)
        // Check permissions (no world-write)
        // Verify not symlink to untrusted location
    }
    ```

4. Add plugin file validation:

    - Must be `.dylib`, `.wasm`, or `.plugin` bundle
    - No symlinks allowed
    - File size limits (configurable, default 50MB)

5. Return `Expected<PluginInfo, PluginError>` with detailed error types:
    ```cpp
    enum class PluginError {
        kInvalidPath,
        kInsecurePermissions,
        kInvalidFormat,
        kSignatureInvalid,
        kManifestMissing
    };
    ```

Update CMakeLists.txt to link Security.framework for macOS permission checks."

---

### 1.2 • Code Signing Verification Infrastructure

**Prompt:**
"Create `src/core/security/code_signature_verifier.{h,cpp}` implementing comprehensive code signing checks:

```cpp
class CodeSignatureVerifier {
public:
    struct SignatureInfo {
        std::string team_id;
        std::string signing_identity;
        bool is_notarized;
        bool is_hardened_runtime;
        std::vector<std::string> entitlements;
    };

    Expected<SignatureInfo, std::string> verifySignature(const fs::path& binary_path);
    bool isTeamIdTrusted(const std::string& team_id) const;
    Expected<void, std::string> verifyNotarization(const fs::path& binary_path);
};
```

Implementation requirements:

1. Use Security.framework APIs:

    - `SecStaticCodeCreateWithPath`
    - `SecStaticCodeCheckValidityWithErrors` with flags:
        - `kSecCSCheckAllArchitectures`
        - `kSecCSStrictValidate`
        - `kSecCSCheckNestedCode`
    - `SecCodeCopySigningInformation` to extract TeamID

2. Notarization check via `stapler validate` or SecAssessment API

3. Extract and validate entitlements - plugins should NOT have:

    - `com.apple.security.cs.disable-library-validation`
    - `com.apple.security.cs.allow-dyld-environment-variables`
    - `com.apple.security.get-task-allow`

4. Cache verification results with TTL (1 hour) to avoid repeated expensive checks

5. Integration with `SecurePluginLoader::load()` - reject unsigned/invalid plugins"

---

### 1.3 • Plugin Manifest System

**Prompt:**
"Create comprehensive plugin manifest system in `src/core/plugin/plugin_manifest.{h,cpp}`:

```cpp
struct PluginCapabilities {
    // File system access
    bool needs_file_read = false;
    bool needs_file_write = false;
    std::vector<std::string> allowed_paths;  // glob patterns

    // Network access
    bool needs_network = false;
    std::vector<std::string> allowed_domains;  // if empty, all domains

    // Process/execution
    bool needs_process_spawn = false;
    std::vector<std::string> allowed_executables;

    // System resources
    bool needs_camera = false;
    bool needs_microphone = false;
    bool needs_location = false;

    // Memory/CPU limits
    size_t max_memory_mb = 512;
    double max_cpu_percent = 50.0;

    // IPC
    bool needs_xpc = false;
    std::vector<std::string> allowed_mach_services;
};

struct PluginManifest {
    // Identity
    std::string id;  // reverse-DNS style
    std::string name;
    std::string version;  // semver
    std::string author;
    std::string description;

    // Security
    std::string min_host_version;
    std::string max_host_version;
    PluginCapabilities capabilities;
    std::string signature_hash;  // SHA256 of binary

    // Runtime
    std::string entry_point;  // function name or wasm export
    std::string runtime_type;  // "native", "wasm", "javascript"
    std::vector<std::string> dependencies;  // other plugin IDs
};

class PluginManifestParser {
public:
    static Expected<PluginManifest, std::string> parseFromFile(const fs::path& manifest_path);
    static Expected<PluginManifest, std::string> parseFromJSON(const std::string& json);
    static Expected<void, std::string> validate(const PluginManifest& manifest);
    static std::string generateSchema();  // JSON Schema for validation
};
```

Use RapidJSON with schema validation. Manifest must be named `plugin.json` and located:

-   For bundles: `PluginName.plugin/Contents/plugin.json`
-   For dylibs: `PluginName.dylib.manifest/plugin.json`
-   For wasm: `PluginName.wasm.manifest/plugin.json`"

---

## Phase 2: Runtime Sandboxing & Isolation

### 2.1 • WebAssembly Runtime Integration

**Prompt:**
"Integrate Wasmtime for secure WebAssembly plugin execution. Create `src/core/wasm/wasm_runtime.{h,cpp}`:

```cpp
class WasmRuntime {
public:
    struct Config {
        size_t max_memory_pages = 1024;  // 64MB default
        size_t max_table_elements = 10000;
        size_t max_instances = 10;
        size_t max_tables = 5;
        bool enable_simd = true;
        bool enable_threads = false;  // disabled for security
        bool enable_reference_types = true;
        uint64_t fuel_limit = 1000000000;  // computational limit
    };

    Expected<std::unique_ptr<WasmPlugin>, std::string> loadPlugin(
        const fs::path& wasm_path,
        const PluginManifest& manifest,
        const Config& config = {});
};

class WasmPlugin {
public:
    // Host functions exposed to WASM (capability-gated)
    struct HostFunctions {
        std::function<void(std::string_view)> log;
        std::function<Expected<std::string, std::string>(std::string_view path)> file_read;
        std::function<Expected<void, std::string>(std::string_view path, std::string_view content)> file_write;
        std::function<Expected<std::string, std::string>(std::string_view url, std::string_view options)> http_fetch;
        std::function<int64_t()> get_timestamp;
        std::function<std::string(std::string_view key)> get_config;
    };

    // Execute plugin function
    Expected<std::string, std::string> call(
        const std::string& function_name,
        const std::string& input_json);

    // Resource management
    void setFuelLimit(uint64_t fuel);
    uint64_t getFuelConsumed() const;
    size_t getMemoryUsage() const;
};
```

Implementation requirements:

1. Add Wasmtime as git submodule in `third_party/wasmtime`
2. Create CMake option `ENABLE_WASM_PLUGINS` (default ON)
3. Implement WASI preview1 with custom restrictions:
    - No raw filesystem access (use host functions)
    - No socket creation (use http_fetch)
    - No process spawning
4. Each host function checks manifest capabilities before execution
5. Implement fuel metering for computational limits
6. Memory limits enforced via Wasmtime config"

---

### 2.2 • XPC Service Container for Native Plugins

**Prompt:**
"Create XPC service for sandboxed native plugin execution in `src/plugins/container/`:

1. Create `kai-plugin-container` XPC service with Info.plist:

```xml
<key>XPCService</key>
<dict>
    <key>ServiceType</key>
    <string>Application</string>
    <key>RunLoopType</key>
    <string>dispatch_main</string>
    <key>JoinExistingSession</key>
    <false/>
</dict>
```

2. Implement `PluginContainerService` class:

```cpp
class PluginContainerService {
public:
    void handleConnection(xpc_connection_t peer);

private:
    struct LoadedPlugin {
        void* handle;
        PluginManifest manifest;
        std::unique_ptr<SandboxProfile> sandbox;
    };

    Expected<LoadedPlugin*, std::string> loadPlugin(
        const std::string& plugin_path,
        const PluginManifest& manifest);

    Expected<std::string, std::string> executePlugin(
        LoadedPlugin* plugin,
        const std::string& function_name,
        const std::string& input_json);
};
```

3. Create entitlements file `container-entitlements.plist`:

```xml
<key>com.apple.security.app-sandbox</key><true/>
<key>com.apple.security.network.client</key><true/>
<key>com.apple.security.files.user-selected.read-only</key><true/>
<key>com.apple.security.temporary-exception.files.home-relative-path.read-only</key>
<array>
    <string>/Library/Application Support/MicroLauncher/PluginData/</string>
</array>
```

4. Implement host-side `PluginProxy` that manages XPC connection:

```cpp
class PluginProxy : public IPlugin {
    xpc_connection_t connection_;
    dispatch_queue_t queue_;

public:
    Expected<std::string, std::string> execute(
        const std::string& function,
        const std::string& input,
        std::chrono::milliseconds timeout = 30s);
};
```

5. Add crash handling - if container crashes, report and disable plugin"

---

### 2.3 • JavaScript Runtime with QuickJS

**Prompt:**
"Integrate QuickJS for lightweight JavaScript plugin support in `src/core/js/quickjs_runtime.{h,cpp}`:

```cpp
class QuickJSRuntime {
public:
    struct Config {
        size_t max_memory_bytes = 64 * 1024 * 1024;  // 64MB
        size_t max_stack_size = 1024 * 1024;  // 1MB
        std::chrono::milliseconds timeout = 5s;
        bool enable_bignum = false;  // disabled for security
        bool enable_std = false;     // no stdlib access
    };

    class JSPlugin {
    public:
        // Sandboxed API exposed to JS
        struct HostAPI {
            // Logging
            void log(level, message);

            // Controlled file access
            std::string readFile(path);  // checks manifest permissions
            void writeFile(path, content);  // checks manifest permissions

            // Network (fetch-like API)
            Promise fetch(url, options);

            // Key-value storage (sandboxed)
            void storageSet(key, value);
            std::string storageGet(key);

            // LLM interaction
            Promise queryLLM(prompt, options);
        };

        Expected<JSValue, std::string> execute(
            const std::string& function_name,
            const std::vector<JSValue>& args);
    };

    Expected<std::unique_ptr<JSPlugin>, std::string> loadPlugin(
        const fs::path& js_path,
        const PluginManifest& manifest);
};
```

Implementation:

1. Add QuickJS as submodule in `third_party/quickjs`
2. Custom JS environment with no access to:
    - eval() / Function constructor
    - Import/require (all code must be bundled)
    - Timers (use host-controlled async)
    - Global objects except provided API
3. Implement interrupt handler for timeout enforcement
4. Memory tracking via custom allocator
5. Freeze all built-in prototypes to prevent tampering"

---

## Phase 3: Permission System & Runtime Enforcement

### 3.1 • Capability-Based Permission System

**Prompt:**
"Create unified permission system in `src/core/security/permission_manager.{h,cpp}`:

```cpp
class PermissionManager {
public:
    // Permission check results
    enum class Decision {
        kAllow,
        kDeny,
        kRequireUserConsent
    };

    struct PermissionRequest {
        std::string plugin_id;
        std::string capability;
        std::string resource;  // file path, URL, etc.
        std::string purpose;   // human-readable explanation
        std::chrono::system_clock::time_point timestamp;
    };

    // Check if operation is allowed
    Expected<Decision, std::string> checkPermission(
        const PluginManifest& manifest,
        const PermissionRequest& request);

    // Record user decision
    void recordUserDecision(
        const PermissionRequest& request,
        bool allowed,
        bool remember = false);

    // Audit log
    std::vector<PermissionRequest> getAuditLog(
        const std::string& plugin_id,
        std::chrono::hours last_n_hours = 24h);

private:
    // Runtime permission cache
    struct PermissionCache {
        std::unordered_map<std::string, bool> decisions;
        std::chrono::system_clock::time_point expires;
    };

    // Check specific capability types
    bool checkFileAccess(const PluginManifest& manifest, const fs::path& path, bool write);
    bool checkNetworkAccess(const PluginManifest& manifest, const std::string& url);
    bool checkProcessSpawn(const PluginManifest& manifest, const std::string& executable);
};
```

Integrate with all plugin runtimes (WASM, XPC, JS) to enforce permissions before operations.
Store user decisions in Keychain for persistence across launches."

---

### 3.2 • Resource Monitoring & Limits

**Prompt:**
"Implement resource monitoring system in `src/core/monitoring/resource_monitor.{h,cpp}`:

```cpp
class ResourceMonitor {
public:
    struct ResourceUsage {
        // CPU
        double cpu_percent;
        std::chrono::nanoseconds cpu_time;

        // Memory
        size_t memory_bytes;
        size_t peak_memory_bytes;

        // I/O
        size_t bytes_read;
        size_t bytes_written;
        size_t network_bytes_sent;
        size_t network_bytes_received;

        // Calls
        size_t api_calls;
        std::unordered_map<std::string, size_t> api_call_counts;
    };

    struct ResourceLimits {
        double max_cpu_percent = 50.0;
        size_t max_memory_bytes = 512 * 1024 * 1024;
        size_t max_disk_io_bytes_per_sec = 10 * 1024 * 1024;
        size_t max_network_bytes_per_sec = 1 * 1024 * 1024;
        size_t max_api_calls_per_minute = 1000;
    };

    // Start monitoring a plugin
    void startMonitoring(const std::string& plugin_id, const ResourceLimits& limits);

    // Get current usage
    ResourceUsage getUsage(const std::string& plugin_id) const;

    // Check if limits exceeded
    Expected<void, std::string> checkLimits(const std::string& plugin_id);

    // Callbacks for limit violations
    using ViolationCallback = std::function<void(const std::string& plugin_id, const std::string& violation)>;
    void setViolationCallback(ViolationCallback callback);
};
```

Implementation:

1. Use libproc on macOS for process stats
2. Integrate with XPC service to monitor container processes
3. For WASM: use Wasmtime's fuel and memory metrics
4. For JS: use QuickJS memory callbacks
5. Implement sliding window rate limiting for I/O operations
6. Auto-terminate plugins exceeding limits"

---

## Phase 4: LLM Integration & Safety

### 4.1 • LLM Output Filtering for Plugin Calls

**Prompt:**
"Create LLM output safety system in `src/core/llm/plugin_safety_filter.{h,cpp}`:

```cpp
class PluginSafetyFilter {
public:
    struct PluginCall {
        std::string plugin_id;
        std::string function_name;
        std::string parameters_json;
        std::string llm_reasoning;  // extracted from model output
    };

    enum class SafetyLevel {
        kSafe,           // proceed without confirmation
        kCaution,        // log but allow
        kUserConfirm,    // require user confirmation
        kBlocked         // never allow
    };

    struct SafetyCheck {
        SafetyLevel level;
        std::string reason;
        std::vector<std::string> risks;  // specific risks identified
    };

    // Analyze LLM output for plugin calls
    Expected<std::vector<PluginCall>, std::string> extractPluginCalls(
        const std::string& llm_output);

    // Check safety of extracted call
    SafetyCheck checkSafety(
        const PluginCall& call,
        const PluginManifest& manifest,
        const Context& current_context);

private:
    // Pattern matching for dangerous operations
    bool isDestructiveFileOp(const PluginCall& call);
    bool isDataExfiltration(const PluginCall& call);
    bool isSystemModification(const PluginCall& call);

    // Context-aware checks
    bool violatesContextBounds(const PluginCall& call, const Context& context);
};
```

Integration with `ConversationManager`:

1. Intercept all LLM outputs before plugin execution
2. Parse structured plugin calls (JSON or custom format)
3. Apply safety rules based on operation type
4. Show confirmation dialog for `kUserConfirm` level
5. Log all plugin calls with safety decisions"

---

### 4.2 • User Confirmation Dialogs

**Prompt:**
"Implement native macOS confirmation dialogs in `src/ui/macos/plugin_confirmation_dialog.{h,mm}`:

```objc
@interface PluginConfirmationDialog : NSObject

+ (void)showConfirmationForPlugin:(NSString*)pluginName
                        operation:(NSString*)operation
                          details:(NSString*)details
                           risks:(NSArray<NSString*>*)risks
                      completion:(void(^)(BOOL allowed, BOOL remember))completion;

+ (void)showBatchConfirmation:(NSArray<PluginOperation*>*)operations
                   completion:(void(^)(NSDictionary<NSString*, NSNumber*>* decisions))completion;

@end
```

Implementation:

1. Use NSAlert with custom accessory view showing:
    - Plugin name and icon
    - Requested operation in plain language
    - Potential risks (file deletion, network access, etc.)
    - 'Remember decision' checkbox
2. Support batch operations with individual toggles
3. Timeout after 30 seconds (deny by default)
4. Log all decisions to audit trail
5. Integrate with `PermissionManager` for persistence"

---

## Phase 5: Plugin Distribution & Updates

### 5.1 • Plugin Package Format & Distribution

**Prompt:**
"Define plugin package format and distribution system in `src/core/plugin/plugin_package.{h,cpp}`:

```cpp
class PluginPackage {
public:
    struct Metadata {
        PluginManifest manifest;
        std::string readme_md;
        std::string changelog_md;
        std::vector<std::string> screenshots;  // URLs or base64
        std::string license;
        std::string source_url;  // optional

        // Cryptographic signatures
        std::string developer_signature;  // manifest + binary hash
        std::string store_signature;      // optional, from plugin store
    };

    // Package structure: .mlplugin (zip file)
    // ├── manifest.json
    // ├── plugin.dylib/wasm/js
    // ├── resources/
    // ├── README.md
    // ├── signatures/
    // └── metadata.json

    static Expected<PluginPackage, std::string> loadFromFile(const fs::path& package_path);
    static Expected<void, std::string> validatePackage(const PluginPackage& package);
    static Expected<void, std::string> installPackage(
        const PluginPackage& package,
        const fs::path& install_dir);
};

class PluginUpdater {
public:
    struct UpdateInfo {
        std::string current_version;
        std::string latest_version;
        std::string download_url;
        std::string changelog;
        size_t download_size;
        std::string sha256_hash;
    };

    // Check for updates (respects user preferences)
    Expected<std::vector<UpdateInfo>, std::string> checkUpdates(
        const std::vector<PluginManifest>& installed_plugins);

    // Download and verify update
    Expected<PluginPackage, std::string> downloadUpdate(
        const UpdateInfo& update,
        std::function<void(double progress)> progress_callback);
};
```

Security requirements:

1. All packages must be signed by developer
2. Hash verification before installation
3. Rollback capability (keep previous version)
4. Update checks over HTTPS only
5. No automatic updates without user consent"

---

### 5.2 • Plugin Store Integration

**Prompt:**
"Create plugin store client in `src/core/plugin/plugin_store_client.{h,cpp}`:

```cpp
class PluginStoreClient {
public:
    struct StoreConfig {
        std::string base_url = "https://plugins.microlauncher.app/api/v1";
        std::string api_key;  // optional, for private plugins
        std::chrono::seconds timeout = 30s;
    };

    struct PluginListing {
        PluginManifest manifest;
        std::string description;
        std::vector<std::string> tags;
        size_t download_count;
        double rating;
        size_t review_count;
        std::string author_verified;  // verification status
        std::chrono::system_clock::time_point last_updated;
    };

    struct SearchFilters {
        std::string query;
        std::vector<std::string> tags;
        std::string runtime_type;  // "native", "wasm", "js"
        bool verified_only = false;
        std::string sort_by = "relevance";  // "downloads", "rating", "updated"
    };

    // Browse and search
    Expected<std::vector<PluginListing>, std::string> search(const SearchFilters& filters);
    Expected<PluginListing, std::string> getPluginDetails(const std::string& plugin_id);

    // Download
    Expected<PluginPackage, std::string> downloadPlugin(
        const std::string& plugin_id,
        std::function<void(double progress)> callback);

    // Submit for review (developers)
    Expected<std::string, std::string> submitPlugin(
        const PluginPackage& package,
        const std::string& developer_token);
};
```

Store backend requirements:

1. All plugins scanned for malware
2. Manual review for high-risk permissions
3. Developer identity verification
4. Automated testing in sandboxed environment
5. User reporting system for malicious plugins"

---

## Phase 6: Security Hardening & Testing

### 6.1 • Security Audit System

**Prompt:**
"Implement comprehensive security audit system in `src/core/security/plugin_auditor.{h,cpp}`:

```cpp
class PluginAuditor {
public:
    struct AuditReport {
        std::string plugin_id;
        std::chrono::system_clock::time_point timestamp;

        struct SecurityChecks {
            bool valid_signature;
            bool manifest_valid;
            bool permissions_reasonable;
            bool no_suspicious_symbols;
            bool no_known_vulnerabilities;
            std::vector<std::string> warnings;
            std::vector<std::string> errors;
        } security;

        struct BehaviorAnalysis {
            std::vector<std::string> file_access_patterns;
            std::vector<std::string> network_endpoints;
            std::vector<std::string> api_usage_patterns;
            bool anomalous_behavior_detected;
        } behavior;

        struct CodeAnalysis {
            bool uses_unsafe_apis;
            std::vector<std::string> suspicious_strings;
            std::vector<std::string> external_dependencies;
            size_t complexity_score;
        } code;
    };

    // Static analysis
    Expected<AuditReport, std::string> auditPlugin(const fs::path& plugin_path);

    // Runtime analysis
    void startRuntimeAudit(const std::string& plugin_id);
    AuditReport getRuntimeAudit(const std::string& plugin_id);

    // Vulnerability database
    void updateVulnerabilityDatabase();
    bool hasKnownVulnerabilities(const std::string& plugin_id, const std::string& version);
};
```

Implementation:

1. Use `nm` and `otool` for symbol analysis
2. String extraction for suspicious patterns
3. Network endpoint extraction from binaries
4. Integration with CVE databases
5. Machine learning for anomaly detection (optional)"

---

### 6.2 • Fuzzing Infrastructure

**Prompt:**
"Create fuzzing harness for plugin system in `tests/fuzzing/`:

```cpp
// fuzz_manifest_parser.cpp
extern "C" int LLVMFuzzerTestOneInput(const uint8_t* data, size_t size) {
    std::string json(reinterpret_cast<const char*>(data), size);
    auto result = PluginManifestParser::parseFromJSON(json);
    // Should not crash, check result validity
    return 0;
}

// fuzz_plugin_api.cpp
class FuzzPluginHost : public IPluginHost {
    // Implement all host APIs with bounds checking
    // Record all calls for pattern analysis
};

// fuzz_wasm_runtime.cpp
void fuzzWasmPlugin(const uint8_t* wasm_data, size_t wasm_size,
                   const uint8_t* input_data, size_t input_size) {
    auto runtime = WasmRuntime();
    auto plugin = runtime.loadFromMemory(wasm_data, wasm_size);
    if (plugin) {
        plugin->call("fuzz_entry", input_data, input_size);
    }
}
```

Build with:

1. LibFuzzer integration in CMake
2. Address Sanitizer (ASAN)
3. Undefined Behavior Sanitizer (UBSAN)
4. Coverage tracking
5. Corpus of real plugin data"

---

### 6.3 • Emergency Response System

**Prompt:**
"Implement plugin kill switch and emergency response in `src/core/security/emergency_response.{h,cpp}`:

```cpp
class EmergencyResponse {
public:
    struct ThreatInfo {
        std::string plugin_id;
        std::string version_range;  // semver range
        std::string threat_type;    // "malware", "vulnerability", "abuse"
        std::string description;
        std::chrono::system_clock::time_point reported_at;
        std::string severity;       // "critical", "high", "medium", "low"
    };

    struct KillSwitchEntry {
        ThreatInfo threat;
        std::string signature;      // cryptographic signature
        std::chrono::system_clock::time_point expires_at;
    };

    // Update kill switch list
    Expected<void, std::string> updateKillSwitch();

    // Check if plugin is blocked
    bool isPluginBlocked(const std::string& plugin_id, const std::string& version);

    // Local threat detection
    void reportSuspiciousActivity(
        const std::string& plugin_id,
        const std::string& activity_description,
        const std::vector<std::string>& evidence);

    // Automated response
    void quarantinePlugin(const std::string& plugin_id);
    void disableAllPlugins(const std::string& reason);

private:
    // Secure communication with threat intelligence server
    Expected<std::vector<KillSwitchEntry>, std::string> fetchKillSwitch();

    // Local encrypted cache
    void saveKillSwitchCache(const std::vector<KillSwitchEntry>& entries);
    Expected<std::vector<KillSwitchEntry>, std::string> loadKillSwitchCache();
};
```

Features:

1. Check kill switch every 6 hours
2. Immediate check on suspicious activity
3. Offline capability with cached list
4. Signed updates to prevent tampering
5. User notification for blocked plugins
6. Telemetry for threat analysis (opt-in)"

---

## Phase 7: Developer Experience & Documentation

### 7.1 • Plugin Development SDK

**Prompt:**
"Create plugin development SDK in `sdk/`:

```cpp
// sdk/include/microlauncher/plugin.h
#define ML_PLUGIN_API extern "C" __attribute__((visibility("default")))

// Plugin interface version
#define ML_PLUGIN_INTERFACE_VERSION 1

// Entry point
ML_PLUGIN_API int ml_plugin_init(const char* host_version);
ML_PLUGIN_API void ml_plugin_shutdown();

// Metadata
ML_PLUGIN_API const char* ml_plugin_get_manifest();

// Main execution
ML_PLUGIN_API const char* ml_plugin_execute(
    const char* function_name,
    const char* input_json,
    char* output_buffer,
    size_t output_buffer_size);

// Host API callbacks (provided by host)
struct MLHostAPI {
    // Logging
    void (*log)(int level, const char* message);

    // File I/O (permission checked)
    int (*read_file)(const char* path, char* buffer, size_t buffer_size);
    int (*write_file)(const char* path, const char* content, size_t content_size);

    // Network (permission checked)
    int (*http_request)(const char* method, const char* url,
                       const char* body, char* response, size_t response_size);

    // Configuration
    int (*get_config)(const char* key, char* value, size_t value_size);
    int (*set_config)(const char* key, const char* value);
};

ML_PLUGIN_API void ml_plugin_set_host_api(const MLHostAPI* api);
```

Create project templates:

1. C++ native plugin with CMake
2. Rust plugin with cargo
3. WebAssembly plugin with Rust/AssemblyScript
4. JavaScript plugin with TypeScript
5. Example plugins demonstrating each capability"

---

### 7.2 • Plugin Testing Framework

**Prompt:**
"Create plugin testing framework in `sdk/testing/`:

```cpp
class PluginTestHarness {
public:
    // Load plugin in test mode
    void loadPlugin(const fs::path& plugin_path);

    // Mock host environment
    void mockFileSystem(const std::map<std::string, std::string>& files);
    void mockNetworkResponse(const std::string& url, const std::string& response);
    void mockConfig(const std::map<std::string, std::string>& config);

    // Execute and verify
    struct TestResult {
        bool success;
        std::string output;
        std::vector<std::string> logs;
        std::vector<std::string> file_operations;
        std::vector<std::string> network_requests;
        ResourceUsage resource_usage;
    };

    TestResult execute(const std::string& function, const std::string& input);

    // Assertions
    void assertFileRead(const std::string& path);
    void assertFileWritten(const std::string& path, const std::string& content);
    void assertNetworkRequest(const std::string& method, const std::string& url);
    void assertNoFileAccess();
    void assertNoNetworkAccess();

    // Security testing
    void testPermissionBoundaries();
    void testResourceLimits();
    void testCrashResilience();
};
```

Provide GitHub Actions workflow for CI:

````yaml
name: Plugin Tests
on: [push, pull_request]
jobs:
  test:
    runs-on: macos-latest
    steps:
      - uses: actions/checkout@v3
      - name: Build plugin
        run: make build
      - name: Run tests
        run: ml-plugin-test ./build/plugin.dylib
      - name: Security scan
        run: ml-plugin-audit ./build/plugin.dylib
```"

---

### 7.3 • Comprehensive Documentation

**Prompt:**
"Create plugin developer documentation in `docs/plugin-development/`:

1. **Getting Started Guide** (`getting-started.md`):
   - System requirements
   - SDK installation
   - Hello World plugin (all runtimes)
   - Basic concepts (manifest, capabilities, lifecycle)

2. **Security Best Practices** (`security.md`):
   - Principle of least privilege
   - Input validation
   - Safe file/network operations
   - Avoiding common vulnerabilities
   - Code signing and notarization guide

3. **API Reference** (`api-reference.md`):
   - Complete host API documentation
   - Capability descriptions and examples
   - Error handling patterns
   - Async operation patterns

4. **Runtime Guides**:
   - `native-plugins.md`: C++/Rust development
   - `wasm-plugins.md`: WebAssembly specifics
   - `js-plugins.md`: JavaScript/TypeScript guide

5. **Testing Guide** (`testing.md`):
   - Unit testing plugins
   - Integration testing
   - Security testing
   - Performance profiling

6. **Distribution Guide** (`distribution.md`):
   - Package format
   - Signing requirements
   - Store submission process
   - Update mechanism

7. **Examples** (`examples/`):
   - File processor plugin
   - Network monitor plugin
   - LLM enhancement plugin
   - UI extension plugin

Include diagrams showing:
- Plugin lifecycle
- Permission model
- Communication flow
- Security boundaries"

---

## Phase 8: Integration & Migration

### 8.1 • Migration from Current Plugin System

**Prompt:**
"Create migration path from current `plugin_loader` to new secure system:

1. Add compatibility layer in `src/core/plugin/legacy_plugin_adapter.{h,cpp}`:
```cpp
class LegacyPluginAdapter : public IPlugin {
    // Wrap old dlopen-based plugins with security checks
    // Generate manifest from inspection
    // Run in XPC container by default
};
````

2. Update `ModelFactory` to use new plugin system:

    - Replace `PluginLoader::loadDirectory()` with `SecurePluginLoader`
    - Migrate provider plugins to new format
    - Add manifest generation tool for existing plugins

3. Create migration tool `tools/migrate-plugin`:

    - Analyze existing .dylib
    - Generate manifest template
    - Suggest required capabilities
    - Package in new format

4. Deprecation timeline:
    - Phase 1: Warning for non-manifest plugins
    - Phase 2: Require manifest but allow unsigned
    - Phase 3: Full security enforcement"

---

### 8.2 • UI Integration

**Prompt:**
"Integrate plugin system with MicroLauncher UI:

1. Add plugin manager view to preferences window:

```objc
@interface PluginManagerViewController : NSViewController
- (void)showInstalledPlugins;
- (void)showPluginStore;
- (void)showPluginDetails:(NSString*)pluginId;
@end
```

2. Create plugin status indicator in launcher bar:

    - Show active plugins
    - Permission requests badge
    - Quick disable switch

3. Implement permission request dialogs:

    - Native NSAlert for confirmations
    - Remember decisions in Keychain
    - Show recent plugin activity

4. Add plugin search results:

    - Plugins can provide custom result types
    - Render plugin-specific UI elements
    - Handle plugin actions

5. Create plugin development menu (debug builds):
    - Reload plugins
    - Show console logs
    - Resource usage monitor
    - Test permission dialogs"

---

## Implementation Priority & Timeline

**Phase 1 (Week 1-2): Foundation**

-   Secure plugin directories (1.1)
-   Code signature verification (1.2)
-   Basic manifest system (1.3)

**Phase 2 (Week 3-4): Sandboxing**

-   WebAssembly runtime (2.1)
-   XPC service container (2.2)
-   Basic permission checks

**Phase 3 (Week 5-6): Security**

-   Permission manager (3.1)
-   Resource monitoring (3.2)
-   LLM safety filter (4.1)

**Phase 4 (Week 7-8): Polish**

-   UI integration (8.2)
-   Developer SDK (7.1)
-   Documentation (7.3)

**Phase 5 (Week 9-10): Hardening**

-   Security audit system (6.1)
-   Emergency response (6.3)
-   Migration tools (8.1)

Each prompt is self-contained and can be implemented independently, but the suggested order provides a logical progression from basic security to full production readiness.
