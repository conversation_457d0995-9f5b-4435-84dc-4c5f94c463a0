################################################################################

# Hardened Plugin System – Implementation Prompts

# ----------------------------------------------------------------------------

# These prompts are intended for "Go mode" sessions. Each prompt is self-

# contained and can be copy-pasted into the <PERSON><PERSON> assistant to implement a

# discrete slice of the hardened plugin architecture outlined in the security

# report. Follow project rules in all code edits (C++23, Obj-C++, CMake, etc).

################################################################################

## Index

1. Integrate Wasmtime runtime (WebAssembly sandbox)
2. Plugin Manifest & Capability Schema
3. Secure Plugin Discovery & Code-Signature Verification
4. Wasm Host API Bridge (capability-based)
5. Native Plugin Migration to Out-of-Process XPC Services (optional tier)
6. Plugin Permission Enforcement Layer
7. Runtime Monitoring & Resource Limits
8. Hardened Runtime & Entitlements Refinement
9. LLM Output Guardrail Layer
10. User-Facing Plugin Manager UI

---

### 1. Integrate Wasmtime runtime (WebAssembly sandbox)

Prompt:

```
Context:
  • Project root uses CMake + Ninja; third-party deps live under src/third_party or via FetchContent.
  • Target macOS 13+, Apple Silicon & Intel; Hardened Runtime is ON.
Task:
  1. Add Wasmtime v14.0.0 as an external dep using FetchContent. Build static libs with JIT enabled.
  2. Create new library target `wasm_runtime` that wraps basic Wasmtime C++ embedding helpers.
  3. Provide `src/core/plugin/wasm_engine.{h,cpp}` exposing:
       – bool loadModule(const fs::path& wasmPath, const PluginManifest& manifest);
       – Result<PluginInstance, PluginError> instantiate(...);
  4. Ensure code compiles with `-std=c++20` and respects project include order.
  5. Update root CMakeLists and CI scripts.
Constraints:
  • Do NOT break existing build; keep optional with `ENABLE_WASM_PLUGINS` flag default ON.
  • Use smart pointers, no raw new/delete.
  • Log via DBG/ERR macros.
```

### 2. Plugin Manifest & Capability Schema

Prompt:

```
Context:
  • Each plugin (Wasm or native) must ship a `plugin.toml` (or JSON) manifest.
Goal:
  • Define clear capability keys: filesystem, network, subprocess, gpu, model-access, etc.
  • Include metadata (id, name, version, min_host_version, developer_sig_sha256).
Task:
  1. Create `src/core/plugin/manifest.{h,cpp}` with struct PluginManifest + parseManifest(path).
  2. Add unit tests under src/tests/manifest_tests.cpp using Catch2.
  3. Extend `PluginLoader` to read and validate manifest before loading code.
```

### 3. Secure Plugin Discovery & Code-Signature Verification

Prompt:

```
Context:
  • Existing PluginLoader blindly dlopen()s any .dylib in "plugins" dir.
Task:
  1. Restrict search path to ~/Library/Application Support/MicroLauncher/Plugins by default.
  2. Add function verifySignature(const fs::path&) using SecStaticCode APIs; fail load on error.
  3. Enforce that plugin file owner UID equals current user and permissions are 0700/0755.
  4. Remove RTLD_GLOBAL flag; use RTLD_LOCAL to minimise symbol bleed-through.
```

### 4. Wasm Host API Bridge (capability-based)

Prompt:

```
Context:
  • After Wasmtime integration, expose minimal host funcs.
Task:
  1. Define C-ABI host funcs under `src/core/plugin/wasm_host_api.cpp`:
       – kai_log(level, ptr, len)
       – kai_request(id, ptr_json, len)
       – kai_read_file(ptr_path, len, buf_ptr, buf_cap) [requires filesystem-read capability]
  2. Capabilities are checked at each call; deny & trap if missing.
  3. Update WasmEngine to attach these imports when instantiating.
```

### 5. Native Plugin Migration to Out-of-Process XPC Services (optional tier)

Prompt:

```
Context:
  • For high-trust or performance-critical native plugins.
Task:
  1. Design XPC interface `com.kai.plugin.<id>` exchanging protobuf messages.
  2. Scaffold generic helper target under src/plugins/native_xpc_helper that listens, loads provider code, replies.
  3. Parent (host) spawns XPC service with per-plugin sandbox profile denying network/fs by default.
  4. Document build & signing requirements for plugin developers.
```

### 6. Plugin Permission Enforcement Layer

Prompt:

```
Task:
  1. Implement `PluginCapabilityManager` that takes PluginManifest and provides `bool has(Capability c)`.
  2. All host APIs and privileged actions must query this manager.
  3. Add user-editable override file (~/.kai/permissions.json) to revoke capabilities post-install.
```

### 7. Runtime Monitoring & Resource Limits

Prompt:

```
Task:
  1. Create `PluginWatchdog` singleton (core/util) that tracks CPU %, mem usage per plugin.
  2. Use `task_info` and `proc_pid_rusage` APIs.
  3. If thresholds exceeded (configurable), send kill signal to XPC process or interrupt Wasm instance.
  4. Emit logs & UI alert.
```

### 8. Hardened Runtime & Entitlements Refinement

Prompt:

```
Task:
  1. Tighten entitlements: keep `com.apple.security.cs.allow-jit` and optionally `disable-library-validation` only when needed.
  2. Update signing scripts in `package_app.sh` to staple notarization + hardened runtime.
  3. Add CI step that runs `codesign --verify --deep --strict`.
```

### 9. LLM Output Guardrail Layer

Prompt:

```
Task:
  1. Before executing any plugin command generated by LLM, pass through `Guardrail::validate(json_cmd)`.
  2. Implement simple rule-based check + TODO for future small LLM filter.
  3. For destructive ops, trigger UI confirmation dialog in main thread.
```

### 10. User-Facing Plugin Manager UI

Prompt:

```
Context:
  • UI layer is Objective-C++ AppKit.
Task:
  1. Add new preference pane `Plugins` listing installed plugins, their capabilities, status.
  2. Allow enable/disable, permission toggles, and manual update check.
  3. Use MVVM; data model backed by `PluginRegistry` in core.
```

---

# End of prompts
