# Hardened Plugin System Implementation Prompts

## Overview

This document contains detailed implementation prompts for creating a secure plugin system for MicroLauncher (kai) based on the comprehensive security architecture report. Each prompt is designed to guide the implementation of specific security features and architectural components.

## 1. Plugin Architecture Foundation

### Prompt 1.1: Design Plugin Interface Architecture

Create the foundational plugin interface architecture with security-first design:

```
Implement a secure plugin interface system for MicroLauncher with the following requirements:

1. Create a new directory structure:
   - src/core/plugin/          (core plugin infrastructure)
   - src/core/plugin/sandbox/  (sandboxing implementation)
   - src/core/plugin/wasm/     (WebAssembly runtime)
   - src/core/plugin/js/       (JavaScript runtime)
   - src/core/plugin/manifest/ (manifest parsing and validation)
   - src/core/plugin/security/ (security policies and validation)

2. Define base plugin interfaces in src/core/interfaces/:
   - IPlugin: Base interface for all plugins
   - IPluginHost: Interface for the host application
   - IPluginManager: Plugin lifecycle management
   - IPluginSandbox: Sandbox policy enforcement
   - IPluginManifest: Plugin metadata and permissions

3. Implement plugin capability system:
   - Define granular capabilities (file_read, file_write, network, process_spawn, etc.)
   - Create permission model similar to mobile app permissions
   - Implement capability checking at API boundaries

4. Design plugin API surface:
   - Minimal, security-oriented API design
   - No direct OS access by default
   - All operations go through capability-checked host functions

Remember:
- Follow composition over inheritance principle
- Use Expected<T, E> for error handling
- Minimize virtual hops and dynamic allocations
- Use modern C++20 features
```

### Prompt 1.2: Implement Plugin Manifest System

Create a comprehensive plugin manifest system:

```
Implement a secure plugin manifest system with the following specifications:

1. Design manifest schema (JSON format):
   {
     "id": "com.example.plugin",
     "name": "Example Plugin",
     "version": "1.0.0",
     "description": "Plugin description",
     "author": {
       "name": "Developer Name",
       "email": "<EMAIL>",
       "signature": "developer_signature"
     },
     "capabilities": {
       "required": ["file_read", "network_client"],
       "optional": ["gpu_access"]
     },
     "entrypoints": {
       "wasm": "plugin.wasm",
       "js": "plugin.js",
       "native": "plugin.dylib"
     },
     "constraints": {
       "memory_limit_mb": 100,
       "cpu_time_limit_ms": 1000,
       "file_access_paths": ["~/Documents/PluginData"],
       "network_domains": ["api.example.com"]
     },
     "signature": "manifest_signature"
   }

2. Implement manifest parser with validation:
   - Strict JSON schema validation
   - Cryptographic signature verification
   - Version compatibility checking
   - Capability validation against allowed set

3. Create manifest signing tools:
   - Tool to sign manifests with developer certificates
   - Verification using Apple's Security framework
   - Support for code signing identity

4. Implement runtime manifest enforcement:
   - Load and validate manifest before plugin loading
   - Enforce declared constraints at runtime
   - Audit manifest changes

Use secure coding practices:
- Validate all inputs
- Use constexpr for compile-time validation where possible
- Log all security-relevant operations with DBG/ERR macros
```

## 2. WebAssembly Sandbox Implementation

### Prompt 2.1: Integrate WebAssembly Runtime

Implement WebAssembly runtime for secure plugin execution:

```
Integrate a WebAssembly runtime (Wasmtime or Wasmer) for secure plugin execution:

1. Add WebAssembly runtime dependency:
   - Use FetchContent in CMakeLists.txt to add Wasmtime C++ API
   - Configure for macOS ARM64/x86_64 support
   - Enable necessary security features (bounds checking, etc.)

2. Create WasmPluginHost class in src/core/plugin/wasm/:
   - Initialize Wasm engine with security-first configuration
   - Implement memory limits and execution timeouts
   - Create sandboxed linear memory space per plugin
   - Disable dangerous WASI capabilities by default

3. Implement host function bindings:
   - File operations (restricted to manifest paths)
   - Network operations (restricted to manifest domains)
   - UI operations (show notification, update status)
   - LLM interaction (send prompts, receive responses)

4. Create WASI capability filter:
   - Implement fine-grained WASI rights management
   - Pre-open only allowed directories
   - Restrict network access to allowed domains
   - No process spawning unless explicitly permitted

5. Handle WebAssembly security:
   - Implement speculative execution mitigations
   - Add memory access pattern obfuscation
   - Monitor for suspicious behavior patterns
   - Implement plugin isolation between instances

Remember to:
- Use the JIT entitlement in Entitlements.plist
- Implement proper error handling with Expected<T, E>
- Add comprehensive logging for security events
- Test with malicious Wasm modules
```

### Prompt 2.2: Design WebAssembly Plugin API

Create the WebAssembly plugin API interface:

```
Design and implement the WebAssembly plugin API with security as the primary concern:

1. Define WASM import/export interface:
   // Host -> Plugin exports
   - plugin_init() -> i32
   - plugin_execute(command_ptr: i32, command_len: i32) -> i32
   - plugin_cleanup() -> void

   // Plugin -> Host imports
   - host_log(level: i32, msg_ptr: i32, msg_len: i32) -> void
   - host_file_read(path_ptr: i32, path_len: i32, buffer_ptr: i32, buffer_len: i32) -> i32
   - host_http_request(request_ptr: i32, request_len: i32, response_ptr: i32) -> i32
   - host_llm_complete(prompt_ptr: i32, prompt_len: i32, response_ptr: i32) -> i32

2. Implement memory management:
   - Use fixed-size buffers for data exchange
   - Implement zero-copy where possible
   - Validate all pointers and lengths
   - Prevent buffer overflows in host functions

3. Create serialization protocol:
   - Use MessagePack or similar for structured data
   - Implement strict schema validation
   - Version the protocol for compatibility

4. Add rate limiting and quotas:
   - Limit API calls per time window
   - Track resource usage per plugin
   - Implement backpressure mechanisms
   - Monitor for abuse patterns

5. Example plugin template:
   - Create Rust template using wasm-bindgen
   - Create C++ template with WASI SDK
   - Include build instructions and security guidelines

Ensure:
- All host functions validate inputs
- No direct memory access between plugins
- Clear documentation of security boundaries
- Comprehensive test suite with adversarial inputs
```

## 3. JavaScript Engine Integration

### Prompt 3.1: Implement JavaScript Plugin Runtime

Integrate JavaScriptCore for JavaScript plugin support:

```
Implement a secure JavaScript plugin runtime using JavaScriptCore:

1. Create JSPluginHost class in src/core/plugin/js/:
   - Initialize JSContext with restricted global object
   - Remove dangerous globals (eval, Function constructor)
   - Implement custom console object for logging
   - Set up memory and execution time limits

2. Design JavaScript plugin API:
   const plugin = {
     metadata: {
       id: 'com.example.jsplugin',
       name: 'JS Plugin Example',
       version: '1.0.0'
     },

     async onLoad(context) {
       // Initialize plugin
     },

     async execute(command, context) {
       // Handle commands
     },

     async onUnload() {
       // Cleanup
     }
   };

3. Implement secure host bindings:
   - launcher.fs.readFile(path) - with path validation
   - launcher.http.fetch(url, options) - with domain restrictions
   - launcher.ui.showNotification(message)
   - launcher.llm.complete(prompt, options)
   - All async with Promise-based API

4. Add security restrictions:
   - No dynamic code evaluation
   - No access to native modules
   - Frozen prototype chains
   - Restricted property access via Proxy

5. Implement plugin isolation:
   - Separate JSContext per plugin
   - No shared memory between contexts
   - Message passing for plugin communication
   - Cleanup contexts on unload

Configuration:
- Enable JIT entitlement for performance
- Use JSExport for Objective-C++ bridging
- Implement CSP-like policies for JS execution
- Add source code validation before execution
```

### Prompt 3.2: QuickJS Alternative Implementation

Provide QuickJS as a lighter alternative:

```
Implement QuickJS as a lightweight, non-JIT JavaScript engine option:

1. Integrate QuickJS in src/core/plugin/js/quickjs/:
   - Add QuickJS source to third_party/
   - Create QuickJSPluginHost as alternative to JSCore
   - No JIT = no JIT entitlement needed
   - Smaller attack surface

2. Implement the same plugin API as JavaScriptCore:
   - Ensure API compatibility between engines
   - Allow runtime engine selection
   - Provide migration path between engines

3. QuickJS-specific security hardening:
   - Compile with stack protection
   - Enable all security flags
   - Remove unnecessary built-ins
   - Custom memory allocator with limits

4. Performance optimizations:
   - Bytecode caching for faster startup
   - Optimize host function calls
   - Profile common operations
   - Consider Hermes-style optimizations

5. Add option to compile QuickJS to WebAssembly:
   - QuickJS-in-WASM for double isolation
   - Performance vs security tradeoff
   - Useful for extremely untrusted code

Benefits over JavaScriptCore:
- No JIT vulnerabilities
- Smaller binary size
- Easier to audit
- More predictable performance
```

## 4. Native Plugin Sandboxing (XPC)

### Prompt 4.1: Implement XPC Service Architecture

Create XPC-based sandboxing for native plugins:

```
Implement XPC service architecture for native plugin isolation:

1. Create XPC service wrapper in src/core/plugin/xpc/:
   - Design XPCPluginService as macOS XPC service
   - Each plugin runs in separate XPC service process
   - Implement bidirectional communication protocol
   - Handle service lifecycle management

2. Define XPC protocol (.h interface):
   @protocol PluginServiceProtocol
   - (void)loadPluginAtPath:(NSString *)path
                  withReply:(void (^)(BOOL success, NSError *error))reply;
   - (void)executeCommand:(NSDictionary *)command
                withReply:(void (^)(NSDictionary *result, NSError *error))reply;
   - (void)unloadPluginWithReply:(void (^)(BOOL success))reply;
   @end

3. Implement XPC service Info.plist:
   - Configure sandbox profile
   - Set minimum entitlements
   - Define service type and identifier
   - Configure launch-on-demand

4. Create custom sandbox profile:
   (version 1)
   (deny default)
   (allow file-read* file-write*
          (subpath "/Users/<USER>/Library/Application Support/MicroLauncher/PluginData/"))
   (allow network-outbound (remote tcp))
   (deny process-fork process-exec)
   (allow mach-lookup (global-name "com.apple.coreservices.launchservicesd"))

5. Implement plugin communication:
   - Serialize/deserialize data safely
   - Handle connection interruptions
   - Implement timeout mechanisms
   - Add retry logic for resilience

Security considerations:
- Each XPC service has unique sandbox
- Use NSXPCConnection with protocols
- Validate all IPC messages
- Monitor service resource usage
- Implement service recycling
```

### Prompt 4.2: Swift Plugin Interface

Create Swift-based plugin interface for native plugins:

```
Implement a Swift-based plugin interface for memory-safe native plugins:

1. Define Swift plugin protocol in src/core/plugin/swift/:
   @objc public protocol MicroLauncherPlugin {
       var identifier: String { get }
       var version: String { get }
       var capabilities: [PluginCapability] { get }

       func initialize(context: PluginContext) throws
       func execute(command: PluginCommand) async throws -> PluginResult
       func shutdown()
   }

2. Create Swift-to-C++ bridge:
   - Use @objc annotations for Objective-C++ interop
   - Implement type conversions safely
   - Handle Swift errors to C++ Expected<T,E>
   - Manage memory correctly across boundaries

3. Implement plugin loader for Swift:
   - Dynamically load Swift frameworks
   - Verify Swift ABI compatibility
   - Instantiate plugin classes safely
   - Handle version mismatches

4. Add Swift plugin template:
   - Example plugin project structure
   - CMake integration for Swift
   - Code signing configuration
   - Distribution guidelines

5. Security hardening for Swift plugins:
   - Enforce memory safety features
   - Disable unsafe Swift features
   - Implement runtime checks
   - Add telemetry for crashes

Benefits:
- Memory safety by default
- Modern async/await support
- Better macOS integration
- Reduced vulnerability surface
```

## 5. Security Infrastructure

### Prompt 5.1: Implement Code Signing Verification

Create comprehensive code signing verification:

```
Implement robust code signing verification for all plugin types:

1. Create CodeSigningVerifier class in src/core/plugin/security/:
   - Use Security.framework for verification
   - Check Developer ID signatures
   - Verify notarization status
   - Validate signing certificates

2. Implement verification flow:
   bool verifyPluginSignature(const std::filesystem::path& pluginPath) {
       // Create SecStaticCode object
       // Check signature validity
       // Verify against requirements
       // Check notarization
       // Validate certificate chain
       return isValid && isNotarized && isTrusted;
   }

3. Add certificate pinning:
   - Maintain list of trusted developer certificates
   - Allow user to trust new certificates
   - Revocation checking via OCSP
   - Handle certificate expiration

4. Implement signature requirements:
   - Require Developer ID Application certificate
   - Check for hardened runtime
   - Verify entitlements match manifest
   - Ensure no dangerous entitlements

5. Create plugin signing tool:
   - Command-line tool for developers
   - Integrate with codesign
   - Add manifest to signature
   - Generate distribution package

Error handling:
- Clear error messages for signature failures
- Log all verification attempts
- Provide remediation steps
- Support offline verification
```

### Prompt 5.2: Runtime Security Monitoring

Implement runtime security monitoring system:

```
Create a comprehensive runtime security monitoring system:

1. Implement SecurityMonitor class in src/core/plugin/security/:
   - Track all plugin API calls
   - Monitor resource usage
   - Detect anomalous behavior
   - Generate security events

2. Create monitoring metrics:
   - API call frequency tracking
   - Memory usage patterns
   - CPU usage over time
   - Network traffic analysis
   - File system access patterns

3. Implement anomaly detection:
   - Baseline normal plugin behavior
   - Detect statistical deviations
   - Flag suspicious patterns
   - Machine learning integration ready

4. Add security event system:
   enum class SecurityEventType {
       ExcessiveAPIUsage,
       UnauthorizedAccess,
       ResourceLimitExceeded,
       SuspiciousPattern,
       SignatureViolation
   };

   struct SecurityEvent {
       SecurityEventType type;
       std::string pluginId;
       std::string details;
       std::chrono::time_point<> timestamp;
       SecuritySeverity severity;
   };

5. Create response mechanisms:
   - Automatic plugin suspension
   - User notification system
   - Security log generation
   - Telemetry reporting (opt-in)
   - Gradual degradation

Integration points:
- Hook into all plugin API calls
- Monitor XPC communications
- Track WebAssembly execution
- Observe JavaScript operations
```

## 6. Plugin Lifecycle Management

### Prompt 6.1: Implement Plugin Manager

Create the main plugin management system:

```
Implement a comprehensive PluginManager with security-first design:

1. Create PluginManager class in src/core/plugin/:
   class PluginManager {
       // Plugin discovery and loading
       Expected<void, Error> discoverPlugins(const fs::path& directory);
       Expected<PluginHandle, Error> loadPlugin(const fs::path& path);

       // Lifecycle management
       Expected<void, Error> enablePlugin(const PluginId& id);
       Expected<void, Error> disablePlugin(const PluginId& id);
       Expected<void, Error> unloadPlugin(const PluginId& id);

       // Security operations
       Expected<void, Error> verifyPlugin(const PluginId& id);
       Expected<Permissions, Error> getPluginPermissions(const PluginId& id);
       Expected<void, Error> updatePluginPermissions(const PluginId& id, const Permissions& perms);
   };

2. Implement plugin discovery:
   - Scan designated directories only
   - Validate plugin structure
   - Check signatures before loading
   - Parse and validate manifests
   - Build plugin registry

3. Create plugin isolation:
   - One plugin per process/context
   - Separate memory spaces
   - Independent failure domains
   - Resource quotas per plugin

4. Add plugin communication:
   - Message-based IPC only
   - No shared memory
   - Structured data exchange
   - Type-safe protocols

5. Implement hot reload support:
   - Graceful plugin updates
   - State preservation
   - Version migration
   - Rollback capability

Thread safety:
- Use read-write locks for registry
- Atomic operations for state changes
- Message queues for async operations
- No blocking in critical paths
```

### Prompt 6.2: Plugin Installation and Updates

Implement secure plugin installation system:

```
Create a secure plugin installation and update system:

1. Implement PluginInstaller class:
   - Download plugins over HTTPS only
   - Verify signatures before installation
   - Check compatibility with host version
   - Validate all dependencies
   - Atomic installation process

2. Create plugin package format (.mlplugin):
   - ZIP archive with specific structure
   - Manifest at root level
   - Binary in Contents/ directory
   - Resources in Resources/
   - Signature in _CodeSignature/

3. Implement secure update mechanism:
   - Check for updates periodically
   - Verify update signatures
   - Download to temporary location
   - Validate before replacing
   - Keep previous version for rollback

4. Add plugin marketplace integration:
   - Secure API for plugin discovery
   - User ratings and reviews
   - Security audit badges
   - Automated security scanning
   - Developer verification

5. Create installation UI:
   - Show plugin permissions clearly
   - Require user consent
   - Display security warnings
   - Show trust indicators
   - Allow permission customization

Security measures:
- Never auto-install plugins
- Always show security prompts
- Log all installations
- Support plugin allowlists
- Implement plugin quarantine
```

## 7. LLM Integration Security

### Prompt 7.1: Implement LLM-Plugin Security Layer

Create security layer for LLM-driven plugin execution:

```
Implement comprehensive security for LLM-plugin interactions:

1. Create LLMPluginGateway class:
   - Intercept all LLM-to-plugin commands
   - Validate command safety
   - Apply rate limiting
   - Require user confirmation for dangerous operations

2. Implement command filtering:
   class CommandFilter {
       bool isCommandSafe(const PluginCommand& cmd);
       bool requiresUserConsent(const PluginCommand& cmd);
       SecurityRisk assessRisk(const PluginCommand& cmd);
   };

   Dangerous patterns to detect:
   - File deletion commands
   - Network requests to unknown domains
   - Excessive resource consumption
   - Potential data exfiltration
   - Privilege escalation attempts

3. Add prompt injection detection:
   - Pattern matching for known injections
   - Anomaly detection in prompts
   - Context boundary enforcement
   - Sanitize plugin inputs from LLM
   - Monitor for instruction hijacking

4. Implement consent system:
   - Categorize operations by risk level
   - Always require consent for high-risk
   - Batch similar operations
   - Remember user preferences
   - Provide detailed explanations

5. Create audit trail:
   - Log all LLM->Plugin commands
   - Track user consent decisions
   - Record command outcomes
   - Enable forensic analysis
   - Generate security reports

Integration:
- Hook before plugin execution
- Non-bypassable gateway
- Fail-safe defaults
- Clear security boundaries
```

### Prompt 7.2: Plugin Capability Description for LLM

Create plugin capability description system:

```
Implement a system to safely describe plugin capabilities to the LLM:

1. Create PluginDescriptor format:
   {
     "id": "file_manager",
     "capabilities": [
       {
         "action": "read_file",
         "description": "Read contents of a file",
         "parameters": {
           "path": "string (must be within allowed directories)"
         },
         "risks": ["privacy"],
         "requires_consent": true
       }
     ],
     "constraints": {
       "allowed_paths": ["~/Documents"],
       "rate_limits": {"calls_per_minute": 10}
     }
   }

2. Implement safe capability exposure:
   - Never expose raw function signatures
   - Use high-level action descriptions
   - Include safety constraints in descriptions
   - Emphasize permission requirements
   - Add risk indicators

3. Create LLM prompt templates:
   "You have access to the following plugins:
   - File Manager: Can read files from ~/Documents only
   - Web Browser: Can fetch data from whitelisted domains
   - Calculator: Can perform mathematical operations

   Always inform the user before using plugins that access files or network."

4. Implement capability negotiation:
   - LLM requests specific capabilities
   - System checks permissions
   - User approves if needed
   - Capability granted temporarily
   - Automatic revocation after use

5. Add semantic understanding layer:
   - Map natural language to safe operations
   - Prevent capability confusion
   - Clarify ambiguous requests
   - Suggest safer alternatives
   - Explain security implications

Best practices:
- Principle of least privilege
- Default deny approach
- Clear capability boundaries
- User empowerment
- Transparent operation
```

## 8. Configuration and Deployment

### Prompt 8.1: Update Build Configuration

Update CMake configuration for security:

```
Update the CMake build configuration for the hardened plugin system:

1. Modify root CMakeLists.txt:
   # Security flags
   set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -fstack-protector-strong -D_FORTIFY_SOURCE=2")
   set(CMAKE_EXE_LINKER_FLAGS "${CMAKE_EXE_LINKER_FLAGS} -Wl,-bind_at_load")

   # Add WebAssembly support
   FetchContent_Declare(
     wasmtime
     GIT_REPOSITORY https://github.com/bytecodealliance/wasmtime-cpp.git
     GIT_TAG v15.0.0
   )
   FetchContent_MakeAvailable(wasmtime)

   # Add QuickJS
   add_subdirectory(src/third_party/quickjs)

2. Create plugin system CMake:
   # src/core/plugin/CMakeLists.txt
   add_library(plugin_system STATIC
     plugin_manager.cpp
     plugin_loader.cpp
     security/code_signing_verifier.mm
     security/security_monitor.cpp
     wasm/wasm_plugin_host.cpp
     js/js_plugin_host.mm
     xpc/xpc_plugin_service.mm
   )

3. Add security-related dependencies:
   find_library(SECURITY_FRAMEWORK Security)
   find_library(CODEFOUNDATION_FRAMEWORK CoreFoundation)
   target_link_libraries(plugin_system
     ${SECURITY_FRAMEWORK}
     wasmtime
     ${JAVASCRIPTCORE_LIBRARY}
   )

4. Configure code signing:
   set_target_properties(launcher PROPERTIES
     XCODE_ATTRIBUTE_CODE_SIGN_IDENTITY "Developer ID Application"
     XCODE_ATTRIBUTE_CODE_SIGN_STYLE "Manual"
     XCODE_ATTRIBUTE_ENABLE_HARDENED_RUNTIME YES
   )

5. Add XPC service target:
   add_executable(PluginService MACOSX_BUNDLE
     src/core/plugin/xpc/plugin_service_main.mm
   )
   set_target_properties(PluginService PROPERTIES
     BUNDLE_EXTENSION "xpc"
     XCODE_ATTRIBUTE_PRODUCT_BUNDLE_IDENTIFIER "com.launcher.PluginService"
   )
```

### Prompt 8.2: Create Entitlements Configuration

Configure application entitlements:

```
Create proper entitlements configuration for the hardened plugin system:

1. Create Entitlements.plist:
   <?xml version="1.0" encoding="UTF-8"?>
   <!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
   <plist version="1.0">
   <dict>
     <!-- Required for hardened runtime -->
     <key>com.apple.security.cs.allow-jit</key>
     <true/>

     <!-- Required for loading signed third-party plugins -->
     <key>com.apple.security.cs.disable-library-validation</key>
     <true/>

     <!-- Network access for plugins -->
     <key>com.apple.security.network.client</key>
     <true/>

     <!-- File access within container -->
     <key>com.apple.security.files.user-selected.read-write</key>
     <true/>

     <!-- No dynamic library environment variables -->
     <key>com.apple.security.cs.allow-dyld-environment-variables</key>
     <false/>

     <!-- No unsigned executable memory -->
     <key>com.apple.security.cs.allow-unsigned-executable-memory</key>
     <false/>
   </dict>
   </plist>

2. Create PluginService.entitlements:
   <?xml version="1.0" encoding="UTF-8"?>
   <!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
   <plist version="1.0">
   <dict>
     <!-- XPC Service specific -->
     <key>com.apple.security.app-sandbox</key>
     <true/>

     <!-- Limited file access -->
     <key>com.apple.security.temporary-exception.files.absolute-path.read-write</key>
     <array>
       <string>/Users/<USER>/Library/Application Support/MicroLauncher/PluginData/</string>
     </array>

     <!-- Network access if needed -->
     <key>com.apple.security.network.client</key>
     <true/>
   </dict>
   </plist>

3. Update Info.plist for XPC:
   <key>XPCServices</key>
   <dict>
     <key>com.launcher.PluginService</key>
     <dict>
       <key>RunLoopType</key>
       <string>dispatch_main</string>
       <key>ServiceType</key>
       <string>Application</string>
     </dict>
   </dict>

4. Configure code signing script:
   #!/bin/bash
   # sign_app.sh

   IDENTITY="Developer ID Application: Your Team"
   APP_PATH="build/bin/MicroLauncher.app"

   # Sign XPC services first
   codesign --force --options runtime --sign "$IDENTITY" \
     --entitlements PluginService.entitlements \
     "$APP_PATH/Contents/XPCServices/com.launcher.PluginService.xpc"

   # Sign main app
   codesign --force --options runtime --sign "$IDENTITY" \
     --entitlements Entitlements.plist \
     "$APP_PATH"

   # Verify
   codesign --verify --deep --strict "$APP_PATH"

5. Add notarization support:
   # notarize.sh
   xcrun notarytool submit "$APP_PATH" \
     --keychain-profile "AC_PASSWORD" \
     --wait

   xcrun stapler staple "$APP_PATH"
```

## 9. Testing and Validation

### Prompt 9.1: Create Security Test Suite

Implement comprehensive security testing:

```
Create a comprehensive security test suite for the plugin system:

1. Implement malicious plugin tests:
   // test/security/malicious_plugin_tests.cpp
   TEST(SecurityTest, PreventMemoryCorruption) {
       // Load plugin that attempts buffer overflow
       // Verify it's contained/crashes safely
   }

   TEST(SecurityTest, PreventFileSystemEscape) {
       // Load plugin that tries to access /etc/passwd
       // Verify access is denied
   }

   TEST(SecurityTest, PreventNetworkExfiltration) {
       // Load plugin that attempts unauthorized network access
       // Verify connection is blocked
   }

2. Create fuzzing harness:
   - Fuzz plugin manifest parser
   - Fuzz WebAssembly module loader
   - Fuzz JavaScript code execution
   - Fuzz XPC message handling
   - Use AFL++ or libFuzzer

3. Implement penetration tests:
   - Attempt code injection
   - Try privilege escalation
   - Test side-channel attacks
   - Verify signature bypass prevention
   - Check for TOCTOU vulnerabilities

4. Add performance tests:
   - Measure sandboxing overhead
   - Test plugin startup time
   - Benchmark API call latency
   - Monitor memory usage
   - Profile CPU usage

5. Create compliance tests:
   - Verify all entitlements
   - Check code signing
   - Validate manifest enforcement
   - Test permission system
   - Audit logging completeness

CI/CD integration:
- Run on every commit
- Block merges on failures
- Generate security reports
- Track metrics over time
```

### Prompt 9.2: Plugin Developer SDK

Create SDK and documentation for plugin developers:

```
Create a comprehensive SDK and documentation for secure plugin development:

1. Create plugin templates:
   /templates
     /wasm-rust/         - Rust WebAssembly plugin
     /wasm-cpp/          - C++ WebAssembly plugin
     /javascript/        - JavaScript plugin
     /swift-native/      - Swift native plugin (sandboxed)

2. Write security guidelines:
   # Plugin Development Security Guide

   ## Mandatory Requirements
   - All plugins must be signed
   - Manifest must declare all capabilities
   - Follow principle of least privilege
   - Handle errors gracefully
   - No use of eval() or similar

   ## Best Practices
   - Minimize dependencies
   - Validate all inputs
   - Use memory-safe languages
   - Implement timeouts
   - Log security-relevant events

3. Create development tools:
   - Plugin manifest generator
   - Local testing harness
   - Mock host environment
   - Security linter
   - Performance profiler

4. Provide example plugins:
   - File browser (restricted)
   - Weather widget
   - Calculator
   - Note taker
   - System monitor

5. Documentation structure:
   /docs
     /getting-started/
     /api-reference/
     /security-guide/
     /examples/
     /troubleshooting/

Include:
- Step-by-step tutorials
- API documentation
- Security checklist
- Submission guidelines
- Review process
```

## 10. Production Readiness

### Prompt 10.1: Implement Plugin Allowlist System

Create plugin allowlist and reputation system:

```
Implement a plugin allowlist and reputation system for production:

1. Create allowlist management:
   class PluginAllowlist {
       // Allowlist operations
       void addToAllowlist(const PluginId& id, const Certificate& cert);
       void removeFromAllowlist(const PluginId& id);
       bool isAllowlisted(const PluginId& id) const;

       // Reputation tracking
       void recordEvent(const PluginId& id, const SecurityEvent& event);
       ReputationScore getReputation(const PluginId& id) const;

       // Remote updates
       Expected<void, Error> updateFromServer(const URL& endpoint);
       void subscribeToUpdates(UpdateCallback callback);
   };

2. Implement reputation scoring:
   - Track security incidents
   - Monitor resource usage
   - User ratings integration
   - Automated scanning results
   - Developer trust level

3. Create plugin store backend:
   - Submission portal
   - Automated security scanning
   - Manual review process
   - Signing service
   - Distribution CDN

4. Add telemetry system:
   - Anonymous usage statistics
   - Crash reporting
   - Security event reporting
   - Performance metrics
   - Opt-in by default

5. Emergency response system:
   - Remote plugin disable
   - Security bulletin system
   - Automatic updates
   - User notifications
   - Rollback capability

Production checklist:
- Set up monitoring
- Configure alerting
- Document procedures
- Train support team
- Prepare incident response
```

### Prompt 10.2: Final Security Hardening

Implement final production security measures:

```
Implement final security hardening measures for production deployment:

1. Add anti-tampering measures:
   - Runtime integrity checks
   - Anti-debugging detection
   - Code obfuscation for sensitive parts
   - Certificate pinning
   - Jailbreak detection

2. Implement security logging:
   class SecurityLogger {
       void logPluginLoad(const PluginId& id, bool success);
       void logAPICall(const PluginId& id, const APICall& call);
       void logSecurityEvent(const SecurityEvent& event);
       void logPermissionRequest(const PluginId& id, const Permission& perm);
   };

3. Create incident response tools:
   - Plugin forensics analyzer
   - Memory dump collector
   - Network traffic capturer
   - Event correlation engine
   - Automated response actions

4. Add defense-in-depth layers:
   - Multiple validation points
   - Redundant security checks
   - Fail-safe mechanisms
   - Graceful degradation
   - Security circuit breakers

5. Production configuration:
   {
     "security": {
       "plugin_allowlist_only": true,
       "require_notarization": true,
       "enforce_permissions": true,
       "enable_telemetry": true,
       "auto_disable_suspicious": true,
       "max_plugin_memory_mb": 100,
       "max_plugin_cpu_percent": 25
     }
   }

Final steps:
- Security audit
- Penetration testing
- Load testing
- Documentation review
- Launch preparation
```

## Implementation Priority Order

1. **Phase 1 - Foundation (Weeks 1-2)**

    - Plugin interface architecture (1.1)
    - Manifest system (1.2)
    - Basic plugin manager (6.1)

2. **Phase 2 - WebAssembly (Weeks 3-4)**

    - WebAssembly runtime integration (2.1)
    - WASM plugin API (2.2)
    - Basic security verification

3. **Phase 3 - JavaScript (Weeks 5-6)**

    - JavaScriptCore integration (3.1)
    - QuickJS alternative (3.2)
    - API compatibility layer

4. **Phase 4 - Security (Weeks 7-8)**

    - Code signing verification (5.1)
    - Runtime monitoring (5.2)
    - LLM security layer (7.1)

5. **Phase 5 - Production (Weeks 9-10)**

    - XPC sandboxing (4.1)
    - Allowlist system (10.1)
    - Final hardening (10.2)

6. **Phase 6 - Polish (Weeks 11-12)**
    - Testing suite (9.1)
    - Developer SDK (9.2)
    - Documentation and deployment

## Notes for Implementation

1. Always prioritize security over features
2. Follow the principle of least privilege
3. Default to deny unless explicitly allowed
4. Make security transparent to users
5. Plan for incident response from day one
6. Consider performance but never compromise security
7. Test with adversarial mindset
8. Document security decisions
9. Keep dependencies minimal and audited
10. Plan for long-term maintenance

Remember: A plugin system is only as secure as its weakest component. Every implementation decision should be made with security as the primary consideration.
