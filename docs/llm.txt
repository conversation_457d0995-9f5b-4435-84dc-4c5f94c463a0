DEEP MODE — Comprehensive analysis of what needs to change so that a single conversation can seamlessly contain messages produced by different LLM models, and so that the user (or your code) can switch models in-flight.

────────────────────────────────────────────────────────
1. Current state of the code-base (relevant pieces only)
────────────────────────────────────────────────────────
1.1  Configuration layer  
 • `src/core/config/ai_provider_config.h` already models many providers (+ per-model metadata).  
 • `ConfigManager` can load & persist an arbitrary list of `AIProviderConfig` objects and stores a *global* default provider/model pair (`models.default_ai_*`).

1.2  LLM abstractions  
 • `LlmModel` (provider-agnostic interface)  
 • `ModelRegistry` + self-registration macro → maps `provider_id` ⇒ factory( model_name , api_key )  
 • `LlmClient` is a thin wrapper that stitches context + history and then delegates to a concrete `LlmModel`.  
 • `LlmFactory` is just syntactic sugar around `ModelRegistry::create`.

1.3  Chat layer  
 • `IChatBackend` defines async chat operations.  
 • `OpenAIChatBackend` is the **only** concrete backend and it *hard-codes* `LlmFactory::openai()` in its ctor.  
 • The UI (`MacOSChatUI`) directly instantiates `OpenAIChatBackend`.

1.4  Conversation / message model  
 • `ConversationManager` holds a vector of `ApiChatMessage`.  
 • `ApiChatMessage` currently stores only `role` + `content`. No place to remember which model produced a message.  
 • JSON persistence (`ConversationStore`) serialises the messages but also without model metadata.

Result:  
• At runtime you can only talk to a single model (OpenAI, default `gpt-3.5-turbo`) per UI window.  
• Even if you changed the backend at runtime you would lose provenance information in stored messages.

────────────────────────────────────────────────────────
2. Target behaviour
────────────────────────────────────────────────────────
A.  The caller (UI, automation, or another agent) can pick a provider + model **every time `sendMessageStream()` (or `complete()`) is invoked**.  
B.  Each assistant message is tagged with the exact provider/model that delivered it, and that information is persisted on disk.  
C.  A conversation may contain a heterogeneous mix of messages from different models, while history is still replayable to any next model call.  
D.  Switching must be cheap (avoid re-creating HTTP clients on every turn) and thread-safe.

────────────────────────────────────────────────────────
3. Design options
────────────────────────────────────────────────────────
Option 1 (Simplest) – “Backend-per-conversation”  
  • Keep one backend instance per chat window but allow the UI to *re-place* that backend with a new instance whenever the user selects a different provider/model.  
  • Store the chosen model *only* at conversation level.  
Pros:  Few code changes; preserves current layering.  
Cons:  Can’t mix models inside one conversation (requirement not met).  
Status:  Reject – does not satisfy requirement C.

Option 2 – “Provider specific backends + message provenance”  
  • Keep current `OpenAIChatBackend`; implement `AnthropicChatBackend`, `GroqChatBackend`, …  
  • UI chooses backend *per call* by switch-statement, caching N backends per window.  
  • Extend `ApiChatMessage {std::string provider; std::string model_id;}` and JSON (both read & write).  
Pros:  Medium effort; clear separation of provider quirks.  
Cons:  API surface of UI/Controller grows; need parallel hierarchies (search, mention, …) for each backend; code duplication; high compile-time cost as providers multiply.

Option 3 (best trade-off) – “UnifiedChatBackend” + model router [RECOMMENDED]  
  3.1  Extend **data model**  
      • Add the following to `ApiChatMessage`:  
           – `std::string provider;` (canonical id e.g. `"openai"`)  
           – `std::string model;`    (e.g. `"gpt-4o"`)  
           – `nlohmann::json extra_meta;` (optional - temperature, latency, cost, …)  
      • Provide backward-compat ctor that defaults provider/model to empty strings so old call-sites keep compiling.

  3.2  Modify **ConversationManager**  
      • `addMessage(role, content, provider="", model="")` overload that fills provenance.  
      • `exportToJson()` / `loadFromJson()` read & write the new fields.  
      • Token-truncation logic unchanged.

  3.3  Implement **UnifiedChatBackend** (replaces `OpenAIChatBackend` in UI)  
      • Holds an `unordered_map< std::string /*provider+model*/, shared_ptr<LlmClient> > cache_`.  
      • Public API identical to `IChatBackend`; signatures gain **one optional parameter** (provider_id, model_id) for `sendMessageStream` & friends.  
      • When invoked  
          – lookup key `"openai::gpt-4o"` in cache → if miss, call `LlmFactory::create(provider, model)` and store.  
          – forward to `LlmClient::stream/complete`.  
      • Keeps all helper functions (`searchMentionableItems`, `requestLLMSuggestions`, …) provider-independent.  
      • Error handling: If provider not registered, respond via `BackendErrorCode::INVALID_ARGUMENT`.

  3.4  UI integration  
      • `MacOSChatUI` now constructs `UnifiedChatBackend`.  
      • When user presses *Send*, UI looks at dropdown / keyboard shortcut / slash-command to decide `(provider,model)` and passes them through.  
      • While streaming, each chunk is still delivered via the same callbacks; on completion, UI calls  
        `conversationManager_->addMessage("assistant", streamed_text, provider, model);`.

  3.5  Configuration changes  
      • Keep global defaults for first message.  
      • Introduce optional “recently used models” for quick UI pick; purely UI concern.  
      • No change needed in `ConfigManager` beyond helper to resolve `(provider_id, model_id)` → API key.

  3.6  Persistence / Migration  
      • On load, if a message object contains no provider/model fields, treat as `"unknown"` (`"user"` messages) or `"openai"` + previous default (`assistant` messages) – this makes old transcripts readable.  
      • Version bump the on-disk JSON schema (add `"schema": 2`).  
      • Provide one-off migration function but keep reader lenient.

Pros:  
 • Satisfies all requirements (A-D).  
 • Single backend implementation; avoids code duplication; future providers only need to register an `LlmModel`.  
 • Caching keeps per-provider HTTP/TLS setup cost amortised.  
 • Clean extensibility – new capabilities (images, tool-calling) attach to `CapabilitySet`.

Cons / trade-offs:  
 • `UnifiedChatBackend` now has mild provider-specific branches (e.g. formatting system prompts); need feature flags based on `CapabilitySet`.  
 • Slightly more complex to unit-test (need to inject a fake `ModelRegistry`).  
 • UI must supply provider/model identifiers every time; need sensible defaulting.

Option 4 – “Micro-service router”  
  • Extract every provider into a separate micro-service and call them over HTTP from a generic backend.  
  • Out of scope for single-binary desktop application; introduces infra complexity.  

────────────────────────────────────────────────────────
4. Recommended implementation plan (Option 3)
────────────────────────────────────────────────────────
Step 1 : Data-model & persistence  
 • `context/context.h` → extend `struct ApiChatMessage` (provider, model, extra_meta).  
 • Update `ConversationManager::{addMessage, exportToJson, loadFromJson}` (+ unit tests).  

Step 2 : Backend router  
 • `src/core/chat/unified_chat_backend.[h|cpp]` implements `IChatBackend`.  
 • Factor out generic utility functions from current `OpenAIChatBackend` (stream parsing, error conversion).  
 • Drop‐in replace `OpenAIChatBackend` usage in UI; keep old class around for regression testing.

Step 3 : LLM client cache  
 • Inside `UnifiedChatBackend` maintain `std::mutex`-protected cache map; key format `${provider}::${model}`.  
 • Reuse existing `LlmClient` objects (thread-safe because each has its own internal mutex & HTTP client).

Step 4 : UI & API changes  
 • Add provider/model parameters to `MacOSChatUI::initiateStreamForMessage` and to Objective-C bridge.  
 • For initial version expose a simple `/model <provider>/<model>` slash command to switch; later add dropdown.  

Step 5 : Config lookup helpers  
 • Add `ConfigManager::getApiKeyFor(provider_id)` to centralise secrets.  
 • Modify `LlmFactory::create` to query config if `api_key` param is empty.

Step 6 : Compile-time gates & clang-format  
 • Update `.clang-format` if new headers require different include order.  
 • Run `./build.sh && ./run.sh` (no `rm -rf build`).

────────────────────────────────────────────────────────
5. Future-proofing / extensions
────────────────────────────────────────────────────────
• “Tool calling” – When a model that supports tools is chosen, `Capability::kToolCalling` will be set; UI can enable extra buttons.  
• Cost tracking – `ConversationManager` can aggregate `input_tokens`, `output_tokens`, `provider`, `model`, `unit_price` from config for each assistant message.  
• Partial transcripts – If a model’s license forbids sending its output to another provider, you can filter history by `msg.provider == next_provider` before dispatching.

────────────────────────────────────────────────────────
6. Conclusion
────────────────────────────────────────────────────────
Option 3 (“UnifiedChatBackend” with message-level provenance) offers the best balance between flexibility, performance and code maintainability. It introduces *one* new class, touches only a handful of existing modules, leverages the already well-abstracted LLM layer, and keeps provider-specific code encapsulated in `LlmModel`s.

When you’re ready to move to **Go mode** just let me know and we will start applying the concrete code edits step-by-step.


Cross-cutting improvements to apply with any option
P1 HTTP Client Reuse
• Provide HttpPool::instance().acquire(provider_id) returning a shared HttpClient configured with base headers; maintain keep-alive.
P2 Async / Co-routine API (C++23)
• Offer std::future<std::string> or generator<std::string> for streaming; keeps UI responsive.
P4 Strong typed configuration
• Replace std::map<string,string> with struct OpenAIOptions { float temperature; int max_tokens; ... }; to avoid stod/stoi scattered.
