# Kai MCP Integration

# Comprehensive Analysis & Strategy Recommendations for MCP Support

## 0. Purpose

This document centralises all information related to integrating the **Model Context Protocol (MCP)** into _Kai_ (MicroLauncher). It is the single source-of-truth for:

-   Architectural approach
-   Work completed to date
-   Remaining gaps / open issues
-   Future improvement ideas & stretch goals
-   References and useful links

Developers should update the _Completed_ / _Open_ sections as slices land.

---

## 1. High-level Architecture

```
┌──────────────────────┐        JSON-RPC 2.0           ┌──────────────────────┐
│     Kai Core         │ <───────────────────────────> │  MCP Server (stdio)  │
│  (Host + Client)     │                               │  Filesystem / Git    │
│  ├─ ServiceRegistry  │                               └──────────────────────┘
│  ├─ McpClientSvc     | ◄────────────────────────────► StdioTransportSvc
│  ├─ ToolRegistry     │                               ┌──────────────────────┐
│  └─ AgentLoop        │ <───────── HTTP / SSE ───-──► │   MCP Server (HTTP)  │
└──────────────────────┘                               └──────────────────────┘
```

-   **McpClientService** – orchestrates a bounded MPMC queue, zero-copy JSON-RPC parsing and tool dispatch. Transports live in a fixed-size, type-erased buffer sized at compile-time (no v-tables, leaner than `std::variant`).
-   **JsonRpcDecoder / Encoder** – SIMDJSON + `ArenaAllocator` for zero-allocation SAX parsing and fast formatting (≈3× throughput vs RapidJSON DOM).
-   **RuntimeManagerSvc** – unified manager that discovers _all_ runtimes (native plugins **and** MCP servers) and hands back a `RuntimeDescriptor` list. Replaces the old `PluginManagerSvc`.
-   **Transports** – zero-virtual CRTP implementations (`StdioTransport`, `HttpTransport`, `DylibTransport`) stored in contiguous **type-erased storage**; avoids v-tables and template bloat while staying extensible.
-   **MetricsRegistry** – compile-time table of `CounterDef` exposed via Diagnostics; zero dynamic registration.
-   **ToolRegistry** – per-server **minimal-perfect hash** (`bbhash`) persisted to an mmapped snapshot; a small `flat_hash_map` fallback handles dynamic additions. Snapshot exposed as `span<const ToolDef>` for cache-friendly iteration.
-   **AgentLoop** – subscribes to `EventBus` for `PartialChunk` events and streams results back to the LLM in real-time.
-   **PolicyEngine** – enforces per-tool capability bitset; seatbelt profiles are generated on the fly for stdio servers.

---

## 2. Completed (Slice-1)

| Date       | Item                        | Notes                                                    |
| ---------- | --------------------------- | -------------------------------------------------------- |
| 2025-05-27 | Skeleton service plumbing   | Added `kMcpClientService`, dependencies, stub start/stop |
| 2025-05-27 | ServiceRegistry integration | Registered `McpClientService` in `main.cpp`; updated DAG |
| 2025-05-27 | Build system                | Added sources to `core/CMakeLists.txt`                   |

All work compiles; `McpClientService::start()` currently logs a placeholder message.

---

## 3. Implementation Plan (next slices)

| Phase                                                    | Scope                                                                                                                                                                                                                                                                                                                                                                                                          | Key Deliverables                                                                                                                                                            | Exit Criteria                                                              |
| -------------------------------------------------------- | -------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | --------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | -------------------------------------------------------------------------- |
| **P0 – Kernel Hardening & Runtime Unification** (1 wk)   | Constexpr service topology array & Lifecycle enum, **checked-in canonical headers** (`service_id.h`, `service_topology.h`, `service_name_phf.h`) replacing the former YAML generator; refactor `PluginManagerSvc` → `RuntimeManagerSvc`, extract generic `runtime_base.h`, unify transports as `RuntimeTransport`, **adaptive MPMC queue with back-pressure**, bootstrap compile-time metric registry skeleton | All existing services report `Lifecycle::kStarted`; `ServiceRegistry` starts via constexpr topo; RuntimeManagerSvc scans OK; metric counters exposed via DiagnosticsService | Unit-tests green; cold-start regression ≤ 0 %; metrics endpoint reachable  |
| **P0.5 – Foundation Hardening** (1 wk)                   | Remove legacy generator; maintain **constexpr topo headers** manually; introduce `ServiceBase<Derived,Deps…>` CRTP constructor injection; switch `EventBus` to blocking `wait_dequeue()` variant **+ idle-CPU metric**; wrap every `IService::start()` in `SpanGuard` for launch profiling                                                                                                                     | Cold-start ≤ 40 ms; `event_bus.idle_cpu_pct` < 0.1 %; compile-time cycle detection blocks invalid DAGs                                                                      | Micro-bench passes; Diagnostics span visible in Jaeger                     |
| **P1 – Core JSON-RPC & Transport Foundations** (3 wks)   | SIMDJSON-backed `JsonRpcDecoder/Encoder`, CRTP `RuntimeTransport` concept (Stdio/HTTP/Dylib), **varint-framed JSON-RPC codec**, **arena-backed zero-allocation decode & encode**, initial direct streaming into AgentLoop                                                                                                                                                                                      | `tools/list` handshake passes; p95 decode ≤ 50 µs; queue auto-grows & reports back-pressure                                                                                 | Tool list visible; latency metric ≤ 50 µs; no queue overflows in soak-test |
| **P1.5 – Unified Runtime Discovery & ProbeCache** (1 wk) | Unified parallel scan for native plugins, JS/Wasm runtimes **and** MCP servers with shared **ProbeCache.sqlite** storing `{path, mtime, sha256, type, verdict}`; XPC-sandboxed seatbelt validator & pre-compiled cache `.sb.bin`                                                                                                                                                                               | Cold scan ≤ 150 ms for 200 runtimes; probe-cache hit ≥ 95 %; `/metrics` exposes `runtime.scan_ms` counter                                                                   | Preferences ▸ Plugins list populates within 200 ms on cold start           |
| **P2 – ToolRegistry & Invocation Path** (3 wks)          | **Minimal-perfect hash** (`bbhash`) registry with small-table fallback, AgentLoop integration, direct streaming of partial chunks (no EventBus hop) **+ compact tool snapshot serialisation (names + caps only for LLM prompts)** **+ bounded queue back-pressure / flow-control metrics**                                                                                                                     | p95 tool call latency ≤ 250 ms; registry footprint < 80 KB for 1 k tools                                                                                                    | LLM answers requiring file read via MCP; streaming tokens rendered         |
| **P3 – Security, Policy & Watchdog** (3 wks)             | Capability bitset, PolicyEngine enforcement, seatbelt generator, transport watchdog w/ timeout kill, diagnostics counters                                                                                                                                                                                                                                                                                      | Hung server auto-killed; denied tool call returns `KaiError::CapabilityDenied`; seatbelt auto-generated                                                                     | Security test suite green; watchdog counter increments visible             |
| **P4 – Packaging & Updates** (2 wks)                     | Lazy fetch & notarise servers via `kai-mcp-bootstrap`, SHA-256 manifest, Preferences ▸ Plugins UI                                                                                                                                                                                                                                                                                                              | Tier-1 servers download on first use; app size growth ≤ 10 %                                                                                                                | Recorded demo, manifest verified                                           |
| **P5 – Advanced Observability & Fuzzing** (2 wks)        | OpenTelemetry spans per tool call, Prometheus histograms, libFuzzer harness for JSON decoder & transports                                                                                                                                                                                                                                                                                                      | Traces visible in Jaeger; 10 k exec/s fuzzing with zero crashes                                                                                                             | CI passes with coverage ≥ 85 %                                             |

> Timeline assumes 1–2 dedicated engineers; slices overlap where possible.

### Detailed Implementation Strategies

#### P0 – Kernel Hardening & Runtime Unification

1. **Constexpr Service Topology**
2. **Canonical Service Headers** – `service_id.h`, `service_topology.h`, `service_name_phf.h` are now maintained manually in core/foundation; compile-time static_asserts validate topology.
3. **Lifecycle Enum**
4. **RuntimeManagerSvc**
5. **RuntimeTransport Concept**
6. **Metric Registry Skeleton**
7. **EventBus Simplification & Executor Integration** – replace dispatcher spin-yield loop with **blocking wait-dequeue** to eliminate idle CPU burn and tighten p95 latency; align publish queue API with Executor tasks.
8. **Adaptive Ring-buffer MPMC Queue** – lock-free ring starts at 1 024 entries; overflow buffer grows on demand and exposes `queue_pct` back-pressure metric.

#### P1 – Core JSON-RPC & Transport Foundations

1. **SIMDJSON Decoder / Encoder**
    - Wrap zero-copy SAX decoder and `fmt::format_to` encoder around ArenaAllocator buffers.
2. **Varint-framed JSON-RPC Codec**
    - Length-prefixed frames eliminate costly delimiter scans and enable zero-copy writes; newline-delimited mode retained behind `#ifdef` for compatibility.
3. **Direct Streaming Path**
    - `RuntimeTransport` pushes `PartialChunk` directly into `AgentLoop`, bypassing EventBus.
4. **Arena-backed Zero-Allocation Decode**
    - Plug `ArenaAllocatorSvc` into SIMDJSON's alloc hooks so JSON parsing and formatting incur zero heap allocations even under streaming load.
5. **Zero-allocation Guard (ASan)**
    - Piggy-backs on ASan allocator interception; unit tests assert `rpmalloc` reports **zero** heap allocations on hot paths (no dedicated compile flag).

#### P1.5 – Unified Runtime Discovery & ProbeCache

1. **Unified Scan & ProbeCache**
    - Scan for native plugins, JS/Wasm runtimes **and** MCP servers with **shared ProbeCache (SQLite)** storing `{path, mtime, sha256, type, caps}`.
2. **Constexpr Topology Validation**
    - Validate topology at compile-time to ensure consistency.
3. **XPC-sandboxed Seatbelt Validator**
    - Validate seatbelt profiles and pre-compile cache `.sb.bin` for XPC-sandboxed execution.

#### P2 – ToolRegistry & Invocation Path

1. **Per-server Minimal-Perfect Hash Table** (snapshot stores schema hash for cache invalidation) – **compact snapshot serialises only `name` + `capabilities` for LLM prompt efficiency**
2. **Capability Bitset (per-tool)**
3. **AgentLoop Integration**
4. **Streaming Partial Chunks**
5. **Lock-free MPMC Ring & Back-Pressure**
    - Single ring per transport with water-mark metrics (`queue_pct`); if >90 % full return `KaiError::BackPressure` and pause LLM generation.
6. **Memory Targets**
    - Registry memory < 80 KB for 1 k tools; verify in unit test using `mallinfo`.

#### P3 – Security, Policy & Watchdog

1. **Capability Bitset**
    - Define `enum class ToolCapability : uint64_t { FileRead=1<<0, FileWrite=1<<1, NetAccess=1<<2, … }`.
    - Parse `capabilities` field in `tools/list` response; default deny unknown bits.
2. **PolicyEngine Rules**
    - YAML file `~/.kai/policy.yaml` editable via Preferences; hot-reloaded via inotify.
    - Blocks call if `(requested_caps & denied_mask) != 0` → `KaiError::CapabilityDenied`.
3. **Seatbelt Generator**
    - For each stdio server, synthesise `.sb` profile with only needed file/network entitlements derived from capability set & user prefs.
4. **Transport Watchdog**
    - Attach `steady_clock` timestamp to each call; monitored via **pidfd/kqueue** (fallback polling) and kill if >30 s. Returns `KaiError::Timeout`; Diagnostics counter `mcp.watchdog_kill_total` increments.
5. **Heap Allocation Target** – zero steady-state heap allocations during MCP traffic verified via rpmalloc stats.

#### P4 – Packaging & Updates

1. **kai-mcp-bootstrap**
    - Hardened signed C++ tool that downloads servers from GitHub releases; verifies SHA-256 + notarisation ticket.
2. **Manifest Cache**
    - `~/Library/Application Support/MicroLauncher/mcp/manifest.json` storing `{server, version, sha256}`.
3. **Preferences ▸ Plugins UI**
    - Objective-C++ table view listing servers with status (Installed / Update Available / Disabled).
4. **App Size Budget**
    - Ensure initial .app bundle growth ≤ 10 %; large servers (>20 MB) fetched lazily.

#### P5 – Advanced Observability & Fuzzing

1. **OpenTelemetry Integration**
    - Create span `mcp.tool.<name>` with attributes `{transport, pid, bytes_in, bytes_out}`.
2. **Prometheus Exporter**
    - Counters: `mcp.calls_total`, `mcp.latency_ms_bucket`, `mcp.errors_total`.
3. **HDR Histogram Buckets & OTLP Exporter**
    - Integrate hdrhistogram-c; Diagnostics exposes p95 latency buckets; OTLP exporter sends spans to Jaeger/Tempo.
4. **libFuzzer Harness**
    - Target `JsonRpcDecoder` and `StdioRuntime::processBuffer()`; seed corpus with MCP spec examples.
    - Run in CI (`-O1 -fsanitize=address,undefined,fuzzer`).
5. **Coverage Goal**
    - Maintain `llvm-cov` aggregated coverage ≥ 85 % across core MCP files.

---

## 4. Remaining Gaps / Open Issues

1. **JSON-RPC implementation** – not yet started.
2. **Transport layer abstraction** – design decided, code pending.
3. **ToolRegistry storage** – schema/out-of-band persistence TBD.
4. **UI wiring** – need callbacks in Objective-C++ UI to surface tool calls.
5. **Testing** – no unit tests for MCP yet; Catch2 harness to be added.
6. **Docs** – developer guide for adding new servers still missing.
7. **Observability wiring** – OpenTelemetry exporter & span creation awaiting P5.
8. **Fuzz harness coverage** – libFuzzer targets to be expanded.
9. **Quicktype code-gen** – DTO generator pipeline not yet landed.

---

## 5. Future Improvements

-   **Persistent Connection Pool** – reuse long-lived pipes / HTTP sessions across launches.
-   **WASM In-Process Server Hosting** – allow lightweight JS/WASM servers to run inside Kai for ultra-low-latency tools.
-   **Remote Discovery Registry** – fetch curated server list from MCP Hub and suggest installs.
-   **Auto-update Servers** – periodic version check + codesigned update.
-   **Advanced Observability (expanded in P5)** – OpenTelemetry spans, Prometheus histograms, distributed tracing across transports.
-   **Zero-copy Code-Gen** – quicktype-lite script turns JSON schema into C++ DTOs compiled into core.
-   **WebSocket Transport** – enable bidirectional streaming over ws:// and wss:// endpoints.

---

## 6. References & Links

-   MCP spec 2025-03-26: <https://modelcontextprotocol.io/specification/2025-03-26>
-   Reference servers repo: <https://github.com/modelcontextprotocol/servers>
-   Kai internal roadmap: `plugin-todo.md` Slices 1–3

---

_Last updated:_ 2025-06-02 (foundation hardening slice, SIMDJSON arena, ProbeCache rename, OTLP histogram upgrade)

### Phase Performance Budgets

| Metric                                      | Target   |
| ------------------------------------------- | -------- |
| JSON-RPC decode p95 (1 KB)                  | ≤ 50 µs  |
| Tool call latency p95 (Filesystem server)   | ≤ 250 ms |
| EventBus publish latency p95                | ≤ 0.5 ms |
| Cold start with 200 runtimes                | ≤ 150 ms |
| Steady-state heap allocations (MCP traffic) | 0        |
