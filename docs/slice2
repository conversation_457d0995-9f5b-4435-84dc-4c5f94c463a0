
DEEP MODE — AUTHORITATIVE "SLICE-2 – SECURITY & DISCOVERY" IMPLEMENTATION BLUEPRINT  
(Revision 2025-06-03 • Supersedes all previous Slice-2 drafts. Aligns with master plan and folds-in the optimisation decisions agreed in the last design review: generic AdaptiveCache, Que<PERSON><PERSON><PERSON><PERSON>, MetricSource, signed FlatSnapshot, compile-time ServiceBase injection.)

──────────────────────────────────────────────────────────────────────────────
0. OBJECTIVES & SCOPE
──────────────────────────────────────────────────────────────────────────────
Primary Goal Deliver a hardened, zero-allocation security substrate and a **unified runtime discovery pipeline** that supports Native, JavaScript, WebAssembly, and MCP Server runtimes while laying down shared abstractions required for Slice-3 (performance, streaming, observability).

Key Success Metrics  
• Cold scan ≤ 150 ms for 200 runtimes (stretch 125 ms)  
• p95 verifier path ≤ 7 µs/runtime (CDHash + Seatbelt)  
• ProbeCache hit-rate ≥ 95 % on warm start  
• Zero steady-state heap allocations on hot paths (guard counter == 0)  
• CI: Release, ASan, TSan green; codesign + notarisation pass

──────────────────────────────────────────────────────────────────────────────
1. HIGH-LEVEL ARCHITECTURE ADDITIONS
──────────────────────────────────────────────────────────────────────────────
1-A Generic AdaptiveCache<K,V,Policy>             (h/inline header-only)  
 • Striped spin-lock free shards; adaptive kWays (2…16)  
 • Policy hooks: `onMiss`, `onEvict`, `stats()`  

1-B QueueTraits & EventQueue Concept              (h/inline)  
 • `RingQueueTraits` (lock-free, spin-wait)  
 • `DispatchChannelTraits` (dispatch, blocking)  
 • Unified struct `QueueStats { uint32_t hit, miss, drop, high_water_pct; }`

1-C MetricSource Concept                          (util/metrics.hh)  
 • `template<class T> concept MetricSource = requires(T t) { t.stats(); };`  
 • Prom exporter enumerates sources at compile-time (`for_each_type`).

1-D Signed FlatSnapshot Format                    (flat_snapshot.hh/.cpp)  
 Header `{magic, version, build_sha, crc32, sig_len, signature}` + payload.  
 Used by ProbeCache, PolicySnapshot, ToolRegistry (future).

1-E ServiceBase<Derived,Deps…> CRTP Injection     (foundation/service_base.hh)  
 Compile-time DAG enforcement; replaces ad-hoc `attach*` helpers.

1-F MuxQueue<EventT> (optional flag)              (routing/mux_queue.hh)  
 Tagged-union event stream for FsEvents, TransportChunks, ProbeInvalidations.

1-G Hot-Patch Verifier Table (flag `KAI_ENABLE_VPATCH`)  
 Optional mmapped table for out-of-band verifier overrides; validated early.

1-H VerdictSnapshot & Layered VerificationStore          (snapshot/verdict_store.hh)  
 • FlatSnapshot-backed persisted L2 store for security verdicts; shares format with ProbeCache.  

1-I Seatbelt Verifier & Profile Generator                (security/seatbelt_verifier.hh)  
 • ObjC++ shim uses `sandbox_check`; PHF generator tool (`tools/seatbelt_delta_gen.py`) regenerates perfect-hash table at build-time.

──────────────────────────────────────────────────────────────────────────────
2. EPICS & DELIVERABLES
──────────────────────────────────────────────────────────────────────────────
E-1 Security Primitives & Verifier Pipeline v2  
 a. `Mask128` helpers (`has`, `set`, `popcount`, `to_string`).  
 b. Replace `ShardedCache` with `AdaptiveCache<CDHash, Verdict>` in `VerificationStore`.  
 c. Compile-time `PolicyMask` to skip optional verifiers per runtime class.  
 d. **(opt)** Hot-patch verifier table loader (flagged).  
 e. SeatbeltVerifier real implementation + PHF generator; enforced in Release & Debug.  
 f. FlatSnapshot-backed persisted verdict store wired into `VerificationStore` (ghost entries for negatives).  

E-2 Discovery Pipeline Unification  
 a. `RuntimeScanner` coroutine reads FsEvents → executor fan-out.  
 b. `RuntimeDescriptor {type, path, caps, mtime_sha256}`.  
 c. Shared `ProbeCache` implemented via FlatSnapshot + AdaptiveCache L1 + SQLite L2.

E-3 Queue & Router Infrastructure  
 a. Introduce `QueueTraits`; refactor existing RingQueue backend.  
 b. **MuxQueue** (flag `KAI_ENABLE_MUX_QUEUE`) with priority tag; default off.  
 c. `QueueStats` exported via MetricSource.

E-4 Service Kernel Hardening  
 a. Land `ServiceBase<Derived,Deps…>` and migrate all existing services.  
 b. Checked-in `service_id.h` + `service_topology.h`; `service_static_checks.h` asserts consistency.

E-5 Diagnostics & Observability  
 a. MetricSource enumeration; caches & queues auto-register.  
 b. Counters: `sec.verifier_ms`, `cache.hit|miss|evict_total`, `queue_pct`.  
 c. FlatSnapshot, seatbelt compile, ProbeCache latency HDR histograms.

E-6 CI / Build System  
 a. Add CMake option `KAI_ENABLE_VPATCH OFF` and `KAI_ENABLE_MUX_QUEUE OFF`.  
 b. Thin-LTO flags prepared but default OFF.  
 c. Fuzzers: `flat_snapshot_fuzzer`, `adaptive_cache_fuzzer`, `verifier_table_fuzzer`.

──────────────────────────────────────────────────────────────────────────────
3. DETAILED TASK BREAKDOWN & FILE GRID
──────────────────────────────────────────────────────────────────────────────
3-1 Core / container  
 • src/core/container/adaptive_cache.hh   (new)  
 • tests/adaptive_cache_stats_test.cpp       (new)  
 • benchmarks/adaptive_cache_hit_bench.cpp

3-2 Core / runtime  
 • src/core/runtime/fs_event.hh           (unchanged)  
 • src/core/runtime/event_queue.hh        (updated: add QueueTraits)  
 • runtime/ring_queue_backend.hh             (updated)  
 • runtime/mux_queue.hh                      (new, flagged)  
 • runtime/runtime_scanner.hh/.cpp           (replaced old scanner)  
 • tests/ring_queue_hazard_pointer_test.cpp  (new ABA harness)

3-3 Security  
 • src/core/security/mask128.hh           (new)  
 • src/core/security/verifier_strategy.hh (updated PolicyMask)  
 • src/core/security/verification_store.hh(updated AdaptiveCache)  
 • src/core/security/seatbelt_verifier.hh/.cpp       (new)  
 • security/hotpatch_verifier_table.hh/.cpp  (flagged)  
 • tools/seatbelt_delta_gen.py               (new build-rule)  
 • tests/verification_pipeline_order_test.cpp  
 • tests/seatbelt_verifier_profile_test.cpp   (new)

3-4 Snapshot & Storage  
 • src/core/storage/flat_snapshot.hh/.cpp (new)  
 • snapshot/probe_cache.hh/.cpp              (rewrite using FlatSnapshot)  
 • snapshot/verdict_store.hh/.cpp            (new FlatSnapshot-based L2)  
 • tests/flat_snapshot_crc_sig_test.cpp  
 • tests/verdict_store_hit_miss_test.cpp     (new)

3-5 Foundation  
 • src/core/foundation/service_base.hh    (new)  
 • core/foundation/service_static_checks.h   (new static_assert helpers)

3-6 Diagnostics  
 • src/core/util/metrics.hh               (updated MetricSource)  
 • diagnostics/metrics_exporter.{h,cpp}      (updated enumeration)  
 • tests/metric_source_enumeration_test.cpp

3-7 Build & CI  
 • cmake/KaiOptions.cmake                    (add new flags)  
 • ci/scripts/codesign_verify.sh             (new codesign/notarise job)  
 • fuzz/flat_snapshot_fuzzer.cpp  
 • fuzz/adaptive_cache_fuzzer.cpp  
 • fuzz/manifest_json_fuzzer.cpp             (new)

──────────────────────────────────────────────────────────────────────────────
4. ENGINEERING PRINCIPLES & SHARED ABSTRACTIONS
──────────────────────────────────────────────────────────────────────────────
• Header-only fast-paths; explicit template instantiation `.cpp` files for caches, queues.  
• No new virtuals; verifiers & transports remain CRTP/constexpr driven.  
• All snapshot files sign & notarise; host validates via `SecStaticCodeCheckValidity`.  
• AdaptiveCache & FlatSnapshot are **generic** — reused by ToolRegistry & ProbeCache in Slice-3.  
• MetricSource concept guarantees a single prom/OTLP exporter for all subsystems.  
• Compile-time DAG via `ServiceBase` ensures no start-time topo sort.

──────────────────────────────────────────────────────────────────────────────
5. TESTING & VALIDATION STRATEGY
──────────────────────────────────────────────────────────────────────────────
Unit (Catch2)  
• mask128_helper_test, adaptive_cache_stats_test, flat_snapshot_crc_sig_test, verification_order_test, queue_traits_spin_block_test, seatbelt_verifier_profile_test, verdict_store_hit_miss_test, verification_store_hit_miss_test.

Fuzz (libFuzzer)  
• flat_snapshot_fuzzer (header corruption), adaptive_cache_fuzzer (adversarial key patterns), verifier_table_fuzzer (malformed mmaps), manifest_json_fuzzer (manifest schema fuzz).

Micro-bench (Google Benchmark)  
• bench_adaptive_cache_hit, bench_verifier_pipeline, bench_runtime_scan_200.

Integration  
• cold_scan_integration_test (200 mixed runtimes); asserts ≤ 150 ms.  
• verifier_path_latency_test (p95 ≤ 7 µs).  
• Policy denial path returns correct `KaiError`.

CI Gates  
• Release + ASan + TSan; fuzzers 5 k exec/s; codesign verify; perf drift check (< 5 %).  
• Notarisation simulation for snapshot & hot-patch dylib artifacts.

──────────────────────────────────────────────────────────────────────────────
6. SCHEDULE (WORKING DAYS 1-20)
──────────────────────────────────────────────────────────────────────────────
| Day | Focus                                               |
| --- | --------------------------------------------------- |
| 1-2 | adaptive_cache.hh + unit/bench scaffolding          |
| 3-4 | QueueTraits refactor + RingQueue backend update     |
| 5-6 | Mask128 helpers + verifier_policy_mask integration  |
| 7-8 | verification_store port to AdaptiveCache            |
| 9-10| flat_snapshot.hh + ProbeCache rewrite               |
| 11  | ServiceBase CRTP skeleton + migrate ServiceRegistry |
| 12  | MetricSource hooks; Diagnostics exporter update     |
| 13-14| RuntimeScanner coroutine + executor fan-out        |
| 15  | cold_scan_integration_test, latency micro-benches   |
| 16  | CI flags, fuzz harnesses, static_assert coverage    |
| 17  | Preferences ▸ Plugins pane read-only update         |
| 18  | Docs refresh (`slice2_architecture.md`)             |
| 19  | Buffer / perf tweak                                 |
| 20  | Final code-freeze → PR reviews, tag `slice-2-rc1`   |

──────────────────────────────────────────────────────────────────────────────
7. ACCEPTANCE CRITERIA (FINAL)
──────────────────────────────────────────────────────────────────────────────
AC-1 Cold start scan (200 runtimes) ≤ 150 ms (stretch 125 ms).  
AC-2 Verification pipeline p95 latency ≤ 7 µs/runtime; zero allocations.  
AC-3 ProbeCache hit-rate ≥ 95  % on second launch; FlatSnapshot CRC + sig validate.  
AC-4 MetricSource exporter shows cache & queue counters; OTLP flag compiles.  
AC-5 Event queue `high_water_pct` < 60 % during MCP stress-test.  
AC-6 All unit + fuzz tests green; CI Release/ASan/TSan pass.  
AC-7 Codesign verify passes for host + snapshot + (opt) hot-patch dylib.  
AC-8 Preferences ▸ Plugins pane lists runtimes & capability masks.  
AC-9 Zero steady-state heap allocations on verifier & discovery hot paths.

──────────────────────────────────────────────────────────────────────────────
8. RISK REGISTER & MITIGATIONS
──────────────────────────────────────────────────────────────────────────────
R1 Template bloat (AdaptiveCache, QueueTraits) → Thin-LTO OFF by default; explicit instantiation.  
R2 Hot-patch table attack surface → feature-flag; notarisation enforced.  
R3 MuxQueue starvation → priority tag; can disable via flag.  
R4 FlatSnapshot format drift → central version enum + fuzz harness.  
R5 ServiceBase migration regressions → static_assert DAG check; staged merge by service group.

──────────────────────────────────────────────────────────────────────────────
9. OPEN QUESTIONS
──────────────────────────────────────────────────────────────────────────────
Q1 Do we enable `KAI_ENABLE_MUX_QUEUE` in production or keep experimental?  
Q2 Which signing authority issues hot-patch verifier dylibs? Enterprise vs. App-Store.  
Q3 Should FlatSnapshot signature be Ed25519 (libsodium) or rely on Apple CMS?  
Q4 Number of AdaptiveCache shards default: CPU or CPU×2?

──────────────────────────────────────────────────────────────────────────────
10. DELIVERABLE INVENTORY SUMMARY
──────────────────────────────────────────────────────────────────────────────
✓ 18 new headers, 10 .cpp, 7 tests, 3 benches, 3 fuzzers, 2 CI jobs, 2 docs  
✓ Generic AdaptiveCache, QueueTraits, MetricSource unify caches/queues across core  
✓ Signed FlatSnapshot secures ProbeCache & future ToolRegistry snapshots  
✓ Compile-time ServiceBase injection eliminates runtime topo sort  
✓ All hot loops remain header-only, zero virtual dispatch, zero steady-state heap allocs  
✓ Foundation laid for Slice-3 streaming & transport work without further refactor

──────────────────────────────────────────────────────────────────────────────
11. END-TO-END FLOW WALK-THROUGH  – HOW THE PIECES INTERLOCK
──────────────────────────────────────────────────────────────────────────────
This section offers a concrete, step-by-step story of what happens from the
instant Kai launches to the moment a runtime is trusted and ready to execute.
It serves as an implementation *compass* that connects every abstraction
introduced in the blueprint.

Step 1 – Service Kernel Boot
• `ServiceRegistry` starts first.  Legacy services still inherit classic
  `IService`, while **new** Slice-2 services (`VerificationStoreSvc`,
  `RuntimeScannerSvc`) use `ServiceBase<Derived,Deps…>` which embeds compile-time
  DAG checks; both styles coexist.
• `ArenaAllocatorSvc` initialises **rpmalloc** (or mimalloc when
  `-DKAI_ALLOCATOR=mimalloc`).  A zero-alloc guard counter is registered in
  `DiagnosticsService`.

Step 2 – Cold Scan Trigger
• `RuntimeScannerSvc` subscribes to FsEvents via `RingQueue<EventT, RingQueueTraits>`.
• The first batch of FsEvents lands and the coroutine `RuntimeScanner::consume()`
  fan-outs `RuntimeDescriptor{path, mtime_sha256}` tasks onto `ExecutorSvc`.

Step 3 – Verification Pipeline
• For each runtime, `SecurityFacade::verify(desc.sha256)` executes:
  1. **L1**: `AdaptiveCache` lookup inside `VerificationStore`.
     – If miss ratio > 15 % the cache auto-grows up to 16-way set-associativity.
  2. **L2**: `VerdictSnapshot` (FlatSnapshot mmap) checked via
     `verification_store->lookupL2()`.  Ghost entries avoid repeated negatives.
  3. **Verifiers**: Compile-time ordered list (CDHash, Seatbelt, optional
     external) executes under `AsyncContext` deadline (default 250 µs).
  4. Result is persisted: `VerificationStore::persistL1L2()` writes into the
     in-mem cache **and** schedules a snapshot roll-forward via `VerdictStoreSvc`.
• Dynamic verifiers from plugins pass through `DynamicVerifierRegistry` which is
  rate-limited to `≤8 verifiers` and `≤1 registration/second`.

Step 4 – ProbeCache & Discovery Completion
• When a runtime passes security checks, the same `Verdict` object is forwarded
  to `ProbeCache` where a separate `AdaptiveCache` instance accelerates future
  scans across all runtime types.  ProbeCache serialises to `FlatSnapshot` so
  cold launch #2 hits memory-mapped verdicts during Step 3.

Step 5 – Metrics & Observability
• Every cache and queue satisfies `MetricSource` so the OTLP exporter can call
  `.stats()` at a fixed 1 Hz with **no** virtual dispatch.
• Histograms capture latency buckets: verifier path (`sec.verifier_ms`) and
  runtime discovery (`runtime.scan_ms`).

Step 6 – UI Surfacing
• Preferences ▸ Plugins pane polls `DiagnosticsService` for latest scan result,
  reading shapshots of `ProbeCache` (via `RuntimeManagerSvc`) and rendering
  capability masks derived from `Mask128` helper functions.

Memory & Allocation Guarantees
• Hot-path structures (`AdaptiveCache`, `RingQueue`) are header-only and use
  per-shard fixed-size arrays to guarantee *zero steady-state* heap activity.
• Any heap use during Snapshot spilling goes through `ArenaAllocatorSvc` which
  tracks bytes and contributes to zero-alloc guard in hot loops.

Security Anchors
• SeatbeltVerifier fetches the current sandbox profile via ObjC++ shim and
  compares against the perfect-hash table generated at *build time* by
  `tools/seatbelt_delta_gen.py`; mismatches return `kRejected`.
• Out-of-band overrides go through the Hot-Patch table which itself is a signed
  FlatSnapshot validated by `SecStaticCodeCheckValidity` before mmap.

Developer Experience
• One-liner macros (`DBG`, `ERR`) remain usable; compile-time `ServiceId` enum
  ensures log context is always available.
• Adding a new cache/queue is 3-step: include AdaptiveCache/QueueTraits header,
  implement `stats()`, list the type in `metric_source_enumeration_test.cpp`.

This narrative should help every engineer map a code change to its runtime
consequence and trace data flow through the Slice-2 substrate without reading
all headers first.
