
DEEP MODE ⓬ – COMPLETE & UPDATED “SLICE-2” IMPLEMENTATION BLUEPRINT  
(Consolidates prior draft, incorporates the optimisation path agreed in last review: ordered verifier list, VerificationStore façade, adaptive ShardedCache, unified EventQueue abstraction. 100 % compatible with Slice-3 roadmap.)

──────────────────────────────────────────────────────────────────────────────
0. NOMENCLATURE & DIRECTORY CONVENTIONS  (unchanged)
──────────────────────────────────────────────────────────────────────────────
• C++ headers:         src/core/<domain>/…  /  include/kai/…  
• ObjC++ glue:         src/core/<domain>/macos/… (only when needed)  
• Tests:               src/core/tests/<domain>/… (Catch2)  
• Benchmarks:          src/core/benchmarks/<domain>/… (Google Benchmark)  
• Tools / scripts:     src/tools/…  /  scripts/…  
• Build flags:         SLICE2_PHASE_<N> (BOOL, default OFF until merged)  
• Macros:              DBG, ERR, DBM, ERM  (debug.h)  
• Error handling:      Expected<T, KaiErrc> (KaiErrc = std::error_code enum)  

──────────────────────────────────────────────────────────────────────────────
1. PHASE-0 – FOUNDATION, COROUTINES & PRIMITIVES
──────────────────────────────────────────────────────────────────────────────
1.1 New files / types  
   a. include/kai/meta/type_list.hh  
      – type_list, concat_t, contains_v …  
      – **NEW** ordered_type_list<T…> + for_each_type<TL>(Fn) (sorts by meta::priority).  
   b. include/kai/async/task.hh  – coroutine wrapper (unchanged).  
   c. include/kai/async/async_context.hh  
      – adds optional `trace_span*` (enabled when KAI_ENABLE_OTLP).  
      – `wrap(Awaitable&&)` auto-creates child span.  
   d. include/kai/async/task_semaphore.hh (awaitable counting semaphore).  
   e. include/kai/container/sharded_cache.hh  
      – **Stats** struct { hit/miss/evict } (atomic).  
      – adaptive `maybeGrow()` doubles kWays when miss_ratio > 15 %.  
   f. include/kai/runtime/ring_queue.hh (lock-free SPSC/MPSC, power-of-2).

1.2 CMake  
   • KaiCoroutines.cmake (+fcoroutines).  
   • target `kai_primitive` exports headers above.

1.3 Tests / Benches  
   • sharded_cache_stats_test.cpp (adaptive growth).  
   • ring_queue_(test|bench).cpp (unchanged).

──────────────────────────────────────────────────────────────────────────────
2. PHASE-1 – SECURITY CORE v2  (L1)  —  ORDERED VERIFIER & CACHE
──────────────────────────────────────────────────────────────────────────────
2.1 Verifier taxonomy  
   a. include/kai/security/verifier_strategy.hh  
      ```
      struct VerifierMeta { VerifierStage stage; uint8_t priority; };
      template<class Impl>
      concept VerifierStrategy = requires { { Impl::meta() } -> std::same_as<VerifierMeta>; };
      ```
   b. Built-in verifiers implement `static constexpr VerifierMeta meta();`.

2.2 Compile-time ordered list  
   ```
   using BuiltinVerifiers = meta::ordered_type_list<
        CDHashVerifier,
        SeatbeltVerifier
   >;
   ```

2.3 Dynamic verifier adapter (unchanged) always appended after built-ins.

2.4 VerificationStore façade  
   • interface `include/kai/security/verification_store.hh`  
     `{lookup(ctx,d), persist(ctx,d,v)}`.  
   • layered impl `layered_verification_store.hh/.cpp`  
     – top: ShardedCache (L1)  
     – slow: SnapshotManager (L2, Phase-2)  
     – auto-insert on miss.  
   • Stats exported via util/metrics.hh.

2.5 SecurityFacade  
   Members:  
   • std::unique_ptr<VerificationStore> store_;  
   • BuiltinVerifiers::for_each_type …  
   • std::vector<DynVerifierAdapter> dyn_;  

2.6 Tests / Benches  
   • verification_order_test.cpp (static_assert order).  
   • verification_store_hit_miss_test.cpp.  
   • security_l1_hit_bench.cpp (co-routine + adaptive cache).

──────────────────────────────────────────────────────────────────────────────
3. PHASE-2 – SNAPSHOT TIER (L2)  —  PERSISTED VERDICTS
──────────────────────────────────────────────────────────────────────────────
3.1 Storage primitives  
   • snapshot/wal_ring.hh/.cpp (mmap, CRC32 page).  
   • snapshot/sstable.hh (immutable).  

3.2 SnapshotManager (unchanged spec)  
   – integrates AsyncContext deadline; negative ghosts.  

3.3 Wired into `LayeredVerificationStore` slow tier.

3.4 Tests / Benches  
   • wal_ring_recovery_test.cpp.  
   • snapshot_lookup_bench.cpp.

──────────────────────────────────────────────────────────────────────────────
4. PHASE-3 – RUNTIME DISCOVERY PIPELINE (FS EVENTS)
──────────────────────────────────────────────────────────────────────────────
4.1 Event abstractions  
   a. include/kai/runtime/fs_event.hh (as-is).  
   b. include/kai/runtime/event_queue.hh  
      – concept `EventQueue<>` (unchanged).  
      – Base `QueueStats` {drop_counter, high_water_pct}.  

4.2 Queue implementations  
   • runtime/ring_queue_backend.hh – wraps RingQueue<T>.  
   • runtime/dispatch_channel_queue.hh/.mm – wraps `dispatch_channel_t`, same stats.  

4.3 Router & Consumer  
   • runtime/fs_event_router.hh  – templated on `EventQueue auto& Q`.  
   • runtime/runtime_scanner.hh  – `task<void> consume(Q&,shard)` converts FsEvent→RuntimeEvent.  

4.4 Metrics  
   – QueueStats exported; Single Prom gauge per shard.

4.5 Tests  
   • event_queue_stats_test.cpp.  
   • fsevent_router_stress_test.cpp (6 prod, 1 M ev).

──────────────────────────────────────────────────────────────────────────────
5. PHASE-4 – OPTIONAL BACKEND FLAGS
──────────────────────────────────────────────────────────────────────────────
5.1 CMake option(KAI_ENABLE_DISPATCH_CHAN OFF)  
   – compile-time guard; Router oblivious.

5.2 CI matrix OFF/ON; nm gate forbids libdispatch symbols when OFF.

──────────────────────────────────────────────────────────────────────────────
6. PHASE-5 – SEATBELT & CAPABILITY TOOLS
──────────────────────────────────────────────────────────────────────────────
6.1 seatbelt_verifier.hh/.cpp gains real impl using ObjC++ sandbox_check shim.  
6.2 tools/seatbelt_delta_gen.py → seatbelt_profile.phf.h (perfect-hash).  
6.3 add_custom_command regenerates phf.h on .sb change.  
6.4 Tests feed modified profile → expect kRejected.

──────────────────────────────────────────────────────────────────────────────
7. PHASE-6 – HYBRID STRATEGY LIST (DYNAMIC VERIFIERS)  (unchanged)
──────────────────────────────────────────────────────────────────────────────
7.1 interface i_dynamic_verifier.hh  
7.2 registry dynamic_verifier_list.hh/.cpp (register + DoS guard).  
7.3 adapter already in Phase-1.  
7.4 Plugin Manager extension ensures stage uniqueness + rate limit.  
7.5 Tests with mock plugin returning kRejected.

──────────────────────────────────────────────────────────────────────────────
8. PHASE-7 – EXTERNAL BRIDGE & CI  (unchanged minor doc edits)
──────────────────────────────────────────────────────────────────────────────
8.1 external_verifier_bridge.hh/.mm (XPC helper).  
8.2 Codesign + notarise helper; CI job `codesign-verify`.  
8.3 Perf dashboards ingest benches.  
8.4 Documentation: docs/slice2_architecture.md updated diagrams.

──────────────────────────────────────────────────────────────────────────────
9. CROSS-PHASE INSTRUMENTATION & GUARDS (updated)
──────────────────────────────────────────────────────────────────────────────
• util/metrics.hh      – exports CacheStats + QueueStats.  
• util/tracing.hh      – KAI_TRACE(category,msg); OTLP behind flag.  
• Sanitizers nightly.  
• clang-tidy rules.  
• perf_regression_gate.py (< 5 % drift).  
• Hazard pointer fuzz for RingQueue.

──────────────────────────────────────────────────────────────────────────────
10. RISKS & MITIGATIONS  (re-evaluated)
──────────────────────────────────────────────────────────────────────────────
R1 Ordered list meta bug → compile-time static_assert.  
R2 Cache adaptive grow thrash → cap kWays ≤ 16; metrics guard.  
R3 Dispatch backend API drift → CI dual matrix.  
R4 Dynamic verifier DoS → register rate-limit, AsyncContext deadline.  
R5 Cross-stage deadlocks → deadline + cancellation in AsyncContext.

──────────────────────────────────────────────────────────────────────────────
11. DELIVERABLE SUMMARY
──────────────────────────────────────────────────────────────────────────────
✓ 38 headers, 19 .cpp/.mm, 15 tests, 6 benches, 2 Python tools, 1 CMake helper,  
  3 CI jobs, 3 docs.  
✓ Guarantees compile-time verifier ordering, unified QueueStats path, adaptive  
  L1 cache, single VerificationStore injection point.  
✓ All hot loops remain header-only, zero virtual dispatch, zero steady-state  
  heap allocs.  
✓ Hooks and interfaces align 1-to-1 with Slice-3 epics (ProbeCache drops into  
  VerificationStore; RuntimeManagerSvc reuses EventQueue).  
