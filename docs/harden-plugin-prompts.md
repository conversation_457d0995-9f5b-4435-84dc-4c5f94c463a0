# Hardened Plugin System – Implementation Prompts

These prompts are **action-oriented check-lists** intended for _Go mode_ once a design option is selected.  
They map directly to concrete pull-requests / tasks and are ordered to minimise merge conflicts.

---

## 0. Pre-flight – confirm security scope

1. Enumerate **threat-vectors to mitigate** for the chosen approach (WebAssembly-first, XPC-sandbox fallback).  
   – Re-state risk appetite, performance budget, and which vectors are _out-of-scope_ for v1.
2. Define **acceptance-criteria**: measurable goals (e.g. _untrusted Wasm plugin cannot read outside its pre-opened dir; host survives plugin crash; maximum startup overhead ≤ 10 ms_).

---

## 1. Core build infrastructure

1. ✨ _Add_ `third_party/wasmtime/` git sub-module (or FetchContent) at a vetted tag.  
   – Update **CMake**: option `KAI_ENABLE_WASM_PLUGINS` (default ON).  
   – Honour `clang-format` + `clang-tidy` for any local wrapper headers.
2. Implement `src/core/plugins/wasm/CMakeLists.txt` producing static lib `wasm_runtime` linked into `core`.
3. Gate **JIT entitlements** in `package_app.sh` & `entitlements.plist` only when `KAI_ENABLE_WASM_PLUGINS` is ON.

---

## 2. Plugin manifest & discovery

1. Create `PluginManifest` struct (`core/plugins/manifest.h`):
    ```cpp
    struct PluginManifest {
        std::string            id;
        std::string            version;
        std::string            author;
        std::vector<std::string> capabilities; // "fs:read", "net:http", …
        std::filesystem::path  entry;          // path to .wasm or dylib
        sha256_t               hash;           // optional integrity
    };
    ```
2. Parser: `manifest::parse(const std::filesystem::path&) -> Expected<PluginManifest, Error>`  
   – Support **TOML v1.0** (use `cpptoml`).  
   – Fail-hard on unknown keys or missing mandatory fields.
3. Directory conventions:
   • User plugins: `~/Library/Application Support/Kai/Plugins/`  
   • System plugins: `${APP_BUNDLE}/Contents/Plugins/`  
   – `PluginManager::discover()` walks both; ignore world-writable dirs.

---

## 3. WebAssembly runtime integration

1. Wrapper class `WasmRuntime` (PIMPL to hide Wasmtime headers).
   – Compile with _component-model disabled_, WASI preview-2.
2. Expose **capability-based host API** via imports:
    ```cpp
    // host.fs.read(path: string) -> Buffer | Error
    // host.net.fetch(url: string, options) -> Response | Error
    // host.llm.complete(prompt: string) -> string
    ```
    – Enforce permissions by checking `PluginManifest.capabilities`.
3. Per-instance constraints:
   – **Memory limit** = 64 MiB, **fuel** counter to bound CPU.  
   – Trap → convert to `PluginError` and propagate.
4. JIT toggle: compile with `WASM_RUNTIME_ENABLE_JIT` build flag; when off, pre-compile modules (`*.cwasm`).

---

## 4. PluginManager refactor

1. Replace `core/llm/plugin_loader.*` with `core/plugins/plugin_manager.*` that supports **three back-ends**:
   • `WasmBackend` (default)  
   • `NativeSandboxBackend` (XPC helper, TBD)  
   • `InProcessDylibBackend` (legacy; _disabled by default_ via CMake option).
2. `PluginManager::load(const PluginManifest&)` selects backend based on `entry` extension.
3. Provide RAII handle `PluginInstance` offering limited async API:
    ```cpp
    Expected<Json, Error> call(std::string_view function, const Json& args);
    util::AsyncStream<Json> callStream(...);
    ```
4. **Isolation**: run each Wasm instance on its own `std::jthread`; enforce deadline.

---

## 5. Security enforcement & verification

1. Integrity: compute SHA-256 of plugin file; compare with `manifest.hash`.  
   – If mismatch → refuse to load.
2. **Code-signing** (native plugins): use `SecStaticCodeCheckValidity`.  
   – Enforce Developer ID & notarisation.
3. Sandboxing (native/XPC):
   – Generate minimal `.sb` seatbelt at runtime from requested capabilities.  
   – Spawn helper via `posix_spawn` + `sandbox_init`.
4. Logging: all plugin API calls -> `PluginAudit.log`; use `DBG` macro with structured JSON.

---

## 6. UI & UX

1. New **Preferences › Plugins** pane (Objective-C++) listing:
   • plugin id, version, author, capabilities  
   • toggle _Enable / Disable_  
   • button _Open in Finder_.
2. First-run consent dialog when a plugin requests elevated capability (e.g., network).  
   – Persist choice in `~/.config/kai/plugins.cfg` (ini or JSON).

---

## 7. Testing & CI

1. Unit-tests: `src/tests/plugins/` covering manifest parsing, Wasm sandbox escape attempts, resource limits.
2. Fuzz test Wasm import boundaries with `libFuzzer` (only in CI sanitiser build).
3. Integration test: compile sample `echo` Wasm plugin; assert round-trip ≤ 5 ms.
4. Add GitHub Action to scan for outdated Wasmtime CVEs weekly.

---

## 8. Migration path

1. Convert existing `echo` provider → `plugins/echo_wasm/echo.wat`.  
   – Provide both native & Wasm during transition, compile native only when `KAI_ENABLE_INPROCESS_DYLIB=ON`.
2. Deprecate direct usage of `ModelRegistry` from plugins; route through `host.llm.*` import.

---

## 9. Documentation

1. `docs/plugins/author_guide.md`: step-by-step to build & sign Wasm plugin.
2. `docs/plugins/security.md`: threat model, capability matrix, sandbox profile examples.

---

### DONE-when

-   All default builds pass `./run.sh` with **–plugins-safe** flag and no Hardened-Runtime violations.
-   Malicious POC Wasm (writes `/etc/passwd`) is denied & logged.
-   `PluginManager` unit-suite green under ASan/TSan.
