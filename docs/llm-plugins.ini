Objective: detach the existing OpenAI and Anthropic “providers” from the core tree and ship them as first-class Kai plugins while maximising simplicity, re-usability, future expansibility, scalability and performance, and while respecting the hard-security envelope (hardened runtime, capability masks, seat-belt, codesign).

─────────────────────────────────────────────────────────────────────────────  
1. Current State Snapshot
   1.1 Location  
       • `src/core/providers/openai/*` – ~7 files, 450 LOC main impl.  
       • `src/core/providers/anthropic/*` – (mirrors OpenAI, size similar).  
       • Both are built into the **core binary** and exposed through (likely) `LlmFactoryService` or direct calls.  
   1.2 Coupling Points  
       • HTTP stack: `core/net/http/*` (sync/async requests).  
       • Config: access tokens picked from `ConfigService`.  
       • Diagnostics macros (`DBG`, `ERR`).  
       • `ILlmProvider` / `LlmModel` interface (look-up via `LlmFactoryService`).  
   1.3 Security Foot-print  
       • Need outbound HTTPS, wall-clock timers, env-driven tokens → high-risk credentials inside host address-space.  
       • Currently share host allocator (good) but run **unenforced** capability boundaries.

─────────────────────────────────────────────────────────────────────────────  
2. Why Turn Them Into Plugins?
   • Isolation of third-party network logic → smaller TCB; host shrink.  
   • Enables hot-patchable provider updates independent of host release cycle.  
   • Clear capability gating (`NetAccess`, `FileRead:config/token`) and seat-belt enforcement.  
   • Future providers (e.g. Gemini, Claude-3, Mistral, Local LLM) can follow same pattern.  
   • CI: provider‐specific fuzzing, ASan/TSan, codesign.

─────────────────────────────────────────────────────────────────────────────  
3. Architectural Options

Option A – In-Process Native Plugin (.dylib, default path)  
   ✦ Each provider compiles to a hardened dylib loaded by `RuntimeManagerSvc`.  
   ✦ Exports standard ABI functions; inside `kai_plugin_initialize()` registers a concrete `OpenAiProviderService` implementing `ILlmProvider`.  
   ✦ Re-uses host HTTP subsystem via injected `ServiceRegistry` pointer.  
   ✔  Zero IPC latency; re-uses existing code almost verbatim.  
   ✘  Secrets still live in same address space; crash in plugin still nukes host (mitigated by try/catch boundaries).

Option B – XPC Sandboxed Helper (Native Plugin + Helper)  
   ✦ Plugin dylib is only a thin shim that spawns an XPC helper (seat-belt profile denies file system; restricts network domains).  
   ✔  Secrets never enter host space; memory isolation; hardened runtime satisfied.  
   ✘  IPC overhead (~0.15–0.3 ms per request); helper packaging & notarisation complexity; need new XPC management code (Day-3 work).

Option C – MCP Server (External Process, Stdio or HTTP)  
   ✦ Treat provider as MCP server; host talks JSON-RPC over pipes (TransportBase<Stdio>).  
   ✔  Re-uses zero-alloc MCP stack; language-agnostic; isolates faults.  
   ✘  Significantly higher latency (0.7–1.5 ms + JSON encode); duplicates request → JSON overhead; bigger deployment size.

─────────────────────────────────────────────────────────────────────────────  
4. Trade-off Matrix

| Dimension         | Option A Native  | Option B XPC        | Option C MCP            |
|-------------------|------------------|---------------------|-------------------------|
| Latency (p95)     | ★ 0.2–0.3 ms     | 0.35–0.6 ms         | 0.9–1.6 ms              |
| Security Isolation| ❍ basic (seat-belt only) | ★ strong (proc boundary) | ★★ strongest (proc + JSON) |
| Implementation Effort | ★ (2-3 dev-days) | ★★ (4-5 days + entitlements) | ★★ (5-6 days + MCP codec) |
| Re-use of Existing Code | ★★ high        | ★ high             | ❍ medium (wrap JSON)    |
| Future Provider Pattern | ★★ simple templates | ★ semi-simple     | ★ simple                |
| Memory Overhead   | ★ smallest (shared) | ❍ helper (10 MB)   | ❍ server (15 MB)        |
| Hot-patch ease    | ★★ dyload swap   | ★ requires helper   | ★ server restart        |

★ = good; ❍ = okay; blank = weak.

─────────────────────────────────────────────────────────────────────────────  
5. Recommended Strategy (Best-Balanced)

Primary path: **Option A – Native In-Process Plugin** for v1  
   • Fastest to land; minimal refactor; keeps cold-start target ≤ 125 ms.  
   • Harden with seat-belt (`com.apple.security.network.client`, restrict to api.openai.com / api.anthropic.com).  
   • CapabilityMask: `kNetHttps`, `kConfigRead`.  
   • Crash containment: wrap all plugin service entrypoints with `PluginGuard` RAII (already used for Null runtime); guard logs & converts panics to `KaiError::PluginCrash`.  
   • Future toggle (`KAI_ENABLE_XPC_PROVIDERS`) can lift them to Option B without breaking ABI.

Security note: We still protect tokens by loading them through `ConfigService` which consults macOS Keychain – plugin receives token only via API call, never raw filesystem read.

─────────────────────────────────────────────────────────────────────────────  
6. High-Level Refactor Plan

Step-0: Introduce `ILlmProviderService` (if not yet formalised) in `core/llm/interfaces/`.  
Step-1: Create `src/plugins/openai/` & `llm_anthropic/` with CMake skeleton (copy `sample_null`).  
Step-2: Move code:  
   • `openai_model.*`, `openai_chat_api.h`, options structs  → plugin dir.  
   • Do **not** drag HTTP or util headers → keep includes `<core/net/http/http_client.h>`.  
Step-3: Plugin Glue  
```cpp
extern "C" KaiStatus kai_plugin_initialize(KaiServiceRegistry* reg) {
    auto* diag   = reg->get<DiagnosticsService>();
    auto* config = reg->get<ConfigService>();
    // Instantiate provider with injected deps
    auto svc = std::make_unique<OpenAiProviderService>(*config, *diag);
    return reg->registerService(std::move(svc));
}
```  
Step-4: Update `KaiPluginInfo` in each plugin:  
```
id           = "openai" / "llm_anthropic"
capabilities = maskWith(kNetHttps | kConfigRead);
runtime      = KAI_RUNTIME_NATIVE_XPC? (future) else KAI_RUNTIME_NULL
```  
(Null runtime is fine – host performs no additional runtime init.)

Step-5: Delete provider sources from `core/providers`; adapt `LlmFactoryService` to perform **runtime lookup**:  
   • At `start()`, query `ServiceRegistry::byConcept<ILlmProviderService>()` and build provider map keyed by `model_id`.  
   • Remove compile-time `#ifdef OPENAI` toggles.

Step-6: Seat-belt + Capability Enforcement  
   • Generate `.sb` with `allow network-outbound (domain "api.openai.com")` etc.  
   • Set plugin capability bitmask; PolicyEngine denies if missing.  
   • Pre-compile blob `.sb.bin` via seat-belt generator step already in CI.

Step-7: CI / CMake  
   • Add two SHARED targets; copy dylibs into `App/Contents/Plugins/`.  
   • Codesign with “com.kai.plugin.llm_openai” ID; entitlements same as host minus JIT.  
   • Unit tests move to `tests/plugins/`.

Step-8: Metrics  
   • Expose `openai.request_ms`, `openai.errors_total` counters from inside plugin via DiagnosticsService API.

─────────────────────────────────────────────────────────────────────────────  
7. Re-usability & Future Expansion Patterns

7.1 **LlmHttpProviderBase<Derived>** (template in `src/core/llm/http_provider_base.hh`)  
   • Holds common logic: auth header assembly, retry w/ back-off, JSON (simdjson) parsing helpers, rate-limit bucket.  
   • Plugin class derives from it, overrides `buildRequest()`, `parseResponse()`.  
   • Macro `KAI_REGISTER_LLM_PROVIDER(Derived, "openai")` auto-generates service wrapper.

7.2 **Provider Capability Enumeration**  
   • Reserve upper 32 bits of Mask128 for “network domains” ; mask bit `kCapNetDomainApiOpenai`.  
   • PolicyEngine can whitelist/blacklist at domain granularity.

7.3 **Plugin Update Pipeline**  
   • `kai-plugin-updater` CLI fetches new dylib + signature; RuntimeManager performs hot-swap (`stop`, copy, `dlopen`, `start`) while UI shows toast.

7.4 **Migration Path for Other Providers**  
   • Copy template, adjust endpoints, register.  
   • Optional: compile as MCP server if binary dependency heavier (e.g. CUDA).

─────────────────────────────────────────────────────────────────────────────  
8. Performance Considerations

• HTTP stack already async; keep provider fully non-blocking (futures or coroutines) to avoid EventBus clog.  
• Plugin compiles with `-flto=thin` inheriting host’s LTO pipeline; avoid RTTI (`-fno-rtti`) to shrink size.  
• All request/response DTOs parsed with `simdjson::ondemand` into stack structs (no DOM).  
• Allocator: plugin links `kai_plugin_alloc_shim.h` to reuse rpmalloc heap → no mixed allocator fragmentation.  
• Warm context pool size 2 (tuning knob) to ensure p95 latency.

─────────────────────────────────────────────────────────────────────────────  
9. Risk & Mitigation

| Risk                         | Mitigation                                                |
|------------------------------|-----------------------------------------------------------|
| Token leakage in core logs   | Never log raw token; redact via `DiagnosticsService`.     |
| ABI drift between plugin & core | Maintain `kKaiAbiMajor` increment policy; CI verifier. |
| Network domain change        | Plugin exposes `provider.domain()` for PolicyEngine diff. |
| Seat-belt entropy mismatch   | Pre-compile & hash profile; ProbeCache denies on mismatch.|

─────────────────────────────────────────────────────────────────────────────  
10. Summary / Action Items

1. Approve **Option A** (native dylib plugin) for Slice-2 sprint Day-7–8.  
2. Land `ILlmProviderService` interface + `LlmHttpProviderBase`.  
3. Scaffold `openai` & `llm_anthropic` plugin directories; move code.  
4. Update `RuntimeManagerSvc` discovery filter & `LlmFactoryService`.  
5. Add seat-belt profiles, capability bits, diagnostics counters.  
6. Wire CI codesign, notarisation, and unit tests.  
7. Plan optional XPC migration flag (`KAI_ENABLE_XPC_PROVIDERS`) after slice-2 exit criteria met.

Once merged, core binary size should shrink ~80 KB, cold start unaffected, provider releases decouple from app, and new providers become nearly copy-paste exercises.

Let me know when you would like to switch to **Go mode** to implement the scaffolding and initial OpenAI plugin migration.


─────────────────────────────────────────────────────────────────────────────  
Design an effective plugin adapter interface that optimize for simplicity, reusability, future expansibility, scalability and performance
─────────────────────────────────────────────────────────────────────────────  
Design Goal  
Create a “plugin adapter interface” that lets any network-backed LLM provider (OpenAI, Anthropic, Gemini, etc.) plug into Kai with:

• Simplicity – one lightweight header for plugin authors.  
• Re-usability – shared HTTP / retry / JSON utilities factored out.  
• Future-proofing – easy to lift in-process dylib → XPC helper → MCP server.  
• Scalability & Perf – zero/low-virt on the hot path; coroutine-friendly; no heap churn; capability gating baked in.

─────────────────────────────────────────────────────────────────────────────  
1. Guiding Principles

1. Zero-overhead call path. No per-token virtual dispatch.  
2. Asynchronous first. Provider calls return `Task<>` (C++23 coroutine) so we can co-compose with other services or run inside an XPC transport later.  
3. Capability aware. Adapter exposes `capabilityMask()` at compile-time for PolicyEngine.  
4. ServiceRegistry friendly. The adapter itself is an `IService` so the host can auto-discover and hot-swap providers.  
5. Header-only façade for plugin authors; common “http-provider base” lives in core to share code.  
6. No RTTI / `dynamic_cast`; use concepts + CRTP.

─────────────────────────────────────────────────────────────────────────────  
2. Layer Diagram

```
┌───────────────────────────────┐
│  Host Core (Kai main binary)  │
│                               │
│  LlmFactoryService            │
│    └── map<model_id, ProviderPtr>────┐  (type-erased)
│                               │      │
│  ServiceRegistry              │      │
└───────────────────────────────┘      │
        ▲         ▲                    │ IService API
        │         │ registerService()  │
        │         │                    ▼
┌─────────────────────────────────────────────────────┐
│         Plugin dylib  (openai.dylib)           │
│                                                     │
│  OpenAiProviderService : LlmProviderAdapterBase     │
│     ├─ coroutine HTTP logic (simdjson, fmt)         │
│     └─ KAI_REGISTER_LLM_PROVIDER() macro            │
└─────────────────────────────────────────────────────┘
```

─────────────────────────────────────────────────────────────────────────────  
3. Core Types

3.1 `LlmRequest` / `LlmResponse`  
   • POD structs; no virtuals; string views + `span<float>` for embeddings.  
   • Pre-allocated in caller’s Arena; adapter guarantees **read-only** access.

3.2 `Task<T>`  
   • Alias to `kai::task<T>` which is a symmetric transfer coroutine returning `T` or `KaiError`; no allocations on suspend (uses intrusively linked awaiters pooled in ArenaAllocator).

3.3 `ILlmProviderService`  
```cpp
struct ILlmProviderService : public IService {
    virtual std::string_view providerId() const noexcept = 0;
    virtual span<const LlmModelInfo> models() const noexcept = 0;
    virtual Task<Expected<LlmResponse, KaiError>>
        complete(const LlmRequest& req) noexcept = 0;
};
```
Single v-table cost, **once per call**; inside `complete()` the real work is done via CRTP with no further virtual hops.

─────────────────────────────────────────────────────────────────────────────  
4. Re-usable Adapter Base (header-only)

`llm_provider_adapter_base.hh`

```cpp
template <class Derived>
class LlmProviderAdapterBase final : public ILlmProviderService {
  public:
    std::string_view providerId() const noexcept override {
        return Derived::kProviderId;
    }
    span<const LlmModelInfo> models() const noexcept override {
        return static_cast<const Derived*>(this)->modelsImpl();
    }
    Task<Expected<LlmResponse, KaiError>>
    complete(const LlmRequest& req) noexcept override {
        return static_cast<Derived*>(this)->completeImpl(req);
    }
    static constexpr Mask128 kRequiredCaps = Derived::kCaps;
};
```
• Only three forwards; no new allocations.  
• Compile-time `kRequiredCaps` lets PolicyEngine verify at plugin load time.

─────────────────────────────────────────────────────────────────────────────  
5. Registration Macro (reduces boilerplate)

```cpp
#define KAI_REGISTER_LLM_PROVIDER(DerivedType)                  \
extern "C" KaiStatus kai_plugin_initialize(KaiServiceRegistry* reg) { \
    auto svc = std::make_unique<DerivedType>();                 \
    return reg->registerService(std::move(svc));                \
}                                                               \
extern "C" KaiPluginInfo kai_plugin_get_info() {                \
    KaiPluginInfo info{};                                       \
    std::strncpy(info.id, DerivedType::kProviderId, sizeof(info.id)); \
    info.runtime = KAI_RUNTIME_NULL;                            \
    mask_to_bytes(DerivedType::kCaps, info.capabilities);       \
    info.abi_major = kKaiAbiMajor;                              \
    info.abi_minor = kKaiAbiMinor;                              \
    return info;                                                \
}
```
• Plugin author writes **two lines**:
```cpp
class OpenAiProviderService : public LlmProviderAdapterBase<OpenAiProviderService> {
    static constexpr char kProviderId[] = "openai";
    static constexpr Mask128 kCaps = kCapNetHttps | kCapConfigRead;
    // impl…
};
KAI_REGISTER_LLM_PROVIDER(OpenAiProviderService)
```

─────────────────────────────────────────────────────────────────────────────  
6. Extensibility Hooks

6.1 Extra Operations  
   • Adapter base exposes a template mix-in: `supports_embeddings_v<Derived>` etc. LlmFactoryService can down-cast via concepts, not RTTI.

6.2 Transport Futures  
   • When provider moves to XPC helper, we keep the same interface; only the underlying `completeImpl()` awaits an IPC coroutine instead of HTTP call.

6.3 Metrics  
   • `LlmProviderAdapterBase` has utility `recordLatency(ms)` that pushes to DiagnosticsService—implementation injected by dependency tuple in ctor.

─────────────────────────────────────────────────────────────────────────────  
7. Performance & Memory Notes

• Only one dynamic allocation on provider creation (`std::make_unique`).  
• All per-request buffers are allocated in caller’s Arena; provider must not `new`.  
• `completeImpl()` must `co_await httpClient.asyncPost(...)`; HTTP client already zero-alloc with string-view payload.

• No RTTI: macro sets `-fno-rtti` on plugin target.  
• Thin-LTO & ICF dedup identical adapter code across providers.

─────────────────────────────────────────────────────────────────────────────  
8. Comparison Against Alternatives

| Variant                            | Overhead | Author Effort | Future XPC / MCP |
|-----------------------------------|----------|---------------|------------------|
| Pure virtual interface (no CRTP)  | 1 v-call per provider + 1 per request | Low | Same, but extra v-calls |
| CRTP adapter + single v-table (ours) | **best** (1 v-call per request) | Low | Same code; swap impl |
| Concept + type-erased Any (std::poly) | std::function erase cost | Medium | Complicates ABI |
Our design balances minimal runtime cost with dead-simple author experience.

─────────────────────────────────────────────────────────────────────────────  
9. Next Step Check-list

1. Approve header spec & macro names.  
2. Add `llm_provider_adapter_base.hh` under `src/core/llm/`.  
3. Update `LlmFactoryService` to discover `ILlmProviderService` via ServiceRegistry.  
4. Migrate OpenAI & Anthropic to new adapter.  
5. Write sample “echo” provider unit test in `tests/plugins/`.

Say “go” when you want the implementation patches generated; say “deep” if you’d like to iterate further on the interface.


─────────────────────────────────────────────────────────────────────────────  
Create the first plugin with a comprehensive automation tests to make sure everything work correctly
─────────────────────────────────────────────────────────────────────────────  
Objective  
Deliver the **first real plugin (“llm_openai”) ​+ an automated test-suite** that proves the whole pipeline works: discovery ➜ seat-belt / capability check ➜ ServiceRegistry registration ➜ LlmFactoryService lookup ➜ happy-path request/response ➜ error paths.  Implementation will happen only after you reply “go”.

─────────────────────────────────────────────────────────────────────────────  
1. Scope Definition  
   • Plugin: OpenAI network provider as native in-process dylib using the adapter interface designed earlier (`LlmProviderAdapterBase`).  
   • Tests:  
     – Unit tests for request/response helpers.  
     – Integration test that dlopens the plugin via `RuntimeManagerSvc` and performs an end-to-end call against a **local stub HTTPS server** (no external traffic).  
     – Failure-mode tests (capability denied, 429 retry, network error).  
   • CI: Run in Debug + ASan matrix; verify zero heap allocs on hot path via rpmalloc stats.

─────────────────────────────────────────────────────────────────────────────  
2. Plugin Implementation Blueprint

Dir layout:
```
src/plugins/openai/
 ├─ CMakeLists.txt
 ├─ openai_provider_service.h   <-- derives from LlmProviderAdapterBase
 ├─ openai_provider_service.cpp
 └─ entrypoint.cpp              <-- only the KAI_REGISTER_LLM_PROVIDER macro
```

Key points:
• constexpr `char kProviderId[] = "openai";`  
• `static constexpr Mask128 kCaps = kCapNetHttps | kCapConfigRead;`  
• Uses injected `HttpClient&` from ServiceRegistry (dependency captured in ctor).  
• `completeImpl()` is a coroutine; builds JSON payload with `fmt::format_to`, posts to `https://api.openai.com/v1/chat/completions`, reads JSON via `simdjson::ondemand`.  
• Tokens parsed as `std::string_view` referencing the response buffer (no copy).  
• Logs via `DBG`/`ERR`.  
• Link against `kai_plugin_alloc_shim.h` to share rpmalloc heap.  
• Seat-belt profile compiled at install: allow outbound TCP 443 api.openai.com, deny all else.

CMake flags:
```
add_library(openai SHARED …)
target_link_libraries(openai PRIVATE core_http core_util fmt simdjson)
target_compile_options(openai PRIVATE -fno-rtti -fvisibility=hidden)
set_target_properties(openai PROPERTIES PREFIX "")
kai_install_plugin(openai)
```

─────────────────────────────────────────────────────────────────────────────  
3. Automated Test Strategy  

3.1 Framework  
• Use **Catch2 v3** (already present in `src/tests/`).  
• Helper `test_http_stub_server.hh` spins an in-proc HTTPS stub on `127.0.0.1:8443` with a pre-generated self-signed cert (test only).  
• All tests run with env `OPENAI_BASE_URL=https://127.0.0.1:8443` so the plugin hits the stub not the real API.

3.2 Unit Tests  
   a. `openai_request_build.cpp`  
      – Construct `LlmRequest` with 3 messages, call private helper `buildPayload(req)`, assert JSON matches golden string and **allocations==0** (rpmalloc stats).  
   b. `openai_response_parse.cpp`  
      – Feed sample JSON; verify `LlmResponse` fields and that string_views point into original buffer.

3.3 Integration Tests  
   • Fixture:  
     – Boot `AppKernel` (minimal ServiceRegistry + Diagnostics + Config + RuntimeManagerSvc).  
     – Register fake config (`openai.token = "sk-test"`) in memory.  
   • Steps:  
     1. Start stub HTTPS; it returns a canned completion JSON on POST `/v1/chat/completions`.  
     2. Let `RuntimeManagerSvc` scan `src/plugins/openai` directory.  
     3. Check `plugins.discovered_total == 1`.  
     4. Grab provider from `LlmFactoryService::provider("openai/gpt-4o")`.  
     5. Await `complete()` with simple prompt; assert result==“Hello stub”.  
     6. Verify Diagnostics counter `openai.request_ms` incremented, and rpmalloc steady-state allocations==0.

3.4 Negative Path Tests  
   • Capability Denied: start PolicyEngine with mask that omits `kNetHttps`; expect `KaiError::CapabilityDenied`.  
   • Rate-limit Backoff: stub returns HTTP 429 → provider retries with exponential back-off (but capped to 2 attempts), final error `KaiError::RateLimited`.  
   • Network Error: stub closes socket → expect `KaiError::Network`.

3.5 Concurrency Stress  
   • `TEST_CASE("openai_concurrent_32")` fires 32 `co_await`ed calls; assert ≤ 2 timeouts, p95 < 300 ms.

3.6 Zero-alloc Guard  
   • Build test with `-DKAI_VERIFY_ZERO_ALLOC`; macro asserts rpmalloc reports 0 allocs inside completion path.  Integration test wraps each call in `ZeroAllocGuard`.

─────────────────────────────────────────────────────────────────────────────  
4. CI / CMake Glue  

• `tests/CMakeLists.txt` adds `add_subdirectory(plugins/openai/tests)` compiling unit & integration suites.  
• GitHub Actions matrix includes `macos-14` runner building Release, Debug, ASan; runs `ctest -j 4`.  
• Gate: `openai` target must pass `codesign --verify` with hardened runtime.  
• `notarytool dry-run` invoked but ignored in PRs (real notarisation on tagged build).

─────────────────────────────────────────────────────────────────────────────  
5. Risk & Mitigation  

| Risk                  | Mitigation                                          |
|-----------------------|-----------------------------------------------------|
| External network call sneaks into CI | Stub server + env override; CI blocks egress. |
| Seat-belt profile blocks DNS lookup  | Use explicit `api.openai.com` in `network-outbound`. |
| Self-signed cert trust failure       | HTTP client configured with test-CA bundle. |
| Flaky timing on concurrency test     | Use deterministic stub latency; guard upper-bound. |
| Zero-alloc false positives on ASan   | Guard only hot path; allow alloc in setup.   |

─────────────────────────────────────────────────────────────────────────────  
6. Implementation Sequence  

Day-1  
   1. Add `llm_provider_adapter_base.hh`.  
   2. Create plugin directory + CMake.  
   3. Move existing OpenAI code, adapt to adapter.  

Day-2  
   4. Write entry-point macro, seat-belt template.  
   5. Update `LlmFactoryService` to discover providers dynamically.  

Day-3  
   6. Add Catch2 tests: unit + stub HTTPS server (reuse `asio` or tiny_httpd).  
   7. Integration test harness with AppKernel.  
   8. CI config updates, ensure codesign.  

Day-4  
   9. Concurrency & error-path tests.  
   10. Documentation in `docs/plugins/openai.md`.

─────────────────────────────────────────────────────────────────────────────  
Ready to implement? Reply **go** and I will:

• Scaffold `llm_provider_adapter_base.hh` and macro.  
• Add plugin source + CMake.  
• Add test suite skeleton.  
• (Subsequent iterations will flesh out provider logic and stub server.)

Say “deep” if you’d like further discussion or tweaks.
