
DEEP MODE ❽ – DETAILED IMPLEMENTATION BLUE-PRINT (“SLICE-2”)

The goal is to lay out, with engineering-level granularity, exactly how Slice-2 will be landed: which files, C++/ObjC++ types, build‐system updates, test suites, benchmarks, CI hooks, and roll-out gates are touched in every phase.  This complements the high-level eight-phase outline in `slice2.txt`, turning it into an actionable backlog.

──────────────────────────────────────────────────────────────────────
0. NOMENCLATURE & DIRECTORY CONVENTIONS
──────────────────────────────────────────────────────────────────────
• New headers live under `src/core/<domain>/…` (C++23).  
• ObjC++ stubs under `src/core/<domain>/macos/`.  
• Generic templates in `src/core/include/kai/…`.  
• Tests in `src/core/tests/<domain>/…`.  
• Benchmarks in `src/core/benchmarks/<domain>/…`.  
• Tools in `src/tools/…`; Python scripts under `scripts/`.  
• All new targets added to `src/core/CMakeLists.txt` or sub-CMake.  
• Options: `SLICE2_PHASE_<N>` (BOOL, default OFF until merged).

──────────────────────────────────────────────────────────────────────
1. PHASE-0 – FOUNDATION & COROUTINES
──────────────────────────────────────────────────────────────────────
1.1 Files / Types to add  
   a. `include/kai/meta/type_list.hh`
      ```c++
      namespace kai::meta {
      template<class... Ts> struct type_list {};
      template<class List, template<class> class Fn> struct for_each_type;
      // … index_of_v, contains_v, concat_t helpers …
      }
      ```
   b. `include/kai/async/task.hh`
      – `template<typename T> class task;` built on C++23 coroutines.  
      – `task<T> kai::async::make_ready(T)` helper.  
   c. `include/kai/async/task_semaphore.hh`
      – Awaitable counting semaphore (`co_await sem.acquire()`).

   d. `include/kai/runtime/ring_queue.hh` (+ `ring_queue.cpp` only if
      implementation cannot be header-only for TSAN instrumentation)
      – Template params: `typename T, size_t kSizePow2, class MemoryModelTag`.  
      – Public API: `bool tryEnqueue(T&&)`, `bool tryDequeue(T&)`.  
      – Branch-free index wrap with `kSizeMask = kSizePow2-1`.

1.2 CMake  
   • `KaiCoroutines.cmake` – adds `/std:c++20 -fcoroutines` flags.  
   • Link `KaiCoroutines` INTERFACE target to *ALL* `core` libraries.  
   • Add `ring_queue_test` + `ring_queue_bench` executables.

1.3 Tests / Benchmarks  
   • Catch2: `ring_queue_test.cpp` (single-producer, multi-producer).  
   • Google Benchmark: `ring_queue_bench.cpp` (p50/99 latency).  
   • CI perf JSON baseline committed under `build-bench/`.

1.4 Acceptance hooks (CI)  
   – Job `phase0-unit` (ASan + TSan).  
   – Job `phase0-bench` compares JSON; fail if p99 > 20 µs.

──────────────────────────────────────────────────────────────────────
2. PHASE-1 – SECURITY CORE v0 (L1)
──────────────────────────────────────────────────────────────────────
2.1 Cache implementation  
   • `include/kai/cache/set_associative_cache.hh`
     ```c++
     template<class Key, class Val,
              size_t kWays = 4, size_t kLines = 64,
              class Hash = murmur3>
     class SetAssociativeCache { … };
     ```
   • Align whole cache to `hardware_destructive_interference_size`.

2.2 Verifier concepts  
   a. `include/kai/security/verifier_traits.hh`
      – `concept Verifier` (`stage()`, `verify(ctx)` returns `Verdict`).  
      – `enum class VerifierStage : uint8_t { kEarly, kDefault, kLate };`
   b. `include/kai/security/verifier_chain.hh`
      – `template<class Tuple> Verdict invokeChain(const Tuple&, Ctx&);`
      – Fold-expression + `if constexpr`.

2.3 Stub builtin verifier  
   • `src/core/security/verifiers/cdhash_verifier.hh/.cpp`
     – Hardcoded `return kTrusted;` with DBG log.

2.4 Facade  
   • `src/core/security/security_facade.hh/.cpp`
     – Members: `PerCpuProbeCache`, `BuiltinTuple` (alias).  
     – Public `task<Expected<Verdict, KaiError>> verify(const Digest&)`.
     – `co_return` early on L1 hit.

2.5 Unit tests  
   • `security_facade_hit_miss_test.cpp`.  
   • `static_assert(kai::meta::index_of_v<SeatbeltVerifier, BuiltinTuple> == 1);`

2.6 Benchmarks  
   • `security_l1_hit_bench.cpp` measures coroutine + cache hit end-to-end.

──────────────────────────────────────────────────────────────────────
3. PHASE-2 – SNAPSHOT TIER (L2)
──────────────────────────────────────────────────────────────────────
3.1 Disk structures  
   a. `include/kai/snapshot/wal_ring.hh/.cpp`
      – mmap file of power-of-2 pages; CRC32 each page.  
   b. `include/kai/snapshot/sstable.hh`
      – Immutable table: header { level, num_entries, merkle_root, … }.

3.2 Snapshot Manager  
   • `snapshot_manager.hh/.cpp`
     – Coroutine `spill()` and `compactor()`.

3.3 Integration  
   • Extend `SecurityFacade` to: L1 miss ⇒ `snapshotManager.lookup()`.

3.4 Benchmarks  
   • `l2_snapshot_lookup_bench` with synthetic 10 k keys.

3.5 Migration tool  
   • `scripts/migrate_existing_cache.py` – pre-seed snapshot file.

──────────────────────────────────────────────────────────────────────
4. PHASE-3 – RUNTIME DISCOVERY (Ring Backend)
──────────────────────────────────────────────────────────────────────
4.1 Event type  
   • `include/kai/scanner/fs_event.hh`
     ```c++
     struct FsEvent { uint64_t inode; uint32_t flags; uint16_t shard; };
     ```

4.2 Router  
   • `include/kai/runtime/fs_event_router.hh`
     – Template `template<class QueueBackend>`; DSF hash = `std::rotl(inode, 13)`.

4.3 macOS implementation  
   • Modify `macos_app_scanner.mm`  
     – In FsEvents callback: compute shard, `router.enqueue(event)`.

4.4 Shard consumers  
   • `runtime_scanner.hh/.cpp`
     – `task<void> consume(RingQueue<FsEvent>& q, size_t shard_id);`

4.5 Back-pressure policy  
   – If `tryEnqueue` fails, set atomic `kDropping` flag and report via `metrics`.

4.6 Tests  
   • Stress test spawns 6 producers pushing 1 M events – assert 0 drops.

──────────────────────────────────────────────────────────────────────
5. PHASE-4 – DISPATCH CHANNEL BACKEND (Optional)
──────────────────────────────────────────────────────────────────────
5.1 Tag type / Concept  
   • `struct dispatch_tag {};` – specialization of `RingQueue` concept.

5.2 Implementation  
   • `runtime/dispatch_channel_queue.hh/.mm`
     – Wrap `dispatch_channel_t`; `enqueue` uses `dispatch_channel_push`.

5.3 Build option  
   • `CMakeLists.txt`
     ```cmake
     option(KAI_ENABLE_DISPATCH_CHAN "…" OFF)
     if(KAI_ENABLE_DISPATCH_CHAN AND APPLE AND ${CMAKE_OSX_DEPLOYMENT_TARGET} VERSION_GREATER_EQUAL 14.0)
       target_compile_definitions(kai_core PRIVATE KAI_USE_DISPATCH_CHAN=1)
     endif()
     ```

5.4 nm Gate  
   – CI job runs `nm -m` ensuring no `libdispatch` symbols when OFF.

──────────────────────────────────────────────────────────────────────
6. PHASE-5 – SEATBELT & CAPABILITY TOOLS
──────────────────────────────────────────────────────────────────────
6.1 Verifier  
   • `verifiers/seatbelt_verifier.hh/.cpp`
     – Reads current sandbox profile via `sandbox_check` (ObjC++ shim).  
     – Compares diff with PHF-generated baseline.

6.2 Delta tool  
   • `tools/kai-phf-tool/seatbelt_delta_gen.py`
     – Generates `seatbelt_profile.phf.h` (perfect hash table).

6.3 Build rule  
   – `add_custom_command` regenerates `.phf.h` when `.sb` profile changes.

6.4 Tests  
   – Feed modified profile, expect `kRejected`.

──────────────────────────────────────────────────────────────────────
7. PHASE-6 – HYBRID CHAIN (Dynamic Stage)
──────────────────────────────────────────────────────────────────────
7.1 Interface  
   • `include/kai/security/dynamic_verifier.hh`
     ```c++
     struct IDynamicVerifier {
       virtual VerifierStage stage() const noexcept = 0;
       virtual Expected<Verdict, KaiError> verify(const Digest&) noexcept = 0;
       virtual ~IDynamicVerifier() = default;
     };
     ```

7.2 Registry  
   • `dynamic_verifier_list.hh/.cpp`
     – `bool registerVerifier(std::unique_ptr<IDynamicVerifier>)`.

7.3 Integration  
   • `SecurityFacade::verify` after builtin tuple: iterate dynamic vector.

7.4 Plugin Manager  
   • Extended manifest checker ensures `stage()` collision free.

7.5 Tests  
   – Mock plugin adds verifier returning `kRejected`; assert chain aborts.

──────────────────────────────────────────────────────────────────────
8. PHASE-7 – EXTERNAL BRIDGE & CI
──────────────────────────────────────────────────────────────────────
8.1 XPC Bridge  
   • `external_verifier_bridge.hh/.mm`
     – XPC service that implements `IDynamicVerifier` via IPC.

8.2 CI updates  
   • Pipeline step `codesign-verify` for helpers + `notarytool submit`.  
   • Perf dashboards ingest benches (`<EMAIL>`,`@PR.json`).

8.3 Documentation  
   • `docs/slice2_architecture.md` – includes PlantUML + perf charts.  
   • `docs/plugins/verifier_sdk.md` – how to implement external verifier.

──────────────────────────────────────────────────────────────────────
9. CROSS-PHASE INSTRUMENTATION & GUARDS
──────────────────────────────────────────────────────────────────────
• `src/core/util/metrics.hh` – RAII timer pushes to Prometheus textfile.  
• `-fsanitize=thread,undefined,address` in nightly builds.  
• `clang-tidy` checks enabled: `performance*, cppcoreguidelines*, bugprone*`.  
• `codesign --check-notarization` on macOS job.  
• `./scripts/perf_regression_gate.py` returns non-zero on > 5 % regression.

──────────────────────────────────────────────────────────────────────
10. ROLLOUT TIMELINE (WORKING DAYS)
──────────────────────────────────────────────────────────────────────
 WD 1–3   Phase-0 code + tests  
 WD 4–6   Phase-1 cache + verifier skeleton  
 WD 7–10  Phase-2 snapshot + manager  
 WD 11–14 Phase-3 router + shard consumer (ring)  
 WD 15    Performance sync + hard-fail gates  
 WD 16–18 Phase-4 dispatch channel opt-in  
 WD 19–22 Phase-5 seatbelt tooling  
 WD 23–25 Phase-6 hybrid dynamic list  
 WD 26–28 Phase-7 bridge + docs + CI wires  
 WD 29    Canary flip ON in nightly  
 WD 30    Retrospective, tag `v0.2.0`

──────────────────────────────────────────────────────────────────────
11. RISKS & MITIGATIONS (DETAILED)
──────────────────────────────────────────────────────────────────────
R1 RingQueue ABA bugs → add hazard pointer test harness; fuzz with TSAN.  
R2 Cache associativity thrash → perf counter hook monitors miss rate; fallback bigger kLines.  
R3 Dispatch channel unavailable on < 14 → compile-time detect, CI dual matrix.  
R4 Dynamic verifier DoS → rate-limit plugin registration; time-box `verify` via deadline.
