# Kai – Master Implementation Plan for Slice-2 & Slice-3

> Revision: 2025-06-02 • Author: architecture-wg • Status: **DRAFT / AUTHORITATIVE**
>
> This document _supersedes_ individual slice notes in `@slice2.txt`, `@plugin-todo.md`, and
> `@mcp-todo.md` by consolidating them into a single, coherent strategy. All
> technical decisions for Slice-2 inherit **verbatim** from `@slice2.txt`; Slice-3
> builds directly on that foundation.

---

## 0. Executive Digest

• **Slice-2** delivers hardened _Security + Discovery_ across all runtime types
(Native, JavaScript, Wasm, MCP servers) on top of the zero-alloc substrates
defined in `@slice2.txt`.
• **Slice-3** finishes _Performance & Runtime Parity_: Wasm runtime hardening,
MCP streaming path, ToolRegistry, Thin-LTO & module adoption, advanced
observability.
• Both slices converge on **one** storage/queue substrate (`FlatSnapshot`,
`RingBuffer`, `EpochArena`) and a unified **RuntimeManagerSvc** that discovers
every runtime in parallel using a single **ProbeCache**.

---

## 1. Combined Road-map (High-level)

| Slice | Focus                | Duration | Key Epics                                                                                                                                                                                                                                                                                            | Exit Criteria                                                                                                                 |
| ----- | -------------------- | -------- | ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | ----------------------------------------------------------------------------------------------------------------------------- |
| **2** | Security & Discovery | 4 wks    | A) CapabilityMask128 + PolicyEngine ②<br>B) Seatbelt blobs + codesign helpers ②<br>C) ProbeCache v3 + RuntimeScanner (Native/JS/Wasm/MCP) ①③<br>D) RingBuffer + EpochArena rollout ②<br>E) Plugins Pane UI refresh ②<br>F) MCP Kernel hardening (constexpr topo, zero-alloc JSON-RPC decoder stub) ③ | AC-1‒AC-9 from `@slice2.txt` **plus**: MCP runtimes appear in ProbeCache; JS plugin RTT ≤ 5 ms; `tools/list` handshake passes |
| **3** | Performance & Parity | 5 wks    | G) WasmEdge hardened + Wasmtime fallback ①<br>H) MCP JSON-RPC zero-alloc path (SIMDJSON + varint codec) ③<br>I) Transport concept & type-erased storage ①③<br>J) ToolRegistry (MPH) + streaming AgentLoop ③<br>K) Thin-LTO + ICF, incremental C++20 modules ①<br>L) HDR histogram & OTLP exporter ①③ | p95 tool call ≤ 250 ms; Wasm ≥ 85 % native; Thin-LTO shrinks binary ≥ 15 %; OTLP spans visible                                |

Legend: ① = `@plugin-todo.md`, ② = `@slice2.txt`, ③ = `@mcp-todo.md`

---

## 2. Detailed Slice-2 Plan _(authoritative = `@slice2.txt`)_

### 2.1 Security Primitives

-   `Mask128` constexpr helpers (`has`, `set`, `popcount`).
-   `FlatSnapshot` header (CRC32 + SHA-256) for **ProbeCache** & policy snapshots.
-   Seatbelt profile blobs embedded at configure-time; XPC helper retained behind
    `KAI_ENABLE_SB_HELPER`.
-   Zero-alloc guard piggy-backs on ASan (no extra compile flag).

### 2.2 Discovery Pipeline (Unified)

```text
FsEventsRouter ─► RuntimeScanner ─► ExecutorSvc workers ─► ProbeCache (FlatSnapshot)
    (debounce)      (hash/mtime)       (codesign, seatbelt)
```

-   Scans **Native .dylib**, **JS**, **Wasm**, **MCP servers** in a single pass.
-   Results queued via `RingBuffer<DropOldest>` with `queue_pct` metric.
-   **Target:** Cold scan ≤ 150 ms for 200 runtimes (stretch goal 125 ms).

### 2.3 Policy & Capability Enforcement

-   `PolicyEngineService` reads FlatSnapshot; 32-entry L1 cache for hot look-ups.
-   Denied actions → `KaiError::CapabilityDenied` with diff mask.

### 2.4 Diagnostics & UI

-   Counters: `runtime.scan_ms`, `policy.denies_total`, `queue_pct`.
-   Preferences ▸ Plugins pane lists runtimes + capability mask.

### 2.5 Acceptance Criteria (Slice-2)

-   AC-1 … AC-9 from `@slice2.txt` _(unchanged)_.
-   **Plus:**
    -   MCP runtimes visible in ProbeCache.
    -   JS plugin round-trip latency ≤ 5 ms.
    -   `tools/list` handshake passes in MCP integration tests.

---

## 3. Detailed Slice-3 Plan _(superset of `@plugin-todo` + `@mcp-todo`)_

### 3.1 Wasm Runtime Hardening

-   `WasmRuntimeService` wraps WasmEdge; optional Wasmtime fallback via flag.
-   ArenaAllocator hooks into WasmEdge allocator; per-plugin memory budgets.
-   Watchdog kills runtimes > 100 ms CPU; logs via Diagnostics.

### 3.2 MCP Zero-Alloc Streaming Path

-   `JsonRpcDecoder/Encoder` – SIMDJSON _ondemand_ + `fmt::format_to`.
-   Varint-framed codec eliminates delimiter scans; newline mode behind flag.
-   Alloc hooks wired to `EpochArena`; guard verifies zero heap allocs on hot path.

### 3.3 Transport Concept & Type-Erased Storage

-   `TransportBase<Derived>` CRTP (Stdio / HTTP / Dylib).
-   Objects stored in fixed-size inline buffer; no v-tables or RTTI.
-   Each transport owns one `RingBuffer` for partial chunks; back-pressure metric.

### 3.4 ToolRegistry & AgentLoop

-   Per-server minimal-perfect hash snapshot (`bbhash`) + flat-map delta.
-   Snapshot serialises _name + capability_mask_ only (< 80 KB / 1 k tools).
-   `AgentLoop` streams `PartialChunk` directly to UI (no EventBus hop).

### 3.5 Thin-LTO, ICF & C++20 Modules

-   Global enable via CMake; explicit template instantiation files from Slice-2.
-   Migrate `util/debug`, `time_utils`, `capability_mask` to Clang modules behind
    `-DKAI_ENABLE_MODULES` feature flag.

### 3.6 Observability Upgrade

-   HDR histogram buckets in Diagnostics; OTLP exporter gated by flag.
-   Spans: `runtime.load`, `mcp.tool.<name>`, `event_bus.publish`.

### 3.7 Acceptance Criteria (Slice-3)

-   Cold start (200 runtimes) ≤ 125 ms.
-   JSON-RPC decode p95 ≤ 50 µs; tool call p95 ≤ 250 ms.
-   Wasm plugin ≥ 85 % native perf.
-   EventBus publish p95 < 0.5 ms; `RingBuffer` drops < 0.01 %.
-   Thin-LTO shrinks binary ≥ 15 %; module rebuilds ≤ 60 % baseline.
-   OTLP spans visible in Jaeger/Tempo.
-   Zero-alloc guard counter == 0 on all hot paths.
-   CI Release/ASan/TSan, codesign & notarisation **green**.

---

## 4. Deliverables Matrix

| Category            | Slice-2 Artefacts                                                            | Slice-3 Artefacts                                                                                                                           |
| ------------------- | ---------------------------------------------------------------------------- | ------------------------------------------------------------------------------------------------------------------------------------------- |
| **Core**            | `capability_mask.hh`, `ring_buffer.hh`, `epoch_arena.hh`, `flat_snapshot.hh` | `wasm_runtime.{h,cpp}`, `transport_base.hh`, `json_rpc_codec.{h,cpp}`, `hotpatch_table_service.{h}` (skeleton, pending ServiceId generator) |
| **Security**        | `seatbelt_profiles.inc`, `policy_engine_service.{h,cpp}`, `code_signing.h`   | _(unchanged)_                                                                                                                               |
| **Runtime**         | `runtime_scanner.{h,cpp}`, `runtime_manager_service.{h,cpp}`                 | `tool_registry.{h,cpp}`, `agent_loop.{h,cpp}`                                                                                               |
| **UI & Tooling**    | `PluginsPane.mm` update                                                      | Tool install/update UI hooks                                                                                                                |
| **Build & CI**      | Seatbelt generator CMake step                                                | Thin-LTO + modules flags, perf drift check                                                                                                  |
| **Tests & Benches** | Harnesses/fuzzers from Slice-2                                               | JSON-RPC & transport fuzzers                                                                                                                |

---

## 5. Integration Points & Trade-off Analysis

1. **Single ProbeCache** consolidates security verdicts for all runtimes.
   _PRO_: avoids duplicate hashing & DBs; _CON_: larger snapshot – mitigated by
   mmap + 4 KiB paging.
2. **Embedded seatbelt blobs** vs. runtime helper.
   _Embedded_ default avoids IPC (-3 ms cold start); helper retained only for
   salt drift / exotic caps.
3. **Mask128** chosen over 256-bit to halve cache traffic; high bits reserved.
4. **Type-erased Transport** is ≈2× smaller than `std::variant`; removes RTTI &
   v-tables at the cost of slightly more boilerplate.
5. **Varint framing** gives O(1) frame detection; newline mode kept for debug.

---

## 6. Phase Schedule (working days)

### Slice-2 (Days 1-15)

| Day   | Work Item                          |
| ----- | ---------------------------------- |
| 1-3   | Mask128, RingBuffer core + harness |
| 4-6   | EpochArena + FlatSnapshot + tests  |
| 7-9   | ProbeCache v3, RuntimeScanner      |
| 10    | PolicyEngine snapshot + L1 cache   |
| 11    | PluginsPane UI refresh             |
| 12-13 | CI glue, ASan zero-alloc guard     |
| 14-15 | Buffer, docs, fuzz polish          |

### Slice-3 (Days 16-30)

| Day   | Work Item                           |
| ----- | ----------------------------------- |
| 16    | Wasm runtime allocator hooks        |
| 17-18 | TransportBase + Stdio/HTTP impl     |
| 19-21 | JsonRpc varint codec + SIMDJSON     |
| 22-23 | ToolRegistry MPH + snapshot builder |
| 24    | AgentLoop streaming path            |
| 25    | Thin-LTO + modules flags            |
| 26-28 | OTLP exporter, histograms, benches  |
| 29-30 | Fuzz, perf tuning, docs             |

---

## 7. Next Steps

1. Confirm team bandwidth for 4 + 5 week slices.
2. Land `capability_mask.hh` & CMake seatbelt generator scaffolding.
3. Configure CI cache for FlatSnapshot artefacts.
4. Kick-off Day-1 epics (Mask128 + RingBuffer).

---

_End of document_
