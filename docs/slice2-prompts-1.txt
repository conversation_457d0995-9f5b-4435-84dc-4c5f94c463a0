Task: Implement AdaptiveCache<K,V,Policy>
Requirements:
  • Header-only, C++23, zero allocations on hot path.
  • Striped shards, adaptive kWays (2…16) with miss_ratio >15% trigger grow.
  • Provide stats() {hit,miss,evict} atomics; onEvict policy hook.
  • Unit: adaptive_cache_stats_test.cpp must pass.
Outputs:
  • include/kai/container/adaptive_cache.hh
  • tests/adaptive_cache_stats_test.cpp

Task: Integrate AdaptiveCache into VerificationStore
Requirements:
  • Replace ShardedCache with AdaptiveCache<CDHash,Verdict>.
  • Layer L2 VerdictSnapshot (FlatSnapshot).
  • Provide persistL1L2(), lookupL2() APIs.
  • verification_store_hit_miss_test.cpp green.
Outputs:
  • include/kai/security/verification_store.hh
  • snapshot/verdict_store.hh/.cpp
  • tests/verification_store_hit_miss_test.cpp

Task: Implement Mask128 helpers
Requirements:
  • constexpr has(), set(), popcount(), to_string().
  • No heap allocs; fits in two uint64_t.
Outputs:
  • include/kai/security/mask128.hh
  • tests/mask128_helper_test.cpp

Task: Implement Signed FlatSnapshot format
Requirements:
  • Header: magic, version, build_sha, crc32, sig_len, signature.
  • Ed25519 verify WHEN KAI_ENABLE_FLATSNAPSHOT_SIGN=ON.
  • mmap read; serialize write; fuzz target passes.
Outputs:
  • include/kai/storage/flat_snapshot.hh/.cpp
  • fuzz/flat_snapshot_fuzzer.cpp
  • tests/flat_snapshot_crc_sig_test.cpp

Task: Build SeatbeltVerifier + PHF generator
Requirements:
  • ObjC++ shim calls sandbox_check.
  • tools/seatbelt_delta_gen.py generates seatbelt_profile.phf.h.
  • Release builds enforce validation; Debug toggles via env.
Outputs:
  • include/kai/security/seatbelt_verifier.hh/.cpp
  • tools/seatbelt_delta_gen.py
  • tests/seatbelt_verifier_profile_test.cpp

Task: Create QueueTraits & update RingQueue backend
Requirements:
  • RingQueueTraits: lock-free spin wait; stats().
  • event_queue concept updated.
  • Hazard-pointer test + fuzzer pass.
Outputs:
  • include/kai/runtime/event_queue.hh
  • runtime/ring_queue_backend.hh
  • tests/queue_traits_spin_block_test.cpp
  • tests/ring_queue_hazard_pointer_test.cpp
  • fuzz/ring_queue_fuzzer.cpp

Task: Add MetricSource enumeration to Diagnostics
Requirements:
  • Concept MetricSource; exporter enumerates at CT.
  • caches & queues auto-register via static list.
Outputs:
  • include/kai/util/metrics.hh
  • diagnostics/metrics_exporter.{h,cpp}
  • tests/metric_source_enumeration_test.cpp

Task: Introduce ServiceBase<Derived,Deps…>
Requirements:
  • CRTP mix-in; static_assert DAG.
  • Migrate new services; legacy via adapter.
Outputs:
  • include/kai/foundation/service_base.hh
  • core/foundation/service_static_checks.h

Task: CI & Build system hooks
Requirements:
  • FetchContent rpmalloc; option KAI_ALLOCATOR.
  • codesign_verify.sh job; perf drift gate <5%.
  • Add flags KAI_ENABLE_VPATCH, KAI_ENABLE_MUX_QUEUE.
Outputs:
  • cmake/KaiOptions.cmake
  • ci/scripts/codesign_verify.sh
  • perf_regression_gate.py

Task: End-to-End Cold Scan Integration Test
Requirements:
  • Simulate 200 mixed runtimes; cold_scan_integration_test asserts ≤150 ms.
Outputs:
  • tests/cold_scan_integration_test.cpp 