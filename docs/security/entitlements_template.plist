<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <!-- <PERSON> hardened-runtime entitlements template -->
    <key>com.apple.security.app-sandbox</key>
    <true/>
    <!-- Disable library validation only when KAI_ENABLE_INPROCESS_DYLIB=ON (Debug) -->
    <!-- <key>com.apple.security.cs.disable-library-validation</key><true/> -->

    <!-- Enable JIT only when loading Wasm/JS runtimes (guarded by <PERSON><PERSON><PERSON>_ENABLE_WASM_PLUGINS) -->
    <!-- <key>com.apple.security.cs.allow-jit</key><true/> -->

    <!-- Network / file-system capabilities are injected by the host according to manifest.toml -->
</dict>
</plist> 