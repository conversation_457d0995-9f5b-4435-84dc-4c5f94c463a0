; Kai plugin sandbox template – deny-by-default
; Fill in «PLUGIN_ID» and generated capability sections.
(version 1)
(deny default)

; ————— Core allowances —————
(allow file-read*
    (subpath "/System/Library/Fonts"))

; Injected by host based on manifest capabilities
; ##BEGIN_AUTOGEN_CAPABILITIES##
; (allow network-outbound)
; (allow sysctl-read (sysctl-name "hw.machine"))
; ##END_AUTOGEN_CAPABILITIES##

; Harden against DYLD exploits
(deny mach-lookup (xpc-service-name "com.apple.dock"))

; Debug builds may insert (allow process*) when KAI_SB_DISABLE=1 