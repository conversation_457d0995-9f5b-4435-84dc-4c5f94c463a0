DEEP MODE  –  GAP ANALYSIS & PRIORITISATION  
(Repository state @ HEAD, Slice-2 blueprint as reference)

────────────────────────────────────────
1. METHODOLOG<PERSON>
────────────────────────────────────────
• Parsed Slice-2 task list (Tasks 1-35) in `slice2-prompts-0.txt`.  
• Crawled workspace for corresponding source, test, fuzz, bench, CI & doc artefacts.  
• Mapped every task → implementation status → impact on Slice-2 Acceptance Criteria (AC-1…AC-9).  
• Flagged partial implementations (artefact present but spec not fully met).  
• Derived dependency chain (runtime, build/CI, quality-gate, docs).  
• Bucketed gaps into four urgency tiers:

  P0 Critical      – blocks at least one AC or hard security goal.  
  P1 High          – strongly affects reliability / security / perf but not an AC hard-gate.  
  P2 Medium        – nice-to-have quality, fuzz, additional benches.  
  P3 Low           – docs & decision artefacts; can slip after code-freeze.

────────────────────────────────────────
2. STATUS MATRIX BY TASK
────────────────────────────────────────
✓ = spec met ◐ = partial ✗ = missing

| # | Task (abridged)                                   | Impl artefacts found                | Status |
|---|---------------------------------------------------|-------------------------------------|--------|
| 01| Mask128 helpers                                   | `mask128.hh`, unit-test             | ✓ |
| 02| AdaptiveCache + instantiation                     | `adaptive_cache.{hh,cpp}`, tests, bench | ✓ |
| 03| VerificationStore                                 | `verification_store.hh`, integrated | ✓ |
| 04| FlatSnapshot format                               | `flat_snapshot.{hh,cpp}`, tests     | ✓ |
| 05| ProbeCache (L1+L2)                                | `probe_cache.{hh,cpp}`, LMDB layer  | ✓ |
| 06| QueueTraits + RingQueue backend                   | `queue_traits.hh`, `ring_queue_backend.hh`, bench | ✓ |
| 07| RuntimeScanner coroutine                          | `runtime_scanner.{hh,cpp}`, tests   | ✓ |
| 08| MetricSource concept & exporter                   | `util/metrics.hh`, `metrics_exporter.hh` | ✓ |
| 09| ServiceBase + static checks                       | `service_base.h`, `service_graph_check.cpp` | ✓ |
| 10| Build flags & CI glue                             | `cmake/KaiOptions.cmake`, `codesign_verify.sh` | ◐ (CI YAML toggle & perf-gate integration absent) |
| 11| DynamicVerifierRegistry rate-limit                | `dynamic_verifier_registry.{hh,cpp}`, tests | ✓ |
| 12| AdaptiveCache heuristic constants & docs          | Threshold hard-coded (15 %), constant aliases & Doxygen block **missing** | ◐ |
| 13| Global allocator + zero-alloc guard               | `zero_alloc_guard.{h,cpp}`, test    | ✓ |
| 14| RingQueue fuzzer & ABA stress harness             | ABA unit-test present; **libFuzzer harness missing** | ◐ |
| 15| SeatbeltVerifier & PHF table                      | `.hh/.mm`, generated table, tests   | ✓ |
| 16| Hot-patch verifier table (flag)                   | `.hh/.cpp`, compile flag, fuzzer    | ✓ |
| 17| VerdictStore (FlatSnapshot L2)                    | `verdict_store.{hh,cpp}`, tests     | ✓ |
| 18| MuxQueue backend (flag)                           | no `mux_queue.hh`, bench, tests     | ✗ |
| 19| FlatSnapshot fuzzer                               | no `flat_snapshot_fuzzer.cpp`       | ✗ |
| 20| Perf regression gate script                       | `perf_regression_gate.py`, enum test | ✓ |
| 21| PolicyMask integration & verifier bench           | `verifier_strategy.hh`, bench       | ✓ |
| 22| AdaptiveCache fuzzer                              | missing                             | ✗ |
| 23| Manifest JSON fuzzer                              | missing                             | ✗ |
| 24| Verifier pipeline micro-bench                     | `verifier_pipeline_bench.cpp`       | ✓ |
| 25| Runtime scan 200-bench                            | missing                             | ✗ |
| 26| QueueTraits spin-block unit-test                  | missing                             | ✗ |
| 27| Thin-LTO build toggle                             | no `KAI_ENABLE_THINLTO` option      | ✗ |
| 28| Notarisation simulation CI job                    | script `notarisation_sim.sh` absent | ✗ |
| 29| Legacy IService migration plan doc                | `docs/legacy_service_migration.md`  | ✗ |
| 30| HDR histogram export for latency metrics          | hdr_histogram dep & exporter hook   | ✗ |
| 31| Preferences ▸ Plugins Pane UI update              | no `preferences_plugins_pane.mm`, model | ✗ |
| 32| slice2_architecture.md refresh                    | missing                             | ✗ |
| 33| MuxQueue starvation test + high-water alert       | alert logic & tests absent          | ✗ |
| 34| Decision log for open questions                   | docs/slice2_open_questions.md       | ✗ |
| 35| Unified fuzz harness runner                       | CMake meta-target & helper script   | ✗ |

────────────────────────────────────────
3. PRIORITY BUCKETS
────────────────────────────────────────
P0 – CRITICAL (blockers for Slice-2 AC or core security guarantees)
• 33  High-water alert & starvation suite  
  – AC-5 requires queue `high_water_pct` alerts; currently no alert emitter or starve test.  
• 18  MuxQueue backend *(flagged OFF by default but)* EventBus high-prio alert part of Task-33 expects MuxQueue stats; stub or real impl needed.  
• 27  Thin-LTO toggle *(optional for size/perf)* but CI matrix references it; build will fail when flag passed.  
• 10  (remaining part) CI YAML integration for new CMake flags + perf gate; without it AC-1/-2 drift check not enforced.  
• 28  Notarisation simulation job – AC-7 expects snapshot & dylib codesign+notary verify in CI.

P1 – HIGH (security / performance / robustness, but not AC hard-gate)
• 12  Expose `kMissGrowThreshold` & `kMaxWays` constants + Doxygen; update unit-test.  
• 14  RingQueue libFuzzer harness (adds coverage for ABA paths).  
• 19  FlatSnapshot fuzzer (header/payload corruption).  
• 22  AdaptiveCache fuzzer (adversarial keys & shard growth).  
• 23  Manifest JSON fuzzer (plugin manifest parser).  
• 25  Runtime scan 200-bench – monitors AC-1 budget routinely.  
• 26  QueueTraits spin-block unit-test – validates lock-free wait strategy.

P2 – MEDIUM (quality-of-life, instrumentation, docs)
• 30  HDR histogram export – richer latency metrics but not gating.  
• 31  Plugins pane UI revamp (read-only view) – user-visible but not core.  
• 35  Unified fuzzer harness runner – CI convenience; individual fuzzers can still run.  
• 32  slice2_architecture.md refresh.  
• 29  Legacy IService migration doc.  
• 34  Decision log doc.

P3 – LOW (informational / stretch)
• Additional minor doc typos, missing inline comments, small code-style nits.

────────────────────────────────────────
4. DEPENDENCY & ORDER-OF-EXECUTION
────────────────────────────────────────
1. Implement Task-33 (alert logic) – relies on QueueStats already present; does **not** strictly depend on MuxQueue, but if we skip Task-18 we must stub MuxQueue high-water fields to ensure compile flag OFF path compiles.  
2. Land Task-10 remaining CI plumbing together with Task-27/28 to avoid CI breakage in one pass.  
3. Integrate MuxQueue (Task-18) OR provide minimal stub to satisfy starvation test & high-water alert; confirm product decision on flag default (open Q1).  
4. Complete fuzz suite (Tasks 14,19,22,23) then wire Task-35 harness.  
5. Fill in perf & spin-block benches/tests (Tasks 25,26) – gives signal for perf-gate.  
6. Instrument latency histograms (#30) and exporter update – no breaking risk.  
7. Tackle UI & documentation tasks (#31, 32, 29, 34).

────────────────────────────────────────
5. RECOMMENDED NEXT STEPS
────────────────────────────────────────
• Decide quickly on MuxQueue shipping status (enable vs experimental) – influences Tasks 18 & 33 scope.  
• Allocate one sprint-day to CI glue: add `KAI_ENABLE_THINLTO`, notarisation job, perf-gate wiring.  
• Parallelise fuzz workstreams (they touch disjoint code, ideal for separate contributors).  
• After critical queue alert path is green, run full cold-scan & verifier latency benches to capture baseline JSON for perf gate.  
• Schedule documentation tasks for last ½-day before code-freeze.

────────────────────────────────────────
6. TRADE-OFF NOTES
────────────────────────────────────────
• Skipping full MuxQueue implementation keeps binary size & complexity down but forces alert logic to rely solely on RingQueue stats; acceptable if Q1 resolves “keep OFF”.  
• Thin-LTO toggle can default OFF; provide compile option only, delaying CI size regression tracking to Slice-3 if resource-constrained.  
• HDR histogram export adds 30-40 KB to binary due to templated hdr_histogram headers; negligible compared to security gains in observability.  
• Unified fuzzer runner (#35) is convenience; leaving fuzzers as independent targets retains same coverage at cost of CI verbosity.

────────────────────────────────────────
7. SUMMARY
────────────────────────────────────────
The Slice-2 codebase is **~70 % complete**; all core runtime, security and caching primitives are implemented and unit-tested.  
The **major outstanding blockers** are the queue high-water alert pipeline, final CI glue (Thin-LTO flag, notarisation sim, perf gate), and (optionally) the MuxQueue backend.  
Fuzzers, additional benches, and documentation represent the bulk of remaining effort but are non-blocking for the critical acceptance criteria.
