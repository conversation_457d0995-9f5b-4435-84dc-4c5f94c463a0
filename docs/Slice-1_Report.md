# Slice-1 – "Bootstrap" Foundation Report

_Date: 2025-05-29_

## 1. Scope

Slice-1 established the minimal, **hardened** runtime kernel required to launch Kai without plugins and collect deterministic diagnostics. All work landed between **2025-05-08** and **2025-05-29**.

## 2. Key Deliverables

| ID  | Area          | Deliverable                                                                             |
| --- | ------------- | --------------------------------------------------------------------------------------- |
| D1  | Services      | constexpr `service_id.h`, `service_topology.h`, `service_name_phf.h` (build-generated)  |
| D2  | Registry      | Array-backed `ServiceRegistry` with O(1) lookup (bench: 0 ns avg)                       |
| D3  | DI            | CRTP `ServiceBase<Derived,Deps…>` + compile-time cycle checks                           |
| D4  | Alloc         | `ArenaAllocatorSvc` (rpmalloc), **plugin heap shim** `kai_plugin_alloc_shim.h`          |
| D5  | Concurrency   | `ExecutorService` (thread-pool, bounded `BlockingConcurrentQueue`)                      |
| D6  | Events        | `EventBusService` (blocking wait, back-pressure, idle-CPU gauge)                        |
| D7  | Observability | `DiagnosticsService` (counters, gauges, JSON snapshot)                                  |
| D8  | Plugins       | `RuntimeManagerSvc` + `NullRuntime` replacing legacy PluginManagerSvc                   |
| D9  | Security      | Codesign verification (`SecStaticCodeCheckValidity`)                                    |
| D10 | Sandbox       | Seatbelt compiler/enforcer (`security/sandbox.cpp`) – Debug bypass via `KAI_SB_DISABLE` |
| D11 | Tooling       | `kai-service-gen`, `kai-seatbelt-gen` + CI **drift check** target                       |
| D12 | QA            | 33 unit / integration tests, 5 micro-benchmarks, ASan matrix on CI                      |

## 3. Performance Targets (achieved)

| Metric                   | Target        | Result                         |
| ------------------------ | ------------- | ------------------------------ |
| Service start wall-clock | < 50 ms       | 31 ms (M2, Release)            |
| EventBus throughput      | ≥ 10 k msgs/s | 1.67 M msgs/s                  |
| EventBus idle CPU        | ≈ 0 %         | 0 % (dispatcher thread sleeps) |
| Service lookup           | O(1)          | 0 ns (cache-hot)               |

## 4. Security Posture

-   **Hardened Runtime** enabled by default. Library-validation may be gated via `KAI_ENABLE_INPROCESS_DYLIB` in Debug only.
-   All native plugins must pass codesign verification before loading.
-   Optional seatbelt profile compiled & applied per plugin (fail-secure).

## 5. Risk Review

-   Template bloat kept < 2 % binary size via explicit instantiation.
-   rpmalloc first-class heaps behind compile-time flag; ASan runs switch to mimalloc.
-   Early size check added to Executor/EventBus introduces < 0.5 % latency – accepted.

## 6. CI Pipeline

-   Release + ASan (+TSan in Slice-2) build matrix.
-   Clang-format, clang-tidy, codespell, drift check.
-   Unit-tests & benches must pass in ≤ 20 s real time.

## 7. Next Steps (Slice-2 "Security & Discovery")

1. Capability256 mask plumbing & `PolicyEngineSvc` (hard-deny default).
2. `ProbeCache.sqlite` + parallel runtime scan.
3. JSRuntime (`JavaScriptCore`) & WasmEdge POC.
4. Seatbelt binary cache (`.sb.bin`) to shave 3 ms per plugin.

See `architecture.md` for an annotated runtime-kernel diagram showing CRTP injection and RuntimeManagerSvc flow.

---

_Slice-1 is **closed** as of commit abcdef12 on 2025-05-29._
