# Kai MCP Integration – Implementation Log

# Deep Mode – Comprehensive Analysis & Strategy Recommendations for MCP Support

0. Executive Summary  
   ——————————————————  
   <PERSON> already has a solid service kernel, a hardened plugin loader and a roadmap that emphasises capability-gated security, first-class diagnostics and deterministic performance. MCP (Model Context Protocol) fits naturally into this architecture: an MCP **client** can run inside the core, while servers can be shipped in three deployment modes (native dylib plugin, stdio subprocess, or in-process JS/Wasm). The key design question is where to draw the contract boundaries so we maximise performance, minimise risk, and keep the codebase cohesive.

Below we:

• Map MCP concepts onto existing Kai services & plugin system  
• Summarise MCP best-practice patterns observed in reference implementations  
• List the most popular / high-impact MCP servers worth supporting out-of-the-box  
• Present three architectural options (with pros/cons) for Kai’s MCP integration  
• Recommend a hybrid roadmap that leverages Option B (core client + pluggable transports) as the best trade-off.

1. Current Codebase Assessment  
   ——————————————————  
   Key subsystems relevant to MCP:

1. PluginManagerSvc (`src/core/plugins`)  
   • Hardened loader, UID check, ad-hoc codesign verify, per-plugin rpmalloc heap  
   • Can already probe dylibs; trivial to extend to probe **stdio** or **HTTP** “servers” as another plugin type.

1. EventBusSvc & ExecutorSvc  
   • Provide async dispatch and pub/sub – ideal for routing JSON-RPC messages without blocking the UI thread.

1. DiagnosticsService  
   • Counter framework → can instrument MCP traffic (bytes in/out, call latencies, error types).

1. ArenaAllocatorSvc  
   • Deterministic allocator; good for buffering streaming JSON without hitting `malloc`.

1. LlmFactoryService / Model loop (not yet fully in tree)  
   • Needs to detect function-call / tool_use messages and pause/resume generation. Hooks can be added here.

Gap analysis:

• No MCP client transport, handshake or JSON-RPC framing code yet.  
• No “ToolRegistry” abstraction mapping tool-names→handler-callbacks.  
• Security policy layer (Slice 2) will need rules for which MCP servers may run and what capabilities they expose.

2. Web Research: MCP Best Practices  
   ——————————————————  
   Patterns distilled from official docs & ~50 k-star reference repo:

• **JSON-RPC 2.0 everywhere** – framing + incremental decode; servers usually support both stdio & SSE/HTTP transports.  
• **tools/list & resources/list caching** – clients cache for session; servers include `etag` or `rev` hint.  
• **Streaming results** – servers often use incremental chunks (`partial` notifications) → clients must forward to LLM in real time.  
• **Human-in-loop hooks** – many clients inject a policy callback so the UI can veto a tool call before execution.  
• **Timeouts & rate limits** – reference clients default to 30 s call timeout, exponential back-off on failures.  
• **Opaque binary payloads** – large file downloads are served as Resources, not Tool results, to keep JSON small; the client may stream to disk.  
• **Transport fallback** – if stdio server isn’t on PATH, most desktop apps auto-fallback to `npx` or Docker image.  
• **Server discovery via manifest** – JSON file declaring command, args, env; conceptually similar to our plugin manifest.

3. High-usage MCP Servers (2025-05 data)  
   ——————————————————  
   “Popularity” measured by Smithery.ai / MCP Hub usage counts and GitHub stars.

Tier 1 (must-have for first release)  
• Filesystem (secure file read) – official reference; critical for context.  
• Git & GitHub – code context / repo ops.  
• Brave Search or Tavily – web search w/o Google API keys.  
• Fetch – generic HTTP fetcher.  
• Sequential Thinking – structured chain-of-thought scratchpad (great demo).  
• SQLite / Postgres – local DB read-only queries.

Tier 2 (ship as optional downloads)  
• Slack, Google Drive, Redis, Puppeteer (browser automation), Memory K-Graph.

4. Strategic Options for Kai  
   ——————————————————  
   OPTION A – “All-in-core” Embedded MCP Client  
    • Implement full JSON-RPC + transport stacks in core C++.  
    • Launch stdio servers via `NSTask` or `posix_spawn` on demand.  
    • Pros: lowest latency, single trust domain, easier to debug.  
    • Cons: heavier core; any JSON-RPC vuln compromises app; updating transports requires app update.

OPTION B – Core Client **+ Transport Mini-Plugins** (Recommended)  
 • Keep a thin MCPClientSvc in core that forwards transport handling to pluggable “MCPTransport” services:  
  – StdioTransport (launches & pipes) – C++ service shipped in core  
  – HttpSseTransport – uses libcurl / URLSession  
  – NativePluginTransport – bridges to in-process dylib implementing `kai_mcp_tool_call`  
 • ToolRegistry lives in core; each transport registers discovered tools.  
 • Pros: 90 % speed of Option A, but clearer contracts; adding new transport (e.g. WebSocket) is just another service.  
 • Core surface area stays small; transport code can be sandboxed more aggressively.  
 • Cons: Slight indirection; still need care with shared structs across transports.

OPTION C – “Everything is a Plugin”  
 • Ship an `McpClientPlugin.dylib` that links JSON-RPC lib, registers ToolRegistry and runs entirely via C ABI.  
 • Pros: core unchanged, hot-swappable.  
 • Cons: more cross-boundary calls, plugin runs with app privileges anyway, complicates diagnostics wiring.

Performance & Security Comparison (▲ best, ▼ worst)

| Criterion        | A Core          | B Hybrid          | C Plugin         |
| ---------------- | --------------- | ----------------- | ---------------- |
| Latency          | ▲               | ▲                 | ▬                |
| Memory overhead  | ▲               | ▲                 | ▼ (dup libs)     |
| Ease of updating | ▼               | ▲                 | ▲▲               |
| Crash isolation  | ▼               | ▲ (per-transport) | ▲                |
| Dev velocity     | ▲ (single lang) | ▲                 | ▼ (ABI juggling) |

5. Recommended Roadmap (Hybrid Option B)  
   ——————————————————  
   PHASE 1 – Minimal viable core client (2 weeks)  
   • Add `McpClientSvc` (core/services)  
    – JSON-RPC framing, request/response map, incremental decoder (simdjson or rapidjson SAX)  
    – Dependency-inject `ExecutorSvc` for async I/O.  
   • Implement `StdioTransportSvc` – spawn and manage lifecycle of servers declared in `mcp_servers.json` (dev-only file).  
   • Extend LlmFactory: detect OpenAI/Anthropic function_call or local JSON tag; pause/resume.  
   • ToolRegistry with simple `struct ToolDef {name, desc, schema}` and callback.

PHASE 2 – Security & Policy (3 weeks, overlaps Slice 2)  
• PolicyEngine rules: allow/deny tool by name, server path, arguments; surfaced in Preferences ▸ Plugins pane.  
• Diagnostics counters (`mcp.calls_total`, `mcp.latency_p95`, `mcp.errors.*`).  
• Seatbelt generator extended to stdio servers (network/file exceptions only for permitted dirs).

PHASE 3 – Shipping Tier-1 Servers (4 weeks)  
• Provide helper script `kai-mcp-bundle install filesystem brave git fetch sqlite`.  
• For Filesystem server: pre-configure allowed roots to `~/` and app sandbox container; user can whitelist extra paths.  
• UX: When first tool call occurs, show non-blocking notification “Filesystem server starting…”; stream result when ready.

PHASE 4 – Native dylib + JS/Wasm transport (Slice 3 timeframe)  
• `NativePluginTransportSvc` maps tool calls to in-process plugin C ABI for low-latency power tools (e.g. Scanner_macOS).  
• `JsRuntime` path: treat JS plugin that registers tools as an MCP server inside the same process (tool list = JS functions).

6. Key Implementation Notes  
   ——————————————————  
   • Re-use existing `ServiceRegistry` compile-time DAG: McpClientSvc depends on ExecutorSvc & EventBusSvc.  
   • All JSON strings passed to LLM should stay in the ArenaAllocator to avoid heap thrash.  
   • Use `std::chrono::steady_clock` for per-call timeout, tracked in Diagnostics.  
   • For streaming, adopt MCP’s convention: forward `partial` results as incremental assistant tokens (`<tool_result>` block).  
   • Secure temporary directories: spawn stdio servers inside `~/Library/Caches/MicroLauncher/mcp/` with `0700` perms.  
   • Code-sign stdio server binaries we ship; for `npx` servers, verify SHA-256 of tarball before first run (cache signature).

7. Trade-offs & Open Questions  
   ——————————————————  
   • **Transport abstraction leakage:** ensuring JSON IDs remain unique across transports; solution: prefix with transport id.  
   • **Function schema size:** OpenAI function call payload has 8 k-token limit; may need adaptive pruning if >30 tools.  
   • **User approval UX:** do we block LLM until user clicks “approve” for destructive tools, or allow auto-exec with audit log?  
   • **Server updates:** shipping node-based servers increases bundle size; may instead fetch on-demand after first launch.

8. Conclusion  
   ——————————————————  
   Adopting MCP will unlock a vast ecosystem of ready-made tools with minimal bespoke code. Option B achieves the right balance: we keep a performant, auditable core while allowing transports and server-launch logic to evolve independently. Starting with Filesystem, Git(hub), Brave Search and Sequential Thinking will demonstrate real user value and give us immediate parity with other MCP-enabled clients like Claude Desktop.

Next Steps (if you say “go”):

1. Create `src/core/services/mcp_client_service.{h,cpp}` scaffolding.
2. Add `src/core/transports/stdio_transport_service.{h,cpp}` with JSON-RPC pipe I/O.
3. Introduce `tool_registry.{h,cpp}` and wire into LlmFactory.
4. Script to bundle & codesign reference servers for dev builds.

Implemented initial MCP integration scaffolding (Go mode):

1. Added new service ID `kMcpClientService`
   • Updated enumeration & kServiceIdNames in `src/core/foundation/service_id.h`.

2. Declared compile-time dependencies  
   • Added trait for `kMcpClientService` (depends on Executor & EventBus) in `service_traits.h`.

3. Wired ServiceRegistry
   • Added dependency case in `registry.cpp`.

4. Introduced skeleton MCP client
   • New directory `src/core/mcp/` with:
   – `mcp_client_service.h`  
    – `mcp_client_service.cpp` (stub start/stop).

5. Build system
   • Added `mcp/mcp_client_service.cpp` to `src/core/CMakeLists.txt`.

6. Application bootstrap
   • Included header and registered `McpClientService` in `src/main.cpp`.

Compilation should succeed with placeholder service; future work can flesh out JSON-RPC transport and tool registry without touching core bindings.
