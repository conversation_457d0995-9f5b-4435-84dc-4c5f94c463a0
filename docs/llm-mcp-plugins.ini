
DEEP MODE — GAP ANALYSIS FOR LLM + MCP PLUG-INS  
(Reference commit: current `master` in /Users/<USER>/kai-agent, 2025-06-03 build)

────────────────────────────────────────────────────────
0. TL;<PERSON> (Executives & Tech Leads)
────────────────────────────────────────────────────────
• Core substrate (ServiceRegistry, RuntimeManagerSvc, Security) is **70 % there**.  
• LLM back-ends live **inside core**, not as isolated native plug-ins ⇒ breaks trust tiers, hot-patching, and memory-isolation goals.  
• MCP path is a **stub** (≤ 150 lines); none of the transport / JSON-RPC / ToolRegistry / policy plumbing exists.  
• RuntimeManager only supports `kNull` runtime; **no Native, JavaScriptC<PERSON>, Was<PERSON>, MCP server runtimes** implemented.  
• Security pipeline (Mask128, VerificationStore, SeatbeltVerifier) landed, but **ProbeCache wiring is missing** and `RuntimeManagerSvc` never calls it.  
• Diagnostics counters present, but no MetricSource concept ⇒ OTLP/Prom export can’t auto-discover caches/queues yet.  
• UI layer has no Preferences ▸ Plugins pane; plugin enable/disable events are partially wired but never surfaced.

────────────────────────────────────────────────────────
1. Current State Snapshot
────────────────────────────────────────────────────────
1-A  Runtime / Plug-in Layer  
    ✓ `runtime_manager_service.{h,cpp}`  – full discovery / dlopen / lifecycle wrapper **for NullRuntime only**.  
    ✓ `runtime_base.h` enum `RuntimeKind { kNull, kJavaScript, kWasmNative, kNativeXpc }`.  
    ✗ *Missing*: concrete classes `JavaScriptRuntime`, `WasmRuntime`, `NativeRuntime`, `McpRuntime`.  
    ✗ `KaiPluginInfo::runtime` is parsed but never acted upon except for `kNull`.

1-B  LLM Stack  
    ✓ Providers (`openai_model.cpp`, `anthropic_model.cpp` …) compile & unit-test.  
    ✗ Providers are **built into host binary**; no shared-lib boundary, allocator shim, or capability mask.  
    ✗ No exported C façade (`kai_plugin_get_info`, `kai_plugin_initialize` …) exists for `openai`, `llm_anthropic`.

1-C  MCP Path  
    ✓ Skeleton `McpClientService` starts/stops and bumps a diagnostic counter.  
    ✗ No `RuntimeTransport` concept, no JSON-RPC codec, no queue, no ToolRegistry, no watchdog.  
    ✗ `RuntimeManagerSvc` does not scan for MCP servers, so McpClientService never receives descriptors.

1-D  Security  
    ✓ `mask128.hh`, `verification_store.hh`, `seatbelt_verifier.hh/.mm`, `codesign_verify.mm` implemented.  
    ✓ `Sandbox` ObjC++ helper present.  
    ✗ `ProbeCache.sqlite` + FlatSnapshot storage not yet created.  
    ✗ `RuntimeManagerSvc`’s probe path calls `codesign_verify` but **skips VerificationStore & SeatbeltVerifier**.  
    ✗ PolicyEngineService header not found ⇒ capability enforcement is absent.

1-E  Memory / Allocator  
    ✓ `ArenaAllocatorSvc` + rpmalloc in tree; `plugin_heap_scope.h` bridges dlopened images.  
    ✗ Native plug-in allocator shim (`kai_plugin_alloc_shim.h`) not generated; current providers allocate via system `malloc`.

1-F  Observability  
    ✓ `DiagnosticsService` with counters; `DBG/ERR` macros widely used.  
    ✗ No MetricSource concept; caches/queues can’t self-register.  
    ✗ EventBus idle-CPU metric planned but not in code.

1-G  UI  
    ✗ No Objective-C++ `PluginsPane.mm`; no binding to EventBus `PluginScanCompleteEvent`.  
    ✗ No preferences toggles for plug-ins / capabilities.

────────────────────────────────────────────────────────
2. Gap Details & Impact
────────────────────────────────────────────────────────
2-A  Native Runtime • CRITICAL  
     Missing `NativeRuntime` block halts all LLM native plug-ins (`openai.dylib`, `llm_anthropic.dylib`, future embeddings, vector DBs).  
     Impact: providers must stay statically linked → larger binary, no hot-patch, no per-plug-in heap isolation.

2-B  JavaScriptCore & WasmEdge Runtime • CRITICAL  
     RuntimeManager can discover JS/Wasm files, but without `JsRuntime` / `WasmRuntime` classes nothing executes.  
     Impact: planned Community plug-ins (`sample_hello_js`, `sample_math_wasm`) and majority of third-party ecosystem unavailable.

2-C  MCP Discovery & Streaming • CRITICAL  
     McpClientService cannot enumerate or talk to servers.  
     Impact: zero “tool use” capability; LLM cannot call external tools.

2-D  Security Probe Path • HIGH  
     Seatbelt & VerificationStore compiled but never executed → codesign only check.  
     Impact: Hardened Runtime requirements unmet; capability denial path untested.

2-E  Provider Extraction & Shim • HIGH  
     OpenAI/Anthropic embedded ⇒ rebuild required for key rotation/bug-fix; breaks trust tier / update flow.  
     Impact: shipping fixes requires host update, not plug-in update.

2-F  ProbeCache & MetricSource • MEDIUM  
     Without AdaptiveCache & FlatSnapshot warm path, cold-start scan cost high and metrics incomplete.

2-G  UI Surfaces • LOW (but UX)  
     Preferences pane missing; no visibility for users / testers.

────────────────────────────────────────────────────────
3. Design Options & Trade-offs
────────────────────────────────────────────────────────
3-A  Native Runtime Implementation  
    Opt-1 Minimal: hard-code allocator shim + forward lifecycle directly to plug-in exports.  
         • + Fastest, low risk  
         • – Each plug-in must implement IService semantics itself  
    Opt-2 NativeRuntime class that wraps `kai_plugin_*` exports into an internal IService object (recommended).  
         • + Uniform across Null/JS/Wasm; EventBus hooks centralised  
         • + Enables per-plug-in heap via `ArenaAllocatorSvc::acquireHeap()`  
         • – Slightly more code (≈400 LOC)  
    Recommendation: **Opt-2** for consistency & future XPC helper reuse.

3-B  Provider Extraction Strategy  
    Opt-1 “Fat dylib” – build OpenAI/Anthropic provider code unchanged into `openai.dylib` & `llm_anthropic.dylib`.  
         • + Minimal refactor; link existing C++ sources into shared target.  
         • – Duplicates common HTTP / util code across dylibs (≈1.8 MB each)  
    Opt-2 “Thin shim” – keep heavy code in `libllm_core` static, dylib only exports registration function.  
         • + Reuse common code; tiny dylibs (≤30 KB)  
         • – Requires stable ABI for shared lib; increases coupling  
    Recommendation: **Opt-1 for Slice-2** (speed), revisit Thin-Shim in Slice-3 when Thin-LTO in place.

3-C  MCP Path  
    Opt-1 Implement StdioTransport + JSON-RPC codec first; postpone HTTP/WebSocket.  
         • + Unblocks Filesystem & Git reference servers quickly  
         • – Later refactor to generic TransportBase  
    Opt-2 Land `TransportBase<Derived>` type-erased wrapper from start.  
         • + Clean long-term; supports multi-transport metrics uniformly  
         • – Slightly steeper initial complexity (inline buffer, CRTP concepts)  
    Recommendation: **Opt-2** aligns with Slice-3 blueprint and avoids churn.

3-D  Security Probe Sequencing  
    Opt-1 Call `VerificationStore::verify()` synchronously during directory scan.  
         • + Simple, deterministic  
         • – Potential 100 ms+ stall on cold start  
    Opt-2 Fan-out to `ExecutorSvc` workers as blueprint suggests; VerificationStore uses AdaptiveCache shard.  
         • + Parallel ≈6× speed-up on 8-core  
         • – Needs QueueTraits update and Snapshot persistence early  
    Recommendation: **Opt-2** (parallel) — cold-start SLA 150 ms unlikely with synchronous path.

3-E  Metrics Autodiscovery  
    Opt-1 Hand-register every cache/queue with DiagnosticsService.  
         • + Trivial  
         • – Ongoing maintenance, risk of omissions  
    Opt-2 Introduce `MetricSource` concept (#include <util/metrics.hh>) and tag caches/queues via traits.  
         • + One code path for OTLP + Prom exporter; zero runtime registration.  
    Recommendation: **Opt-2**; compile-time enumeration works with C++23 fold-expressions.

────────────────────────────────────────────────────────
4. Proposed “Least-Regret” Implementation Path
────────────────────────────────────────────────────────
Stage-1 (5 – 7 days)  
  • Implement `NativeRuntime` (Opt-2) & allocator shim generator.  
  • Extract OpenAI & Anthropic providers into `plugins/openai`, `plugins/llm_anthropic` dylibs.  
  • RuntimeManager: handle `RuntimeKind::kNativeXpc` (load like Null + init).  
  • Wire VerificationStore + SeatbeltVerifier into probe path (synchronous for now).  
  • Add minimal Preferences pane listing plug-ins (read-only).

Stage-2 (7 – 10 days)  
  • Add `AdaptiveCache` + FlatSnapshot prototypes; persist ProbeCache.  
  • Parallelise directory scan & verification via `ExecutorSvc`.  
  • Finalise MetricSource; OTLP exporter enumerates caches.

Stage-3 (10 – 14 days)  
  • Land `TransportBase<Derived>` CRTP; implement `StdioTransport`.  
  • SIMDJSON varint-framed JSON-RPC codec; zero-alloc via `ArenaAllocatorSvc`.  
  • `McpRuntime` that launches servers (sub-process or pipe) and streams to `McpClientService`.  
  • ToolRegistry MPH snapshot; expose `tools/list` to LLM providers.  
  • Watchdog (steady_clock) + PolicyEngine first cut (deny net/exec by default).

Stage-4 (14 – 18 days)  
  • `JavaScriptRuntime` (JavaScriptCore) with 2-entry context pool.  
  • `WasmRuntime` (WasmEdge) allocator hooks.  
  • Seatbelt binary cache `.sb.bin`; VerificationStore snapshot sign/verify.  
  • Preferences pane enable/disable toggle (publish PluginStateChangeEvent).

────────────────────────────────────────────────────────
5. “Done” Checklist for LLM + MCP Readiness
────────────────────────────────────────────────────────
[ ] NativeRuntime loads & starts `llm_*` dylibs; per-plug-in rpmalloc heap.  
[ ] Providers register in `start()` and deregister in `stop()` to avoid stale pointers.  
[ ] Add `KaiPluginInfo::capabilities` bitmap — LLM plug-ins set `JSON_MODE`, `TOOL_USE`.  
[ ] OpenAI and Anthropic API keys read via ConfigService not env var.  
[ ] ToolRegistry snapshot delivered to LLM prompt builder (for function-calling).  
[ ] McpClientService supports streaming partial chunks to AgentLoop.  
[ ] PolicyEngineService returns `KaiError::CapabilityDenied` when MCP tool requests blocked caps.  
[ ] ProbeCache warm-start hit-rate ≥ 95 %.  
[ ] Diagnostics exporter emits `llm.calls_total`, `mcp.latency_ms_bucket`.  
[ ] Plugins pane lists status + capability mask; toggle working.  
[ ] All hot loops show **zero rpmalloc allocations** under ASan guard.  
[ ] Release build passes `codesign --verify` + notarisation.  

────────────────────────────────────────────────────────
6. Risks & Mitigations
────────────────────────────────────────────────────────
R1 Template/code bloat from CRTP transports → compile with Thin-LTO OFF initially; enable after Slice-3.  
R2 API drift between host headers and plug-ins → lock `abi/abi.h` version enum, add static_assert in `abi_check.cpp`.  
R3 Seatbelt profile false negatives on older macOS → keep XPC helper fallback behind `KAI_ENABLE_SB_HELPER`.  
R4 Parallel scan race conditions → use per-directory `std::atomic_flag` or executor task barrier.  
R5 Provider extraction regressions → integration tests hitting live OpenAI sandbox; CI can mock via WireMock.

────────────────────────────────────────────────────────
7. Next Immediate Actions (Deep-mode Summary)
────────────────────────────────────────────────────────
1. Convert existing LLM provider C++ files into shared-lib targets.  
2. Introduce `NativeRuntime` class & update RuntimeManager switch-case.  
3. Hook VerificationStore + SeatbeltVerifier into probe loop; print verdicts.  
4. Draft C API shim (`kai_plugin_get_info`, etc.) for provider dylibs.  
5. Add unit tests: `native_runtime_lifecycle_test`, `llm_plugin_registration_test`.

Once these land, reassess cold-start timings and memory footprint, then proceed with MCP transport work.
