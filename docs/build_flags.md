# Kai Build Flags

This document enumerates all project-level CMake options that influence the
build and runtime feature set. All options default to the most conservative
configuration and can be toggled at configure time, e.g.

```bash
cmake -G Ninja .. -DKAI_ENABLE_MUX_QUEUE=ON -DKAI_ALLOCATOR=mimalloc
```

| Flag                           | Default    | Purpose                                                                                                                                                                   |
| ------------------------------ | ---------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| `KAI_ENABLE_VPATCH`            | OFF        | Enables hot-patch verifier table loader (Slice-2 security).                                                                                                               |
| `KAI_ENABLE_MUX_QUEUE`         | OFF        | Builds lock-free `MuxQueue` backend and related traits.                                                                                                                   |
| `KAI_ENABLE_FLATSNAPSHOT_SIGN` | OFF        | Adds Ed25519 signature footer to FlatSnapshot files; links libsodium; host verifies signature.                                                                            |
| `KAI_ENABLE_THINLTO`           | OFF        | Enables Clang Thin Link-Time Optimisation (`-flto=thin`) for size/performance tuning. Incompatible with sanitiser builds.                                                 |
| `KAI_ALLOCATOR`                | `rpmalloc` | Global allocator backend: `rpmalloc` or `mimalloc`.                                                                                                                       |
| `KAI_USE_LMDB_PROBECACHE`      | OFF        | Replaces in-memory ProbeCache L2 with LMDB key-value store.                                                                                                               |
| `ENABLE_ASAN`                  | OFF        | Build with AddressSanitizer instrumentation. Mutually exclusive with `ENABLE_TSAN`.                                                                                       |
| `ENABLE_TSAN`                  | OFF        | Build with ThreadSanitizer instrumentation.                                                                                                                               |
| `KAI_BUILD_SHA`                | auto       | **Read-only.** Injected at configure time; contains the first 20 characters of the current Git commit. Embedded into FlatSnapshot headers and diagnostics for provenance. |

All boolean flags are exposed to code as numeric macros `0/1`, allowing safe
`#if KAI_ENABLE_MUX_QUEUE` guards even with `-Wundef`.
