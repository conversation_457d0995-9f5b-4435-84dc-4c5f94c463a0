
DEEP MODE – end-to-end implementation blueprint for Slice-2  
(“Comprehensive & detailed” means: concrete file tree, key API signatures, threading & cache considerations, CMake glue, test surfaces, CI hooks, and risk counter-measures — all refined with the improvements we analysed earlier.)

────────────────────────────────────────────────────────────────────────
0. OVERVIEW – WHAT SUCCESS LOOKS LIKE
────────────────────────────────────────────────────────────────────────
• All Acceptance Criteria AC-1 … AC-9 satisfied, gates pass in CI matrix {Rel, <PERSON><PERSON>, T<PERSON><PERSON>}.  
• Cold start ≤ 125 ms, deny lookup ≤ 5 ns, ProbeCache ≥ 95 % hit, zero heap allocs on hot path.  
• Security flow canonicalised via new thin functional façade.  
• Foundation primitives (enum_mask, ring buffer, epoch arena) stand alone, test-driven.  
• Slice-2 drops behind one PR series (`feature/slice2`) and merges clean.

────────────────────────────────────────────────────────────────────────
1. REFINED FILE-TREE
────────────────────────────────────────────────────────────────────────
core/  
│ foundation/  
│ ├─ enum_mask.hh                  (NEW)  
│ ├─ capability_mask.hh            (wraps enum_mask)  
│ └─ …  
│ async/  
│ ├─ ring_buffer.hh                (cache-aligned indices)  
│ ├─ ring_buffer_pool.hh  
│ └─ epoch_arena.hh  
│ storage/  
│ ├─ flat_snapshot.hh              (PHF reader)  
│ ├─ flat_snapshot_traits.hh  
│ └─ …  
│ security/  
│ ├─ seatbelt_profiles.inc         (constexpr blobs)  
│ ├─ code_signing.hh/.cpp  
│ ├─ probe_cache.hh/.cpp  
│ ├─ policy_engine_service.hh/.cpp  
│ └─ security_facade.hh/.cpp       (NEW thin orchestration)  
runtime/  
│ ├─ fs_events_router.hh/.mm  
│ ├─ runtime_scanner.hh/.cpp  
│ └─ runtime_manager_service.hh/.cpp  
ui/  
│ panels/PluginsPane.mm  
inst/  
│ ├─ ring_buffer_inst.cpp  
│ └─ flat_snapshot_inst.cpp  
tools/  
│ kai-phf-tool/…  
│ common/cli_flags.hh              (shared CLI flags)  
fuzz/  
│ snapshot_fuzzer.cpp  
│ policy_yaml_fuzzer.cpp  
benchmarks/…  
test/…  
cmake/  
│ AddKAI_Snapshot.cmake            (macro)  
docs/…  
CMakeLists.txt (root + sub-directories)

────────────────────────────────────────────────────────────────────────
2. FOUNDATION LAYER DETAILS
────────────────────────────────────────────────────────────────────────
2.1 enum_mask.hh (≈40 LOC)  
```cpp
template <typename Enum, typename Storage = std::underlying_type_t<Enum>>
class EnumMask {
 public:
  constexpr EnumMask() = default;
  constexpr explicit EnumMask(Storage raw) : bits_{raw} {}
  constexpr bool contains(Enum e) const noexcept {
    return bits_ & (Storage{1} << static_cast<Storage>(e));
  }
  constexpr EnumMask& set(Enum e) noexcept {
    bits_ |= (Storage{1} << static_cast<Storage>(e));
    return *this;
  }
  [[nodiscard]] constexpr Storage raw() const noexcept { return bits_; }
  friend constexpr EnumMask operator|(EnumMask a, EnumMask b) noexcept {
    return EnumMask{a.bits_ | b.bits_};
  }
 private:
  Storage bits_{};
};
```
2.2 capability_mask.hh  
```cpp
enum class Capability : uint16_t { kFsRead = 0, kFsWrite, kNetAccess, kAllowJit, kMax_ };
using CapabilityMask = EnumMask<Capability, uint16_t>;

constexpr CapabilityMask makeMask(std::initializer_list<Capability> caps) {
  CapabilityMask m;
  for (auto c : caps) m.set(c);
  return m;
}
```
2.3 ring_buffer.hh  
• Template params: `typename T, size_t kCap, typename Index = uint32_t`.  
• `alignas(64) std::atomic<Index> head_{0}; alignas(64) std::atomic<Index> tail_{0};`  
• `static_assert(std::has_single_bit(kCap));`  
• Push/Pop ops use acquire-release.  
• Provide `constexpr size_t capacity()`.  

2.4 ring_buffer_pool.hh  
• `static constexpr size_t kShards = 8;`  
• `thread_local size_t shard_id_{mix64(pthread_self()) & (kShards-1)};`  
• Shard array `std::array<RingBuffer<T,kCap>, kShards> shards_;`  

2.5 epoch_arena.hh  
• Page struct `alignas(64) Page { uint8_t data[PageSize]; }`.  
• TLS freelist vector of pages.  
• `alloc(n)` — if n ≤ PageSize, carve from current; else fall back to `mmap`.  
• `free` = no-op. Destructor of TLS freelist pushes generation into retire list.  
• Debug-build ABA counter `uint32_t generation_`.  

────────────────────────────────────────────────────────────────────────
3. STORAGE & SNAPSHOT
────────────────────────────────────────────────────────────────────────
3.1 flat_snapshot.hh  
Header layout (128 B):  
```
magic[4] | version[2] | crc32[4] | bucket_count[4] | phf_seed[16] | reserved…
```
`Expected<const Bucket&,KaiError> find(const Key&) const`.  
Buckets aligned to 4 K boundary (`constexpr size_t kDataStartAlign = 4096`).  
On construction: `mmap(MAP_PRIVATE|MAP_POPULATE)` then `std::launder` pointers.  

3.2 kai-phf-tool  
CLI flags:  
`--input`, `--output`, `--validate-only`, `--big-endian`.  
Pipeline: parse JSON → build PHF → emit snapshot.bin + `flat_snapshot_generated.hh`.  
CMake macro `add_kai_snapshot(NAME bin hdr)` copies artefacts into build tree and links hdr.  

────────────────────────────────────────────────────────────────────────
4. SECURITY LAYER
────────────────────────────────────────────────────────────────────────
4.1 seatbelt_profiles.inc  
```cpp
inline constexpr std::array<uint8_t,1234> kDefaultProfile = {
#include "seatbelt/default_profile.inc"
};
```
4.2 code_signing.hh/.cpp  
```cpp
Expected<void,KaiError> verifyCdhash(const fs::path&);    // fast path
Expected<std::array<uint8_t,20>,KaiError> getCdhash(const fs::path&);
```
4.3 probe_cache.hh/.cpp  
2-way set associative, 64 lines.  
```
struct alignas(64) Line { uint64_t key; Verdict verdict; };
std::array<Line,128> l1_;
```
Miss path → mmap WAL snapshot.  

4.4 policy_engine_service.hh/.cpp  
Public API:  
`Expected<Verdict,KaiError> decide(const Key&, CapabilityMask);`  

4.5 security_facade.hh/.cpp (NEW)  
```cpp
struct VerificationParams { CapabilityMask requested_caps; bool allow_jit=false; };
Expected<Verdict,KaiError> verifyPlugin(const fs::path&, const VerificationParams&) noexcept;
```
Flow: ProbeCache → code-sign (cached) → capability superset → seatbelt apply → cache verdict.  

────────────────────────────────────────────────────────────────────────
5. RUNTIME PIPELINE
────────────────────────────────────────────────────────────────────────
5.1 fs_events_router.hh/.mm  
• Objective-C++ using FSEvents.  
• Adaptive delay: token-bucket spin <50 µs else `os_unfair_lock` wait.  

5.2 runtime_scanner.hh/.cpp  
• Worker threads = `std::min(std::thread::hardware_concurrency()/2,6u)`.  
• For each path event: call `security::verifyPlugin()`, write cdhash+verdict snapshot.  

5.3 runtime_manager_service.hh/.cpp  
Publishes `Span<Event>` with ring buffer of recent verdicts for UI.

────────────────────────────────────────────────────────────────────────
6. UI & TOOLING
────────────────────────────────────────────────────────────────────────
PluginsPane.mm  
• Uses `NSTableViewDiffableDataSource`.  
• Renders capabilities via `capabilityToStringList(mask)` helper in `core/foundation`.  

tools/common/cli_flags.hh  
• Posix-style parser reused by future CLIs.

────────────────────────────────────────────────────────────────────────
7. CMAKE & BUILD INTEGRATION
────────────────────────────────────────────────────────────────────────
7.1 Pre-compiled header `kai_pch.hh` (system + std + project root).  
7.2 `enum_mask` and other headers added to umbrella `core` target.  
7.3 `add_kai_snapshot()` macro:  
```
function(add_kai_snapshot NAME BIN HDR)
  add_custom_command(… kai-phf-tool …)
  add_custom_target(${NAME}_snapshot ALL DEPENDS ${BIN})
  target_include_directories(core PUBLIC ${CMAKE_BINARY_DIR}/snapshots)
  target_sources(core PRIVATE ${HDR})
endfunction()
```
7.4 `ENABLE_CCACHE` option toggles compiler launcher.  

────────────────────────────────────────────────────────────────────────
8. TEST MATRIX
────────────────────────────────────────────────────────────────────────
test/capability_mask_test.cpp – static_assert + runtime.  
benchmarks/ring_buffer_bench.cpp – ≤ 50 ns P50 push/pop.  
test/epoch_arena_leak_test.cpp – 1 M allocs LSAN clean.  
test/flat_snapshot_crc_test.cpp – load golden, check CRC/SHA.  
security_facade_test.cpp – matrix of {valid, bad cdhash, seatbelt fail}.  
probe_cache_hitrate_bench.cpp – adversarial keys ≥ 95 % hit.  
cold_start_bench.cpp – measure app launch to ready.  
