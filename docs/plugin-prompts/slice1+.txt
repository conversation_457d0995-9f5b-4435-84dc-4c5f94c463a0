Deep Mode – Prompt-Design Playbook  
“Cursor-o3 can do the coding; we must feed it precise, bounded, security-aware instructions.”  
Below is a ready-made prompt kit—one prompt per Slice-1 gap—plus a meta-template you can reuse for future work. Each prompt is written to satisfy our custom instructions (ObjC++ only for UI, C++23, no `rm -rf`, etc.) and to minimise conversational round-trips.

──────────────────────────────────────────────────────────────────────────── 
0. Meta-template for any new task
────────────────────────────────────────────────────────────────────────────

```
<user_query>
go
Goal
  • High-level outcome in one bullet.

Scope
  • Which directories/files to touch, and which to leave untouched.
  • Hard constraints (C++23 only, no Swift/Storyboards, follow Kai rules).

Acceptance
  • Bullet list of measurable outcomes or unit-tests that must pass.

Implementation hints
  • (Optional) algorithm choices, third-party libs, naming guidelines.

Step-wise plan
  1. …
  2. …
</user_query>
```

Put `go` on the first line to flip o3 into Go-mode immediately.

────────────────────────────────────────────────────────────────────────────
1. YAML-driven service generator (`kai-service-gen`)
────────────────────────────────────────────────────────────────────────────

```
<user_query>
go
Goal
  • Add new CLI tool `src/tools/kai-service-gen/` that reads `services.yaml`
    and auto-generates:
        – src/core/foundation/service_id.h
        – src/core/foundation/service_traits.h
        – src/core/foundation/service_topology.h  (constexpr, topo-sorted)
        – src/core/foundation/service_name_phf.h  (perfect-hash map)
Scope
  • Only edit within `src/tools/`, `src/core/foundation/`, tests & CMake.
  • Must not break existing hand-written headers until generator is verified;
    emit generated files to `build/gen/` first.
  • Use C++23 + `inja` + `yaml-cpp`; keep tool header-only where possible.
Acceptance
  • `./build.sh && ctest -R service_gen` passes.
  • New unit-test verifies cycle detection for invalid YAML.
  • ServiceRegistry builds against generated headers when
    `-DKAI_SERVICE_GEN_USE_OUTPUT=ON`.
Step-wise plan
  1. Scaffold CMake target.
  2. Parse YAML, validate DAG, run Kahn topo at compile-time for cycle-check.
  3. Emit headers with banner “AUTO-GENERATED”.
  4. Add Catch2 tests under `src/tests/tools/service_gen/`.
Implementation hints
  • For perfect-hash use `bbhash` (header-only, small).
  • Topology header exports `constexpr std::array<ServiceId, N> kSorted;`.
</user_query>
```

──────────────────────────────────────────────────────────────────────────── 
2. Replace runtime topo-sort with constexpr array
────────────────────────────────────────────────────────────────────────────

```
<user_query>
go
Goal
  • Refactor ServiceRegistry::startAll() to iterate over the generated
    `kSortedServiceOrder` array; delete in-degree / dynamic Kahn logic.

Scope
  • Only touch `src/core/foundation/registry.*` and adapt tests.
  • Preserve debug logging and error-paths.

Acceptance
  • All existing unit-tests still pass.
  • New micro-benchmark shows ≤ 5 µs start-order overhead.

Step-wise plan
  1. Include `service_topology.h`.
  2. Remove runtime topo-sort code path under `#if !defined(KAI_USE_RUNTIME_TOPO)`.
  3. Adjust tests for deterministic order.
</user_query>
```

──────────────────────────────────────────────────────────────────────────── 
3. Blocking queues + idle-CPU metric
────────────────────────────────────────────────────────────────────────────

```
<user_query>
go
Goal
  • Replace busy-spin loops in ExecutorService and EventBusService with a
    blocking wait (`BlockingConcurrentQueue` or cv-backed ring).
  • Emit gauge `eventbus.idle_cpu_pct`.

Scope
  • Edit only `src/core/async/` and `src/core/events/`.
  • Must compile under existing `moodycamel` dependency—add `blockingconcurrentqueue.h`.

Acceptance
  • Unit-test: dispatcher thread shows <0.1 % CPU after 1 s idle (mocked).
  • Counter `eventbus.queue_overflow` behaviour unchanged.

Implementation hints
  • Use `wait_dequeue_timed(task, 1ms)`.
  • Idle percentage = sleepTicks / (sleepTicks + activeTicks).
</user_query>
```

──────────────────────────────────────────────────────────────────────────── 
4. Diagnostics micro-bench + SpanGuard
────────────────────────────────────────────────────────────────────────────

```
<user_query>
go
Goal
  • Add `SpanGuard` RAII timer (util/span_guard.h) that records elapsed µs
    into Diagnostics counters.
  • Wrap each IService::start() call inside ServiceRegistry with SpanGuard.
  • Provide benchmark `benchmarks/core/service_start_bench.cpp`.

Scope
  • `src/core/util/`, `src/core/foundation/`, `benchmarks/` only.

Acceptance
  • New counter `service.start_us.<Name>` appears in snapshot.
  • Benchmark reports total start time <50 ms on CI setting (use `--benchmark_repetitions=5`).
</user_query>
```

──────────────────────────────────────────────────────────────────────────── 
5. RuntimeManagerSvc (Null-only for Slice-1)
────────────────────────────────────────────────────────────────────────────

```
<user_query>
go
Goal
  • Introduce `src/core/plugins/runtime_manager_service.{h,cpp}` merging
    existing probe from PluginManagerSvc and instantiating `NullRuntime`
    for every discovered plugin.
  • Expose public API `KaiExpected<void> loadPlugins()`; remove
    PluginManagerSvc from ServiceRegistry when complete.

Scope
  • Edit `src/core/plugins/`, update ServiceId enum & traits, tests.

Acceptance
  • `sample_null_plugin` still detected.
  • Diagnostics counter `plugins.loaded_total` increments.

Implementation hints
  • Keep per-plugin heap logic unchanged.
</user_query>
```

──────────────────────────────────────────────────────────────────────────── 
6. Minimal seatbelt enforcement
────────────────────────────────────────────────────────────────────────────

```
<user_query>
go
Goal
  • After successful dlopen in RuntimeManagerSvc, call
    `sandbox_apply_profile(profile_path)` on macOS
    (wrapper around `sandbox_init_with_parameters`).
  • Fail plugin load with `KaiError::CapabilityDenied` on error.

Scope
  • New file `src/core/security/sandbox.h|cpp`.
  • macOS-only code under `#ifdef __APPLE__`.

Acceptance
  • Unit-test uses dummy `.sb` profile; load fails when profile denies `network-outbound`.

Implementation hints
  • Use weak-link to avoid hard dependency on private API.
</user_query>
```

──────────────────────────────────────────────────────────────────────────── 
7. Codesign verification (real)
────────────────────────────────────────────────────────────────────────────

```
<user_query>
go
Goal
  • Implement `verifyCodeSignature()` using SecStaticCode APIs when
    `KAI_DISABLE_CODESIGN_VERIFY` is OFF.

Scope
  • Only `src/core/security/`.

Acceptance
  • New Catch2 test `codesign_verify_test.cpp` signs a temp binary and
    asserts success/failure.

Hints
  • Runtime-link Security.framework; fallback stub on non-Apple hosts.
</user_query>
```

──────────────────────────────────────────────────────────────────────────── 
8. CRTP `ServiceBase<Derived, Deps…>`
────────────────────────────────────────────────────────────────────────────

```
<user_query>
go
Goal
  • Create `ServiceBase.h` that stores `Deps&...` in tuple and auto-registers
    into ServiceRegistry via ctor injection.

Scope
  • Foundation layer only; refactor ArenaAllocatorSvc & ExecutorSvc
    as PoC consumers.

Acceptance
  • Build passes; compile-time error if dep missing.

Hints
  • Provide `get<Dep>()` accessor.
</user_query>
```

──────────────────────────────────────────────────────────────────────────── 
9. `KAI_VERIFY_ZERO_ALLOC` guard + allocator shim
────────────────────────────────────────────────────────────────────────────

```
<user_query>
go
Goal
  • Add header `src/core/memory/zero_alloc_guard.h` with macro
    `KAI_VERIFY_ZERO_ALLOC(Lambda)` that asserts rpmalloc stats unchanged.
  • Generate `kai_plugin_alloc_shim.h` (simple wrapper) during CMake
    configure and automatically `target_include` it for every plugin.

Scope
  • Memory layer + CMake.

Acceptance
  • New unit-test passes when lambda allocates 0 bytes, fails otherwise.
</user_query>
```

──────────────────────────────────────────────────────────────────────────── 
10. Perfect-hash name↔id map
────────────────────────────────────────────────────────────────────────────

```
<user_query>
go
Goal
  • Using bbhash, generate `service_name_phf.h` mapping string→ServiceId
    in O(1); replace linear `fromString()`.

Scope
  • Generated header only; keep fallback path under `#ifdef KAI_PHF_DISABLED`.

Acceptance
  • Unit bench shows ≤40 ns lookup vs previous ~250 ns.
</user_query>
```

──────────────────────────────────────────────────────────────────────────── 
11. Benchmarks & KPIs
────────────────────────────────────────────────────────────────────────────

```
<user_query>
go
Goal
  • Add Google Benchmark target `benchmarks/eventbus_throughput.cpp`
    measuring ≥10k msgs/s and reporting idle CPU.

Scope
  • Only `benchmarks/`, CMake.

Acceptance
  • CI greps for “EventBus throughput:” ≥10000 lines/s.
</user_query>
```

────────────────────────────────────────────────────────────────────────────
How to use this kit
────────────────────────────────────────────────────────────────────────────

1. Copy one prompt block at a time into the Cursor chat.
2. Keep “go” as first word to flip us into Go-mode; otherwise we’ll stay in Deep.
3. After each task completes, type `deep` if you want a post-mortem or next-gap discussion.

This ensures all Slice-1 gaps are addressed methodically while honouring our security & style constraints.
