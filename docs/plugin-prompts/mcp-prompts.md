# MCP Integration – Prompt Catalogue

> High-leverage prompts for Claude / GPT-4 to accelerate development of <PERSON>'s MCP stack. Use these during pair-programming or when generating code automatically. All prompts assume the **Kai codebase & conventions** (C++20 core, Obj-C++ UI, CMake, Hardened Runtime) and the **Model Context Protocol 2025-03-26** spec.

---

## 1. JSON-RPC Stream Layer

```
You are an expert in high-performance C++ networking.
Implement a **header-only** `JsonRpcStream` that:
1. Parses incremental UTF-8 bytes into JSON-RPC 2.0 Request / Response / Notification structs using simdjson SAX API.
2. Supports out-of-order responses, maps id→promise.
3. Emits `onRequest`, `onNotification`, `onError` callbacks via `std::function`.
4. Zero dynamic allocations; all buffers come from `ArenaAllocatorSvc` (Kai API).
5. Must compile under C++20 (`-std=c++20`) and respect 100-column limit.
Return: *json_rpc_stream.h* only.
```

---

## 2. Stdio Transport Service

```
Context: Kai uses `ExecutorService` for async IO.
Goal: Add `StdioTransportSvc` (`IService`) that spawns an MCP server binary, wires pipes, and drives `JsonRpcStream`.
Instructions:
1. Constructor args: `std::filesystem::path exe`, `std::vector<std::string> args`, env map, pointer to `ExecutorService`.
2. Use `posix_spawnp` (macOS) with `posix_spawn_file_actions` to set up `stdout`,`stdin` pipes; FD must be `O_CLOEXEC`.
3. Read bytes on dedicated executor thread, forward to `JsonRpcStream::feed()`.
4. Provide `call(const std::string& method, nlohmann::json params)` → `KaiExpected<nlohmann::json>` future.
5. On `JsonRpcStream` request callback, bridge to internal handler lambda (for server-initiated messages).
6. On `stop()` ensure process terminated nicely, kill after timeout.
Produce: *stdio_transport_service.{h,cpp}* with full implementation + minimal tests.
```

---

## 3. Tool Discovery

```
Prompt:
"Given the MCP spec, implement `ToolRegistry` that lazily caches tool definitions per server.  It should:
• Store struct ToolDef { std::string name; std::string description; nlohmann::json schema; TransportHandle* transport; }.
• Provide `list()` → span of ToolDef sorted by name; `find(name)`; `call(name, params)`.
• Emit `ToolRegisteredEvent` via EventBusService.
• Use `ArenaAllocatorSvc` for storage (vector<InlinedVector<…>> acceptable).
Ensure O(1) lookup by name (unordered")."
```

---

## 4. Agent Loop Integration

```
System prompt for refactoring the LlmFactory pipeline:
"Instrument the streaming generation loop so that when the model produces a JSON object with keys `"tool"` and `"args"`, generation pauses and a coroutine calls `ToolRegistry::call()`.  The tool result should be appended as a system message `Tool result:` before generation resumes.  Ensure thread-safe coordination between `ExecutorService` queues and UI updates.  Provide code patches only for files needing changes; follow Kai logging macros."
```

---

## 5. Policy Enforcement

```
"Implement capability gating in `PolicyEngineService` for MCP tool calls:
• Add `bool isAllowed(const ToolDef&, const UserContext&)`.
• Deny by default; allow based on JSON policy file in `~/Library/Application Support/Kai/policy.json`.
• Return `KaiError::CapabilityDenied` on violation; McpClientService must convert to JSON-RPC error -32001 per spec.
Include unit tests (Catch2) covering allow, deny, missing policy.
```

---

## 6. Seatbelt Profile Generator

```
"Write a CLI `kai-seatbelt-server` that takes a `manifest.toml` and emits a macOS sandbox profile suitable for the stdio MCP server.  Rules:
• Network denied unless `manifest.sandbox.network` has entries.
• FS read-only paths from `manifest.sandbox.filesystem`.
• No exec/jit unless explicitly set.
Output file named `<id>.sb` next to manifest.  Use Objective-C++ for macOS specific APIs, pure C++ elsewhere."
```

---

## 7. Packaging Tasks

```
"Create a CMake `ExternalProject_Add` block that downloads specific version of `@modelcontextprotocol/server-filesystem` with SHA-256 verification, installs to `${CMAKE_RUNTIME_OUTPUT_DIRECTORY}/mcp/`, and codesigns executable in post-build step (use `codesign --sign - --timestamp`).  Provide instructions for adding more servers via `add_mcp_server(name,url,sha256)`."
```

---

## 8. Developer Docs Prompt

```
"Generate markdown section for *docs/mcp_implementation.md* titled 'Writing Custom MCP Servers for Kai'.  Include: prerequisites, security considerations, manifest schema, local testing with `kai --mcp-dev`, and submission checklist."
```

---

## Usage Tips

-   Prefix each prompt with the relevant file path when asking the LLM; our tooling auto-creates diffs.
-   Keep prompts short but explicit on constraints (allocator, logging, style).
-   Always reference Kai macros (`DBG/ERR`) and error types (`KaiExpected`).

_Last updated: 2025-05-27_
