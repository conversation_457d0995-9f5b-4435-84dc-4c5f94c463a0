# MicroLauncher – Slice 2 Prompt Pack

Below is a ready-to-use set of prompts for driving an LLM (e.g. ChatGPT, Copilot) to implement **Slice 2 – Security** of the Kai plugin system.

---

## Prompt 0 – Slice-2 Contract Block (send **once per session**)

```text
You are a Go-mode AI generating C++20 / Objective-C++ code for the Kai project (native macOS app).

HARD RULES
1. Languages: Core = C++20; UI glue = Objective-C++ (.mm). No Swift/SwiftUI.
2. Build: CMake + Ninja (`./build.sh`) – add targets where needed.
3. Style: Google C++, 4-space indent, 100-column limit, clang-format enforced.
4. Logging: DBG/ERR macros only (src/core/util/debug.h).
5. Memory: no malloc/new directly; use ArenaAllocatorSvc or stack. Avoid dynamic dispatch unless justified.
6. Security: hardened runtime, no system/popen. Follow capability gating & seatbelt model.
7. Each edit must compile **incrementally** and pass `./build.sh && ./tests.sh`.
8. After code edits, emit a summary of files touched and WAIT for my review.

REFERENCE DOCS (read-only, do not modify)
• plugin-todo.md – slice timeline & acceptance criteria.
• core/foundation/service.h – IService base.
• capability256.h – generated enum (slice 1 output).

END CONTRACT
```

---

Below, each deliverable is packaged as a **leaf prompt**. Send **Prompt 0** first, then exactly one leaf prompt. After the AI produces the code, review the diff, run the tests, and only then proceed to the next leaf.

---

## Prompt 1 – capability_helpers.h

```text
### Task: Header-only capability helpers

1. Create `src/core/foundation/capability_helpers.h` (header-only).
2. Functions required (constexpr where possible):
   • has(mask, cap)         → bool
   • add(mask, cap)         → Capability256Mask
   • remove(mask, cap)      → Capability256Mask
   • to_string(cap)         → const char*
3. Accept & return the opaque `Capability256Mask` struct generated by capability256.h.
4. No heap allocations; use bit operations only.
5. Unit tests: `tests/capability_helpers_test.cpp` covering add/remove/has.
6. Update CMake (`core/CMakeLists.txt`, `tests/CMakeLists.txt`).
7. clang-format.

WAIT for my review
```

---

## Prompt 2 – PolicyEngineService

```text
### Task: Implement PolicyEngineService (hard-deny default)

1. Files:
   • `src/core/services/policy_engine_service.{h,cpp}`
   • `tests/policy_engine_service_test.cpp`
2. IService phases: initialize(reg), start(), stop().
3. Public API:
   Expected<void, KaiError> evaluate(const HostOp& op, const Capability256Mask& caps).
4. Default policy = DENY (return KaiError::CapabilityDenied) unless `--dry-run` flag set.
5. Accept config via cmd-line flag parsing already in Slice-1 DiagnosticsService (see flag registry pattern).
6. No global static state; store flags in member vars.
7. Tests:  coverage for allow/deny/dry-run.
8. Update ServiceRegistry DAG (service_traits.h) – depends only on DiagnosticsService.

WAIT for my review
```

---

## Prompt 3 – Seatbelt Enforcement

```text
### Task: Integrate seatbelt profile enforcement

1. Hook point: after plugin manifest parsed but before code load (PluginManagerService).
2. Implement `SeatbeltEnforcer` helper in `src/core/security/seatbelt_enforcer.{h,mm}` (ObjC++ needed for macOS sandbox API).
3. Call `sandbox_init_with_parameters()` in Debug/Release; honour env `KAI_SB_DISABLE` ONLY in Debug.
4. Generate profile via existing `kai-seatbelt-gen` CLI (already built) and feed to enforcer.
5. On failure, return KaiError::SandboxInitFailed.
6. Add micro-benchmark: enforcer init < 2 ms.
7. Tests: simulate profile, assert denial disables network access via dummy syscall (use `socket(AF_INET,…)` expect EPERM).

WAIT for my review
```

---

## Prompt 4 – JsRuntime & JSContextPool

```text
### Task: Implement JsRuntime derived from RuntimeBase

1. Create:
   • `src/core/js/js_runtime.{h,mm}`
   • `src/core/js/js_context_pool.{h,mm}`
2. RuntimeBase already defined in Slice-1 – derive and override virtuals.
3. JSContextPool:
   • LRU capacity = 2 (constexpr kPoolSize = 2).
   • Use std::array<JSContextRef, kPoolSize>, std::bitset for occupancy.
4. Public API:
   Expected<JSContextRef, KaiError> acquire();
   void release(JSContextRef ctx);
5. Expose `executeScript(plugin_id, script_src)` function – returns KaiError on exception.
6. Integrate into ServiceRegistry as JsEngineService, depends on ArenaAllocatorSvc.
7. Unit tests: acquire/release stress loop 10 k iterations; exec simple "1+1".
8. Memory: rpmalloc hooks for JSC, no new/malloc.
9. Bench target: JS round-trip ≤ 5 ms (p95) – micro-bench in `benchmarks/js_roundtrip.cpp`.

WAIT for my review
```

---

## Prompt 5 – WasmEdge Experimental Flag

```text
### Task: Add experimental WasmEdge runtime path

1. Guard all code with `#if KAI_USE_WASM_EDGE`.
2. Wrap WasmEdge C++ API inside `src/core/wasm/wasm_runtime_wedge.{h,cpp}` sharing interface with existing RuntimeBase.
3. CMake option `-DKAI_USE_WASM_EDGE=ON` must propagate to compile definitions.
4. Update ServiceRegistry to instantiate either WasmEdge or current Wasmtime runtime depending on flag.
5. Provide minimal integration test that loads sample_math.wasm and calls `add(2,3) == 5`.
6. Docs: update README snippet under "Build Flags".

WAIT for my review
```

---

## Prompt 6 – Codesign Perms Check & Manifest Lint

```text
### Task: Codesign owner UID + perms check; manifest lint tool

A. Loader Check
1. In `PluginManagerService::verifySignature()`, add:
   • owner UID == current uid
   • file mode == 0700 (file) / 0755 (dir)
2. Use <sys/stat.h>, no Objective-C.

B. Manifest Lint CLI
1. New tool: `kai-manifest-lint.cpp` in `tools/` (stand-alone).
2. Validate against manifest.schema.json (nlohmann/json + json-schema-validator header-only).
3. Check: required fields, min_host_version ≤ host abi, declared capabilities ≤ 256 mask.
4. Exit code 0 = OK, 1 = error; print descriptive ERR lines.

WAIT for my review
```
