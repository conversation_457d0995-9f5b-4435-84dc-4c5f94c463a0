# MicroLauncher – Slice 1 Prompt Pack

Below is a ready-to-use set of prompts for driving an LLM (e.g. ChatGPT, Copilot) to implement **Slice 1 – Bootstrap** of the Kai plugin system.

---

## Prompt 0 – Global System Context (send once per session)

```text
You are an expert C++20/Objective-C++ engineer working on MicroLauncher (“kai”), a hardened macOS desktop app.  Follow these NON-NEGOTIABLE constraints in ALL replies:

• Languages: C++20 for core, Objective-C++ for UI glue; no Swift/SwiftUI.
• Build: CMake + Ninja; project root has build.sh & run.sh.
• Style: Google C++ with 4-space indent, 100 col, enforced by .clang-format.
• Logging macros: DBG(...), ERR(...) (C++); DBM(...), ERM(...) (Objective-C++).
• Memory: rpmalloc via ArenaAllocatorSvc; `KAI_ALLOCATOR=mimalloc` for ASan runs.
• Error handling: kai::Expected<T,E>.
• Security: Hardened runtime, no system(), no dlopen() outside PluginManager, follow capability mask.
• Architecture: composition over inheritance; core/* must NOT depend on ui/*.
• NEVER run destructive commands.

You must include implementation + header + unit-tests for every new component and keep code self-contained.
```

---

## Prompt 1 – ServiceRegistry & Compile-time DAG

```text
Task: Implement ServiceRegistry, ServiceId enum, and compile-time ServiceTraits DAG (Slice 1, Deliverable 1).

Requirements
1. Numeric enum ServiceId (core/foundation/service_id.h) with initial IDs: kServiceRegistry, kArenaAllocatorSvc, kExecutorService, kEventBusService, kDiagnosticsService, kPluginManagerSvc, kIndexService, kConfigService, kHistoryService, kLlmFactoryService.
2. service_traits.h – `template<ServiceId>` specialisations declare `using Dependencies = kai::TypeList<...>;`.
3. ServiceRegistry (registry.{h,cpp})
   • Fixed-size std::array<IService*, kMaxServices>.
   • O(1) lookup, deterministic two-phase start/stop respecting DAG.
   • API: `registerService(IService&)`, `template<typename T> T& get()`, `KaiExpected<void, KaiError> startAll()`, `stopAll()`.
   • Return KaiError::CircularDependency if DAG invalid.

Performance budget: lookup ≤ 5 ns (p95) on Apple M3.

Outputs
  ├── src/core/foundation/service_id.h
  ├── src/core/foundation/service_traits.h
  ├── src/core/foundation/registry.{h,cpp}
  └── tests/test_service_registry.cpp
```

---

## Prompt 2 – ArenaAllocatorSvc (rpmalloc façade)

```text
Task: Implement ArenaAllocatorSvc (Slice 1 Deliverable 2).

Requirements
1. Header-only façade `arena_allocator.h` in src/core/memory.
2. Integrate rpmalloc (FetchContent in CMake); expose `void* kai::alloc(size_t)`, `void kai::free(void*)`.
3. ArenaAllocatorSvc implements IService; on `start()` call `rpmalloc_initialize()`, on `stop()` call `rpmalloc_finalize()`.
4. Provide stats via `ArenaStats getStats()`.
5. Support `KAI_ALLOCATOR=mimalloc` switch at compile time.

Outputs: arena_allocator.h, arena_allocator_service.{h,cpp}, tests/test_arena_allocator.cpp.
```

---

## Prompt 3 – ExecutorSvc & EventBusSvc

```text
Task: Implement lightweight thread-pool ExecutorSvc and lock-free EventBusSvc (Slice 1 Deliverable 3).

ExecutorSvc
• Fixed pool size = std::thread::hardware_concurrency().
• submit() returns std::future.
• stop() drains queue then joins threads.

EventBusSvc
• Single-producer / multi-consumer ring buffer (boost::lockfree::spsc_queue OK).
• publish<E>(const E&), subscribe<E>(handler).
• Use ExecutorSvc threads to dispatch handlers.
• p95 latency < 0.5 ms for 10 k messages (benchmark).

Provide benchmarks/event_bus_bench.cpp + unit-tests.
```

---

## Prompt 4 – DiagnosticsService Baseline

```text
Task: Implement DiagnosticsService collecting counters and exposing a JSON snapshot (Slice 1 Deliverable 4).

• Thread-safe incrementCounter(name), setGauge(name,val).
• Snapshot returns kai::json::Document using nlohmann/json (FetchContent).
• Emit snapshot to DBG every 5 s.
• Unit-tests verify race-free increments under TSan build.
```

---

## Prompt 5 – capability256.h Generator CLI

```text
Task: Implement `kai-capability-gen` CLI tool generating header capability256.h (Slice 1 Deliverable 5).

• Read manifest.schema.json capability list.
• Emit constexpr 256-bit bitmap enum + helpers (has(cap), set(cap)).
• Use C++20 <format> for code-gen.
• Integration test compares output hash to golden file.
```

---

## Prompt 6 – NullRuntime & Stub PluginManagerSvc

```text
Task: Implement NullRuntime and PluginManagerSvc able to load/stop a null plugin (Slice 1 Deliverable 6).

• PluginManagerSvc scans plugin dir, validates codesign owner UID.
• Loads with RTLD_LOCAL | RTLD_FIRST, resolves KaiPluginInfo, immediately unloads (Slice 1 scope).
• Provide sample plugins/sample_null/ + CMake target; unit-test loads/unloads 100× leak-free (ASan).
```

---

## Prompt 7 – kai-seatbelt-gen (render-only) + codesign verify wrapper

```text
Task: Implement `kai-seatbelt-gen` CLI (Slice 1 Deliverable 7) and codesign_verify.h.

• seatbelt-gen: input manifest.toml → output .sb profile (render-only, no sandbox-exec).
• codesign_verify: wrapper around SecStaticCodeCheckValidity; returns KaiExpected<void, KaiError>.
• Unit-tests use fixture manifest and compare generated profile to golden string.
```

---

### Usage Tips

1. Paste Prompt 0 into the LLM as system/context before any deliverable prompt.
2. For each deliverable, paste the corresponding Prompt n and iterate until unit-tests & benchmarks pass.
3. After all deliverables merge, verify Slice 1 exit criteria checklist.
