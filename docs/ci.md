# Kai Continuous Integration (GitHub Actions)

The CI pipeline lives in `.github/workflows/ci.yml` and covers:

1. **Build Matrix** (macOS-14 runner)
   | Job | CMake Flags | Purpose |
   |-----|-------------|---------|
   | `release` | `-DCMAKE_BUILD_TYPE=Release` | Optimised artefacts, perf gate, codesign check. |
   | `asan` | `-DENABLE_ASAN=ON -DCMAKE_BUILD_TYPE=Debug` | AddressSanitizer memory errors. |
   | `tsan` | `-DENABLE_TSAN=ON -DCMAKE_BUILD_TYPE=Debug` | ThreadSanitizer data races. |
   | `sign` | `-DKAI_ENABLE_FLATSNAPSHOT_SIGN=ON -DCMAKE_BUILD_TYPE=Release` | Builds with FlatSnapshot signing path and libsodium. |
   | `thinlto` | `-DKAI_ENABLE_THINLTO=ON -DCMAKE_BUILD_TYPE=Release` | Release build with Thin-LTO for binary size & performance monitoring. |

2. **Common Steps**

    - checkout → brew install cmake & ninja → configure → ninja build
    - run fast test subset (`scripts/run_tests_fast.sh`)
    - codesign verification via `ci/scripts/codesign_verify.sh`

3. **Performance Regression Gate**
   The `perf_gate` job builds and runs `bench_adaptive_cache_hit` under Release
   and compares the produced JSON against `ci/perf_baseline.json` using
   `ci/scripts/perf_regression_gate.py`. Baseline entries with `real_time == 0`
   are considered placeholders and ignored. To refresh baseline:

    ```bash
    ./build.sh --no-test            # build Release locally
    ./build/bin/bench_adaptive_cache_hit \
        --benchmark_format=json \
        --benchmark_out=ci/perf_baseline.json
    git add ci/perf_baseline.json
    ```

4. **Updating Flags**
   New CMake flags should be reflected both in `docs/build_flags.md` and the
   matrix above.
