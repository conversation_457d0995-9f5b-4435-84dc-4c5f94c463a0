## R5 – Security Hardening & CI (Target: v1.0.0, Owner: Infrastructure Team)

### Objective

Finalize hardened runtime, codesign + notarisation pipeline, seatbelt profile enforcement, and comprehensive CI coverage to gate GA release.

### Deliverables

-   [ ] Codesign + notarisation workflow in GitHub Actions (macOS runner).
-   [ ] Revocation list fetch & enforcement in PluginManager.
-   [ ] Seatbelt profile generator integrated, enforced at plugin launch.
-   [ ] ASan / TSan builds executed nightly.
-   [ ] Fuzzers for IDL message parsing and manifest schema.
-   [ ] OWASP audit report and remediation checklist.
-   [ ] Release checklist automation (binary size, perf regressions, security scan).

### Detailed Tasks

| #   | Task                                                        | Lead      | ETA | Notes |
| --- | ----------------------------------------------------------- | --------- | --- | ----- |
| 1   | Implement codesign step using `xcrun codesign` in CI        | @infra    |     |       |
| 2   | Add notarisation submission via `xcrun notarytool`          | @infra    |     |       |
| 3   | Integrate revocation list check into `PluginManagerService` | @security |     |       |
| 4   | Enforce seatbelt profiles at load time                      | @security |     |       |
| 5   | Configure ASan/TSan matrices in CI                          | @infra    |     |       |
| 6   | Write LibFuzzer harness for `kai.idl` deserialiser          | @tests    |     |       |
| 7   | Produce OWASP audit & security checklist                    | @security |     |       |

### Acceptance Criteria

-   All binaries pass `codesign --verify --deep` and `xcrun notarytool staple`.
-   CI fails on unsigned / mis-signed plugin.
-   Fuzzer achieves ∼100 % coverage of IDL parsing paths with zero sanitizer findings.
-   Security audit shows no P1 vulnerabilities remaining.

### Risk & Mitigation

| Risk                          | Mitigation                       |
| ----------------------------- | -------------------------------- |
| Notary queue delays CI        | Cache tokens; parallelise builds |
| Seatbelt rule false positives | Gradual rollout with allowlist   |

### References

-   Apple Notarization Guide – https://developer.apple.com/documentation/security/notarizing_macos_software_before_distribution
-   LLVM LibFuzzer documentation
