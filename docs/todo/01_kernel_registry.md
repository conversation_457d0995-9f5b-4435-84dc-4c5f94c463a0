## R1 – Kernel & Service Registry (Target: v0.9.0-alpha, Owner: Core Team)

### Objective

Implement the minimal service kernel (IService, ServiceRegistry, ArenaAllocatorSvc, ExecutorSvc) required to bootstrap the application and enable further runtime work.

### Deliverables

-   [ ] `core/foundation/service.h` – `enum class ServiceState`, `Expected<void, KaiError> start()` etc.
-   [ ] `core/foundation/registry.{h,cpp}` – dependency-aware start/stop order, constexpr DAG.
-   [ ] `core/async/executor.{h,cpp}` – `std::jthread` pool with cooperative stop.
-   [ ] `core/services/arena_allocator_service.{h,cpp}` – TLS arenas, 64-KiB slabs.
-   [ ] `core/services/executor_service.{h,cpp}` wrapping the executor.
-   [ ] Unit tests validating deterministic startup/shutdown (< 50 ms total).
-   [ ] Micro-benchmark target: allocator `allocate(128B)` ≤ 10 ns avg.
-   [ ] Initial diagnostics counters surfaced via DBG/ERR.

### Detailed Tasks

| #   | Task                                                     | Lead         | ETA | Notes |
| --- | -------------------------------------------------------- | ------------ | --- | ----- |
| 1   | Define `IService` interface in `service.h`               | @core-dev    |     |       |
| 2   | Implement topological sort start order in `registry.cpp` | @core-dev    |     |       |
| 3   | Write `executor.cpp` with stop_token handling            | @concurrency |     |       |
| 4   | Integrate arenas with executor worker local storage      | @concurrency |     |       |
| 5   | Add GTest suite `kernel_tests.cpp`                       | @tests       |     |       |
| 6   | Benchmark harness in `benchmarks/kernel_start_bench.cpp` | @bench       |     |       |

### Acceptance Criteria

-   Application boots to idle in < 50 ms (Debug build on M2 Pro).
-   All GTests pass; coverage ≥ 80 % for kernel code.
-   `./benchmarks/kernel_start_bench` hits performance targets.
-   No dynamic allocations in hot paths (`ExecutorSvc::submit`).

### Risk & Mitigation

| Risk                             | Mitigation                                                      |
| -------------------------------- | --------------------------------------------------------------- |
| Deadlock in service dependencies | Unit test with cyclic graph → expect KaiError                   |
| Thread oversubscription          | Executor queries `std::thread::hardware_concurrency()` and caps |

### References

-   Herb Sutter "CppCon 2020: Back to Basics – Threading"
-   Facebook folly `Executor` design docs
