## R2 – Plugin Manager & Sandbox Skeleton (Target: v0.9.0-beta, Owner: Runtime Team)

### Objective

Enable loading/unloading of signed plugins with preliminary security checks and runtime type dispatch, paving the way for capability enforcement.

### Deliverables

-   [ ] `core/services/plugin_manager_service.{h,cpp}` handling discovery, manifest parsing, codesign verification.
-   [ ] `runtimes/native/` – `NativeRuntime` loader with `dlopen(..., RTLD_LOCAL)`.
-   [ ] `runtimes/jsc/` – `JsRuntime` wrapper creating per-plugin JSContext (no capabilities yet).
-   [ ] Seatbelt profile template generator (no enforcement yet).
-   [ ] `CapabilityManager` stub returning `true` for all checks.
-   [ ] Error propagation via `Expected<T, KaiError>` for signature, permission, ownership failures.
-   [ ] CLI command `kai plugin list` showing discovered plugins and trust tier.
-   [ ] Sample JS & native plugins load successfully.

### Detailed Tasks

| #   | Task                                                                 | Lead      | ETA | Notes |
| --- | -------------------------------------------------------------------- | --------- | --- | ----- |
| 1   | Implement manifest scanning in `PluginManagerService`                | @runtime  |     |       |
| 2   | Integrate macOS codesign verification (`SecStaticCodeCheckValidity`) | @security |     |       |
| 3   | Implement `NativeRuntime::load` and trampoline to C symbols          | @runtime  |     |       |
| 4   | Create `JsRuntime` with lightweight JSGlobalContext                  | @runtime  |     |       |
| 5   | Add seatbelt profile render function (inactive)                      | @security |     |       |
| 6   | Write CLI plumbing in `kai-plugin-tool`                              | @tools    |     |       |
| 7   | End-to-end test: load, start, stop sample plugins                    | @tests    |     |       |

### Acceptance Criteria

-   Plugin discovery takes < 20 ms for 10 plugins.
-   Incorrect signature ⇒ `KaiError::SignatureInvalid`.
-   Non-owner UID plugin refused with `KaiError::OwnershipMismatch`.
-   Memory leak checker shows 0 leaks after unload cycle.

### Risk & Mitigation

| Risk                    | Mitigation                                       |
| ----------------------- | ------------------------------------------------ |
| Codesign API complexity | Use Apple sample code; wrap in thin helper       |
| JSContext leaks         | Ensure `JSGlobalContextRelease` called in `stop` |

### References

-   Apple Security Framework docs – `SecStaticCode` APIs
-   dyld `dlopen` best practices
