## R3 – Capability Model & Policy Engine (Target: v0.9.1, Owner: Security Team)

### Objective

Implement hierarchical capability enforcement, watchdog limits, and the PolicyEngine that decides whether a host operation is permitted.

### Deliverables

-   [ ] `core/security/capability.{h,cpp}` – runtime checks and bitset helpers.
-   [ ] `core/services/policy_engine_service.{h,cpp}` – evaluates `ActionRequest` → allow/deny.
-   [ ] `host_api/dispatcher.{h,cpp}` – injects capability-filtered host functions into runtimes.
-   [ ] `CapabilityManager` wired into `PluginManagerService`.
-   [ ] Watchdog: per-plugin execution time & heap limit (JSC API + Wasm fuel).
-   [ ] CLI `kai plugin lint` verifying capability declarations vs code usage.
-   [ ] Unit tests: denied op returns `KaiError::CapabilityDenied`.

### Detailed Tasks

| #   | Task                                                                     | Lead      | ETA | Notes |
| --- | ------------------------------------------------------------------------ | --------- | --- | ----- |
| 1   | Flesh out capability hierarchy (domain/sub/flags)                        | @security |     |       |
| 2   | Implement `has(plugin_id, cap)` fast path (hop to constexpr table)       | @security |     |       |
| 3   | Hook dispatcher into `JsRuntime` and `NativeRuntime`                     | @runtime  |     |       |
| 4   | Add execution time limit via `JSGlobalContextGroupSetExecutionTimeLimit` | @runtime  |     |       |
| 5   | Implement wasm fuel counter & soft heap                                  | @runtime  |     |       |
| 6   | Extend `kai-plugin-tool` lint rules                                      | @tools    |     |       |
| 7   | Add GTest coverage for PolicyEngineService                               | @tests    |     |       |

### Acceptance Criteria

-   Denied host call incurs < 100 ns overhead.
-   Watchdog terminates runaway JS loop within 100 ms plus grace.
-   `kai plugin lint` flags undeclared capability usage.
-   All previously passing plugins still load (with correct capabilities declared).

### Risk & Mitigation

| Risk                     | Mitigation                                                |
| ------------------------ | --------------------------------------------------------- |
| False positives in lint  | Allow override via `--allow=cap` CLI flag; review process |
| Watchdog kills too early | Tune limit; expose config pref                            |

### References

-   Apple JSC execution time limit API
-   Google Chrome seatbelt profile generator
