## R0 – Contract Freeze (Target: v0.9.0-alpha, Owner: Core Team)

### Objective

Freeze foundational contracts (ABI, manifest schema, capability layout, IService lifecycle, IDL) so that all other teams can develop in parallel with stable interfaces.

### Deliverables

-   [ ] `abi/abi.h` with `KaiPluginInfo`, version negotiation constants, and required C symbols.
-   [ ] `manifest.schema.json` under `docs/manifest/`, validated by JSON Schema.
-   [ ] `core/security/capability.h` – constexpr `Capability` (32-bit packed).
-   [ ] `docs/lifecycle.md` – IService state machine & threading guarantees.
-   [ ] `kai.idl` (Cap'n Proto or FlatBuffers) describing host-API surface.
-   [ ] `tools/kai-idl-gen` code generator emitting C++/ObjC++/TS stubs.
-   [ ] Sample hello-world plugin skeletons (JS, Wasm, Native) consuming generated stubs.
-   [ ] CI job that invokes `kai-idl-gen` & schema validation (fails on mismatch).

### Detailed Tasks

| #   | Task                                                                              | Lead      | ETA | Notes |
| --- | --------------------------------------------------------------------------------- | --------- | --- | ----- |
| 1   | Draft `abi/abi.h` with `KaiPluginInfo` struct & version constants                 | @core-dev |     |       |
| 2   | Author `manifest.schema.json` incl. capability list validation                    | @core-dev |     |       |
| 3   | Implement `Capability` struct & helpers in `capability.h`                         | @core-dev |     |       |
| 4   | Write `docs/lifecycle.md` with sequence diagrams                                  | @docs     |     |       |
| 5   | Decide IDL technology (Cap'n Proto vs FlatBuffers) via spike                      | @arch     |     |       |
| 6   | Prototype `kai.idl` with 3 sample host calls (`log`, `fetch`, `eventBus.publish`) | @arch     |     |       |
| 7   | Implement `kai-idl-gen` CMake external tool + integration                         | @tools    |     |       |
| 8   | Create stub bindings for JS & TS SDK                                              | @runtime  |     |       |
| 9   | Integrate schema & IDL checks into CI pipeline                                    | @infra    |     |       |

### Acceptance Criteria

-   `abi/abi.h` compiles with `clang -std=c17 -Weverything`.
-   `manifest.example` passes JSON-schema validation.
-   `tools/kai-idl-gen` produces compilable stubs for all targets.
-   All three sample plugins build against frozen contracts.
-   Core builds < 60 s (Debug, no LTO) using new headers.

### Risk & Mitigation

| Risk                     | Mitigation                                               |
| ------------------------ | -------------------------------------------------------- |
| Disagreement on IDL tech | Time-boxed spike & decision vote by day 3                |
| ABI churn after freeze   | Introduce `KaiAbiMinor`; forbid breaking changes post-R0 |

### References

-   Apple JavaScriptCore API Docs – https://developer.apple.com/documentation/javascriptcore
-   Cap'n Proto design notes – https://capnproto.org
