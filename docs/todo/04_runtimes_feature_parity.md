## R4 – Runtime Feature Parity (Target: v0.9.2, Owner: Runtime Team)

### Objective

Achieve functional parity across JavaScript, WebAssembly, and Native runtimes with capability-filtered host calls, memory pools, and performance benchmarks.

### Deliverables

-   [ ] `JsRuntime` injects `kai` global with Promise-based host API stubs.
-   [ ] `WasmRuntime` adapter using Wasmtime-cpp; memory pool hooks.
-   [ ] `NativeRuntime` XPC helper template for elevated plugins.
-   [ ] Benchmarks: JS round-trip latency ≤ 5 ms (p95), Wasm ≥ 85 % native speed.
-   [ ] EventBus integration available to all runtimes.
-   [ ] Sample math.wasm plugin and hello.js plugin pass tests.

### Detailed Tasks

| #   | Task                                                         | Lead     | ETA | Notes |
| --- | ------------------------------------------------------------ | -------- | --- | ----- |
| 1   | Implement `kai.emit()` binding in JSCore                     | @runtime |     |       |
| 2   | Map Wasm imports to host IDL via trampolines                 | @runtime |     |       |
| 3   | Add pooled allocator for Wasm linear memory                  | @runtime |     |       |
| 4   | Create XPC helper skeleton project templates                 | @runtime |     |       |
| 5   | Build micro-benchmarks in `benchmarks/wasm_plugin_bench.cpp` | @bench   |     |       |
| 6   | Update documentation & SDK typings                           | @docs    |     |       |

### Acceptance Criteria

-   JS plugin round-trip call ≤ 5 ms (p95) measured by benchmark.
-   Wasm add/mul loops ≥ 85 % native speed.
-   Native XPC plugin compiles & loads under hardened runtime.
-   EventBus publishes ≥ 10 k msgs/s with < 0.5 ms latency (p95).

### Risk & Mitigation

| Risk                     | Mitigation                         |
| ------------------------ | ---------------------------------- |
| Wasm runtime RSS bloat   | Shared memory pool & watchdog      |
| JS Promise glue overhead | Use C API fast-path; micro-profile |

### References

-   Wasmtime C++ embedding guide – https://docs.wasmtime.dev/cpp.html
-   Apple XPC services programming guide
