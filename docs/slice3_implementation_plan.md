# Kai – Slice-3 Implementation Plan (Performance & Parity / MCP Completion)

> Revision: 2025-06-02 • Author: architecture-wg • Status: **DRAFT / WORKING**  
> Pre-requisites: Slice-2 `Security & Discovery` delivered and merged into `main`.

---

## 0. Object<PERSON> & Scope

Slice-3 finalises runtime parity, performance tuning, and advanced observability across **all** plugin types and MCP servers. The slice builds **directly** on the zero-alloc security substrate and unified discovery pipeline shipped in Slice-2.

High-level goals:

1. **Runtime Parity** – Hardened WasmEdge runtime (Wasmtime fallback) reaches ≥ 85 % native speed; MCP servers deliver ≤ 250 ms p95 tool latency.
2. **Zero-Alloc Streaming Path** – JSON-RPC decode/encode path incurs **zero** steady-state heap allocations; p95 decode latency ≤ 50 µs @ 1 KiB.
3. **Observability Upgrade** – HDR histograms for latency buckets, OTLP exporter, OpenTelemetry spans for runtimes, transports, and tool calls.
4. **Binary Size & Build Time** – Enable Thin-LTO + ICF; adopt initial C++20 modules to cut incremental rebuilds by ≥ 40 %. Binary shrinks ≥ 15 %.

Out-of-scope (future slices): Remote discovery registry, auto-update for servers, WebSocket transport.

---

## 1. Architectural Additions

| ID  | Component                   | Language | Key Interfaces                                                          | Notes                                                                                                       |
| --- | --------------------------- | -------- | ----------------------------------------------------------------------- | ----------------------------------------------------------------------------------------------------------- |
| 3-A | `WasmRuntimeService`        | C++      | `IRuntime`, `IService`                                                  | Hosts WasmEdge; adapter layer for Wasmtime when `KAI_USE_WASMTIME=ON`. Alloc hooks → `ArenaAllocatorSvc`.   |
| 3-B | `JsonRpcCodec`              | C++      | `decode(const span<uint8_t>&)`, `encode(const JsonRpcMsg&, span<char>)` | SIMDJSON _ondemand_ parse + `fmt::format_to`. Varint-framed.                                                |
| 3-C | `TransportBase<Derived>`    | C++      | `start()`, `send(span<const char>)`, `shutdown()`                       | CRTP – Stdio, HTTP, Dylib specialisations. Stored in fixed-size inline buffer (no v-tables).                |
| 3-D | `ToolRegistry`              | C++      | `lookup(string_view name) -> const ToolDef*`                            | bbhash-generated MPH + flat-map delta. Snapshot mmapped.                                                    |
| 3-E | `AgentLoop`                 | C++      | `handlePartialChunk(span<const char>)`                                  | Streams chunks directly to UI; bypasses EventBus.                                                           |
| 3-F | OTLP Exporter               | C++      | `exportSpan(const Span&)`                                               | Gated by `KAI_ENABLE_OTLP`; configurable endpoint.                                                          |
| 3-G | HotpatchTableSvc (skeleton) | C++      | `find(cdhash)`, `stats()`                                               | Skeleton only (header), awaits ServiceId generator update; wires HotpatchVerifierTable into Service kernel. |

### 1.1 Data Flow Diagram (Runtime → UI)

```
┌──────────────┐   PartialChunk   ┌──────────────┐
│ Transport(s) │ ───────────────► │  AgentLoop   │
│  (Stdio/HTTP)│                  │ (Zero-copy)  │
└──────────────┘                  └─────┬────────┘
                                        │ SwiftTermString
                                        ▼
                                ┌──────────────┐
                                │   UI Layer   │
                                └──────────────┘
```

---

## 2. Detailed Epics & Deliverables

### Epic G – Wasm Runtime Hardening

| Task | Description                                                  | Owner        | Done-when                                                  |
| ---- | ------------------------------------------------------------ | ------------ | ---------------------------------------------------------- |
| G-1  | Integrate WasmEdge 0.14-stable with host allocator shim.     | core-runtime | WasmEdge demo runs in-process.                             |
| G-2  | Add Wasmtime fallback behind `KAI_USE_WASMTIME` flag.        | core-runtime | CI matrix builds both variants.                            |
| G-3  | Watchdog timer (kqueue / pidfd) kills runtimes > 100 ms CPU. | diagnostics  | Diagnostics counter `wasm.watchdog_kill_total` increments. |
| G-4  | Memory budget enforcement via ArenaAllocator stats.          | runtime-mgr  | Exceed → `KaiError::BudgetExceeded`.                       |

### Epic H – MCP Zero-Alloc Streaming Path

| Task | Description                                                          | Owner    | Metric Target                         |
| ---- | -------------------------------------------------------------------- | -------- | ------------------------------------- |
| H-1  | Implement `JsonRpcDecoder` using SIMDJSON _ondemand_.                | mcp-team | p95 decode ≤ 50 µs.                   |
| H-2  | Implement `JsonRpcEncoder` with `fmt::format_to` + pre-sized buffer. | mcp-team | Zero allocations; guard counter == 0. |
| H-3  | Varint framing on transport pipes; newline mode behind flag.         | mcp-team | Throughput +15 %.                     |
| H-4  | End-to-end soak test (1 M msg) with zero leaks (ASan).               | QA       | PASS.                                 |

### Epic I – Transport Framework

Deliver `TransportBase<Derived>` with Stdio + HTTP implementations.

-   Inline buffer size: `kTransportBuf = 128` bytes.
-   Move/copy semantics: `std::bit_cast` to `std::array<std::byte, kBuf>`.
-   Each transport owns `RingBuffer<PartialChunk, 1024, RingPolicy::DropOldest>`.

### Epic J – ToolRegistry & AgentLoop

-   CLI `kai-mph-gen` outputs bbhash seed + snapshot `.bin` → part of build.
-   Snapshot schema: `{tool_id: u32, cap_mask: Mask128, name_len: u8, name}`.
-   `AgentLoop` subscribes to transport-level chunks, streams to UI in ≤ 16 ms frame.

### Epic K – Thin-LTO + C++20 Modules

-   Enable `-flto=thin` + `-Wl,--icf=safe` in Release profiles.
-   Module pilot: convert `util/debug`, `time_utils`, `capability_mask` into `kai.*.ixx`.
-   Cache BMIs via `ccache` to cut incremental build time.

### Epic L – Observability Upgrade

-   Integrate `hdrhistogram_c` → wrap in `Histogram` utility.
-   Expose histograms via Diagnostics HTTP endpoint `/metrics/hdr`.
-   OTLP exporter ships spans to Jaeger by default (`localhost:4317`).

---

## 3. Testing & Validation Strategy

1. **Unit Tests** – GTest for `JsonRpcCodec`, `TransportBase`, `ToolRegistry` (coverage ≥ 85 %).
2. **Fuzzing** – libFuzzer harnesses: `json_rpc_fuzzer`, `transport_fuzzer`. 5 k exec/s budget.
3. **Micro-benches** – `bench_jsonrpc_decode`, `bench_wasm_call`, `bench_tool_call`.
4. **Integration Tests** – End-to-end plugin load → MCP tool call → UI render within budget.
5. **Watchdog Tests** – Inject infinite loop Wasm, assert kill + error.

---

## 4. CI/CD Updates

| Stage             | Change                                                |
| ----------------- | ----------------------------------------------------- |
| Configure         | Add `kai-mph-gen` + WasmEdge & Wasmtime FetchContent. |
| Build Matrix      | Release+Thin-LTO, ASan, TSan.                         |
| Tests             | Run new unit & fuzzer targets.                        |
| Codesign/Notarise | Sign new CLI binaries, WasmEdge dylibs.               |
| Perf Drift        | Compare histograms vs. baseline JSON; fail > 5 %.     |

---

## 5. Schedule (Working Days 16-30)

| Day   | Focus                                  |
| ----- | -------------------------------------- |
| 16    | WasmEdge allocator hooks + demo        |
| 17-18 | TransportBase + Stdio/HTTP             |
| 19-20 | JsonRpcDecoder/Encoder, varint framing |
| 21    | End-to-end zero-alloc benchmark        |
| 22-23 | ToolRegistry MPH + snapshot builder    |
| 24    | AgentLoop streaming path               |
| 25    | Thin-LTO flags, module pilot           |
| 26    | OTLP exporter integration              |
| 27    | HDR histogram wiring                   |
| 28    | Micro-bench polish, fuzz soak          |
| 29-30 | Risk buffer, docs, PR reviews          |

---

## 6. Acceptance Criteria (Slice-3)

1. Cold start (200 runtimes) ≤ 125 ms _(measured on M2, Release+Thin-LTO)_.
2. JSON-RPC decode p95 ≤ 50 µs for 1 KiB message.
3. Tool call p95 ≤ 250 ms (Filesystem server benchmark).
4. Wasm plugin ≥ 85 % native in `sample_math_wasm` micro-bench.
5. EventBus publish p95 < 0.5 ms; `RingBuffer` drops < 0.01 %.
6. Thin-LTO reduces binary size ≥ 15 %; incremental rebuilds ≤ 60 % baseline.
7. Steady-state heap allocs on MCP traffic == 0 (guard counter).
8. OTLP spans visible in Jaeger; histograms export correct p95.
9. CI Release/ASan/TSan green; codesign + notarisation pass.

---

## 7. Risk Register & Mitigation

| Risk                                 | Likelihood | Impact                      | Mitigation                                                                                       |
| ------------------------------------ | ---------- | --------------------------- | ------------------------------------------------------------------------------------------------ |
| WasmEdge API drift                   | Low        | Build failures              | Pin release tag; compile smoke test daily.                                                       |
| MPH seed collision                   | Low        | Wrong tool lookup           | 64-bit seed + fuzz harness.                                                                      |
| Transport buffer overflow            | Med        | Crash                       | `span` bounds checks + ASan CI.                                                                  |
| OTLP exporter latency                | Med        | UI lag                      | Async queue + back-pressure metric.                                                              |
| Template bloat (CRTP)                | Low        | Binary size                 | Explicit instantiation `.cpp`; Thin-LTO.                                                         |
| Thin-LTO build time spike            | Med        | CI slowdown                 | ccache + artefact cache; only Release job uses LTO.                                              |
| ServiceId drift for HotpatchTableSvc | Low        | Build failures / missing ID | Land skeleton header only; update generator early in Slice-3, compile-time static_assert guards. |

---

## 8. Open Questions

1. Do we standardise on Wasmtime 16.x or track upstream trunk? (Decision by Day 18.)
2. Which OTLP endpoint URL is shipped by default in production builds? (DevOps consultation.)
3. Do we gate C++20 modules behind `KAI_ENABLE_MODULES` _at runtime_ or compile-time only? (Compiler team to advise.)

---

_End of document_
