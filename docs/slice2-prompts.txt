Task: Implement header-only AdaptiveCache<K,V,Policy>
Requirements:
• Striped shard design; spin-lock free look-ups.
• Adaptive kWays growth (2…16) triggered when miss_ratio > 15 %.
• Expose Stats{hit,miss,evict} and stats() accessor to satisfy MetricSource.
Outputs:
• include/kai/container/adaptive_cache.hh (fully documented, clang-format compliant)
• tests/adaptive_cache_stats_test.cpp verifying auto-grow & eviction

Task: Build Layered VerificationStore using AdaptiveCache (L1) + VerdictSnapshot (L2)
Requirements:
• FlatSnapshot-backed L2 store with ghost entries for negative look-ups.
• Public API `{lookup(ctx,digest), persist(ctx,digest,verdict)}` returning Expected.
• Zero heap allocations on hot path; explicit instantiation in .cpp.
Outputs:
• include/kai/security/verification_store.hh / .cpp
• snapshot/verdict_store.hh / .cpp
• tests/verdict_store_hit_miss_test.cpp

Task: Implement SeatbeltVerifier with PHF profile table
Requirements:
• ObjC++ shim around sandbox_check(3) on macOS.
• Build-time tool `tools/seatbelt_delta_gen.py` regenerates perfect-hash profile header.
• Enforced in Release & Debug; unit test feeds modified profile and expects kRejected.
Outputs:
• include/kai/security/seatbelt_verifier.hh / .mm
• tools/seatbelt_delta_gen.py + CMake add_custom_command rule
• tests/seatbelt_verifier_profile_test.cpp

Task: Introduce QueueTraits abstraction and update RingQueue backend
Requirements:
• Define `template<class T> struct RingQueueTraits` with tryEnqueue/tryDequeue semantics.
• Migrate runtime/ring_queue_backend.hh to use traits; no perf regression (>5 %).
• Add hazard-pointer ABA unit test and libFuzzer target.
Outputs:
• include/kai/runtime/event_queue.hh (concept + traits)
• runtime/ring_queue_backend.hh
• tests/ring_queue_hazard_pointer_test.cpp
• fuzz/ring_queue_fuzzer.cpp

Task: Wire MetricSource enumeration into Diagnostics exporter
Requirements:
• Provide `template<class List> void registerMetricSources()` meta-function using for_each_type.
• OTLP/Prom exporter scrapes stats() at 1 Hz; no virtual dispatch allowed.
• Update docs and add enumeration test.
Outputs:
• include/kai/util/metrics.hh updated
• diagnostics/metrics_exporter.{h,cpp}
• tests/metric_source_enumeration_test.cpp 