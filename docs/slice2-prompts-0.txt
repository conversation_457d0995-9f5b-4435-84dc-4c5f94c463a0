Task 1 – Implement Mask128 Helpers
Requirements:
• Create header `src/core/security/mask128.hh`.
• Define `struct Mask128 { uint64_t lo, hi; }` with constexpr `has`, `set`, `clear`, `popcount`, `to_string`.
• Provide adapter helpers for `kai::Capability` enum (generated capability128.h).
• Zero heap allocations, header-only, fully `constexpr` where possible.
• Add Catch2 test `src/tests/core/security/mask128_helper_test.cpp` with >4 coverage cases.
Outputs:
• New header and unit-test compiling & passing.

Task 2 – Implement AdaptiveCache
Requirements:
• Add header `src/core/container/adaptive_cache.hh` + impl file for explicit instantiation.
• Template `<Key,Value,Policy,Shards>`; open-address N-way buckets (2-16) per shard, spin-lock via `std::atomic_flag`.
• Provide `getOrInsert`, `erase`, `stats()`; zero steady-state heap allocs.
• Include Policy hooks `onMiss`, `onEvict`.
• Explicitly instantiate `<CDHash,Verdict>` & `<PathHash,ProbeResult>` in `container/adaptive_cache.cpp`.
• Bench: `src/benchmarks/adaptive_cache_hit_bench.cpp` hitting 1e6 ops.
Outputs:
• Header, cpp instantiation, unit-test, benchmark.

Task 3 – VerificationStore
Requirements:
• File `src/core/security/verification_store.hh` wrapping `AdaptiveCache<CDHash,Verdict>`.
• API: `find(cdhash)`, `insert(cdhash, verdict)` returning Expected.
• Zero allocations after warm-up; expose `stats()` implementing MetricSource.
• Modify `RuntimeManagerSvc::probeLibrary` to consult store before heavy checks.
Outputs:
• New header, adaptation in RuntimeManagerSvc, unit-test.

Task 4 – FlatSnapshot Format
Requirements:
• Add `src/core/storage/flat_snapshot.hh` and `.cpp`.
• Binary header `{magic:"KFSN", ver:1, build_sha[20], crc32, sig_len, sig_bytes}`.
• API `create(vec<byte> payload, path out)`, `mmapReadOnly(path)`, `verify()` (CRC + CMS signature).
• Use SecStaticCode APIs for CMS verify (macOS only); stub success on non-Apple.
• Unit-test corrupt header detection.
Outputs:
• Header, cpp, test.

Task 5 – ProbeCache
Requirements:
• New `src/snapshot/probe_cache.hh/.cpp`.
• Compose L1 AdaptiveCache + mmap FlatSnapshot; L2 SQLite (amalgam) for persistence.
• API: `lookup(path, mtime_sha256) -> Verdict`, `update(descriptor, verdict)`, `flush()`.
• MetricSource implementation.
• Integration test: cold scan 100 dummy runtimes, ensure hit-rate ≥95 % on second run.
Outputs:
• ProbeCache implementation, integration test.

Task 6 – QueueTraits & RingQueueBackend
Requirements:
• Header `src/core/runtime/queue_traits.hh` defining concept.
• `src/core/runtime/ring_queue_backend.hh` – fixed-size power-of-2 ring buffer, blocking `wait_pop`.
• Expose `QueueStats {hit, miss, drop, high_water_pct}`.
• Refactor `EventBusService` to use new backend; bounded capacity 1024, back-pressure returns `KaiError::BackPressure`.
• Add micro-bench `src/benchmarks/ring_queue_bench.cpp`.
Outputs:
• New headers, refactored EventBus, bench, unit/tests.

Task 7 – RuntimeScanner Coroutine
Requirements:
• Files `src/core/runtime/runtime_scanner.hh/.cpp` using `src/core/util/file_watcher`.
• Coroutine gathers FsEvents, debounces, posts tasks to `ExecutorService` for hashing & verification.
• Generates `RuntimeDescriptor {type, path, caps, mtime_sha256}`; on success publishes `RuntimeDiscoveredEvent` via EventBus.
• Cold scan goal: ≤150 ms for 200 mocked runtimes (unit bench).
Outputs:
• Scanner implementation, performance test, EventBus event struct.

Task 8 – MetricSource Concept & Exporter
Requirements:
• Update `src/core/util/metrics.hh` with `concept MetricSource` (requires `stats()` returning struct).
• Adapt AdaptiveCache, RingQueue, ProbeCache to implement `stats()`.
• Update `diagnostics/metrics_exporter.cpp` to iterate over compile-time typelist via `for_each_type` pattern collecting stats each tick.
Outputs:
• Updated metrics headers, exporter, unit-test verifying enumeration.

Task 9 – ServiceBase<Derived,Deps…> & Service Topology Static Checks
Requirements:
• Introduce header `src/core/foundation/service_base.hh` implementing CRTP mix-in and migrate new services while keeping legacy IService via thin adapter.
• CMake step `cmake/CodeGenServices.cmake` to emit `build/generated/services/{service_id.h,service_meta.h,service_topology.h,service_static_checks.h}` from registry YAML.
• Add `static_assert(validateServiceGraph())` in a single TU to guarantee DAG and id ordering.
• Update CI to fail on drift.
Outputs:
• New ServiceBase header, generated topology headers (including `service_meta.h`), compile-time check TU, CI script update.

Task 10 – Build Flags & CI Glue
Requirements:
• Add CMake options `KAI_ENABLE_VPATCH` & `KAI_ENABLE_MUX_QUEUE`, default OFF.
• Gate hot-patch verifier table & MuxQueue code with `#if KAI_ENABLE_*`.
• Add CMake options `KAI_ENABLE_FLATSNAPSHOT_SIGN` and `KAI_ALLOCATOR` (rpmalloc|mimalloc).
• Inject compile-time definition `KAI_BUILD_SHA` (first 20 chars of current git commit) via `configure_file` so FlatSnapshot embeds build provenance.
• Extend CI pipeline: Release+ASan+TSan, codesign verify for host binaries, plugins, and FlatSnapshot; notarisation simulation respects `KAI_ENABLE_FLATSNAPSHOT_SIGN`.
• Add `ci/scripts/codesign_verify.sh` and `ci/scripts/perf_regression_gate.py` enforcing ≤5 % perf drift.
Outputs:
• Updated `cmakeOptions.cmake`, CI YAML, codesign verify script, perf regression gate script, documentation entry.

Task 11 – DynamicVerifierRegistry Rate-Limit
Requirements:
• Implement `src/core/security/dynamic_verifier_registry.hh/.cpp` that wraps the existing vector of `DynVerifierAdapter`.
• Enforce hard caps: max 8 verifiers total, max 1 registration per second (sliding window), and per-call `verify()` budget ≤250 µs enforced via `AsyncContext` deadline.
• Exceeding the limits returns `KaiError::TooManyVerifiers` or `KaiError::VerifierTimeout`.
• Provide unit-test `dynamic_verifier_rate_limit_test.cpp` covering saturation & timeout cases.
Outputs:
• Registry implementation, error enum additions, unit-test.

Task 12 – AdaptiveCache Heuristic Constants & Documentation
Requirements:
• Expose `constexpr kMissGrowThreshold = 0.15` and `constexpr kMaxWays = 16` inside `adaptive_cache.hh` policy section.
• Update `tests/adaptive_cache_stats_test.cpp` to assert growth triggers when miss-ratio >15 % and stops at 16 ways.
• Add Doxygen comment block explaining the heuristic and why 16 is chosen (cache-line fit).
Outputs:
• Updated header, extended unit-test, generated docs.

Task 13 – Global Allocator Baseline & Zero-Alloc Guard
Requirements:
• Add third-party rpmalloc via CMake `FetchContent` (placed in `third_party/rpmalloc`).
• Introduce CMake option `KAI_ALLOCATOR` with choices `rpmalloc` (default) or `mimalloc`.
• Implement `allocator_zero_alloc_guard_test.cpp` that executes a representative hot-path scenario (AdaptiveCache hit, RingQueue push/pop, verifier pipeline) under ASan and checks guard counter == 0.
• Update CI pipeline to build both allocators in matrix.
Outputs:
• CMake changes, allocator integration code, zero-alloc guard test, CI update.

Task 14 – RingQueue Fuzzer & ABA Stress Harness
Requirements:
• Create `fuzz/ring_queue_fuzzer.cpp` using libFuzzer that randomly interleaves multi-producer / multi-consumer operations on `RingQueue<T>` with TSAN instrumentation enabled.
• Reuse hazard-pointer ABA detection assertions; capture crashes or data races.
• Add CI target `ring_queue_fuzzer` executed for 30 s and must reach ≥5 k exec/s.
Outputs:
• Fuzzer source, CMake target, CI step.

Task 15 – SeatbeltVerifier & PHF Profile Table
Requirements:
• Add `src/core/security/seatbelt_verifier.hh/.mm` with ObjC++ shim calling `sandbox_check`.
• `tools/seatbelt_delta_gen.py` generates `seatbelt_profile.phf.h` at build time.
• Release & Debug builds enforce validation; env toggle for unit tests.
• Unit-test `tests/seatbelt_verifier_profile_test.cpp` verifies rejection path.
Outputs:
• Header, implementation, generator script, unit test.

Task 16 – Hot-Patch Verifier Table (flag `KAI_ENABLE_VPATCH`)
Requirements:
• Files `src/security/hotpatch_verifier_table.hh/.cpp` implementing a FlatSnapshot-signed override table validated via `SecStaticCodeCheckValidity`.
• Feature gated by `KAI_ENABLE_VPATCH` (default OFF) and compile-time flag guards.
• Fuzzer `fuzz/verifier_table_fuzzer.cpp` covers malformed snapshots.
Outputs:
• New header & cpp, fuzzer, CMake flag wiring, CI step.

Task 17 – VerdictStore (FlatSnapshot L2 for VerificationStore)
Requirements:
• Files `snapshot/verdict_store.hh/.cpp` implementing memory-mapped FlatSnapshot with ghost entries for negative look-ups.
• API: `lookup(digest)` and `persist(digest, verdict)`; implements MetricSource.
• Unit-test `tests/verdict_store_hit_miss_test.cpp`.
Outputs:
• VerdictStore header, cpp, unit test.

Task 18 – MuxQueue Backend (flag `KAI_ENABLE_MUX_QUEUE`)
Requirements:
• Header `runtime/mux_queue.hh` implementing tagged-priority queue built atop RingQueueTraits.
• Compile flag `KAI_ENABLE_MUX_QUEUE`; defaults OFF.
• Micro-bench `bench/mux_queue_prio_bench.cpp`; perf regression <5 %.
Outputs:
• Header, bench, flag wiring, unit test.

Task 19 – FlatSnapshot Fuzzer
Requirements:
• File `fuzz/flat_snapshot_fuzzer.cpp` fuzzes header and payload corruption.
• CI target executes 30 s with ≥5 k exec/s.
Outputs:
• Fuzzer source, CMake target, CI step.

Task 20 – Performance Regression Gate & Metric Enumeration Script
Requirements:
• Script `ci/scripts/perf_regression_gate.py` consumes Google Benchmark JSON output and fails when drift >5 % versus baseline.
• Update metrics exporter enumeration test to ensure all MetricSource registrants are reported.
Outputs:
• Perf gate script, CI integration, updated `tests/metric_source_enumeration_test.cpp`.

Task 21 – PolicyMask Integration & Verifier Pipeline Bench
Requirements:
• Update `src/core/security/verifier_strategy.hh` to integrate `constexpr PolicyMask` allowing compile-time skipping of optional verifiers.
• Ensure the ordered verifier list remains deterministic; static_assert on ordering.
• Add unit-test `tests/verification_pipeline_order_test.cpp` verifying mask logic & order.
• Add micro-bench `bench/bench_verifier_pipeline.cpp` measuring p95 latency.
Outputs:
• Updated header, compile-time assertions, unit-test, benchmark.

Task 22 – AdaptiveCache Fuzzer
Requirements:
• Create `fuzz/adaptive_cache_fuzzer.cpp` exercising adversarial key patterns and shard growth.
• Wire CMake target `adaptive_cache_fuzzer`; run under ASan+TSan for 30 s in CI (≥5 k exec/s).
Outputs:
• Fuzzer source, CMake target, CI step.

Task 23 – Manifest JSON Fuzzer
Requirements:
• Add `fuzz/manifest_json_fuzzer.cpp` targeting the plugin manifest schema parser.
• Ensure malformed inputs are rejected without OOB reads.
Outputs:
• Fuzzer source, CMake target, CI integration.

Task 24 – Verifier Pipeline Micro-Benchmark
Requirements:
• Implement `benchmarks/verifier_pipeline_bench.cpp` measuring p95 latency across full pipeline (L1+L2+verifiers) for 10 k iterations; goal ≤7 µs.
Outputs:
• Benchmark source, integrated into perf regression gate.

Task 25 – Runtime Scan 200 Benchmark
Requirements:
• Add `benchmarks/runtime_scan_200_bench.cpp` simulating 200 mocked runtimes; cold scan target ≤150 ms.
Outputs:
• Benchmark source, perf gate baseline JSON.

Task 26 – QueueTraits Spin-Block Unit-Test
Requirements:
• Implement `tests/queue_traits_spin_block_test.cpp` validating lock-free spin/sleep behaviour under contention.
• Part of RingQueueTraits coverage.
Outputs:
• New unit-test compiled & passing.

Task 27 – Thin-LTO Build Toggle
Requirements:
• Add CMake option `KAI_ENABLE_THINLTO` (default OFF) wiring `-flto=thin`.
• Update CI matrix to include `Release-ThinLTO` build for size/perf monitoring.
Outputs:
• CMake update, CI YAML change, documentation entry.

Task 28 – Notarisation Simulation CI Job
Requirements:
• Script `ci/scripts/notarisation_sim.sh` that signs FlatSnapshot & hot-patch dylib artifacts with ad-hoc cert, then runs `codesign --verify` + mock `notarytool`.
• CI stage `notarisation_sim` invoked for Release pipeline.
Outputs:
• Script, CI YAML update.

Task 29 – Legacy IService Migration Plan
Requirements:
• Document `docs/legacy_service_migration.md` enumerating remaining IService implementations, migration checklist, and deadlines.
• Add TODO comments in affected services referencing doc.
Outputs:
• Documentation file committed.

Task 30 – HDR Histogram Export for Latency Metrics
Requirements:
• Integrate header-only `hdr_histogram` (FetchContent) into `diagnostics/metrics_exporter.cpp`.
• Export histograms `sec.verifier_ms` and `runtime.scan_ms` at 1 Hz.
• Add unit-test `tests/hdr_latency_metric_test.cpp` ensuring buckets record values.
Outputs:
• Dependency addition, exporter update, unit-test.

Task 31 – Preferences ▸ Plugins Pane UI Update
Requirements:
• Update `ui/preferences_plugins_pane.mm` (Objective-C++) to display runtime id, version, and capability mask via `Mask128::to_string()`.
• Implement lightweight view-model `RuntimePluginCellModel` bridging core→UI.
• Read data from `RuntimeManagerSvc` snapshot; read-only display.
• Add unit-test `tests/ui/plugins_pane_render_test.mm` using XCT.
Outputs:
• UI implementation, model header, Objective-C++ unit-test.

Task 32 – slice2_architecture.md Refresh
Requirements:
• Revise documentation with latest diagrams, end-to-end flow, and updated glossary.
Outputs:
• Updated markdown committed.

Task 33 – MuxQueue Starvation Test, High-Water Alert & Bench
Requirements:
• Add `tests/mux_queue_starvation_test.cpp` ensuring high-priority items cannot starve low-priority beyond 100 µs window.
• Benchmark `bench/mux_queue_starvation_bench.cpp` reports starvation percentile; budget <1 %.
• Implement queue high-water alert rule in `diagnostics/queue_alerts.cpp` that inspects `QueueStats::high_water_pct` each tick and emits `WARN` when >60 % (generic) and >85 % for MuxQueue (legacy EventBus already handled).
• Wire exporter update in `diagnostics/metrics_exporter.cpp` to surface alert metric.
• Unit-test `tests/queue_high_water_alert_test.cpp`.
Outputs:
• Bench, exporter & alert implementation, unit-tests.

Task 34 – Decision Log for Open Questions
Requirements:
• New file `docs/slice2_open_questions.md` capturing decisions on:
  – Default for `KAI_ENABLE_MUX_QUEUE`
  – Signing scheme (Ed25519 vs CMS) for FlatSnapshot
  – AdaptiveCache default shard count & growth factor
  – Signing authority for hot-patch dylibs.
• Update README to point to decision log.
Outputs:
• Decision log committed, README link.

Task 35 – Unified Fuzz Harness Runner
Requirements:
• Add CMake target `kai_all_fuzzers` that depends on individual fuzzers (`ring_queue_fuzzer`, `flat_snapshot_fuzzer`, `adaptive_cache_fuzzer`, `manifest_json_fuzzer`, `verifier_table_fuzzer`).
• CI job `fuzz_matrix` runs each fuzzer for 30 s with ≥5 k exec/s, aggregates crashes, and uploads corpus artifacts.
• Provide helper script `ci/scripts/run_all_fuzzers.sh` to facilitate local runs.
Outputs:
• CMake target, CI YAML update, helper script.