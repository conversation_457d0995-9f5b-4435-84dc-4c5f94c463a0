
DEEP MODE ⓾ – RE-AUTHORED “SLICE-2” IMPLEMENTATION BLUEPRINT  
(This replaces the original slice2.txt; incorporates ShardedCache, unified VerifierStrategy, IEventQueue, AsyncContext, and ancillary improvements.)

──────────────────────────────────────────────────────────────────────────────
0. NOMENCLATURE & DIRECTORY CONVENTIONS
──────────────────────────────────────────────────────────────────────────────
• C++ headers:         src/core/<domain>/… / include/kai/…  
• ObjC++ glue:         src/core/<domain>/macos/… (only when needed)  
• Tests:               src/core/tests/<domain>/… (Catch2)  
• Benchmarks:          src/core/benchmarks/<domain>/… (Google Benchmark)  
• Tools / scripts:     src/tools/…  /  scripts/…  
• Build flags:         SLICE2_PHASE_<N> (BOOL, default OFF until merged)  
• Macros:              DBG, ERR, DBM, ERM  (debug.h)  
• Error handling:      Expected<T, KaiErrc> (KaiErrc = std::error_code enum)  

──────────────────────────────────────────────────────────────────────────────
1. PHASE-0 – FOUNDATION, COROUTINES & PRIMITIVES
──────────────────────────────────────────────────────────────────────────────
1.1 New files / types  
   a. include/kai/meta/type_list.hh  
      – type_list, concat_t, index_of_v, contains_v.  
   b. include/kai/async/task.hh  
      – task<T> (C++23 coroutine).  
      – make_ready(), make_exception().  
   c. include/kai/async/async_context.hh  
      ```c++
      namespace kai::async {
      struct AsyncContext {
        Deadline       deadline;
        metrics::ScopeTimer timer;
        CancellationSlot cancel;
        template<class Awaitable> task<Expected<void,KaiErrc>> wrap(Awaitable&&);
      };
      }
      ```  
   d. include/kai/async/task_semaphore.hh (awaitable counting semaphore).  
   e. include/kai/container/sharded_cache.hh  
      ```c++
      template<class Key,class Val,
               size_t kWays = 4, size_t kShards = std::max(4u, std::thread::hardware_concurrency())>
      class ShardedCache { … tryLookup / insertOrUpdate … };
      ```  
   f. include/kai/runtime/ring_queue.hh (lock-free SPSC/MPSC, power-of-2).  

1.2 CMake  
   • KaiCoroutines.cmake (+fcoroutines).  
   • target `kai_primitive` (INTERFACE) exports headers above.  

1.3 Tests / Benchmarks  
   • sharded_cache_test.cpp (hit/miss, concurrency).  
   • ring_queue_test.cpp (SPSC/MPSC stress, TSAN).  
   • ring_queue_bench.cpp (p50 / p99 latency JSON).  

1.4 CI hooks  
   – Job `phase0-unit`, `phase0-bench` (perf regression gate 5 %).  

──────────────────────────────────────────────────────────────────────────────
2. PHASE-1 – SECURITY CORE v1  (L1)  —  VERIFIER STRATEGY + CACHE
──────────────────────────────────────────────────────────────────────────────
2.1 Concepts / Enums  
   a. include/kai/security/verifier_strategy.hh  
      ```c++
      enum class VerifierStage : uint8_t { kEarly, kDefault, kLate };
      template<class Impl>
      concept VerifierStrategy = requires(Impl v, const Digest& d, async::AsyncContext& ctx) {
        { Impl::stage() } -> std::convertible_to<VerifierStage>;
        { v.verify(ctx,d) } -> kai::ExpectedConvertible<Verdict>;
      };
      ```  

2.2 Built-in verifiers  
   • cdhash_verifier.hh/.cpp   – DBG-only stub returns kTrusted.  
   • seatbelt_verifier.hh/.cpp – TODO Phase-5.  

   All wrap in `CDHashVerifier`, `SeatbeltVerifier` (struct) satisfying concept.

2.3 Dynamic verifier adapter  
   include/kai/security/dyn_verifier_adapter.hh  
   ```c++
   struct DynVerifierAdapter {
     std::unique_ptr<IDynamicVerifier> impl;
     static constexpr VerifierStage stage() { return impl->stage(); }
     Expected<Verdict,KaiErrc> verify(async::AsyncContext& ctx,const Digest& d){
       return impl->verify(d);                       // cancellation ignored by legacy
     }
   };
   ```

2.4 SecurityFacade  
   src/core/security/security_facade.hh/.cpp  
   Members:  
   • ShardedCache<Digest,Verdict> l1_cache_;  
   • static constexpr meta::type_list<CDHashVerifier> kBuiltinTuple;  
   • std::vector<DynVerifierAdapter> dyn_;  

   Algorithm (`co_await`): L1 lookup → for_each_type(builtins) → dyn_ loop → L1 insert.

2.5 Tests / Benchmarks  
   • security_facade_hit_miss_test.cpp  
   • security_l1_hit_bench.cpp (co-routine + cache).  

2.6 CI  
   – stage-collision static-assert + unit tests.

──────────────────────────────────────────────────────────────────────────────
3. PHASE-2 – SNAPSHOT TIER (L2)
──────────────────────────────────────────────────────────────────────────────
3.1 Storage primitives  
   • snapshot/wal_ring.hh/.cpp  (mmap, CRC32 page).  
   • snapshot/sstable.hh        (immutable table).  

3.2 SnapshotManager  
   snapshot_manager.hh/.cpp  
   – lookup(), spill(), compactor() – all coroutine; integrates AsyncContext.  
   – optional ShardedCache ghost entries for negative lookups.

3.3 SecurityFacade integration  
   – On L1 miss: `co_await snapshotManager_->lookup(ctx,digest)`.

3.4 Tests / Benches  
   • wal_ring_recovery_test.cpp, snapshot_lookup_bench.cpp.

──────────────────────────────────────────────────────────────────────────────
4. PHASE-3 – RUNTIME DISCOVERY  (FS EVENT PIPELINE)
──────────────────────────────────────────────────────────────────────────────
4.1 Event types  
   include/kai/scanner/fs_event.hh  
   ```c++
   struct FsEventHeader { uint64_t ino; uint32_t flags; uint16_t shard; uint16_t size; };
   struct FsEvent { FsEventHeader hdr; std::array<std::byte,0> payload; };
   ```

4.2 Queue abstraction  
   a. include/kai/runtime/event_queue_concept.hh  
      ```c++
      template<class Q,class T>
      concept EventQueue = requires(Q q,T t){
        { q.tryEnqueue(std::move(t)) } -> std::same_as<bool>;
        { q.tryDequeue(t) }           -> std::same_as<bool>;
      };
      ```  
   b. include/kai/runtime/any_queue.hh   (type-erased inline-buffer 64 B).  

4.3 FsEventSource abstraction  
   include/kai/runtime/fs_event_source.hh (CRTP base).  
   – macos_fsevents_source.mm implements CRTP.  

4.4 Router & Consumers  
   • runtime/fs_event_router.hh   – templated on EventQueue.  
   • runtime/runtime_scanner.hh/.cpp  
     – task<void> consume(Q&,shard_id).  

4.5 Back-pressure & metrics  
   • per-shard atomic `uint32_t drop_counter` with high-bit reason.  
   • metrics exporter (`util/metrics.hh`) scrapes counters.  

4.6 Tests  
   • fsevent_router_stress_test.cpp (6 producers, 1 M events).  

──────────────────────────────────────────────────────────────────────────────
5. PHASE-4 – EVENT QUEUE BACKEND (OPTIONAL Dispatch Channel)
──────────────────────────────────────────────────────────────────────────────
5.1 dispatch_channel_queue.hh/.mm  
   – conforms to EventQueue; wraps macOS `dispatch_channel_t`.  

5.2 Build flag  
   option(KAI_ENABLE_DISPATCH_CHAN "…" OFF) in CMakeLists.txt; compile-def
   `KAI_USE_DISPATCH_CHAN` guarded by macOS 14 check.  

5.3 CI  
   – dual matrix OFF/ON; nm gate forbids libdispatch symbols when OFF.

──────────────────────────────────────────────────────────────────────────────
6. PHASE-5 – SEATBELT & CAPABILITY TOOLS
──────────────────────────────────────────────────────────────────────────────
6.1 SeatbeltVerifier impl (uses sandbox_check via ObjC++ shim).  
6.2 tools/seatbelt_delta_gen.py → seatbelt_profile.phf.h (perfect hash table).  
6.3 add_custom_command regenerates phf.h on .sb change.  
6.4 Tests feed modified profile → expect kRejected.  

──────────────────────────────────────────────────────────────────────────────
7. PHASE-6 – HYBRID STRATEGY LIST (Dynamic Verifiers)
──────────────────────────────────────────────────────────────────────────────
7.1 interface   include/kai/security/i_dynamic_verifier.hh  
7.2 registry    dynamic_verifier_list.hh/.cpp (`registerVerifier` with DoS guard).  
7.3 adapter already in Phase-1 (DynVerifierAdapter).  
7.4 Plugin Manager extension ensures stage uniqueness + rate limit.  
7.5 Tests with mock plugin returning kRejected.  

──────────────────────────────────────────────────────────────────────────────
8. PHASE-7 – EXTERNAL BRIDGE & CI INTEGRATION
──────────────────────────────────────────────────────────────────────────────
8.1 external_verifier_bridge.hh/.mm (XPC service implementing IDynamicVerifier).  
8.2 Codesign + notarise helper; CI job `codesign-verify`.  
8.3 Perf dashboards ingest benches (`<EMAIL>`, `@PR.json`).  
8.4 Documentation: docs/slice2_architecture.md (with PlantUML and perf graphs);  
                   docs/plugins/verifier_sdk.md.  

──────────────────────────────────────────────────────────────────────────────
9. CROSS-PHASE INSTRUMENTATION & GUARDS
──────────────────────────────────────────────────────────────────────────────
• util/metrics.hh        – RAII timers → Prometheus textfile.  
• util/tracing.hh        – KAI_TRACE(category,msg) (off by default).  
• Sanitizers (thread, UB, address) in nightly builds.  
• clang-tidy (performance*, cppcoreguidelines*, bugprone*).  
• perf_regression_gate.py (fail on > 5 % regression).  
• Hazard pointer fuzz for ring_queue (R1 mitigation).  

──────────────────────────────────────────────────────────────────────────────
10. RISKS & MITIGATIONS
──────────────────────────────────────────────────────────────────────────────
R1 RingQueue ABA          → hazard pointer harness + TSAN fuzz.  
R2 Cache thrash           → metrics miss ratio; runtime kShards tune.  
R3 Dispatch channel avail → compile-time check; CI dual matrix.  
R4 Dynamic verifier DoS   → register rate-limit, deadline in AsyncContext.  
R5 Cross-stage deadlocks  → AsyncContext deadline + cancellation drill.  

──────────────────────────────────────────────────────────────────────────────
11. DELIVERABLE SUMMARY
──────────────────────────────────────────────────────────────────────────────
✓ 36 new headers, 18 new .cpp/.mm, 14 tests, 6 benchmarks, 2 Python tools,  
  1 CMake helper, 3 CI jobs, 2 docs.  
✓ Removes all `#ifdef` decision logic from hot paths; compile-time polymorphism.  
✓ Provides clear extension points for future L3 caches, alternate event
  sources, remote verifiers, and new sandbox policies with zero touching of
  core hot loops.
