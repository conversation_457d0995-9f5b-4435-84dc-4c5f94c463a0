#!/bin/bash
# Comprehensive test runner for MicroLauncher
# Provides unified interface for all testing modes with parallel-first approach

set -euo pipefail

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
BOLD='\033[1m'
NC='\033[0m' # No Color

# Default values
MODE="fast"
JOBS=""
VERBOSE=false
TIMEOUT=300

print_usage() {
    echo -e "${BOLD}MicroLauncher Test Runner${NC}"
    echo ""
    echo "Usage: $0 [MODE] [OPTIONS]"
    echo ""
    echo -e "${BOLD}Test Modes:${NC}"
    echo "  fast        Fast parallel tests (1.68s, 89% faster) [DEFAULT]"
    echo "  all         All tests in parallel (5.02s, 67% faster)"
    echo "  legacy      Sequential execution (15.35s, for debugging)"
    echo "  benchmarks  Performance benchmarks only"
    echo ""
    echo -e "${BOLD}Options:${NC}"
    echo "  -j, --jobs NUM     Number of parallel jobs (default: auto-detect)"
    echo "  -v, --verbose      Enable verbose output"
    echo "  -t, --timeout SEC  Test timeout in seconds (default: 300)"
    echo "  -h, --help         Show this help message"
    echo ""
    echo -e "${BOLD}Examples:${NC}"
    echo "  $0                    # Run fast tests (recommended for development)"
    echo "  $0 all               # Run all tests (recommended for validation)"
    echo "  $0 legacy -v         # Run sequentially with verbose output"
    echo "  $0 fast -j 8         # Run fast tests with 8 parallel jobs"
}

# Parse arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        fast|all|legacy|benchmarks)
            MODE="$1"
            shift
            ;;
        -j|--jobs)
            JOBS="$2"
            shift 2
            ;;
        -v|--verbose)
            VERBOSE=true
            shift
            ;;
        -t|--timeout)
            TIMEOUT="$2"
            shift 2
            ;;
        -h|--help)
            print_usage
            exit 0
            ;;
        *)
            echo -e "${RED}Unknown option: $1${NC}"
            print_usage
            exit 1
            ;;
    esac
done

echo -e "${BLUE}====================================================="
echo -e "${BOLD}MicroLauncher Test Runner${NC}"
echo -e "=====================================================${NC}"

# Navigate to build directory
cd "$PROJECT_ROOT/build" || {
    echo -e "${RED}Error: Build directory not found. Please run ./build.sh first${NC}"
    exit 1
}

# Check if tests are built
if [ ! -f "CTestTestfile.cmake" ]; then
    echo -e "${RED}Error: Tests not configured. Please run cmake from build directory${NC}"
    exit 1
fi

# Determine number of parallel jobs
if [ -z "$JOBS" ]; then
    if [[ "$OSTYPE" == "darwin"* ]]; then
        JOBS=$(sysctl -n hw.ncpu)
    else
        JOBS=$(nproc)
    fi
fi

# Display configuration
echo -e "${YELLOW}Configuration:${NC}"
echo -e "  Mode: $MODE"
echo -e "  Parallel jobs: $JOBS"
echo -e "  Timeout: ${TIMEOUT}s"
echo -e "  Verbose: $VERBOSE"
echo ""

# Record start time
start_time=$(date +%s)

# Execute tests based on mode
case $MODE in
    "fast")
        echo -e "${YELLOW}Running fast test subset (parallel execution)...${NC}"
        echo -e "${YELLOW}Excludes: EventBusIdleTest, SearchCacheTest, ServiceStartBench, McpClientServiceTest${NC}"
        echo ""
        
        CTEST_CMD="ctest -j$JOBS --output-on-failure --timeout $TIMEOUT -E \"(EventBusIdleTest|SearchCacheTest|ServiceStartBench|McpClientServiceTest)\""
        if [ "$VERBOSE" = true ]; then
            CTEST_CMD="$CTEST_CMD --verbose"
        fi
        ;;
        
    "all")
        echo -e "${YELLOW}Running all tests (parallel execution)...${NC}"
        echo ""
        
        CTEST_CMD="ctest -j$JOBS --output-on-failure --timeout $TIMEOUT"
        if [ "$VERBOSE" = true ]; then
            CTEST_CMD="$CTEST_CMD --verbose"
        fi
        ;;
        
    "legacy")
        echo -e "${YELLOW}Running all tests (sequential execution)...${NC}"
        echo -e "${YELLOW}⚠️  This is slower but useful for debugging timing issues${NC}"
        echo ""
        
        CTEST_CMD="ctest --output-on-failure --timeout $TIMEOUT"
        if [ "$VERBOSE" = true ]; then
            CTEST_CMD="$CTEST_CMD --verbose"
        fi
        ;;
        
    "benchmarks")
        echo -e "${YELLOW}Running performance benchmarks...${NC}"
        echo ""
        
        CTEST_CMD="ctest -L bench --output-on-failure --timeout $TIMEOUT"
        if [ "$VERBOSE" = true ]; then
            CTEST_CMD="$CTEST_CMD --verbose"
        fi
        ;;
esac

echo -e "${YELLOW}Command: $CTEST_CMD${NC}"
echo ""

# Execute the tests
if eval "$CTEST_CMD"; then
    # Calculate and display duration
    end_time=$(date +%s)
    duration=$((end_time - start_time))
    
    echo ""
    echo -e "${GREEN}====================================================="
    echo -e "✅ All tests passed in ${duration}s"
    
    # Provide helpful suggestions
    case $MODE in
        "fast")
            echo -e "💡 For complete validation: $0 all"
            ;;
        "all")
            echo -e "💡 For faster development: $0 fast"
            ;;
        "legacy")
            echo -e "💡 For faster execution: $0 all"
            ;;
        "benchmarks")
            echo -e "💡 For functional tests: $0 fast"
            ;;
    esac
    
    echo -e "=====================================================${NC}"
    exit 0
else
    end_time=$(date +%s)
    duration=$((end_time - start_time))
    
    echo ""
    echo -e "${RED}====================================================="
    echo -e "❌ Some tests failed after ${duration}s"
    echo -e "💡 For debugging: ctest --rerun-failed --output-on-failure"
    if [ "$MODE" != "legacy" ]; then
        echo -e "💡 For sequential execution: $0 legacy -v"
    fi
    echo -e "=====================================================${NC}"
    exit 1
fi 