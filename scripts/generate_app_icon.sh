#!/bin/bash

# Script to generate app icon for macOS app bundle
# This script creates an .icns file from our programmatic icon

set -e  # Exit on error

# Create temporary directory for icon generation
TEMP_DIR="$(mktemp -d)"
ICONSET_DIR="$TEMP_DIR/AppIcon.iconset"
mkdir -p "$ICONSET_DIR"

# Determine script directory and project root
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# Output paths
OUTPUT_DIR="$PROJECT_ROOT/src/ui/macos/resources"
ICNS_FILE="$OUTPUT_DIR/AppIcon.icns"

# Create Objective-C program to generate icons
ICON_GENERATOR="$TEMP_DIR/icon_generator"
cat > "$TEMP_DIR/icon_generator.m" << 'EOF'
#import <Cocoa/Cocoa.h>
#import <Foundation/Foundation.h>

// Copy of the AppIconView implementation
@interface AppIconView : NSView
@end

@implementation AppIconView

- (void)drawRect:(NSRect)dirtyRect {
    [super drawRect:dirtyRect];
    
    NSRect bounds = self.bounds;
    CGFloat size = MIN(bounds.size.width, bounds.size.height);
    NSRect drawRect = NSMakeRect(
        bounds.origin.x + (bounds.size.width - size) / 2,
        bounds.origin.y + (bounds.size.height - size) / 2,
        size,
        size
    );
    
    // Draw a rocket icon
    NSBezierPath *path = [NSBezierPath bezierPath];
    
    // Rocket body
    CGFloat rocketWidth = size * 0.5;
    CGFloat rocketHeight = size * 0.8;
    NSRect rocketRect = NSMakeRect(
        drawRect.origin.x + (drawRect.size.width - rocketWidth) / 2,
        drawRect.origin.y + (drawRect.size.height - rocketHeight) / 2,
        rocketWidth,
        rocketHeight
    );
    
    [path moveToPoint:NSMakePoint(NSMidX(rocketRect), NSMaxY(rocketRect))];
    [path lineToPoint:NSMakePoint(NSMaxX(rocketRect), NSMidY(rocketRect) + rocketHeight * 0.2)];
    [path lineToPoint:NSMakePoint(NSMaxX(rocketRect), NSMinY(rocketRect) + rocketHeight * 0.1)];
    [path lineToPoint:NSMakePoint(NSMidX(rocketRect) + rocketWidth * 0.3, NSMinY(rocketRect))];
    [path lineToPoint:NSMakePoint(NSMidX(rocketRect) - rocketWidth * 0.3, NSMinY(rocketRect))];
    [path lineToPoint:NSMakePoint(NSMinX(rocketRect), NSMinY(rocketRect) + rocketHeight * 0.1)];
    [path lineToPoint:NSMakePoint(NSMinX(rocketRect), NSMidY(rocketRect) + rocketHeight * 0.2)];
    [path closePath];
    
    // Window
    CGFloat windowSize = rocketWidth * 0.4;
    NSRect windowRect = NSMakeRect(
        NSMidX(rocketRect) - windowSize / 2,
        NSMidY(rocketRect) + rocketHeight * 0.1,
        windowSize,
        windowSize
    );
    NSBezierPath *windowPath = [NSBezierPath bezierPathWithOvalInRect:windowRect];
    
    // Fins
    NSBezierPath *finPath = [NSBezierPath bezierPath];
    [finPath moveToPoint:NSMakePoint(NSMinX(rocketRect), NSMinY(rocketRect) + rocketHeight * 0.2)];
    [finPath lineToPoint:NSMakePoint(NSMinX(rocketRect) - rocketWidth * 0.2, NSMinY(rocketRect))];
    [finPath lineToPoint:NSMakePoint(NSMinX(rocketRect), NSMinY(rocketRect) + rocketHeight * 0.1)];
    [finPath closePath];
    
    NSBezierPath *finPath2 = [NSBezierPath bezierPath];
    [finPath2 moveToPoint:NSMakePoint(NSMaxX(rocketRect), NSMinY(rocketRect) + rocketHeight * 0.2)];
    [finPath2 lineToPoint:NSMakePoint(NSMaxX(rocketRect) + rocketWidth * 0.2, NSMinY(rocketRect))];
    [finPath2 lineToPoint:NSMakePoint(NSMaxX(rocketRect), NSMinY(rocketRect) + rocketHeight * 0.1)];
    [finPath2 closePath];
    
    // Flame
    NSBezierPath *flamePath = [NSBezierPath bezierPath];
    [flamePath moveToPoint:NSMakePoint(NSMidX(rocketRect) - rocketWidth * 0.2, NSMinY(rocketRect))];
    [flamePath lineToPoint:NSMakePoint(NSMidX(rocketRect), NSMinY(rocketRect) - rocketHeight * 0.3)];
    [flamePath lineToPoint:NSMakePoint(NSMidX(rocketRect) + rocketWidth * 0.2, NSMinY(rocketRect))];
    [flamePath closePath];
    
    // Add shadow for larger icons
    if (size > 32) {
        NSShadow *shadow = [[NSShadow alloc] init];
        [shadow setShadowColor:[[NSColor blackColor] colorWithAlphaComponent:0.5]];
        [shadow setShadowOffset:NSMakeSize(0, -2)];
        [shadow setShadowBlurRadius:4.0];
        [shadow set];
    }
    
    // Draw with colors
    NSGradient *flameGradient = [[NSGradient alloc] initWithStartingColor:[NSColor redColor] 
                                                              endingColor:[NSColor orangeColor]];
    [flameGradient drawInBezierPath:flamePath angle:90];
    
    NSGradient *bodyGradient = [[NSGradient alloc] initWithStartingColor:[NSColor colorWithCalibratedRed:0.8 green:0.8 blue:0.9 alpha:1.0] 
                                                             endingColor:[NSColor colorWithCalibratedRed:0.6 green:0.6 blue:0.7 alpha:1.0]];
    [bodyGradient drawInBezierPath:path angle:90];
    
    [[NSColor darkGrayColor] setStroke];
    [path setLineWidth:MAX(1.0, size/50.0)];
    [path stroke];
    
    NSGradient *windowGradient = [[NSGradient alloc] initWithStartingColor:[NSColor colorWithCalibratedRed:0.4 green:0.6 blue:1.0 alpha:1.0] 
                                                               endingColor:[NSColor colorWithCalibratedRed:0.2 green:0.4 blue:0.8 alpha:1.0]];
    [windowGradient drawInBezierPath:windowPath angle:90];
    
    NSGradient *finGradient = [[NSGradient alloc] initWithStartingColor:[NSColor colorWithCalibratedRed:0.7 green:0.7 blue:0.8 alpha:1.0] 
                                                            endingColor:[NSColor colorWithCalibratedRed:0.5 green:0.5 blue:0.6 alpha:1.0]];
    [finGradient drawInBezierPath:finPath angle:90];
    [finGradient drawInBezierPath:finPath2 angle:90];
    
    // Add highlight for larger icons
    if (size > 32) {
        NSBezierPath *highlightPath = [NSBezierPath bezierPath];
        [highlightPath moveToPoint:NSMakePoint(NSMidX(rocketRect), NSMaxY(rocketRect))];
        [highlightPath lineToPoint:NSMakePoint(NSMidX(rocketRect) + rocketWidth * 0.25, NSMidY(rocketRect) + rocketHeight * 0.3)];
        [highlightPath lineToPoint:NSMakePoint(NSMidX(rocketRect) - rocketWidth * 0.25, NSMidY(rocketRect) + rocketHeight * 0.3)];
        [highlightPath closePath];
        
        [[NSColor colorWithCalibratedWhite:1.0 alpha:0.3] setFill];
        [highlightPath fill];
    }
}

@end

NSImage* createAppIcon(CGFloat size) {
    NSImage *image = [[NSImage alloc] initWithSize:NSMakeSize(size, size)];
    [image lockFocus];
    
    AppIconView *view = [[AppIconView alloc] initWithFrame:NSMakeRect(0, 0, size, size)];
    [view drawRect:NSMakeRect(0, 0, size, size)];
    
    [image unlockFocus];
    return image;
}

int main(int argc, const char * argv[]) {
    @autoreleasepool {
        if (argc < 3) {
            NSLog(@"Usage: %s <size> <output_path>", argv[0]);
            return 1;
        }
        
        // Parse arguments
        CGFloat size = atof(argv[1]);
        NSString *outputPath = [NSString stringWithUTF8String:argv[2]];
        
        // Create icon
        NSImage *icon = createAppIcon(size);
        
        // Save to file
        NSData *tiffData = [icon TIFFRepresentation];
        NSBitmapImageRep *imageRep = [NSBitmapImageRep imageRepWithData:tiffData];
        NSDictionary *options = @{NSImageCompressionFactor: @1.0};
        NSData *pngData = [imageRep representationUsingType:NSBitmapImageFileTypePNG properties:options];
        
        if ([pngData writeToFile:outputPath atomically:YES]) {
            NSLog(@"Icon saved to %@", outputPath);
            return 0;
        } else {
            NSLog(@"Failed to save icon to %@", outputPath);
            return 1;
        }
    }
    return 0;
}
EOF

# Compile the icon generator
echo "Compiling icon generator..."
clang -framework Cocoa -framework Foundation "$TEMP_DIR/icon_generator.m" -o "$ICON_GENERATOR"

# Generate icons in various sizes required for macOS
echo "Generating icons..."
"$ICON_GENERATOR" 16 "$ICONSET_DIR/icon_16x16.png"
"$ICON_GENERATOR" 32 "$ICONSET_DIR/<EMAIL>"
"$ICON_GENERATOR" 32 "$ICONSET_DIR/icon_32x32.png"
"$ICON_GENERATOR" 64 "$ICONSET_DIR/<EMAIL>"
"$ICON_GENERATOR" 128 "$ICONSET_DIR/icon_128x128.png"
"$ICON_GENERATOR" 256 "$ICONSET_DIR/<EMAIL>"
"$ICON_GENERATOR" 256 "$ICONSET_DIR/icon_256x256.png"
"$ICON_GENERATOR" 512 "$ICONSET_DIR/<EMAIL>"
"$ICON_GENERATOR" 512 "$ICONSET_DIR/icon_512x512.png"
"$ICON_GENERATOR" 1024 "$ICONSET_DIR/<EMAIL>"

# Create .icns file
echo "Creating .icns file..."
mkdir -p "$OUTPUT_DIR"
iconutil -c icns -o "$ICNS_FILE" "$ICONSET_DIR"

# Also save a large PNG for other uses
"$ICON_GENERATOR" 1024 "$OUTPUT_DIR/AppIcon.png"

# Clean up
echo "Cleaning up..."
rm -rf "$TEMP_DIR"

echo "Done! Icon file created at $ICNS_FILE"
echo "PNG icon also available at $OUTPUT_DIR/AppIcon.png" 