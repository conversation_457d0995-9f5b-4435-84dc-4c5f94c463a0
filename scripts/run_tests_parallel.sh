#!/bin/bash
# Parallel test runner for complete validation
# Runs all tests including slow ones with optimal parallelization

set -euo pipefail

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default values
JOBS=""
VERBOSE=false
TIMEOUT=300

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -j|--jobs)
            JOBS="$2"
            shift 2
            ;;
        -v|--verbose)
            VERBOSE=true
            shift
            ;;
        -t|--timeout)
            TIMEOUT="$2"
            shift 2
            ;;
        -h|--help)
            echo "Usage: $0 [OPTIONS]"
            echo "Options:"
            echo "  -j, --jobs NUM     Number of parallel jobs (default: auto-detect)"
            echo "  -v, --verbose      Enable verbose output"
            echo "  -t, --timeout SEC  Test timeout in seconds (default: 300)"
            echo "  -h, --help         Show this help message"
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            exit 1
            ;;
    esac
done

echo -e "${BLUE}====================================================="
echo -e "Parallel Test Runner - Complete Validation"
echo -e "=====================================================${NC}"

# Determine build directory (prefer build-debug if exists)
if [ -d "$PROJECT_ROOT/build-debug" ]; then
    BUILD_DIR="$PROJECT_ROOT/build-debug"
else
    BUILD_DIR="$PROJECT_ROOT/build"
fi

# Navigate to build directory
cd "$BUILD_DIR" || {
    echo -e "${RED}Error: Build directory not found. Please run ./build.sh first${NC}"
    exit 1
}

# Check if tests are built
if [ ! -f "CTestTestfile.cmake" ]; then
    echo -e "${RED}Error: Tests not configured. Please run cmake from build directory${NC}"
    exit 1
fi

# Determine number of parallel jobs
if [ -z "$JOBS" ]; then
    if [[ "$OSTYPE" == "darwin"* ]]; then
        JOBS=$(sysctl -n hw.ncpu)
    else
        JOBS=$(nproc)
    fi
fi

echo -e "${YELLOW}Configuration:${NC}"
echo -e "  Parallel jobs: $JOBS"
echo -e "  Timeout: ${TIMEOUT}s"
echo -e "  Verbose: $VERBOSE"
echo ""

# Build CTest command
CTEST_CMD="ctest -j$JOBS --output-on-failure --timeout $TIMEOUT"

if [ "$VERBOSE" = true ]; then
    CTEST_CMD="$CTEST_CMD --verbose"
fi

echo -e "${YELLOW}Running all tests in parallel...${NC}"
echo -e "${YELLOW}Command: $CTEST_CMD${NC}"
echo ""

# Record start time
start_time=$(date +%s)

# Run all tests
if eval "$CTEST_CMD"; then
    # Calculate and display duration
    end_time=$(date +%s)
    duration=$((end_time - start_time))
    
    echo ""
    echo -e "${GREEN}====================================================="
    echo -e "✅ All tests passed in ${duration}s"
    echo -e "💡 For faster development, use: ./scripts/run_tests_fast.sh"
    echo -e "=====================================================${NC}"
    exit 0
else
    end_time=$(date +%s)
    duration=$((end_time - start_time))
    
    echo ""
    echo -e "${RED}====================================================="
    echo -e "❌ Some tests failed after ${duration}s"
    echo -e "💡 For debugging, try: ctest --rerun-failed --output-on-failure"
    echo -e "=====================================================${NC}"
    exit 1
fi 