#!/bin/bash
# Fast test runner for development workflow
# Excludes slow tests and runs in parallel for quick feedback

set -euo pipefail

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}====================================================="
echo -e "Fast Test Runner for Development"
echo -e "=====================================================${NC}"

# Determine build directory (prefer build-debug if exists)
if [ -d "$PROJECT_ROOT/build-debug" ]; then
    BUILD_DIR="$PROJECT_ROOT/build-debug"
else
    BUILD_DIR="$PROJECT_ROOT/build"
fi

# Navigate to build directory
cd "$BUILD_DIR" || {
    echo -e "${RED}Error: Build directory not found. Please run ./build.sh first${NC}"
    exit 1
}

# Check if tests are built
if [ ! -f "CTestTestfile.cmake" ]; then
    echo -e "${RED}Error: Tests not configured. Please run cmake from build directory${NC}"
    exit 1
fi

# Get number of CPU cores for optimal parallelism
if [[ "$OSTYPE" == "darwin"* ]]; then
    CORES=$(sysctl -n hw.ncpu)
else
    CORES=$(nproc)
fi

echo -e "${YELLOW}Running fast tests with $CORES parallel jobs...${NC}"
echo -e "${YELLOW}Excluding slow tests: EventBusIdleTest, SearchCacheTest, ServiceStartBench, McpClientServiceTest${NC}"
echo ""

# Record start time
start_time=$(date +%s)

# Run tests excluding the slow ones (including McpClientServiceTest due to DiagnosticsService 5s delay)
if ctest -j"$CORES" --output-on-failure -E "(EventBusIdleTest|SearchCacheTest|ServiceStartBench|McpClientServiceTest)"; then
    # Calculate and display duration
    end_time=$(date +%s)
    duration=$((end_time - start_time))
    
    echo ""
    echo -e "${GREEN}====================================================="
    echo -e "✅ All fast tests passed in ${duration}s"
    echo -e "💡 For complete validation: ./scripts/run_tests_parallel.sh"
    echo -e "=====================================================${NC}"
    exit 0
else
    echo ""
    echo -e "${RED}====================================================="
    echo -e "❌ Some tests failed"
    echo -e "=====================================================${NC}"
    exit 1
fi 