#!/usr/bin/env bash
# -----------------------------------------------------------------------------
# run_llm_bench.sh – build & execute the LLM micro-benchmarks
#
# Usage:  ./scripts/run_llm_bench.sh [build-dir]
# If no build directory is supplied, `build-bench` is used.
# -----------------------------------------------------------------------------
set -euo pipefail

BUILD_DIR=${1:-build-bench}

echo "[bench] Build directory: $BUILD_DIR"

# Configure (Release for realistic perf numbers)
cmake -S . -B "$BUILD_DIR" -DCMAKE_BUILD_TYPE=Release

# Compile only the benchmark target using all available cores
cmake --build "$BUILD_DIR" --target llm_baseline_bench -- -j$(sysctl -n hw.ncpu || echo 4)

echo "[bench] Running benchmark…"
"$BUILD_DIR/bin/llm_baseline_bench" | cat 