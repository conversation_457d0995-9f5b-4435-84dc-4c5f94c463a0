# Scripts

This directory contains utility scripts for the MicroLauncher project.

## Icon Generation

### generate_app_icon.sh

This script generates the app icon files for the macOS app bundle. It creates:

1. An `.icns` file that contains all the required icon sizes for macOS app bundles
2. A high-resolution PNG file that can be used for other purposes

#### Usage

```bash
./scripts/generate_app_icon.sh
```

#### Output

The script generates the following files:

-   `src/ui/macos/resources/AppIcon.icns` - The icon file for the macOS app bundle
-   `src/ui/macos/resources/AppIcon.png` - A high-resolution PNG version of the icon

#### How it works

The script:

1. Creates a temporary directory
2. Compiles a small Objective-C program that uses the same icon drawing code as the app
3. Generates PNG files for all required icon sizes (16x16, 32x32, 64x64, 128x128, 256x256, 512x512, 1024x1024)
4. Uses `iconutil` to package these into an `.icns` file
5. Cleans up temporary files

#### Requirements

-   macOS (uses macOS-specific tools like `iconutil`)
-   Clang compiler
-   Cocoa framework

## Resource Management

### copy_resources.sh

This script copies resources (including the app icon) to the app bundle during the build process.

#### Usage

```bash
./scripts/copy_resources.sh <app_bundle_path>
```

Example:

```bash
./scripts/copy_resources.sh build/MicroLauncher.app
```

#### What it does

The script:

1. Copies the `AppIcon.icns` file to the app bundle's Resources directory
2. Copies the `AppIcon.png` file to the app bundle's Resources directory
3. Can be extended to copy other resources as needed

#### When to use

This script should be integrated into your build process after the app bundle has been created but before it is signed or packaged.
