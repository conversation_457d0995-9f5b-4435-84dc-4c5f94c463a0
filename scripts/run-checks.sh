#!/bin/bash

# <PERSON>ript to run all code quality checks locally

set -e  # Exit on error

echo "Running code quality checks..."

# Check if required tools are installed
MISSING_TOOLS=()

if ! command -v clang-format &> /dev/null; then
    MISSING_TOOLS+=("clang-format")
fi

if ! command -v cppcheck &> /dev/null; then
    MISSING_TOOLS+=("cppcheck")
fi

if ! command -v cmake &> /dev/null; then
    MISSING_TOOLS+=("cmake")
fi

if [ ${#MISSING_TOOLS[@]} -ne 0 ]; then
    echo "Error: The following required tools are missing:"
    for tool in "${MISSING_TOOLS[@]}"; do
        echo "  - $tool"
    done
    echo "Please install them and try again."
    exit 1
fi

# Format code
echo "Formatting code with clang-format..."
find src -name "*.cpp" -o -name "*.h" -o -name "*.hpp" | xargs clang-format -i
echo "Code formatting complete."

# Run static analysis
echo "Running static analysis with cppcheck..."
cppcheck --enable=all --std=c++17 --language=c++ --suppress=missingIncludeSystem --error-exitcode=1 -i build -i external src/
echo "Static analysis complete."

# Build and run tests
echo "Building project and running tests..."
mkdir -p build
cd build

# Configure with modern settings
cmake .. -DCMAKE_BUILD_TYPE=Debug -G Ninja -DCMAKE_EXPORT_COMPILE_COMMANDS=ON

# Build with optimal parallelism
if [[ "$OSTYPE" == "darwin"* ]]; then
    num_cores=$(sysctl -n hw.ncpu)
else
    num_cores=$(nproc)
fi
cmake --build . --parallel $num_cores

# -------------------------------------------------------------------------
# Validate that generated service headers are in sync with services.yaml.
# -------------------------------------------------------------------------
echo "Checking generated service headers for drift..."
# Path to freshly built generator
GENERATOR_PATH="$(pwd)/bin/kai-service-gen"
if [ ! -x "$GENERATOR_PATH" ]; then
    echo "Error: kai-service-gen executable not found at $GENERATOR_PATH" >&2
    exit 1
fi

# Temporary dir for re-generation
TMP_HEADERS=$(mktemp -d)
$GENERATOR_PATH -y "${PWD}/../services.yaml" -o "$TMP_HEADERS"

# Compare directories; any difference means drift
if ! diff -qr "$TMP_HEADERS" "${PWD}/generated/services" >/dev/null; then
    echo "Error: Generated service headers drift from committed copies. Run kai-service-gen and commit results." >&2
    exit 1
else
    echo "Service header drift check passed."
fi

# Run tests in parallel (fast subset for CI checks, complete for validation)
echo "Running parallel tests..."
if [ -f "../scripts/run_tests_parallel.sh" ]; then
    # Use complete parallel validation for thorough checks
    ../scripts/run_tests_parallel.sh
else
    # Fallback to CTest with parallel execution
    ctest -j"$num_cores" --output-on-failure
fi

echo "Tests complete."

echo "All checks passed successfully!" 