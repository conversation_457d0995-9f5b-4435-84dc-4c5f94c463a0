#!/usr/bin/env bash
# scripts/pre_commit_lint.sh
# Runs clang-format and clang-tidy on the staged changes only.
# Intended for use as a Git pre-commit hook or ad-hoc quality gate.
#
# Usage (from repo root):
#   ./scripts/pre_commit_lint.sh
# -----------------------------------------------------------------------------
set -euo pipefail

# Locate clang-format & clang-tidy
CFORMAT=${CLANG_FORMAT:-clang-format}
CTIDY=${CLANG_TIDY:-clang-tidy}

# Ensure build dir with compile_commands.json exists
BUILD_DIR=${BUILD_DIR:-build_tidy}
if [[ ! -f "$BUILD_DIR/compile_commands.json" ]]; then
  echo "[lint] compile_commands.json missing – building minimal DB…" >&2
  cmake -B "$BUILD_DIR" -DCMAKE_EXPORT_COMPILE_COMMANDS=ON .
fi

# 1. Format staged lines
staged_files=$(git diff --cached --name-only --diff-filter=ACM | grep -E '\.(cpp|hpp|cc|c|h|mm|m)$' || true)
if [[ -n "$staged_files" ]]; then
  echo "[lint] running clang-format…"
  echo "$staged_files" | xargs "$CFORMAT" -i --style=file
  git add $staged_files
fi

# 2. Run clang-tidy (only on changed files, no fixes)
if [[ -n "$staged_files" ]]; then
  echo "[lint] running clang-tidy…"
  echo "$staged_files" | xargs "$CTIDY" -p "$BUILD_DIR" --quiet || true
fi

echo "[lint] done" 