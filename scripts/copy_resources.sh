#!/bin/bash

# Script to copy resources to the app bundle
# This script should be run as part of the build process

set -e  # Exit on error

# Determine script directory and project root
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# Source and destination paths
RESOURCES_DIR="$PROJECT_ROOT/src/ui/macos/resources"
APP_BUNDLE_PATH="$1"

if [ -z "$APP_BUNDLE_PATH" ]; then
    echo "Error: App bundle path not specified"
    echo "Usage: $0 <app_bundle_path>"
    echo "Example: $0 build/MicroLauncher.app"
    exit 1
fi

if [ ! -d "$APP_BUNDLE_PATH" ]; then
    echo "Error: App bundle path does not exist: $APP_BUNDLE_PATH"
    exit 1
fi

# Create Resources directory in app bundle if it doesn't exist
APP_RESOURCES_DIR="$APP_BUNDLE_PATH/Contents/Resources"
mkdir -p "$APP_RESOURCES_DIR"

# Copy icon files
echo "Copying icon files to app bundle..."
cp "$RESOURCES_DIR/AppIcon.icns" "$APP_RESOURCES_DIR/"
cp "$RESOURCES_DIR/AppIcon.png" "$APP_RESOURCES_DIR/"

# Copy other resources if needed
# cp "$RESOURCES_DIR/other_resource.xyz" "$APP_RESOURCES_DIR/"

echo "Resources copied successfully to $APP_RESOURCES_DIR" 