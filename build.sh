#!/bin/bash

# Build script for the project
# Creates a build directory, configures and builds the project using CMake

# Parse command line arguments
RUN_TESTS="default"
BUILD_TYPE="Debug"
# Map build type -> out-of-source build directory to avoid cross-contamination
BUILD_DIR="build-debug"
while [[ $# -gt 0 ]]; do
    case $1 in
        --test)
            RUN_TESTS="fast"
            shift
            ;;
        --test-all)
            RUN_TESTS="all"
            shift
            ;;
        --test-legacy)
            RUN_TESTS="legacy"
            shift
            ;;
        --no-test)
            RUN_TESTS="none"
            shift
            ;;
        --debug)
            BUILD_TYPE="Debug"
            BUILD_DIR="build-debug"
            shift
            ;;
        --release)
            BUILD_TYPE="Release"
            BUILD_DIR="build"
            shift
            ;;
        --help)
            echo "Usage: $0 [OPTIONS]"
            echo "Options:"
            echo "  --test          Run fast tests after build (default, 1.68s)"
            echo "  --test-all      Run all tests after build (5.02s)"
            echo "  --test-legacy   Run tests sequentially (15.35s, for debugging)"
            echo "  --no-test       Skip tests after build"
            echo "  --debug         Build with Debug configuration (default)"
            echo "  --release       Build with Release configuration"
            echo "  --help          Show this help message"
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            echo "Use --help for usage information"
            exit 1
            ;;
    esac
done

# Print header with timestamp
echo "====================================================="
echo "Build started: $(date)"
echo "====================================================="

# Print system information
echo "System information:"
echo "  OS: $(uname -s)"
echo "  Hostname: $(hostname)"
echo "  Kernel: $(uname -r)"
echo "  Architecture: $(uname -m)"

# Print CMake version if available
if command -v cmake &> /dev/null; then
    echo "CMake version: $(cmake --version | head -n1)"
fi

# Print compiler information if available
if command -v g++ &> /dev/null; then
    echo "GCC version: $(g++ --version | head -n1)"
elif command -v clang++ &> /dev/null; then
    echo "Clang version: $(clang++ --version | head -n1)"
fi
echo "====================================================="

# Record start time for build duration calculation
start_time=$(date +%s)

echo "Creating build directory ($BUILD_DIR)..."
# Create build directory if it doesn't exist
mkdir -p "$BUILD_DIR"

# Navigate to the build directory
cd "$BUILD_DIR"

# Configure the project
echo "====================================================="
echo "Configuring with CMake..."
echo "====================================================="
# ------------------- Compiler cache detection -------------------------------
# Prefer `sccache` (supports Clang dep-scanning + remote caching).  Fallback
# to `ccache` if sccache is absent. Linker selection is handled in CMakeLists.

cmake_common_flags=(
    -G Ninja ..
    -DCMAKE_EXPORT_COMPILE_COMMANDS=ON
    -DCMAKE_BUILD_TYPE=${BUILD_TYPE}
    -Wno-dev
)

if [ -f CMakeCache.txt ] && grep -q "CMAKE_BUILD_TYPE:STRING=${BUILD_TYPE}" CMakeCache.txt; then
    echo "CMake already configured for ${BUILD_TYPE} – skipping configure step"
else
    cmake "${cmake_common_flags[@]}"
fi
cmake_status=$?
if [ $cmake_status -ne 0 ]; then
    echo "Error: CMake configuration failed with exit code $cmake_status"
    exit $cmake_status
fi

# Build the project
echo "====================================================="
echo "Building the project..."
echo "====================================================="
# Get the number of CPU cores, default to 4 if detection fails
if [[ "$OSTYPE" == "darwin"* ]]; then
    # macOS
    num_cores=$(sysctl -n hw.ncpu 2>/dev/null || echo 4)
else
    # Linux and others (common case)
    num_cores=$(nproc 2>/dev/null || echo 4)
fi
# Build with parallel jobs based on core count
cmake --build . --parallel $num_cores
build_status=$?
if [ $build_status -ne 0 ]; then
    echo "Error: Build failed with exit code $build_status"
    exit $build_status
fi

# Calculate and print build duration
end_time=$(date +%s)
duration=$((end_time - start_time))
minutes=$((duration / 60))
seconds=$((duration % 60))

echo "====================================================="
echo "Build completed successfully!"
echo "Build duration: ${minutes}m ${seconds}s"
echo "Build directory: $(pwd)"

# Run tests based on configuration (default is fast parallel tests)
if [ "$RUN_TESTS" != "none" ]; then
    echo "====================================================="
    echo "Running tests..."
    echo "====================================================="
    
    case $RUN_TESTS in
        "default"|"fast")
            echo "Running fast test subset (parallel, ~1.68s)..."
            ../scripts/run_tests_fast.sh
            ;;
        "all")
            echo "Running all tests (parallel, ~5.02s)..."
            ../scripts/run_tests_parallel.sh
            ;;
        "legacy")
            echo "Running tests (sequential, ~15.35s)..."
            ctest --output-on-failure
            ;;
    esac
    
    test_status=$?
    if [ $test_status -ne 0 ]; then
        echo "Error: Tests failed with exit code $test_status"
        exit $test_status
    fi
    
    echo "====================================================="
    echo "✅ All tests passed! Build and test completed successfully."
else
    echo "====================================================="
    echo "✅ Build completed successfully. (Tests skipped)"
fi

echo "=====================================================" 