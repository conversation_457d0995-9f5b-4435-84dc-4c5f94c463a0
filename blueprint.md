# Deep Mode – Comparative Analysis & Strategy Synthesis

We now have two independent blue-prints:

A. **Hybrid Plugin + Header-Only Architecture** (B + C) – my most recent proposal  
 • Tiny `kai-core` kernel (C façade).  
 • Drop-in `.dylib` plugins for scanners, LLMs, etc.  
 • Header-only/C++20-module algorithms, arena allocators, CRTP; minimum virtual dispatch.  
 • `ServiceRegistry` for dependency injection; no explicit service _lifecycle_ contract yet.  
 • Thread-pool executor provided.  
 • Focus on performance and runtime extensibility.

B. **architecture.md Recommendation** – Service-Oriented + selective Message Bus  
 • `IService` with explicit `initialize ↦ start ↦ stop ↦ shutdown` phases.  
 • `ServiceRegistry` plus `ServiceFactory`; deterministic start-order graph.  
 • Message bus for decoupled events where needed.  
 • Plugin/Hot-reload postponed to a later phase.  
 • Strong emphasis on clean layer directories (`foundation/`, `services/`, `ui/`).

The question: _Which concepts from each plan should we keep or drop to maximise simplicity, future expansion and performance?_

---

1. Common Ground

---

1. Service Registry / DI is essential.
2. We must replace monolithic `AppContext`.
3. Performance optimisations: arena/pool allocators, thread-pool, strict interface boundaries.

Hence, no conflict—only ordering and degree of modularity differ.

---

2. Key Differences & Impact

---

| Topic                     | Plugin + Header-Only Plan                       | architecture.md Plan                                   | Trade-off                                                                                                |
| ------------------------- | ----------------------------------------------- | ------------------------------------------------------ | -------------------------------------------------------------------------------------------------------- |
| **Runtime extensibility** | First-class: plugins (.dylib) loaded at startup | Deferred: internal services first, plugins later       | Earlier plugin focus forces ABI stabilisation sooner but yields faster feature add-ons.                  |
| **Service lifecycle**     | Implicit through DI; no formal phases           | Explicit IService phases                               | Lifecycle phases clarify startup/shutdown, aid testing; negligible perf cost.                            |
| **Message bus**           | Not included (direct calls or async futures)    | Included selectively for decoupled events              | Bus improves scalability/observability but adds overhead/complexity.                                     |
| **Build system**          | C++20 modules, header-only libs, LTO            | Conventional header+cpp; module adoption not specified | Modules reduce compile time and boost inlining, but toolchain maturity & IDE support are still evolving. |
| **Cross-plugin ABI**      | Pure C structs, versioned                       | Not yet defined                                        | ABI work unavoidable for plugin strategy.                                                                |
| **Initial complexity**    | Higher (dlopen, codesign, ABI)                  | Lower (services all in-process)                        | Could stagger: get service registry stable first, then expose plugin boundary.                           |

---

3. Synthesis – Best-of-Both Strategy

---

Step-wise path that leverages architecture.md’s clarity while retaining plugin & perf advantages:

Phase 1 – **Service Kernel Foundation**  
• Implement `IService` + `ServiceRegistry` with `initialize/start/stop` phases (architecture.md).  
• Migrate current subsystems (Index, Config, History, LLM) to services.  
• Introduce `Executor` and `arena_allocator` as kernel utilities (perf goals from B + C).

Phase 2 – **C++20 Modules & Header-Only Hot Paths**  
• Move reusable algorithms (`fuzzy_match`, `ranking`, JSON utils) to header-only modules.  
• Keep service _interfaces_ in module units (`export module kai.index;`).  
• Maintain normal `.cpp` for service _implementations_ to avoid massive binary growth.

Phase 3 – **Plugin Boundary Introduction**  
• Freeze a minimal C ABI in `core/api/abi.h`.  
• Extract **non-critical** components first: e.g., `scanner_macos` as plugin.  
• Keep critical path (AppIndex) in-process until ABI proves stable.  
• Provide template CMake macro `kai_add_plugin()`.  
• All plugins still use ServiceRegistry by receiving an opaque handle in `kaiRegisterPlugin`.

Phase 4 – **Selective Message Bus**  
• Add lightweight `EventBus` service (pub/sub with intrusive queues).  
• Use bus for cross-cutting notifications: `AppInstalled`, `SearchQueryLogged`, `LLMTokenMetric`.  
• Hot paths (search scoring) remain direct calls.

Phase 5 – **Hardening & CI**  
• Enforce ABI version checks, codesign, notarisation for plugins.  
• Benchmarks to track inlining, allocations, startup time.  
• Turn on LTO + ICF for binary deduplication.

---

4. Why This Blend Works

---

1. **Incremental Difficulty** – Start with internal refactor (low risk), plug-in later (higher risk).
2. **Clear Lifecycle** – Services still gain explicit states; tests can spin up partial graphs.
3. **Performance First** – Header-only & arenas appear immediately where they matter.
4. **Extensibility Path** – Once stable, new scanners/LLMs can be shipped without host rebuild.
5. **Observability** – Message bus + diagnostics service gives central hook for metrics & tracing.

---

5. Concrete Next Steps (should we go)

---

1. Code:  
   a. `core/foundation/service.h` – define `IService`.  
   b. `core/foundation/registry.h/.cpp` – ServiceRegistry with graph & phases.  
   c. `core/async/executor.{h,cpp}`.  
   d. Refactor `AppIndex` into `IndexService` implementing `IService`.

2. Build: flag `KAI_ENABLE_MODULES` in root `CMakeLists.txt`; create first sample module `kai.fuzzy`.

3. Tests: add GTest for ServiceRegistry lifecycle ordering & failure propagation.

Reply **“go”** to move into implementation of Phase 1 items, or **“deep”** to refine any part further.
