# Style Cheatsheet (Quick-Ref)

-   **Indent / Width**: 4 spaces, max 100 cols.
-   **Naming**
    -   Types/Enums: `PascalCase`
    -   Functions/Methods: `camelCase`
    -   Variables: `snake_case`
    -   Constants/Enums: `kConstantName`
    -   Member vars: `camelCase_` (trailing underscore)
-   **Includes Order**
    1. System `<...>`
    2. Library headers
    3. Project headers
-   **Brace Style**: Attach (`int foo() {`)
-   **Modern C++**
    -   Use `auto` when type obvious, not everywhere.
    -   Prefer `std::unique_ptr` / `std::shared_ptr` over raw.
    -   Use `override` on virtuals, `=default/ =delete` where appropriate.
-   **Comments**
    -   Doxygen `///` for public APIs.
    -   Inline comments only for non-obvious code.
    -   TODO(username): for follow-ups.
-   **Error Handling**
    -   Early return; structured `Result` objects.
    -   Exceptions rare; catch at boundaries.
-   **Macros**
    -   Use logging macros; avoid new macros unless cross-platform benefit.

## Modern C++20/23 Best Practices

-   Prefer **`std::span`** / **`std::string_view`** for non-owning views; avoid raw pointers/`const std::string&` for read-only params.
-   Use the **ranges** library (`std::ranges::transform`, `views::filter`, etc.) instead of manual loops when clarity is improved.
-   Express template constraints with **`concepts`** (`requires` clauses) rather than `enable_if` or SFINAE.
-   Leverage **`constexpr` / `consteval`** to move compile-time computations out of runtime; favour **compile-time string hashes** for IDs.
-   Mark functions **`[[nodiscard]]`** and **`noexcept`** where appropriate; use `std::expected<T, E>` or structured `Result` if available.
-   Prefer **`co_await` / coroutines** (via `std::generator`, custom task) for asynchronous pipelines when it simplifies control flow.
-   Use **three-way comparison (`<=>`)** to auto-generate ordering operators.
-   Default-construct aggregates; use **designated initialisers** for clarity.
-   Use **`using enum EnumType;`** for scope imports in limited blocks, not globally.
-   Favour **`std::format`** over `printf`-style or `stringstream` for human-facing text.
-   Avoid mutable globals; if singletons are unavoidable, wrap them in **`std::atomic`** or thread-safe init.

See `.clang-format` for complete rules.
