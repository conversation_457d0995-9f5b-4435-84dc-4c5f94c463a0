#!/bin/bash

# Exit on error
set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Print usage information
function print_usage {
    echo -e "${BLUE}Usage:${NC} $0 [options]"
    echo ""
    echo "Options:"
    echo "  -h, --help                 Show this help message"
    echo "  -v, --version VERSION      Specify version to tag (e.g., 1.2.3)"
    echo "  --major                    Increment major version (X.0.0)"
    echo "  --minor                    Increment minor version (0.X.0)"
    echo "  --patch                    Increment patch version (0.0.X)"
    echo "  -m, --message MESSAGE      Release message (default: 'Release VERSION')"
    echo "  --dry-run                  Show what would be done without making changes"
    echo "  --no-push                  Create tag but don't push to remote"
    echo "  --build                    Build macOS app bundle after tagging"
    echo "  --sign                     Sign the macOS app bundle (implies --build)"
    echo "  --developer-id ID          Developer ID to use for signing"
    echo "  --cert-type TYPE           Certificate type (default: 'Apple Development')"
    echo ""
    echo "Examples:"
    echo "  $0 --patch                 # Increment patch version"
    echo "  $0 -v 2.0.0                # Tag version 2.0.0"
    echo "  $0 --minor -m 'New features' # Increment minor version with custom message"
    echo "  $0 --patch --build         # Increment patch version and build app bundle"
    echo "  $0 --patch --sign --developer-id 'Your Name (TEAMID)' # Build and sign app bundle"
}

# Extract current version from CHANGELOG.md
function get_current_version {
    # Look for the latest version in CHANGELOG.md
    local version=$(grep -E '## \[[0-9]+\.[0-9]+\.[0-9]+\]' CHANGELOG.md | head -1 | sed -E 's/## \[([0-9]+\.[0-9]+\.[0-9]+)\].*/\1/')
    
    if [ -z "$version" ]; then
        echo "0.0.0"  # Default if no version found
    else
        echo "$version"
    fi
}

# Increment version based on semver rules
function increment_version {
    local version=$1
    local increment_type=$2
    
    # Split version into components
    IFS='.' read -r major minor patch <<< "$version"
    
    case "$increment_type" in
        major)
            major=$((major + 1))
            minor=0
            patch=0
            ;;
        minor)
            minor=$((minor + 1))
            patch=0
            ;;
        patch)
            patch=$((patch + 1))
            ;;
    esac
    
    echo "$major.$minor.$patch"
}

# Update version in package_app.sh
function update_package_app_sh {
    local version=$1
    local dry_run=$2
    
    if [ "$dry_run" = true ]; then
        echo -e "${YELLOW}Would update version in package_app.sh to $version${NC}"
    else
        sed -i.bak "s/APP_VERSION=\"[0-9]*\.[0-9]*\.[0-9]*\"/APP_VERSION=\"$version\"/" package_app.sh
        rm package_app.sh.bak
        echo -e "${GREEN}Updated version in package_app.sh to $version${NC}"
    fi
}

# Update version in CMakeLists.txt if it contains version information
function update_cmake_lists {
    local version=$1
    local dry_run=$2
    
    # Check if CMakeLists.txt contains version variables
    if grep -q "PROJECT_VERSION" CMakeLists.txt; then
        if [ "$dry_run" = true ]; then
            echo -e "${YELLOW}Would update version in CMakeLists.txt to $version${NC}"
        else
            # Split version into components
            IFS='.' read -r major minor patch <<< "$version"
            
            # Update version components
            sed -i.bak "s/set(PROJECT_VERSION_MAJOR [0-9]*)/set(PROJECT_VERSION_MAJOR $major)/" CMakeLists.txt
            sed -i.bak "s/set(PROJECT_VERSION_MINOR [0-9]*)/set(PROJECT_VERSION_MINOR $minor)/" CMakeLists.txt
            sed -i.bak "s/set(PROJECT_VERSION_PATCH [0-9]*)/set(PROJECT_VERSION_PATCH $patch)/" CMakeLists.txt
            sed -i.bak "s/set(PROJECT_VERSION \"[0-9]*\.[0-9]*\.[0-9]*\")/set(PROJECT_VERSION \"$version\")/" CMakeLists.txt
            rm CMakeLists.txt.bak
            echo -e "${GREEN}Updated version in CMakeLists.txt to $version${NC}"
        fi
    fi
}

# Move "Unreleased" section in CHANGELOG.md to new version
function update_changelog {
    local version=$1
    local date=$(date +%Y-%m-%d)
    local dry_run=$2
    
    if [ "$dry_run" = true ]; then
        echo -e "${YELLOW}Would update CHANGELOG.md with new version $version ($date)${NC}"
    else
        # Create a temporary file
        local temp_file=$(mktemp)
        
        # Process the CHANGELOG.md file
        awk -v version="$version" -v date="$date" '
        BEGIN { unreleased_found = 0; unreleased_content = ""; in_unreleased = 0; }
        
        /^## \[Unreleased\]/ { 
            unreleased_found = 1; 
            in_unreleased = 1;
            print $0;
            next;
        }
        
        /^## \[[0-9]+\.[0-9]+\.[0-9]+\]/ { 
            if (in_unreleased) {
                in_unreleased = 0;
                print "\n## [" version "] - " date "\n";
                print unreleased_content;
            }
            print $0;
            next;
        }
        
        { 
            if (in_unreleased) {
                unreleased_content = unreleased_content $0 "\n";
            } else {
                print $0;
            }
        }
        
        END {
            if (unreleased_found == 0) {
                print "Error: Could not find ## [Unreleased] section in CHANGELOG.md" > "/dev/stderr";
                exit 1;
            }
            if (in_unreleased) {
                print "\n## [" version "] - " date "\n";
                print unreleased_content;
            }
        }
        ' CHANGELOG.md > "$temp_file"
        
        # Check if the awk command was successful
        if [ $? -ne 0 ]; then
            rm "$temp_file"
            echo -e "${RED}Failed to update CHANGELOG.md${NC}"
            exit 1
        fi
        
        # Replace the original file with the modified content
        mv "$temp_file" CHANGELOG.md
        echo -e "${GREEN}Updated CHANGELOG.md with new version $version ($date)${NC}"
    fi
}

# Create git tag and push
function create_and_push_tag {
    local version=$1
    local message=$2
    local dry_run=$3
    local no_push=$4
    
    if [ "$dry_run" = true ]; then
        echo -e "${YELLOW}Would commit changes with message: $message${NC}"
        echo -e "${YELLOW}Would create tag v$version with message: $message${NC}"
        if [ "$no_push" = false ]; then
            echo -e "${YELLOW}Would push tag v$version to remote${NC}"
        fi
    else
        # Commit changes
        git add CHANGELOG.md package_app.sh CMakeLists.txt 2>/dev/null || true
        git commit -m "Bump version to $version" || true
        echo -e "${GREEN}Committed version changes${NC}"
        
        # Create tag
        git tag -a "v$version" -m "$message"
        echo -e "${GREEN}Created tag v$version${NC}"
        
        # Push tag if requested
        if [ "$no_push" = false ]; then
            git push origin "v$version"
            echo -e "${GREEN}Pushed tag v$version to remote${NC}"
            echo -e "${BLUE}CD workflow should now be triggered to build and release version $version${NC}"
        fi
    fi
}

# Build macOS app bundle
function build_app_bundle {
    local version=$1
    local sign=$2
    local developer_id=$3
    local cert_type=$4
    local dry_run=$5
    
    if [ "$dry_run" = true ]; then
        echo -e "${YELLOW}Would build macOS app bundle for version $version${NC}"
        if [ "$sign" = true ]; then
            echo -e "${YELLOW}Would sign app bundle with certificate: $cert_type: $developer_id${NC}"
        fi
    else
        echo -e "${BLUE}Building macOS app bundle for version $version...${NC}"
        
        # Prepare build command
        local build_cmd="./package_app.sh"
        
        if [ "$sign" = true ]; then
            build_cmd="$build_cmd --sign --developer-id \"$developer_id\""
            if [ -n "$cert_type" ]; then
                build_cmd="$build_cmd --cert-type \"$cert_type\""
            fi
        fi
        
        # Execute build command
        echo -e "${BLUE}Executing: $build_cmd${NC}"
        eval "$build_cmd"
        
        echo -e "${GREEN}Successfully built macOS app bundle for version $version${NC}"
        
        # Create a zip archive of the app bundle for easy distribution
        if [ -d "build-release/MicroLauncher.app" ]; then
            echo -e "${BLUE}Creating zip archive of the app bundle...${NC}"
            (cd build-release && zip -r "MicroLauncher-$version.zip" "MicroLauncher.app")
            echo -e "${GREEN}Created zip archive: build-release/MicroLauncher-$version.zip${NC}"
        fi
    fi
}

# Main script execution starts here

# Default values
VERSION=""
INCREMENT_TYPE=""
MESSAGE=""
DRY_RUN=false
NO_PUSH=false
BUILD_APP=false
SIGN_APP=false
DEVELOPER_ID=""
CERT_TYPE="Apple Development"

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            print_usage
            exit 0
            ;;
        -v|--version)
            VERSION="$2"
            shift 2
            ;;
        --major)
            INCREMENT_TYPE="major"
            shift
            ;;
        --minor)
            INCREMENT_TYPE="minor"
            shift
            ;;
        --patch)
            INCREMENT_TYPE="patch"
            shift
            ;;
        -m|--message)
            MESSAGE="$2"
            shift 2
            ;;
        --dry-run)
            DRY_RUN=true
            shift
            ;;
        --no-push)
            NO_PUSH=true
            shift
            ;;
        --build)
            BUILD_APP=true
            shift
            ;;
        --sign)
            SIGN_APP=true
            BUILD_APP=true  # Signing implies building
            shift
            ;;
        --developer-id)
            DEVELOPER_ID="$2"
            shift 2
            ;;
        --cert-type)
            CERT_TYPE="$2"
            shift 2
            ;;
        *)
            echo -e "${RED}Unknown option: $1${NC}"
            print_usage
            exit 1
            ;;
    esac
done

# Check if we're in a git repository
if ! git rev-parse --is-inside-work-tree >/dev/null 2>&1; then
    echo -e "${RED}Error: Not in a git repository${NC}"
    exit 1
fi

# Check for uncommitted changes
if [ -n "$(git status --porcelain)" ]; then
    echo -e "${YELLOW}Warning: You have uncommitted changes${NC}"
    echo -e "${YELLOW}It's recommended to commit or stash changes before tagging a release${NC}"
    read -p "Continue anyway? (y/N) " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 1
    fi
fi

# Validate signing parameters
if [ "$SIGN_APP" = true ] && [ -z "$DEVELOPER_ID" ]; then
    echo -e "${RED}Error: Developer ID is required for signing. Use --developer-id option.${NC}"
    exit 1
fi

# Get current version
CURRENT_VERSION=$(get_current_version)
echo -e "${BLUE}Current version: $CURRENT_VERSION${NC}"

# Determine new version
if [ -n "$VERSION" ]; then
    NEW_VERSION="$VERSION"
elif [ -n "$INCREMENT_TYPE" ]; then
    NEW_VERSION=$(increment_version "$CURRENT_VERSION" "$INCREMENT_TYPE")
else
    echo -e "${RED}Error: Either specify a version with -v or an increment type (--major, --minor, --patch)${NC}"
    print_usage
    exit 1
fi

echo -e "${BLUE}New version: $NEW_VERSION${NC}"

# Set default message if not provided
if [ -z "$MESSAGE" ]; then
    MESSAGE="Release $NEW_VERSION"
fi

# Confirm action
if [ "$DRY_RUN" = false ]; then
    echo -e "${YELLOW}This will:${NC}"
    echo "1. Update version in package_app.sh"
    echo "2. Update version in CMakeLists.txt (if applicable)"
    echo "3. Update CHANGELOG.md"
    echo "4. Create git tag v$NEW_VERSION"
    if [ "$NO_PUSH" = false ]; then
        echo "5. Push tag to remote (triggering CD workflow)"
    fi
    if [ "$BUILD_APP" = true ]; then
        echo "6. Build macOS app bundle"
        if [ "$SIGN_APP" = true ]; then
            echo "7. Sign the app bundle with certificate: $CERT_TYPE: $DEVELOPER_ID"
        fi
    fi
    read -p "Continue? (y/N) " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 1
    fi
fi

# Update files
update_package_app_sh "$NEW_VERSION" "$DRY_RUN"
update_cmake_lists "$NEW_VERSION" "$DRY_RUN"
update_changelog "$NEW_VERSION" "$DRY_RUN"

# Create and push tag
create_and_push_tag "$NEW_VERSION" "$MESSAGE" "$DRY_RUN" "$NO_PUSH"

# Build macOS app bundle if requested
if [ "$BUILD_APP" = true ]; then
    build_app_bundle "$NEW_VERSION" "$SIGN_APP" "$DEVELOPER_ID" "$CERT_TYPE" "$DRY_RUN"
fi

echo -e "${GREEN}Done!${NC}" 