# Helper function to define a Kai plugin target that automatically links the
# global allocator shim and core library.
#
# Usage:
#   add_kai_plugin(<target> <src1> [src2 ...])
# After invocation the caller can still set additional target properties
# (PREFIX, OUTPUT_NAME, etc.) or add further link libraries.
#
# The function automatically:
#   • creates a SHARED library target
#   • adds `${CMAKE_SOURCE_DIR}/src` to include dirs
#   • links to `kai_plugin_alloc_shim` and `core`
#   • copies the built dylib into `${CMAKE_RUNTIME_OUTPUT_DIRECTORY}/Plugins`
function(add_kai_plugin TARGET)
  # First argument is target name, the rest are sources
  set(options)
  set(oneValueArgs)
  set(multiValueArgs)
  cmake_parse_arguments(ARG "${options}" "${oneValueArgs}" "${multiValueArgs}" ${ARGN})

  add_library(${TARGET} SHARED ${ARG_UNPARSED_ARGUMENTS})

  target_include_directories(${TARGET} PRIVATE
      ${CMAKE_SOURCE_DIR}/src)

  # Link allocator shim + core for allocator symbols
  target_link_libraries(${TARGET} PRIVATE kai_plugin_alloc_shim core)

  # Convenience: auto-install plugin into runtime output directory for dev runs
  set(PLUGIN_DEST "${CMAKE_RUNTIME_OUTPUT_DIRECTORY}/Plugins")
  add_custom_command(TARGET ${TARGET} POST_BUILD
      COMMAND ${CMAKE_COMMAND} -E make_directory ${PLUGIN_DEST}
      COMMAND ${CMAKE_COMMAND} -E copy $<TARGET_FILE:${TARGET}> ${PLUGIN_DEST}
      COMMENT "Installing ${TARGET} into ${PLUGIN_DEST}")
endfunction() 