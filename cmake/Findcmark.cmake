# Find the cmark (CommonMark) library
#
# This module defines:
#  CMARK_FOUND        - True if cmark is found
#  CMARK_INCLUDE_DIRS - Directory to find cmark.h
#  CMARK_LIBRARIES    - Libraries to link against for cmark
#  CMARK_VERSION      - Version of cmark found

# Try using pkg-config first if available
find_package(PkgConfig QUIET)
if(PkgConfig_FOUND)
  pkg_check_modules(PC_CMARK QUIET libcmark)
endif()

# Find the include directory
find_path(CMARK_INCLUDE_DIR
  NAMES cmark.h
  PATHS
    ${PC_CMARK_INCLUDE_DIRS}
    /usr/include
    /usr/local/include
    ${CMAKE_PREFIX_PATH}/include
    $ENV{CMARK_DIR}/include
  DOC "cmark include directory"
)

# Find the library
find_library(CMARK_LIBRARY
  NAMES cmark libcmark
  PATHS
    ${PC_CMARK_LIBRARY_DIRS}
    /usr/lib
    /usr/local/lib
    ${CMAKE_PREFIX_PATH}/lib
    $ENV{CMARK_DIR}/lib
  DOC "cmark library"
)

# Extract version if we found the library and include directory
if(CMARK_INCLUDE_DIR AND CMARK_LIBRARY AND EXISTS "${CMARK_INCLUDE_DIR}/cmark_version.h")
  file(STRINGS "${CMARK_INCLUDE_DIR}/cmark_version.h" CMARK_VERSION_MAJOR REGEX "^#define[ \t]+CMARK_VERSION_MAJOR[ \t]+[0-9]+$")
  file(STRINGS "${CMARK_INCLUDE_DIR}/cmark_version.h" CMARK_VERSION_MINOR REGEX "^#define[ \t]+CMARK_VERSION_MINOR[ \t]+[0-9]+$")
  file(STRINGS "${CMARK_INCLUDE_DIR}/cmark_version.h" CMARK_VERSION_PATCH REGEX "^#define[ \t]+CMARK_VERSION_PATCH[ \t]+[0-9]+$")
  
  string(REGEX REPLACE "^#define[ \t]+CMARK_VERSION_MAJOR[ \t]+([0-9]+)$" "\\1" CMARK_VERSION_MAJOR "${CMARK_VERSION_MAJOR}")
  string(REGEX REPLACE "^#define[ \t]+CMARK_VERSION_MINOR[ \t]+([0-9]+)$" "\\1" CMARK_VERSION_MINOR "${CMARK_VERSION_MINOR}")
  string(REGEX REPLACE "^#define[ \t]+CMARK_VERSION_PATCH[ \t]+([0-9]+)$" "\\1" CMARK_VERSION_PATCH "${CMARK_VERSION_PATCH}")
  
  set(CMARK_VERSION "${CMARK_VERSION_MAJOR}.${CMARK_VERSION_MINOR}.${CMARK_VERSION_PATCH}")
endif()

# Set standard CMake find module variables
include(FindPackageHandleStandardArgs)
find_package_handle_standard_args(cmark
  REQUIRED_VARS CMARK_LIBRARY CMARK_INCLUDE_DIR
  VERSION_VAR CMARK_VERSION
)

# Set output variables
if(CMARK_FOUND)
  set(CMARK_LIBRARIES ${CMARK_LIBRARY})
  set(CMARK_INCLUDE_DIRS ${CMARK_INCLUDE_DIR})
endif()

# Mark as advanced variables
mark_as_advanced(CMARK_INCLUDE_DIR CMARK_LIBRARY) 