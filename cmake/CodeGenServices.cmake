# -----------------------------------------------------------------------------
# CodeGenServices.cmake  – generate service_* headers at build time
# -----------------------------------------------------------------------------
# This file is included from the root CMakeLists.txt after the kai-service-gen
# executable has been added (src/tools/kai-service-gen).  It wires a custom
# build rule that runs the generator when either services.yaml or the generator
# itself changes.  The produced headers land in
#   ${CMAKE_BINARY_DIR}/generated/services/
# and the directory is added to the global include path so that
#   #include <service_id.h>
# works for every target.
# -----------------------------------------------------------------------------

# Path to the YAML registry
set(KAI_SERVICES_YAML ${CMAKE_SOURCE_DIR}/services.yaml)

# Directory where the headers will be emitted
set(KAI_SERVICES_OUT_DIR ${CMAKE_BINARY_DIR}/generated/services)

# Ensure output directory exists during configuration so IDEs can index headers
file(MAKE_DIRECTORY ${KAI_SERVICES_OUT_DIR})

# Define the primary output we care about; the generator produces five files but
# we list the first three explicitly so other targets can depend on them.
set(_KAI_SERVICE_HEADER_ID        ${KAI_SERVICES_OUT_DIR}/service_id.h)
set(_KAI_SERVICE_HEADER_TOPOLOGY  ${KAI_SERVICES_OUT_DIR}/service_topology.h)
set(_KAI_SERVICE_HEADER_CHECKS    ${KAI_SERVICES_OUT_DIR}/service_static_checks.h)
set(_KAI_SERVICE_HEADER_META      ${KAI_SERVICES_OUT_DIR}/service_meta.h)
set(_KAI_SERVICE_HEADER_PHF       ${KAI_SERVICES_OUT_DIR}/service_name_phf.h)

add_custom_command(
    OUTPUT  ${_KAI_SERVICE_HEADER_ID}
            ${_KAI_SERVICE_HEADER_TOPOLOGY}
            ${_KAI_SERVICE_HEADER_CHECKS}
    BYPRODUCTS
            ${_KAI_SERVICE_HEADER_META}
            ${_KAI_SERVICE_HEADER_PHF}
    COMMAND ${CMAKE_COMMAND} -E echo "[CodeGen] Generating service headers"
    COMMAND $<TARGET_FILE:kai-service-gen>
            -y ${KAI_SERVICES_YAML}
            -o ${KAI_SERVICES_OUT_DIR}
    DEPENDS ${KAI_SERVICES_YAML} kai-service-gen
    COMMENT "Generating Service Registry headers from services.yaml"
    VERBATIM)

# Make header generation part of the default build so that all sources
# including <service_id.h> are guaranteed to have the header generated
# before compilation.
add_custom_target(generate_service_headers ALL
    DEPENDS ${_KAI_SERVICE_HEADER_ID})

# Make headers visible to all subsequent targets.
include_directories(${KAI_SERVICES_OUT_DIR})

# Optionally, let every target of interest depend on the headers.  We cannot
# reference the `core` library here because it may not be declared yet when this
# script is included.  Instead, rely on implicit dependency via include path and
# build graph: any target that includes these headers will automatically depend
# on them through CMake's implicit dependency scanning. 