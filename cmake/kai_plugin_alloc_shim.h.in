// kai_plugin_alloc_shim.h - auto-generated by <PERSON><PERSON><PERSON> at configure time.
// Injected via -include flag for every plugin target to ensure all operator
// new/delete calls inside the plugin are routed through <PERSON>'s arena allocator.
//
// This header is intentionally minimal and header-only to avoid symbol bloat.
// If a plugin author needs a custom allocator they can disable this shim by
// compiling with -DKAI_DISABLE_PLUGIN_ALLOC_SHIM.

#ifndef KAI_DISABLE_PLUGIN_ALLOC_SHIM
#define KAI_DISABLE_GLOBAL_NEW  // Prevent double definitions from core header

#include <cstddef>
#include "core/memory/arena_allocator.h"

// All definitions marked inline to permit multiple inclusion across TUs within
// the same plugin without violating the ODR.
namespace kai_plugin_alloc_shim {
using namespace launcher::core::memory;
} // namespace kai_plugin_alloc_shim

inline void* operator new(std::size_t size) {
    if (void* p = launcher::core::memory::alloc(size)) {
        return p;
    }
    throw std::bad_alloc();
}
inline void* operator new[](std::size_t size) {
    if (void* p = launcher::core::memory::alloc(size)) {
        return p;
    }
    throw std::bad_alloc();
}
inline void* operator new(std::size_t size, const std::nothrow_t&) noexcept {
    return launcher::core::memory::alloc(size);
}
inline void* operator new[](std::size_t size, const std::nothrow_t&) noexcept {
    return launcher::core::memory::alloc(size);
}

inline void operator delete(void* ptr) noexcept {
    launcher::core::memory::free(ptr);
}
inline void operator delete[](void* ptr) noexcept {
    launcher::core::memory::free(ptr);
}
inline void operator delete(void* ptr, std::size_t) noexcept {
    launcher::core::memory::free(ptr);
}
inline void operator delete[](void* ptr, std::size_t) noexcept {
    launcher::core::memory::free(ptr);
}

#endif // KAI_DISABLE_PLUGIN_ALLOC_SHIM 