option(KAI_ENABLE_VPATCH "Enable hot-patch verifier table" OFF)
option(KAI_ENABLE_MUX_QUEUE "Enable MuxQueue backend (priority queue)" ON) # Default ON as of Slice-2 GA – strict priority guarantees
option(KAI_ENABLE_FLATSNAPSHOT_SIGN "Enable signature (Ed25519) on FlatSnapshot files" ON) # Default ON – Ed25519 via libsodium
option(KAI_ENABLE_THINLTO "Enable Thin-LTO (-flto=thin)" OFF)

# Convert ON/OFF booleans to 1/0 macros so that `#if KAI_ENABLE_*` is valid even with -Wundef.
foreach(_var IN ITEMS KAI_ENABLE_VPATCH KAI_ENABLE_MUX_QUEUE KAI_ENABLE_FLATSNAPSHOT_SIGN KAI_ENABLE_THINLTO)
    if(${_var})
        add_compile_definitions(${_var}=1)
    else()
        add_compile_definitions(${_var}=0)
    endif()
endforeach() 