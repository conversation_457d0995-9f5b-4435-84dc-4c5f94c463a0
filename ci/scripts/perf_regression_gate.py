#!/usr/bin/env python3
"""Kai perf regression gate.
Compares current benchmark JSON (google-benchmark) against a baseline
and fails when real_time deviation exceeds threshold (default 5%).
"""
import argparse, json, sys, math, pathlib


def load(path: pathlib.Path):
    with path.open() as fh:
        data = json.load(fh)
    return {
        bench["name"]: bench for bench in data["benchmarks"] if "real_time" in bench
    }


def main():
    parser = argparse.ArgumentParser(description="Kai perf regression gate")
    parser.add_argument("--baseline", required=True)
    parser.add_argument("--current", required=True)
    parser.add_argument(
        "--threshold",
        type=float,
        default=0.05,
        help="Allowed relative drift (0.05 => 5%)",
    )
    args = parser.parse_args()

    base = load(pathlib.Path(args.baseline))
    cur = load(pathlib.Path(args.current))

    failing = []

    for name, cur_b in cur.items():
        if name not in base:
            print(
                f"::warning ::Benchmark '{name}' not present in baseline – new benchmark? Skipping."
            )
            continue
        base_time = base[name]["real_time"]
        cur_time = cur_b["real_time"]
        if base_time <= 0:
            # Placeholder baseline – skip until real numbers recorded.
            print(f"::notice ::Skipping '{name}' – baseline is placeholder (0).")
            continue
        ratio = (cur_time - base_time) / base_time
        if abs(ratio) > args.threshold:
            failing.append((name, base_time, cur_time, ratio))

    if failing:
        print("::error ::Performance regression detected:")
        for name, base_time, cur_time, ratio in failing:
            pct = ratio * 100
            print(f"  {name}: {base_time:.2f} → {cur_time:.2f} ({pct:+.2f} %) ")
        sys.exit(1)

    print("✅ Performance within threshold (±{:.1%}).".format(args.threshold))


if __name__ == "__main__":
    main()
