#!/usr/bin/env bash
# Kai notarisation simulation helper.
# Signs FlatSnapshot, dylib and other security-critical artefacts with an ad-hoc certificate,
# verifies the signature using the system verifier, then performs a mock notarisation step.
# Fails CI on the first error. Usage:
#   ci/scripts/notarisation_sim.sh [PATH_TO_ARTEFACT_DIR]
#
# PATH_TO_ARTEFACT_DIR defaults to <repo>/build/bin – the location used by the Release job.
# The script is intentionally dependency-free and must run on GitHub macOS runners.

set -euo pipefail

ROOT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )/../.." && pwd )"
ARTEFACT_DIR="${1:-"$ROOT_DIR/build/bin"}"

if [[ ! -d "$ARTEFACT_DIR" ]]; then
    echo "::error::Artefact directory $ARTEFACT_DIR does not exist"
    exit 1
fi

echo "🔍 Searching for artefacts inside $ARTEFACT_DIR …"
# Collect candidate files – executables, dylibs, FlatSnapshots (extensions may evolve).
mapfile -t artefacts < <(find "$ARTEFACT_DIR" \
    -type f \
    \( -perm -111 -o -name '*.dylib' -o -name '*.snapshot' -o -name '*.fsnap' -o -name '*.snap' \) \
    -not -path '*_deps/*')

if [[ ${#artefacts[@]} -eq 0 ]]; then
    echo "No signable artefacts found – exiting successfully." && exit 0
fi

echo "Found ${#artefacts[@]} artefact(s). Proceeding with ad-hoc signing and verification…"

# --- helpers ---------------------------------------------------------------
function mock_notary() {
    local file="$1"
    if [[ ! -s "$file" ]]; then
        echo "::error file=$file::Zero-byte file – cannot be notarised"
        return 1
    fi
    local tid="MOCK-$(shasum -a 256 "$file" | cut -c1-8)"
    echo "notarytool: Submitted $file (ticket id: $tid) – Status: Accepted (simulated)"
    return 0
}

function sign_and_verify() {
    local file="$1"

    # 1. Sign with ad-hoc identity. Hardened Runtime + timestamp to mimic real flow.
    codesign --force --options runtime --timestamp --sign - "$file"

    # 2. System verification (deep/strict) must succeed.
    codesign --verify --deep --strict --verbose=2 "$file"

    # 3. Simulated notarisation.
    mock_notary "$file"

    echo "✅ $file – signed, verified, notarisation simulated."
}

for artefact in "${artefacts[@]}"; do
    sign_and_verify "$artefact"
done

echo "🎉 Notarisation simulation completed successfully." 