#!/usr/bin/env bash
# Kai codesign verification helper. Fails CI if any binary, dylib, or snapshot is not properly signed.
set -euo pipefail
ROOT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )/../.." && pwd )"
BIN_DIR="$ROOT_DIR/build/bin"

function verify_file() {
    local file="$1"
    # codesign --verify returns non-zero on failure.
    if ! codesign --verify --deep --strict "$file"; then
        echo "::error file=$file::codesign verify failed"
        exit 1
    fi
    # spctl assessment simulates Gatekeeper notarisation check.
    if ! spctl --assess -vv "$file"; then
        echo "::warning file=$file::spctl assessment failed (expected for adhoc signature)"
    fi
}

# Discover executable or dylib targets (+ snapshot files)
mapfile -t files < <(find "$BIN_DIR" -type f \( -perm -111 -o -name '*.dylib' -o -name '*.snapshot' \))

if [[ ${#files[@]} -eq 0 ]]; then
    echo "No binaries found under $BIN_DIR – nothing to verify."
    exit 0
fi

for f in "${files[@]}"; do
    verify_file "$f"
    echo "Verified $f"
done

echo "✅ Codesign verification completed successfully." 