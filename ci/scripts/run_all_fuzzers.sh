#!/usr/bin/env bash
# ---------------------------------------------------------------------------
# run_all_fuzzers.sh  <fuzzer-bin | name>  [duration=30]  [min_execs_per_sec=5000]
#
# Executes a single libFuzzer binary for the requested duration and verifies
# that the throughput meets the minimum exec/s threshold.  Accepts either the
# bare executable name (assumed to live in build/bin/) or an explicit path.
# ---------------------------------------------------------------------------
set -euo pipefail

if [[ $# -lt 1 ]]; then
  echo "Usage: $0 <fuzzer> [duration] [min_execs_per_sec]" >&2
  exit 1
fi

FUZZER=$1; shift || true
DURATION=${1:-30}
MIN_RATE=${2:-5000}

# Resolve binary path when only a basename is supplied.
if [[ ! -f "$FUZZER" ]]; then
  FUZZER="build/bin/$FUZZER"
fi

if [[ ! -x "$FUZZER" ]]; then
  echo "Fuzzer binary '$FUZZER' not found or not executable" >&2
  exit 1
fi

echo "Running $FUZZER for $DURATION s (expect ≥ $MIN_RATE exec/s)"

# Capture stats; libFuzzer prints a summary line with 'exec/s:'.
"$FUZZER" -max_total_time="$DURATION" -runs=0 -print_final_stats=1 2>&1 | tee fuzz_run.log

# Extract numeric throughput (last occurrence is final rate).
RATE=$(grep -oE "exec/s: *[0-9]+" fuzz_run.log | tail -n1 | awk '{print $2}')

if [[ -z "$RATE" ]]; then
  echo "Could not parse exec/s from fuzzer output" >&2
  exit 1
fi

echo "Throughput: $RATE exec/s"
if (( RATE < MIN_RATE )); then
  echo "FAIL – throughput below threshold ($MIN_RATE exec/s)" >&2
  exit 1
fi

echo "PASS" 