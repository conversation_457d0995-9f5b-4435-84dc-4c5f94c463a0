{
  "context": {
    "date": "2025-05-31T22:01:20+07:00",
    "host_name": "AS-FV7DH27614",
    "executable": "./bin/adaptive_cache_hit_bench",
    "num_cpus": 14,
    "mhz_per_cpu": 24,
    "cpu_scaling_enabled": false,
    "caches": [
      {
        "type": "Data",
        "level": 1,
        "size": 65536,
        "num_sharing": 0
      },
      {
        "type": "Instruction",
        "level": 1,
        "size": 131072,
        "num_sharing": 0
      },
      {
        "type": "Unified",
        "level": 2,
        "size": 4194304,
        "num_sharing": 1
      }
    ],
    "load_avg": [
      15.9399,
      10.5762,
      8.74365
    ],
    "library_build_type": "release"
  },
  "benchmarks": {
    "name": "BM_AdaptiveCache_Hit/iterations:1000000",
    "family_index": 0,
    "per_family_instance_index": 0,
    "run_name": "BM_AdaptiveCache_Hit/iterations:1000000",
    "run_type": "iteration",
    "repetitions": 1,
    "repetition_index": 0,
    "threads": 1,
    "iterations": 1000000,
    "real_time": 2.3701246827840805,
    "cpu_time": 2.3710000000001230,
    "time_unit": "ns"
  }
}
{
  "context": {
    "date": "2025-05-31T22:01:20+07:00",
    "host_name": "AS-FV7DH27614",
    "executable": "./bin/adaptive_cache_hit_bench",
    "num_cpus": 14,
    "mhz_per_cpu": 24,
    "cpu_scaling_enabled": false,
    "caches": [
      {
        "type": "Data",
        "level": 1,
        "size": 65536,
        "num_sharing": 0
      },
      {
        "type": "Instruction",
        "level": 1,
        "size": 131072,
        "num_sharing": 0
      },
      {
        "type": "Unified",
        "level": 2,
        "size": 4194304,
        "num_sharing": 1
      }
    ],
    "load_avg": [
      15.9399,
      10.5762,
      8.74365
    ],
    "library_build_type": "release"
  },
  "benchmarks": {
    "name": "BM_VerifierPipeline/iterations:1000000",
    "family_index": 0,
    "per_family_instance_index": 0,
    "run_name": "BM_VerifierPipeline/iterations:1000000",
    "run_type": "iteration",
    "repetitions": 1,
    "repetition_index": 0,
    "threads": 1,
    "iterations": 1000000,
    "real_time": 0.28558261692523956,
    "cpu_time": 0.28200000000011549,
    "time_unit": "ns"
  }
}
{
  "context": {
    "date": "2025-05-31T22:01:20+07:00",
    "host_name": "AS-FV7DH27614",
    "executable": "./bin/adaptive_cache_hit_bench",
    "num_cpus": 14,
    "mhz_per_cpu": 24,
    "cpu_scaling_enabled": false,
    "caches": [
      {
        "type": "Data",
        "level": 1,
        "size": 65536,
        "num_sharing": 0
      },
      {
        "type": "Instruction",
        "level": 1,
        "size": 131072,
        "num_sharing": 0
      },
      {
        "type": "Unified",
        "level": 2,
        "size": 4194304,
        "num_sharing": 1
      }
    ],
    "load_avg": [
      15.9399,
      10.5762,
      8.74365
    ],
    "library_build_type": "release"
  },
  "benchmarks": {
    "name": "BM_RuntimeScan200/iterations:1/manual_time",
    "family_index": 0,
    "per_family_instance_index": 0,
    "run_name": "BM_RuntimeScan200/iterations:1/manual_time",
    "run_type": "iteration",
    "repetitions": 1,
    "repetition_index": 0,
    "threads": 1,
    "iterations": 1,
    "real_time": 3803500.0000000000,
    "cpu_time": 9626999.9999998305,
    "time_unit": "ns"
  }
}
