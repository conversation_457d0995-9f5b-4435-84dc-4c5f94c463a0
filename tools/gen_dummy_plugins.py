#!/usr/bin/env python3
"""
Generate a large number of dummy Kai plugins for stress-testing cold-start and
UI scalability.  By default the script emits lightweight `*.toml` manifest files
suitable for the RuntimeManagerSvc `loadManifests()` fast-path.  An optional
`--mode dylib` switch builds actual shared libraries (slow!) for scenarios
where the full dlopen() validation path must be exercised.

Usage examples:
  # Generate 5 000 manifest-only plugins under ./Plugins
  ./tools/gen_dummy_plugins.py --count 5000 --out ./Plugins

  # Generate 1 000 real dylib plugins (requires clang)
  ./tools/gen_dummy_plugins.py --count 1000 --out ./Plugins --mode dylib
"""
import argparse
import pathlib
import subprocess
import sys

template_cpp = r"""
#include "core/plugins/abi.h"
#include <cstring>
extern "C" {
KaiPluginInfo kai_plugin_get_info() {{
    KaiPluginInfo info{{}};
    std::strncpy(info.id, "{plugin_id}", sizeof(info.id));
    info.abi_major = kKaiAbiMajor;
    info.abi_minor = kKaiAbiMinor;
    info.runtime   = KAI_RUNTIME_NULL;
    std::memset(info.capabilities, 0, sizeof(info.capabilities));
    return info;
}}
}
"""


def build_dylib(
    src_cpp: pathlib.Path, out_dylib: pathlib.Path, clang: str, cxxflags: str
):
    cmd = [
        clang,
        "-std=c++17",
        "-shared",
        "-fPIC",
        "-O2",
        "-DNDEBUG",
        "-x",
        "c++",
        str(src_cpp),
        "-o",
        str(out_dylib),
        *cxxflags.split(),
    ]
    try:
        subprocess.check_call(cmd, stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
    except subprocess.CalledProcessError as exc:
        print(f"Failed to compile {out_dylib}: {exc}", file=sys.stderr)
        sys.exit(1)


def generate_manifest(path: pathlib.Path, plugin_id: str):
    path.write_text(f'id = "{plugin_id}"\nruntime = "null"\n', encoding="utf-8")


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--count", type=int, default=5000, help="number of plugins to generate"
    )
    parser.add_argument("--out", type=str, default="./Plugins", help="output directory")
    parser.add_argument(
        "--mode",
        choices=["manifest", "dylib"],
        default="manifest",
        help="generation mode",
    )
    parser.add_argument(
        "--clang",
        type=str,
        default="clang",
        help="C/C++ compiler (only when --mode dylib)",
    )
    parser.add_argument(
        "--cxxflags",
        type=str,
        default="",
        help="extra CXX flags for clang when building dylibs",
    )
    args = parser.parse_args()

    out_dir = pathlib.Path(args.out).expanduser().resolve()
    out_dir.mkdir(parents=True, exist_ok=True)

    temp_src = None
    try:
        for i in range(args.count):
            plugin_id = f"dummy_{i}"
            if args.mode == "manifest":
                manifest_path = out_dir / f"{plugin_id}.toml"
                generate_manifest(manifest_path, plugin_id)
            else:
                # Build real dylib
                if temp_src is None:
                    temp_src = pathlib.Path("/tmp/dummy_plugin.cpp")
                src_cpp = temp_src
                src_cpp.write_text(
                    template_cpp.format(plugin_id=plugin_id), encoding="utf-8"
                )
                dylib_name = (
                    f"{plugin_id}.dylib"
                    if sys.platform == "darwin"
                    else f"{plugin_id}.so"
                )
                build_dylib(src_cpp, out_dir / dylib_name, args.clang, args.cxxflags)
        print(f"Generated {args.count} dummy plugins in {out_dir}")
    finally:
        if temp_src and temp_src.exists():
            try:
                temp_src.unlink()
            except OSError:
                pass


if __name__ == "__main__":
    main()
