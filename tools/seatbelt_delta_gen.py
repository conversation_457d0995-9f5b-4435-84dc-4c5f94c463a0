#!/usr/bin/env python3
"""
seatbelt_delta_gen.py — build-time generator for Seatbelt profile allow-list
############################################################################
Reads a newline-separated list of allowed sandbox profile strings and emits a
header containing a branch-free perfect-hash lookup.  The implementation is a
simple two-phase search for a seed such that `(fnv1a32(key) ^ seed) % N` is
collision-free where `N` is the number of entries rounded up to the next power
of two.

Usage (invoked from CMake):
    python seatbelt_delta_gen.py <input_profiles.txt> <output_header.h>

The output is written to <output_header.h> and is idempotent – no change when
profiles list is unchanged, so incremental builds remain fast.
"""
from __future__ import annotations

import sys
import pathlib
import random
from typing import List, Tuple

FNV_OFFSET = 0x811C9DC5
FNV_PRIME = 0x01000193

HEADER_PREAMBLE = """// -----------------------------------------------------------------------------
// @file seatbelt_profile.phf.h  (AUTOGENERATED — DO NOT EDIT)
// -----------------------------------------------------------------------------
// Generated by tools/seatbelt_delta_gen.py from resources/seatbelt_profiles.txt
// -----------------------------------------------------------------------------
#pragma once

#include <string_view>
#include <cstdint>

namespace launcher::core::security {
"""

HEADER_EPILOGUE = """
} // namespace launcher::core::security
"""


def fnv1a32(data: str) -> int:
    h = FNV_OFFSET
    for c in data.encode("utf-8"):
        h ^= c
        h = (h * FNV_PRIME) & 0xFFFFFFFF
    return h


def next_pow2(n: int) -> int:
    p = 1
    while p < n:
        p <<= 1
    return p


def find_seed(hashes: List[int], table_size: int) -> int:
    """Brute-force search for XOR seed producing collision-free mapping."""
    attempts = 0
    while True:
        seed = random.getrandbits(32)
        seen = [False] * table_size
        collision = False
        for h in hashes:
            idx = (h ^ seed) & (table_size - 1)  # since table_size is power-of-2
            if seen[idx]:
                collision = True
                break
            seen[idx] = True
        if not collision:
            return seed
        attempts += 1
        if attempts % 100000 == 0:
            random.seed(seed ^ 0xDEADBEEF)  # Stir entropy


def generate_header(profiles: List[str]) -> str:
    hashes = [fnv1a32(p) for p in profiles]
    table_size = next_pow2(len(profiles))
    seed = find_seed(hashes, table_size)

    # Build table initialiser – default slot 0xFFFFFFFF indicates empty
    slots: List[Tuple[int, int]] = [(0xFFFFFFFF, 0xFFFF)] * table_size
    string_pool = ""
    offsets: List[int] = []
    for p in profiles:
        offsets.append(len(string_pool))
        string_pool += p + "\0"

    for i, (profile, h) in enumerate(zip(profiles, hashes)):
        idx = (h ^ seed) & (table_size - 1)
        slots[idx] = (h, offsets[i])

    # Emit C++ source
    lines: List[str] = [HEADER_PREAMBLE]
    lines.append(f"static constexpr uint32_t kSeatbeltPHFSeed = 0x{seed:08X}u;")
    lines.append(
        f"static constexpr uint32_t kSeatbeltPHFTableBits = {table_size.bit_length()-1};"
    )
    lines.append(
        f"static constexpr uint32_t kSeatbeltPHFTableSize = 1u << kSeatbeltPHFTableBits;\n"
    )

    # Struct definition
    lines.append("struct SeatbeltPHFEntry { uint32_t hash; uint16_t offset; };\n")

    # Table initialiser
    lines.append(
        "static constexpr SeatbeltPHFEntry kSeatbeltPHFTable[kSeatbeltPHFTableSize] = {"
    )
    for hash_val, offset in slots:
        lines.append(f"    {{0x{hash_val:08X}u, {offset}}},")
    lines.append("};\n")

    # String pool
    pool_bytes = ", ".join(str(b) for b in string_pool.encode("utf-8"))
    lines.append(
        "static constexpr unsigned char kSeatbeltStringPool[] = {" + pool_bytes + "};\n"
    )

    # Lookup helper
    lookup_func = """
inline bool isSeatbeltProfileAllowed(uint32_t hash, std::string_view profile) noexcept {
    uint32_t idx = (hash ^ kSeatbeltPHFSeed) & (kSeatbeltPHFTableSize - 1);
    const SeatbeltPHFEntry &e = kSeatbeltPHFTable[idx];
    if (e.hash != hash) return false;
    const char *entry = reinterpret_cast<const char*>(kSeatbeltStringPool + e.offset);
    return profile == std::string_view(entry);
}
"""
    lines.append(lookup_func)
    lines.append(HEADER_EPILOGUE)
    return "\n".join(lines)


def main(argv: List[str]):
    if len(argv) != 3:
        print(
            "usage: seatbelt_delta_gen.py <profiles.txt> <out_header.h>",
            file=sys.stderr,
        )
        return 1
    in_path = pathlib.Path(argv[1])
    out_path = pathlib.Path(argv[2])
    if not in_path.exists():
        print(f"error: input file {in_path} not found", file=sys.stderr)
        return 1

    profiles = [
        line.strip()
        for line in in_path.read_text().splitlines()
        if line.strip() and not line.startswith("#")
    ]
    if len(set(profiles)) != len(profiles):
        print("error: duplicate entries in profile list", file=sys.stderr)
        return 1

    header = generate_header(profiles)

    # Write only if contents changed to avoid needless recompiles.
    if out_path.exists() and out_path.read_text() == header:
        return 0
    out_path.write_text(header)
    return 0


if __name__ == "__main__":
    sys.exit(main(sys.argv))
