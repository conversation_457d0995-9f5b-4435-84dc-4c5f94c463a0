#!/usr/bin/env python3
"""kai-seatbelt-gen – tiny helper that converts a plain-text seatbelt profile
into a C/ObjC++ include file containing a static const unsigned char blob and a
size constant.  The host embeds the header into a plugin so the seatbelt binary
travels with the dylib (no extra filesystem lookup).

Usage:
    kai-seatbelt-gen input.sb output.inc [symbol_prefix]

The generator performs *no* actual binary compilation; macOS will accept the
text profile string at runtime via sandbox_init().  For performance, Kai host
hashes the blob and caches a compiled .sb.bin on first use.
"""
import sys, pathlib, textwrap, binascii


def main():
    if len(sys.argv) < 3:
        print("usage: kai-seatbelt-gen input.sb output.inc [prefix]", file=sys.stderr)
        sys.exit(1)

    in_path = pathlib.Path(sys.argv[1])
    out_path = pathlib.Path(sys.argv[2])
    prefix = sys.argv[3] if len(sys.argv) > 3 else in_path.stem

    text = in_path.read_bytes()
    # Ensure file ends with newline
    if not text.endswith(b"\n"):
        text += b"\n"

    # Emit C array
    hex_bytes = ", ".join(f"0x{b:02x}" for b in text)
    array_name = f"{prefix}_seatbelt_blob"
    size_name = f"{prefix}_seatbelt_blob_len"

    header = textwrap.dedent(
        f"""
        // AUTO-GENERATED BY kai-seatbelt-gen – DO NOT EDIT
        #ifdef __cplusplus
        extern "C" {{
        #endif
        __attribute__((visibility("default")))
        const unsigned char {array_name}[] = {{ {hex_bytes} }};
        __attribute__((visibility("default")))
        const unsigned int  {size_name}  = sizeof({array_name});
        #ifdef __cplusplus
        }}
        #endif
    """
    )

    out_path.write_text(header)
    print(
        f"[kai-seatbelt-gen] Wrote {out_path} ({len(text)} bytes, symbol={array_name})"
    )


if __name__ == "__main__":
    main()
