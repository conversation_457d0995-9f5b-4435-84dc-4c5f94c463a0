DEEP MODE – comprehensive scan of `src/` (core + UI)  
❶ Scope  
• Looked for “TODO”, “NOT_IMPLEMENTED”, empty-stub returns (`return {}`) and obvious placeholders.  
• Ignored build/third-party trees.  
• Cited files use format start:end:filepath.

──────────────────────────────────────────────────────────────────────────────
A. CORE-LAYER GAPS (high impact)
1. Backend feature stubs – `UnifiedChatBackend`
```82:117:src/core/chat/unified_chat_backend.cpp
// Stubs for optional APIs – currently return NOT_IMPLEMENTED
void UnifiedChatBackend::searchMentionableItems(...)
void UnifiedChatBackend::searchHistory(...)
void UnifiedChatBackend::requestLLMSuggestions(...)
```
Impact: UI completion panel always falls back to empty result or error; user-visible.  
Required: asynchronous implementations that respect `CancellationToken`, leverage caching, and stream results.

2. Conversation persistence – `ConversationManager`
```104:114:src/core/chat/conversation_manager.h
// TODO(hole): Implement optional persistence methods save(path) and load(path)
```
Impact: chats lost between launches; blocks History + multi-window workflows.  
Need: serialization (JSON) + versioned schema; integrate with `ConfigService` for storage path.

3. Highlight.js resource handling
```114:138:src/core/renderer/HighlightJSBridge.mm
// TODO: Remove this and make sure we have a proper resource bundle
```
Absolute path fallback breaks notarised builds; violates hardened-runtime.  
Need: move `highlight.min.js` into bundle Resources via CMake & use `[NSBundle pathForResource:]` only.

4. Context error logging
```303:327:src/core/context/context.cpp
// TODO(hole): Log an error or warning here? …
```
Low risk but hurts diagnostics; should use `ERR()` and propagate via Expected<T,E>.

──────────────────────────────────────────────────────────────────────────────
B. UI-LAYER PLACEHOLDERS (medium impact)
1. Mention pop-up skeleton
```5:9:src/ui/chat/mention/MentionViewController.mm
// TODO: Add a Table View or Collection View to display suggestions
```
Table view is actually implemented now – remove outdated comment or finish edge-cases (selection persistence, accessibility).

2. Avatar/secondary text in suggestion list
```70:74:src/ui/chat/macos_chat_ui.mm
// TODO: Add secondary text if available in MentionableItem
```
Requires enriching `core::MentionableItem` and plumbing to UI.

3. ComposerView enhancements
```197:205:src/ui/chat/ComposerView.mm   – placeholder text support (macOS 10.14+)
693:705:src/ui/chat/ComposerView.mm   – “Smart Context Extraction”
1283:1293:src/ui/chat/ComposerView.mm – context heuristics
```
Functional but UX polish missing; contextual extraction ties into Backend #1.

4. Sidebar symbol pool auto-generation
```63:66:src/ui/chat/sidebar_symbol_pool.mm
// TODO(assistant): expand further with script-generated list.
```

5. Menu-bar “Stop Generating” enable/disable
```178:182:src/ui/macos/menu_bar_manager.mm
// TODO(onhao): enable/disable "Stop Generating" item dynamically.
```
Requires subscription to backend stream-state events.

──────────────────────────────────────────────────────────────────────────────
C. OTHER LOW-PRIORITY ITEMS
• `return {}` fall-throughs in various managers (registry, search_cache, etc.) are intentional “empty result” paths, not stubs.  
• Tests/bench/third-party have many TODOs – safe to ignore.

──────────────────────────────────────────────────────────────────────────────
❷ OPTION SPACE & TRADE-OFFS

1. Implement full backend search/completion API
   a) Local-only: use SQLite FTS on conversation log + Contacts.framework for mentions.  
   b) Hybrid: same as (a) + optional cloud call (OpenAI / Anthropic) for LLM suggestions.  
   c) Plugin-based: expose capability-checked hook so external plugins supply results.

   Pros | Cons
   -----|------
   a) no network, easy to harden | quality limited, heavier on local indexing
   b) best quality/UX | network latency, privacy, entitlements
   c) extensible, aligns with plugin architecture | more surface area, needs manifest/schema work

   Recommendation: **b) Hybrid** – implement local FTS first (unblocks history/mention) then fall back to provider → LLM for deeper suggestions. Wrap in `CapabilitySet::kCompletion`.

2. Conversation persistence
   Options:  
   • JSON per-conversation file (one file per ID)  
   • Single SQLite with normalized tables  
   • Append-only log file (cheap, fast restore)

   JSON-file approach matches existing `exportToJson()` and keeps dependencies zero; use `~/Library/Application Support/MicroLauncher/Conversations/<id>.json`. Add `Expected<void,Error>` return values.

3. Resource bundling
   Use CMake `configure_file` + `add_custom_command` to copy `highlight.min.js` into `Contents/Resources/`. Remove absolute path fallback to satisfy notarization.

4. Menu-bar state syncing
   Provide `AppEventBus::StreamStateChanged(active)` published by backend; `MenuBarManager` subscribes (weak delegate) and toggles menu item.

5. ComposerView smart context
   After Backend 1 is live, reuse the same FTS API to grab last N tokens before caret; keep heuristics in C++ helper for testability.

──────────────────────────────────────────────────────────────────────────────
❸ NEXT-STEP ORDERING (critical → nice-to-have)

1. Backend stubs (#A1)  
2. Conversation save/load (#A2)  
3. Menu “Stop Generating” toggle (#B5)  
4. Highlight.js bundling (#A3)  
5. Avatar/secondary text plumbing (#B2)  
6. ComposerView placeholder + context heuristics (#B3)  
7. Sidebar symbol auto-gen & comment hygiene

Once you decide which path to take (e.g., “go with hybrid backend + JSON persistence”), say “go” and I’ll switch to Go mode to implement.
