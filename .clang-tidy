Checks: >
  -*,
  clang-analyzer-* ,
  bugprone-* ,
  performance-* ,
  modernize-* ,
  readability-* ,
  objc-arc-* ,
  darwin-* ,
  misc-unused-parameters ,
  clang-diagnostic-unused-variable

# Treat definite bug-class groups as errors–failing CI while allowing stylistic
# issues to surface as warnings.
WarningsAsErrors: 'clang-analyzer-*,bugprone-*,performance-*'

# Analyse only our code, skip system headers and build trees
HeaderFilterRegex: '^(src)/(core|ui)/.*'

# Delegate all rewrites to clang-format for consistency
FormatStyle: file

CheckOptions:
  - key: readability-identifier-naming.NamespaceCase
    value: CamelCase
  - key: readability-identifier-naming.ClassCase
    value: PascalCase
  - key: readability-identifier-naming.FunctionCase
    value: camelBack
  - key: misc-unused-parameters.IgnoreUnusedParametersPattern
    value: '^_' 