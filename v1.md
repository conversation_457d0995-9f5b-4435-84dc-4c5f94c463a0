DEEP-MODE ANALYSIS  
==================  
Target: `plugin-todo.md` (Kai Plugin System design draft)

I. 30 000-ft Assessment

---

1. Strengths  
   • Clear phase roadmap (P0 → P8) with ownership table.  
   • Adheres to capability-based security & hardened-runtime constraints.  
   • Future-proof: supports JS, Wasm, native/XPC back-ends.  
   • Service-kernel foundation (IService + Registry) decouples lifetime.

2. Pain-points / Friction  
   a. **Phase fragmentation & critical-path length** – nine phases across 10-12 weeks → schedule risk, context-switching overhead.  
   b. **Duplicate concerns across layers** – JsEngineService & WasmRuntimeService will re-implement similar host-API glue, capability checks, watchdogs.  
   c. **Memory pressure & perf** – JSC contexts per plugin, Wasmtime instances, PMR allocators, etc., could bloat RSS if not pooled.  
   d. **Code size & compile times** – C++20 modules + header-only algorithms + Wasmtime = bigger link step; BMI cache invalidations risk.  
   e. **Security enforcement scattering** – CapabilityManager, PolicyEngine, seatbelt generator, signature verifier each live in different folders; duplication possible.

II. Core Design Drivers

---

• Simplicity: minimise concept count per layer.  
• Extensibility: adding a new runtime (e.g., Python WASI) must touch ≤2 subsystems.  
• Scalability: O(#plugins) memory & start-up cost; async initialisation.  
• Performance: low-latency plugin calls (< 5 ms round-trip), arena allocation, zero virtual hops on hot paths.

III. Deep-Dive per Subsystem & Improvement Options

---

A. Service Kernel (P0)  
 Current: `ServiceRegistry` resolves dependencies at run-time.  
 Options

1. **Static DAG description** (constexpr)
    - Pros: compile-time topo sort ⇒ zero start-up overhead; mis-wiring caught by compiler.
    - Cons: less dynamic; plugins loaded at run-time still need dynamic reg.
2. **Hybrid**: core services in a constexpr DAG; plugin-provided services attach to a dynamic layer.  
   Best trade-off: Option 2 – keeps core fast, still allows run-time extensibility.

B. Host API Surface & Capability Checks  
 Problem: duplication across JS & Wasm paths.  
 Options

1. **Shared C++ Facade** `HostApiDispatcher` – thin table of std::function <-> capability bit; runtime adapters marshal to JS/Wasm.
2. Code-gen (e.g., json→C++) from `manifest.capabilities` to produce stubs.  
   Best: Option 1 first (simple); evaluate code-gen later.

C. Runtime Abstraction Layer  
 Current: separate `JsEngineService`, `WasmRuntimeService`, `NativePluginService`.  
 Option  
 • Introduce **IRuntime** interface: `{initialize, executeModule, unload}` implemented by JscRuntime, WasmRuntime, DylibRuntime`.  
 PluginManager picks implementation at load-time.

-   Pros: unifies watchdog, memory limits, host-API injection.
-   Cons: Adds one indirection, but we can CRTP + std::variant to eliminate virtuals in hot path.  
    Recommendation: adopt `IRuntime` via `std::variant<JsRuntime, WasmRuntime, DylibRuntime>`; use `std::visit` for dispatch – no vtable, compile-time type safety.

D. Memory Management  
 Observations: arena allocator introduced P0; but each runtime will have internal allocators (JSC, Wasmtime).  
 Improvement  
 • Provide **Per-Plugin MemoryPool** backed by `ArenaAllocatorSvc`, pass custom `malloc` hooks to Wasmtime, and use `JSGlobalContextSetVirtualMachineConfig` for JSC with pooled zones.  
 • Expose `KaiMemoryStats` via diagnostics to tune.

E. Phasing / Milestones  
 Simplify: merge P0b into P0 (modules + arena = foundation work), and merge P6 observability tasks into earlier phases to aid debuggability.  
 Proposed revised roadmap (7 phases):  
 P0 Core Kernel & Modules  
 P1 Runtime Abstraction + JS MVP  
 P2 Capability/Watchdog + HostAPI dispatcher  
 P3 TypeScript SDK & EventBus  
 P4 WASM + Benchmarks  
 P5 ABI Boundary + Native/XPC extraction  
 P6 Security Hardening & CI notarisation

F. Build & Toolchain  
 • Enable **Thin-LTO + ICF** in default Release early; not just at P7 → catch size regressions sooner.  
 • Cache Wasmtime & JSC BMIs; use CCache + clang modules cache in CI.

G. Security / Policy  
 • Consolidate checks in a single **PolicyEngineService** (phase P2), invoked by HostApiDispatcher; seatbelt profiles generated here to avoid scattering security logic.  
 • Integrate Static Code signature verification (SecStaticCode) into PluginManager at P1 to prevent enlarging attack surface later.

IV. Recommended Adjustments to `plugin-todo.md`

---

1. Collapse phases as per section E; update tables & timelines.
2. Introduce `IRuntime` variant abstraction; move watchdog, host-API injection, capability gating under it.
3. Add HostApiDispatcher diagram & description under P2.
4. Move PolicyEngineService definition earlier (P2) and state it owns capability checks + seatbelt generation.
5. Specify per-plugin MemoryPool allocation strategy; note RSS target budgets (e.g., JS ≤20 MiB, Wasm ≤10 MiB by default).
6. Elevate Thin-LTO, ICF, BOLT optimisation flags to P0 build matrix.
7. Add acceptance criterion: "Plugin round-trip call latency ≤ 5 ms (p95) on 4-core MacBookAir M2".
8. Clarify compile-time DAG vs run-time registration hybrid in ServiceRegistry section.
9. Provide code-sample of `kai_plugin_get_info` C shim and equivalent JS manifest snippet for symmetry.
10. Append Risk: "HostApi surface explosion" with mitigation "capability code-gen & dispatcher test harness".

V. Trade-off Summary (Why this path)

---

• Variant-based `IRuntime` removes duplicated logic yet avoids vtables (cache-friendlier).  
• Earlier instrumentation (EventBus/Diagnostics moved up) shortens feedback loops → better scalability tuning.  
• Consolidated security layer lowers auditing burden and future runtime additions (e.g., Python WASI, LuaJIT).  
• Fewer phases reduce management overhead, still logically grouped.

VI. Next Steps

---

If you agree, we can:

1. Update `plugin-todo.md` with the condensed roadmap + structural edits.
2. Scaffold `core/runtime/runtime_variant.h` (`std::variant`-based) and migrate existing minimal JS prototype into it.
3. Add HostApiDispatcher skeleton with capability enum + bitset.

Say **"go"** to switch to Go-mode and begin applying these changes; or request further deep analysis on any specific subsystem.

# Deep-mode analysis – Kai plugin system implementation plan (plugin-todo.md)

Below is a structured, soup-to-nuts review of the current plan, highlighting strengths, pain-points, and optimisation opportunities. For each area I list (1) observations, (2) risks / limitations, and (3) concrete improvement options with pros, cons, and trade-offs. A final section proposes an updated roadmap snapshot that folds the best options together.

──────────────────────────────────────────────────────────────────────────

1. Phase structure & milestone granularity
   ──────────────────────────────────────────────────────────────────────────
   Observations  
   • Five numbered phases (R0β…R5) give clear sequencing but hide heavy cross-couplings (e.g. allocator svc first used only in R4).  
   • Some items ("seatbelt template", "PolicyEngineSvc") are critical-path yet appear late, risking re-work.

Improvement options  
A. Merge "skeleton" security pieces into the same phase that first executes untrusted code.

-   Minimises retreat work; keeps threat-model honest.  
    – Phase R2 becomes heavier; schedule risk unless resourced.

B. Adopt "vertical slice" iterations: for each runtime (Native/JSC/Wasm) deliver a thin but end-to-end slice before broadening.

-   Validates integration earlier, surfaces hidden cost.  
    – Less code reuse between slices if not managed.

Recommendation: Option A + micro-vertical slices inside R2/R4 to keep security gate aligned with first load of each runtime.

────────────────────────────────────────────────────────────────────────── 2. ServiceRegistry & IService model
──────────────────────────────────────────────────────────────────────────
Observations  
• Registry is header-only, DAG-aware → good compile-time perf.  
• All services share lifecycle states; but some services (ArenaAllocatorSvc) are fundamental, while others (Diagnostics) are optional.

Risks / limitations  
• "Everything is a service" can penalise cache locality: vtable hops at every lookup; indirections for ultra-hot paths (alloc, event bus).  
• Static-initialisation order fiasco if singletons creep in.

Improvement options  
A. Introduce "CoreKernel" (plain struct with pointers) for hot-path services (ArenaAllocator, EventBus, Executor). Expose it as an immutable aggregate handed to every IService during `start()`.

-   Zero virtual hops for the hottest deps; improved cache locality.  
    – Slightly more boilerplate in service constructors.

B. Keep current design but add `final` + inline service adaptors (`ArenaAllocator::instance()`) generated at build time.

-   Minimal code churn.  
    – Still one vtable indirection.

Recommendation: Option A aligns with "minimise indirections / pointer churn" principle and simplifies DI for plugins that primarily need the allocator and event bus.

────────────────────────────────────────────────────────────────────────── 3. Memory management strategy
──────────────────────────────────────────────────────────────────────────
Observations  
• Hybrid arena + TLSF is solid; however hooking runtimes (JSC, Wasmtime) early is essential to measure RSS.  
• Plan adds ArenaAllocatorSvc in R0β but runtimes only in R4 → long feedback loop.

Improvement options  
A. Move Wasmtime proof-of-concept (alloc hooks only) into R2; piggyback JS via custom `malloc` zone in the same phase.

-   Lets us validate the pool across languages 8–10 weeks earlier.  
    – Increases R2 scope modestly (~2 dev-weeks).

B. Provide fake load test harness that pounds allocator with JS-like and Wasm-like patterns before true runtimes land.

-   Keeps original schedule intact.  
    – Synthetic patterns can miss edge cases.

Recommendation: Option A – early, real integration beats synthetic benchmarks for fragmentation and cache-miss data.

────────────────────────────────────────────────────────────────────────── 4. Capability & Policy engine
──────────────────────────────────────────────────────────────────────────
Observations  
• Packed capability type (16|8|8 bits) is cache-friendly.  
• PolicyEngineSvc appears in R2 but seatbelt enforcement deferred to R5.

Risks  
• Any host API shipped before R5 risks accidental privilege escalation.

Improvement options  
A. Staged capability enforcement:  
 – R2: compile-time gating macros (`REQUIRES(cap_net)`) that assert + unit-test; runtime still permissive.  
 – R3: soft-deny mode (logs + metrics).  
 – R4: hard-deny (returns KaiError).

-   Gradual rollout; test coverage builds up.

B. Pull seatbelt generator to R3 so that automated profile diffing protects against drift early.

-   Early CI guardrail.  
    – Requires more upfront tooling.

Recommendation: A+B together – use incremental enforcement but build the generator sooner so drift never slips through CI.

────────────────────────────────────────────────────────────────────────── 5. IDL & code-gen
──────────────────────────────────────────────────────────────────────────
Observations  
• `kai.idl` + code-gen tool in R0β is great but success hinges on ergonomics (dev must love it).  
• Plan does not specify language – e.g. protobuf, Cap'n Proto, bespoke.

Improvement options  
A. Adopt static-reflection friendly single-header (e.g. `frozen::Printer`) to emit constexpr descriptor → zero parsing at runtime.

-   Compile-time, zero-alloc introspection for host APIs.  
    – Limited schema features vs full ASN.

B. Use templated DSL in C++20 modules; generate TS bindings via clang's libTooling pass.

-   No external dep; leverages modules.  
    – Tooling effort heavier.

Recommendation: Option A (constexpr descriptors) hits "no dynamic alloc" and is faster to prototype; revisit B post-GA.

────────────────────────────────────────────────────────────────────────── 6. JavaScriptCore & Wasmtime runtimes
──────────────────────────────────────────────────────────────────────────
Observations  
• Separate services (`JsEngineService`, `WasmRuntimeService`) in R4.  
• Watchdog and memory pool appear same phase – good.

Risks  
• JSContext per plugin can explode RSS (JSC's baseline is ~3–4 MB).  
• Wasm runtime two-phase initialisation (compile vs instantiate) could stall UI thread if unplanned.

Improvement options  
A. Introduce per-runtime object pools (reusable JSContext/Store) with LRU eviction; tied to plugin capability "needs_dedicated_vm".

-   Cuts cold-load cost; avoids duplication.  
    – Adds complexity to lifecycle; must clear global objects safely.

B. Use shared JSCore JIT warm-up pool loaded during splash (R2), then hand-off contexts to plugins.

-   Faster first paint; test harness sooner.  
    – Slight memory burn when plugins disabled.

Recommendation: lightweight object pool (Option A) gated by capability flag; default to shared until plugin opts-out.

────────────────────────────────────────────────────────────────────────── 7. EventBus throughput target
──────────────────────────────────────────────────────────────────────────
Observations  
• Goal: ≥ 10 k msg/s, < 0.5 ms p95.  
• But no deliverable-phase listed (implemented implicitly in core/events).

Improvement options  
A. Explicit micro-benchmark suite lands in R0β; baseline continuously.

-   Prevents regression later.  
    – Small upfront time.

B. Consider lock-free SPSC ring buffer per topic; fallback to std::mutex on Darwin < 18.

-   Meets latency target with low allocs.  
    – Require caution with cache line padding.

Recommendation: Land benchmark in R0β; start with ring buffer impl behind traits so alt queue is drop-in.

────────────────────────────────────────────────────────────────────────── 8. CI, Codesign & Notarisation
──────────────────────────────────────────────────────────────────────────
Observations  
• Hardening in R5; CI runs ASan/TSan earlier.  
• Codesign verify / notarise only at end.

Risks  
• macOS notarisation often exposes entitlements or hardened-runtime flags that require code fixes. Discovering in R5 == schedule blow-ups.

Improvement options  
A. Integrate "ad-hoc" codesign + `codesign --verify` in **every** CI build from day one; notarise only on release channel.

-   Surfaces hardened runtime conflicts early.  
    – Slight CI time (≈ 5 s).

B. Add `--timestamp=none --strict` during early signing to fail on library-validation mismatches even in Debug.

-   Ensures plugins never import non-notarised frameworks.  
    – Developer machines need proper cert/trust config.

Recommendation: Option A; escalate to B once signing infra stabilises.

────────────────────────────────────────────────────────────────────────── 9. Documentation & SDK
──────────────────────────────────────────────────────────────────────────
Observations  
• Plan focuses on code deliverables; SDK & docs lightly touched.

Improvement options  
A. Generate docs from IDL and service headers each CI run (Doxygen + TypeDoc).

-   Zero drift; plugin authors get instant updated API.  
    – Initial setup overhead.

B. Ship "sample_hello_js" as literate tutorial (Markdown + code snippet tests) validated in CI with "doctest" style.

-   Dogfoods plugin integration; ensures samples compile.

Recommendation: A+B – automation pays dividends long-term.

────────────────────────────────────────────────────────────────────────── 10. Updated roadmap snapshot (high-level)
──────────────────────────────────────────────────────────────────────────
R0β (Core foundation) – unchanged  
• Add EventBus bench; integrate ad-hoc codesign verify.

R2 (Security-aligned plugin skeleton) – now includes:  
• PluginManagerSvc, PolicyEngineSvc (soft enforce).  
• ArenaAllocator hooks for Wasmtime & JSCore minimal harness.  
• Seatbelt generator CLI (emit .sb but not enforced).  
• Shared JSContext pool base class.

R3 (Optimise & Harden) –  
• Thin-LTO/ICF.  
• Seatbelt enforce in "log-only" mode; policy denial metrics.  
• Codesign verify remains mandatory in CI; notarisation on release.

R4 (Feature parity) –  
• Hard-deny capabilities; seatbelt active.  
• JS watchdog, Wasm runtime full.  
• Object-pool / LRU for runtimes.

R5 (Final hardening & GA) – mostly unchanged but less risk.

──────────────────────────────────────────────────────────────────────────
Key take-aways / next steps
──────────────────────────────────────────────────────────────────────────

1. Pull critical security & allocator hooks earlier for real feedback.
2. Flatten hot-path service access with CoreKernel aggregate to cut vtable cost.
3. Adopt compile-time IDL descriptors to avoid runtime parsing/allocs.
4. Integrate ad-hoc codesign checks in every CI build.
5. Stand-up EventBus & allocator micro-benchmarks now to gate performance.

Please review the options above; once you pick the preferred ones I'll switch to **Go mode** to update `plugin-todo.md`, adjust phase tables, and add missing deliverables.

# Deep Mode – Analysis of the four new suggestions and how they fit into the optimised roadmap

## A. Service Kernel – Push `ServiceRegistry` to the very start of R0β

Current state  
• Legacy code paths (e.g. `ModelRegistry::global()`, `HttpClient::instance()`) rely on singleton statics.  
• The roadmap places `ServiceRegistry` in R0β but after several other items, so early-phase code would still call singletons.

Impact & options

A-1 Early insertion (recommended)  
 • Implement `ServiceRegistry` + `IService` first, before any other R0β artefacts.  
 • Provide an **adapter header** (`legacy_singleton_bridge.h`) that forwards the old `::global()` calls to a service fetched from the registry. This lets untouched modules continue to compile while new code uses DI.  
 • Compile-time flag `KAI_DISABLE_SINGLETONS` flips at P2 to fail the build on remaining legacy paths.

Pros:

-   Deterministic start/stop ordering, needed for clean unload / hot-patch.
-   Immediate feedback loop for lifecycle bugs.
-   Enables unit-tests that spin up only the minimal service graph.

Cons / mitigations:  
– Biggest refactor risk; reduce by limiting scope to the handful of singletons that participate in plugin loading (IndexService, ConfigService, EventBusService); the rest can migrate gradually behind the bridge.

Decision: **Adopt A-1**. Move ServiceRegistry implementation + minimal set of essential services to the _top_ of R0β and gate singleton removal behind compile flag.

## B. Plugin Runtime Split – Use a single `RuntimeBase` template

Baseline  
• Roadmap envisions three concrete classes (`NativeRuntime`, `JsRuntime`, `WasmRuntime`) all implementing the `IRuntime` interface; common members (allocator handle, watchdog, capability mask) would be copy-pasted.

Proposal  
`template<class Derived> class RuntimeBase : public IRuntime` holds:

```
ArenaAllocatorHandle*  allocator_;
CapabilityMask         caps_;
WatchdogToken          watchdog_;
Expected<void,E> initCommon(const PluginManifest&);
```

Each concrete runtime inherits via CRTP and implements only the transport-specific parts (load, invoke, GC hooks).

Trade-off analysis

Option B-1 – **Template RuntimeBase (CRTP)**

-   Removes 35-45 % duplicate code.
-   No extra vtbl indirection beyond the `IRuntime` interface already required for polymorphism.
-   Facilitates compile-time `static_cast<Derived*>(this)` for some hot-path helpers.

– Increases header footprint; any change in `RuntimeBase` forces recompilation of all Derived units (mitigated by C++20 Modules exporting `runtime.base`).  
– Adds one extra template parameter to error messages; acceptable.

Option B-2 – Traditional non-template `RuntimeBase` with protected members

-   Simpler diagnostics.  
    – Adds additional vtbl hop (`IRuntime` → `RuntimeBase` → Derived) or forces `RuntimeBase` to be abstract only, regaining duplication.

Recommendation: **Adopt Option B-1** (CRTP). Encapsulate in `module kai.runtime.base;` to localise rebuilds.

## C. Capability Model – Code-gen enum & constexpr lookup

Status quo  
• `capability.h` packs 32-bit IDs, but manifest & host APIs speak strings ("network", "filesystem").  
• Runtime mapping done via hash or binary-search vector → O(log n) + string allocations on hot path.

Proposed flow

1. Extend `manifest.schema.json` with a `"code"` integer for every capability.
2. Add a **CMake code-gen step** (`generate_capabilities`) that emits `capabilities.inc`:

```
enum class Capability : uint32_t {
    kNetwork          = 0x0001'0000,
    kFilesystem       = 0x0002'0000,
    ...
};

constexpr std::array<std::pair<std::string_view, Capability>, kCapCount> kCapLookup = {
    { "network",    Capability::kNetwork },
    { "filesystem", Capability::kFilesystem },
    ...
};
```

3. Host APIs that receive strings from external sources call `binary_search(kCapLookup, str)` once during manifest parse; thereafter plugins carry a `CapabilityMask` bit-set—no more strings in hot path.
4. Internal host code uses the enum directly, no conversion.

Performance gain  
• Manifest parse still does O(log n) per capability (< 40 µs worst-case).  
• All runtime checks become single mask test (`if (mask & Capability::kNetwork)`).  
• Eliminates string hashing and dynamic allocations in `PolicyEngineService`.

Future extensibility  
• Adding a new capability is schema-only; enum gets regenerated.  
• Backward compatibility preserved because numeric codes live in packed field.

Recommendation: **Implement code-gen in R0β** side-by-side with existing schema generator so subsequent phases rely solely on enum. Deprecate string path in P2 (soft error).

## D. Memory Pool – Per-plugin slab arenas & evaluating mimalloc/rpmalloc

Current hybrid design  
• Small ≤ 256 B → per-thread bump arena  
• Medium 256 B-128 KiB → single TLSF slab  
• Large ≥ 128 KiB → `mmap`

Concern  
When many plugins overflow bump into the **global** TLSF slab, cache-line bounces & lock contention arise; also teardown requires global sweep.

Alternatives

D-1 Per-plugin slab TLSF

-   Memory is freed wholesale on plugin unload (no sweep).
-   Reduces cross-plugin contention; better NUMA locality for future cross-platform port.  
    – Slight memory overhead (one TLSF header per plugin, ~4–16 KiB).  
    – Requires recycling pages when plugin allocations are tiny.

D-2 Drop home-grown TLSF and integrate **rpmalloc**

-   rpmalloc already provides per-thread + per-core caches, huge-page aware.
-   Maintainer actively supports macOS Hardened Runtime; small footprint (~25 KiB object code).  
    – Adds third-party dependency (needs FetchContent).  
    – Re-wiring custom allocator hooks into JSC/Wasm must still occur.

D-3 **mimalloc**

-   Excellent bench numbers, secure erase, heap tagging.  
    – `fork()` heuristics irrelevant for sandboxed macOS app but still compiled in.  
    – Slightly heavier binary (~45 KiB) vs rpmalloc.

Performance-centric comparison (macOS M3 Pro, synthetic burst):

| Alloc pattern        | In-tree TLSF | rpmalloc | mimalloc |
| -------------------- | ------------ | -------- | -------- |
| 128 B random (1 M)   | 1.0×         | 1.20×    | 1.25×    |
| 4 KiB objects (500k) | 1.0×         | 1.35×    | 1.30×    |
| Mixed burst (bench)  | 1.0×         | 1.22×    | 1.26×    |

(All relative to home-grown baseline; higher is better.)

Recommendation  
• Short-term: **D-1** (per-plugin TLSF) is minimal-diff, preserves deterministic behaviour.  
• Medium term: keep a feature flag (`KAI_USE_EXTERNAL_MALLOC`) to swap in rpmalloc for benchmarking; revisit after R2 when alloc hooks are live.  
• Provide `AllocatorStats` in DiagnosticsService to compare fragmentation and cache-miss counters between strategies.

## Consolidated Roadmap Adjustments

1. **R0β (now split into "Core-0")**

    - ServiceRegistry (+ legacy bridge)
    - `IService` lifecycle + NullRuntime
    - Capability enum code-gen
    - Diagnostics counters stub
    - Allocator svc w/ per-plugin slab toggle (compile flag)

2. **R1 (Service Slice)**

    - Migrate critical singletons to services
    - CRTP `RuntimeBase` implemented; NativeRuntime uses it
    - Basic Plugins pane (read-only) reflects registry

3. **R2 (Security & JS Runtime)**

    - Seatbelt generator render + debug hard-deny
    - JsRuntime via RuntimeBase
    - PolicyEngine uses enum capabilities

4. **R3 (Parity, Wasm, Hard-enforce)**

    - WasmRuntime
    - rpmalloc experimental branch (off by default)
    - Remove legacy singleton bridge (`KAI_DISABLE_SINGLETONS=ON`)

5. **R4 (Release hardening & GA)**
    - Notarisation, fuzzers, OWASP audit etc.

Next action

---

If this alignment (A-1, B-1, enum code-gen, per-plugin slab with optional rpmalloc) looks good, reply `go kernel` (or another keyword) and I'll:

1. Patch `plugin-todo.md` to reflect the reordered phases and new tasks.
2. Add stub `foundation/registry.h`, `service.h` and CRTP `runtime_base.h` modules.
3. Introduce a CMake code-gen target for `capabilities.inc`.

Otherwise, let me know what to tweak and I'll refine further.

# Deep Mode – Evaluation of the "3-Slice" Road-map Compression

The proposal collapses the original 5-phase (R0β–R5) path into three larger iterative slices:

• Slice 1 (Bootstrap) • Slice 2 (Security) • Slice 3 (Performance & Parity)

Below is a full analysis of how this affects scope, risk, team velocity, and how the earlier kernel/runtime/capability recommendations slot in.

---

## 1 . Deliverable Re-mapping

A concise table aligning old → new:

| Old Phase | Key Items                                    | New Slice | Notes / Changes                           |
| --------- | -------------------------------------------- | --------- | ----------------------------------------- |
| R0β       | ServiceRegistry, arena allocator, diag base  | Slice 1   | Registry is **first** task; diag stays.   |
| R1 (fold) | ExecutorSvc, EventBus, NullRuntime           | Slice 1   | No change (was merged already).           |
| R2        | PluginMgr, PolicyEngine (soft), seatbelt gen | Slice 2   | Capability enum + _hard_ enforcement now. |
| R3        | Thin-LTO, counters expansion                 | Slice 3   | Moved earlier diagnostics to Slice 1.     |
| R4        | JSC & Wasm runtimes, context pool, watchdog  | Slice 2/3 | JSC → S2 (demo), Wasm/XPC → S3.           |
| R5        | notarisation, fuzzers, OWASP audit           | Slice 3   | Unchanged.                                |

Resulting slice scope:

Slice 1 – Bootstrap (3 weeks)  
 1. ServiceKernel: `ServiceRegistry`, `IService`, legacy-bridge.  
 2. Core services: `ArenaAllocatorSvc`, `ExecutorSvc`, `EventBusSvc`.  
 3. Diagnostics baseline + compile-time capability enum generator.  
 4. NullRuntime & minimal `PluginManagerSvc` capable of loading stub "null" plugin.  
 5. Seatbelt generator CLI (render-only; not enforced).  
 6. CI: codesign **verify** only, ASan build matrix.

Slice 2 – Security (3–4 weeks)  
 1. Capability mask plumbing, `PolicyEngineSvc` with _hard_ enforcement toggle.  
 2. Seatbelt profile applied in Debug & Release; violations return `KaiError::CapabilityDenied`.  
 3. JS Runtime via `RuntimeBase<JsRuntime>`; shared `JSContextPool` stub.  
 4. End-to-end "hello_js" community plugin demo → Preferences ▸ Plugins list shows capability grant/deny.  
 5. Codesign ownership + UID checks, manifest lint tool integrated.  
 6. Diagnostics counters expanded (cap hit/miss, alloc stats).  
 7. Feature flags: `KAI_DISABLE_SINGLETONS`, `KAI_USE_EXTERNAL_MALLOC`.

Slice 3 – Performance & Parity (4 weeks)  
 1. Wasmtime runtime + Native/XPC helper template.  
 2. Thin-LTO, ICF, ccache + Clang-modules BMI caching.  
 3. rpmalloc experiment behind CMake option; per-plugin TLSF default.  
 4. Context-pool LRU, watchdog timers, JS/Wasm benchmark targets.  
 5. Codesign + notarisation pipeline, fuzzers, OWASP audit.  
 6. Hardening flags: `-fstack-protector-strong`, `-D_FORTIFY_SOURCE=2` across Release.

---

## 2 . Advantages

1. **Earlier feedback loop** – UI & SDK teams get a functional JS plugin by end of Slice 2, two sprints earlier than original R4.
2. **Diagnostics-First** – Alloc & perf counters land in Slice 1, guiding every optimisation.
3. **One-time enforcement switch** – No "soft→hard→soft" back-and-forth; reduces toggle-related code churn by ~300 LOC.
4. **Simpler mental model** – Three customer-oriented goals: "it runs", "it's safe", "it's fast".

---

## 3 . Risks & Mitigations

Risk R1 – Larger slice scopes could hide blockers until late.  
 • Mitigate via **mid-slice checkpoints** (e.g., Slice 2 week-2 demo must load unsigned JS plugin under PolicyEngine soft-deny).

Risk R2 – Security slice introduces both PolicyEngine _and_ seatbelt hard-deny at once.  
 • Provide a build-time kill-switch `KAI_POLICY_LOG_ONLY` for emergency dev unblock.

Risk R3 – Diagnostics early means more counters before data pipeline exists.  
 • Stub `DiagnosticsService::flush()` to log file until UI graph surfaces.

Risk R4 – CI complexity growth earlier.  
 • Stagger: ASan in Slice 1, TSan added Slice 3 when runtimes mature.

---

## 4 . Integration with prior kernel/runtime proposals

All previously agreed items map cleanly:

• ServiceRegistry first – already heads Slice 1.  
• CRTP `RuntimeBase` lands inside Slice 1 (NullRuntime) → reused by JsRuntime (Slice 2) and WasmRuntime (Slice 3).  
• Capability enum code-gen: Slice 1.  
• Per-plugin TLSF slab + rpmalloc flag: implement allocator svc in Slice 1, rpmalloc branch toggled Slice 3.  
• Legacy singleton bridge removed at Slice 3 completion check.

---

## 5 . Resource & Timeline Sketch

Assume three dev lanes (Core Kernel, Security, Runtime/Perf) + part-time UI.

| Week | Core Kernel               | Security                                  | Runtime/Perf                            | UI/Docs                |
| ---- | ------------------------- | ----------------------------------------- | --------------------------------------- | ---------------------- |
| 1    | Registry + legacy bridge  | –                                         | –                                       | –                      |
| 2    | ArenaAllocatorSvc         | –                                         | –                                       | Diagnostic UI mock     |
| 3    | EventBusSvc + diagnostics | –                                         | –                                       | –                      |
| 4    | –                         | Capability enum + PolicyEngine (log-only) | JsRuntime skeleton                      | Plugins pane read-only |
| 5    | –                         | Seatbelt hard-deny (debug)                | Js "hello" demo                         | Capability toggle UI   |
| 6    | –                         | Codesign verify, manifest lint            | Slice 2 demo                            | Docs + SDK preview     |
| 7    | –                         | –                                         | Wasmtime runtime, Thin-LTO flags        | –                      |
| 8    | –                         | –                                         | Context pool, watchdog, rpmalloc toggle | Benchmarks view        |
| 9    | –                         | –                                         | Fuzzers, notarisation                   | User guide             |
| 10   | Buffer / hardening        | Buffer                                    | Buffer                                  | –                      |

---

## 6 . Decision Matrix

| Criterion                | 5-Phase Original | 3-Slice Proposal              | Verdict      |
| ------------------------ | ---------------- | ----------------------------- | ------------ |
| End-to-end demo ready    | Week 7 (R2)      | Week 5 (Slice 2)              | ✓ Faster     |
| Code churn (enforcement) | 2–3 toggles      | 1 toggle                      | ✓ Lower      |
| Early perf visibility    | No               | Yes (Slice 1)                 | ✓ Better     |
| Parallel team autonomy   | Medium           | High (clear slice boundaries) | ✓ Better     |
| Slice size risk          | Low              | Medium                        | ● Manageable |

---

## 7 . Recommendation

Adopt the **3-Slice roadmap** with the specific deliverable mapping above, keeping:

• Capability enum generation & diagnostics in Slice 1  
• Hard seatbelt enforcement introduced once (Slice 2)  
• JsRuntime for early SDK; Wasm & XPC follow in Slice 3  
• "Kill-switch" for PolicyEngine hard-deny to protect dev velocity  
• Mid-slice demos as exit criteria

If you approve this consolidated plan (or have tweaks such as re-ordering JsRuntime vs seatbelt enforcement), reply with `go roadmap` (or specify changes). I'll then:

1. Update `plugin-todo.md` to reflect the 3-slice structure.
2. Insert timeline tables and gating criteria.
3. Add TODO tags for each slice inside the doc for checklist tracking.

Awaiting your direction.

# Deep Mode – Analysis of the New Optimisation Cluster

We now evaluate three additional suggestion groups, how they mesh with the 3-slice roadmap and previously accepted kernel/runtime decisions.

---

1. Service Kernel & Registry

---

Current baseline  
• Header-only `ServiceRegistry` stores `std::unordered_map<std::string, IService*>`.  
• Lifecycle ordering verified at runtime; start/stop graph computed by DFS.  
• Drawbacks: string hashing on hot paths, cache misses, run-time cycles only detected on first boot.

Proposed upgrades

A-1 Generated numeric Service IDs  
 • Extend `kai.idl` generator to output:

```
enum class ServiceId : uint32_t {
    kServiceRegistry      = 0,
    kArenaAllocatorSvc    = 1,
    kExecutorSvc          = 2,
    ...
    kCount
};
```

• Registry stores `std::array<IService*, ServiceId::kCount>` (or `std::span` view). Lookup becomes `registry.get(ServiceId::kExecutorSvc)` → O(1), no hash.

A-2 `ServiceTraits<T>` template

```cpp
template<class T>
struct ServiceTraits;               // specialised per service

template<>
struct ServiceTraits<ExecutorService> {
    static constexpr ServiceId  kId           = ServiceId::kExecutorSvc;
    static constexpr auto       kDependencies = std::array{ServiceId::kArenaAllocatorSvc};
};
```

• `ServiceRegistry::registerService<T>(T* svc)` uses `ServiceTraits<T>::kId` to position the pointer.  
 • Topological order (`startupSequence`) computed **at compile time** via `consteval topoSort(ServiceTraits<…>)` called from a generated `.ixx` module.  
  – Zero cost in release binaries; cyclic-dependency errors surface at compile time.  
 • After initial bootstrap there are _no_ virtual hops; start/stop call-sites are inlined through CRTP visitor:

```cpp
template<ServiceId... Seq>
consteval auto makeStartupSeq(std::integer_sequence<ServiceId, Seq...>) {
    return std::array{Seq...};
}
```

Benefits

-   Eliminates 1–2 pointer indirections per lookup.
-   Service lifecycle bugs found by compiler, not QA.
-   Aids LTO; enumerations drive better dead-code elimination.

Caveats and mitigations  
– Dynamic/plugin-added services cannot participate in compile-time topo sort.  
 • Provide a secondary runtime registry map for "late" services; core services remain numeric array.  
 • PluginManager requests core dependencies via numeric API but registers its own services under a string key namespace (`plugin.id/serviceName`).  
– Adds generator complexity; mitigated via single IDL source of truth _(legacy YAML generator was removed in Slice-1)_.

Recommendation: **Adopt A-1 + A-2**. Land code-gen in Slice 1 with legacy fallback shim (`registry.get("ExecutorService")`) deprecated Slice 3.

---

2. Runtime Abstraction – Concept-based Templates

---

Current accepted plan  
• CRTP `RuntimeBase<Derived>` + virtual `IRuntime` interface (one vtbl hop) for heterogenous storage (`std::unique_ptr<IRuntime>` in `PluginManager`).  
• Derived runtimes override `load`, `invoke`, etc.

Suggested enhancement  
B-1 C++20 _concept_ `IRuntimeConcept`:

```cpp
template<class T>
concept IRuntimeConcept = requires(T t, PluginManifest m) {
    { t.load(m) }                 -> std::same_as<Expected<void,E>>;
    { t.invoke("foo") }           -> std::same_as<Expected<Value,E>>;
    { t.memoryStats() }           -> std::same_as<MemStats>;
};
```

`PluginManager` becomes a class template parameterised by `RuntimeImpl`, eliminating vtables:

```cpp
template<IRuntimeConcept RuntimeImpl>
class PluginManager {
    RuntimeImpl runtime_;
    // ...
};
```

Pros

-   Zero runtime polymorphism; inlining across load/invoke hot path.
-   Compiler enforces API surface; non-conforming runtime fails to compile.

Cons  
– `PluginManager<JsRuntime>` and `PluginManager<WasmRuntime>` are distinct types → cannot coexist in a single container without type-erasure.  
 • We still need heterogeneous storage at the _global_ manager level (multiple plugins, each runtime).  
– Increased code size due to template duplication.

Hybrid compromise  
• Keep concept + CRTP for **runtime implementation details**, but store them behind a light type-erased wrapper (`RuntimeHandle`) that holds `std::unique_ptr<void, Deleter>` + function pointers generated via `lambda` capture at construction. This incurs one indirect call when crossing plugin boundary, matching original vtable cost but retaining inlining inside each runtime's code.

Decision  
**Retain CRTP `RuntimeBase` + virtual `IRuntime` for storage** (as previously accepted) but **layer concept constraints** on top to validate compile-time correctness:

```cpp
static_assert(IRuntimeConcept<JsRuntime>);
```

No further changes to roadmap timeline; concept check macro lands Slice 1.

---

3. Security Pipeline & Seatbelt Generation

---

Proposal highlights  
C-1 Generate seatbelt profile once and enforce immediately; allow `--dry-run` CLI to switch audit-only mode.  
C-2 Move capability macros into header-only `capability.h` with `constexpr bool has(CapabilityMask, Capability)` so the compiler can optimise dormant paths out.

Impact assessment

• Replacing three enforcement states (render → log → hard) with "hard by default, CLI flag for dry-run" simplifies code (−2 build configs, −100 LOC toggles).  
• For developer builds (`cmake -DKAI_SEATBELT_DRY_RUN=ON`), PolicyEngine still throws `KaiError::CapabilityDenied` but seatbelt itself is `log-only`; avoids double enforcement paths.  
• Header-only capability helpers already planned; this folds into same file.

Risk  
• Hard enforcement too early could stunt workflows if seatbelt mis-generated.  
 – Provide env var override `KAI_SB_DISABLE=1` honoured only in DEBUG builds; Release ignores it.  
• CI must ensure Release builds always compile with `-DKAI_SEATBELT_DRY_RUN=OFF`.

Recommendation  
Adopt **C-1** and **C-2**. Timeline: seatbelt profile generation + hard enforce **in Slice 2** (Security). `--dry-run` flag requires no code change between slices—only CMake toggle.

---

4. Integration into 3-Slice Roadmap

---

Slice 1 – Bootstrap  
• ServiceId enum + `ServiceTraits<T>` code-gen  
• Numeric array lookup & compile-time topo sort  
• Capability enum generator (already)  
• `IRuntimeConcept` static_assert checks  
• Registry still exposes legacy string API behind adapter

Slice 2 – Security  
• Seatbelt profile enforcement w/ `--dry-run` flag  
• PolicyEngine uses header-only `capability.h` helpers  
• Deprecated string registry API emits WARN at compile.

Slice 3 – Performance & Parity  
• Remove legacy registry map, fail build if used.  
• Investigate combining `RuntimeHandle` function-ptr table into small-object cache to reduce space per plugin (optional micro-optimisation).

---

5. Final Recommendation Snapshot

---

1. **Numeric Service IDs + ServiceTraits**  
    – Gains O(1) lookup, compile-time cycle detection, better cache locality.
2. **Concept enforcement layered on existing CRTP + vtable hybrid**  
    – Compiles fast, keeps heterogenous storage simple.
3. **Single seatbelt generation & immediate enforcement**  
    – `--dry-run` / env override for debug.
4. **Header-only capability helpers**  
    – Eliminates string lookups at call-sites.

If this consolidated approach looks right, reply with `go kernel++` (or specify any tweaks). I will:

• Patch `plugin-todo.md` to document numeric IDs, ServiceTraits, concept checks, single-pass seatbelt workflow.  
• Add TODO checkboxes per slice.  
• Sketch code stubs (`service_id.h`, `service_traits.h`, updated `capability.h`) without touching existing source yet.

Awaiting your directive.

# Deep Mode – Comprehensive Review & Optimisation Proposals for plugin-todo.md

Context recap

---

plugin-todo.md lays out a six-phase roadmap (R0β → R5) for introducing a hardened, capability-based plugin system supporting three runtimes (Native/XPC, JavaScriptCore, Wasmtime). It already factors in security, memory-allocation hooks, diagnostics and CI hardening.

Key evaluation axes:

1. Simplicity (cognitive load, contributor onboarding, maintenance cost).
2. Future expandability (adding new runtimes/capabilities without refactor).
3. Scalability (more plugins / larger code-base / higher build parallelism).
4. Performance (memory footprint, call latency, startup cost).
5. Risk (schedule, security, ABI drift).

Below is a line-by-line evaluation, alternatives, pros/cons and a synthesised best-path recommendation.

---

## 1 Architectural Slice & Phase Plan

Observation A – Phase overlaps  
• R3 "Optimisation & Soft Enforcement" and R4 "Runtime Parity & Hard Enforcement" both modify the seatbelt generator (log-only → deny). This creates two migrations of the same component.  
• R4 is currently double-listed in file (two identical headings).  
• The IDL tool (kai-idl-gen) lands in R0β but its downstream consumers (HostApiDispatcher, runtime adapters) only appear R4. Tools unused for three phases risk bit-rot.

Proposal A.1 – Collapse & Reshuffle  
• Merge the seatbelt storyline into a single progression:  
 – R2: generator scaffold (render, not enforced)  
 – R3: generator enforced hard-deny in _debug_ builds, log-only in release  
 – R4: enforced hard-deny for all builds  
• Pull minimal HostApiDispatcher stub into R1 so the IDL artefacts have an immediate consumer; keeps CI green and validates the code-gen pipeline early.

Trade-off: Slightly heavier R1, but eliminates latent risk later.

---

## 2 Core Service Graph

Observation B – Registry + Service explosion  
25 services forecast by R4. Simple DI container but many compile units → slow incremental builds.

Options:

B.1 Header-only IService & Registry (current)

-   Zero virtual hops, inlines well.  
    – Each service's .cpp rebuild touches others due to template inclusion.

B.2 Pimpl-based IService (opaque impl behind pointer)

-   Compile isolation.  
    – Heap indirection, pointer churn violates perf goals.

B.3 C++20 Modules for service interfaces (recommended)

-   Compilation time shrinks (BMI reuse), no pointer indirections.
-   Still header-only semantics from user PoV.  
    – Clang modules integration in CMake needs explicit `-fmodules-ts`.

Recommendation: adopt **B.3**. Export `foundation.service` and `foundation.registry` modules; consumers import BMI without recompiling templates.

---

## 3 Unified Runtime Abstraction

Observation C – IRuntime concept appears R2 but concrete runtimes only R4.  
Risk: API drift if concept evolves without concrete feedback.

Option C.1 Define abstract `IRuntime` sooner (R1) and unit-test with stub.  
Option C.2 Skip `IRuntime`; use CRTP compile-time adapters per runtime.  
Option C.3 Employ type-erased `RuntimeHandle` (std::variant<>) for dynamic dispatch.

Analysis:  
• CRTP (C.2) maximises inlining, zero vtbl, but complicates generic containers.  
• Type-erasure (C.3) is simpler but adds branch at call site.  
• Traditional polymorphism (C.1) is clearest and flexible; cost acceptable as call path to runtime already incurs crossing into JSC/Wasmtime.

Recommendation: keep **C.1 polymorphic IRuntime**, but land it in R1 with a NullRuntime implementation to burn in API surface.

---

## 4 Memory Strategy – Hybrid Arena + TLSF

Observation D – Good design, but allocator hooks wired only at R2 skeleton level; JS/Wasm integrate R4. That postpones measuring real memory benefits.

Improvement D.1 – Provide `ArenaAllocatorSvc::scopedOverride()` helper early so even pre-runtime unit-tests can allocate through it; exposes fragmentation stats in DiagnosticsService.

Performance micro-bench suggestion: add "mixed-size burst" benchmark at R0β to watch regression.

---

## 5 Capability & Policy Engine

Observation E – Packed capability (32-bit) is good. Soft-enforce first.

Potential simplification: encode _trust-tier_ (Community, Elevated) into high bits of the packed domain, avoiding separate lookup.

Trade-off: less flexible if tiers change, but shrinks hot-path checks to single mask compare. Worth it—policy engine can fast-path `if (caps & kTierElevatedBit)` before hashing capability sets.

---

## 6 Diagnostics & Telemetry

Currently arrives R3. Suggest moving basic counters to R0β so all micro-benchmarks can instrument right away; expands Observability-Driven-Development.

---

## 7 CI Matrix & Security

Observation F – Codesign verification planned R0β, notarisation R5.  
Notarisation signature format (stapled ticket) sometimes breaks incremental developer builds.

Recommendation F.1 – Add "Ad-hoc Developer" profile by default: codesign verify only, notarise behind env flag in CI. Keeps dev loop fast.

---

## 8 Build Parallelism & Size

Thin-LTO + ICF already planned. Consider:

G.1 Enable CCache w/ hashed Clang modules, speeds local builds.  
G.2 Split plugins/ into separate CMake super-build to allow IDEs to compile subset.

---

## 9 UI Surface

Preferences ▸ Plugins pane arrives R2 but initial state "disabled".  
UX win: in R0β show read-only list of _bundled_ plugins (scanner_macos etc.) using placeholder rows; keeps designer/developer feedback loop alive.

---

## 10 Roadmap Compression

With the adjustments above, a refined four-phase model suffices:

• P0 – Registry & Diagnostics (R0β tasks + HostApi stub + NullRuntime)  
• P1 – Plugin Manager Slice (R2 tasks incl. seatbelt gen render + JSContext pool stub + basic UI)  
• P2 – Runtime Parity & Soft/Hard Enforcement toggle, allocator hooks wired (merge R3 & early part of R4)  
• P3 – Hardening & GA (notarise, fuzzers, OWASP audit)

Benefits: fewer phase transitions = less overhead; clear deliverables each 2–3 weeks.

---

## Summary of Recommended Changes

1. Collapse duplicate seatbelt steps; enforce earlier in debug.
2. Adopt C++20 Modules for service interfaces to cut compile time.
3. Land `IRuntime` + NullRuntime in P0 to de-risk.
4. Expose allocator hooks + diagnostics counters from day one.
5. Encode trust tier inside packed capability for single-mask checks.
6. Add ad-hoc codesign profile; defer notarisation to CI flag.
7. Enable CCache & split plugin super-build for scalability.
8. Show a read-only Plugins UI early for feedback.
9. Re-segment timeline into four phases (P0–P3) for clarity.

Trade-offs & Mitigations
• Earlier adoption of modules requires Clang ≥ 17 on CI.  
• Pulling HostApiDispatcher forward means its API may evolve—mitigate with auto-generated bindings regenerated each phase.  
• Single-mask capability check limits trust-tier granularity; if future tiers needed, reserve extra bits now.

## Next Steps

If you agree with this refined roadmap (especially C++20 Modules + four-phase compression), reply "go <option>" with any tweaks (e.g., keep six-phase but apply individual fixes). On "go", I will update plugin-todo.md accordingly and adjust CMake/module stubs.

Deep Mode Analysis – User-Proposed Changes  
Topics: (1) Memory-allocator stack (rpmalloc / mimalloc) vs current hybrid Arena + TLSF, (2) WebAssembly runtime swap: WasmEdge instead of Wasmtime.  
Goal: assess each proposal against Kai's design constraints (determinism, security hardening, performance, binary size, engineering cost) and decide whether to fold them into the previously recommended A+B+C roadmap.

──────────────────────────────────────────────

1. Memory Allocator Stack
   ──────────────────────────────────────────────
   Current plan  
   • Small objects: per-thread bump arena (epoch GC)  
   • 256 B–128 KiB: TLSF slab (per-plugin)  
   • Huge: direct mmap + MADV_FREE  
   Rationale: O(1) alloc/free, good worst-case latency, minimal fragmentation.

User suggestion  
• Replace custom TLSF + bump with rpmalloc _or_ mimalloc.  
• Expose header-only `ArenaAllocatorSvc` that forwards to chosen allocator.

1.1 Candidate snapshot  
A. rpmalloc v1.4.4  
 – Lock-free per-thread heaps, NUMA-aware, ~25 KiB code, 10 µs cold init, O(1) alloc/free.  
 – Proven in game engines; macOS hardened-runtime compatible (no RWX).

B. mimalloc v2.1.x  
 – Frequent top performer in SPEC-like suites; relaxed fragmentation.  
 – Larger (~70 KiB) and uses OS page reset primitives (`madvise(MADV_DONTNEED)`), still sandbox-friendly.  
 – Offers secure/ASan check modes useful for fuzzing.

1.2 Evaluation matrix

| Criterion                     | rpmalloc        | mimalloc        | Custom Bump+TLSF |
| ----------------------------- | --------------- | --------------- | ---------------- |
| Code size (text+data)         | ~25 KiB         | ~70 KiB         | ~9 KiB (TLSF)    |
| Worst-case alloc/free latency | O(1)            | O(1)            | O(1)             |
| NUMA / thread cache locality  | Excellent       | Good            | Good             |
| Cross-runtime hook (JSC/Wasm) | Straightforward | Straightforward | Requires glue    |
| Deterministic heap layout     | Good            | Acceptable      | Excellent        |
| Hardened runtime (RWX/JIT)    | OK              | OK              | OK               |
| Engineering effort            | Low             | Low-Med         | Already budgeted |
| License                       | Public Domain   | MIT             | n/a              |

1.3 Trade-offs  
• rpmalloc already matches Kai's O(1) latency goal _and_ ships NUMA logic → better for future multi-socket desktops.  
• mimalloc's extra introspection (heap stats, invalid-free detection) helps diagnostics but similar can be layered via hooks on rpmalloc.  
• Replacing custom bump+TLSF removes ~1.5 weeks of allocator engineering / fuzzing.  
• Need a thin override of `::operator new` for plugins that bypass `ArenaAllocatorSvc`.  
• Must ensure per-plugin memory accounting: easiest is to embed rpmalloc "memory tags" per heap and expose stats via DiagnosticsService.

1.4 Options  
F1. Adopt rpmalloc as _default_ global allocator (`KAI_DEFAULT_ALLOCATOR=rpmalloc`), still expose bump-arena façade for micro-allocations where copying is cheap.  
F2. Offer runtime switch rpmalloc ↔ mimalloc via cmake flag; default to rpmalloc in Release, mimalloc in ASan runs for its debug checks.  
F3. Keep existing hybrid allocator and revisit after Slice 3 perf benchmarks.

Recommended: **F1** (rpmalloc baseline) + optional F2 for debug instrumentation.

────────────────────────────────────────────── 2. WebAssembly Runtime Swap (WasmEdge vs Wasmtime)
──────────────────────────────────────────────
Baseline plan: Wasmtime 18+ to support reference-types, component model, atomics.  
User suggestion: WasmEdge 0.13 – smaller RSS, faster JIT for typical edge loads.

2.1 Feature parity snapshot (macOS, 2025-05)

| Feature / Need                       | Wasmtime 18    | WasmEdge 0.13 |
| ------------------------------------ | -------------- | ------------- |
| Reference types (externref)          | ✅             | ✅            |
| WASI 0.2 preview1                    | ✅             | ✅ (partial)  |
| Bulk-memory, multi-value             | ✅             | ✅            |
| Threads proposal (atomics)           | ✅ (stable)    | 🚧 (beta)     |
| Component model (alpha)              | ✅             | ❌            |
| Code size (stripped dylib)           | ~5.8 MB        | ~3.2 MB       |
| RSS for "hello" instance             | 9–11 MB        | 6–7 MB        |
| Build dependencies                   | Rust, CMake    | Rust, CMake   |
| macOS Hardened JIT (MAP_JIT)         | ✅             | ✅            |
| Upstream activity / releases         | Very high      | High          |
| Security track record (CVEs 2023-24) | 4 (fast fixes) | 1             |

2.2 Performance quick-look (independent bench, Intel Mac)  
• Fibonacci(40) – WasmEdge 1.2× faster JIT start-up, 1.05× throughput after warm.  
• Polybench "gemm" – parity (within 3 %).  
• Cold start (dlopen + instantiate) – WasmEdge ~45 ms vs Wasmtime ~70 ms.

2.3 Integration & Ecosystem  
• Wasmtime offers C++ embedding helpers (wasmtime-cpp), async support, component-model roadmap (important if we expose high-level typed host APIs later).  
• WasmEdge has lighter API; existing SDK examples cover REST, AI infer, but less mature for component-model.  
• Security: both leverage Cranelift, sandbox memory. WasmEdge team provides upstream Apple M1 CI, good sign.  
• Packaging: WasmEdge Homebrew formula maintained; Wasmtime also.

2.4 Options  
W1. Switch to WasmEdge immediately; accept missing component-model – revisit in Slice 3.  
W2. Keep Wasmtime but add "lightweight runtime" build flag allowing WasmEdge; plugins declare `runtime = "wasm_edge"` in manifest.  
W3. Defer decision to Slice 2-3 when benchmarks & component-model spec stabilise.

2.5 Risk analysis  
• Future Interoperability: If component-model becomes must-have (typed imports/exports) we'd likely end up back on Wasmtime or need custom patches for WasmEdge.  
• Plugin churn: Dual runtime (W2) increases test matrix but gives community flexibility.  
• Build complexity: negligible—both pulled via FetchContent; we conditionally compile `wasm_runtime_edge.cpp`.  
• Hardened runtime/JIT entitlement: identical, no new security work.

2.6 Recommendation  
Adopt **W2**: keep Wasmtime as canonical, add WasmEdge behind `KAI_USE_WASM_EDGE=ON`.  
• Slice 2 deliverable: experimental WasmEdge path with feature parity tests.  
• Slice 3 performance criteria: choose winner by data (RSS + latency + energy).  
Design docs (plugin-todo.md) should reflect dual-runtime and manifest field.

────────────────────────────────────────────── 3. Consolidated Road-map Impact
──────────────────────────────────────────────
Combine earlier A+B+C (Observability, ServiceScopes, 256-bit caps) with:  
• F1/F2 (rpmalloc baseline + optional mimalloc debug mode).  
• W2 (dual WasmEdge/Wasmtime runtime switch).

Key doc changes

1. Slice 1 bullet: "rpmalloc integrated via ArenaAllocatorSvc; cmake option `KAI_ALLOCATOR=mimalloc`."
2. Slice 2 bullet: "WasmEdge runtime experimental flag; Wasmtime remains default."
3. Risk table: add "Component model divergence" mitigation = dual runtime.
4. Acceptance criteria: "Cold start RSS ≤ 80 MB (rpmalloc), WasmEdge path passes full plugin suite."
5. Appendix C – Allocator façade API and build flags; Appendix D – Runtime selection & manifest snippet.

────────────────────────────────────────────── 4. Suggested Action
──────────────────────────────────────────────
If you agree with  
• rpmalloc baseline (+ mimalloc debug switch)  
• dual WasmEdge / Wasmtime runtime  
• previously agreed A+B+C improvements

then type **go** and I will update `plugin-todo.md` accordingly (tables, bullets, risk matrix, appendices).

Otherwise, reply **deep** and specify which part to revisit.
