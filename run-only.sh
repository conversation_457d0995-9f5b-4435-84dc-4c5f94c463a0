#!/bin/bash

# Run-only script for the project
# Runs the application without building it

# Print header with timestamp
echo "====================================================="
echo "Application run started: $(date)"

# Print system information
echo "System information:"
echo "  OS: $(uname -s)"
echo "  Hostname: $(hostname)"
echo "  Kernel: $(uname -r)"
echo "  Architecture: $(uname -m)"

# Record start time for runtime calculation
start_time=$(date +%s)

# Check if build directory exists
if [ ! -d "./build" ]; then
    echo "Error: Build directory not found"
    echo "Please run ./build.sh first to build the application"
    exit 1
fi

# Navigate to the build directory
cd build

# Check if the binary exists
if [ ! -f "./bin/launcher" ]; then
    echo "Error: Application binary not found at ./bin/launcher"
    echo "Please run ./build.sh first to build the application"
    exit 1
fi

# Run the application
echo "====================================================="
./bin/launcher
run_status=$?

# Calculate runtime
end_time=$(date +%s)
duration=$((end_time - start_time))
minutes=$((duration / 60))
seconds=$((duration % 60))

echo "====================================================="
if [ $run_status -eq 0 ]; then
    echo "Application exited successfully"
else
    echo "Application exited with code $run_status"
fi
echo "Runtime: ${minutes}m ${seconds}s"
echo "=====================================================" 