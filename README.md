# MicroLauncher

A minimal, efficient app launcher similar to Alfred, Raycast, and Spotlight, with a focus on extremely low disk space and RAM utilization. The application runs on both macOS and Windows platforms without using Electron, Python, or any scripting languages.

## Features

-   Fast application indexing and launching
-   Minimal resource usage (RAM, disk space, CPU)
-   Fuzzy search with prefix matching
-   Frequency-based search result ranking
-   Search result caching for improved performance
-   Static actions
-   AI-powered capabilities (optional)
-   Cross-platform support (macOS)

## Technical Stack

-   **Core**: C/C++
-   **UI**: Native frameworks (AppKit for macOS, Win32 API for Windows) or lightweight cross-platform alternatives (FLTK or ImGui)
-   **Build System**: CMake
-   **Dependencies**: Minimal external libraries

## Building from Source

### Prerequisites

-   CMake 3.14 or higher
-   C++20 compatible compiler
-   nlohmann/json (automatically fetched by CMake)
-   Catch2 (for tests, automatically fetched by CMake)

### Build Instructions

```bash
# Clone the repository
git clone https://github.com/hoang17/kai.git
cd kai

# Create a build directory
mkdir build
cd build

# Configure and build
cmake ..
cmake --build .

# Run tests
ctest
```

### Core-service headers & compile-time DAG safety

Core service identifiers (`service_id.h`) and the deterministic start-order array
(`service_topology.h`) are **maintained in source control**. When introducing a
new service or changing dependencies:

1. Append the new enumerator to `ServiceId` in
   `src/core/foundation/service_id.h` (never reorder existing items).
2. Insert the service into the correct position of the `kSorted` array inside
   `src/core/foundation/service_topology.h` so that every dependency appears
   _before_ the service that needs it.

The `static_assert` in `service_static_checks.h` validates the order at compile
time and breaks the build if it detects a violation. No external YAML spec or
code-generation step is required.

## Project Structure

```
microlauncher/
├── src/                    # Source code
│   ├── core/               # Core functionality
│   │   ├── index/          # Application indexing
│   │   ├── search/         # Search functionality (history, caching)
│   │   └── ...
│   └── tests/              # Test code
├── CMakeLists.txt          # Main CMake configuration
├── README.md               # This file
└── ...
```

## Development Status

This project is currently in early development. See the [todo.md](todo.md) file for the current development status and planned features.

## Code Quality

We maintain high code quality standards through:

-   **Automated Testing**: Comprehensive unit tests with high code coverage
-   **Static Analysis**: Using cppcheck to detect potential issues
-   **Code Formatting**: Enforcing consistent style with clang-format
-   **Continuous Integration**: Automated builds and tests for all changes
-   **Code Reviews**: All contributions are reviewed before merging

To set up the git hooks for automatic code formatting:

```bash
./setup-hooks.sh
```

To run all code quality checks locally:

```bash
./run-checks.sh
```

## Performance Optimizations

The launcher implements several performance optimizations:

-   **Fuzzy Search Algorithm**: Efficient matching of user queries against application names
-   **Search History**: Tracks frequently used searches to improve result ranking
-   **Search Result Caching**: Caches search results to avoid redundant calculations
-   **Incremental Index Updates**: Only updates the application index when changes are detected

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Building and Distribution

### macOS App Bundle

To build a macOS app bundle for distribution, use the provided `package_app.sh` script:

```bash
# Basic build without signing
./package_app.sh

# Build and sign with your Developer ID
./package_app.sh --sign --developer-id "Your Name (TEAMID)"
```

The script will:

1. Build the application in Release mode
2. Create a proper `.app` bundle structure
3. Generate the necessary Info.plist file
4. Generate and include the custom app icon
5. Optionally sign the application and create a DMG for distribution

For more detailed information about the packaging process, see [PACKAGING.md](PACKAGING.md).

#### App Icon

The application uses a custom rocket icon that is generated programmatically. The icon is created using the `scripts/generate_app_icon.sh` script, which:

1. Creates an `.icns` file with all required sizes for macOS app bundles
2. Creates a high-resolution PNG file for other uses

The icon files are stored in `src/ui/macos/resources/` and are automatically included in the app bundle during packaging.

#### Code Signing and Notarization

For distribution outside the Mac App Store, your app should be signed and notarized:

1. Sign the app with your Developer ID (as shown above)
2. Notarize the app with Apple:
    ```bash
    xcrun notarytool submit "MicroLauncher-1.0.0.dmg" --apple-id "your-apple-id" --password "app-specific-password" --team-id "your-team-id" --wait
    ```
3. Staple the notarization ticket:
    ```bash
    xcrun stapler staple "MicroLauncher.app"
    xcrun stapler staple "MicroLauncher-1.0.0.dmg"
    ```

#### Installation

After building, you can install the app by:

1. Copying `build-release/MicroLauncher.app` to your Applications folder
2. Or, if you created a DMG, by mounting the DMG and dragging the app to Applications

# Kai Agent

A modern, powerful UI agent application.

## Project Structure

The project is organized into several key components:

-   `src/core`: Core functionality and backend interfaces
-   `src/ui`: UI implementations including macOS, Windows, and common components
-   `src/ui/chat`: Chat UI implementation
-   `src/ui/chat/mention`: Mention functionality components

## Mention Functionality

The mention functionality allows users to tag other users in chat messages using the "@" symbol. This feature includes:

1. **Components**:

    - `MentionPopoverController`: Controls the popover UI that appears when typing "@"
    - `MentionViewController`: Handles the table view of mention suggestions
    - `MentionSuggestion`: Data model for suggestion items

2. **Implementation Notes**:

    - All mention component implementations are consolidated in `src/ui/chat/mention/MentionComponents.mm` for build simplicity
    - `src/ui/chat/model/MentionSuggestion.mm` contains the data model implementation
    - The UI sends asynchronous requests to the backend through `MacOSChatUI::searchMentionableItemsWithQuery`
    - Error handling uses the `BackendError` class to wrap C++ error codes

3. **Debugging**:
    - Debug logs are available throughout the code using the `DBG` and `DBM` macros
    - Error handling includes proper cancellation detection via `BackendErrorCode::CANCELLED`

## Building and Running

To build the project:

```bash
./build.sh
```

To run in debug mode:

```bash
./run.sh

# Run micro-benchmarks (requires Release build)
make bench          # builds if necessary then executes Google Bench suite
```

## Contributing

When contributing to the mention functionality, be aware of these implementation details:

1. The Objective-C++ bridge between C++ backend and Objective-C UI requires careful handling of memory and object lifetimes.
2. Asynchronous operations use a combination of NSOperations and dispatch queues with proper cancellation handling.
3. Error handling propagates through the `BackendError` class which converts C++ error codes to Objective-C NSError objects.

## Codebase Architecture Overview

The repository is organized into modular components that separate backend logic, UI, ranking, and tests.

### Core (`src/core`)

-   Backend logic shared across all interfaces.
-   Includes submodules for chat backends (`openai_chat_backend`), context providers, HTTP utilities, LLM models, indexing, and search history.

### User Interface (`src/ui`)

-   Platform-specific implementations with a focus on macOS Objective-C++ code.
-   Contains the chat UI and mention system under `src/ui/chat` and `src/ui/chat/mention`.

### Ranking (`src/ranking`)

-   Implements a lightweight contextual bandit reranker using a linear UCB algorithm.
-   Combines feature extraction and heuristic scoring to order results.

### Tests (`src/tests`)

-   Unit tests built with Catch2 covering indexing, caching, and other core functionality.

### Configuration (`config`)

-   Default configuration and JSON schema used to validate user settings.

### Build and Development Scripts

-   `build.sh` and `run.sh` handle configuration and launching.
-   `scripts/run-checks.sh` formats code with clang-format, runs cppcheck, then builds and executes the test suite.

## Plugin System (Slice-1 Preview)

Kai now supports a minimal plugin bootstrap path driven by **RuntimeManagerSvc**. At this stage only a **null plugin** is loaded to validate the discovery / validation pipeline – no untrusted code runs in-process.

### How it works

1. **RuntimeManagerSvc** scans two directories on start-up:  
   • `${APP_BUNDLE}/Contents/Plugins/` (bundled)  
   • `~/Library/Application Support/MicroLauncher/Plugins/` (user)
2. Each candidate `.dylib` (or `.so` on Linux) is validated:  
   • `stat()` owner UID must match current user.  
   • **Codesign verify** via `SecStaticCodeCheckValidity` (macOS).  
   • Optional Seatbelt profile `plugin.sb` is compiled & enforced (fail-secure).  
   • Mandatory symbol `kai_plugin_get_info` is resolved.
3. The library is unloaded immediately. A `PluginDescriptor` is cached and a `PluginScanCompleteEvent` is published on the global **EventBusService** so the UI can surface the discovered list.
4. Diagnostics counters such as `plugins.discovered`, `plugins.dlopen_failed`, `plugins.back_pressure` are visible via **DiagnosticsService**. Back-pressure on the EventBus is reported via `eventbus.back_pressure`.
5. All plugin allocations are funnelled through the host's rpmalloc heap by the auto-included `kai_plugin_alloc_shim.h`, eliminating mixed-allocator fragmentation.

### Null Plugin example (`sample_null`)

The repository ships an example under `src/plugins/sample_null/`.

```cpp
extern "C" KaiPluginInfo kai_plugin_get_info() {
    KaiPluginInfo info{};
    std::strncpy(info.id, "sample_null", sizeof(info.id));
    info.abi_major = kKaiAbiMajor;
    info.abi_minor = kKaiAbiMinor;
    info.runtime   = KAI_RUNTIME_NULL; // tells host no runtime needed
    std::memset(info.capabilities, 0, sizeof(info.capabilities));
    return info;
}
```

Building with `./build.sh` produces `lib/sample_null.dylib` which is automatically copied into `build/Plugins/` via a post-build step so the application discovers it at runtime.

### Interpreting errors

`PluginResult<T>` uses `PluginError` enum; you can turn values into strings via `toString(err)` for UI presentation:

```
stat_failed │ File could not be stat()'d
uid_mismatch│ File owned by another user – denied by policy
codesign_invalid │ Signature failed ad-hoc verification
```

Future slices will extend the runtime to JavaScript & Wasm and enforce capability bitmaps declared in `manifest.toml`. For now the pipeline validates loading and surfaces diagnostics so you can verify the Hardened Runtime settings.

## Service Registry Maintenance

Changing the `ServiceId` enum, adding a new service, or altering a service's dependency list **requires** updating `src/core/foundation/service_topology.h` (the pre-sorted `kSorted` array). If these files drift, a `static_assert` in `service_static_checks.h` fires and CI fails immediately – no extra generator step is needed.

## Development

See [CONTRIBUTING.md](CONTRIBUTING.md) for coding standards, service header maintenance rules, and pull-request checklist.
